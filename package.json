{"name": "mobile73", "version": "2.16.66", "private": true, "scripts": {"android": "react-native run-android", "ios": "npx react-native run-ios --simulator 'iPhone 16'", "lint": "eslint .", "start": "react-native start", "test": "jest", "fastlane:android:internal": "cd android/ && fastlane android internal", "fastlane:android:buildApk": "cd android/ && fastlane android buildApk"}, "dependencies": {"@codler/react-native-keyboard-aware-scroll-view": "^2.0.1", "@heap/react-native-heap": "^0.22.8", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/netinfo": "^11.3.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-firebase/analytics": "^19.2.2", "@react-native-firebase/app": "^19.2.2", "@react-native-firebase/messaging": "^19.2.2", "@react-native-firebase/remote-config": "^19.2.2", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.26", "amazon-cognito-identity-js": "^6.3.12", "aws-amplify": "^5.0.14", "aws-amplify-react-native": "^7.0.8", "axios": "0.27.2", "bignumber.js": "^9.1.2", "crypto-js": "^4.2.0", "d3": "^7.9.0", "d3-interpolate": "^3.0.1", "d3-interpolate-path": "^2.3.0", "d3-scale": "^4.0.2", "d3-shape": "^3.2.0", "deprecated-react-native-prop-types": "^5.0.0", "google-protobuf": "^3.21.2", "grpc-web": "^1.5.0", "html-entities": "^2.5.2", "iso-3166-1": "^2.1.1", "iso-3166-2": "^1.0.0", "launchdarkly-react-native-client-sdk": "^9.2.0", "lottie-react-native": "^6.7.2", "moment": "^2.30.1", "multicoin-address-validator": "^0.5.16", "papaparse": "^5.4.1", "react": "18.2.0", "react-native": "0.73.6", "react-native-bootsplash": "^5.5.3", "react-native-calendar-picker": "^8.0.3", "react-native-camera": "^4.2.1", "react-native-circular-progress": "^1.4.0", "react-native-device-info": "^10.13.2", "react-native-dropdown-picker": "^5.4.6", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.16.1", "react-native-google-analytics": "^1.3.2", "react-native-image-picker": "^7.1.2", "react-native-jumio-mobilesdk": "github:Jumio/mobile-react#v4.11.0", "react-native-keychain": "^8.2.0", "react-native-linear-gradient": "^2.8.3", "react-native-permissions": "^4.1.5", "react-native-plaid-link-sdk": "11.10.0", "react-native-push-notification": "^8.1.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-qrcode-svg": "^6.3.1", "react-native-reanimated": "^3.11.0", "react-native-redash": "^18.1.3", "react-native-safe-area-context": "^4.10.1", "react-native-screens": "^3.31.1", "react-native-svg": "^15.2.0", "react-native-svg-transformer": "^1.3.0", "react-native-swipeable": "^0.6.0", "react-native-swiper-flatlist": "git://github.com/JeffKGabriel/react-native-swiper-flatlist.git#93a9bb2c91bc2756bba0da0028b32382dabf91e0", "react-native-switch-pro": "^1.0.5", "react-native-vision-camera": "^4.0.4", "react-native-webview": "^13.8.7", "react-redux": "^7.2.8", "redux": "^4.2.1", "redux-beacon": "^2.1.0", "redux-thunk": "^2.4.2", "rn-fetch-blob": "^0.13.0-beta.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@grpc/grpc-js": "^1.9.0", "@grpc/proto-loader": "^0.7.8", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "0.72.3", "prettier": "2.8.8", "protobufjs": "^7.2.5", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}