/**
 * @fileoverview gRPC-Web generated client stub for api.trading.v1
 * @enhanceable
 * @public
 */

// Code generated by protoc-gen-grpc-web. DO NOT EDIT.
// versions:
// 	protoc-gen-grpc-web v1.4.2
// 	protoc              v4.24.3
// source: api/trading/v1/price.proto


/* eslint-disable */
// @ts-nocheck



const grpc = {};
grpc.web = require('grpc-web');


var google_protobuf_timestamp_pb = require('google-protobuf/google/protobuf/timestamp_pb.js')

var google_protobuf_empty_pb = require('google-protobuf/google/protobuf/empty_pb.js')
const proto = {};
proto.api = {};
proto.api.trading = {};
proto.api.trading.v1 = require('./price_pb.js');

/**
 * @param {string} hostname
 * @param {?Object} credentials
 * @param {?grpc.web.ClientOptions} options
 * @constructor
 * @struct
 * @final
 */
proto.api.trading.v1.PriceClient =
    function(hostname, credentials, options) {
  if (!options) options = {};
  options.format = 'text';

  /**
   * @private @const {!grpc.web.GrpcWebClientBase} The client
   */
  this.client_ = new grpc.web.GrpcWebClientBase(options);

  /**
   * @private @const {string} The hostname
   */
  this.hostname_ = hostname.replace(/\/+$/, '');

};


/**
 * @param {string} hostname
 * @param {?Object} credentials
 * @param {?grpc.web.ClientOptions} options
 * @constructor
 * @struct
 * @final
 */
proto.api.trading.v1.PricePromiseClient =
    function(hostname, credentials, options) {
  if (!options) options = {};
  options.format = 'text';

  /**
   * @private @const {!grpc.web.GrpcWebClientBase} The client
   */
  this.client_ = new grpc.web.GrpcWebClientBase(options);

  /**
   * @private @const {string} The hostname
   */
  this.hostname_ = hostname.replace(/\/+$/, '');

};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.google.protobuf.Empty,
 *   !proto.api.trading.v1.AssetPairsResponse>}
 */
const methodDescriptor_Price_GetAssetPairs = new grpc.web.MethodDescriptor(
  '/api.trading.v1.Price/GetAssetPairs',
  grpc.web.MethodType.UNARY,
  google_protobuf_empty_pb.Empty,
  proto.api.trading.v1.AssetPairsResponse,
  /**
   * @param {!proto.google.protobuf.Empty} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.api.trading.v1.AssetPairsResponse.deserializeBinary
);


/**
 * @param {!proto.google.protobuf.Empty} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.api.trading.v1.AssetPairsResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.api.trading.v1.AssetPairsResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.api.trading.v1.PriceClient.prototype.getAssetPairs =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/api.trading.v1.Price/GetAssetPairs',
      request,
      metadata || {},
      methodDescriptor_Price_GetAssetPairs,
      callback);
};


/**
 * @param {!proto.google.protobuf.Empty} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.api.trading.v1.AssetPairsResponse>}
 *     Promise that resolves to the response
 */
proto.api.trading.v1.PricePromiseClient.prototype.getAssetPairs =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/api.trading.v1.Price/GetAssetPairs',
      request,
      metadata || {},
      methodDescriptor_Price_GetAssetPairs);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.api.trading.v1.PriceRequest,
 *   !proto.api.trading.v1.PriceEvent>}
 */
const methodDescriptor_Price_WatchPrice = new grpc.web.MethodDescriptor(
  '/api.trading.v1.Price/WatchPrice',
  grpc.web.MethodType.SERVER_STREAMING,
  proto.api.trading.v1.PriceRequest,
  proto.api.trading.v1.PriceEvent,
  /**
   * @param {!proto.api.trading.v1.PriceRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.api.trading.v1.PriceEvent.deserializeBinary
);


/**
 * @param {!proto.api.trading.v1.PriceRequest} request The request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!grpc.web.ClientReadableStream<!proto.api.trading.v1.PriceEvent>}
 *     The XHR Node Readable Stream
 */
proto.api.trading.v1.PriceClient.prototype.watchPrice =
    function(request, metadata) {
  return this.client_.serverStreaming(this.hostname_ +
      '/api.trading.v1.Price/WatchPrice',
      request,
      metadata || {},
      methodDescriptor_Price_WatchPrice);
};


/**
 * @param {!proto.api.trading.v1.PriceRequest} request The request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!grpc.web.ClientReadableStream<!proto.api.trading.v1.PriceEvent>}
 *     The XHR Node Readable Stream
 */
proto.api.trading.v1.PricePromiseClient.prototype.watchPrice =
    function(request, metadata) {
  return this.client_.serverStreaming(this.hostname_ +
      '/api.trading.v1.Price/WatchPrice',
      request,
      metadata || {},
      methodDescriptor_Price_WatchPrice);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.api.trading.v1.UnaryPriceRequest,
 *   !proto.api.trading.v1.PriceResponse>}
 */
const methodDescriptor_Price_GetPrice = new grpc.web.MethodDescriptor(
  '/api.trading.v1.Price/GetPrice',
  grpc.web.MethodType.UNARY,
  proto.api.trading.v1.UnaryPriceRequest,
  proto.api.trading.v1.PriceResponse,
  /**
   * @param {!proto.api.trading.v1.UnaryPriceRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.api.trading.v1.PriceResponse.deserializeBinary
);


/**
 * @param {!proto.api.trading.v1.UnaryPriceRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.api.trading.v1.PriceResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.api.trading.v1.PriceResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.api.trading.v1.PriceClient.prototype.getPrice =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/api.trading.v1.Price/GetPrice',
      request,
      metadata || {},
      methodDescriptor_Price_GetPrice,
      callback);
};


/**
 * @param {!proto.api.trading.v1.UnaryPriceRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.api.trading.v1.PriceResponse>}
 *     Promise that resolves to the response
 */
proto.api.trading.v1.PricePromiseClient.prototype.getPrice =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/api.trading.v1.Price/GetPrice',
      request,
      metadata || {},
      methodDescriptor_Price_GetPrice);
};


/**
 * @const
 * @type {!grpc.web.MethodDescriptor<
 *   !proto.api.trading.v1.HistoricalPriceRequest,
 *   !proto.api.trading.v1.HistoricalPriceResponse>}
 */
const methodDescriptor_Price_GetHistoricalPrice = new grpc.web.MethodDescriptor(
  '/api.trading.v1.Price/GetHistoricalPrice',
  grpc.web.MethodType.UNARY,
  proto.api.trading.v1.HistoricalPriceRequest,
  proto.api.trading.v1.HistoricalPriceResponse,
  /**
   * @param {!proto.api.trading.v1.HistoricalPriceRequest} request
   * @return {!Uint8Array}
   */
  function(request) {
    return request.serializeBinary();
  },
  proto.api.trading.v1.HistoricalPriceResponse.deserializeBinary
);


/**
 * @param {!proto.api.trading.v1.HistoricalPriceRequest} request The
 *     request proto
 * @param {?Object<string, string>} metadata User defined
 *     call metadata
 * @param {function(?grpc.web.RpcError, ?proto.api.trading.v1.HistoricalPriceResponse)}
 *     callback The callback function(error, response)
 * @return {!grpc.web.ClientReadableStream<!proto.api.trading.v1.HistoricalPriceResponse>|undefined}
 *     The XHR Node Readable Stream
 */
proto.api.trading.v1.PriceClient.prototype.getHistoricalPrice =
    function(request, metadata, callback) {
  return this.client_.rpcCall(this.hostname_ +
      '/api.trading.v1.Price/GetHistoricalPrice',
      request,
      metadata || {},
      methodDescriptor_Price_GetHistoricalPrice,
      callback);
};


/**
 * @param {!proto.api.trading.v1.HistoricalPriceRequest} request The
 *     request proto
 * @param {?Object<string, string>=} metadata User defined
 *     call metadata
 * @return {!Promise<!proto.api.trading.v1.HistoricalPriceResponse>}
 *     Promise that resolves to the response
 */
proto.api.trading.v1.PricePromiseClient.prototype.getHistoricalPrice =
    function(request, metadata) {
  return this.client_.unaryCall(this.hostname_ +
      '/api.trading.v1.Price/GetHistoricalPrice',
      request,
      metadata || {},
      methodDescriptor_Price_GetHistoricalPrice);
};


module.exports = proto.api.trading.v1;

