
syntax = "proto3";

package grpc.gateway.testing;

message Empty {}

message EchoRequest {
  string message = 1;
}

message EchoResponse {
  string message = 1;
  int32 message_count = 2;
}

message ServerStreamingEchoRequest {
  // Message string for server streaming request.
  string message = 1;

  // The total number of messages to be generated before the server
  // closes the stream; default is 10.
  int32 message_count = 2;

  // The interval (ms) between two server messages. The server implementation
  // may enforce some minimum interval (e.g. 100ms) to avoid message overflow.
  int32 message_interval = 3;
}

// Response type for server streaming response.
message ServerStreamingEchoResponse {
  // Response message.
  string message = 1;
}

message ClientStreamingEchoRequest {
  string message = 1;
}

message ClientStreamingEchoResponse {
  int32 message_count = 1;
}

// A simple echo service.
service EchoService {
  rpc Echo(EchoRequest) returns (EchoResponse);

  rpc ServerStreamingEcho(ServerStreamingEchoRequest)
      returns (stream ServerStreamingEchoResponse);


//breaktest
 rpc ServerStreamingEchoAbort(ServerStreamingEchoRequest)
     returns (stream ServerStreamingEchoResponse) {}

 rpc ClientStreamingEcho(stream ClientStreamingEchoRequest)
     returns (ClientStreamingEchoResponse);

 rpc FullDuplexEcho(stream EchoRequest) returns (stream EchoResponse);

 rpc HalfDuplexEcho(stream EchoRequest) returns (stream EchoResponse);

}
