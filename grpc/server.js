const grpc = require('@grpc/grpc-js')
const protoLoader = require('@grpc/proto-loader')

const PROTO_PATH = './hello.proto'
const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
  keepCase: true,
  longs: String,
  enums: String,
  defaults: true,
  oneofs: true,
})
//const helloProto = grpc.loadPackageDefinition(packageDefinition).Greeter
const helloProto = grpc.loadPackageDefinition(packageDefinition)

const server = new grpc.Server()

server.addService(helloProto.Greeter.service, {
  SayHello: (call, callback) => {
    callback(null, {greeting: `Hello, ${call.request.name}!`})
  },
})

server.bindAsync('127.0.0.1:50051', grpc.ServerCredentials.createInsecure(), () => {
  server.start()
  console.log('Server running on http://127.0.0.1:50051')
})
