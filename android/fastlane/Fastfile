# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Submit a new Beta Build to Google Play Alpha"
  lane :alpha do
    versionCode = File.read("metadata/versionCode").to_i
    versionCode = versionCode + 1

    f = File.new('metadata/versionCode','w')
    f.write(versionCode)
    f.close

    gradle(
      task: "clean assembleRelease",
      properties: {
        'versionCode' => versionCode
      }
    )
    supply(track:"alpha",
      skip_upload_metadata: true, # Dont upload this things at the moment
      skip_upload_images: true,
      skip_upload_screenshots: true)

    # sh "your_script.sh"
    # You can also use other beta testing services here
  end

  desc "Submit a new Beta Build to Google Play Beta"
  lane :beta do
    versionCode = File.read("metadata/versionCode").to_i
    versionCode = versionCode + 1

    f = File.new('metadata/versionCode','w')
    f.write(versionCode)
    f.close

    gradle(
      task: "clean assembleRelease",
      properties: {
        'versionCode' => versionCode
      }
    )
    supply(track:"beta",
      skip_upload_metadata: true, # Dont upload this things at the moment
      skip_upload_images: true,
      skip_upload_screenshots: true)

    # sh "your_script.sh"
    # You can also use other beta testing services here
  end

  desc "Submit a new InternalTest Build to Google Play Beta"
  lane :internal do
    versionCode = File.read("metadata/versionCode").to_i
    versionCode = versionCode + 1

    f = File.new('metadata/versionCode','w')
    f.write(versionCode)
    f.close

    gradle(
      task: "clean assembleRelease",
      properties: {
        'versionCode' => versionCode
      }
    )
    supply(track:"internal",
      skip_upload_metadata: true, # Dont upload this things at the moment
      skip_upload_images: true,
      skip_upload_screenshots: true)

    # sh "your_script.sh"
    # You can also use other beta testing services here
  end

  desc "Builds the APK file without uploading it"
  lane :buildApk do
    gradle(task: "clean assembleRelease")

    # Find the generated APK file
    apk_path = Dir["app/build/outputs/apk/release/*.apk"].last

    if apk_path
      UI.message("APK built successfully: #{apk_path}")
    else
      UI.error("APK build failed or file not found!")
    end
  end

  desc "Deploy a new version to the Google Play"
  lane :deploy do
    gradle(task: "clean assembleRelease")
    upload_to_play_store(
      root_url: "https://androidpublisher.googleapis.com/",
    )
  end

 
  
end
