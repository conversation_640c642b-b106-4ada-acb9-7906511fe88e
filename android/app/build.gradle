apply plugin: "com.android.application"
apply plugin: "com.google.gms.google-services"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"
apply plugin: 'com.github.johnrengelman.shadow'


/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '..'
    // root = file("../")
    //   The folder where the react-native NPM package is. Default is ../node_modules/react-native
    // reactNativeDir = file("../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../node_modules/@react-native/codegen
    // codegenDir = file("../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../node_modules/react-native/cli.js
    // cliFile = file("../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
}

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    namespace "com.saltlending.mobile"
    defaultConfig {
        applicationId "com.saltlending.mobile"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode project.hasProperty('versionCode') ? project.property('versionCode') as int : 646
        versionName "2.16.67"
        missingDimensionStrategy 'react-native-camera', 'general'
        multiDexEnabled true
    }
    signingConfigs {
        release {
                storeFile file(MOBILEAPP_RELEASE_STORE_FILE)
                storePassword MOBILEAPP_RELEASE_STORE_PASSWORD
                keyAlias MOBILEAPP_RELEASE_KEY_ALIAS
                keyPassword MOBILEAPP_RELEASE_KEY_PASSWORD
        }
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }
    configurations{
        all*.exclude module: 'bcprov-jdk15on'
    }
}

repositories {
    google()
    mavenCentral()
    gradlePluginPortal()
    exclusiveContent {
        forRepository {
            maven {
                url 'https://repo.mobile.jumio.ai'
            }
        }
        filter {
            includeGroup "com.jumio.android"
            includeGroup "com.iproov.sdk"
        }
    }
}


dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")
    implementation("com.facebook.react:flipper-integration")

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
    //implementation project(':lottie-react-native')
    //implementation "com.github.socure-inc:socure-docv:x.y.z"
    implementation("androidx.core:core-splashscreen:1.0.0")
    implementation project(':react-native-jumio-mobilesdk')

    //implementation("com.plaid.link:sdk:3.11.0") {
    //    exclude group: 'org.bouncycastle', module: 'bcprov-jdk15to18'
    //}


    /*
    implementation("org.bouncycastle:bcprov-jdk18on:1.76") {
        exclude group: 'org.bouncycastle', module: 'bcprov-jdk15to18'
    }
    implementation("androidx.work:work-runtime:2.8.1") {
        exclude group: 'androidx.work', module: 'work-runtime-ktx'
    }
    */
    /////////

    /*
    implementation("org.bouncycastle:bcprov-jdk15to18:1.76") {
      exclude group: 'org.bouncycastle', module: 'bcprov-jdk18on'
    }
    implementation 'androidx.work:work-runtime-ktx:2.8.1' {
        exclude group: 'androidx.work', module: 'work-runtime'
    }
    */

    
    /*
    // Plaid dependency
    implementation('com.plaid.link:sdk-core:3.8.0') {
        exclude group: 'org.bouncycastle', module: 'bcprov-jdk15to18'
    }

    // Jumio dependency
    implementation(project(':react-native-jumio-mobilesdk')) {
        exclude group: 'org.bouncycastle', module: 'bcprov-jdk15to18'
    }

    // Add both versions of BouncyCastle explicitly
    implementation 'org.bouncycastle:bcprov-jdk15to18:1.76' // For Jumio
    implementation 'org.bouncycastle:bcprov-jdk18on:1.72' // For Plaid
    */
    

    /*  
    implementation 'org.bouncycastle:bcprov-jdk15to18:1.76'
    implementation 'org.bouncycastle:bcprov-jdk18on:1.72'
    */

}

configurations.all {
    c -> c.resolutionStrategy.eachDependency {
        DependencyResolveDetails dependency ->
            println dependency.requested.group
            if (dependency.requested.group == 'org.bouncycastle') {
                dependency.useTarget 'org.bouncycastle:bcprov-jdk15to18:1.76'
            }
            if (dependency.requested.group == 'androidx.work') {
                dependency.useTarget 'androidx.work:work-runtime:2.8.1'
            }
    }
}

/*
shadowJar {
    relocate 'org.bouncycastle', 'com.myapp.bouncycastle'
}
*/

apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
