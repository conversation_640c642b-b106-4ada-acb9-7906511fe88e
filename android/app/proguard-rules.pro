# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# Keep all classes in your relocated package
-keep class com.myapp.bouncycastle.** { *; }

# Prevent ProGuard from showing warnings about the relocated package
-dontwarn com.myapp.bouncycastle.**


####

# Keep all Bouncy Castle classes
-keep class org.bouncycastle.** { *; }

# Prevent warnings for Bouncy Castle classes
-dontwarn org.bouncycastle.**
