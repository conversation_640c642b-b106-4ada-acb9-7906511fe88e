rootProject.name = 'mobile73'
apply from: file("../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesSettingsGradle(settings)
include ':app'
includeBuild('../node_modules/@react-native/gradle-plugin')


include ':react-native-jumio-mobilesdk'
project(':react-native-jumio-mobilesdk').projectDir = new File(rootProject.projectDir, '../../android')
apply from: file("../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesSettingsGradle(settings)
include ':react-native-jumio-mobilesdk'
project(':react-native-jumio-mobilesdk').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-jumio-mobilesdk/android')

include ':react-native-reanimated'
project(':react-native-reanimated').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-reanimated/android')
