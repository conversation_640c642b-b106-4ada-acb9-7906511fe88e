

buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 23
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "25.1.8937393"
        kotlinVersion = "1.8.0"
    }
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        exclusiveContent {
            forRepository {
                maven {
                    url 'https://repo.mobile.jumio.ai'
                }
            }
            filter {
                includeGroup "com.jumio.android"
                includeGroup "com.iproov.sdk"
            }
        }
    }
    dependencies {
        classpath("com.android.tools.build:gradle:7.2.1")
        classpath("com.google.android.gms:play-services-vision:20.1.3")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:1.5.10")
        classpath("com.google.gms:google-services:4.4.1")
    }
}

plugins {
    id 'com.github.johnrengelman.shadow' version '8.1.1'
}

apply plugin: "com.facebook.react.rootproject"
