syntax = "proto3";

option go_package = "wallet-service-go/clients/grpc/proto/watch/price";

package api.watch.v1;

import "google/protobuf/timestamp.proto";
import "api/queue/v1/wire.proto";

service Watcher {
  // Produces a stream of updates representing the position activity.
  rpc WatchTrades(WatchTradesRequest) returns (stream PositionUpdate) {}
  // Produces a stream of updates describing the position of a specific account or portfolio.
  rpc WatchTradeDetails(WatchDetailsRequest) returns (stream PositionDetailUpdate) {}
  // Produces a stream of updates describing external volume based on watched order executions.
  rpc WatchVolume(WatchExternalVolumeRequest) returns (stream ExternalVolumeUpdate) {}
  // Produces a stream response of strategy order events aggregated by strategy identifier.
  rpc WatchOrdersByStrategy(StrategyOrderDetailRequest) returns (stream StrategyOrderEvent) {}
  // Produces a stream response of strategy update events aggregated by strategy identifier.
  rpc WatchStrategyStatus(StrategyStatusDetailRequest) returns (stream StrategyStatusEvent) {}
  // Produces a response of historical spreader strategies
  rpc ListStrategyHistory(ListStrategyRequest) returns (ListStrategyResponse) {}
  // Produces a response of an individual spreader strategy
  rpc GetStrategy(GetStrategyRequest) returns (Strategy) {}
}

// WatchTradesRequest requests the stream of updates
// to observe the position state over time for accounts and portfolios.
message WatchTradesRequest {
}

message WatchDetailsRequest {
  // Identifies the account or portfolio for which position updates were requested.
  uint32 entity_id = 1;
}

message WatchExternalVolumeRequest {}

message ExternalVolumeUpdate {
  // Map of instrument IDs to market volume updates.
  map<uint64, ExternalVolume> market_volume_update = 1;
}

message ExternalVolume {
  google.protobuf.Timestamp execution_timestamp = 1;
  string buys = 2;
  string sells = 3;
  string total = 4;
}

message PositionUpdate {
  // Timestamp of the position state.
  google.protobuf.Timestamp timestamp = 1;

  // This is a map of entity IDs ( account or portfolio ) to entity position.
  // If a entity ID is not included in this map, then its position has *not* changed sine the
  // last `PositionUpdate` in which they were present. If as entity's ID
  // *is* included in this map, the corresponding value represents a complete
  // snapshot of the entity's position in the current time window.
  map<uint32, Position> position_update = 2;
}

message PositionDetailUpdate {
  // Timestamp of the position state.
  google.protobuf.Timestamp timestamp = 1;
  // Account or Portfolio id.
  uint32 entity_id = 2;
  // Position associated with specific account or portfolio.
  Position position_update = 3;
}

message Position {
  // Indicates whether the position is associated with an account or portfolio.
  enum Kind {
    // Indicates position is associated with an account.
    ACCOUNT = 0;
    // Indicates position is associated with a portfolio.
    PORTFOLIO = 1;
  }
  Kind kind = 1;
  // The name of the account or portfolio.
  string entity_name = 2;
  uint32 asset_id = 3;
  string asset_name = 4;
  string buy_quantity = 5;
  string sell_quantity = 6;
  string buy_value = 7;
  string sell_value = 8;
  string mean_buy_value = 9;
  string mean_sell_value = 10;
  string position_value = 11;
  string currency = 12;
  string five_min_pnl = 13;
  string thirty_min_pnl = 14;
  string one_hour_pnl = 15;
  string session_pnl = 16;
  string session_fees = 17;
}

message StrategyStatusDetailRequest {
  // Specifies which strategy updates should be streamed (`template_alias:ticket_alias-SubN`)
  string strategy_identifier = 1;
}

message StrategyStatusEvent {
  // In the format of `template_alias:ticket_alias-SubN` where N is some seq number
  string strategy_identifier = 1;

  // A strategy event
  oneof event {
    AnnounceStrategy announce_strategy = 2;
    AnnounceStrategyOn announce_strategy_on = 3;
    AnnounceStrategyOff announce_strategy_off = 4;
    AnnounceStrategyMode announce_strategy_mode = 5;
    AnnounceStrategyConfig strategy_config = 6;
    StrategyAlphaUpdate strategy_alpha_update = 7;
  };

  message AnnounceStrategy {
    google.protobuf.Timestamp timestamp = 1;
    uint64 strategy_order_token = 2;
    string strategy_identifier = 3;
    uint64 application_instance_token = 4;
  }

  message AnnounceStrategyOn {
    google.protobuf.Timestamp timestamp = 1;
    string strategy_identifier = 2;
    uint64 application_instance_token = 3;
  }

  message AnnounceStrategyOff {
    google.protobuf.Timestamp timestamp = 1;
    string strategy_identifier = 2;
    uint64 application_instance_token = 3;
  }

  message AnnounceStrategyMode {
    google.protobuf.Timestamp timestamp = 1;
    string strategy_identifier = 2;
    enum StrategyMode
    {
        UNKNOWN = 0; 
        NORMAL = 1;
        CLOSE_ONLY = 2;
        PRECAUTIONARY_CLOSE_ONLY = 3;
        LIQUIDATE = 4;
        DEBUG = 5;
        COMPLETED = 6;
    }
    StrategyMode mode = 3;
  }

  message AnnounceStrategyConfig {
    google.protobuf.Timestamp timestamp = 1;
    StrategyConfig config = 2;
  }

  message StrategyAlphaUpdate {
    google.protobuf.Timestamp timestamp = 1;
    string strategy_identifier = 2;
    StrategyStats value = 3;
  }
}

message StrategyOrderDetailRequest {
  // Specifies which strategy order events should be streamed.
  string ticket_alias = 1;
}

  


message StrategyOrderEvent {
  // In the format of `template_alias:ticket_alias-SubN` where N is some seq number
  string strategy_identifier = 1;
  // In the format of `strategy_order_token:strategy_order_seq_num`
  string strategy_order_id = 2;
  // A order event
  oneof event {
    // `announce_new_strategy_order`
    NewStrategyOrder new_strategy_order = 3;
    // `announce_new_market_order`
    NewMarketOrder new_market_order = 4;
    // `announce_cancel_market_order`
    // Note that this does not necessarily mean that the order has been canceled, only that a cancel request was sent.
    CancelSent cancel_sent = 5;
    // market has acknowledged an order `order_market_ack`
    OrderAck order_ack = 6;
    // `order_market_execution`
    OrderExecution execution = 7;
    // `order_market_canceled`
    MarketCanceled market_canceled = 8;
    // `order_market_rejected`
    Rejected rejected = 9;
    // `manual_reject_market_order`
    ManualReject manual_reject = 10;
    // `market_order_completely_filled`
    CompletelyFilled filled = 11;
  };

  message NewStrategyOrder {
    google.protobuf.Timestamp timestamp = 1;
    string strategy_order_id = 2;
    string strategy_identifier = 3;
    queue.v1.OrderType order_type = 4;
    bool is_buy = 5;
    string quantity = 6;
    string show_quantity = 7;
    string price = 8;
    uint64 instrument_id = 9;
    uint32 account_id = 10;
    uint32 portfolio_id = 11;
  }

  message NewMarketOrder {
    google.protobuf.Timestamp timestamp = 1;
    string strategy_order_id = 2;
    string marvel_order_id = 3;
    queue.v1.OrderType order_type = 5;
    queue.v1.OrderSide side = 6;
    string quantity = 7;
    string show_quantity = 8;
    string price = 9;
    uint64 instrument_id = 10;
    uint32 account_id = 11;
    uint32 portfolio_id = 12;
    uint32 order_gateway = 13;
    uint32 market_center_id = 14;
  }

  message CancelSent {
    google.protobuf.Timestamp timestamp = 1;
    // marvel_order_id
    string canceled_order_id = 2;
    string strategy_order_id = 3;
  }

  message OrderAck {
    google.protobuf.Timestamp message_receive_time = 1;
    uint32 gateway_id = 2;
    string marvel_order_id = 3;
  }

  message OrderExecution {
    string marvel_order_id = 2;
    string strategy_order_id = 3;
    google.protobuf.Timestamp timestamp = 4;
    google.protobuf.Timestamp execution_time = 5;
    google.protobuf.Timestamp receive_time = 6;
    queue.v1.OrderType order_type = 7;
    queue.v1.OrderSide side = 8;
    string price = 9;
    string quantity = 10;
    uint64 instrument_id = 11;
    uint32 account_id = 12;
    uint32 portfolio_id = 13;
    uint32 order_gateway = 14;
  }

  message MarketCanceled {
    uint32 gateway_id = 1;
    string marvel_order_id = 2;
    string exchange_order_id = 3;
    enum CanceledReason
    {
      UNKNOWN = 0;
      REQUESTED = 1;
      POST_ONLY_VIOLATION = 2;
      FILL_OR_KILL_MISS = 3;
      FORCE_CANCEL = 4;
      LOST_ORDER = 5;
      DISCONNECTED = 6;
    }
    CanceledReason reason = 4;
    google.protobuf.Timestamp message_receive_time = 5;
  }

  message Rejected {
    google.protobuf.Timestamp timestamp = 1;
    uint32 gateway_id = 2;
    string marvel_order_id = 3;
    enum OrderRejectReason
    {
      BROKER_OPTION = 0;
      UNKNOWN_SYMBOL = 1;
      EXCHANGE_CLOSED = 2;
      ORDER_EXCEEDS_LIMIT = 3;
      TOO_LATE_TO_ENTER = 4;
      UNKNOWN_ORDER = 5;
      DUPLICATE_ORDER = 6;
      DUPLICATE_OF_VERBAL = 7;
      STALE_ORDER = 8;

      // INTERNALLY DEFINED
      POST_ONLY_VIOLATION = 50;
      TOO_MANY_CANCEL_REJECTS = 51;
      INTERNAL_EXCEPTION = 52;
      EXTERNAL_EXCEPTION = 53;
      OUTSIDE_LIMITS = 54;
      INVALID_PRICE = 55;
      INVALID_TYPE = 56;
      UNDEFINED = 255;
    }
    OrderRejectReason reject_reason_code = 4;
    string reject_text = 5;
  }

  message ManualReject {
    google.protobuf.Timestamp timestamp = 1;
    string marvel_order_id = 2;
  }

  message CompletelyFilled {
    google.protobuf.Timestamp timestamp = 1;
    string marvel_order_id = 2;
  }
}

message ListStrategyRequest {
  // The maximum number of strategies to return in the response.
  uint32 page_size = 1;

  // A pagination token returned from a previous call to `ListStrategyHistory`
  // that indicates where this listing should continue from.
  string page_token = 2;
}

message ListStrategyResponse {
  // paginated response of historical strategies
  repeated Strategy strategies = 1;
  // A pagination token returned from a previous call to `ListStrategyHistory`
  // that indicates from where listing should continue.
  string next_page_token = 2;
  // Total number of strategy records
  uint64 full_count = 3;
}

message GetStrategyRequest {
// Specifies which strategy's historical data to return.
string strategy_identifier = 1;
}

message Strategy {
  google.protobuf.Timestamp timestamp = 1;
  // The Type of strategy Currently only one supported is "spreader_strategy_v1"
  string strategy_type = 2;
  // In the format of `template_alias:ticket_alias(-SubN)` where N is some seq number
  string strategy_identifier = 3;
  // Config of the Strategy
  StrategyConfig config = 4;
  // Avg fill data of the Strategy
  StrategyStats stats = 5;
}

message StrategyProduct
{
  // id of instrument to use
  uint64 instrument_id = 1;
  // id of account to trade in
  uint32 account_id = 2;
  // qty of this instrument per "ticket"
  string quantity = 3;
  // factor on this instrument set to 1 unless fixed income
  string factor = 4;
  // divisor on this instrument set to 1 unless fixed income
  string divisor = 5;
  // id of gateway to route orders
  uint32 gateway_id = 6;
  // maximum unhedged amount
  string maximum_unhedged_amount = 7;
  // disable active orders for this leg
  bool disable_actives = 8;
  // disable passive orders for this leg
  bool disable_passives = 9;
  // is this the reference leg for dynamic hdeging
  bool is_reference = 10;
}

message StrategyStats
{
  // average execution spread price
  string average_spread_price_buy = 1;
  // average execution spread price
  string average_spread_price_sell = 2;
}

message StrategyConfig
{
  // name of trader adding the strategy
  string trader = 1;
  // id of the portfolio to trade strategy out of
  uint32 portfolio = 2;
  // array of products that are part of this strategy
  repeated StrategyProduct products = 3;
  // level of spread to enter into positions at
  string spread_level = 4;
  // total size to trade in the strategy
  string total_size = 5;
  // direction to trade strategy in (Buy,Sell)
  string direction = 6;
  // "time in force" (how long this should stay open) of the strategy (DAY, GTC)
  string tif = 7;
  // Size Multiplier (optional default is 1)
  uint32 size_multiplier = 8;
  // Auto Cover Differential
  string auto_cover_diff = 9;
  // Is auto cover enabled
  bool auto_cover_enabled = 10;
  // Max Trips Reserved for Future Use.
  uint32 max_trips = 11;
  // Type of hedging (STATIC,DYNAMIC)
  string hedging_type = 12;
}