syntax = "proto3";

option go_package = "wallet-service-go/clients/grpc/proto/trading/price";

package api.trading.v1;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

// Allows observers to stream price events for a given `WatchPriceRequest` filer.
service Price {
    // Retrieve all asset pairs by exchange
    rpc GetAssetPairs (google.protobuf.Empty) returns (AssetPairsResponse) {}
    // Produces a stream of VWAP price events for the given filter.
    rpc WatchPrice (PriceRequest) returns (stream PriceEvent) {}
    // Produces a unary of current VWAP price at this given time.
    rpc GetPrice (UnaryPriceRequest) returns (PriceResponse) {}
    // Produces a unary of VWAP price over a given time period
    rpc GetHistoricalPrice (HistoricalPriceRequest) returns (HistoricalPriceResponse) {}
}

message AssetPairsResponse {
    repeated AssetPair asset_pairs = 1;
}

// Start watching price events with the provided filter.
message PriceRequest {
    // Filter on an asset pair ids.
    repeated AssetPairId asset_pairs = 1;
}

message UnaryPriceRequest {
    // Filter on an asset pair ids.
    repeated AssetPairId asset_pairs = 1;
    oneof parameter {
        int64 time = 4;
    } 
}

message PriceResponse {
    repeated PriceEvent prices = 1;
}

message HistoricalPriceRequest {
    // Filter on an asset pair ids.
    repeated AssetPairId asset_pairs = 1;
    TimeDenomination time_denomination = 2;
    uint32 time_duration = 3;
    oneof parameter {
        int64 time_end = 4;
    }
}

message HistoricalPriceResponse {
    repeated HistoricalPrice historical_prices = 1;
}

enum TimeDenomination {
	SEC = 0;
    MIN = 1;
    HRS = 2;
    DAY = 3;
}

enum PriceSource {
    VWAP = 0;
    COIN_API = 1;
    STATIC = 2;
}

message PriceEvent {
    // The system time when this update was recorded.
    google.protobuf.Timestamp time = 1;
    AssetPair asset_pair = 2;
    string price = 3;
    PriceSource source = 4;
}

message HistoricalPriceEvent {
    google.protobuf.Timestamp time = 1; 
    string price = 3;
}

message HistoricalPrice {
    AssetPair asset_pair = 1;
    repeated HistoricalPriceEvent quotes = 2;
    PriceSource source = 3;
}

// Asset ticker info
message AssetPair {
    Currency base_currency = 1;
    Currency quote_currency = 2;
}

message AssetPairId {
    uint32 base_currency_id = 1;
    uint32 quote_currency_id = 2;
}

message Currency {
    uint32 asset_id = 1;
    string display_name = 2;
    string description = 3;
    string ticker = 4;
}