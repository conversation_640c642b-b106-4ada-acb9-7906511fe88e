syntax = "proto3";

option go_package = "wallet-service-go/clients/grpc/proto/trading/gateway";

package api.trading.v1;

import "google/protobuf/timestamp.proto";
import "api/queue/v1/wire.proto";

service TradingGateway {
    rpc Quote(RequestForQuote) returns (stream QuoteResponse) {}
    rpc Execute(ExecutionRequest) returns (TransactionExecutionResponse) {}
}

message RequestForQuote {
    bool single_quote = 1;
    Currency base_currency = 2;
    Currency quote_currency = 3;
    queue.v1.Rational quantity = 4;
    queue.v1.OrderSide side = 5;

    enum Currency {
        USD = 0;
        BTC = 1;
        ETH = 2;
        LTC = 3;
        BCH = 4;
        DASH = 5;
        XRP = 6;
        DOGE = 7;
        TUSD = 8;
        USDC = 9;
        USDP = 10;
        PAXG = 11;
    }
}

message QuoteResponse {
    UUID request_id = 1;
    queue.v1.Rational price = 2;
    queue.v1.Rational quantity = 3;
    google.protobuf.Timestamp expiration_time = 4;
}

message ExecutionRequest {
    UUID request_id = 1;
    optional queue.v1.Rational limit_price = 2;
    queue.v1.OrderType order_type = 3;
}

message TransactionExecutionResponse {
    // An execution response event.
    oneof response_event {
        TransactionExecutionAck execution_fills = 1;
        Rejected rejected = 2;
    };

    message TransactionExecutionAck {
        UUID request_id = 1;
        repeated Fill fills = 2;
    }
    message Rejected {
        UUID request_id = 2;
        string reject_reason = 3;
    }
}

message Fill {
    queue.v1.Rational price = 1;
    queue.v1.Rational quantity = 2;
}

message UUID {
    string value = 1;
}

