syntax = "proto3";

option go_package = "wallet-service-go/clients/grpc/proto/common/net";

package api.common.v1;

message IPAddress {
    oneof ip {
        fixed32 ipv4 = 1;
        IPv6 ipv6 = 2;
    }
}

message IPNetwork {
    IPAddress ip = 1;
    uint32 prefix_len = 2;
}

message IPv6 {
    fixed64 first = 1;
    fixed64 last = 2;
}

message TcpAddress {
    IPAddress ip = 1;
    uint32 port = 2;
}
