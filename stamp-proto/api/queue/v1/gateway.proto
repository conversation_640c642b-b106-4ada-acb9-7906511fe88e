syntax = "proto3";

option go_package = "wallet-service-go/clients/grpc/proto/queue/gateway";

package api.queue.v1;


import "api/queue/v1/wire.proto";


service SpreaderGateway
{
    // This is the call back you will listen to constantly for streaming requests
    // For Example when you request order book the book will be streamed via
    // this call
    rpc StreamCallback(SpreaderRequest) returns (stream WireApplicationEvent)
    {}


    // This will send a new order from the client into the system.
    rpc NewOrder(GatewayNewOrder) returns (ErrorResponse)
    {}
    // This will send a new order from the client into the system.
    rpc CancelOrder(CancelGatewayOrder) returns (ErrorResponse)
    {}

    // This will request the order book for a given instrument.
    // The response will come over the StreamCallBackMethod with a WireMultiPriceIndicationResponse message
    rpc RequestBook(GatewayBookRequest) returns (ErrorResponse)
    {}
    // This will cancel a previous order book request and end the stream.
    rpc CancelRequestBook(CancelGatewayBookRequest) returns (ErrorResponse)
    {}

    // Adds a "defined" strategy ticket with an alias.  Note that this does not actually submit a strategy order,
    // but rather the definition of an order that could be submitted.
    rpc AddStrategyTemplate(StrategyTemplate) returns (StrategyTemplateResponse)
    {}

    // Deletes a "defined" strategy ticket with an alias.
    rpc DeleteStrategyTemplate(StrategyTemplate) returns (ErrorResponse)
    {}


    // Adds a "pending" strategy "ticket" that has the additional parameters needed to actually
    // trade the strategy, e.g. direction of the trade, and trade level.
    rpc AddStrategyTicket(StrategyTicket) returns (StrategyTicketResponse)
    {}

    // Deletes a strategy "ticket".
    rpc DeleteStrategyTicket(StrategyTicket) returns (ErrorResponse)
    {}

    // Updates the value of a parameter for a given strategy "template"
    rpc UpdateStrategyParam(StrategyParam) returns (StrategyTemplateResponse)
    {}
    // Updates the value of a parameter for a given strategy "ticket"
    rpc UpdateStrategyTicketParam(StrategyTicketParam) returns (StrategyTicketResponse)
    {}

    // Start or Stop a particular strategy "ticket"
    rpc StartStopStrategy(StartorStopStrategy) returns (ErrorResponse)
    {}

    rpc StartAllStrategies(SpreaderRequest) returns (ErrorResponse)
    {}
    rpc StopAllStrategies(SpreaderRequest) returns (ErrorResponse)
    {}

    // rpcs to retrieve information from the trading app, assets, instruments etc..
    // this can be used upon startup or just to refresh things.
    rpc RequestAssets(SpreaderRequest) returns (AssetResponse)
    {}
    rpc RequestInstruments(SpreaderRequest) returns (InstrumentResponse)
    {}
    rpc RequestAccounts(SpreaderRequest) returns (AccountsResponse)
    {}
    rpc RequestPortfolios(SpreaderRequest) returns (PortfoliosResponse)
    {}
    rpc RequestTraders(SpreaderRequest) returns (TradersResponse)
    {}
    rpc RequestStrategyTemplates(SpreaderRequest) returns (StrategyTemplateResponse)
    {}
    rpc RequestStrategyTickets(SpreaderRequest) returns (StrategyTicketResponse)
    {}
    rpc RequestAvailableGateways(SpreaderRequest) returns (GatewaysResponse)
    {}
    rpc RequestExecutions(ExecutionsRequest) returns (ExecutionsResponse)
    {}

    // This is the call back you will listen to constantly for streaming prices
    rpc StreamPriceCallback(StreamPriceRequest) returns (stream StreamPriceResponse)
    {}

    // This will send a smart stabilization or conversion into the system.
    rpc ExecuteTrade(TradeRequest) returns (StrategyTicketResponse)
    {}
}

message SpreaderRequest
{}


message ErrorResponse
{
    repeated string error_string = 2;
}

message GatewayNewOrder
{
    // the entity id of the client doing the request>
    uint32 client_id = 1;
    // ID of the instrument the order is for
    uint64 instrument_id = 2;
    // unique id for this order. must be unique across all of time. uuid is suggested>
    string order_id = 3;
    // Side of the order
    OrderSide order_side = 4;
    // Type of order
    OrderType order_type = 5;
    // TODO: rational
    Rational quantity = 6;
    // Price of the order (if applicable)
    Rational order_price = 7;
}
message CancelGatewayOrder
{
    // unique id for this order. must be unique across all of time. uuid is suggested>
    string order_id = 1;
    // unique id of this cancel message
    string cancel_id = 2;
}


message TradeRequest
{
    // the entity id of the client doing the request>
    uint32 client_id = 1;
    // asset id of from instrument EX, 50000 for BTC
    // Can get this ID from Request Assets API
    uint64 from_asset_id = 2;
    // asset id of to instrument Set to 1 for stabilization
    uint64 to_asset_id = 3;
    // direction to trade strategy in (Buy,Sell)
    string direction = 4;
    // Quantity to Buy/Sell
    string quantity = 5;
    // Unique ID to identify this transaction Could be loan Id.
    string transaction_id = 6;
}


message GatewayBookRequest
{
    // the entity id of the client doing the request>
    uint32 client_id = 1;
    // ID of the instrument the order is for
    uint64 instrument_id = 2;
    // Type of Book "L1" for level 1 and "L2" for level 2
    string book_type = 3;
    // unique id for this request. must be unique across all of time. uuid is suggested>
    string request_id = 4;
}
message CancelGatewayBookRequest
{
    // id used to make the GatewayBookRequest
    string request_id = 1;
}

message StrategyProduct
{
    // id of instrument to use
    uint64 instrument_id = 1;
    // id of account to trade in
    uint32 account_id = 2;
    // qty of this instrument per "ticket"
    string quantity = 3;
    // factor on this instrument set to 1 unless fixed income
    string factor = 4;
    // divisor on this instrument set to 1 unless fixed income
    string divisor = 5;
    // id of gateway to route orders
    uint32 gateway_id = 6;
    // maximum unhedged amount
    string maximum_unhedged_amount = 7;
    // disable active orders for this leg
    bool disable_actives = 8;
    // disable passive orders for this leg
    bool disable_passives = 9;
    // is this the reference leg for dynamic hdeging
    bool is_reference = 10;
}

message StrategyConfig
{
    // name of trader adding the strategy
    string trader = 1;
    // id of the portfolio to trade strategy out of
    uint32 portfolio = 2;
    // array of products that are part of this strategy
    repeated StrategyProduct products = 3;
    // level of spread to enter into positions at
    string spread_level = 4;
    // total size to trade in the strategy
    string total_size = 5;
    // direction to trade strategy in (Buy,Sell)
    string direction = 6;
    // "time in force" (how long this should stay open) of the strategy (DAY, GTC)
    string tif = 7;
    // Size Multiplier (optional default is 1)
    uint32 size_multiplier = 8;
    // Auto Cover Differential
    string auto_cover_diff = 9;
    // Is auto cover enabled
    bool auto_cover_enabled = 10;
    // Max Trips Reserved for Future Use.
    uint32 max_trips = 11;
    // Type of hedging (STATIC,DYNAMIC)
    string hedging_type = 12;
}

message StrategyTemplate
{
    // The Type of strategy Currently only one supported is "spreader_strategy_v1"
    string strategy_type = 1;
    // Alias
    string template_alias = 2;
    // Config of the Strategy
    StrategyConfig config = 3;
}

message LegStats
{
    uint64 instrument_id = 1;
    string num_buy_executions = 2;
    string num_sell_executions = 3;
    string dynamic_size = 4;
}

message StrategyTicket
{
    // The Type of strategy
    string strategy_type = 1;
    // the alias of the template to use
    string template_alias = 2;
    // the alias to use for this ticket
    string ticket_alias = 3;
    // Config of the Strategy
    StrategyConfig config = 4;
    // Is the strategy on or off
    // strategy will be off when
    // creating a new ticket
    bool is_running = 5;
    // average execution spread price
    string average_spread_price_buy = 6;
    // Trading stats per leg
    repeated LegStats leg_stats = 7;
    // average execution spread price
    string average_spread_price_sell = 8;
    // The strategy is at it's full open position
    bool at_full_position_open = 9;
    // The strategy is at it's full close position
    bool at_full_position_close = 10;
}

// Updates the value of a parameter for a given strategy "template"
message StrategyParam
{
    // The Type of strategy
    string strategy_type = 1;
    // the alias of the template to use
    string template_alias = 2;
    // Parameter to Update
    oneof parameter
    {
        // level of spread to enter into positions at
        string spread_level = 3;
        // total size to trade in the strategy
        string total_size = 4;
        // "time in force" (how long this should stay open) of the strategy (DAY, GTC)
        string tif = 5;
        // Size Multiplier (optional default is 1)
        uint32 size_multiplier = 6;
        // Auto Cover Differential, Not Used Reserved for future Use
        string auto_cover_diff = 7;
    }
}

message StrategyTicketParam
{
    // The Type of strategy
    string strategy_type = 1;
    // the alias of the template to use
    string template_alias = 2;
    // the alias to use for this ticket
    string ticket_alias = 3;

    oneof parameter
    {
        // level of spread to enter into positions at
        string spread_level = 4;
        // Size Multiplier (optional default is 1)
        uint32 size_multiplier = 5;
        // Auto Cover Differential, Not Used Reserved for future Use
        string auto_cover_diff = 6;
        // Is auto cover enabled
        bool auto_cover_enabled = 7;
    }
}

message StartorStopStrategy
{
    // The Type of strategy
    string strategy_type = 1;
    // the alias of the template to use
    string template_alias = 2;
    // the alias to use for this ticket
    string ticket_alias = 3;
    // set to true for start
    bool start_strategy = 4;
}

message Assets
{
    // asset id
    uint64 id = 1;
    // display name
    string display_name = 2;
    // ticker
    string ticker = 3;
}

message AssetResponse
{
    // Any errors will be reported here blank for success
    repeated string error_string = 1;
    repeated Assets assets = 2;
}


message Instruments
{
    // Instrument id
    uint64 id = 1;
    // description
    string description = 2;
    // ticker
    string ticker = 3;
    // asset id
    uint64 asset_id = 4;
    // contra asset id
    uint64 contra_asset_id = 5;
}

message InstrumentResponse
{
    // Any errors will be reported here blank for success
    repeated string error_string = 1;
    repeated Instruments instruments = 2;
}


message Accounts
{
    // Account id
    uint64 id = 1;
    // description
    string description = 2;
}
message AccountsResponse
{
    // Any errors will be reported here blank for success
    repeated string error_string = 1;
    repeated Accounts accounts = 2;
}

message Portfolios
{
    // Account id
    uint64 id = 1;
    // description
    string description = 2;
}
message PortfoliosResponse
{
    // Any errors will be reported here blank for success
    repeated string error_string = 1;
    repeated Portfolios portfolios = 2;
}

message Traders
{
    // Account id
    uint64 id = 1;
    // name
    string name = 2;
}
message TradersResponse
{
    // Any errors will be reported here blank for success
    repeated string error_string = 1;
    repeated Traders traders = 2;
}

message StrategyTemplateResponse
{
    // Any errors will be reported here blank for success
    repeated string error_string = 1;
    repeated StrategyTemplate strategy_templates = 2;
}

message StrategyTicketResponse
{
    // Any errors will be reported here blank for success
    repeated string error_string = 1;
    repeated StrategyTicket strategy_ticket = 2;
}

message FillInfo
{
    uint64 instrument_id = 1;
    //< (signed) number of units traded
    string exec_size = 2;
    ///< Average price traded
    string exec_price = 3;
}

message StrategyFillInfo
{
    string side = 1;
    ///< number of full spreads opened
    string num_spreads_opened = 2;
    ///< Spread price traded at
    string entry_spread_price = 3;
    ///< number of full spreads closed
    string num_spreads_closed = 4;
    ///< Spread price traded at
    string close_spread_price = 5;
    ///< Individual legs traded.
    repeated FillInfo legs = 6;
    // average execution spread price
    string average_spread_price_buy = 7;
    // Trading stats per leg
    repeated LegStats leg_stats = 8;
    // average execution spread price
    string average_spread_price_sell = 9;
    // The strategy is at it's full open position
    bool at_full_position_open = 10;
    // The strategy is at it's full close position
    bool at_full_position_close = 11;
}
message ExecutionsRequest
{
    // The Type of strategy
    string strategy_type = 1;
    // the alias of the template to use
    string template_alias = 2;
    // the alias to use for this ticket
    string ticket_alias = 3;
}


message PendingOrderInfo
{
    ///< Instrument Id of order
    uint64 instrument_id = 1;
    ///< Type of Order
    string type = 2;
    ///< order side
    string side = 3;
    ///< initial size of order
    string initial_size = 4;
    ///< remaning size
    string remaining_size = 5;
    ///< order price
    string price = 6;
    ///< Time of order
    string order_time = 7;
}

message ExecutionsResponse
{
    // Any errors will be reported here blank for success
    repeated string error_string = 1;
    repeated StrategyFillInfo executions = 2;
    repeated PendingOrderInfo pending = 3;
}

message Gateways
{
    // The ID for the gateway
    uint64 gateway_id = 1;
    // Description of this gateway
    string description = 2;
}


message GatewaysResponse
{
    // Any errors will be reported here blank for success
    repeated string error_string = 1;
    repeated Gateways gateways = 2;
}

message StreamPriceRequest
{
    // name of trader adding the strategy
    string trader = 1;
    // id of the portfolio to trade strategy out of
    uint32 portfolio = 2;
    // instrument Ids to get streaming data
    repeated uint64 instrument_id = 3;
}

// Note not all these fields will be populated
// it depends on the update from the exchange
message InstrumentPrices
{
    repeated string bid_prices = 1;
    repeated string bid_sizes = 2;
    repeated string ask_prices = 3;
    repeated string ask_sizes = 4;
    string mid_price = 5;
    string last_price = 6;
    string last_size = 7;
}
message StreamPriceResponse
{
    repeated string error_string = 1;
    // the instrument the message is for
    uint64 instrument_id = 2;
    // pricing data for this instrument
    InstrumentPrices prices = 3;
}
