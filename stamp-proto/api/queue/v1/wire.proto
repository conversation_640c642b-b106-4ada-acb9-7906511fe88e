syntax = "proto3";

option go_package = "wallet-service-go/clients/grpc/proto/queue/wire";

package api.queue.v1;

import "google/protobuf/timestamp.proto";
import "api/common/v1/net.proto";

service MessageQueue
{
    rpc Observe(DescriptorRequest) returns (stream WireApplicationEvent)
    {}

    // Response Manager
    rpc ResponseCallback(WireResponseManagerMessage) returns (MessageAck)
    {}
}


message MessageAck
{}

message DescriptorRequest
{}

message Rational
{
    // Improper fraction representation to denote that
    // the numerator can be greater than the denominator
    int64 numerator = 1;
    uint64 denominator = 2;
}

message StrategyOrderId
{
    // The unique token used by this generator for creating ids
    uint64 generator_token = 1;
    // The last sequence number doled out
    uint32 seq_num = 2;
}

message MarvelOrderId
{
    // The time this factory was created.  Used to ensure uniqueness between nexuses.
    google.protobuf.Timestamp base_time = 1;
    // The next sequence number to be used
    uint64 seq_num = 2;
}

message WireResponseManagerMessage
{
    uint64 application_instance_token = 1;
    oneof serialized_message
    {
        WireOrderMarketExecution order_market_execution = 2;
        WireRequestAllFills get_all_fills = 3;
        WireRequestAllTransfers get_all_transfers = 4;
        WireSubscribeAllFills subscribe_all_fills = 5;
        WireSubscribeAllTransfers subscribe_all_transfers = 6;
        WireSubscribeAllPositionAdjustments subscribe_all_position_adjustments = 7;
        WireGetPortfolioPositions get_portfolio_positions = 8;
        WireGetAllPortfolioPositions get_all_portfolio_positions = 9;
        WireGetAccountPositions get_account_positions = 10;
        WireGetAllAccountPositions get_all_account_positions = 11;
        WireSetPortfolioPosition set_portfolio_position = 12;
        WireSetAccountPosition set_account_position = 13;
        WirePortfolioPositionsReply portfolio_positions_reply = 14;
        WireAccountPositionsReply account_positions_reply = 15;
        WireExternalPayment announce_external_payment = 16;
        WireTransferAccountPosition transfer_account_position = 17;
        WireTransferPortfolioPosition transfer_portfolio_position = 18;
        WireExternalTransfer announce_external_transfer = 19;
        WireRealizedPNLCredit announce_realized_pnl_credit = 20;
        WireTransactionApplied announce_transaction = 21;
        WireAnnounceApplicationInstanceBootstrapStart announce_application_instance_bootstrap_start = 22;
        WireAnnounceApplicationInstanceBootstrapEnd announce_application_instance_bootstrap_end = 23;
        WireGetDataMutantValue get_data_mutant_value = 24;
        WireBootstrapBoolMutantValue bootstrap_bool_mutant_value = 25;
        WireGetBoolMutantValue get_bool_mutant_value = 26;
        WireBootstrapDataMutantValue bootstrap_data_mutant_value = 27;
    }
}

message WireApplicationEvent
{
    google.protobuf.Timestamp message_time = 2;
    uint64 application_instance_token = 3;
    oneof application_message
    {
        WireHostStartup host_startup = 4;
        WireManualStrategyOrder manual_strategy_order = 5;
        WireCancelAllOrders cancel_all_orders = 6;
        WireCancelOrdersInstrument cancel_orders_instrument = 7;
        WireCancelReplaceOrder cancel_replace_order = 8;
        WireManualRejectMarketOrder manual_reject_market_order = 9;
        WireFlowOrderReceived flow_order_received = 10;
        WireFlowOrderAck flow_order_ack = 11;
        WireFlowOrderReject flow_order_reject = 12;
        WireFlowOrderExecution flow_order_execution = 13;
        WireFlowOrderExpired flow_order_expired = 14;
        WireFlowOrderCancel flow_order_cancel = 15;
        WireFlowOrderCancelAck flow_order_cancel_ack = 16;
        WireFlowOrderCancelReject flow_order_cancel_reject = 17;
        WireRiskLimitExceeded risk_limit_exceeded = 18;
        WireStrategyRiskLimitExceeded strategy_risk_limit_exceeded = 19;
        WireGatewaySentOrder gateway_sent_order = 20;
        WireGatewayRejectedOrder gateway_rejected_order = 21;
        WireStrategyOrderRejected strategy_order_rejected = 22;
        WireNotifyMarketOrderCompletelyFilled market_order_completely_filled = 23;
        WireOrderMarketAck order_market_ack = 24;
        WireOrderMarketExecution order_market_execution = 25;
        WireOrderMarketCanceled order_market_canceled = 26;
        WireOrderMarketRejected order_market_rejected = 27;
        WireOrderMarketExecutionCanceled order_market_execution_canceled = 28;
        WireOrderMarketCancelRejected order_market_cancel_rejected = 29;
        WireUnparsableMarketMessage unparsable_market_message = 30;
        WireUnattributableMarketExecution unattributable_market_execution = 31;
        WireRequestAllOpenOrders get_all_open_orders = 32;
        WireRequestOpenOrdersForInstrument get_open_orders_for_instrument = 33;
        WireRequestOrderStatus request_order_status = 34;
        WireRequestAllFills get_all_fills = 35;
        WireRequestAllTransfers get_all_transfers = 36;
        WireSubscribeAllFills subscribe_all_fills = 37;
        WireSubscribeAllTransfers subscribe_all_transfers = 38;
        WireSubscribeAllPositionAdjustments subscribe_all_position_adjustments = 39;
        WireGetPortfolioPositions get_portfolio_positions = 40;
        WireGetAllPortfolioPositions get_all_portfolio_positions = 41;
        WireGetAccountPositions get_account_positions = 42;
        WireGetAllAccountPositions get_all_account_positions = 43;
        WireSetPortfolioPosition set_portfolio_position = 44;
        WireSetAccountPosition set_account_position = 45;
        WirePortfolioPositionsReply portfolio_positions_reply = 46;
        WireAccountPositionsReply account_positions_reply = 47;
        WireAnnounceCurrentAccountPosition announce_current_account_position = 48;
        WireAnnounceCurrentPortfolioPosition announce_current_portfolio_position = 49;
        WireAssetMarkUpdate announce_asset_mark_update = 50;
        WireAnnounceAccountVolume announce_account_volume = 51;
        WireAnnouncePortfolioVolume announce_portfolio_volume = 52;
        WireBrokerLocateUpdate announce_broker_locate_update = 53;
        WireExternalPayment announce_external_payment = 54;
        WireTransferAccountPosition transfer_account_position = 55;
        WireTransferPortfolioPosition transfer_portfolio_position = 56;
        WireSetAssetAccountEntryPrice set_asset_account_position_entry_price = 57;
        WireSetAssetPortfolioEntryPrice set_asset_portfolio_position_entry_price = 58;
        WireExternalTransfer announce_external_transfer = 59;
        WireAssetConversion announce_asset_conversion = 60;
        WireRealizedPNLCredit announce_realized_pnl_credit = 61;
        WireTransactionApplied announce_transaction = 62;
        WireGatewayLogon gateway_logon = 63;
        WireGatewayLoggedOn gateway_logged_on = 64;
        WireGatewayLogoff gateway_logoff = 65;
        WireStopGateway gateway_stop = 66;
        WireStartGateway gateway_start = 67;
        WireGatewayStarted gateway_started = 68;
        WireGatewayStartError gateway_start_error = 69;
        WireGatewayStopped gateway_stopped = 70;
        WireGatewayStopError gateway_stop_error = 71;
        WireAnnounceNewMarketOrder announce_new_market_order = 72;
        WireAnnounceNewStrategyOrder announce_new_strategy_order = 73;
        WireAnnounceCancelMarketOrder announce_cancel_market_order = 74;
        WireAnnounceCancelStrategyOrder announce_cancel_strategy_order = 75;
        WireAnnounceExternalOrder announce_external_order = 76;
        WireApplicationInstanceAnnounceStartup announce_application_instance_start = 77;
        WireApplicationInstanceAnnounceShutdown announce_application_instance_stop = 78;
        WireAnnounceAllHostApplicationsShutdown announce_application_host_stop = 79;
        WireApplicationInstanceAnnounceGatewayConnection announce_application_instance_gateway_connection = 80;
        WireApplicationInstanceAnnounceGatewayDisconnection announce_application_instance_gateway_disconnection = 81;
        WireApplicationAnnounceTrackedPortfolio announce_application_instance_tracked_portfolio = 82;
        WireApplicationAnnounceTrackedAccount announce_application_instance_tracked_account = 83;
        WireApplicationInstanceAnnounceRunningStrategy announce_application_instance_running_strategy = 84;
        WireApplicationInstanceAnnounceMarketData announce_application_instance_marketdata = 85;
        WireApplicationInstanceManualOrderReceived announce_application_instance_manual_order_received = 86;
        WireApplicationInstanceCancelOrderReceived announce_application_instance_cancel_order_received = 87;
        WireApplicationInstanceCancelAllOrdersReceived announce_application_instance_cancel_all_received = 88;
        WireApplicationInstanceSetAccountPositionReceived announce_application_instance_set_account_position_received = 89;
        WireApplicationInstanceSetPortfolioPositionReceived announce_application_instance_set_portfolio_position_received = 90;
        WireApplicationInstanceStartStopStrategyReceived announce_application_instance_strategy_start_received = 91;
        WireApplicationInstanceStartStopAllStrategiesReceived announce_application_instance_strategy_start_all_received = 92;
        WireApplicationInstanceMarketDataSubscriptionReceived announce_application_instance_marketdata_subscription_received = 935;
        WireAnnounceApplicationInstanceBootstrapStart announce_application_instance_bootstrap_start = 94;
        WireAnnounceApplicationInstanceBootstrapEnd announce_application_instance_bootstrap_end = 95;
        WireApplicationInstanceBootstrapStartAck application_instance_bootstrap_start_ack = 96;
        WireApplicationInstanceBootstrapEndAck application_instance_bootstrap_end_ack = 97;
        WireApplicationInstanceHeartbeat announce_application_instance_heartbeat = 98;
        WireApplicationInstanceAnnounceConfiguration announce_application_instance_configuration = 99;

        // Strategy Messages
        WireAnnounceStrategy announce_strategy = 102;
        WireAnnounceStrategyOn announce_strategy_on = 103;
        WireAnnounceStrategyOff announce_strategy_off = 104;
        WireStrategyAlphaUpdate strategy_alpha_update = 105;
        WireAnnounceStrategyMode announce_strategy_mode = 106;

        // Market data messages
        WireMarketDataMessage market_data_message = 107;
        WireAddElementToMultiverse add_element_to_multiverse = 108;
        MarketDataRawPacket marketdata_raw = 109;
        WireMarketDataSubscription marketdata_subscription = 110;
        WireMDRealtimeStats marketdata_realtime_statistics = 119;
        WireMDRealtimeMissedPacket marketdata_realtime_missed_packet = 120;
        WireMDCaptureStats marketdata_capture_statistics = 121;
        WireMDCaptureMissedPacket marketdata_capture_missed_packet = 122;
        // command_messages
        WireStartStopTradingNow strategy_stop = 123;
        WireStartStopTradingAll strategy_stop_all = 124;
        WireStrategyUpdatePositionLimit strategy_update_position_limit = 125;

        // pnl_messages
        WireAccountPNLUpdate account_pnl_update = 126;
        WirePortfolioPNLUpdate portfolio_pnl_update = 127;
        WireAggregateAccountPNLUpdate aggregate_account_pnl_update = 128;
        WireAggregatePortfolioPNLUpdate aggregate_portfolio_pnl_update = 129;

        // log_messages
        WireLogMessage log_message = 130;

        // mutant_message
        WireAnnounceMutant announce_mutant = 131;
        WireAnnounceTempus announce_tempus = 132;
        WireDataMutantValueUpdate data_mutant_value_update = 133;
        WireGetDataMutantValue get_data_mutant_value = 134;
        WireBootstrapBoolMutantValue bootstrap_bool_mutant_value = 135;
        WireBoolMutantValueUpdate bool_mutant_value_update = 136;
        WireGetBoolMutantValue get_bool_mutant_value = 137;
        WireBootstrapDataMutantValue bootstrap_data_mutant_value = 138;

        // application_control_messages
        // TODO

        // execution algo messages
        WirePriceIndicationRequest price_indication_request = 139;
        WireMultiPriceIndicationRequest multi_price_indication_request = 140;
        WirePriceIndicationResponse price_indication_response = 141;
        WireMultiPriceIndicationResponse multi_price_indication_response = 142;
        WirePriceIndicationCancel price_indication_cancel = 143;

        WireNewOrder new_order = 144;
        WireCancelStrategyOrder cancel_strategy_order = 145;
    }
}

message WireHostStartup
{
    string hostname = 1;
    enum Type
    {
        // host_messages
        ANNOUNCE_HOST_STARTUP = 0;
        ANNOUNCE_HOST_SHUTDOWN = 1;
        HOST_HEARTBEAT = 2;
    }
    Type message_type = 2;
    common.v1.IPAddress ip = 3;
    uint32 message_listen_port = 4;
}

message WireManualStrategyOrder
{
    // the unique strategy identifier on the order
    string strategy_identifier = 1;

    // the id of the instrument the order is for
    uint64 instrument_id = 2;

    // the type of the order (market/limit/etc)
    OrderType order_type = 3;

    // the side of the order (buy/sell/short)
    OrderSide side = 4;

    // the number of units (shares, contracts, etc) on the order
    Rational size = 5;

    // The size of the order to show (if nan show full quantity)
    Rational show_size = 6;

    // the price of the order
    Rational price = 7;

    // who sent the order
    string originator = 8;
}

message WireCancelAllOrders
{
    // who sent the message
    string originator = 1;
}

message WireCancelOrdersInstrument
{
    // the id of the instrument to cancel orders for
    uint64 instrument_id = 1;

    // who sent the message
    string originator = 2;
}

message WireCancelReplaceOrder
{
    // the marvel order id of the order to be replaced
    MarvelOrderId marvel_order_id_to_be_replaced = 1;

    // the order to replace with
    WireNewOrder replacement_order = 2;

    // who sent this message
    string originator = 3;
}

message WireManualRejectMarketOrder
{
    MarvelOrderId marvel_order_id = 1;
}

message WireFlowOrderReceived
{
    // ID of gateway order came from
    uint32 gateway_id = 1;
    // ID of the instrument the order is for
    uint64 instrument_id = 2;
    // Internal order ID assigned to order
    MarvelOrderId marvel_order_id = 3;
    // External order ID
    string external_order_id = 4;
    // Side of the order
    OrderSide order_side = 5;
    // Type of order
    OrderType order_type = 6;
    // TODO: rational
    Rational quantity = 7;
    // Price of the order (if applicable)
    Rational order_price = 8;
    // ID of market center we received order from
    uint32 originator_market = 9;
    // ID of entity that sent the order
    uint32 originator_entity = 10;
    // time that the order was created
    google.protobuf.Timestamp origination_time = 11;
    // time that the order expires (is no longer valid)
    google.protobuf.Timestamp expiration_time = 12;
    // if true, order quantity is expressed in the term currency (not base, as usual)
    bool qty_in_term = 13;
    // he user who ran this script
    string originator = 14;
}

// TODO: set appropriate defaults
// WireNewOrder is sent when something wants to send an order.
message WireNewOrder
{
    //  the order id of the strategy order that generated this
    StrategyOrderId strategy_order_id = 1;

    //  the marvel order id of this order
    MarvelOrderId internal_order_id = 2;

    // the type of order (market, limit, etc)
    OrderType order_type = 3;

    // the side of the order (buy/sell/short, etc)
    OrderSide side = 4;

    //  the number of units (shares, contracts, etc) of the order
    Rational size = 5;

    // The amount of the order to show lit
    Rational show_size = 6;

    // the id of the instrument of the order
    uint64 instrument_id = 7;

    //  the price of the order, if applicable
    Rational price = 8;

    // the id of the market center to route to
    uint32 market_center_id = 9;

    // the id of the account the order is for
    uint32 account_id = 10;

    // the id of the portfolio the order is for
    uint32 portfolio_id = 11;

    // the string tag to put on the order to denote the account
    string account_order_tag = 12;

    // Time the order was sent (can be a historic time)
    google.protobuf.Timestamp send_time = 14;

    // Whether this is a warmup order
    bool is_warmup = 15;

    // Whether this is a long life order
    bool long_life = 16;
}

message WireFlowOrderAck
{
    // Internal order ID assigned to order
    MarvelOrderId marvel_order_id = 1;
}

message WireFlowOrderReject
{
    // Internal order ID assigned to order
    MarvelOrderId marvel_order_id = 1;

    enum OrderRejectReason
    {
        BROKER_OPTION = 0;
        UNKNOWN_SYMBOL = 1;
        EXCHANGE_CLOSED = 2;
        ORDER_EXCEEDS_LIMIT = 3;
        TOO_LATE_TO_ENTER = 4;
        UNKNOWN_ORDER = 5;
        DUPLICATE_ORDER = 6;
        DUPLICATE_OF_VERBAL = 7;
        STALE_ORDER = 8;
        POST_ONLY_VIOLATION = 9;
        TOO_MANY_CANCEL_REJECTS = 10;
        INTERNAL_EXCEPTION = 11;
        EXTERNAL_EXCEPTION = 12;
        OUTSIDE_LIMITS = 13;
        INVALID_PRICE = 14;
        INVALID_TYPE = 15;
        UNDEFINED = 255;
    }
    OrderRejectReason reject_reason = 2;
}

message WireFlowOrderExecution
{
    // Internal order ID assigned to order
    MarvelOrderId marvel_order_id = 1;
    string execution_id = 2;
    uint32 account_id = 3;
    uint32 portfolio_id = 4;
    uint64 instrument_id = 5;
    OrderSide order_side = 6;
    Rational exec_quantity = 7;
    Rational exec_price = 8;
    string originator = 9;
}

message WireFlowOrderExpired
{
    // Internal order ID assigned to order
    MarvelOrderId marvel_order_id = 1;
}

message WireFlowOrderCancel
{
    // Internal order ID assigned to order
    MarvelOrderId marvel_order_id = 1;
}

message WireFlowOrderCancelAck
{
    // Internal order ID assigned to order
    MarvelOrderId marvel_order_id = 1;
}

message WireFlowOrderCancelReject
{
    // Internal order ID assigned to order
    MarvelOrderId marvel_order_id = 1;
    enum CancelRejectReason
    {
        TOO_LATE_TO_CANCEL = 0;
        UNKNOWN_ORDER = 1;
        BROKER_OPTION = 2;
        ORDER_ALREADY_PENDING_CANCEL_OR_PENDING_REPLACE = 3;

        // INTERNALLY DEFINED
        BROKER_ERROR = 10;
        ORDER_AWAITING_ACK = 11;
        ORDER_TIMEOUT = 12;

        UNDEFINED = 255;
    }
    CancelRejectReason reason = 2;
    string text = 3;
}

message WireRiskLimitExceeded
{
    // The order that would exceed risk limits
    WireNewOrder rejected_order = 1;
}

message WireStrategyRiskLimitExceeded
{
    string strategy_identifier = 1;
    OrderType type = 2;
    bool is_buy = 3;
    Rational quantity = 4;
    Rational price = 5;
    uint64 instrument_id = 6;
    uint32 account_id = 7;
    uint32 portfolio_id = 8;
}

message WireGatewaySentOrder
{
    // Internal order ID assigned to order
    MarvelOrderId marvel_order_id = 1;
    google.protobuf.Timestamp gateway_time = 2;
    google.protobuf.Timestamp send_time = 3;
    google.protobuf.Timestamp send_finished_time = 4;
}

message WireGatewayRejectedOrder
{
    MarvelOrderId marvel_order_id = 1;
    string reject_reason = 2;
}

message WireStrategyOrderRejected
{
    StrategyOrderId order_id = 1;
    string reject_reason = 2;
}

message WireNotifyMarketOrderCompletelyFilled
{
    MarvelOrderId marvel_order_id = 1;
}

message WireOrderMarketAck
{
    uint32 gateway_id = 1;
    MarvelOrderId marvel_order_id = 2;
    string exchange_order_id = 3;
    // TODO: should this be a timestamp?
    string exchange_transact_time = 4;
    google.protobuf.Timestamp message_receive_time = 5;
}

message WireOrderMarketExecution
{
    uint32 gateway_id = 1;
    StrategyOrderId strategy_order_id = 2;
    MarvelOrderId marvel_order_id = 3;
    string exchange_order_id = 4;
    string exchange_exec_id = 5;
    uint32 account_id = 6;
    uint32 portfolio_id = 7;
    uint64 instrument_id = 8;
    OrderType order_type = 9;
    OrderSide order_side = 10;
    Rational size = 11;
    Rational price = 12;
    string liquidity_type = 13;
    uint32 execution_venue = 14;
    google.protobuf.Timestamp execution_time = 15;
    google.protobuf.Timestamp receive_time = 16;
    string response_location = 17;
}

message WireOrderMarketCanceled
{
    uint32 gateway_id = 1;
    MarvelOrderId marvel_order_id = 2;
    string exchange_order_id = 3;
    enum CanceledReason
    {
        UNKNOWN = 0;
        REQUESTED = 1;
        POST_ONLY_VIOLATION = 2;
        FILL_OR_KILL_MISS = 3;
        FORCE_CANCEL = 4;
        LOST_ORDER = 5;
        DISCONNECTED = 6;
    }
    CanceledReason reason = 4;
    string exchange_transact_time = 5;
    google.protobuf.Timestamp message_receive_time = 6;
}

message WireOrderMarketRejected
{
    uint32 gateway_id = 1;
    MarvelOrderId marvel_order_id = 2;
    enum OrderRejectReason
    {
        BROKER_OPTION = 0;
        UNKNOWN_SYMBOL = 1;
        EXCHANGE_CLOSED = 2;
        ORDER_EXCEEDS_LIMIT = 3;
        TOO_LATE_TO_ENTER = 4;
        UNKNOWN_ORDER = 5;
        DUPLICATE_ORDER = 6;
        DUPLICATE_OF_VERBAL = 7;
        STALE_ORDER = 8;

        // INTERNALLY DEFINED
        POST_ONLY_VIOLATION = 50;
        TOO_MANY_CANCEL_REJECTS = 51;
        INTERNAL_EXCEPTION = 52;
        EXTERNAL_EXCEPTION = 53;
        OUTSIDE_LIMITS = 54;
        INVALID_PRICE = 55;
        INVALID_TYPE = 56;
        UNDEFINED = 255;
    }
    OrderRejectReason reject_reason_code = 3;
    string reject_text = 4;
}

message WireOrderMarketExecutionCanceled
{
    uint32 gateway_id = 1;
    MarvelOrderId marvel_order_id = 2;
    string exchange_order_id = 3;
    string canceled_exchange_exec_id = 4;
    string exchange_exec_id_of_cancel = 5;
    enum MarvelOrderStatus
    {
        PENDING_NEW = 0;                 ///< THE ORDER HAS BEEN CREATED BY IS STILL PENDING ACTUAL SEND
        GATEWAY_SENT = 1;                ///< THE ORDER HAS BEEN SENT BY THE GATEWAY
        WORKING = 2;                     ///< THE ORDER HAS RECEIVED AN ACK FROM THE EXCHANGE AND IS WORKING WITH NO FILLS
        PARTIALLY_FILLED = 3;            ///< THE ORDER HAS RECEIVED SOME FILL BUT NOT A COMPLETE FILL
        COMPLETELY_FILLED = 4;           ///< THE ORDER HAS BEEN COMPLETELY FILLED (AND IS THUS FINISHED)
        DONE_FOR_THE_DAY = 5;            ///< THE ORDER HAS BEEN MARKED DONE FOR THE DAY (AND IS THUS FINISHED)
        PENDING_CANCEL = 6;              ///< THE ORDER HAS HAD A CANCEL CREATED BUT THAT CANCEL HASN'T BEEN CONFIRMED
        CANCELED = 7;                    ///< THE ORDER HAS RECEIVED A CONFIRMED CANCEL (AND IS THUS FINISHED)
        REPLACED = 8;                    ///< THE ORDER HAS BEEN REPLACED BY ANOTHER ORDER (AND IS THUS FINISHED)
        REJECTED = 9;                    ///< THE ORDER WAS REJECTED (AND IS THUS FINISHED)
        FAILED = 10;                     ///< WE FAILED TO SEND THE ORDER FOR SOME REASON (AND IS THUS FINISHED)
        CANCELED_PARTIALLY_FILLED = 11;  ///< THE ORDER IS CANCELED BUT RECEIVED FILLS BEFORE CANCEL
        DONE_PARTIALLY_FILLED = 12;      ///< THE ORDER IS DONE BUT RECEIVED FILLS
        EXPIRED = 13;                    ///< THE ORDER HAS EXPIRED (AND IS THUS FINISHED)
        UNINITIALIZED = 255;             ///< NO STATUS HAS BEEN SET
    }
    MarvelOrderStatus marvel_order_status = 6;
    Rational original_quantity = 7;
    Rational quantity = 8;
    Rational leaves_quantity = 9;
    Rational average_price = 10;
}

message WireOrderMarketExecutionCorrected
{
    uint32 gateway_id = 1;
    MarvelOrderId marvel_order_id = 2;
    string corrected_exchange_exec_id = 3;
    WireOrderMarketExecution corrected_execution = 4;
}

message WireOrderMarketCancelRejected
{
    uint32 gateway_id = 1;
    MarvelOrderId rejected_cancel_request_order_id = 2;
    MarvelOrderId rejected_canceled_order_id = 3;
    enum CancelRejectReason
    {
        TOO_LATE_TO_CANCEL = 0;
        UNKNOWN_ORDER = 1;
        BROKER_OPTION = 2;
        ORDER_ALREADY_PENDING_CANCEL_OR_PENDING_REPLACE = 3;

        // INTERNALLY DEFINED
        BROKER_ERROR = 10;
        ORDER_AWAITING_ACK = 11;
        ORDER_TIMEOUT = 12;
        UNDEFINED = 255;
    }
    CancelRejectReason reject_reason_code = 4;
    string reject_text = 5;
}

message WireUnparsableMarketMessage
{
    uint32 gateway_id = 1;
    string message = 2;
}

message WireUnattributableMarketExecution
{
    uint32 gateway_id = 1;
    MarvelOrderId marvel_order_id = 2;
    string exchange_order_id = 3;
    string exchange_exec_id = 4;
    string ticker = 5;
    OrderType type = 6;
    OrderSide side = 7;
    Rational size = 8;
    Rational price = 9;
    string liquidity_type = 10;
    uint32 execution_venue = 11;
    google.protobuf.Timestamp execution_time = 12;
    google.protobuf.Timestamp receive_time = 13;
}

message WireRequestAllOpenOrders
{
    string response_location = 1;
    string originator = 2;
}

message WireRequestOpenOrdersForInstrument
{
    uint64 instrument_id = 1;
    string response_location = 2;
    string originator = 3;
}

message WireRequestOrderStatus
{
    MarvelOrderId marvel_order_id = 1;
    string response_location = 2;
}

message WireRequestAllFills
{
    string response_location = 1;
    string originator = 2;
}

message WireRequestAllTransfers
{
    string response_location = 1;
}

message WireSubscribeAllFills
{
    repeated uint64 instruments = 1;
    repeated uint32 accounts = 2;
    repeated uint32 portfolios = 3;
    repeated uint64 excluded_applications = 4;
    string response_location = 5;
    uint64 requesting_application = 6;
}

message WireSubscribeAllTransfers
{
    repeated uint32 assets = 1;
    repeated uint32 accounts = 2;
    repeated uint32 portfolios = 3;
    repeated uint64 excluded_applications = 4;
    string response_location = 5;
    uint64 requesting_application = 6;
}

message WireSubscribeAllPositionAdjustments
{
    repeated uint32 assets = 1;
    repeated uint32 accounts = 2;
    repeated uint32 portfolios = 3;
    repeated uint64 excluded_applications = 4;
    string response_location = 5;
    uint64 requesting_application = 6;
}

message WireGetPortfolioPositions
{
    uint32 portfolio_id = 1;
    string response_location = 2;
    string originator = 3;
}

message WireGetAllPortfolioPositions
{
    string response_location = 1;
    string originator = 2;
}

message WireGetAccountPositions
{
    uint32 account_id = 1;
    string response_location = 2;
    string originator = 3;
}

message WireGetAllAccountPositions
{
    string response_location = 1;
    string originator = 2;
}

message WireSetPortfolioPosition
{
    uint32 portfolio_id = 1;
    uint32 asset_id = 2;
    Rational position = 3;
    string originator = 4;
}

message WireSetAccountPosition
{
    uint32 account_id = 1;
    uint32 asset_id = 2;
    Rational position = 3;
    string originator = 4;
}

message WirePortfolioPositionsReply
{
    uint32 portfolio_id = 1;
    repeated OnePosition positions = 2;
}

message WireAccountPositionsReply
{
    uint32 account_id = 1;
    repeated OnePosition positions = 2;
}

message WireAnnounceCurrentAccountPosition
{
    uint32 account_id = 1;
    uint32 asset_id = 2;
    Rational internal_position = 3;
    Rational subaccount_position = 4;
    Rational entry_price = 5;
}

message WireAnnounceCurrentPortfolioPosition
{
    uint32 portfolio_id = 1;
    uint32 asset_id = 2;
    Rational internal_position = 3;
    Rational subaccount_position = 4;
    Rational entry_price = 5;
}

message WireAssetMarkUpdate
{
    uint32 asset_id = 1;
    uint32 mark_asset = 2;
    uint32 value_asset = 3;
    Rational mark_price = 4;
    Rational unit_value = 5;
}

message WireAnnounceAccountVolume
{
    uint32 account_id = 1;
    uint32 asset_id = 2;
    uint32 value_currency = 3;
    Rational buy_quantity = 4;
    Rational sell_quantity = 5;
    Rational buy_value = 6;
    Rational sell_value = 7;
    Rational average_buy_price = 8;
    Rational average_sell_price = 9;
}

message WireAnnouncePortfolioVolume
{
    uint32 portfolio_id = 1;
    uint32 asset_id = 2;
    uint32 value_currency = 3;
    Rational buy_quantity = 4;
    Rational sell_quantity = 5;
    Rational buy_value = 6;
    Rational sell_value = 7;
    Rational average_buy_price = 9;
    Rational average_sell_price = 10;
}

message WireBrokerLocateUpdate
{
    uint32 locate_broker = 1;
    uint32 asset_id = 2;
    Rational locate_quantity = 3;
}

message WireExternalPayment
{
    uint32 account_id = 1;
    uint32 portfolio_id = 2;
    uint32 asset_id = 3;
    Rational amount = 4;
    uint32 counterparty_id = 5;
    string payment_id = 6;
    string metadata = 7;
    google.protobuf.Timestamp payment_time = 8;
    enum PaymentType
    {
        GENERIC = 0;          ///< A payment of some generic, otherwise undefined, type
        SWAP_FUNDING = 1;     ///< A funding payment on a Swap contract position
        INTEREST = 2;         ///< An interest payment
        BORROW = 3;           ///< A payment made due to a short position held (or lent)
        GENERIC_FEE = 4;      ///< A generic fee payment made
        TRANSFER_FEE = 5;     ///< A fee payment on a transfer of assets
        TRANSACTION_FEE = 6;  ///< A fee payment charged on a transaction
        RISK_FEE = 7;         ///< A fee charged based on risk taken by a counterparty
        WHITE_LABEL_FEE = 8;  ///< A fee charged for white labeled software
        WIRE_FEE = 9;         ///< A fee charged for a wire transfer
        IB_FEE = 10;          ///< A fee charged to introduce a broker
    }
    PaymentType payment_type = 9;
    uint32 associated_asset_id = 10;
    string response_location = 11;
}

message WireTransferAccountPosition
{
    uint32 from_account_id = 1;
    uint32 to_account_id = 2;
    uint32 asset_id = 3;
    Rational position = 4;
    Rational transfer_price = 5;
    string transfer_id = 6;
    google.protobuf.Timestamp transfer_time = 7;
    string response_location = 8;
}

message WireTransferPortfolioPosition
{
    uint32 from_portfolio_id = 1;
    uint32 to_portfolio_id = 2;
    uint32 asset_id = 3;
    Rational position = 4;
    Rational transfer_price = 5;
    string transfer_id = 6;
    google.protobuf.Timestamp transfer_time = 7;
    string response_location = 8;
}

message WireSetAssetAccountEntryPrice
{
    uint32 asset_id = 1;
    uint32 account_id = 2;
    Rational price_entry = 3;
    uint32 price_asset = 4;
}

message WireSetAssetPortfolioEntryPrice
{
    uint32 asset_id = 1;
    uint32 portfolio_id = 2;
    Rational price_entry = 3;
    uint32 price_asset = 4;
}

message WireExternalTransfer
{
    uint32 account_id = 1;
    uint32 portfolio_id = 2;
    uint32 asset_id = 3;
    Rational amount = 4;
    string transfer_id = 5;
    string metadata = 6;
    google.protobuf.Timestamp transfer_time = 7;
    string response_location = 8;
}

message WireAssetConversion
{
    uint32 account_id = 1;
    uint32 portfolio_id = 2;
    uint32 asset_id = 3;
    uint32 converted_asset_id = 4;
    Rational amount = 5;
    string conversion_id = 6;
    string metadata = 7;
    google.protobuf.Timestamp conversion_time = 8;
    Rational conversion_rate = 9;
    uint32 output_account_id = 10;
}

message WireRealizedPNLCredit
{
    uint32 account_id = 1;
    uint32 portfolio_id = 2;
    uint32 asset_id = 3;
    Rational amount = 4;
    string credit_id = 5;
    string metadata = 6;
    google.protobuf.Timestamp credit_time = 7;
}

message WireTransactionApplied
{
    enum TransactionType
    {
        GENERIC = 0;                                  ///< A transaction of unknown / unspecific type
        TRADE = 1;                                    ///< A 'normal' (buy/sell) trade
        TRADING_FEE = 2;                              ///< A payment made that is directly related to an order or trade
        EXTERNAL_PAYMENT = 3;                         ///< A payment not directly related to an order or trade
        INTERNAL_TRANSFER = 4;                        ///< A transfer of a position between accounts or portfolios due to an internal book entry
        EXTERNAL_TRANSFER = 5;                        ///< A transfer of an asset position from outside
        ASSET_CONVERSION = 6;                         ///< A conversion from one asset to another (outside of 'normal' trading procedures)
        POSITION_ADJUSTMENT = 7;                      ///< An adjustment of a position with no real transaction associated
        REALIZED_PNL_CREDIT = 8;                      ///< A credit of PNL realized from trading into an account/portfolio
        FORWARD_LOAN_AGREEMENT_INITIATION = 9;        ///< Initial position creation for a FLA
        FORWARD_LOAN_AGREEMENT_COLLATERAL_CALL = 10;  ///< Collateral call adjusting positions for a FLA
        FORWARD_LOAN_AGREEMENT_SETTLEMENT = 11;       ///< Final position closing for a FLA
    }
    TransactionType type = 1;
    uint32 account_id = 2;
    uint32 portfolio_id = 3;
    uint32 asset_id = 4;
    Rational amount = 5;
    string transaction_id = 6;
    string metadata = 7;
    google.protobuf.Timestamp transaction_time = 8;
}

message WireGatewayLogon
{
    string client_listen_location = 1;
    string gateway_listen_location = 2;
}

// TODO
message WireGatewayLoggedOn
{}

// TODO
message WireGatewayLogoff
{}

message WireStopGateway
{
    uint32 gateway_id = 1;
}

message WireStartGateway
{
    uint32 gateway_id = 1;
}

message WireGatewayStarted
{
    uint32 gateway_id = 1;
}

message WireGatewayStartError
{
    uint32 gateway_id = 1;
    enum GatewayStartErrorCode
    {
        UNKNOWN = 0;
        ALREADY_STARTED = 1;  ///< indicates the gateway was already started when it received the command
        BAD_GATEWAY_ID = 2;   ///< indicates the gateway id requested to start is invalid somehow
        CANNOT_CONNECT = 3;   ///< indicates the gateway couldn't connect to something it needs to
        BAD_TIME = 4;         ///< indicates it is a time when the gateway should not be started
    }
    GatewayStartErrorCode error_code = 2;
    string error_text = 3;
}

message WireGatewayStopped
{
    uint32 gateway_id = 1;
}

message WireGatewayStopError
{
    uint32 gateway_id = 1;
    enum GatewayStopErrorCode
    {
        UNKNOWN = 0;
        ALREADY_STOPPED = 1;  ///< indicates the gateway was already stopped
        BAD_GATEWAY_ID = 2;   ///< indicates the gateway id requested to stop is invalid somehow
    }
    GatewayStopErrorCode error_code = 2;
    string error_text = 3;
}

message WireAnnounceNewMarketOrder
{
    WireNewOrder order = 1;
    uint32 order_gateway = 2;
    uint32 mutant_update_count = 3;
    uint64 mutant_sequence_number = 4;
    uint64 message_queue_wait_duration_ns = 5;
    google.protobuf.Timestamp trigger_time = 6;
    google.protobuf.Timestamp trigger_processed_time = 7;
    google.protobuf.Timestamp app_time = 8;
    google.protobuf.Timestamp serviced_time = 9;
    google.protobuf.Timestamp strategy_start_time = 10;
    google.protobuf.Timestamp strategy_sent_time = 11;
    google.protobuf.Timestamp order_handler_start_time = 12;
    google.protobuf.Timestamp order_handler_sent_time = 13;
}

message WireAnnounceNewStrategyOrder
{
    StrategyOrderId order_id = 1;
    string strategy_identifier = 2;
    OrderType type = 3;
    bool is_buy = 4;
    Rational quantity = 5;
    Rational show_quantity = 6;
    Rational price = 7;
    uint64 instrument_id = 8;
    uint32 account_id = 9;
    uint32 portfolio_id = 10;
}

message WireAnnounceCancelMarketOrder
{
    MarvelOrderId canceled_order_id = 1;
    MarvelOrderId cancel_request_order_id = 2;
    StrategyOrderId strategy_order_id = 3;
}

message WireAnnounceCancelStrategyOrder
{
    StrategyOrderId strategy_order_id = 1;
}

message WireAnnounceExternalOrder
{
    StrategyOrderId order_id = 1;
    MarvelOrderId marvel_order_id = 2;
    string strategy_identifier = 3;
    OrderType type = 4;
    OrderSide side = 5;
    Rational quantity = 6;
    Rational show_quantity = 7;
    Rational price = 8;
    uint64 instrument_id = 9;
    uint32 account_id = 10;
    uint32 portfolio_id = 11;
    uint32 gateway_id = 12;
    uint32 market_center_id = 13;
}

message WireApplicationInstanceAnnounceStartup
{
    uint64 application_instance_token = 1;
    string application_identifier = 2;
    string application_hostname = 3;
    string application_message_queue = 4;
    google.protobuf.Timestamp startup_time = 5;
}

message WireApplicationInstanceAnnounceShutdown
{
    uint64 application_instance_token = 1;
    google.protobuf.Timestamp shutdown_time = 2;
}

message WireAnnounceAllHostApplicationsShutdown
{
    string hostname = 1;
}

message WireApplicationInstanceAnnounceGatewayConnection
{
    uint64 application_instance_token = 1;
    uint32 gateway_id = 2;
}

message WireApplicationInstanceAnnounceGatewayDisconnection
{
    uint64 application_instance_token = 1;
    uint32 gateway_id = 2;
}

message WireApplicationAnnounceTrackedPortfolio
{
    uint64 application_instance_token = 1;
    uint32 portfolio_id = 2;
}

message WireApplicationAnnounceTrackedAccount
{
    uint64 application_instance_token = 1;
    uint32 account_id = 2;
}

message WireApplicationInstanceAnnounceRunningStrategy
{
    uint64 application_instance_token = 1;
    string strategy_identifier = 2;
}

message WireApplicationInstanceAnnounceMarketData
{
    uint64 application_instance_token = 1;
    uint32 feed_id = 2;
    map<uint64, bool> instruments = 3;
}

message WireApplicationInstanceManualOrderReceived
{
    uint64 application_instance_token = 1;
    WireManualStrategyOrder manual_order = 2;
}

message WireApplicationInstanceCancelOrderReceived
{
    uint64 application_instance_token = 1;
    WireCancelStrategyOrder cancel_order = 2;
}

message WireCancelStrategyOrder
{
    StrategyOrderId strategy_order_id = 1;
    string originator = 2;
}

message WireApplicationInstanceCancelAllOrdersReceived
{
    uint64 application_instance_token = 1;
    WireCancelAllOrders cancel_orders = 2;
}

message WireApplicationInstanceSetAccountPositionReceived
{
    uint64 application_instance_token = 1;
    WireSetAccountPosition set_positions = 2;
}

message WireApplicationInstanceSetPortfolioPositionReceived
{
    uint64 application_instance_token = 1;
    WireSetPortfolioPosition set_positions = 2;
}

message WireApplicationInstanceStartStopStrategyReceived
{
    uint64 application_instance_token = 1;
    WireStartStopTradingNow command = 2;
    enum Type
    {
        ANNOUNCE_APPLICATION_INSTANCE_STRATEGY_START_RECEIVED = 0;
        ANNOUNCE_APPLICATION_INSTANCE_STRATEGY_STOP_RECEIVED = 1;
    }
    Type message_type = 3;
}

message WireApplicationInstanceStartStopAllStrategiesReceived
{
    uint64 application_instance_token = 1;
    WireStartStopTradingAll command = 2;
    enum Type
    {
        ANNOUNCE_APPLICATION_INSTANCE_STRATEGY_START_ALL_RECEIVED = 0;
        ANNOUNCE_APPLICATION_INSTANCE_STRATEGY_STOP_ALL_RECEIVED = 1;
    }
    Type message_type = 3;
}

message WireStartStopTradingNow
{
    enum Type
    {
        STRATEGY_START = 0;
        STRATEGY_STOP = 1;
    }
    Type message_type = 1;
    string strategy_identifier = 2;
}
message WireStartStopTradingAll
{
    enum Type
    {
        STRATEGY_START = 0;
        STRATEGY_STOP = 1;
    }
    Type message_type = 1;
}
message WireApplicationInstanceMarketDataSubscriptionReceived
{
    uint64 application_instance_token = 1;
    WireMarketDataSubscription subscription = 2;
}

message WireMarketDataSubscription
{
    repeated uint64 instrument_ids = 1;
    uint32 feed_id = 2;
    string response_location = 3;
}

message WireAnnounceApplicationInstanceBootstrapStart
{
    uint64 application_instance_token = 1;
    string response_location = 2;
}

message WireAnnounceApplicationInstanceBootstrapEnd
{
    uint64 application_instance_token = 1;
    string response_location = 2;
}

message WireApplicationInstanceBootstrapStartAck
{
    uint64 application_instance_token = 1;
}

message WireApplicationInstanceBootstrapEndAck
{
    uint64 application_instance_token = 1;
}

message WireApplicationInstanceHeartbeat
{
    uint64 application_instance_token = 1;
}

message WireApplicationInstanceAnnounceConfiguration
{
    string config = 1;
}

message WireAnnounceStrategy
{
    uint64 application_instance_token = 1;
    uint64 strategy_order_token = 2;
    string strategy_identifier = 3;
}

message WireAnnounceStrategyOn
{
    uint64 application_instance_token = 1;
    string strategy_identifier = 2;
}

message WireAnnounceStrategyOff
{
    uint64 application_instance_token = 1;
    string strategy_identifier = 2;
}

message WireStrategyAlphaUpdate
{
    string strategy_identifier = 1;
    // Json of fields to be updated.
    // Ex {AvgBuyFill="3.56",AvgSellFill="5.8"}
    string value =2;
}

message WireAnnounceStrategyMode
{
    string strategy_identifier = 1;
    enum StrategyMode
    {
        UNKNOWN = 0;                   ///< An unknown strategy mode
        NORMAL = 1;                    ///< Normal trading
        CLOSE_ONLY = 2;                ///< Only send position closing orders
        PRECAUTIONARY_CLOSE_ONLY = 3;  ///< Like CLOSE_ONLY but a special mode set due to some aberrant input
        LIQUIDATE = 4;                 ///< Like CLOSE_ONLY but should be more aggressive
        DEBUG = 5;                     ///< Acts like normal but turn on debug logging
        COMPLETED = 6;                 ///< Strategy Has completed
    }
    StrategyMode mode = 2;
}

message WireMDRealtimeStats
{
    uint32 feed_id = 1;
    uint32 partition_id = 2;
    uint32 partition_stream_id = 3;
    google.protobuf.Timestamp time = 4;
    uint64 min_latency = 5;
    uint64 max_latency = 6;
    uint64 average_latency = 7;
    uint32 current_queue_size = 8;
    uint32 max_queue_size = 9;
    uint32 max_per_second = 10;
    uint32 pps_since_last = 11;
    uint32 count_since_last = 12;
    uint32 total_message_count = 13;
}

message WireMDRealtimeMissedPacket
{
    uint32 feed_id = 1;
    uint32 partition_id = 2;
    uint32 partition_stream_id = 3;
    uint32 first_sequence_number_missed = 4;
    uint32 last_sequence_number_missed = 5;
    google.protobuf.Timestamp missed_time = 6;
}

message WireMDCaptureStats
{
    uint64 min_latency = 1;
    uint64 max_latency = 2;
    uint64 average_latency = 3;
    uint64 max_per_second = 4;
    uint64 max_per_100ms = 5;
    uint64 pps_since_last = 6;
    uint64 count_since_last = 7;
    uint64 total_message_count = 8;
    google.protobuf.Timestamp proc_time = 9;
}

message WireMDCaptureMissedPacket
{
    uint32 feed_id = 1;
    uint32 partition_id = 2;
    uint32 partition_stream_id = 3;
    uint32 first_sequence_number_missed = 4;
    uint32 last_sequence_number_missed = 5;
    google.protobuf.Timestamp missed_time = 6;
}

// WireStrategyUpdatePositionLimit is sent to tell a trading application to update a position limit
message WireStrategyUpdatePositionLimit
{
    string strategy_identifier = 1;
    uint32 asset_id = 2;
    Rational limit = 3;
}

message WireAccountPNLUpdate
{
    uint32 account_id = 1;
    uint32 asset_id = 2;
    uint32 pnl_currency = 3;
    Rational mark_price = 4;
    Rational session_internal_pnl = 5;
    Rational session_total_pnl = 6;
    Rational accumulated_internal_pnl = 7;
    Rational accumulated_total_pnl = 8;
    Rational unrealized_pnl = 9;
    // TODO: Fees should be represented as a hashset somehow - can't use map<TYPE, TYPE> because message TYPES can't be keys
    repeated Fee session_internal_fees = 10;
    repeated Fee session_total_fees = 11;
    Rational session_internal_payments = 12;
    Rational session_total_payments = 13;
}

// WirePortfolioPNLUpdate is sent whenever the PNL of some asset within a portfolio changes.
message WirePortfolioPNLUpdate
{
    uint32 portfolio_id = 1;
    uint32 asset_id = 2;
    uint32 pnl_currency = 3;
    Rational mark_price = 4;
    Rational session_internal_pnl = 5;
    Rational session_total_pnl = 6;
    Rational accumulated_internal_pnl = 7;
    Rational accumulated_total_pnl = 8;
    Rational unrealized_pnl = 9;
    // TODO: Fees should be represented as a hashset somehow - can't use map<TYPE, TYPE> because message TYPES can't be keys
    repeated Fee session_internal_fees = 10;
    repeated Fee session_total_fees = 11;
    Rational session_internal_payments = 12;
    Rational session_total_payments = 13;
}

message WireAggregateAccountPNLUpdate
{
    uint32 account_id = 1;
    map<uint32, Rational> session_internal_pnl = 2;
    map<uint32, Rational> session_total_pnl = 3;
    map<uint32, Rational> accumulated_internal_pnl = 4;
    map<uint32, Rational> accumulated_total_pnl = 5;
    // TODO: Fees should be represented as a hashset somehow - can't use map<TYPE, TYPE> because message TYPES can't be keys
    repeated Fee session_internal_fees = 6;
    repeated Fee session_total_fees = 7;
}

message WireAggregatePortfolioPNLUpdate
{
    uint32 portfolio_id = 1;
    map<uint32, Rational> session_internal_pnl = 2;
    map<uint32, Rational> session_total_pnl = 3;
    map<uint32, Rational> accumulated_internal_pnl = 4;
    map<uint32, Rational> accumulated_total_pnl = 5;
    // TODO: Fees should be represented as a hashset somehow - can't use map<TYPE, TYPE> because message TYPES can't be keys
    repeated Fee session_internal_fees = 6;
    repeated Fee session_total_fees = 7;
}

message Fee
{
    Rational amount = 1;
    uint32 currency = 2;
    uint32 counterparty = 3;
    enum FeeType
    {
        GENERIC = 0;           ///< A fee of some generic, otherwise undefined, type
        BROKER_EXECUTION = 1;  ///< A fee charged by an execution broker for a trade
        MARKET_EXECUTION = 2;  ///< A fee charged by an execution venue for trading on it
        CLEARING = 3;          ///< A fee charged by a prime broker for clearing/settlements
        SEC = 4;               ///< A fee charged by the regulatory body for trading
        TICKET = 5;            ///< A fee charged on a per-ticket fee basis
    }
    FeeType type = 4;
    bool adjust_position = 5;
}

message WireLogMessage
{
    enum LogType
    {
        UNTYPED = 0;         ///< Standard type that gets no special treatment
        INFO = 1;            ///< Special type for informational logs
        WARN = 2;            ///< Special type for a warning of a potentially dangerous situation
        EXCEPTION = 4;       ///< Special type for exceptions that are thrown
        ERROR = 3;           ///< Special type for an error that isn't necessarily fatal
        DEBUG = 5;           ///< Special type for debugging
        TRACE = 6;           ///< Special type for super fine grained debugging
        DATA_INTEGRITY = 7;  ///< Special type for messages related to data integrity checks and reporting
        ALERT = 8;           ///< Special type for an "alert", which is something to be noted, but is not necessarily good or bad, or error or not.
    }
    LogType log_type = 1;
    string message = 2;
}

message WireAnnounceMutant
{
    string mutant_id = 1;
    string display_name = 2;
}

message WireAnnounceTempus
{
    string mutant_id = 1;
    google.protobuf.Timestamp reference_time = 2;
}

message WireDataMutantValueUpdate
{
    string mutant_id = 1;
    Rational value = 2;
}

message WireGetDataMutantValue
{
    string mutant_id = 1;
    string response_location = 2;
}

message WireBootstrapBoolMutantValue
{
    string mutant_id = 1;
    string response_location = 2;
}

message WireBoolMutantValueUpdate
{
    string mutant_id = 1;
    bool value = 2;
}

message WireGetBoolMutantValue
{
    string mutant_id = 1;
    string response_location = 2;
}

message WireBootstrapDataMutantValue
{
    string mutant_id = 1;
    string response_location = 2;
}

message WirePriceIndicationRequest
{
    string request_id = 1;
    uint32 asset = 2;
    uint32 price_asset = 3;
    uint32 entity = 4;
    Rational quantity = 5;
    OrderSide side = 6;
    google.protobuf.Timestamp expiration_time = 7;
    string response_location = 8;
}

message WireMultiPriceIndicationRequest
{
    string request_id = 1;
    uint32 request_entity = 2;
    repeated SinglePriceIndicationInfo indications = 3;
    string response_location = 4;
}

message WirePriceIndicationResponse
{
    string request_id = 1;
    Rational price = 2;
    Rational quantity = 3;
    google.protobuf.Timestamp expiration_time = 4;
}

message WireMultiPriceIndicationResponse
{
    string request_id = 1;
    repeated SinglePriceIndicationResponse responses = 2;
}

message WirePriceIndicationCancel
{
    string request_id = 1;
}

message SinglePriceIndicationInfo
{
    uint32 asset = 1;
    uint32 price_asset = 2;
    Rational quantity = 3;
    OrderSide side = 4;
}

message SinglePriceIndicationResponse
{
    uint32 asset = 1;
    uint32 price_asset = 2;
    Rational quantity = 3;
    OrderSide side = 4;
    Rational price = 5;
}

message OnePosition
{
    uint32 asset_id = 1;
    uint32 mark_currency = 2;
    Rational position = 3;
    Rational position_mark = 4;
    Rational accumulated_pnl = 5;
    Rational incoming = 6;
    Rational incoming_mark = 7;
    Rational incoming_pnl = 8;
    Rational buy_quantity = 9;
    Rational sell_quantity = 10;
    Rational buy_exec_quantity = 11;
    Rational sell_exec_quantity = 12;
    Rational buy_value = 13;
    Rational sell_value = 14;
    Rational buy_exec_value = 15;
    Rational sell_exec_value = 16;
    Rational entry_price = 17;
}

// MarvelMessageType::marketdata_unsubscription
message WireMarketDataUnsubscription
{
    repeated uint64 instrument_ids = 1;
    uint32 feed_id = 2;
    string response_location = 3;
}

// market_data_messages
message WireMarketDataMessage
{
    // A flag denoting whether this message should be applied to books in the application
    bool apply_to_book = 1;
    // A flag denoting whether this is the last message in a single market event
    bool end_of_event = 2;
    // the feed the message is part of
    uint32 feed_id = 3;
    // the feed partition the message is part of
    uint32 partition_id = 4;
    // the feed partition stream the message is part of
    uint32 partition_stream_id = 5;
    // the sequence number of the message
    uint64 sequence_number = 6;
    // the instrument the message is for
    uint64 instrument_id = 7;
    // the time the message "happened", potentially in historic time
    google.protobuf.Timestamp time = 13;
    // the time that we "received" the message (always in current time space and is essentially when it came off disk/nic/etc)
    google.protobuf.Timestamp recv_time = 14;
    // the time that we "processed" the message (always in current time space and is when we have processed the raw message into this normalized message)
    google.protobuf.Timestamp proc_time = 15;

    oneof message_type
    {
        LevelBook level_book = 16;
        LevelBookExecution level_book_execution = 17;
        Level1Update level1_update = 18;
        TradeUpdate trade_update = 19;
        OrderBook order_book = 20;
        OrderBookExecutedOrder order_book_executed_order = 21;
        OrderBookPartialCancelOrder order_book_partial_cancel_order = 22;
        OrderBookFullCancelOrder order_book_full_cancel_order = 23;
        OrderBookReplacedOrder order_book_replaced_order = 24;
        OrderBookExecutedUnknownOrder order_book_executed_unknown_order = 25;
        OrderBookExecutionCanceled order_book_execution_canceled = 26;
        MOCImbalance moc_imbalance = 27;
        OfficialClose official_close = 28;
        ResetBook reset_book = 29;
    }

    // MarvelMessageType::marketdata_levelbook_(newlevel|modifylevel|removelevel)
    message LevelBook
    {
        OrderSide side = 1;
        Rational price = 2;
        Rational quantity = 3;
        uint32 orders = 4;
        uint32 book_level = 5;
        enum State
        {
            NEW_LEVEL = 0;
            MODIFY_LEVEL = 1;
            REMOVE_LEVEL = 2;
        }
        State state = 6;
    }

    // WireMDLevelBookExecution
    message LevelBookExecution
    {
        OrderSide aggressor_side = 1;
        Rational price = 2;
        Rational quantity = 3;
        uint32 exec_id = 4;
    }

    // MarvelMessageType::marketdata_level1_update
    message Level1Update
    {
        uint32 ask_orders = 1;
        uint32 bid_orders = 2;
        Rational ask_quantity = 3;
        Rational ask_price = 4;
        Rational bid_price = 5;
        Rational bid_quantity = 6;
    }

    // MarvelMessageType::marketdata_trade_update
    message TradeUpdate
    {
        Rational size = 1;
        Rational price = 2;
    }

    // WireMDOrderBookNewOrder | WireMDOrderBookModifyOrder
    // WireMDOrderBookNewOrder represents a new order
    // WireMDOrderBookModifyOrder represents when an order is modified without losing priority
    message OrderBook
    {
        uint32 id = 1;
        OrderSide side = 2;
        Rational price = 3;
        Rational quantity = 4;
        uint64 priority = 5;
        enum State
        {
            NEW_ORDER = 0;
            MODIFY_ORDER = 1;
        }
        State state = 6;
    }

    // WireMDOrderBookExecutedOrder represents when an order is traded against.
    message OrderBookExecutedOrder
    {
        uint32 id = 1;
        uint32 exec_id = 2;
        Rational price = 3;
        Rational quantity = 4;
    }

    // WireMDOrderBookPartialCancelOrder represents when an order is partially canceled.
    message OrderBookPartialCancelOrder
    {
        uint32 id = 1;
        Rational canceled_quantity = 2;
    }

    // WireMDOrderBookFullCancelOrder represents when an order is fully canceled.
    message OrderBookFullCancelOrder
    {
        uint32 id = 1;
    }

    // WireMDOrderBookReplacedOrder represents when an order is replaced by another order
    message OrderBookReplacedOrder
    {
        uint32 id = 1;
        uint32 replaced_id = 2;
        uint32 new_id = 3;
        OrderSide side = 4;
        Rational price = 5;
        Rational quantity = 6;
    }

    // WireMDOrderBookExecutedUnknownOrder represents when an unknown order trades
    message OrderBookExecutedUnknownOrder
    {
        uint32 id = 1;
        uint32 exec_id = 2;
        Rational quantity = 3;
        Rational price = 4;
    }

    // WireMDOrderBookExecutionCanceled represents when an execution is canceled
    message OrderBookExecutionCanceled
    {
        uint32 id = 1;
        uint32 exec_id = 2;
    }

    // A MOC imbalance message
    message MOCImbalance
    {
        OrderSide imbalance_side = 1;
        Rational imbalance_reference_price = 2;
        Rational imbalance_quantity = 3;
    }

    // WireMDOfficialClose
    message OfficialClose
    {
        Rational close_auction_volume = 1;
        Rational close_price = 2;
    }

    // WireMDResetBook is sent to instruct clients to reset (completely) empty a given book.
    message ResetBook
    {}
}

// MarvelMessageType::marketdata_raw
// MDRawPacket is a message sent by a tesseract for parsing raw data
message MarketDataRawPacket
{
    // The id of the feed the message is part of
    uint32 feed_id = 1;
    // Time packet hit NIC
    google.protobuf.Timestamp packet_time = 2;
    // Time packet hit software
    google.protobuf.Timestamp recv_time = 3;
    bytes packet_data = 4;
    // Size (in bytes) of packet data
    bytes packet_data_size = 5;
}

// MarvelMessageType::marketdata_add_element_to_multiverse
message WireAddElementToMultiverse
{
    // Configuration of Element to Add (raw_json)
    string element_config = 1;
}

enum OrderType
{
    UNINITIALIZED = 0;  ///< The order type wasn't given

    // Below are the values that FIX tag OrdType (60) may take.
    MARKET = 1;           ///< Market order
    LIMIT = 2;            ///< Limit order
    STOP = 3;             ///< Stop order
    STOP_LIMIT = 4;       ///< Stop order with a limit
    MARKET_ON_CLOSE = 5;  ///< Market on close order
    LIMIT_ON_CLOSE = 6;   ///< Limit on close order

    // Below are Marvel-only order types.
    IOC = 100;             ///< immediate or cancel order (subset of limit that auto cancels if not marketable)
    ICEBERG = 101;         ///< iceberg (subset of limit order where some size is hidden)
    UNDISPLAYED = 102;     ///< undisplayed (subset of limit order where all size is hidden)
    INTERNAL_CROSS = 103;  ///< an order intended to be crossed between two internal accounts/portfolios
    POST_ONLY = 104;       ///< an order that should only post liquidity,
    FOK = 105;             ///< an immediate or cancel order that must be entirely filled or cancelled
}

enum OrderSide
{
    UNKNOWN = 0;      ///< THE TYPE ISN'T UNKNOWN
    BUY = 1;          ///< A BUY ORDER
    SELL = 2;         ///< A SELL ORDER
    BUYMINUS = 3;     ///< A BUY ON A DOWN TICK
    SELLPLUS = 4;     ///< A SELL ON AN UP TICK
    SELLSHORT = 5;    ///< A SHORT SALE
    BUYTOCOVER = 6;   ///< A BUY TO COVER A POSITION
    SELLTOCLOSE = 7;  ///< A SELL TO CLOSE A POSITION
    UNDISCLOSED = 8;  ///< TYPE IS NOT GIVEN
    CROSS = 9;
    CROSSSHORT = 10;
    BUYSELL = 11;
}
