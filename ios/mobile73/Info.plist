<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>SALT</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>2.16.67</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>saltApp</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>saltApp</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>133</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>For Image Document Capture or QR Code scanning</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>This application requires your location in order to dermine your locale for processing your documents</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Verify location during kyc or document upload processes</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>$(PRODUCT_NAME) would like to save photos to your photo gallery</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>For photo select</string>
	<key>NSAppleMusicUsageDescription</key>
	<string>This permission is not needed by the app, but it is required by an underlying API.</string>
	<key>UIAppFonts</key>
	<array>
		<string>MrEavesXLModOT-Bold.otf</string>
		<string>MrEavesXLModOT-Light.otf</string>
		<string>MrEavesXLModOT-Reg.otf</string>
		<string>EuropaBold.otf</string>
		<string>EuropaBoldItalic.otf</string>
		<string>EuropaLight.otf</string>
		<string>EuropaLightItalic.otf</string>
		<string>EuropaRegular.otf</string>
		<string>EuropaRegularItalic.otf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>com.apple.developer.associated-domains</key>
	<array>
		<string>applinks:saltlending.tech</string>
		<string>applinks:saltlending.com</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>BootSplash.storyboard</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarHidden</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
