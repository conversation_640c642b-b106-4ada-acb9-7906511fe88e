# Resolve react_native_pods.rb with node to allow for hoisting
#require Pod::Executable.execute_command('node', ['-p',
#  'require.resolve(
#    "react-native/scripts/react_native_pods.rb",
#    {paths: [process.argv[1]]},
#  )', __dir__]).strip


def node_require(script)
  # Resolve script with node to allow for hoisting
  require Pod::Executable.execute_command('node', ['-p',
    "require.resolve('#{script}', {paths: [process.argv[1]]})", __dir__]).strip
end

# Use it to require both react-native's and this package's scripts:
node_require('react-native/scripts/react_native_pods.rb')
node_require('react-native-permissions/scripts/setup.rb')

# platform :ios, min_ios_version_supported
platform :ios, '14.0'

prepare_react_native_project!

setup_permissions([
  # 'AppTrackingTransparency',
  # 'Bluetooth',
  # 'Calendars',
  # 'CalendarsWriteOnly',
   'Camera',
  # 'Contacts',
  # 'FaceID',
   'LocationAccuracy',
   'LocationAlways',
   'LocationWhenInUse',
   'MediaLibrary',
  # 'Microphone',
  # 'Motion',
   'Notifications',
   'PhotoLibrary',
  # 'PhotoLibraryAddOnly',
  # 'Reminders',
  # 'Siri',
  # 'SpeechRecognition',
  # 'StoreKit',
])


# If you are using a `react-native-flipper` your iOS build will fail when `NO_FLIPPER=1` is set.
# because `react-native-flipper` depends on (FlipperKit,...) that will be excluded
#
# To fix this you can also exclude `react-native-flipper` using a `react-native.config.js`
# ```js
# module.exports = {
#   dependencies: {
#     ...(process.env.NO_FLIPPER ? { 'react-native-flipper': { platforms: { ios: null } } } : {}),
# ```
#flipper_config = ENV['NO_FLIPPER'] == "1" ? FlipperConfiguration.disabled : FlipperConfiguration.enabled
flipper_config = FlipperConfiguration.disabled

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target "mobile73" do
    config = use_native_modules!

    #use_frameworks!
    #$RNFirebaseAsStaticFramework = true


    use_react_native!(
      :path => config[:reactNativePath],
      # Enables Flipper.
      #
      # Note that if you have use_frameworks! enabled, Flipper will not work and
      # you should disable the next line.
      # :flipper_configuration => flipper_config,
      # An absolute path to your application root.
      :app_path => "#{Pod::Config.instance.installation_root}/.."
    )
    #pod 'Permission-Camera', :path => "../node_modules/react-native-permissions/ios/Camera"

    pod 'Firebase', :modular_headers => true
    pod 'FirebaseABTesting', :modular_headers => true
    pod 'FirebaseCore', :modular_headers => true
    pod 'FirebaseInstallations', :modular_headers => true
    pod 'FirebaseCoreInternal', :modular_headers => true
    pod 'GoogleUtilities', :modular_headers => true
    pod 'Plaid', :modular_headers => true
    #pod 'Plaid', '~> 12.0.0'

    use_frameworks!
    $RNFirebaseAsStaticFramework = true

    dynamic_frameworks = ['iProov']

    # make all the other frameworks into static frameworks by overriding the static_framework? function to return true
    pre_install do |installer|
      installer.pod_targets.each do |pod|
        if !dynamic_frameworks.include?(pod.name)
          puts "Overriding the static_framework? method for #{pod.name}"
          def pod.static_framework?;
            true
          end
          def pod.build_type;
            Pod::BuildType.static_library
          end
        end
      end
    end

    post_install do |installer|
      installer.pods_project.targets.each do |target|
        if dynamic_frameworks.include?(target.name)
          target.build_configurations.each do |config|
            config.build_settings['BUILD_LIBRARY_FOR_DISTRIBUTION'] = 'YES'
          end
       end
      end

      react_native_post_install(
        installer,
        config[:reactNativePath],
        :mac_catalyst_enabled => false
      )

      installer.pods_project.targets.each do |target|
        target.build_configurations.each do |config|
          config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
        end
      end
    end
end