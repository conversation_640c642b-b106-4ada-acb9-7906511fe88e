<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="14113" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina5_9" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="Aspect ratio constraints" minToolsVersion="5.1"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="ZKV-NQ-mxV"/>
                        <viewControllerLayoutGuide type="bottom" id="xRi-K5-4hW"/>
                    </layoutGuides>
                    <view key="view" contentMode="center" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="AppSplashLong.png" adjustsImageSizeForAccessibilityContentSizeCategory="YES" translatesAutoresizingMaskIntoConstraints="NO" id="hQB-oc-GXg">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="hQB-oc-GXg" secondAttribute="height" multiplier="375:812" id="7oN-Ad-Eue"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="hQB-oc-GXg" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" id="4RL-PD-VOy"/>
                            <constraint firstAttribute="trailing" secondItem="hQB-oc-GXg" secondAttribute="trailing" id="7js-Mu-D3x"/>
                            <constraint firstItem="hQB-oc-GXg" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="c9I-xB-VYD"/>
                            <constraint firstItem="hQB-oc-GXg" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="ud9-ym-uTg"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52" y="371.67487684729065"/>
        </scene>
    </scenes>
    <resources>
        <image name="AppSplashLong.png" width="3726" height="8067"/>
    </resources>
</document>
