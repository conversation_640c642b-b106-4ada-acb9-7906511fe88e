import React, {Component} from 'react'
import {View, Image, Dimensions, TouchableOpacity} from 'react-native'
import {connect} from 'react-redux'
import {WebView} from 'react-native-webview'
import LottieView from 'lottie-react-native'
import {decode} from 'html-entities'

import commonStyles from '../../styles/commonStyles'
import {BackgroundHeader, TextReg, TextBold, Button} from '..'
import config from '../../config.json'
import {toggleNewAgreement} from '../../store/user/user.actions'

const {height: ScreenHeight} = Dimensions.get('window')

class Agreements extends Component {
  constructor(props) {
    super(props)
    let contract = {body: null}
    let loaded = false
    if (this.props?.newAgreement?.body) {
      contract = this.props?.newAgreement
      loaded = true
    }
    this.state = {
      contract,
      signed: false,
      loading: false,
      loaded,
    }
  }

  componentDidMount() {
    //console.log('agreements mount');
    //this.getAgreement();
  }

  componentDidUpdate(prevProps) {
    if (prevProps?.newAgreement != this.props.newAgreement) {
      console.log(
        'componentDidUpdate newAgreement',
        prevProps?.newAgreement,
        this.props.newAgreement,
      )
      this.setState({contract: this.props.newAgreement, loaded: true})
    }
    /*
    if (!prevProps.user?.address && this.props.user?.address) {
      this.getAgreement();
    }
    */
  }

  /*
  getAgreement = () => {
    console.log('get agreement');
    if (!this.props.user?.address) return;

    let {countryCode, province} = this.props.user?.address;

    console.log('agreements this.props', this.props);

    this.props.WebService.getUnsignedAgreement('membership-agreement', countryCode, province, config.pactSafe.accessId)
      .then(res => {
        console.log('getUnsignedAgreement res', res);
        this.setState({contract: res.data, loaded: true});
      })
      .catch(err => {
        console.log('getUnsignedAgreement err', err);
      });
  };
  */

  agreePactSafe = () => {
    console.log(
      'agreePactSafe',
      this.state.contract.contract.toString(),
      this.state.contract.id,
      config.pactSafe.accessId,
    )

    this.setState({signed: true, loading: true})
    this.props.WebService.signAgreement({
      contractId: this.state.contract.contract.toString(),
      versionId: this.state.contract.id,
      siteId: config.pactSafe.accessId,
    })
      .then(async res => {
        console.log('agreePactSafe res', res)
        this.setState({loading: false, contract: {body: null}})
        this.props.dispatch(toggleNewAgreement({}))
      })
      .catch(err => {
        console.log('agreePactSafe err', err)
        this.setState({loading: false})
      })
  }

  render() {
    if (!this.state.contract.body) {
      return null
    }
    //
    const bodyDecoded = decode(this.state.contract.body)
    const contractStyling = `<style>
    div{
      overflow-x: hidden;
    }
    .ps-contract-body{
      margin-top: -6px;
    }
    .ps-section{
      margin-top: 10px;
      margin-bottom: 10px;
    }
    </style>`

    return (
      <View
        style={{
          alignSelf: 'stretch',
          height: ScreenHeight,
          zIndex: 60,
          alignItems: 'center',
          position: 'absolute',
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: '#28283D',
        }}>
        <BackgroundHeader title={'Terms'} />

        <TextReg style={{marginTop: 30, fontSize: 20, marginBottom: 20}}>
          Terms Have Changed
        </TextReg>
        <TextReg style={{width: 300, textAlign: 'center', marginBottom: 12}}>
          Our <TextBold>Terms of Use</TextBold> has changed, you must agree to the revised terms
          below before continuing.
        </TextReg>

        <View
          style={{
            height: 320,
            width: 350,
            borderRadius: 14,
          }}>
          {this.state.loaded ? (
            <WebView
              source={{
                html: `<div>${contractStyling} <div class="ps-contract-body ps-contract-full ps-contract">${bodyDecoded}</div></div>`,
              }}
              style={{height: 360, width: 350}}
              scalesPageToFit
              useWebKit={false}
            />
          ) : (
            <View
              style={{
                width: 350,
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: 320,
              }}>
              <LottieView
                ref={animation => {
                  this.animation = animation
                }}
                style={{
                  width: 60,
                  height: 60,
                  opacity: 0.9,
                }}
                source={require('../../imgs/lotti/loading-white-dots.json')}
                autoPlay
              />
            </View>
          )}
        </View>

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: 310,
            height: 120,
            borderRadius: 14,
          }}>
          <TouchableOpacity onPress={() => this.agreePactSafe()}>
            {this.state.signed ? (
              <Image source={require('../../imgs/check-box.png')} style={{height: 40, width: 40}} />
            ) : (
              <Image
                source={require('../../imgs/blank-check-box.png')}
                style={{height: 40, width: 40}}
              />
            )}
          </TouchableOpacity>
          {this.state.loading ? (
            <View
              style={{
                width: 260,
                flexDirection: 'column',
                alignItems: 'center',
                height: 60,
              }}>
              <LottieView
                ref={animation => {
                  this.animation = animation
                }}
                style={{
                  width: 60,
                  height: 60,
                  opacity: 0.9,
                }}
                source={require('../../imgs/lotti/loading-white-dots.json')}
                autoPlay
              />
            </View>
          ) : (
            <TextReg
              style={{
                color: '#FFF',
                fontSize: 14,
                width: 260,
                textAlign: 'center',
              }}>
              {`I have read, understand, and consent to the language and authorzations outlined in SALT's Membership Agreement.`}
            </TextReg>
          )}
        </View>
      </View>
    )
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  newAgreement: state.user.newAgreement,
})

export default connect(mapStateToProps)(Agreements)
