import React from 'react';
import { View } from 'react-native';
import { connect } from 'react-redux';
import commonStyles from '../../styles/commonStyles';
import { Background } from '..';

const LoadingScreen = () => (
  <View style={commonStyles.container}>
    <Background />
  </View>
);

const mapStateToProps = (state) => ({
  WebService: state.auth.WebService
});

export default connect(mapStateToProps)(LoadingScreen);
