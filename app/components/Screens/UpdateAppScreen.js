import React from 'react';
import {View, Image, Text} from 'react-native';
import {connect} from 'react-redux';

import {Button} from '..';
import commonStyles from '../../styles/commonStyles';

const UpdateAppScreen = props => (
  <View style={commonStyles.container}>
    <Image
      source={require('../../imgs/updatePhonePic.png')}
      style={commonStyles.updatePhonePic}
    />
    <Text style={commonStyles.updateTitle}>New App Version</Text>
    <Text style={commonStyles.updateDescription}>
      Before continuing, please update this app with the latest version
    </Text>
    {props.showUpdateError && (
      <Text style={commonStyles.updateErrorText}>Error Opening Store</Text>
    )}
  </View>
);

//<Button onPress={props.handleUpdatePress}>Update</Button>

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
});

export default connect(mapStateToProps)(UpdateAppScreen);
