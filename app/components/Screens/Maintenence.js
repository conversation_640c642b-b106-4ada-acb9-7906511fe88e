import React from 'react';
import { View, Image } from 'react-native';
import { connect } from 'react-redux';
import commonStyles from '../../styles/commonStyles';
import { BackgroundHeader, TextReg, Button } from '..';

const LoadingScreen = () => (
  <View style={commonStyles.container}>
    <BackgroundHeader title={'Maintenence'} />

    <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
      <Image source={require('../../imgs/graphics/maintenance.png')} style={{ height: 160, width: 212, marginTop: -60 }} />
      <TextReg style={{ width: 300, textAlign: 'center', fontSize: 16, marginTop: 10 }}>
				We are performing scheduled maintenance Please try again soon.
      </TextReg>
      <TextReg style={{ marginTop: 30 }}>Last Checked : '1234'</TextReg>
      <Button>Refresh</Button>
    </View>
  </View>
);

const mapStateToProps = (state) => ({
  WebService: state.auth.WebService
});

export default connect(mapStateToProps)(LoadingScreen);
