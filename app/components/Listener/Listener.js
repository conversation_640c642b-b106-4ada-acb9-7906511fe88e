import React, {useEffect} from 'react'
import {useDispatch} from 'react-redux'
import {useNavigationState} from '@react-navigation/native'
import {updatePage} from '../../store/user/user.actions'

const Listener = () => {
  const dispatch = useDispatch()
  const routes = useNavigationState(state => state.routes)
  const currentRoute = routes[routes.length - 1].name

  useEffect(() => {
    changed(currentRoute)
  }, [currentRoute])

  let changed = route => {
    dispatch(updatePage(route))
  }

  return null
}

export default Listener
