import React, {Component} from 'react'
import {TouchableOpacity, Image, View} from 'react-native'
import TextReg from '../Text/TextReg'
import LottieView from 'lottie-react-native'
import Heap from '@heap/react-native-heap'

import styles from './styles'

class But<PERSON> extends Component {
  render() {
    let {isLoading, disabled, theme, style, children, title, ...props} = this.props

    return isLoading ? (
      <View style={[styles.whiteDotsAnimationBox, styles[theme], style, disabled && styles.disabled, {backgroundColor: 'none'}]}>
        <LottieView
          ref={animation => {
            this.animation = animation
          }}
          style={styles.whiteDotsAnimation}
          source={require('../../imgs/lotti/loading-white-dots.json')}
          autoPlay
        />
      </View>
    ) : (
      <TouchableOpacity
        style={[styles.button, styles[theme], style, disabled && styles.disabled]}
        disabled={disabled}
        {...props}
        title={'test'}>
        <TextReg style={[styles[`${theme}Text`], styles.buttonText, disabled && styles.disabledText]}>{children}</TextReg>
      </TouchableOpacity>
    )
  }
}

Button.defaultProps = {
  isLoading: false,
  theme: 'primary',
  disabled: false,
}

export default Button
