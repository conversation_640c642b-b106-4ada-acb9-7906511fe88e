import { StyleSheet } from 'react-native'

const styles = StyleSheet.create({
  button: {
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    zIndex: 5,
    minWidth: 220,
    height: 50,
    fontSize: 22,
    paddingVertical: 9,
    paddingHorizontal: 21
  },
  buttonText: {
    fontSize: 22,
    marginBottom: -2,
    marginTop: -2
  },
  loadingDots: {
    height: 50,
    width: 80,
    opacity: 0.6,
    alignSelf: 'center',
    zIndex: 5
  },
  disabled: {
    backgroundColor: 'lightgray',
    borderColor: 'lightgray'
  },
  disabledText: {
    color: '#fff'
  },
  primary: {
    backgroundColor: '#00FFBD'
  },
  primaryText: {
    color: '#000'
  },
  secondary: {
    backgroundColor: '#00ffc3',
    borderColor: '#05868e'
  },
  secondaryText: {
    color: '#000'
  },
  whiteDotsAnimation: {
    width: 60,
    height: 60,
    opacity: 0.9,
    alignSelf: 'center',
    zIndex: 5
  },
  whiteDotsAnimationBox: {
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    zIndex: 5,
    minWidth: 220,
    height: 50,
    fontSize: 22,
    paddingVertical: 9,
    paddingHorizontal: 21
  }
})

export default styles
