import React, { Component } from 'react'
import { View, FlatList, TouchableOpacity, ScrollView, RefreshControl } from 'react-native'
import { connect } from 'react-redux'
import { tokenList } from '../../util/enumerables'
import { TextReg } from '../../components'
import { updateUnread, getNotifications, updateNotificationArr } from '../../store/notifications/notifications.actions'
import styles from './styles'

class NotificationsScroll extends Component {
  constructor(props) {
    super(props)
    this.state = {
      error: false,
      refreshing: true,
      page: 1,
      showOptions: false
    }
  }

  componentDidUpdate(prevProps, prevState) {
    if (this.props.unread > prevProps.unread) {
      this.getNotifications()
    }
    if (prevProps.showPinScreen !== this.props.showPinScreen && this.props.showPinScreen === false) {
      this.getNotifications()
    }
    if (this.props.refreshCount !== prevProps.refreshCount) {
      this.getNotifications()
    }
  }

  componentWillUnmount() {
    this.setState({ showOptions: false })
  }

  getNotifications = async () => {
    this.setState({ refreshing: true })
    await this.props.dispatch(getNotifications(this.state.page))
    this.setState({ refreshing: false })
  }

  padStartTwo = (num) => {
    if (num.length === 1) {
      return `0${num}`
    }
  }

  clickedNotif = (notif) => {
    if (!notif.viewedAt) {
      this.props.WebService.readNotification(notif.id)
      this.props.dispatch(updateUnread(this.props.unread - 1))
      const notificationArr = this.props.notificationArr.map((a) => {
        if (a.id === notif.id) {
          a.viewedAt = 'read'
        }
        return a
      })
      this.props.dispatch(updateNotificationArr(notificationArr))
    }

    if (notif.relationType === 'loan') {
      //this.props.navigation.navigate('Loans')
      return
    } else if (notif.relationType === 'wallet') {
      const arrOfTokens = tokenList

      let includesToken = ''
      arrOfTokens.map((a, k) => {
        if (notif.text.includes(a)) {
          includesToken = a
        }
      })

      if (includesToken !== '') {
        //console.log(`this.props.navigation.navigate('Collateral Detail')`)
        //have to figure a way to navigate outside of navigator
        /*
        this.props.navigation.dispatch(
          NavigationActions.reset({
            index: 0,
            key: 'Collateral',
            actions: [
              NavigationActions.navigate({
                routeName: 'Collateral',
              }),
            ],
          })
        )
        this.props.navigation.navigate('Detail', { title: includesToken })
        */
      }
    }
  }

  /*
  readAllNotifs = () => {
    this.props.WebService.readAllNotifications()
      .then(res => {
        const notificationArr = this.state.notificationArr.map(a => {
          a.viewedAt = 'read'
          return a
        })
        this.setState({ notificationArr })
        this.props.dispatch(updateUnread(0))
        this.setState({ showOptions: false })
        this.getNotifications()
      })
      .catch(() => {
        this.setState({ showOptions: false })
      })
  }
  */

  loadMore = () => {
    this.setState({ page: this.state.page + 1 }, () => this.getNotifications())
  }

  render() {
    let lastDate = ''

    const showNotifs = this.props.notificationArr?.map((item, k) => {
      let dateLine = null
      if (item.date !== lastDate) {
        dateLine = (
          <View style={styles.listItemDate}>
            <TextReg style={styles.listItemDateText}>{item.date}</TextReg>
          </View>
        )
        lastDate = item.date
      }

      let showLoadMore = false
      if (
        item === this.props.notificationArr[this.props.notificationArr.length - 1] &&
        this.props.totalNotifs > 100 * this.state.page
      ) {
        showLoadMore = true
      }
      return (
        <View key={k}>
          {dateLine}
          <TouchableOpacity onPress={() => this.clickedNotif(item)}>
            <View style={styles.listItem}>
              <View style={styles.notifScrollBox}>
                <View style={{ flexDirection: 'row' }}>
                  {!item.viewedAt ? <View style={styles.unreadNotifDot} /> : <View />}
                  <TextReg style={styles.notifEvent}>{item.event}</TextReg>
                </View>
                <TextReg style={styles.listItemTimeText}>{item.time}</TextReg>
              </View>

              <View style={styles.listItemInner}>
                <TextReg style={styles.listItemText}>{item.text}</TextReg>
              </View>
            </View>
          </TouchableOpacity>
          {showLoadMore && (
            <TouchableOpacity onPress={() => this.loadMore()}>
              <View style={styles.listItemLoadMore}>
                <TextReg style={styles.listItemTextLoadMore}>Load More</TextReg>
              </View>
            </TouchableOpacity>
          )}
        </View>
      )
    })

    return (
      <ScrollView
        style={[styles.swiperContainer, { marginTop: 10 }]}
        refreshControl={
          <RefreshControl
            refreshing={this.state.refreshing}
            onRefresh={this.getNotifications}
            colors={['#28283D']}
            progressBackgroundColor={['#FFFFFF70']}
            tintColor={'#fff'}
          />
        }
      >
        {this.state.error && (
          <View style={styles.showErrorBox}>
            <TextReg style={styles.showErrorText}>Error</TextReg>
          </View>
        )}
        {this.props.notificationArr && this.props.notificationArr.length === 0 ? (
          <View style={styles.noNotifsBox}>
            <TextReg style={styles.noNotifsText}>No Notifications</TextReg>
          </View>
        ) : (
          <>{showNotifs}</>
        )}
      </ScrollView>
    )
  }
}

const mapStateToProps = (state) => ({
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
  unread: state.notifications.unread,
  showPinScreen: state.auth.pinScreen,
  totalNotifs: state.notifications.totalNotifs,
  notificationArr: state.notifications.notificationArr,
  refreshCount: state.user.refreshCount
})

export default connect(mapStateToProps)(NotificationsScroll)
