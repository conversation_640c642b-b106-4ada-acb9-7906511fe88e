import React, { Component } from 'react'
import { View, TouchableOpacity, Animated, Dimensions, Image, Platform, StatusBar } from 'react-native'

import { connect } from 'react-redux'
import { showNotifications } from '../../store/notifications/notifications.actions'
import NotificationsScroll from '../../components/Notifications/NotificationsScroll'

const { width: ScreenWidth, height: ScreenHeight } = Dimensions.get('window')
import { TextReg } from '../../components'
import styles from './styles'

const notifWidth = ScreenWidth - 60

class Notifications extends Component {
  constructor(props) {
    super(props)
    this.state = {
      notificationArr: null,
      error: false,
      refreshing: true,
      page: 1,
      totalNotifs: 0,
      showOptions: false
    }

    this.inputs = {}
    this.scrollValue = new Animated.Value(ScreenWidth)
    this.modalOpacity = new Animated.Value(0)
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    if (!prevProps.showNotifications && this.props.showNotifications) {
      this.enterNotifications()
    }
  }

  enterNotifications = () => {
    Animated.sequence([
      Animated.timing(this.modalOpacity, {
        toValue: 1,
        duration: 0,
        useNativeDriver: true
      }),
      Animated.timing(this.scrollValue, {
        toValue: 0,
        duration: notifWidth,
        useNativeDriver: true
      })
    ]).start()
  }

  exitNotifications = () => {
    Animated.sequence([
      Animated.timing(this.scrollValue, {
        toValue: ScreenWidth,
        duration: notifWidth,
        useNativeDriver: true
      }),
      Animated.timing(this.modalOpacity, {
        toValue: 0,
        duration: 0,
        useNativeDriver: true
      })
    ]).start(() => {
      //this.setState({ showFadeWall: false })
      this.props.dispatch(showNotifications(false))
    })
  }

  notifSettingsPress = () => {
    console.log('im not sure if we can navigate to settings from here yet')
  }

  render() {
    let closeViewHeight = 30
    let closeTopMargin = 44
    if (Platform.OS === 'android') {
      closeViewHeight = StatusBar.currentHeight
      closeTopMargin = StatusBar.currentHeight + 10
    }

    const colorInterpolated = this.scrollValue.interpolate({
      inputRange: [0, notifWidth],
      outputRange: ['rgba(255,255,255,1)', 'rgba(0,0,0,1)']
    })

    return (
      <Animated.View
        style={[
          styles.notificationsBox,
          {
            width: ScreenWidth,
            opacity: this.modalOpacity,
            transform: [{ translateX: this.scrollValue }]
          }
        ]}
      >
        <Animated.View
          style={{
            //flexDiretion: 'row',
            alignItems: 'flex-start',
            opacity: this.modalOpacity
          }}
        >
          <TouchableOpacity onPress={() => this.exitNotifications()}>
            <Animated.View
              style={{
                height: ScreenHeight,
                width: ScreenWidth - notifWidth,
                //flexDiretion: 'column',
                justifyContent: 'space-between'
                //backgroundColor: colorInterpolated,
              }}
            >
              <View
                style={{
                  height: closeViewHeight,
                  paddingTop: closeTopMargin,
                  alignSelf: 'stretch',
                  alignItems: 'center'
                }}
              >
                <Image source={require('../../imgs/notifClose.png')} style={{ height: 0, width: 0 }} />
              </View>
            </Animated.View>
          </TouchableOpacity>
          <View style={[styles.notificationsRight, { left: ScreenWidth - notifWidth }]}>
            <View style={styles.notificationsHeader}>
              <TextReg style={{ color: '#FFF', fontSize: 22 }}>Notifications</TextReg>
              <View />
            </View>
            <NotificationsScroll />
          </View>
        </Animated.View>
      </Animated.View>
    )
  }
}

Notifications.navigationOptions = ({ navigation }) => ({
  title: null,
  header: <View style={{ height: 40, position: 'absolute' }} />
})

const mapStateToProps = (state) => ({
  loanData: state.user.loanData,
  WebService: state.auth.WebService,
  showNotifications: state.notifications.showNotifications,
  unread: state.notifications.unread
})

export default connect(mapStateToProps)(Notifications)
