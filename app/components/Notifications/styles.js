import { StyleSheet, Platform } from 'react-native'

const $offWhite = '#eef0f0'

const styles = StyleSheet.create({
  notificationsBox: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    right: 0,
    left: 0,
  },
  notificationsHeader: {
    marginTop: Platform.OS === 'ios' ? 44 : 60,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 16,
    marginRight: 16,
    alignItems: 'center',
  },
  notificationsRight: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    right: 0,
    backgroundColor: '#28283d', //'#e6e6e6',
  },
  header: {
    alignSelf: 'stretch',
    height: 10,
  },
  list: {
    flex: 1,
    alignSelf: 'stretch',
  },
  listItem: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    alignSelf: 'stretch',
    //borderWidth: 0.5,
    //borderColor: $offWhite,
    marginLeft: 16,
    marginRight: 16,
    paddingLeft: 12,
    paddingRight: 10,
    paddingTop: 10,
    paddingBottom: 10,
    backgroundColor: '#FFFFFF20',
    borderRadius: 14,
    marginBottom: 10,
  },
  listItemInner: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    alignSelf: 'stretch',
  },
  listItemText: {
    fontSize: 15,
    color: '#FFF',
  },
  listItemDate: {
    height: 32,
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingBottom: 6,
    paddingLeft: 20,
  },
  listItemDateText: {
    fontSize: 12,
    color: '#FFF',
  },
  listItemTimeText: {
    fontSize: 12,
    color: '#FFF',
    marginTop: 4,
  },
  swiperContainer: {
    flex: 1,
    alignSelf: 'stretch',
    flexDirection: 'column',
  },
  showErrorBox: {
    marginTop: 20,
    flexDirection: 'column',
    alignSelf: 'stretch',
    alignItems: 'center',
  },
  showErrorText: {
    fontSize: 18,
    color: '#FFF',
  },
  noNotifsBox: {
    alignSelf: 'stretch',
    marginTop: 100,
  },
  noNotifsText: {
    color: '#FFF',
    fontSize: 18,
    textAlign: 'center',
  },
  unreadNotifDot: {
    height: 12,
    width: 12,
    borderRadius: 6,
    backgroundColor: '#ef5656',
    marginTop: 7,
    marginRight: 7,
    marginLeft: 0,
  },

  //Load More
  listItemLoadMore: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'stretch',
    //borderWidth: 0.5,
    //borderColor: $offWhite,
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 20,
    paddingBottom: 12,
  },
  listItemTextLoadMore: {
    fontSize: 17,
    color: '#e6e6e6',
    height: 30,
    textAlign: 'center',
  },

  //Read all
  readAllText: {
    color: '#FFF',
    marginRight: 6,
  },
  optionsContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
    flexDirection: 'column',
    backgroundColor: 'rgba(60, 60, 60, 0.5)',
  },
  optionsRow: {
    alignSelf: 'stretch',
    height: 56,
    backgroundColor: '#FFF',
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  optionButton: {
    flex: 1,
    alignSelf: 'stretch',
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionText: {
    fontSize: 17,
    paddingLeft: 26,
    fontFamily: 'Europa-Regular',
  },
  moreDots: {
    height: 6,
    width: 28,
    marginRight: 16,
  },

  notifScrollBox: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignSelf: 'stretch',
    marginLeft: 0,
    marginBottom: 6,
    marginRight: 4,
  },
  notifEvent: {
    color: '#FFF',
    fontSize: 20,
    width: 180,
  },
})

export default styles
