import React, {Component} from 'react'
import {View, TouchableOpacity, Animated, Dimensions, Image, Platform, StatusBar} from 'react-native'

import {connect} from 'react-redux'
import {showToast} from '../../store/notifications/notifications.actions'

const {width: ScreenWidth, height: ScreenHeight} = Dimensions.get('window')
import {TextReg} from '../../components'
import styles from './styles'

const notifWidth = ScreenWidth - 60

class Toast extends Component {
  constructor(props) {
    super(props)
    this.state = {
      notificationArr: null,
      error: false,
      refreshing: true,
      page: 1,
      totalNotifs: 0,
      showOptions: false,
    }

    this.inputs = {}
    this.scrollValue = new Animated.Value(-130) //ScreenWidth
    this.modalOpacity = new Animated.Value(1)
  }

  componentDidUpdate(prevProps, prevState) {
    if (!prevProps.showToast && this.props.showToast) {
      this.enterNotifications()
      setTimeout(() => {
        this.closeToast()
      }, 7000)
    }
    if (prevProps.showToast && !this.props.showToast) {
      this.exitNotifications()
    }
  }

  enterNotifications = () => {
    Animated.sequence([
      Animated.timing(this.modalOpacity, {
        toValue: 1,
        duration: 0,
        useNativeDriver: true,
      }),
      Animated.timing(this.scrollValue, {
        toValue: 0,
        duration: notifWidth,
        useNativeDriver: true,
      }),
    ]).start()
  }

  exitNotifications = () => {
    Animated.sequence([
      Animated.timing(this.scrollValue, {
        toValue: -120,
        duration: notifWidth,
        useNativeDriver: true,
      }),
      Animated.timing(this.modalOpacity, {
        toValue: 0,
        duration: 0,
        useNativeDriver: true,
      }),
    ]).start()
  }

  clickToast = () => {
    this.props.goTo()
    this.props.dispatch(showToast(false))
  }

  closeToast = () => {
    this.props.dispatch(showToast(false))
  }

  render() {
    if (Platform.OS === 'android') {
      closeViewHeight = StatusBar.currentHeight
      closeTopMargin = StatusBar.currentHeight + 10
    }

    return (
      <Animated.View
        style={[
          styles.notificationsBox,
          {
            width: ScreenWidth,
            opacity: this.modalOpacity,
            transform: [{translateY: this.scrollValue}],
            zIndex: 50,
          },
          styles.toastBox,
        ]}>
        <TouchableOpacity style={styles.toastCloseButton} onPress={() => this.closeToast()}>
          <Image source={require('../../imgs/notifClose.png')} style={{height: 26, width: 26}} />
        </TouchableOpacity>
        <TextReg style={{color: '#FFF', fontSize: 20, marginBottom: 8}}>Access Blocked</TextReg>
        <TouchableOpacity onPress={() => this.clickToast()}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <TextReg style={{color: '#FFF', fontSize: 16, marginRight: 5}}>{`To access, please setup 2FA Auth`}</TextReg>
            <Image source={require('../../imgs/rightArrowWhite.png')} style={styles.toastWhiteArrow} />
          </View>
        </TouchableOpacity>
      </Animated.View>
    )
  }
}

const mapStateToProps = state => ({
  loanData: state.user.loanData,
  WebService: state.auth.WebService,
  showNotifications: state.notifications.showNotifications,
  unread: state.notifications.unread,
  showToast: state.notifications.showToast,
})

export default connect(mapStateToProps)(Toast)
