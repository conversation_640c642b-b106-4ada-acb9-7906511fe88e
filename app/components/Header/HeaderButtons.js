import React from 'react'
import { View, TouchableOpacity, Image, SafeAreaView } from 'react-native'

import closeWithdrawImg from '../../imgs/notifClose.png'
import closeWithdrawImgDark from '../../imgs/notifCloseDark.png'

import { TextReg } from '../../components'
import styles from './styles'

const HeaderButtons = props => (
  <SafeAreaView style={{ alignSelf: 'stretch' }}>
    <View style={styles.headerButtonBox}>
      <View style={styles.headerButtonRow}>
        {props.goBack ? (
          <TouchableOpacity onPress={() => props.goBack()}>
            <View style={styles.goBackBox}>
              <Image
                source={require('../../imgs/backToSettings.png')}
                style={styles.goBackImg}
              />
            </View>
          </TouchableOpacity>
        ) : (
          <View style={{ width: 42 }} />
        )}

        <TextReg style={{ color: props.dark ? '#000' : '#FFF', fontSize: 24 }}>
          {props.title}
        </TextReg>

        <TouchableOpacity onPress={() => props.close()} style={{ width: 42 }}>
          <Image
            source={props.dark ? closeWithdrawImgDark : closeWithdrawImg}
            style={styles.closeWithdrawImg}
          />
        </TouchableOpacity>
      </View>
    </View>
  </SafeAreaView>
)

export default HeaderButtons
