import React, {useState, useEffect} from 'react'
import {Modal, View, Image, TouchableOpacity, ScrollView, Dimensions} from 'react-native'
import {TextReg, TextBold} from '../../components'

let DistBar = ({wallets}) => {
  let getColor = token => {
    switch (token) {
      case 'BTC':
        return '#F90'
      case 'BCH':
        return '#8BDD77'
      case 'ETH':
        return '#6987F6'
      case 'LTC':
        return '#AEAEAE'
      case 'USDC':
        return '#4184E6'
      case 'USDT':
        return '#3C8B8C'
      case 'TUSD':
        return '#3f9192'
      case 'USDP':
        return '#FDF9D4'
      case 'SALT':
        return '#828282'
      case 'XRP':
        return '#828282'
      default:
        return '#F90'
    }
  }

  let totalVal = 0
  wallets?.map(a => {
    totalVal += Number(a.value)
  })
  let percentages = wallets?.map(a => {
    return {per: Number(a.value) / totalVal, color: getColor(a.currency)}
  })
  console.log('per', percentages)
  /*
  [
  {per: 0, color: '#E1E7FD'}
  {per: 1, color: '#4390F7'}
  ]
  */
  return (
    <View style={local.distBar}>
      {percentages?.map((p, index) => {
        return (
          <View
            key={index}
            style={{
              flex: p.per,
              backgroundColor: p.color,
              height: '100%',
            }}
          />
        )
      })}
    </View>
  )
}

let local = {
  distBar: {
    height: 8,
    borderRadius: 4,
    alignSelf: 'stretch',
    marginRight: 70,
    backgroundColor: '#F90',
    marginTop: 10,
    overflow: 'hidden',
    marginBottom: 20,
    flexDirection: 'row',
  },
}

export default DistBar
