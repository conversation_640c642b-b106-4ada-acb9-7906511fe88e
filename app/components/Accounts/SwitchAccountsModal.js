import React, { Component } from 'react'
import { View, TouchableOpacity, Image, Keyboard, TextInput } from 'react-native'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { connect } from 'react-redux'
import { TextBold, TextReg } from '..'
import {
  updateLoans,
  updateUser,
  updateAccount,
  updateWallets,
  //updateLoanStatus,
  resetWallets,
  increaseRefreshDataCount
} from '../../store/user/user.actions'
import { isAuthed } from '../../store/auth/auth.actions'

import { dig } from '../../util/helpers'
import { derivedStatusMap } from '../../util/enumerables'
import styles from './styles'

class SwitchAccountsModal extends Component {
  constructor(props) {
    super(props)
    this.state = {
      activeRef: this.props?.accountRef || 1,
      createAccount: false,
      updateNewAccountName: '',
      updateNewAccountType: 'Personal',
      newAccountLoading: false,
      error: false,
      success: false
    }
    this.inputs = []
  }

  componentDidMount() {
    //const lowerCaseEmail = this.props.user?.primaryEmail?.toLowerCase();
    /*
    this.props.AsyncStorage.getItem(`REF-${lowerCaseEmail}`)
      .then((lastRef) => {
        if (lastRef) {
          this.setState({ activeRef: lastRef });
        }
      })
      .catch((err) => {
        console.log('err', err);
      });
      */
    console.log('accountRef', this.props.accountRef)
  }

  changeAccount = async (ref) => {
    if (ref == this.state.activeRef) {
      return
    }

    this.props.WebService.updateRef(ref)
    ref = ref.toString()

    const email = this.props.user?.primaryEmail?.toLowerCase()
    await AsyncStorage.setItem(`REF-${email}`, ref)
    this.props.dispatch(isAuthed({ ref, email: this.props.user.primaryEmail }))
    this.setState({ activeRef: ref })
    if (ref == 0) {
      this.props.navigation.navigate('Home')
    }
    //this.refreshData();
    this.props.dispatch(resetWallets())
    this.props.dispatch(increaseRefreshDataCount())
    this.props.navigation.navigate('Home')
    this.props.close()
  }

  refreshData = () => {
    this.getLoans()
    this.getWallets()
    this.getUser()
  }

  getLoans = () => {
    this.props.WebService.getLoans().then((res) => {
      const loanStatus = derivedStatusMap.get(dig(res.data[0], 'status'))
      if (res.data.length > 0 && loanStatus === 'active') {
        this.props.dispatch(updateLoans(res.data[0]))
      } else if (loanStatus === 'pending') {
        this.props.dispatch(updateLoans(res.data[0]))
      }
      if (res.data.length === 0) {
        this.props.dispatch(updateLoans({ status: 'none' }))
      }
    })
  }

  getWallets = () => {
    this.props.WebService.getWallets(this.props.accountRef)
      .then((res) => {
        res.data.map((wallet) => this.props.dispatch(updateWallets(wallet)))
      })
      .catch((err) => {
        throw err
      })
  }

  getUser = () => {
    this.props.WebService.getSaltUser().then((res) => {
      this.props.dispatch(updateUser(res.data))
      this.getAccounts()
    })
  }

  getAccounts = () => {
    this.props.WebService.getSaltAccount().then((res) => {
      this.props.dispatch(updateAccount(res.data))
    })
  }

  showCreateAccount = () => {
    this.props.createAccountPage()
  }

  updateNewAccountName = (text) => {
    this.setState({ updateNewAccountName: text })
  }

  updateNewAccountType = (type) => {
    this.setState({ updateNewAccountType: type })
  }

  createNewAccount = () => {
    this.setState({ newAccountLoading: true })
    this.props.WebService.createSaltAccount({
      name: this.state.updateNewAccountName,
      type: this.state.updateNewAccountType.toLowerCase()
    })
      .then((res) => {
        this.getAccounts()
        this.setState({
          error: false,
          success: true,
          createAccount: false
        })
      })
      .catch((err) => {
        this.setState({
          error: true
        })
      })
  }

  render() {
    let showusers = <View />
    if (this.props.user.accounts && this.props.user.accounts.length >= 1) {
      showusers = this.props.user.accounts.map((a, k) => (
        <TouchableOpacity key={k} onPress={() => this.changeAccount(a.ref)}>
          <View
            style={{
              width: 260,
              borderBottomWidth: 0.5,
              borderColor: '#f0f0f0',
              paddingBottom: 4,
              flexDirection: 'row',
              height: 54,
              alignItems: 'center'
            }}
          >
            {this.state.activeRef == a.ref ? (
              <Image
                source={require('../../imgs/checkmarkCircle.png')}
                style={{ height: 20, width: 20, marginLeft: 10 }}
              />
            ) : (
              <View
                style={{
                  backgroundColor: '#555',
                  height: 20,
                  width: 20,
                  borderRadius: 10,
                  marginLeft: 10
                }}
              />
            )}
            <TextReg style={{ fontSize: 17, marginLeft: 10 }}>{a.name}</TextReg>
          </View>
        </TouchableOpacity>
      ))
    }
    return (
      <View style={styles.switchContainer}>
        {this.state.createAccount ? (
          <View style={styles.switchBox}>
            <View style={styles.switchHeader}>
              <TextBold style={{ fontSize: 18, marginTop: 4, marginBottom: 4 }}>Create Account</TextBold>
              <TouchableOpacity onPress={() => this.props.close()}>
                <Image source={require('../../imgs/closeX.png')} style={{ height: 30, width: 30 }} />
              </TouchableOpacity>
            </View>
            <TextReg style={{ marginTop: 14, fontSize: 16, marginBottom: 4 }}>Account Name:</TextReg>
            <TextInput
              blurOnSubmit={false}
              onChangeText={(text) => this.updateNewAccountName(text)}
              onSubmitEditing={() => Keyboard.dismiss()}
              ref={(input) => (this.inputs.accountName = input)}
              returnKeyType={'next'}
              textContentType="none"
              underlineColorAndroid="transparent"
              value={this.state.newAccountName}
              style={styles.switchInput}
              autoFocus
              keyboardAppearance="dark"
            />
            <TextReg style={{ marginTop: 14, fontSize: 16, marginBottom: 4 }}>Account Type:</TextReg>
            <TouchableOpacity onPress={() => this.updateNewAccountType('Personal')} style={styles.switchPersonalButton}>
              <Image
                source={require('../../imgs/accountPersonal.png')}
                style={{ height: 36, width: 36, marginRight: 20 }}
              />
              <TextBold>Personal</TextBold>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => this.updateNewAccountType('Business')} style={styles.switchBusinessButton}>
              <Image
                source={require('../../imgs/accountBusiness.png')}
                style={{ height: 50, width: 30, marginRight: 20 }}
              />
              <TextBold>Business</TextBold>
            </TouchableOpacity>

            <TouchableOpacity onPress={() => this.createNewAccount()}>
              <TextBold>Create</TextBold>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.switchBox}>
            <View style={styles.switchAccountsTitle}>
              <TextBold style={{ fontSize: 18, marginTop: 4, marginBottom: 4 }}>Switch Accounts</TextBold>
              <TouchableOpacity onPress={() => this.props.close()}>
                <Image source={require('../../imgs/closeX.png')} style={{ height: 30, width: 30 }} />
              </TouchableOpacity>
            </View>
            {showusers}
            {this.props.user?.accounts?.length >= 2 && (
              <TouchableOpacity onPress={() => this.changeAccount(0)}>
                <View style={styles.rowOptionView}>
                  <Image source={require('../../imgs/icons/eye.png')} style={styles.eyeIcon} />
                  <TextBold style={styles.switchCreateAccountText}>VIEW ALL ACCOUNTS</TextBold>
                </View>
              </TouchableOpacity>
            )}
            {this.props.user.accounts && this.props.user.accounts.length <= 6 && (
              <TouchableOpacity style={{ marginTop: 10, marginBottom: 4 }} onPress={() => this.showCreateAccount()}>
                <View style={styles.rowOptionView}>
                  <Image source={require('../../imgs/icons/plusWhite.png')} style={styles.plusIcon} />
                  <TextBold style={styles.switchCreateAccountText}>CREATE ACCOUNT</TextBold>
                </View>
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>
    )
  }
}

const mapStateToProps = (state) => ({
  user: state.user.user,
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref
})

export default connect(mapStateToProps)(SwitchAccountsModal)
