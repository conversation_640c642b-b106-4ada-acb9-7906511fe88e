import React, {Component} from 'react'
import {View, Modal, TextInput, Keyboard, TouchableOpacity, Image} from 'react-native'
import {connect} from 'react-redux'
import AsyncStorage from '@react-native-async-storage/async-storage'

import {updateAccount, resetWallets, increaseRefreshDataCount} from '../../store/user/user.actions'
import {isAuthed} from '../../store/auth/auth.actions'

import {TextReg, BackgroundHeader, TextBold, Button} from '../../components'
import styles from './styles'

class CreateAccountModal extends Component {
  constructor(props) {
    super(props)
    let accounts = this.props?.user?.accounts
    console.log('accounts', accounts)
    let accountType = 'Personal'
    let personalAcc = accounts.filter(a => a.type == 'personal' && a.productType == 'loan') || []
    if (personalAcc.length > 0) {
      accountType = 'Business'
    }

    this.state = {
      accountType,
      accountName: '',
      newAccountLoading: false,
      typeLeverage: props.typeLeverage || null,
    }
    this.inputs = []
  }

  getAccounts = () => {
    /*
    this.props.WebService.getSaltAccount().then(res => {
      this.props.dispatch(updateAccount(res.data));
    });
    */
  }

  /*
  switchToNewRef = async ref => {
    this.props.WebService.updateRef(ref);
    ref = ref.toString();

    const email = this.props.user?.primaryEmail?.toLowerCase();
    await AsyncStorage.setItem(`REF-${email}`, ref);
    this.props.dispatch(isAuthed({ref, email: this.props.user.primaryEmail}));
    this.setState({activeRef: ref});
    //this.refreshData();
    this.props.dispatch(resetWallets());
    this.props.dispatch(increaseRefreshDataCount());
    this.props.navigation.navigate('Home');
    this.props.close();
  };
  */

  createNewAccount = () => {
    //entity select flow
    if (true) {
      this.props.navigation.navigate('EntitySelect')
      return
    }
    let isTestAcc = this.props?.user?.primaryEmail == '<EMAIL>'
    if (isTestAcc) {
      this.props.navigation.navigate('LoanRequest')
      return
    }
    this.setState({newAccountLoading: true, error: false})

    this.props.WebService.createSaltAccount({
      name: this.state.accountName,
      type: this.state.accountType.toLowerCase(),
      leveraged: this.state.typeLeverage,
    })
      .then(res => {
        console.log('createNewAccount res', res)
        this.props.dispatch(increaseRefreshDataCount(this.state.accountName))
        this.props.navigation.navigate('Home')
        this.props.close()
        //this.getAccounts();
        //const newRef = res.data?.ref;
        /*
        this.setState({
          error: false,
          success: true,
          newAccountLoading: false,
        });
        */
        //this.switchToNewRef(newRef);
      })
      .catch(err => {
        console.log('catch err?', err)

        const errorText = err.data.body.error
        this.setState({
          error: errorText || true,
          newAccountLoading: false,
        })
      })
  }

  updateAccountType = accountType => {
    Keyboard.dismiss()
    this.setState({accountType})
  }

  updateAccountName = accountName => {
    this.setState({accountName})
  }

  render() {
    console.log('create account state', this.state)

    return (
      <View style={styles.accountModalBox}>
        <TouchableOpacity
          onPress={() => this.props.close()}
          style={{
            alignSelf: 'flex-end',
            marginBottom: -30,
          }}>
          <Image source={require('../../imgs/closeX.png')} style={{width: 30, height: 30, opacity: 0.5}} />
        </TouchableOpacity>
        <TextReg style={styles.accountNameTxt}>Account Name:</TextReg>
        <TextInput
          onChangeText={text => this.updateAccountName(text)}
          onSubmitEditing={() => Keyboard.dismiss()}
          onBlur={() => Keyboard.dismiss()}
          ref={input => (this.inputs.accountName = input)}
          returnKeyType={'next'}
          textContentType="none"
          underlineColorAndroid="transparent"
          value={this.state.accountName}
          style={styles.accountInput}
          autoFocus
          keyboardAppearance="dark"
        />

        <TextReg style={styles.accountTypeTxt}>Account Type:</TextReg>
        <View
          style={{
            opacity: this.state.accountType === 'Business' ? 0.3 : 1,
          }}>
          <TouchableOpacity onPress={() => this.updateAccountType('Personal')} activeOpacity={1} style={styles.accountPersonalButton}>
            {this.state.accountType === 'Personal' ? (
              <Image source={require('../../imgs/checkmarkCircle.png')} style={styles.checkmarkCircleImg} />
            ) : (
              <Image source={require('../../imgs/accountPersonal.png')} style={styles.accountPersonalImg} />
            )}
            <View style={{width: 170}}>
              <TextBold style={{color: '#00FFBD', fontSize: 17, marginBottom: 4}}>Personal Loan</TextBold>
              <TextReg>For indivduals leveraging their crypto</TextReg>
            </View>
          </TouchableOpacity>
        </View>
        <View
          style={{
            opacity: this.state.accountType === 'Personal' ? 0.3 : 1,
            marginBottom: 40,
          }}>
          <TouchableOpacity onPress={() => this.updateAccountType('Business')} activeOpacity={1} style={styles.accountBusinessButton}>
            {this.state.accountType === 'Business' ? (
              <Image source={require('../../imgs/checkmarkCircle.png')} style={styles.checkmarkCircleImg} />
            ) : (
              <Image source={require('../../imgs/accountBusiness.png')} style={styles.accountBusinessImg} />
            )}
            <View style={{width: 170}}>
              <TextBold style={{color: '#00FFBD', fontSize: 17, marginBottom: 4}}>Business Loan</TextBold>
              <TextReg style={{width: 140}}>Startups or companies that need growth</TextReg>
            </View>
          </TouchableOpacity>
        </View>

        <View
          style={{
            width: 260,
            alignItems: 'center',
            opacity: this.state.accountName.length > 0 && this.state.accountType !== '' ? 1 : 0.3,
          }}>
          <Button isLoading={this.state.newAccountLoading} onPress={() => this.createNewAccount()} style={styles.accountCreateButton}>
            <TextReg style={{color: '#000'}}>CREATE</TextReg>
          </Button>
        </View>
        {this.state.error && (
          <TextReg
            style={{
              color: '#E5705A',
              fontSize: 18,
              marginTop: 10,
              width: 260,
              textAlign: 'center',
            }}>{`${this.state.error}`}</TextReg>
        )}
      </View>
    )
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  account: state.auth.account,
})

export default connect(mapStateToProps)(CreateAccountModal)
