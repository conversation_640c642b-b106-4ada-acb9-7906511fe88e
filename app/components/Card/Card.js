import React from 'react';
import {View, Dimensions} from 'react-native';

import commonStyles from '../../styles/commonStyles';
//import CardView from 'react-native-cardview'

const {width: screenWidth} = Dimensions.get('window');

const Card = ({children, ...props}) => {
  const cardWidth = props.cardWidth || screenWidth - 28;
  /*
  return (
    <CardView
      cardElevation={1}
      cardMaxElevation={1}
      cornerRadius={14}
      style={{
        marginBottom: props.cardMarginBottom || 14,
        marginTop: props.marginTop || 0
      }}
    >
      <View style={[commonStyles.tileInner, { width: cardWidth }, props.style]}>{children}</View>
    </CardView>
  )
  */
  return (
    <View
      style={{
        marginBottom: props.cardMarginBottom || 14,
        marginTop: props.marginTop || 0,
        borderRadius: 14,
      }}>
      <View style={[commonStyles.tileInner, {width: cardWidth}, props.style]}>
        {children}
      </View>
    </View>
  );
};

export default Card;
