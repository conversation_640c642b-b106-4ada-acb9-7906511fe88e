import React, { Component } from 'react'
import { View, TouchableHighlight, TouchableOpacity } from 'react-native'

import { TextReg } from '../../components'
import styles from './styles'

export default class MyComponent extends Component {
  handleEnterPin = text => {
    this.props.handleEnterText(text)
  }

  bottomButton = () => {
    console.log('pressed keypad bottomButton')
  }

  render() {
    const underlayColor = '#ffffff30'
    return (
      <View style={styles.pinBottomBox}>
        <View style={styles.pinInputBox}>
          <View style={styles.pinInputRow}>
            <TouchableHighlight
              style={styles.pinInputButton}
              onPress={() => this.handleEnterPin(1)}
              underlayColor={underlayColor}
            >
              <View>
                <TextReg style={styles.pinInputNum}>1</TextReg>
                <TextReg style={styles.pinInputDetails} />
              </View>
            </TouchableHighlight>
            <TouchableHighlight
              style={styles.pinInputButton}
              onPress={() => this.handleEnterPin(2)}
              underlayColor={underlayColor}
            >
              <View>
                <TextReg style={styles.pinInputNum}>2</TextReg>
                <TextReg style={styles.pinInputDetails}>ABC</TextReg>
              </View>
            </TouchableHighlight>
            <TouchableHighlight
              style={styles.pinInputButton}
              onPress={() => this.handleEnterPin(3)}
              underlayColor={underlayColor}
            >
              <View>
                <TextReg style={styles.pinInputNum}>3</TextReg>
                <TextReg style={styles.pinInputDetails}>DEF</TextReg>
              </View>
            </TouchableHighlight>
          </View>
          <View style={styles.pinInputRow}>
            <TouchableHighlight
              style={styles.pinInputButton}
              onPress={() => this.handleEnterPin(4)}
              underlayColor={underlayColor}
            >
              <View>
                <TextReg style={styles.pinInputNum}>4</TextReg>
                <TextReg style={styles.pinInputDetails}>GHI</TextReg>
              </View>
            </TouchableHighlight>
            <TouchableHighlight
              style={styles.pinInputButton}
              onPress={() => this.handleEnterPin(5)}
              underlayColor={underlayColor}
            >
              <View>
                <TextReg style={styles.pinInputNum}>5</TextReg>
                <TextReg style={styles.pinInputDetails}>JKL</TextReg>
              </View>
            </TouchableHighlight>
            <TouchableHighlight
              style={styles.pinInputButton}
              onPress={() => this.handleEnterPin(6)}
              underlayColor={underlayColor}
            >
              <View>
                <TextReg style={styles.pinInputNum}>6</TextReg>
                <TextReg style={styles.pinInputDetails}>MNO</TextReg>
              </View>
            </TouchableHighlight>
          </View>
          <View style={styles.pinInputRow}>
            <TouchableHighlight
              style={styles.pinInputButton}
              onPress={() => this.handleEnterPin(7)}
              underlayColor={underlayColor}
            >
              <View>
                <TextReg style={styles.pinInputNum}>7</TextReg>
                <TextReg style={styles.pinInputDetails}>PQRS</TextReg>
              </View>
            </TouchableHighlight>
            <TouchableHighlight
              style={styles.pinInputButton}
              onPress={() => this.handleEnterPin(8)}
              underlayColor={underlayColor}
            >
              <View>
                <TextReg style={styles.pinInputNum}>8</TextReg>
                <TextReg style={styles.pinInputDetails}>TUV</TextReg>
              </View>
            </TouchableHighlight>
            <TouchableHighlight
              style={styles.pinInputButton}
              onPress={() => this.handleEnterPin(9)}
              underlayColor={underlayColor}
            >
              <View>
                <TextReg style={styles.pinInputNum}>9</TextReg>
                <TextReg style={styles.pinInputDetails}>WXYZ</TextReg>
              </View>
            </TouchableHighlight>
          </View>
          <View style={styles.pinInputRow}>
            <TouchableHighlight
              style={styles.pinInputButton}
              onPress={() => this.handleEnterPin('.')}
              underlayColor={underlayColor}
            >
              <View>
                <TextReg style={styles.pinInputNum}>.</TextReg>
                <TextReg style={styles.pinInputDetails} />
              </View>
            </TouchableHighlight>
            <TouchableHighlight
              style={styles.pinInputButton}
              onPress={() => this.handleEnterPin(0)}
              underlayColor={underlayColor}
            >
              <View>
                <TextReg style={styles.pinInputNum}>0</TextReg>
                <TextReg style={styles.pinInputDetails} />
              </View>
            </TouchableHighlight>

            <TouchableHighlight
              style={styles.pinInputButton}
              onPress={() => this.handleEnterPin('back')}
              underlayColor={underlayColor}
            >
              <View>
                <TextReg style={styles.pinInputNumDelete}>{'Delete'}</TextReg>
                <TextReg style={styles.pinInputDetails} />
              </View>
            </TouchableHighlight>
          </View>
        </View>
        {this.props.forgotPin && (
          <View style={styles.pinDone}>
            <TouchableOpacity onPress={() => this.bottomButton()}>
              <TextReg style={styles.pinForgot}>Forgot PIN?</TextReg>
            </TouchableOpacity>
          </View>
        )}
      </View>
    )
  }
}
