import { StyleSheet, Dimensions, PixelRatio } from 'react-native'

const { width, height } = Dimensions.get('window')

const SignUpPageHeight = height - 60

const styles = StyleSheet.create({
  authContainerSpaceBetween: {
    flexDirection: 'column',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'space-between',
    alignSelf: 'stretch',
  },
  authContainerFlexStart: {
    flexDirection: 'column',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-start',
    alignSelf: 'stretch',
  },
  loginFormBox: {
    justifyContent: 'flex-start',
    alignSelf: 'stretch',
    zIndex: 5,
    marginBottom: 12,
  },
  loginFormBox2FA: {
    justifyContent: 'flex-start',
    alignSelf: 'stretch',
    zIndex: 5,
    marginBottom: 14,
  },
  keyboardAvoidingView: {
    alignSelf: 'stretch',
    zIndex: 5,
  },
  input: {
    margin: 15,
    height: 46,
    marginLeft: 40,
    marginRight: 40,
    marginTop: 4,
    borderRadius: 14,
    paddingLeft: 14,
    fontSize: 18 / PixelRatio.getFontScale(),
    backgroundColor: '#c3e6e8',
    color: '#3B3F43',
    letterSpacing: 0.75,
  },
  inputLabel: {
    marginLeft: 40,
    marginBottom: 4,
  },
  inputLabelPassword: {
    marginTop: 6,
    marginLeft: 40,
    marginBottom: 4,
  },
  inputLabelText: {
    color: '#FFF',
    fontSize: 18,
  },
  totpButton: {
    borderRadius: 14,
    borderColor: '#05868e',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -4,
  },
  totpText: {
    color: '#FFF',
    fontSize: 16,
  },
  loginButton: {
    marginTop: 10,
  },
  backButton: {
    marginTop: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backText: {
    color: '#FFF',
    fontSize: 14,
  },
  signUpLink: {
    marginTop: 20,
    marginBottom: 30,
  },
  signUpLinkText: {
    color: '#FFF',
    fontSize: 18,
  },
  forgotPasswordLink: {
    marginTop: 20,
  },
  loginErrorBox: {
    marginBottom: 20,
  },
  loginErrorText: {
    color: '#FFF',
    fontSize: 17,
  },
  saltLogo: {
    height: 36,
    width: 180,
    borderRadius: 0,
    marginTop: 70,
    marginBottom: 50,
    alignSelf: 'center',
  },
  loadingDots: {
    height: 64,
    width: 80,
    marginTop: 14,
    opacity: 0.6,
  },
  versionInfo: {
    position: 'absolute',
    bottom: 3,
    right: 6,
    color: '#FFF',
    zIndex: 10,
    opacity: 0.6,
    fontSize: 14,
  },
  versionInfoPin: {
    position: 'absolute',
    bottom: 3,
    right: 6,
    color: '#FFF',
    zIndex: 10,
    opacity: 0.5,
  },
  prodButton: {
    position: 'absolute',
    bottom: 3,
    right: 6,
    height: 50,
    width: 70,
    zIndex: 10,
  },
  backgroundBox: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    flex: 1,
    borderRadius: 0,
    zIndex: -1,
  },
  backgroundImg: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    flex: 1,
    borderRadius: 0,
    zIndex: -1,
  },

  //Pin
  pinContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'absolute',
    //backgroundColor: '#FFF', //'#128a98',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 100,
  },
  pinTopBox: {
    flexDirection: 'column',
    alignItems: 'center',
    //opacity: 0,
  },
  pinBottomBox: {},
  pinInputBox: {
    flexDirection: 'column',
    justifyContent: 'flex-end',
    alignSelf: 'stretch',
  },
  pinInputRow: {
    alignSelf: 'stretch',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  pinInputRowLast: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingLeft: 90,
  },
  pinInputButton: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: 80,
    width: 90,
    borderRadius: 14,
  },
  pinInputNum: {
    fontSize: 22,
    color: '#FFF',
    alignSelf: 'center',
    borderRadius: 140,
    height: 26,
    width: 40,
    textAlign: 'center',
    marginBottom: 2,
  },
  pinInputNumDelete: {
    fontSize: 14,
    color: '#FFF',
    alignSelf: 'center',
    borderRadius: 140,
    height: 22,
    width: 80,
    textAlign: 'center',
    paddingTop: 5,
  },
  pinInputDetails: {
    fontSize: 12,
    color: '#FFF',
    alignSelf: 'center',
    width: 40,
    height: 14,
    textAlign: 'center',
    opacity: 0.7,
  },
  pinTitle: {
    fontSize: 20,
    marginTop: 30,
    color: '#FFF',
  },
  pinDone: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 24,
    marginLeft: 20,
    marginRight: 20,
    marginTop: 14,
    color: '#FFF',
  },
  pinBubbleBox: {
    flexDirection: 'row',
    marginTop: 40,
  },
  pinBubbleActive: {
    height: 20,
    width: 20,
    marginLeft: 8,
    marginRight: 8,
    backgroundColor: '#FFF',
    borderRadius: 10,
  },
  pinBubble: {
    height: 20,
    width: 20,
    marginLeft: 8,
    marginRight: 8,
    backgroundColor: '#ffffff30',
    borderRadius: 10,
  },
  saltLogoWhite: {
    height: 30,
    width: 150,
    marginTop: 50,
  },
  pinForgot: {
    color: '#FFF',
    fontSize: 14,
    marginBottom: 0,
  },
  modalText: {
    marginBottom: 15,
  },
  modalButton: {
    marginTop: 15,
    marginBottom: 25,
  },
  timoutNoticeText: {
    color: '#FFF',
    fontSize: 22,
    marginBottom: 10,
    marginTop: 40,
    textAlign: 'center',
  },
  timoutNoticeTextBottom: {
    color: '#FFF',
    fontSize: 18,
    textAlign: 'center',
  },

  // SignUp
  signUpContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  signUpPageContainer: {
    flex: 1,
    width,
    height: SignUpPageHeight,
    flexDirection: 'column',
    alignItems: 'center',
  },
  signUpPageContainerKeyboardAvoid: {
    flex: 1,
    width,
    height: SignUpPageHeight,
    flexDirection: 'column',
  },
  signUpHeader: {
    alignSelf: 'stretch',
    height: 70,
    marginTop: 0,
    marginBottom: 20,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'flex-end',
  },
  signUpHeaderText: {
    fontSize: 20,
    color: '#FFF',
    fontFamily: 'Europa-Regular',
  },
  paginationDots: {
    height: 10,
    width: 10,
    marginLeft: 5,
    marginRight: 5,
  },
  signUpTitleLead: {
    fontSize: 18,
    color: '#FFF',
    marginBottom: 6,
    fontFamily: 'Europa-Regular',
    width: 300,
    textAlign: 'center',
  },
  signUpTitlePactSafe: {
    fontSize: 18,
    color: '#FFF',
    marginBottom: 6,
    fontFamily: 'Europa-Regular',
    textAlign: 'center',
    alignSelf: 'stretch',
  },
  signUpTitleItalics: {
    fontSize: 16,
    color: '#FFF',
    fontStyle: 'italic',
    marginBottom: 14,
    fontFamily: 'Europa-Regular',
  },
  signUpTitle: {
    fontSize: 26,
    color: '#FFF',
    fontFamily: 'Europa-Bold',
  },
  signUpTitleSmall: {
    fontSize: 20,
    color: '#FFF',
    fontFamily: 'Europa-Regular',
  },
  signUpTextInput: {
    height: 50,
    borderRadius: 6,
    width: 300,
    backgroundColor: '#c3e0e2',
    marginTop: 30,
    padding: 6,
    paddingLeft: 14,
    fontFamily: 'Europa-Regular',
    fontSize: 20,
    color: '#e6e6e6',
  },
  signUpTextInputLower: {
    height: 50,
    borderRadius: 6,
    width: 300,
    backgroundColor: '#c3e0e2',
    padding: 6,
    paddingLeft: 14,
    fontFamily: 'Europa-Regular',
    fontSize: 20,
    color: '#e6e6e6',
    marginTop: 20,
  },
  backToLoginButton: {
    position: 'absolute',
    left: 18,
    top: 44,
    height: 24,
    width: 28,
  },
  backToLoginImg: {
    height: 24,
    width: 28,
    opacity: 0.85,
  },
  signUpPasswordConfirmText: {
    fontSize: 18,
    color: '#FFF',
    fontFamily: 'Europa-Regular',
    marginTop: 20,
  },
  signUpTextInputEmailVerificationCode: {
    opacity: 0,
  },
  signUpVerifcationBlocksBox: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 4,
  },
  signUpVerifcationBlocksBox2FA: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
  },
  signUpVerifcationBlock: {
    borderRadius: 6,
    backgroundColor: '#c3e0e2',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 4,
    height: 46,
    width: 46,
  },
  signUpVerifcationBlockActive: {
    borderWidth: 2,
    borderColor: '#FFF',
  },
  signUpVerifcationBlockText: {
    fontSize: 20,
  },
  signUpGoogleAuthBox: {
    marginTop: 20,
    flexDirection: 'column',
    alignItems: 'center',
    zIndex: 10,
  },
  signUpGoogleAuthBoxLower: {
    marginTop: 30,
    flexDirection: 'column',
    alignItems: 'center',
  },
  signUpCopyCodeBox: {
    backgroundColor: '#c3e0e2',
    borderRadius: 6,
    height: 46,
    width: 300,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 16,
    marginTop: 20,
    zIndex: 12,
    overflow: 'hidden',
  },
  signUpCopyCodeBoxButton: {
    backgroundColor: '#FFF',
    height: 46,
    width: 46,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  signUpCopyCodeBoxButtonActive: {
    backgroundColor: '#05868e',
    height: 46,
    width: 46,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#FFF',
  },
  signUpCopyCodeText: {
    color: '#e6e6e6',
    width: 238,
    fontSize: 14,
    overflow: 'scroll',
    height: 46,
    paddingTop: 5,
    marginLeft: -6,
    textAlign: 'center',
  },
  signUpLocationBox: {
    marginLeft: 30,
    marginRight: 30,
    marginBottom: 10,
    marginTop: -6,
    alignSelf: 'stretch',
  },
  signUpErrorBox: {
    backgroundColor: '#c3e0e2',
    borderRadius: 6,
    padding: 8,
    width: 260,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
    textAlign: 'center',
  },
  signUpJumioErrorBox: {
    backgroundColor: '#c3e0e2',
    borderRadius: 6,
    padding: 8,
    width: 300,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
    textAlign: 'center',
  },
  signUpErrorText: {
    color: '#F00',
    fontSize: 16,
    textAlign: 'center',
  },
  signUpAgreeText: {
    color: '#FFF',
    fontSize: 14,
    width: 260,
    textAlign: 'center',
  },
  signUpBeginJumioButton: {
    marginTop: 30,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    zIndex: 5,
    minWidth: 200,
    height: 60,
    fontSize: 22,
    paddingVertical: 9,
    paddingHorizontal: 21,
    backgroundColor: '#FFF',
  },
  signUpEndButton: {
    marginTop: 26,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    zIndex: 5,
    minWidth: 160,
    height: 50,
    fontSize: 22,
    paddingVertical: 9,
    paddingHorizontal: 21,
    backgroundColor: '#FFF',
  },
  signUpErrorRetryButton: {
    marginTop: 20,
    borderRadius: 14,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    zIndex: 5,
    minWidth: 120,
    height: 40,
    fontSize: 22,
    paddingVertical: 8,
    paddingHorizontal: 21,
    backgroundColor: '#FFF',
  },
  signUpNextButton: {
    marginBottom: 30,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    zIndex: 5,
    minWidth: 220,
    height: 50,
    fontSize: 22,
    paddingVertical: 8,
    paddingHorizontal: 21,
    backgroundColor: '#FFF',
  },
  signUpNextButtonInactive: {
    opacity: 0.5,
    marginBottom: 30,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    zIndex: 5,
    minWidth: 220,
    height: 50,
    fontSize: 22,
    paddingVertical: 8,
    paddingHorizontal: 21,
    backgroundColor: '#FFF',
  },
  signUpSpaceBetween: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
  },
  jumioErrorText: {
    width: 280,
    marginTop: 16,
    color: '#FFF',
    fontSize: 16,
    textAlign: 'center',
  },
})

export default styles
