import React from 'react'
import {Modal, View, Text, Image, TouchableOpacity} from 'react-native'

import styles from './styles'

const LoadingModal = props => (
  <Modal
    animationType="fade"
    transparent
    visible={props.modalVisable && !props.showPinScreen}
    onRequestClose={() => props.closeContactSupport()}>
    <View style={styles.helpModalBox}>
      <View style={styles.helpModalSquare}>
        <TouchableOpacity
          style={styles.helpModalX}
          onPress={() => {
            props.closeContactSupport()
          }}>
          <Image
            source={require('../../imgs/closeX.png')}
            style={styles.closeModalImg}
          />
        </TouchableOpacity>
        <View style={styles.helpModalTitleBox}>
          <Text style={styles.helpModalTitle}>{props.title}</Text>
        </View>
        <View style={styles.helpModalDescriptionBox}>
          <Text style={styles.helpModalDescription}>Submitting Ticket</Text>
        </View>
        <Image
          source={require('../../imgs/loadingDots.gif')}
          style={styles.loadingDots}
        />
      </View>
    </View>
  </Modal>
)

export default LoadingModal
