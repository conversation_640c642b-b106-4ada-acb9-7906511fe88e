import React from 'react'
import {Modal, View, Image, TouchableOpacity, ScrollView} from 'react-native'
import {TextBold} from '../../components'

import styles from './styles'
import commonStyles from '../../styles/commonStyles'

const AddTokenModal = props => (
  <Modal
    animationType="slide"
    transparent
    visible={props.showAddToken && !props.showPinScreen}
    onRequestClose={() => props.toggleAddToken()}>
    <View
      style={{
        flex: 1,
        alignItems: 'center',
        backgroundColor: '#28283D',
      }}>
      <View style={styles.addTokenModalBox}>
        <TouchableOpacity
          style={{width: 30}}
          onPress={() => {
            props.toggleAddToken()
          }}>
          <Image
            source={require('../../imgs/backToSettings.png')}
            style={commonStyles.closeModalImg}
          />
        </TouchableOpacity>
        <TextBold style={{fontSize: 22, color: '#FFF', marginTop: 10, marginLeft: 10}}></TextBold>
      </View>
      <ScrollView style={{alignSelf: 'stretch', marginTop: 40, marginBottom: 40}}>
        {props.showCollaterals}
      </ScrollView>
    </View>
  </Modal>
)

export default AddTokenModal
