import React from 'react'
import {Modal, View, Image, TouchableOpacity, TextInput, Keyboard, Linking} from 'react-native'

import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import QRCode from 'react-native-qrcode-svg'
import {But<PERSON>, TextReg, TextBold, Background} from '../../components'
import copyButtonImg from '../../imgs/copyButtonGreen.png'
import checkMarkCopiedImg from '../../imgs/checkMarkCopied.png'

import styles from './styles'

const DisabledModal = props => {
  let disabledStabilize = props.launchDarkly['disable-stabilization'] || false

  return (
    <Modal
      animationType="slide"
      transparent
      visible={props.showSetup && !props.showPinScreen}
      onRequestClose={() => props.closeSetup()}>
      <Background backgroundColor={'#28283D'} />
      <KeyboardAwareScrollView
        style={styles.scrollViewContainer}
        contentContainerStyle={styles.modalScroll}>
        <TouchableOpacity style={styles.successModalXDisabled} onPress={() => props.closeSetup()}>
          <Image source={require('../../imgs/closeX.png')} style={styles.closeModalImg} />
        </TouchableOpacity>
        <View
          style={{
            alignSelf: 'stretch',
            marginLeft: 30,
            marginRight: 30,
            marginTop: 70,
          }}>
          {/* <TextReg
            style={{
              fontSize: 17,
            }}>{`Recent industry events have impacted our business and some of our key partners. Until we are able to determine the extent of this impact, we have paused ${
            props.promptType == 'deposit' ? 'deposits' : 'withdrawals'
          } on the SALT platform effective immediately.`}</TextReg>
          {props.promptType == 'deposit' && (
            <TextReg
              style={{
                fontSize: 17,
                marginTop: 20,
              }}>{`While our platform will recognize on-chain deposits, we strongly advise against depositing more funds to your account.`}</TextReg>
          )}
          <TextReg
            style={{
              fontSize: 17,
              marginTop: 20,
            }}>{`During this time, ${
            disabledStabilize
              ? 'SALT has decided not to stabilize your loan based on its LTV. Your'
              : 'your'
          } loan will remain active and all our loan monitoring systems will be fully operational. We are working diligently with our partners to secure a clear path forward.`}</TextReg>
          <TextReg
            style={{
              fontSize: 17,
              marginTop: 20,
            }}>{`We understand that this is difficult news during these turbulent times. We will provide updates as frequently as possible.`}</TextReg> */}
          <TextReg>
            {props.promptType == 'deposit' ? 'Deposits' : 'Withdrawals'} are currently disabled on
            this account. Please contact{' '}
            <TextReg
              onPress={() => {
                Linking.openURL('mailto:<EMAIL>')
              }}
              style={{
                color: '#00FFBD',
                textAlign: 'center',
              }}>
              <EMAIL>
            </TextReg>{' '}
            for more information.
          </TextReg>

          <Button
            style={{
              marginTop: 30,
              backgroundColor: '#28283D',
              borderWidth: 1,
              borderColor: '#fff',
            }}
            onPress={() => props.closeSetup()}>
            <TextReg style={{color: '#ccc'}}>Close</TextReg>
          </Button>
        </View>
      </KeyboardAwareScrollView>
    </Modal>
  )
}

export default DisabledModal
