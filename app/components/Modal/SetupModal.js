import React from 'react';
import {
  Modal,
  View,
  Image,
  TouchableOpacity,
  TextInput,
  Keyboard,
} from 'react-native';

import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view';
import QRCode from 'react-native-qrcode-svg';
import {Button, TextReg, TextBold, Background} from '../../components';
import copyButtonImg from '../../imgs/copyButtonGreen.png';
import checkMarkCopiedImg from '../../imgs/checkMarkCopied.png';

import styles from './styles';

const SetupModal = props => (
  <Modal
    animationType="slide"
    transparent
    visible={props.showSetup && !props.showPinScreen}
    onRequestClose={() => props.closeSetup()}>
    <Background backgroundColor={'#28283D'} />
    <KeyboardAwareScrollView
      style={styles.scrollViewContainer}
      contentContainerStyle={styles.modalScroll}>
      <TouchableOpacity
        style={styles.successModalX}
        onPress={() => props.closeSetup()}>
        <Image
          source={require('../../imgs/closeX.png')}
          style={styles.closeModalImg}
        />
      </TouchableOpacity>
      {props.step === 0 && (
        <View style={{marginTop: 60, width: 300, alignItems: 'center'}}>
          <TextBold style={{marginBottom: 24, fontSize: 20}}>
            Enable 2-Step Verification
          </TextBold>
          <TextReg style={{fontSize: 16, marginBottom: 30}}>
            Download an Authenticator app on your mobile device and create an
            account. We suggest either{' '}
            <TextBold style={{color: '#00FFBD'}}>Google Authenticator</TextBold>{' '}
            or <TextBold style={{color: '#00FFBD'}}>Authy</TextBold>.
          </TextReg>
          <Image
            source={require('../../imgs/authenticator.png')}
            style={{width: 200, height: 127, marginBottom: 30}}
          />
          <Button
            isLoading={props.setupLoading}
            style={{backgroundColor: '#00FFBD', marginTop: 12}}
            onPress={() => props.setupNext(0)}>
            <TextBold style={styles.modalNextText}>I HAVE THE APP</TextBold>
          </Button>
        </View>
      )}
      {props.step === 1 && (
        <View style={{marginTop: 60, width: 300, alignItems: 'center'}}>
          <TextBold style={{marginBottom: 24, fontSize: 20}}>
            Enable 2-Step Verification
          </TextBold>
          <TextReg style={{fontSize: 16, marginBottom: 30}}>
            We have emailed you a one-time setup code, please enter it in below
            (case sensitive)
          </TextReg>

          <View style={{alignSelf: 'stretch'}}>
            <TextReg style={styles.changePasswordInputTitle}>
              One-Time Code
            </TextReg>
            <TextInput
              style={styles.OneTimeCodeInput}
              onChangeText={text => props.handleUpdateSetupTwoFactor(text)}
              value={props.setupTwoFactor}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'done'}
              autoCapitalize={'none'}
              onSubmitEditing={() => {
                Keyboard.dismiss();
              }}
              placeholder={''}
              keyboardAppearance="dark"
            />
            <View style={styles.setupResendBox}>
              <TouchableOpacity onPress={() => props.setupNext(0)}>
                <View style={styles.setupResend}>
                  <TextReg style={{color: '#fff'}}>Resend Email</TextReg>
                </View>
              </TouchableOpacity>
            </View>
            <TextReg style={styles.changePasswordInputTitle}>
              Verify Login Password
            </TextReg>
            <TextInput
              style={styles.twoFactorInput}
              onChangeText={text => props.handleUpdatePassword(text)}
              value={props.loginPassword}
              underlineColorAndroid="transparent"
              blurOnSubmit
              secureTextEntry
              textContentType="password"
              returnKeyType={'done'}
              onSubmitEditing={() => {
                Keyboard.dismiss();
                props.setupNext(1);
              }}
              placeholder={''}
              keyboardAppearance="dark"
            />
          </View>
          <Button
            disabled={!props.loginPassword || !props.setupTwoFactor}
            isLoading={props.setupLoading}
            style={styles.modalLoadingButton}
            onPress={() => props.setupNext(1)}>
            <TextBold style={styles.modalNextText}>NEXT</TextBold>
          </Button>
          {props.setupError && (
            <View style={styles.modalErrorBox}>
              <TextReg style={styles.setupErrorText}>
                Incorrect One-Time Code
              </TextReg>
            </View>
          )}
          {props.setupErrorPassword && (
            <View style={styles.modalErrorBox}>
              <TextReg style={styles.setupErrorText}>
                Incorrect Login Password
              </TextReg>
            </View>
          )}
        </View>
      )}

      {props.step === 2 && (
        <View style={{marginTop: 60, width: 300, alignItems: 'center'}}>
          <TextBold style={{marginBottom: 24, fontSize: 20}}>
            Enable 2-Step Verification
          </TextBold>
          <TextReg style={{fontSize: 16, marginBottom: 10}}>
            Please generate a new two-factor entry with this secret:
          </TextReg>

          <View style={styles.depositQrBox}>
            <QRCode
              value={`otpauth://totp/SaltLending?secret=${props.secret}`}
              size={130}
              bgColor={'#000'}
              fgColor={'#FFF'}
            />
          </View>
          <View style={styles.signUpCopyCodeBox}>
            <TextReg style={styles.signUpCopyCodeText}>{props.secret}</TextReg>
            <TouchableOpacity onPress={props.copy2FACode}>
              {props.showCopied ? (
                <View style={styles.signUpCopyCodeBoxButtonActive}>
                  <Image
                    source={checkMarkCopiedImg}
                    style={{height: 16, width: 20}}
                  />
                </View>
              ) : (
                <View style={styles.signUpCopyCodeBoxButton}>
                  <Image
                    source={copyButtonImg}
                    style={{height: 20, width: 20}}
                  />
                </View>
              )}
            </TouchableOpacity>
          </View>
          <View style={{alignSelf: 'stretch'}}>
            <TextReg style={styles.changePasswordInputTitle}>
              Two-Factor
            </TextReg>
            <TextInput
              style={styles.twoFactorInput}
              onChangeText={text => props.handleUpdateNewTwoFactor(text)}
              value={props.newTwoFactor}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'done'}
              onSubmitEditing={() => {
                Keyboard.dismiss();
                props.setupNext(2);
              }}
              placeholder={''}
              keyboardType={'numeric'}
              keyboardAppearance="dark"
            />
          </View>

          <Button
            disabled={props.rateLimitError}
            isLoading={props.setupLoading}
            style={styles.modalLoadingButton}
            onPress={() => props.setupNext(2)}>
            <TextBold style={styles.modalNextText}>SUBMIT</TextBold>
          </Button>

          {props.setupError && (
            <View style={styles.modalErrorBox}>
              <TextReg style={styles.setupErrorText}>Invalid 2FA Code</TextReg>
            </View>
          )}
          {props.rateLimitError && (
            <View style={styles.modalErrorBox}>
              <TextReg style={styles.setupErrorText}>
                {`Timeout due to 3 invalid attempts, please try again in ${props.timer} seconds.`}
              </TextReg>
            </View>
          )}
        </View>
      )}
    </KeyboardAwareScrollView>
  </Modal>
);

export default SetupModal;
