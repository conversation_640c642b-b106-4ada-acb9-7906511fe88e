import React, {useState, useEffect} from 'react'
import {Modal, View, Image, TouchableOpacity, ScrollView, Dimensions} from 'react-native'
import {TextReg, TextBold} from '../../components'
import AsyncStorage from '@react-native-async-storage/async-storage'

import styles from './styles'
import commonStyles from '../../styles/commonStyles'
import packageJson from '../../../package.json'

const {width: ScreenWidth, height: ScreenHeight} = Dimensions.get('window')

const VersionCheck = ({openStore, newVer, req, contVer}) => {
  return (
    <View
      style={{
        flex: 1,
        width: ScreenWidth,
        height: ScreenHeight,
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignSelf: 'stretch',
        alignItems: 'center',
        backgroundColor: '#28283D',
      }}>
      <View style={{flex: 1, flexDirection: 'column', justifyContent: 'center', alignSelf: 'stretch', alignItems: 'center'}}>
        <Image
          source={require('../../imgs/logos/saltDelta-white.png')}
          style={{height: 45, width: 42, marginBottom: 20, marginTop: -100}}
        />
        <TextReg style={{fontSize: 20}}>{`Update Available`}</TextReg>
        <TextReg style={{fontSize: 16}}>{`v${newVer} is out`}</TextReg>
        <TextReg
          style={{
            width: 310,
            textAlign: 'center',
            marginTop: 20,
          }}>{`An updated version of the SALT app is available.`}</TextReg>
        <TextReg
          style={{
            width: 310,
            textAlign: 'center',
            marginTop: 10,
          }}>{`Discover new features and improved performance by updating your SALT app today.`}</TextReg>
      </View>

      <View style={{alignSelf: 'stretch', marginLeft: 30, marginRight: 30}}>
        <TouchableOpacity
          onPress={() => openStore()}
          style={{
            alignSelf: 'stretch',
            height: 50,
            borderRadius: 14,
            borderColor: '#fff',
            justifyContent: 'center',
            alignItems: 'center',
            borderWidth: 1,
            backgroundColor: '#00FFBD',
            marginBottom: req ? 50 : 10,
          }}>
          <TextReg style={{color: '#000', fontSize: 18, letterSpacing: 1}}>{`UPDATE`}</TextReg>
        </TouchableOpacity>
        {!req && (
          <TouchableOpacity
            onPress={() => contVer(newVer)}
            style={{
              alignSelf: 'stretch',
              height: 50,
              borderRadius: 14,
              borderColor: '#fff',
              justifyContent: 'center',
              alignItems: 'center',
              borderWidth: 1,
              marginBottom: 50,
            }}>
            <TextReg style={{fontSize: 18, letterSpacing: 1}}>{`CONTINUE`}</TextReg>
          </TouchableOpacity>
        )}
      </View>
    </View>
  )
}

export default VersionCheck
