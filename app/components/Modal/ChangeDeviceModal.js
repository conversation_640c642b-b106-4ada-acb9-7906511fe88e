import React from 'react'
import {
  Modal,
  View,
  TextInput,
  Image,
  TouchableOpacity,
  Keyboard,
} from 'react-native'

import QRCode from 'react-native-qrcode-svg'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {Button, TextReg, TextBold, Background} from '../../components'
import copyButtonImg from '../../imgs/copyButtonGreen.png'
import checkMarkCopiedImg from '../../imgs/checkMarkCopied.png'

import styles from './styles'

const ChangeDeviceModal = props => (
  <Modal
    animationType="slide"
    transparent
    visible={props.showChangeDevice && !props.showPinScreen}
    onRequestClose={() => props.closeChangeDevice()}>
    <Background backgroundColor={'#28283D'} />
    <KeyboardAwareScrollView
      style={styles.scrollViewContainer}
      contentContainerStyle={styles.modalScroll}>
      <TouchableOpacity
        style={styles.successModalX}
        onPress={() => props.closeChangeDevice()}>
        <Image
          source={require('../../imgs/closeX.png')}
          style={styles.closeModalImg}
        />
      </TouchableOpacity>
      {props.changeStep === 0 && (
        <View style={{marginTop: 60, width: 300}}>
          <TextBold style={{marginBottom: 24, fontSize: 20}}>
            Change Device
          </TextBold>
          <TextReg style={{fontSize: 16, marginBottom: 30}}>
            Please enter your current 2FA code:
          </TextReg>

          <TextReg style={styles.changePasswordInputTitle}>Two-Factor</TextReg>
          <TextInput
            style={styles.twoFactorInput}
            onChangeText={text => props.handleUpdateTwoFactor(text)}
            value={props.currentTwoFactor}
            underlineColorAndroid="transparent"
            blurOnSubmit
            returnKeyType={'done'}
            onSubmitEditing={() => {
              props.handleChangeStep0()
            }}
            placeholder={''}
            keyboardType={'numeric'}
            keyboardAppearance="dark"
          />

          <Button
            isLoading={props.changeLoading}
            style={styles.modalLoadingButton}
            onPress={() => props.handleChangeStep0()}>
            <TextBold style={styles.modalNextText}>NEXT</TextBold>
          </Button>
          {props.changeError && (
            <View style={styles.modalErrorBox}>
              <TextReg style={styles.changeDeviceErrorText}>
                {props.changeError}
              </TextReg>
            </View>
          )}
        </View>
      )}
      {props.changeStep === 1 && (
        <View style={{marginTop: 60, width: 300, alignItems: 'center'}}>
          <TextBold style={{marginBottom: 24, fontSize: 20}}>
            Change Device
          </TextBold>
          <TextReg style={{fontSize: 16, marginBottom: 10}}>
            Please generate a new two-factor entry with this secret:
          </TextReg>

          <View style={styles.depositQrBox}>
            <QRCode
              value={`otpauth://totp/SaltLending?secret=${props.secret}`}
              size={130}
              bgColor={'#000'}
              fgColor={'#FFF'}
            />
          </View>
          <View style={styles.signUpCopyCodeBox}>
            <TextReg style={styles.signUpCopyCodeText}>{props.secret}</TextReg>
            <TouchableOpacity onPress={props.copy2FACode}>
              {props.showCopied ? (
                <View style={styles.signUpCopyCodeBoxButtonActive}>
                  <Image
                    source={checkMarkCopiedImg}
                    style={{height: 16, width: 20}}
                  />
                </View>
              ) : (
                <View style={styles.signUpCopyCodeBoxButton}>
                  <Image
                    source={copyButtonImg}
                    style={{height: 20, width: 20}}
                  />
                </View>
              )}
            </TouchableOpacity>
          </View>
          <View style={{alignSelf: 'stretch'}}>
            <TextReg style={styles.changePasswordInputTitle}>
              New Two-Factor
            </TextReg>
            <TextInput
              style={styles.twoFactorInput}
              onChangeText={text => props.handleUpdateNewTwoFactor(text)}
              value={props.newTwoFactor}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'done'}
              onSubmitEditing={() => Keyboard.dismiss()}
              placeholder={''}
              keyboardType={'numeric'}
              keyboardAppearance="dark"
            />
          </View>

          <Button
            isLoading={props.changeLoading}
            style={styles.changeDeviceSubmitButton}
            onPress={() => props.changeDeviceNext(1)}>
            <TextBold style={styles.modalNextText}>SUBMIT</TextBold>
          </Button>
          {props.changeError && (
            <View style={styles.modalErrorBox}>
              <TextReg style={styles.changeDeviceErrorText}>
                {props.changeError}
              </TextReg>
            </View>
          )}
        </View>
      )}
    </KeyboardAwareScrollView>
  </Modal>
)

export default ChangeDeviceModal
