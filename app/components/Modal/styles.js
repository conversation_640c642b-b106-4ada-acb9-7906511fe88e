import {StyleSheet, PixelRatio} from 'react-native'

const styles = StyleSheet.create({
  modal: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(74, 74, 74, 0.5)',
  },
  square: {
    width: '86%',
    backgroundColor: '#FFF',
    borderRadius: 8,
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: 30,
  },
  modalX: {
    position: 'absolute',
    top: 4,
    right: 4,
  },
  titleBox: {
    marginTop: 24,
    marginBottom: 14,
  },
  title: {
    fontSize: 24,
    color: '#e6e6e6',
  },
  button: {
    marginVertical: 30,
  },
  descriptionBox: {
    marginLeft: 30,
    marginRight: 30,
  },
  description: {
    color: '#e6e6e6',
    textAlign: 'center',
    fontSize: 16,
  },
  closeImg: {
    height: 30,
    width: 30,
    margin: 4,
  },
  scrollViewContainer: {
    alignSelf: 'stretch',
  },
  scrollViewContainerRedeemSalt: {
    alignSelf: 'stretch',
    paddingTop: 40,
    paddingRight: 20,
    paddingLeft: 20,
  },
  setupModalBox: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },

  successModalBox: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(74, 74, 74, 0.5)',
  },
  successModalSquare: {
    width: '86%',
    backgroundColor: '#3D3D50',
    borderRadius: 8,
    flexDirection: 'column',
    alignItems: 'center',
    paddingBottom: 30,
    paddingTop: 30,
    borderWidth: 1,
    borderColor: '#777',
  },
  successModalXDisabled: {
    position: 'absolute',
    top: 52,
    right: 10,
    height: 30,
    width: 30,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 50,
  },
  successModalX: {
    position: 'absolute',
    top: 42,
    right: 10,
    height: 30,
    width: 30,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 50,
  },
  successModalXRedeemSalt: {
    position: 'absolute',
    top: 36,
    right: 10,
    height: 30,
    width: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  successModalTitleBox: {
    marginTop: 14,
    marginBottom: 10,
    flexDirection: 'column',
    alignItems: 'center',
  },
  successModalTitle: {
    fontSize: 18,
    color: '#e6e6e6',
    margin: 10,
  },
  successModalButton: {
    backgroundColor: '#05868e',
    borderRadius: 6,
    width: 160,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 30,
    marginBottom: 30,
  },
  successModalButtonText: {
    fontSize: 22,
    color: '#FFF',
  },
  successModalDescriptionBox: {
    marginLeft: 30,
    marginRight: 30,
  },
  errorModalDescriptionBox: {
    marginLeft: 30,
    marginRight: 30,
    marginBottom: 50,
  },
  successModalDescription: {
    color: '#e6e6e6',
    textAlign: 'center',
    fontSize: 16,
  },
  closeModalImg: {
    height: 30,
    width: 30,
    margin: 4,
  },
  loadingDots: {
    height: 80,
    width: 80,
    marginTop: -10,
    opacity: 0.6,
  },
  checkmarkCircleImg: {
    height: 50,
    width: 50,
    marginBottom: 20,
  },
  notificationSettingsHeader: {
    marginTop: 24,
    marginBottom: 20,
    marginLeft: 20,
    marginRight: 20,
    color: '#e6e6e6',
    textAlign: 'center',
  },
  twoFactorInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 20,
  },
  OneTimeCodeInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 4,
  },
  depositQrBox: {
    overflow: 'hidden',
    backgroundColor: '#FFF',
    padding: 10,
    borderRadius: 14,
  },

  signUpCopyCodeBox: {
    backgroundColor: '#fff',
    borderRadius: 6,
    height: 46,
    width: 300,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 16,
    marginTop: 10,
    zIndex: 12,
    overflow: 'hidden',
    borderColor: '#e6e6e6',
    borderWidth: 1,
    marginBottom: 20,
  },
  signUpCopyCodeBoxButton: {
    backgroundColor: '#FFF',
    height: 46,
    width: 46,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: '#00ffc3',
  },
  signUpCopyCodeBoxButtonActive: {
    backgroundColor: '#05868e',
    height: 46,
    width: 46,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#FFF',
  },
  signUpCopyCodeText: {
    color: '#000',
    width: 238,
    fontSize: 14,
    overflow: 'scroll',
    height: 46,
    paddingTop: 5,
    marginLeft: -6,
    textAlign: 'center',
  },
  modalNextText: {
    color: '#000',
    fontSize: 17,
    marginBottom: -2,
    marginTop: -2,
  },
  modalErrorBox: {
    alignSelf: 'stretch',
    justifyContent: 'center',
    marginBottom: 20,
  },
  changeDeviceErrorText: {
    color: '#de4a2e',
    fontSize: 17,
    textAlign: 'center',
  },
  modalLoadingButton: {
    backgroundColor: '#00FFBD',
    marginTop: 12,
    marginBottom: 20,
  },
  modalScroll: {
    alignSelf: 'stretch',
    alignItems: 'center',
    paddingTop: 20,
    justifyContent: 'flex-start',
  },
  modalScrollRedeemSalt: {
    alignSelf: 'stretch',
    alignItems: 'center',
    paddingTop: 20,
    justifyContent: 'flex-start',
    flex: 1,
    paddingBottom: 40,
  },
  changeDeviceSubmitButton: {
    backgroundColor: '#00FFBD',
    marginTop: 12,
    marginBottom: 18,
  },
  setupResendBox: {
    alignSelf: 'stretch',
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  setupResend: {
    borderRadius: 14,
    backgroundColor: '#7B5BED',
    padding: 4,
    paddingLeft: 6,
    paddingRight: 6,
  },
  setupErrorText: {
    color: '#de4a2e',
    fontSize: 17,
    textAlign: 'center',
  },
  addTokenModalBox: {
    height: 90,
    alignSelf: 'stretch',
    justifyContent: 'space-between',
    paddingRight: 30,
    paddingLeft: 30,
    paddingTop: 50,
  },
})

export default styles
