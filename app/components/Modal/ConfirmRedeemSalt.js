import React from 'react'
import {Modal, View, Image, TouchableOpacity} from 'react-native'

import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'

import {Button, TextReg, TextBold, Background} from '../../components'
import commonStyles from '../../styles/commonStyles'

import styles from './styles'

const ConfirmRedeemSalt = props => (
  <Modal
    animationType="slide"
    transparent
    visible={props.showConfirm && !props.showPinScreen}
    onRequestClose={() => props.toggleConfirm()}>
    <Background backgroundColor={'#28283d'} />
    <KeyboardAwareScrollView
      style={styles.scrollViewContainerRedeemSalt}
      contentContainerStyle={styles.modalScrollRedeemSalt}>
      <TouchableOpacity
        style={styles.successModalXRedeemSalt}
        onPress={() => props.toggleConfirm()}>
        <Image
          source={require('../../imgs/closeX.png')}
          style={styles.closeModalImg}
        />
      </TouchableOpacity>
      <View style={commonStyles.tileContainerDark}>
        <TextReg style={{color: '#FFF', marginTop: 20, fontSize: 18}}>
          Confirm SALT redemption
        </TextReg>
        <TextReg style={{color: '#FFF', fontSize: 18, marginTop: 60}}>
          Redeem
        </TextReg>
        <TextReg style={{color: '#FFF', fontSize: 56, marginTop: 4}}>
          {props.totalSaltNeeded} SALT
        </TextReg>
        <TextReg style={{color: '#FFF', fontSize: 18, marginTop: 18}}>
          For A
        </TextReg>
        <TextReg style={{color: '#FFF', fontSize: 56, marginTop: 4}}>
          {props.newApr}% APR
        </TextReg>
      </View>
      <Button
        style={{marginBottom: 8, width: 320}}
        isLoading={props.loading}
        onPress={() => props.submit()}>
        CONFIRM
      </Button>
    </KeyboardAwareScrollView>
  </Modal>
)

export default ConfirmRedeemSalt
