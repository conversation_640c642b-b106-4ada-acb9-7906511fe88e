import React, {useEffect} from 'react'
import {View, Text, TouchableOpacity, Image, Linking, Platform} from 'react-native'

import {TextBold, TextReg, Button} from '../../components'
import {useDispatch, useSelector} from 'react-redux'

import styles from './styles'
import notificationsBell from '../../imgs/notifBell-white2.png'
import checkmarkCircle from '../../imgs/checkmark.png'
import packageJson from '../../../package.json'

const UpdateIntro = props => {
  let WebService = useSelector(state => state.auth.WebService || {})

  useEffect(() => {
    const version = packageJson.version
    console.log('get versions, check mine', version)
    getV()
  }, [])

  let getV = async () => {
    try {
      let res = await WebService.versionInfo()
      console.log('res', res)
    } catch (err) {
      console.log('err', err)
    }
  }

  let later = () => {
    console.log('later')
  }
  let get = () => {
    console.log('get')

    let link = 'https://play.google.com/store/apps/details?id=com.saltlending.mobile'
    if (Platform.OS === 'ios') {
      link = 'https://itunes.apple.com/app/id1383851676'
    }

    Linking.openURL(link).catch(err => console.error('An error occurred', err))

    /*
    const link = 'itms-apps://apps.apple.com/id/app/id1383851676?l=id'
    Linking.canOpenURL(link).then(
      supported => {
        supported && Linking.openURL(link)
      },
      err => console.log(err),
    )
    */
  }
  return (
    <View style={local.box}>
      <View style={{alignSelf: 'stretch', alignItems: 'center'}} />
      <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
        <Image
          source={notificationsBell}
          style={{
            height: 80,
            width: 74,
            marginBottom: 24,
          }}
        />
        <TextBold style={local.title}>Update Available</TextBold>
        <TextReg style={{...local.title, marginTop: 10}}>A new version of the app is available</TextReg>
      </View>
      <View style={{alignSelf: 'stretch'}}>
        <Button
          isLoading={false}
          style={{
            alignSelf: 'stretch',
            marginTop: 10,
            backgroundColor: '#00FFBD',
            marginBottom: 10,
          }}
          onPress={() => get()}
          theme={'secondary'}>
          <TextReg style={{color: '#000', fontSize: 18}}>GET NOW</TextReg>
        </Button>
        <Button
          isLoading={false}
          style={{
            alignSelf: 'stretch',
            marginTop: 4,
            borderColor: '#00FFBD',
            borderWidth: 1,
            marginBottom: 30,
            backgroundColor: '#28283D',
          }}
          onPress={() => goBack()}>
          <TextReg style={{color: '#00FFBD', fontSize: 18}}>LATER</TextReg>
        </Button>
      </View>
    </View>
  )
}

export default UpdateIntro

let local = {
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#28283D', //#F5FCFF
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 50,
    paddingLeft: 30,
    paddingRight: 30,
  },
  title: {
    fontSize: 20,
  },
}
