import React from 'react'
import {
  Modal,
  View,
  Image,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  Dimensions,
  PixelRatio,
} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'

import TextReg from '../Text/TextReg'
import Discounts from '../../views/Home/LoanReq/Discounts'
import TextBold from '../Text/TextBold'
import Button from '../Button/Button'

const {width: screenWidth} = Dimensions.get('window')

const PromoCodeModal = props => {
  const {
    codeError,
    promoData,
    submitCode,
    updateCode,
    removeCode,
    codeLoading,
    modalVisable,
    closeContactSupport,
    showPinScreen,
    openPromoTerms,
  } = props

  const {affliateValue, data} = promoData
  const disabled = codeError || !affliateValue
  return (
    <Modal
      animationType="fade"
      transparent
      style={{backgroundColor: 'red'}}
      visible={modalVisable && !showPinScreen}
      avoidKeyboard={true}
      onRequestClose={() => closeContactSupport()}>
      <SafeAreaView style={{flex: 1, backgroundColor: '#28283D', alignItems: 'center'}}>
        <View
          style={{
            flexDirection: 'row',
            alignSelf: 'stretch',
            justifyContent: 'space-between',
            marginTop: 5,
            marginBottom: 20,
          }}>
          <View style={{height: 40, width: 40}} />
          <TextReg style={{color: '#FFF', fontSize: 18}}>Apply Promotional Code</TextReg>
          <TouchableOpacity onPress={() => closeContactSupport()}>
            <Image
              source={require('../../imgs/closeX.png')}
              style={{height: 22, width: 22, marginRight: 12}}
            />
          </TouchableOpacity>
        </View>
        <KeyboardAwareScrollView
          style={{
            flex: 1,
            backgroundColor: '#28283D',
            alignSelf: 'stretch',
          }}
          contentContainerStyle={{
            alignItems: 'center',
            flexGrow: 1,
          }}
          enableResetScrollToCoords={true}>
          <View style={{width: screenWidth - 40, flex: 1}}>
            <View style={local.titleContainer}>
              <Image source={require('../../imgs/promoCodeBig.png')} style={local.handImage} />
            </View>

            <TextReg style={{fontSize: 18, marginVertical: 48}}>
              If you have a promotional code, you can apply it for this purchase.
            </TextReg>

            <View style={{flexDirection: 'row', justifyContent: 'space-between', marginBottom: 5}}>
              <TextReg style={{fontSize: 16}}>Promotional Code</TextReg>
              <TouchableOpacity onPress={() => openPromoTerms()}>
                <TextReg style={{color: '#00FFBD', fontSize: 16}}>{`TERMS & CONDITIONS`}</TextReg>
              </TouchableOpacity>
            </View>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                width: screenWidth - 40,
                gap: 10,
                opacity: data.rateDiscount != '' ? 0.7 : 1,
              }}>
              <TextInput
                style={[
                  codeError ? {borderColor: '#E5705A'} : {borderColor: '#cbcbcb'},
                  {
                    width:
                      data.rateDiscount != '' || data.originationFeeDiscount != '' || codeError
                        ? '90%'
                        : '100%',
                    height: 56,
                    borderWidth: 1,
                    borderRadius: 4,
                    paddingLeft: 10,
                    alignItems: 'stretch',
                    alignSelf: 'stretch',
                    color: '#FFF',
                    fontSize: 16 / PixelRatio.getFontScale(),
                    lineHeight: 22,
                    opacity: data.rateDiscount != '' ? 0.6 : 1,
                  },
                ]}
                onChangeText={text => updateCode(text)}
                value={`${affliateValue}`}
                underlineColorAndroid="transparent"
                blurOnSubmit
                returnKeyType={'next'}
                placeholder={'Enter Code'}
                placeholderTextColor={'#AFAFAF'}
                // onBlur={submitCode}
                onSubmitEditing={submitCode}
                keyboardAppearance="dark"
              />
              {(data.rateDiscount != '' || data.originationFeeDiscount != '' || codeError) && (
                <TouchableOpacity onPress={removeCode}>
                  <Image
                    source={require('../../imgs/closeX.png')}
                    style={{height: 22, width: 22}}
                  />
                </TouchableOpacity>
              )}
            </View>
            {codeError && (
              <TextReg style={{fontSize: 16, color: '#E5705A', marginTop: 2}}>
                Invalid promotional code
              </TextReg>
            )}
            <Discounts rateDiscount={data.rateDiscount} feeDiscount={data.originationFeeDiscount} />
          </View>

          <View style={{...local.buttonContainer}}>
            <Button
              isLoading={codeLoading}
              disabled={disabled}
              onPress={() => submitCode()}
              style={local.continueButton}>
              <TextBold style={{color: disabled ? '#FFF' : '#000', letterSpacing: 2.16}}>
                APPLY
              </TextBold>
            </Button>
            <Button
              isLoading={false}
              onPress={() => closeContactSupport()}
              style={local.backButton}>
              <TextBold style={local.backButtonText}>CANCEL</TextBold>
            </Button>
          </View>
        </KeyboardAwareScrollView>
      </SafeAreaView>
    </Modal>
  )
}

export default PromoCodeModal

let local = {
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
  titleContainer: {
    width: 140,
    height: 140,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#535364',
    borderRadius: 70,
    alignSelf: 'center',
  },
  actionTitleTxt: {
    marginTop: 30,
    fontSize: 18,
    marginBottom: 25,
  },
  termOption: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#535364',
    borderRadius: 8,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  term: {flexDirection: 'row', alignItems: 'center', gap: 8},
  descriptionBox: {
    width: screenWidth - 40,
  },
  buttonContainer: {
    alignItems: 'center',
    alignSelf: 'stretch',
    paddingLeft: 30,
    paddingRight: 30,
  },
  continueButton: {
    width: screenWidth - 40,
    backgroundColor: '#00FFBD',
    height: 60,
    borderRadius: 15,
    marginTop: 20,
    marginBottom: 12,
  },
  backButton: {
    width: screenWidth - 40,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  backButtonText: {color: '#00FFBD', fontSize: 18, letterSpacing: 2.16},
  handImage: {height: 81, width: 81},
  card: {
    backgroundColor: '#535364',
    borderWidth: 2,
    flexDirection: 'row',
    alignSelf: 'stretch',
    alignItems: 'center',
    gap: 22,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  drop: {
    height: 54,
    borderWidth: 1,
    borderRadius: 12,
    borderColor: '#474756',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    backgroundColor: '#474756',
    fontSize: 16,
    marginBottom: 18,
    minHeight: 40,
  },
  additionalAmountInput: {
    height: 57,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#cbcbcb',
    backgroundColor: '#3D3D50',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#fff',
    marginBottom: 5,
    fontSize: 16 / PixelRatio.getFontScale(),
  },
}
