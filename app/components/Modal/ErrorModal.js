import React from 'react'
import {Modal, View, Text, Image, TouchableOpacity} from 'react-native'

import styles from './styles'

const ErrorModal = props => (
  <Modal
    animationType="fade"
    transparent
    visible={props.modalVisable && !props.showPinScreen}
    onRequestClose={() => props.closeErrorModal()}>
    <View style={styles.helpModalBox}>
      <View style={styles.helpModalSquare}>
        <TouchableOpacity
          style={styles.helpModalX}
          onPress={() => {
            props.closeErrorModal()
          }}>
          <Image
            source={require('../../imgs/closeX.png')}
            style={styles.closeModalImg}
          />
        </TouchableOpacity>
        <View style={styles.helpModalTitleBox}>
          <Text style={styles.helpModalTitle}>{props.title}</Text>
        </View>
        <View style={styles.errorModalDescriptionBox}>
          <Text style={styles.helpModalDescription}>
            Error submitting your ticket
          </Text>
          <Text style={styles.helpModalDescription}>
            Please try again in a few minutes
          </Text>
        </View>
      </View>
    </View>
  </Modal>
)

export default ErrorModal
