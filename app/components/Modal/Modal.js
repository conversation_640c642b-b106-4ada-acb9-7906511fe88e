import React from 'react';
import {
  Modal as NativeModal,
  View,
  Text,
  Image,
  TouchableOpacity,
} from 'react-native';

import styles from './styles';

const Modal = props => (
  <NativeModal animationType="fade" transparent visible={props.visible}>
    <View style={styles.modal}>
      <View style={styles.square}>
        <TouchableOpacity style={styles.modalX} onPress={props.close}>
          <Image
            source={require('../../imgs/closeX.png')}
            style={styles.closeImg}
          />
        </TouchableOpacity>
        <View style={styles.titleBox}>
          <Text style={styles.title}>{props.title}</Text>
        </View>
        <View style={styles.descriptionBox}>{props.children}</View>
      </View>
    </View>
  </NativeModal>
);

export default Modal;
