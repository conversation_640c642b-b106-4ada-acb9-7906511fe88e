import React from 'react'
import {Modal, View, Text, Image, TouchableOpacity} from 'react-native'

import {Button} from '../../components'

import styles from './styles'

const SuccessModal = props => (
  <Modal
    animationType="fade"
    transparent
    visible={props.showModal && !props.showPinScreen}
    onRequestClose={() => props.closeModal()}>
    <View style={styles.successModalBox}>
      <View style={styles.successModalSquare}>
        <TouchableOpacity
          style={styles.successModalX}
          onPress={() => props.closeModal()}>
          <Image
            source={require('../../imgs/closeX.png')}
            style={styles.closeModalImg}
          />
        </TouchableOpacity>
        <View style={styles.successModalTitleBox}>
          <Image
            source={require('../../imgs/checkmarkCircle.png')}
            style={styles.checkmarkCircleImg}
          />
          <Text style={styles.successModalTitle}>{props.title}</Text>
          <Button style={{marginTop: 20}} onPress={() => props.closeModal()}>
            Ok
          </Button>
        </View>
      </View>
    </View>
  </Modal>
)

export default SuccessModal
