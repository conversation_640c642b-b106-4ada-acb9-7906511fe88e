import React, {Component} from 'react'
import DropDownPicker from 'react-native-dropdown-picker'

import styles from './styles'

class LocationSelect extends Component {
  constructor(props) {
    super(props)
    this.state = {
      open: false,
      value: null,
      items: [],
    }

    this.setValue = this.setValue.bind(this)
  }

  setOpen = open => {
    this.setState({
      open,
    })
  }

  setValue = callback => {
    this.setState(state => ({
      value: callback(state.value),
    }))
    const value = callback()
    if (this.props.onSelect) {
      this.props.onSelect(value)
    }
  }

  setItems = callback => {
    this.setState(state => ({
      items: callback(state.items),
    }))
  }

  render() {
    const {open, value} = this.state
    let {newValue} = this.props || null
    let items = this.props?.options || []
    if (!this.props.dataCorrect) {
      items = items.map((a, k) => ({label: a, value: a, key: `${a}${k}`}))
    }
    const textStyle = this.props.textStyle
      ? this.props.textStyle
      : {
          fontSize: 16,
          color: '#FFF',
        }

    return (
      <DropDownPicker
        open={open}
        value={newValue || value}
        items={items}
        setOpen={this.setOpen}
        setValue={this.setValue}
        setItems={this.setItems}
        itemKey="key"
        theme="DARK"
        listMode={this.props.listmode || 'MODAL'}
        searchable={this.props.searchable != 'no'}
        closeAfterSelecting={this.props.closeAfterSelecting != 'no'}
        placeholder={this.props.placeholder}
        style={this.props.style ? this.props.style : styles.unit21InfoInput}
        textStyle={textStyle}
        placeholderStyle={this.props.placeholderStyle ? this.props.placeholderStyle : textStyle}
        translation={{
          NOTHING_TO_SHOW: 'Not Found!',
        }}
      />
    )
  }
}

export default LocationSelect
