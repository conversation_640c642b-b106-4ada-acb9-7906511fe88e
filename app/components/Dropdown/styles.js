import {StyleSheet, PixelRatio} from 'react-native';

const styles = StyleSheet.create({
  countryBox: {
    height: 40,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    marginBottom: 10,
    backgroundColor: '#3D3D50',
  },
  countryBoxHighlight: {
    height: 40,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#00FFBD',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    marginBottom: 10,
    backgroundColor: '#3D3D50',
  },
  renderRowBox: {
    paddingLeft: 10,
    backgroundColor: '#fff',
    width: 280,
  },
  renderRowText: {
    height: 40,
    width: 200,
    paddingTop: 8,
    fontSize: 16,
  },
  buttonText: {
    height: 40,
    width: 200,
    paddingTop: 8,
    fontSize: 16,
  },
  unit21InfoInput: {
    height: 54,
    borderWidth: 1,
    borderRadius: 6,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    backgroundColor: '#474756', //'#3D3D50',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 18,
    minHeight: 40,
  },
});

export default styles;
