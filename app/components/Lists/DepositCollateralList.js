import React, { Component } from 'react';
import { connect } from 'react-redux';
import { View, Image, TouchableOpacity } from 'react-native';

import { getDepositCollateralPic } from '../../util/tokens';
import { TextReg } from '../../components';
import styles from './styles';

class SwitchAccountsModal extends Component {
	openCollateral = (token) => {
	  this.props.showConfirmDeposit(token);
	}

	render () {
	  const collaterals = this.props.loanData.collaterals;
	  const showList = collaterals.map((a, k) => {
	    if (a.currency == 'XRP' || a.currency == 'DASH' || a.currency == 'DOGE' || a.currency == 'SALT') {
	      return;
	    }
	    const collateralImg = getDepositCollateralPic(a.currency);
	    return (
	      <TouchableOpacity
	        key={k}
	        onPress={() => {
	          this.openCollateral(a.currency);
	        }}
	        style={{ alignItems: 'center', margin: 10 }}
	      >
	        <Image source={collateralImg} style={{ height: 80, width: 80, marginBottom: 10 }} />
	        <TextReg style={{ fontSize: 18 }}>{a.currency}</TextReg>
	      </TouchableOpacity>
	    );
	  });
	  return (
	    <View
	      style={{
	        flexDirection: 'row',
	        flexWrap: 'wrap',
	        width: 300
	      }}
	    >
	      {showList}
	    </View>
	  );
	}
}

const mapStateToProps = (state) => ({
  user: state.user.user,
  WebService: state.auth.WebService,
  loanData: state.user.loanData
});

export default connect(mapStateToProps)(SwitchAccountsModal);
