import React, {Component} from 'react'
import {
  AppState,
  View,
  TouchableOpacity,
  TextInput,
  Clipboard,
} from 'react-native'

import closeWithdrawImg from '../../imgs/notifClose.png'
import {TextReg} from '../../components'
import styles from './styles'

class VerificationInput extends Component {
  constructor(props) {
    super(props)
    this.state = {
      code: '',
      textInputActive: false,
      submitting: false,
      appState: AppState.currentState,
    }

    this.inputs = {}
    this.appStateSubscription = null
  }

  componentDidMount() {
    this.setState({textInputActive: true}, () => {
      setTimeout(() => this.inputs.twoFactor.focus(), 0)
    })

    AppState.addEventListener('change', this.handleAppStateChange)
  }

  componentWillUnmount() {
    //this.appStateSubscription.remove();
    //AppState.removeEventListener('change', this.handleAppStateChange)
  }

  handleAppStateChange = async nextAppState => {
    if (
      // background to forground
      this.state.appState.match(/inactive|background/) &&
      nextAppState === 'active'
    ) {
      this.setState({appState: 'active'}, () => {
        this.checkClipboard()
      })
    } else if (
      // foreground to background
      this.state.appState === 'active' &&
      (nextAppState === 'inactive' || nextAppState === 'background')
    ) {
      this.setState({appState: 'background'})
    }
  }

  checkClipboard = async () => {
    const code = await Clipboard.getString()
    if (code.length == 6) {
      this.props.updateTwoFactor(code)
    }
  }

  showBlock = num => {
    if (this.props.code.length > num) {
      return this.props.code[num]
    }
    return ''
  }

  handleVerificationCode = (text = this.state.code) => {
    if (text.length > 6) return
    if (text.length < 6) {
      this.setState({submitting: false})
    }
    if (text.length === 6) {
      this.setState({submitting: true})
    }
    this.props.updateTwoFactor(text)

    /*
    if (text.length === 6) {
      this.setState({ code: text })
      this.props.updateTwoFactor(text)
      return
    }

    if (text.length < 6) {
      this.setState({ code: text })
    }
    */
  }

  verifyCode = (text = this.state.code) => {
    if (!this.state.submitting) {
      this.setState({submitting: true}, () => {
        this.props.handleLogin()
      })
    }
  }

  focusTextInput = () => {
    this.setState({textInputActive: true})
  }

  blurTextInput = () => {
    this.setState({textInputActive: false})
  }

  render() {
    const highlightInput = this.state.textInputActive
      ? this.props.code.length
      : false

    return (
      <View style={styles.headerButtonBox}>
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => this.inputs.twoFactor.focus()}>
          <View style={styles.signUpVerifcationBlocksBox2FA}>
            <View
              style={[
                styles.signUpVerifcationBlock,
                highlightInput === 0 && styles.signUpVerifcationBlockActive,
              ]}>
              <TextReg style={styles.signUpVerifcationBlockText}>
                {this.showBlock(0)}
              </TextReg>
            </View>
            <View
              style={[
                styles.signUpVerifcationBlock,
                highlightInput === 1 && styles.signUpVerifcationBlockActive,
              ]}>
              <TextReg style={styles.signUpVerifcationBlockText}>
                {this.showBlock(1)}
              </TextReg>
            </View>
            <View
              style={[
                styles.signUpVerifcationBlock,
                highlightInput === 2 && styles.signUpVerifcationBlockActive,
              ]}>
              <TextReg style={styles.signUpVerifcationBlockText}>
                {this.showBlock(2)}
              </TextReg>
            </View>
            <View
              style={[
                styles.signUpVerifcationBlock,
                highlightInput === 3 && styles.signUpVerifcationBlockActive,
              ]}>
              <TextReg style={styles.signUpVerifcationBlockText}>
                {this.showBlock(3)}
              </TextReg>
            </View>
            <View
              style={[
                styles.signUpVerifcationBlock,
                highlightInput === 4 && styles.signUpVerifcationBlockActive,
              ]}>
              <TextReg style={styles.signUpVerifcationBlockText}>
                {this.showBlock(4)}
              </TextReg>
            </View>
            <View
              style={[
                styles.signUpVerifcationBlock,
                highlightInput === 5 && styles.signUpVerifcationBlockActive,
              ]}>
              <TextReg style={styles.signUpVerifcationBlockText}>
                {this.showBlock(5)}
              </TextReg>
            </View>
          </View>
        </TouchableOpacity>
        <TextInput
          style={styles.signUpTextInputEmailVerificationCode}
          onChangeText={this.handleVerificationCode}
          value={this.props.code}
          returnKeyType={'done'}
          ref={input => (this.inputs.twoFactor = input)}
          onSubmitEditing={() => this.verifyCode()}
          keyboardType={'numeric'}
          onFocus={this.focusTextInput}
          onBlur={this.blurTextInput}
          keyboardAppearance="dark"
        />
      </View>
    )
  }
}

export default VerificationInput
