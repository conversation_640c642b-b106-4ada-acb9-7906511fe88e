import React, {Component} from 'react'
import {View, Image, Animated, TouchableOpacity, Platform} from 'react-native'
import {connect} from 'react-redux'

import {TextReg} from '../../components'
import styles from './styles'
import commonStyles from '../../styles/commonStyles'

class BackgroundHeader extends Component {
  render() {
    const headerHeight = Platform.OS === 'ios' ? 88 : 50
    let marginTop = Platform.OS === 'ios' ? 14 : 0

    return (
      <React.Fragment>
        <Animated.View
          style={[styles.backgroundBox, {height: headerHeight}, this.props.style]}
          pointerEvents="none"
        />
        {this.props.title && (
          <View style={[styles.BackgroundHeaderBox, {height: headerHeight, marginTop}]}>
            {this.props.goBack ? (
              <TouchableOpacity onPress={() => this.props.goBack()}>
                <View style={styles.backToSettingsBox}>
                  <Image
                    source={require('../../imgs/backToSettings.png')}
                    style={styles.backToSettingsImg}
                  />
                </View>
              </TouchableOpacity>
            ) : (
              <View style={{width: 40}}>
                {this.props.leftIcon == 'homeScreen' && (
                  <>
                    {this.props.notification && (
                      <TouchableOpacity
                        onPress={() => this.props.toggleNotifications()}
                        style={styles.notificationButton}>
                        <Image
                          source={
                            this.props.showNotifications
                              ? require('../../imgs/closeX.png')
                              : require('../../imgs/notifBell-white.png')
                          }
                          style={
                            this.props.showNotifications
                              ? {height: 24, width: 24, marginLeft: -2, marginBottom: -2}
                              : styles.notificationImg
                          }
                        />
                      </TouchableOpacity>
                    )}
                  </>
                )}
              </View>
            )}

            <TextReg style={styles.BackgroundHeaderTitle}>{this.props.title}</TextReg>
            <View style={{width: 40}}>
              {this.props.close ? (
                <TouchableOpacity
                  style={{marginTop: -4, width: 40, marginRight: 2}}
                  onPress={() => {
                    this.props.closeFn ? this.props.closeFn() : this.props.navigation.popToTop()
                  }}>
                  <Image
                    source={require('../../imgs/closeX.png')}
                    style={commonStyles.closeModalImg}
                  />
                </TouchableOpacity>
              ) : (
                <>
                  {this.props.navigateSettings && (
                    <TouchableOpacity onPress={() => this.props.navigateSettings()}>
                      <Image
                        source={require('../../imgs/icons/dark/settings.png')}
                        style={{height: 22, width: 22}}
                      />
                    </TouchableOpacity>
                  )}
                </>
              )}
            </View>
          </View>
        )}
      </React.Fragment>
    )
  }
}

const mapStateToprops = state => ({
  showNotifications: state.notifications.showNotifications,
})

export default connect(mapStateToprops)(BackgroundHeader)
