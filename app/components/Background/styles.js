import { StyleSheet, Platform } from 'react-native'

const styles = StyleSheet.create({
  backgroundBox: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    width: '100%',
    flex: 1,
    borderRadius: 0,
    zIndex: -1,
    borderWidth: 0,
    backgroundColor: '#28283D'
  },
  backgroundImg: {
    top: 0,
    bottom: 0,
    width: '100%',
    flex: 1
  },
  BackgroundHeaderBox: {
    alignSelf: 'stretch',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 32 : -14
  },
  backToSettingsBox: {
    width: 40,
    justifyContent: 'center',
    marginLeft: 6
  },
  backToSettingsImg: {
    height: 24,
    width: 30
  },
  BackgroundHeaderTitle: {
    fontSize: 19,
    color: '#FFF',
    marginBottom: 1
  },
  notificationButton: {
    height: 30,
    justifyContent: 'center',
    marginLeft: 20
  },
  notificationImg: {
    width: 20,
    height: 22,
    opacity: 0.5
  }
})

export default styles
