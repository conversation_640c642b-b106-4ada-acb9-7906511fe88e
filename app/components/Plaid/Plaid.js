/*
import React from 'react'
import { View, Image } from 'react-native'

import PlaidLink from 'react-native-plaid-link-sdk'
import { TextBold } from '../../components'
import styles from './styles'


const Plaid = props => (
  <View style={{ borderRadius: 14 }}>
    {props.linkingBank ? (
      <View style={styles.plaidBox}>
        <Image
          source={require('../../imgs/loadingDots.gif')}
          style={styles.loadingDots}
        />
      </View>
    ) : (
      <PlaidLink
        activeOpacity={0.5}
        publicKey="96e9bf8207672b64c2aa0df65c71b3"
        clientName="SALT Lending"
        env="production"
        onSuccess={e => props.successfulLink(e)}
        onClick={props.resetAndClose}
        onExit={props.resetAndClose}
        product={['transactions']}
      >
        <View
          style={{
            ...styles.plaidButton,
            backgroundColor: props.active ? '#00FFBD' : 'lightgray',
          }}
        >
          <TextBold style={{ color: '#FFF', fontSize: 20 }}>Finalize</TextBold>
        </View>
      </PlaidLink>
    )}
  </View>
)
export default Plaid

*/
