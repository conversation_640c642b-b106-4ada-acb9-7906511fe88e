import React, {useEffect, useState, useRef} from 'react'
import {View, TouchableOpacity, Image} from 'react-native'
import closeX from '../../../imgs/closeX.png'

import {TextReg, TextBold} from '../../../components'

import styles from '../styles'

let Margin1 = ({next}) => {
  return (
    <View style={styles.child}>
      <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
        <View
          style={{backgroundColor: '#3D3D50', height: 180, width: 180, borderRadius: 90, justifyContent: 'center', alignItems: 'center'}}>
          <Image source={require('../../../imgs/graphics/stop.png')} style={{width: 90, height: 90}} />
        </View>
        <View style={{width: 330, alignItems: 'center'}}>
          <TextReg style={{...styles.titleHealth, color: '#E6705B'}}>{`MARGIN CALL`}</TextReg>
          <TextBold
            style={{
              ...styles.title2,
            }}>{`Your LTV is above 83.33%, so Pay Down Principal with Stablecoin`}</TextBold>
          <TextReg
            style={{
              marginTop: 20,
              fontSize: 16,
            }}>{`Make a one time stablecoin payment towards your principal balance.`}</TextReg>

          <TextReg
            style={{
              marginTop: 10,
              fontSize: 16,
            }}>{`This option gives you the best impact towards reestablishing the health of your loan.`}</TextReg>
        </View>
      </View>
      <TouchableOpacity onPress={() => next()} style={styles.button}>
        <TextBold style={{fontSize: 18, letterSpacing: 0.75, color: '#000'}}>{`NEXT`}</TextBold>
      </TouchableOpacity>
    </View>
  )
}

export default Margin1
