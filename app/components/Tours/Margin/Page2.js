import React, {useEffect, useState, useRef} from 'react'
import {View, TouchableOpacity, Image} from 'react-native'
import closeX from '../../../imgs/closeX.png'

import {TextReg, TextBold} from '../../../components'

import styles from '../styles'

let Margin2 = ({next}) => {
  return (
    <View style={styles.child}>
      <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
        <View
          style={{backgroundColor: '#3D3D50', height: 180, width: 180, borderRadius: 90, justifyContent: 'center', alignItems: 'center'}}>
          <Image source={require('../../../imgs/graphics/stop.png')} style={{width: 90, height: 90}} />
        </View>
        <View style={{width: 330}}>
          <TextReg style={{...styles.titleHealth, color: '#E6705B', alignSelf: 'center'}}>{`MARGIN CALL`}</TextReg>
          <TextBold
            style={{
              ...styles.title2,
            }}>{`Or Deposit Additional Collateral`}</TextBold>
          <View style={{flexDirection: 'row', alignSelf: 'stretch'}}>
            <View style={{marginRight: 10}}>
              <TextReg
                style={{
                  marginTop: 20,
                  fontSize: 16,
                }}>{`1.`}</TextReg>
              <TextReg
                style={{
                  marginTop: 30,
                  fontSize: 16,
                }}>{`2.`}</TextReg>
            </View>
            <View>
              <TextReg
                style={{
                  marginTop: 20,
                  fontSize: 16,
                }}>{`Deposit additional stablecoin collateral (Recommended)`}</TextReg>
              <TextReg
                style={{
                  marginTop: 10,
                  fontSize: 16,
                }}>{`Deposit additional volatile collateral (Might require an additional action depending on the market)`}</TextReg>
            </View>
          </View>

          <TextReg
            style={{
              marginTop: 10,
              fontSize: 16,
            }}>{`We provide the calculated amount needed to cure your LTV to a healthy state.`}</TextReg>
        </View>
      </View>
      <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
        <TouchableOpacity onPress={() => next()} style={styles.button}>
          <TextBold style={{fontSize: 18, letterSpacing: 0.75, color: '#000'}}>{`MANAGE LTV`}</TextBold>
        </TouchableOpacity>
      </View>
    </View>
  )
}

export default Margin2
