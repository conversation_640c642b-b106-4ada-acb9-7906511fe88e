import {StyleSheet, Dimensions, PixelRatio, Platform} from 'react-native'

const {width, height} = Dimensions.get('window')

const styles = StyleSheet.create({
  paginationDots: {
    height: 10,
    width: 10,
    marginLeft: 5,
    marginRight: 5,
    marginBottom: 6,
  },
  modal: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#28283D', //#F5FCFF
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: 20,
    zIndex: 100,
  },
  child: {
    flex: 1,
    width,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 40,
  },
  top: {
    flexDirection: 'row',
    alignSelf: 'stretch',
    justifyContent: 'flex-end',
    margin: 10,
    padding: 6,
  },
  closeX: {
    height: 26,
    width: 26,
    opacity: 0.7,
  },
  button: {backgroundColor: '#00FFBD', height: 50, width: 300, borderRadius: 6, justifyContent: 'center', alignItems: 'center'},
  button2: {
    backgroundColor: '#28283D',
    borderColor: '#00FFBD',
    borderWidth: 2,
    height: 50,
    width: 300,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {marginTop: 30, fontSize: 30, textAlign: 'center'},
  title2: {marginTop: 30, fontSize: 22},
  titleHealth: {marginTop: 20, fontSize: 15, letterSpacing: 2},
})

export default styles
