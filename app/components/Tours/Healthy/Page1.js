import React, {useEffect, useState, useRef} from 'react'
import {View, TouchableOpacity, Image} from 'react-native'
import closeX from '../../../imgs/closeX.png'

import {TextReg, TextBold} from '../../../components'

import styles from '../styles'

let Convert1 = ({next}) => {
  return (
    <View style={styles.child}>
      <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
        <View
          style={{backgroundColor: '#3D3D50', height: 180, width: 180, borderRadius: 80, justifyContent: 'center', alignItems: 'center'}}>
          <Image source={require('../../../imgs/graphics/loanDark.png')} style={{width: 180, height: 180}} />
        </View>
        <View style={{width: 330}}>
          <TextBold
            style={{
              ...styles.title,
              paddingLeft: 20,
              paddingRight: 20,
            }}>{`Welcome to your Loan Dashboard!`}</TextBold>
          <TextReg
            style={{
              marginTop: 20,
              fontSize: 16,
              textAlign: 'center',
            }}>{`Take a quick tour to learn about your loan. We make sure you will find everything you need to get started.`}</TextReg>
        </View>
      </View>
      <TouchableOpacity onPress={() => next()} style={styles.button}>
        <TextBold style={{fontSize: 18, letterSpacing: 0.75, color: '#000'}}>{`NEXT`}</TextBold>
      </TouchableOpacity>
    </View>
  )
}

export default Convert1
