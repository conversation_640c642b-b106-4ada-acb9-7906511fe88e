import React, {useEffect, useState, useRef} from 'react'
import {View, TouchableOpacity, Image} from 'react-native'
import closeX from '../../../imgs/closeX.png'

import {TextReg, TextBold} from '../../../components'

import styles from '../styles'

let Health4 = ({next, forStabilization}) => {
  return (
    <View style={styles.child}>
      <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
        <View className={'flexCol'} style={{width: 330, alignItems: 'center'}}>
          <View
            style={{
              backgroundColor: '#3D3D50',
              height: 180,
              width: 330,
              borderRadius: 14,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Image
              source={require('../../../imgs/graphics/health3.png')}
              style={{width: 300, height: 57}}
            />
            <View
              style={{
                width: 300,
                flexDirection: 'row',
                justifyContent: 'space-between',
                backgroundColor: '#3D3D50',
                marginTop: -13,
              }}>
              <TextReg style={{fontSize: 12}}>Healthy</TextReg>
              <TextReg style={{fontSize: 12}}>
                {forStabilization ? 'Stabilization' : 'Liquidation'}
              </TextReg>
            </View>
          </View>
          <TextBold
            style={{
              ...styles.title,
              paddingLeft: 20,
              paddingRight: 20,
            }}>{`Manage your Loan's Health!`}</TextBold>
          <TextReg style={{...styles.titleHealth, color: '#E6705B'}}>{`MARGIN CALL`}</TextReg>
          <TextBold
            style={{
              marginTop: 14,
              fontSize: 22,
            }}>{`When LTV is 83.33% - 90.90%`}</TextBold>
          <TextReg
            style={{
              marginTop: 10,
              fontSize: 16,
              textAlign: 'center',
              paddingLeft: 10,
              paddingRight: 10,
            }}>{`Take action to cure your loan back to a healthy state.`}</TextReg>
        </View>
      </View>
      <TouchableOpacity onPress={() => next()} style={styles.button}>
        <TextBold style={{fontSize: 18, letterSpacing: 0.75, color: '#000'}}>{`NEXT`}</TextBold>
      </TouchableOpacity>
    </View>
  )
}

export default Health4
