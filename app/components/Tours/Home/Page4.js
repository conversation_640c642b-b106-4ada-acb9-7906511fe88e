import React, {useEffect, useState, useRef} from 'react'
import {View, TouchableOpacity, Image} from 'react-native'
import closeX from '../../../imgs/closeX.png'

import {TextReg, TextBold} from '../../../components'

import styles from '../styles'

let HomeTour = ({next}) => {
  return (
    <View style={styles.child}>
      <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
        <View
          style={{backgroundColor: '#3D3D50', height: 160, width: 160, borderRadius: 80, justifyContent: 'center', alignItems: 'center'}}>
          <Image source={require('../../../imgs/graphics/bell2.png')} style={{width: 80, height: 80}} />
        </View>
        <TextBold style={styles.title}>{`Notifications`}</TextBold>
        <TextReg
          style={{
            marginTop: 20,
            fontSize: 16,
            textAlign: 'center',
            width: 280,
          }}>{`Find important noftifications about your SALT account here.`}</TextReg>
      </View>
      <TouchableOpacity onPress={() => next()} style={styles.button}>
        <TextBold style={{fontSize: 18, letterSpacing: 0.75, color: '#000'}}>{`NEXT`}</TextBold>
      </TouchableOpacity>
    </View>
  )
}

export default HomeTour
