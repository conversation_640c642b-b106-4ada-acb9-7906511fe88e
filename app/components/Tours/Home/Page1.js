import React, {useEffect, useState, useRef} from 'react'
import {View, TouchableOpacity, Image} from 'react-native'
import closeX from '../../../imgs/closeX.png'

import {TextReg, TextBold} from '../../../components'

import styles from '../styles'

let HomeTour = ({next}) => {
  return (
    <View style={styles.child}>
      <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
        <Image source={require('../../../imgs/graphics/globe2.png')} style={{width: 160, height: 160}} />
        <TextBold style={styles.title}>{`Welcome To SALT!`}</TextBold>
        <TextReg
          style={{
            marginTop: 20,
            fontSize: 16,
            textAlign: 'center',
            width: 280,
          }}>{`The world of crypto loans and wallets, on an easy and secure platform.`}</TextReg>
      </View>
      <TouchableOpacity onPress={() => next()} style={styles.button}>
        <TextBold style={{fontSize: 18, letterSpacing: 0.75, color: '#000'}}>{`NEXT`}</TextBold>
      </TouchableOpacity>
    </View>
  )
}

export default HomeTour
