import React, {useEffect, useState, useRef} from 'react'
import {View, SafeAreaView, Image, TouchableOpacity} from 'react-native'
import {useSelector} from 'react-redux'

import AsyncStorage from '@react-native-async-storage/async-storage'
import SwiperFlatList from 'react-native-swiper-flatlist'
import closeX from '../../../imgs/closeX.png'
import styles from '../styles'

import Page1 from './Page1'
import Page2 from './Page2'
import Page3 from './Page3'
import Page4 from './Page4'
import Page5 from './Page5'

let HomeTour = ({}) => {
  let launchDarkly = useSelector(state => state.launchDarkly)
  let tourCount = useSelector(state => state.user.tourCount)
  let [comp, setComp] = useState(false)
  let swiperRef = useRef(null)

  useEffect(() => {
    const getStorage = async () => {
      let isComplete = await AsyncStorage.getItem('HOME_TOUR')
      if (isComplete === 'true') {
        setComp(true)
      } else {
        setComp(false)
      }
    }

    getStorage()
  }, [tourCount])

  let close = async () => {
    await AsyncStorage.setItem('HOME_TOUR', 'true')
    setComp(true)
  }

  //if complete || not on
  if (comp || !launchDarkly['show-home-tour']) {
    return null
  }

  return (
    <SafeAreaView style={styles.modal}>
      <View style={styles.top}>
        <TouchableOpacity onPress={() => close()}>
          <Image source={require('../../../imgs/closeX.png')} style={styles.closeX} />
        </TouchableOpacity>
      </View>
      <SwiperFlatList
        ref={swiperRef}
        scrollEventThrottle={16}
        showPagination
        paginationDefaultColor={'#7E7E89'}
        paginationActiveColor={'#00ffc3'}
        paginationStyle={{marginBottom: 20}}
        paginationStyleItem={styles.paginationDots}>
        <Page1 next={() => swiperRef.current._scrollToIndex(1)} />
        <Page2 next={() => swiperRef.current._scrollToIndex(2)} />
        <Page3 next={() => swiperRef.current._scrollToIndex(3)} />
        <Page4 next={() => swiperRef.current._scrollToIndex(4)} />
        <Page5 next={() => close()} />
      </SwiperFlatList>
    </SafeAreaView>
  )
}

export default HomeTour
