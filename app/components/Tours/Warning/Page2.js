import React, {useEffect, useState, useRef} from 'react'
import {View, TouchableOpacity, Image} from 'react-native'
import closeX from '../../../imgs/closeX.png'

import {TextReg, TextBold} from '../../../components'

import styles from '../styles'

let Warning2 = ({next, forStabilization}) => {
  return (
    <View style={styles.child}>
      <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
        <View
          style={{
            backgroundColor: '#3D3D50',
            height: 180,
            width: 180,
            borderRadius: 90,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Image
            source={require('../../../imgs/graphics/warn.png')}
            style={{width: 90, height: 80}}
          />
        </View>
        <View style={{width: 330}}>
          <TextReg
            style={{
              ...styles.titleHealth,
              color: '#F7D956',
              alignSelf: 'center',
            }}>{`WARNING LOAN STATUS`}</TextReg>
          <TextBold
            style={{
              ...styles.title2,
            }}>
            How to cure your LTV to below 70% to avoid{' '}
            {forStabilization ? 'Stabilization' : 'Liquidation'}
          </TextBold>
          <View style={{flexDirection: 'row', alignSelf: 'stretch'}}>
            <View style={{marginRight: 10}}>
              <TextReg
                style={{
                  marginTop: 20,
                  fontSize: 16,
                }}>{`1.`}</TextReg>
              <TextReg
                style={{
                  marginTop: 30,
                  fontSize: 16,
                }}>{`2.`}</TextReg>
              <TextReg
                style={{
                  marginTop: 30,
                  fontSize: 16,
                }}>{`3.`}</TextReg>
            </View>
            <View>
              <TextReg
                style={{
                  marginTop: 20,
                  fontSize: 16,
                }}>
                <View
                  style={{
                    backgroundColor: '#5A33E3',
                    borderRadius: 4,
                    paddingLeft: 2,
                    paddingRight: 2,
                    marginBottom: -2,
                  }}>
                  <TextReg>{`RECOMMENDED`}</TextReg>
                </View>
                {` Make a one time payment with stablecoin towards your principal balance`}
              </TextReg>
              <TextReg
                style={{
                  marginTop: 10,
                  fontSize: 16,
                }}>{`Deposit additional stablecoin collateral to your loan’s collateral wallet`}</TextReg>
              <TextReg
                style={{
                  marginTop: 10,
                  fontSize: 16,
                }}>{`Deposit additional volatile collateral to your loan’s collateral wallet`}</TextReg>
            </View>
          </View>
        </View>
      </View>
      <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
        <TouchableOpacity onPress={() => next()} style={styles.button}>
          <TextBold
            style={{fontSize: 18, letterSpacing: 0.75, color: '#000'}}>{`MANAGE LTV`}</TextBold>
        </TouchableOpacity>
      </View>
    </View>
  )
}

export default Warning2
