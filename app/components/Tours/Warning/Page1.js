import React, {useEffect, useState, useRef} from 'react'
import {View, TouchableOpacity, Image} from 'react-native'
import closeX from '../../../imgs/closeX.png'

import {TextReg, TextBold} from '../../../components'

import styles from '../styles'

let Warning1 = ({next, forStabilization}) => {
  return (
    <View style={styles.child}>
      <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
        <View
          style={{
            backgroundColor: '#3D3D50',
            height: 180,
            width: 180,
            borderRadius: 90,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Image
            source={require('../../../imgs/graphics/warn.png')}
            style={{width: 90, height: 80}}
          />
        </View>
        <View style={{width: 330, alignItems: 'center'}}>
          <TextReg
            style={{...styles.titleHealth, color: '#F7D956'}}>{`WARNING LOAN STATUS`}</TextReg>
          <TextBold
            style={{
              ...styles.title2,
              alignSelf: 'flex-start',
            }}>{`Your LTV is above 75%, and you are now in Warning Status`}</TextBold>
          <TextReg
            style={{
              marginTop: 20,
              fontSize: 16,
            }}>
            When the market starts crashing and your Loan-to-Value ratio (LTV) reaches 90.91%, we
            {forStabilization
              ? 'stabilize your portfolio and convert it to USDC stablecoin to preserve its value.'
              : 'liquidate portion of your portfolio to pay the principle amount due on the loan to cure you loan in to a healthy status.'}
          </TextReg>
        </View>
      </View>
      <TouchableOpacity onPress={() => next()} style={styles.button}>
        <TextBold style={{fontSize: 18, letterSpacing: 0.75, color: '#000'}}>{`NEXT`}</TextBold>
      </TouchableOpacity>
    </View>
  )
}

export default Warning1
