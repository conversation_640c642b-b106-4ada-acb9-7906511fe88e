import React, {useEffect, useState, useRef} from 'react'
import {View, TouchableOpacity, Image} from 'react-native'
import closeX from '../../../imgs/closeX.png'

import {TextReg, TextBold} from '../../../components'

import styles from '../styles'

let Stabilize2 = ({next}) => {
  return (
    <View style={styles.child}>
      <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
        <View
          style={{backgroundColor: '#3D3D50', height: 180, width: 180, borderRadius: 90, justifyContent: 'center', alignItems: 'center'}}>
          <Image source={require('../../../imgs/graphics/raft.png')} style={{width: 90, height: 90}} />
        </View>
        <View style={{width: 330}}>
          <TextReg style={{...styles.titleHealth, color: '#AFAFAF', alignSelf: 'center'}}>{`STABILIZATION`}</TextReg>
          <View
            style={{
              width: 100,
              height: 40,
              borderRadius: 8,
              marginTop: 20,
              backgroundColor: '#5A33E3',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <TextReg>{`BEST OPTION`}</TextReg>
          </View>
          <TextBold
            style={{
              ...styles.title2,
              textAlign: 'left',
              marginTop: 10,
            }}>{`Cure Your Loan's Health!`}</TextBold>
          <TextReg
            style={{
              marginTop: 8,
              fontSize: 16,
            }}>
            {`Make a one time principal payment with stablecoin to get your loan back to a healthy state of`}
            <TextReg style={{color: '#00FFBD'}}>{` below 70% LTV.`}</TextReg>
          </TextReg>
          <TextBold
            style={{
              ...styles.title2,
              textAlign: 'left',
              marginTop: 20,
            }}>{`Not ready to cure your loan yet? Manage it instead.`}</TextBold>

          <TextReg
            style={{
              marginTop: 8,
              fontSize: 16,
            }}>
            {`Make a payment to manage your loan to`}
            <TextReg style={{color: '#F7D956'}}>{` below 83.33%`}</TextReg>
            {` to get back into the market faster instead of curing your loan.`}
          </TextReg>
        </View>
      </View>
      <TouchableOpacity onPress={() => next()} style={styles.button}>
        <TextBold style={{fontSize: 18, letterSpacing: 0.75, color: '#000'}}>{`NEXT`}</TextBold>
      </TouchableOpacity>
    </View>
  )
}

export default Stabilize2
