import React, {useEffect, useState, useRef} from 'react'
import {View, TouchableOpacity, Image} from 'react-native'
import closeX from '../../../imgs/closeX.png'

import {TextReg, TextBold} from '../../../components'

import styles from '../styles'

let Stabilize1 = ({next}) => {
  return (
    <View style={styles.child}>
      <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
        <View
          style={{backgroundColor: '#3D3D50', height: 180, width: 180, borderRadius: 90, justifyContent: 'center', alignItems: 'center'}}>
          <Image source={require('../../../imgs/graphics/raft.png')} style={{width: 90, height: 90}} />
        </View>
        <View style={{width: 330}}>
          <TextReg style={{...styles.titleHealth, color: '#AFAFAF', alignSelf: 'center'}}>{`STABILIZATION`}</TextReg>
          <TextBold
            style={{
              ...styles.title2,
            }}>{`Your LTV Exceeded 90.91%, so You've Been Stabilized!`}</TextBold>
          <TextReg
            style={{
              marginTop: 20,
              fontSize: 16,
            }}>{`Your loan was stabilized to protect it from liquidation. Take action now to become eligible to cure your loan or convert it to your desired collateral portfolio.`}</TextReg>
        </View>
      </View>
      <TouchableOpacity onPress={() => next()} style={styles.button}>
        <TextBold style={{fontSize: 18, letterSpacing: 0.75, color: '#000'}}>{`NEXT`}</TextBold>
      </TouchableOpacity>
    </View>
  )
}

export default Stabilize1
