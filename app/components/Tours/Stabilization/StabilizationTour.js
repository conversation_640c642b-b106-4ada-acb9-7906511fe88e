import React, {useEffect, useState, useRef} from 'react'
import {View, SafeAreaView, Image, TouchableOpacity} from 'react-native'
import {useSelector} from 'react-redux'

import {useNavigation} from '@react-navigation/native'
import AsyncStorage from '@react-native-async-storage/async-storage'
import SwiperFlatList from 'react-native-swiper-flatlist'
import closeX from '../../../imgs/closeX.png'
import styles from '../styles'

import Page1 from './Page1'
import Page2 from './Page2'
import Page3 from './Page3'

let StabiliziationTour = ({}) => {
  let navigation = useNavigation()
  let launchDarkly = useSelector(state => state.launchDarkly)
  let tourCount = useSelector(state => state.user.tourCount)

  let tab = useSelector(state => state.user.tab)
  let page = useSelector(state => state.user.page)
  let [comp, setComp] = useState(true)

  let loanData = useSelector(state => state.user.loanData || {})
  let accountRef = useSelector(state => state.auth.account.ref)
  let user = useSelector(state => state.user.user)
  let swiperRef = useRef(null)

  const {ltv, thresholds} = loanData
  const canRequestConversion = user?.accounts?.filter(a => a.ref == accountRef)[0]?.product?.loan?.canRequestConversion
  const isStabilized = user?.accounts?.filter(a => a.ref == accountRef)[0]?.product?.loan?.isStabilized
  const marginCallThresh = thresholds?.marginCall
  let showConvertBack = false
  if (Number(ltv) < Number(marginCallThresh) && canRequestConversion) {
    showConvertBack = true
  }

  useEffect(() => {
    const getStorage = async () => {
      let isComplete = await AsyncStorage.getItem('STABILIZATION_TOUR')
      if (isComplete === 'true') {
        setComp(true)
      } else {
        setComp(false)
      }
    }

    getStorage()
  }, [tourCount])

  let close = async () => {
    await AsyncStorage.setItem('STABILIZATION_TOUR', 'true')
    setComp(true)
  }

  let goToManage = () => {
    navigation.navigate('ManageLtv')
    close()
  }

  //only loans page
  if (page != 'Loans' || tab != 'Home') {
    return null
  }

  //only show for stabilized & not convert
  if (showConvertBack || !isStabilized) {
    return null
  }

  //if complete || not on
  if (comp || !launchDarkly['show-stabilization-tour']) {
    return null
  }

  return (
    <SafeAreaView style={styles.modal}>
      <View style={styles.top}>
        <TouchableOpacity onPress={() => close()}>
          <Image source={require('../../../imgs/closeX.png')} style={styles.closeX} />
        </TouchableOpacity>
      </View>
      <SwiperFlatList
        ref={swiperRef}
        scrollEventThrottle={16}
        showPagination
        paginationDefaultColor={'#7E7E89'}
        paginationActiveColor={'#00ffc3'}
        paginationStyle={{marginBottom: 20}}
        paginationStyleItem={styles.paginationDots}>
        <Page1 next={() => swiperRef.current._scrollToIndex(1)} />
        <Page2 next={() => swiperRef.current._scrollToIndex(2)} />
        <Page3 next={() => goToManage()} />
      </SwiperFlatList>
    </SafeAreaView>
  )
}

export default StabiliziationTour
