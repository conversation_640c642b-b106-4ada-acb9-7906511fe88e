import React, {useEffect, useState, useRef} from 'react'
import {View, TouchableOpacity, Image} from 'react-native'
import closeX from '../../../imgs/closeX.png'

import {TextReg, TextBold} from '../../../components'

import styles from '../styles'

let Convert2 = ({next}) => {
  return (
    <View style={styles.child}>
      <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
        <TextBold style={{...styles.title, width: 260, marginTop: -10}}>{`How to convert your loan?`}</TextBold>
        <View style={{width: 330}}>
          <View
            style={{
              backgroundColor: '#FFF',
              height: 60,
              width: 60,
              borderRadius: 30,
              justifyContent: 'center',
              alignItems: 'center',
              marginTop: 20,
            }}>
            <Image source={require('../../../imgs/logos/main/usdc.png')} style={{width: 40, height: 40}} />
          </View>
          <TextBold style={{...styles.title2, marginTop: 10}}>{`Converted amount during stabilization`}</TextBold>
          <TextReg
            style={{
              marginTop: 10,
              fontSize: 16,
            }}>{`When the stabilization event occurred, your loan was converted to USDC. You can see the converted amount here on the left side.`}</TextReg>
          <View style={{flexDirection: 'row', marginTop: 10}}>
            <View
              style={{
                backgroundColor: '#FFF',
                height: 60,
                width: 60,
                borderRadius: 30,
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: 20,
              }}>
              <Image source={require('../../../imgs/logos/main/usdc.png')} style={{width: 40, height: 40}} />
            </View>
            <View
              style={{
                backgroundColor: '#FFF',
                height: 60,
                width: 60,
                borderRadius: 30,
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: 20,
                marginLeft: -22,
                borderWidth: 2,
                borderColor: '#28283D',
              }}>
              <Image source={require('../../../imgs/logos/main/bch.png')} style={{width: 40, height: 40}} />
            </View>
            <View
              style={{
                backgroundColor: '#FFF',
                height: 60,
                width: 60,
                borderRadius: 30,
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: 20,
                marginLeft: -22,
                borderWidth: 2,
                borderColor: '#28283D',
              }}>
              <Image source={require('../../../imgs/logos/main/eth.png')} style={{width: 40, height: 40}} />
            </View>
            <View
              style={{
                backgroundColor: '#FFF',
                height: 60,
                width: 60,
                borderRadius: 30,
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: 20,
                marginLeft: -22,
                borderWidth: 2,
                borderColor: '#28283D',
              }}>
              <Image source={require('../../../imgs/logos/main/btc.png')} style={{width: 40, height: 40}} />
            </View>
          </View>
          <TextBold style={{...styles.title2, marginTop: 10}}>{`Convert back to collateral`}</TextBold>
          <TextReg
            style={{
              marginTop: 10,
              fontSize: 16,
            }}>{`You can now convert your loan back to collateral.`}</TextReg>
          <TextReg
            style={{
              marginTop: 2,
              fontSize: 16,
            }}>{`We set the collaterals to the original collateral mix, but you can now add other currencies too.`}</TextReg>
        </View>
      </View>
      <TouchableOpacity onPress={() => next()} style={styles.button}>
        <TextBold style={{fontSize: 18, letterSpacing: 0.75, color: '#000'}}>{`CONVERT NOW`}</TextBold>
      </TouchableOpacity>
    </View>
  )
}

export default Convert2
