import React, {useEffect, useState, useRef} from 'react'
import {View, TouchableOpacity, Image} from 'react-native'
import closeX from '../../../imgs/closeX.png'

import {TextReg, TextBold} from '../../../components'

import styles from '../styles'

let Convert1 = ({next}) => {
  return (
    <View style={styles.child}>
      <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
        <View
          style={{backgroundColor: '#3D3D50', height: 160, width: 160, borderRadius: 80, justifyContent: 'center', alignItems: 'center'}}>
          <Image source={require('../../../imgs/graphics/convert.png')} style={{width: 80, height: 80}} />
        </View>
        <View style={{width: 330}}>
          <TextBold style={styles.title2}>{`You're eligable to convert!`}</TextBold>
          <TextReg
            style={{
              marginTop: 20,
              fontSize: 16,
            }}>{`Your loan was stabilized and you’ve cured your LTV, so now you’re eligible to convert your loan back to volatile collateral when you’re ready to re-enter the market.`}</TextReg>
        </View>
      </View>
      <TouchableOpacity onPress={() => next()} style={styles.button}>
        <TextBold style={{fontSize: 18, letterSpacing: 0.75, color: '#000'}}>{`SHOW ME HOW`}</TextBold>
      </TouchableOpacity>
    </View>
  )
}

export default Convert1
