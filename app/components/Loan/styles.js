import {StyleSheet} from 'react-native';

const styles = StyleSheet.create({
  loanBarBox: {
    marginTop: 2,
    height: 34,
    position: 'relative',
  },
  loanBarGreen: {
    backgroundColor: '#3d4b45', //'#ccfbe6',
    height: 28,
    position: 'absolute',
    zIndex: 12,
    opacity: 0.7,
    marginTop: 6,
    borderColor: '#3D3D50',
    borderWidth: 0.5,
  },
  loanBarYellow: {
    backgroundColor: '#fcf6d4',
    height: 28,
    position: 'absolute',
    zIndex: 11,
    opacity: 0.7,
    marginTop: 6,
    borderColor: '#3D3D50',
    borderWidth: 0.5,
  },
  loanBarOrange: {
    backgroundColor: '#efa99c',
    height: 28,
    position: 'absolute',
    zIndex: 10,
    opacity: 0.7,
    marginTop: 6,
    borderColor: '#3D3D50',
    borderWidth: 0.5,
  },
  loanBarRed: {
    backgroundColor: '#afafaf',
    height: 28,
    position: 'absolute',
    zIndex: 9,
    opacity: 0.7,
    marginTop: 6,
    borderColor: '#3D3D50',
    borderWidth: 0.5,
  },
  loanBarWhite: {
    position: 'absolute',
    height: 39,
    width: 2,
    backgroundColor: '#FFF',
    top: -5,
    zIndex: 14,
  },
  loanBarFooter: {
    alignSelf: 'stretch',
    height: 14,
    marginLeft: 10,
    marginRight: 10,
    marginTop: 6,
    marginBottom: 12,
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
});

export default styles;
