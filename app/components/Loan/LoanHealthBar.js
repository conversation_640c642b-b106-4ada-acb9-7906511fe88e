import React from 'react'
import {View} from 'react-native'

import {TextBold, TextReg} from '../../components'

import styles from './styles'

const LoanHealthBar = props => (
  <View style={{alignSelf: 'stretch'}}>
    <View style={{flexDirection: 'row', paddingLeft: 10, marginTop: 4}}>
      <TextBold style={{fontSize: 40}}>{props.ltv}%</TextBold>
    </View>
    <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
      <View style={[styles.loanBarBox, {width: props.barWidth}]}>
        <View
          style={{
            backgroundColor: props.barColor,
            height: 34,
            width: props.ltvWidth,
            zIndex: 13,
          }}
        />
        <View style={[styles.loanBarGreen, {width: props.barWidth * 0.75}]} />
        <View style={[styles.loanBarYellow, {width: props.barWidth * 0.8333}]} />
        <View style={[styles.loanBarOrange, {width: props.barWidth * 0.9091}]} />
        <View style={[styles.loanBarRed, {width: props.barWidth}]} />
        <View
          style={[
            styles.loanBarWhite,
            {
              left: props.ltvWidth,
            },
          ]}
        />
      </View>
    </View>
    <View style={styles.loanBarFooter}>
      <TextReg style={{fontSize: 12}}>Healthy</TextReg>
      <TextReg style={{fontSize: 12}}>
        {props?.forStabilization ? 'Stabilization' : 'Liquidation'}
      </TextReg>
    </View>
  </View>
)
export default LoanHealthBar
