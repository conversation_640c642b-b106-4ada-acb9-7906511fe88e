import React, { Component } from 'react'
import { View } from 'react-native'

import { TextReg } from '../../components'

class ShowThresholds extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  render() {
    const displayThresholds = this.props.thresholds.map((a, k) => (
      <View
        key={k}
        style={{
          flexDirection: 'row',
          marginTop: 6,
          marginLeft: 10,
        }}
      >
        <View
          style={{
            height: 12,
            width: 12,
            borderRadius: 6,
            backgroundColor: a.color,
            marginTop: 2,
          }}
        />
        <View
          style={{
            width: 120,
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}
        >
          <TextReg style={{ fontSize: 12, marginLeft: 6 }}>{a.name}</TextReg>
          <TextReg style={{ fontSize: 12 }}>{a.percentage}</TextReg>
        </View>
      </View>
    ))
    return <View style={{ alignSelf: 'stretch' }}>{displayThresholds}</View>
  }
}

export default ShowThresholds
