import React from 'react'
import { View, Text, TextInput } from 'react-native'
import { Button } from '../../../components'

import commonStyles from '../../../styles/commonStyles'

import styles from '../styles'

const PurchaseRefund = (props) => (
  <View style={commonStyles.container}>
    <Text style={styles.purcahseSaltRefundParagraph}>
      In the case that we are unable to process your SALT purchase, please provide a bitcoin address to refund your
      payment.
    </Text>
    <View style={styles.purcahseSaltRefundAddressBox}>
      <Text style={styles.purcahseSaltRefundTitle}>Refund Address</Text>
      <TextInput
        onChangeText={(text) => props.updateRefundAddress(text)}
        style={styles.purchaseSaltRefundInput}
        underlineColorAndroid="transparent"
        blurOnSubmit
        value={props.refundAddress}
        multiline
        returnKeyType={'done'}
        onSubmitEditing={() => props.hideKeyboard()}
        keyboardAppearance="dark"
      />
      <Text style={styles.purchaseSaltRefundDescription}>
        Please note: Using anything other than a Bitcoin address may result in a loss of funds.
      </Text>
    </View>
    <Button
      isLoading={props.showLoading}
      style={styles.purchaseSaltRefundButton}
      onPress={props.handleRefundNext}
      disabled={!props.validRefundAddress}
    >
      Next
    </Button>
  </View>
)

export default PurchaseRefund
