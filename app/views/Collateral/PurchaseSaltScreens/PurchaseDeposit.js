import React from 'react';
import {ScrollView, View, Text, Image, TouchableOpacity} from 'react-native';

import QRCode from 'react-native-qrcode-svg';
import {Button} from '../../../components';

import commonStyles from '../../../styles/commonStyles';
import checkMarkCopied from '../../../imgs/checkMarkCopied.png';
import copyButtonImg from '../../../imgs/copyDepositButton.png';

import styles from '../styles';

const PurcahseDeposit = props => (
  <View style={commonStyles.container}>
    <ScrollView>
      <View style={styles.purchaseSaltPriceBox}>
        <Image
          source={require('../../../imgs/purchaseSaltLockPrice.png')}
          style={styles.purcahseSaltLockPrice}
        />
        <Text style={styles.purchaseSaltPriceTextTop}>1 Salt</Text>
        <Text style={styles.purchaseSaltPriceTextMiddle}>
          $ {props.saltPrice}
        </Text>
        <Text style={styles.purchaseSaltPriceTextBottom}>
          {props.saltInCrypto()} BTC
        </Text>
      </View>
      <Text style={styles.purcahseSaltTimerText}>
        Expires in {props.convertSecToMins(props.priceTimer)} min
      </Text>
      <Text style={styles.purcahseSaltRefundParagraph}>
        A 3% transaction fee will be deducted from your purchase.
      </Text>
      <View style={styles.purchaseSaltQRCodeBox}>
        <QRCode
          value={props.purchaseAddress}
          size={130}
          backgroundColor={'#000'}
          color={'#FFF'}
        />
      </View>

      <View style={styles.purcahseSaltPurchaseAddressBox}>
        <View style={styles.depositAddressBox}>
          <Text style={styles.depositAddressText}>{props.purchaseAddress}</Text>
          {props.showCopiedButton ? (
            <TouchableOpacity style={styles.depositAddressCopyButtonApplied}>
              <Image
                source={checkMarkCopied}
                style={{
                  height: 14,
                  width: 18,
                }}
              />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={styles.depositAddressCopyButton}
              onPress={() => props.copyAddress()}>
              <Image
                source={copyButtonImg}
                style={{
                  height: 20,
                  width: 20,
                }}
              />
            </TouchableOpacity>
          )}
        </View>
        <View style={styles.purchaseSaltPurchaseDescription}>
          <Text style={styles.purchaseSaltPurchaseParagraph}>
            Please Note: Sending anything other than Bitcoin to this address may
            result in a loss of funds.
          </Text>
          <Text style={styles.purchaseSaltPurchaseParagraph}>
            We do not support sending from exchanges or smart contracts. Do not
            send Bitcoin from an exchange or smart contract.
          </Text>
          <Text style={styles.purchaseSaltPurchaseParagraph}>
            If we are unable to process this purchase, the Bitcoin will be
            returned to:
          </Text>
          <Text style={styles.purchaseSaltPurchaseParagraph}>
            {props.refundAddress}
          </Text>
        </View>
      </View>
      <Button
        style={styles.purchaseSaltRefundButton}
        onPress={props.handlePurchaseDone}>
        Done
      </Button>
    </ScrollView>
  </View>
);

export default PurcahseDeposit;
