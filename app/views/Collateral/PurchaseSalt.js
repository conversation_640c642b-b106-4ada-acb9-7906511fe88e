import React, { Component } from 'react'
import { View, Keyboard, Clipboard, Animated, Text } from 'react-native'
import { connect } from 'react-redux'

import { isValidBTCAddress, updateActiveTabListener } from '../../util/helpers'

import PurchaseRefund from './PurchaseSaltScreens/PurchaseRefund'
import PurchaseDeposit from './PurchaseSaltScreens/PurchaseDeposit'

import commonStyles from '../../styles/commonStyles'
import styles from './styles'

class PurchaseSalt extends Component {
  constructor(props) {
    super(props)
    this.state = {
      step: 0,
      refundAddress: '',
      validRefundAddress: false,
      purchaseAddress: '',
      saltPrice: '',
      feeRate: 0.01,
      priceTimer: 1200,
      showCopiedButton: false,
      showLoading: false,
    }
    this.popUpValue = new Animated.Value(-60)
  }
  componentDidMount() {
    updateActiveTabListener(this.props.navigation)
  }

  updateRefundAddress = text => {
    const validRefundAddress = isValidBTCAddress(text)
    this.setState({ refundAddress: text, validRefundAddress })
  }

  handleRefundNext = () => {
    this.hideKeyboard()
    this.setState({ showLoading: true })
    this.props.WebService.saltPurchase(this.state.refundAddress)
      .then(res => {
        this.setState({
          step: 1,
          purchaseAddress: res.data.address,
          saltPrice: res.data.saltPriceUsd,
          feeRate: res.data.feeRate,
          cryptoPrice: res.data.cryptoPriceUsd,
          refundAddress: res.data.refundAddress,
          showLoading: false,
        })
        this.startPriceTimer()
      })
      .catch(err => {
        //console.log('saltPurchase err', err)
      })
  }

  getPurchaseAddress = () => {
    this.props.WebService.saltPurchase(this.state.refundAddress)
  }

  convertSecToMins = timer => {
    const mins = Math.floor(timer / 60).toFixed(0)
    let secs = timer % 60
    secs = this.padStartTwo(secs.toString())
    return `${mins}:${secs}`
  }

  padStartTwo = num => {
    if (num.length === 1) {
      return `0${num}`
    }
    return num
  }

  hideKeyboard = () => {
    Keyboard.dismiss()
  }

  saltInCrypto = () =>
    (this.state.saltPrice / this.state.cryptoPrice).toFixed(11)

  startPriceTimer = () => {
    setInterval(() => {
      if (this.state.priceTimer - 1 <= 0) {
        this.setState({ priceTimer: 0 })
      } else {
        this.setState({ priceTimer: this.state.priceTimer - 1 })
      }
    }, 1000)
  }

  copyAddress = () => {
    Clipboard.setString(this.state.purchaseAddress)
    this.setState({ showCopiedButton: true })
    Animated.timing(this.popUpValue, {
      toValue: 0,
      duration: 300,
    }).start(() => {
      setTimeout(() => {
        Animated.timing(this.popUpValue, {
          toValue: -60,
          duration: 300,
        }).start(() => {
          this.setState({ showCopiedButton: false })
        })
      }, 2000)
    })
  }

  handlePurchaseDone = () => {
    this.props.navigation.pop(1)
  }

  render() {
    return (
      <View style={commonStyles.container}>
        <Animated.View
          style={[styles.depositCopyPopup, { top: this.popUpValue }]}
        >
          <Text style={styles.depositCopyPopupText}>Copied</Text>
        </Animated.View>
        {this.state.step === 0 && (
          <PurchaseRefund
            validRefundAddress={this.state.validRefundAddress}
            updateRefundAddress={this.updateRefundAddress}
            refundAddress={this.state.refundAddress}
            handleRefundNext={this.handleRefundNext}
            hideKeyboard={this.hideKeyboard}
            showLoading={this.state.showLoading}
          />
        )}
        {this.state.step === 1 && (
          <PurchaseDeposit
            purchaseAddress={this.state.purchaseAddress}
            showCopiedButton={this.state.showCopiedButton}
            refundAddress={this.state.refundAddress}
            handlePurchaseDone={this.handlePurchaseDone}
            copyAddress={this.copyAddress}
            feeRate={this.state.feeRate}
            saltPrice={this.state.saltPrice}
            cryptoPrice={this.state.cryptoPrice}
            saltInCrypto={this.saltInCrypto}
            priceTimer={this.state.priceTimer}
            convertSecToMins={this.convertSecToMins}
          />
        )}
      </View>
    )
  }
}

PurchaseSalt.navigationOptions = props => ({
  title: 'Purchase SALT',
  headerTintColor: '#FFF',
  headerStyle: {
    backgroundColor: '#05868e',
  },
  headerBackTitle: null,
  tabBarLabel: 'Wallets',
})

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  accountRef: state.auth.account.ref,
})

export default connect(mapStateToProps)(PurchaseSalt)
