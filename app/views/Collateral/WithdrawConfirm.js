import React, { Component } from 'react'
import { Keyboard, Linking, Platform, View, Text, TextInput, TouchableOpacity } from 'react-native'
import { Button } from '../../components'
import { connect } from 'react-redux'
import styles from './styles'
import commonStyles from '../../styles/commonStyles'
import { KeyboardAwareScrollView } from '@codler/react-native-keyboard-aware-scroll-view'
import { updateActiveTabListener, dig } from '../../util/helpers'
import { screenView } from '../../store/analytics/analytics.actions'
import { BigNumber } from 'bignumber.js'

class WithdrawConfirm extends Component {
  constructor(props) {
    super(props)
    this.state = {
      twoFactor: '',
      twoFAError: false,
      withdrawProcessing: false,
      withdrawButtonActive: false,
      withdrawError: false,
      errorMessage: 'Error Submitting Request. Please check your balance and address and try again.'
    }
    this.inputs = {}
  }

  componentDidMount() {
    updateActiveTabListener(this.props.navigation)
    const { params } = this.props.route
    this.props.dispatch(screenView(`Withdraw Success - ${params.title}`))
  }

  openTotp = () => {
    Linking.openURL('totp://').catch((err) => {
      Linking.openURL('authy://app')
    })
  }

  handleCancelButton = () => {
    this.props.navigation.goBack()
  }

  handleSubmit = () => {
    const { params } = this.props.route

    this.setState({
      withdrawProcessing: true,
      withdrawButtonActive: false,
      withdrawError: false,
      twoFactorModalVisible: false,
      errorMessage: 'Error Submitting Request. Please check your balance and address and try again.'
    })

    const withdrawData = {
      amount: params.amount,
      currency: params.currency.toLowerCase(),
      toAddress: params.toAddress,
      twoFactorCode: this.state.twoFactor
    }

    this.props.WebService.withdraw(withdrawData)
      .then((res) => {
        this.setState({ withdrawButtonActive: true, withdrawProcessing: false })

        const successData = {
          params: this.props.route.params,
          amountPrice: params.amountPrice,
          ...withdrawData
        }

        this.props.navigation.navigate('WithdrawSuccess', successData)
      })

      .catch((err) => {
        if (dig(err, 'data', 'body', 'error') === 'invalid mfa code') {
          this.setState({
            withdrawButtonActive: true,
            twoFAError: true,
            withdrawProcessing: false
          })
        } else {
          if (
            err.data.body.error.includes('insufficient funds') ||
            err.data.body.error.includes('Insufficient Funds')
          ) {
            this.setState({
              errorMessage: 'After taking fees into account, amount too small for withdrawal. Please contact support.'
            })
          }
          this.setState({
            withdrawButtonActive: true,
            withdrawError: true,
            withdrawProcessing: false
          })
        }
      })
  }

  render() {
    const { params } = this.props.route
    return (
      <View style={commonStyles.container}>
        <KeyboardAwareScrollView style={styles.scrollViewContainer}>
          <View style={styles.withdrawSuccessAmountBox}>
            <Text style={styles.withdrawSuccessText}>Amount</Text>
            <View style={styles.withdrawSuccessAmountInnerBox}>
              <Text style={styles.withdrawSuccessTextBig}>{params.amount}</Text>
              <Text style={styles.withdrawSuccessTextBigDesc}>{params.currency}</Text>
            </View>
            <View style={styles.withdrawSuccessAmountInnerBox}>
              <Text style={styles.withdrawSuccessTextMed}>${new BigNumber(params.amountPrice).toFormat(2)}</Text>
              <Text style={styles.withdrawSuccessTextMedDesc}>USD</Text>
            </View>
          </View>
          <View style={styles.withdrawSuccessAddressBox}>
            <Text style={styles.withdrawSuccessText}>Destination Address</Text>
            <Text style={styles.withdrawSuccessAddressText}>{params.toAddress}</Text>
          </View>
          <Text style={styles.withdrawConfirmDescription}>
            Please input your 2-Step Verification Code to confirm your withdrawal request.
          </Text>
          <Text style={styles.withdrawAddressTitle}>2-Step Verification Code</Text>
          <TextInput
            ref={(input) => (this.inputs.twoFactor = input)}
            onChangeText={(text) =>
              this.setState({
                twoFAError: false,
                twoFactor: text,
                withdrawError: false
              })
            }
            value={this.state.twoFactor}
            underlineColorAndroid="transparent"
            returnKeyType={'done'}
            keyboardType={'numeric'}
            onSubmitEditing={() => Keyboard.dismiss()}
            style={styles.withdrawAddressInputBox}
            maxLength={6}
            keyboardAppearance="dark"
          />

          {this.state.twoFAError && (
            <View style={styles.withdrawErrorBox}>
              <Text style={styles.withdrawErrorText}>Invalid Code. Please try again.</Text>
            </View>
          )}
          {Platform.OS === 'ios' && (
            <TouchableOpacity style={styles.totpButton} onPress={() => this.openTotp()}>
              <Text style={styles.totpText}>Open Authenticator App</Text>
            </TouchableOpacity>
          )}
          {this.state.withdrawError && (
            <View style={styles.withdrawErrorBox}>
              <Text style={styles.withdrawErrorText}>{`${this.state.errorMessage}`}</Text>
            </View>
          )}
          <View style={styles.withdrawSuccessContainer}>
            <Button
              isLoading={this.state.withdrawProcessing}
              style={styles.withdrawSubmitButton}
              onPress={() => this.handleSubmit()}
            >
              Confirm Withdrawal
            </Button>
            <TouchableOpacity onPress={this.handleCancelButton}>
              <Text style={styles.cancelText}>Back</Text>
            </TouchableOpacity>
          </View>
        </KeyboardAwareScrollView>
      </View>
    )
  }
}

WithdrawConfirm.navigationOptions = ({ navigation }) => ({
  title: 'Wallets',
  headerTintColor: '#FFF',
  headerStyle: {
    backgroundColor: '#05868e',
    borderBottomWidth: 0
  },
  headerLeft: null
})

const mapStateToProps = (state) => ({
  loanData: state.user.loanData,
  WebService: state.auth.WebService
})

export default connect(mapStateToProps)(WithdrawConfirm)
