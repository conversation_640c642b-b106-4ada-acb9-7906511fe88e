import React, { Component } from 'react'
import { View, Text, Image, ScrollView } from 'react-native'
import { connect } from 'react-redux'
import styles from './styles'
import commonStyles from '../../styles/commonStyles'
import { updateActiveTabListener } from '../../util/helpers'
import { screenView } from '../../store/analytics/analytics.actions'
import { updateWalletTransactions } from '../../store/user/user.actions'
import { updateUnread } from '../../store/notifications/notifications.actions'

import { Button } from '../../components'

import checkmarkCircle from '../../imgs/checkmarkCircle.png'

class WithdrawSuccess extends Component {
  componentDidMount() {
    updateActiveTabListener(this.props.navigation)
    const { params } = this.props.route
    this.props.dispatch(screenView(`Withdraw Success - ${params.title}`))
  }

  componentWillUnmount() {
    this.props.WebService.getNotifications(1).then(res => {
      const unread = res.data.unread
      this.props.dispatch(updateUnread(unread))
    })
  }

  handleReturn = () => {
    this.props.WebService.getTransactionHistory(
      this.props.route.params.currency
    )
      .then(res => {
        this.setState({ updatedTransactions: true })
        this.props.dispatch(updateWalletTransactions(res.data))
      })
      .then(() => {
        this.props.navigation.pop(3)
      })
      .catch(err => {
        this.props.navigation.pop(3)
      })
  }

  render() {
    const { params } = this.props.route
    return (
      <View style={commonStyles.container}>
        <ScrollView>
          <View style={styles.withdrawSuccessContainer}>
            <Image
              source={checkmarkCircle}
              style={{
                height: 60,
                width: 60,
                marginTop: 30,
                marginBottom: 20,
              }}
            />
            <Text style={styles.withdrawSuccessDescription}>
              Successfully submitted withdrawal request
            </Text>
            <View style={styles.withdrawSuccessAmountBox}>
              <Text style={styles.withdrawSuccessText}>Amount</Text>
              <View style={styles.withdrawSuccessAmountInnerBox}>
                <Text style={styles.withdrawSuccessTextBig}>
                  {params.amount}
                </Text>
                <Text style={styles.withdrawSuccessTextBigDesc}>
                  {params.currency}
                </Text>
              </View>
              <View style={styles.withdrawSuccessAmountInnerBox}>
                <Text style={styles.withdrawSuccessTextMed}>
                  ${params.amountPrice}
                </Text>
                <Text style={styles.withdrawSuccessTextMedDesc}>USD</Text>
              </View>
            </View>
            <View style={styles.withdrawSuccessAddressBox}>
              <Text style={styles.withdrawSuccessText}>
                Destination Address
              </Text>
              <Text style={styles.withdrawSuccessAddressText}>
                {params.toAddress}
              </Text>
            </View>
            <Button
              style={styles.withdrawSubmitButton}
              onPress={this.handleReturn}
            >
              Return to Wallet
            </Button>
          </View>
        </ScrollView>
      </View>
    )
  }
}

WithdrawSuccess.navigationOptions = ({ navigation }) => ({
  title: 'Wallets',
  headerTintColor: '#FFF',
  headerStyle: {
    backgroundColor: '#05868e',
  },
  headerLeft: null,
  tabBarLabel: 'Wallets',
})

const mapStateToProps = state => ({
  loanData: state.user.loanData,
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(WithdrawSuccess)
