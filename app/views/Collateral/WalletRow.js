import React from 'react'
import {View, Image, TouchableOpacity} from 'react-native'

import {Card, TextReg, TextBold} from '../../components'

import {numberWithCommas} from '../../util/helpers'

import styles from './styles'

const WalletRow = props => {
  let valueOpacity = 1
  if (props.token.value === '$0.00') {
    valueOpacity = 0.5
  }
  if (props.token.value === '$0') {
    props.token.value = '$0.00'
    valueOpacity = 0.5
  }
  const titleUSD = `${props.token.title}-USD`
  let tokenPrice = Number(props.tokenPrices[titleUSD].price)
  let tokenPrice24h = Number(props.tokenPrices24h[titleUSD].price)

  if (titleUSD === 'DOGE-USD') {
    tokenPrice = tokenPrice.toFixed(5)
  } else {
    tokenPrice = tokenPrice.toFixed(2)
  }

  if (props?.token?.title == 'PREF') {
    let noDollar = props?.token?.price?.replace('$', '')?.replace(/,/g, '')
    tokenPrice = Number(noDollar)
    tokenPrice24h = tokenPrice
  }

  let difference = 0
  let differenceColor = '#00FFBD'
  let differenceOpacity = 0.7
  let showChange = true
  if (tokenPrice24h - tokenPrice < 0) {
    differenceColor = '#34e89e'
    differenceOpacity = 1
    difference = ((tokenPrice - tokenPrice24h) / tokenPrice) * 100
  }

  if (tokenPrice24h - tokenPrice > 0) {
    differenceColor = '#fb1f63'
    differenceOpacity = 1
    difference = ((tokenPrice - tokenPrice24h) / tokenPrice) * 100
  }

  tokenPrice = numberWithCommas(tokenPrice)
  difference = difference.toFixed(2)

  if (tokenPrice24h == 0) {
    // history price not coming in - do not show % change
    showChange = false
  }

  return (
    <TouchableOpacity onPress={() => props.openCollateral(props.token)}>
      <Card cardMarginBottom={6}>
        <View style={styles.walletRowBox}>
          <View style={styles.walletRowLeft}>
            <Image source={props.token.pic} style={styles.walletIcon} />
            <View style={{flexDirection: 'column', alignItems: 'flex-start'}}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'flex-end',
                }}>
                <TextReg style={{fontSize: 16}}>{props.token.fullName}</TextReg>
                <TextReg style={{fontSize: 14, opacity: 0.5, marginLeft: 6}}>
                  {props.token.title}
                </TextReg>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <TextBold style={[styles.collateralListItemPrice, {opacity: valueOpacity}]}>
                  {props.token.value}
                </TextBold>
              </View>
            </View>
          </View>
          <View style={styles.walletRowRight}>
            <TextReg style={{fontSize: 16}}>{`$${tokenPrice}`}</TextReg>
            <TextReg
              style={{
                fontSize: 13,
                color: differenceColor,
                height: 20,
                marginTop: 2,
                opacity: showChange ? differenceOpacity : 0.3,
              }}>
              {showChange ? `${difference}%` : 'x.x%'}
            </TextReg>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  )
}

export default WalletRow
