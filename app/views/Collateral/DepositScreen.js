import React, {Component} from 'react'
import {View, Image, TouchableOpacity, Animated, Clipboard, Modal, Linking} from 'react-native'
import {connect} from 'react-redux'
import QRCode from 'react-native-qrcode-svg'

import styles from './styles'
import {TextReg, TextBold, Background} from '../../components'

import {cryptoNameMap} from '../../util/enumerables'
import {screenView, sendEvent} from '../../store/analytics/analytics.actions'
import {updateUnread} from '../../store/notifications/notifications.actions'
import closeWithdrawImg from '../../imgs/notifClose.png'
import checkMarkCopied from '../../imgs/checkMarkCopied.png'

class DepositScreen extends Component {
  constructor(props) {
    super(props)
    this.state = {
      showCopiedButton: false,
    }
    this.popUpValue = new Animated.Value(-60)
  }

  componentDidMount() {
    const {title} = this.props
    this.props.dispatch(screenView(`Collateral Deposit - ${title}`))
  }

  componentWillUnmount() {
    this.props.WebService.getNotifications(1).then(res => {
      const unread = res.data.unread
      this.props.dispatch(updateUnread(unread))
    })
  }

  copyAddress = (address, title) => {
    this.props.dispatch(sendEvent(`Copy Depoit Address - ${title}`))

    Clipboard.setString(address)
    this.setState({showCopiedButton: true})
  }

  openReserveLink = () => {
    Linking.openURL(
      'https://saltlending.zendesk.com/hc/en-us/articles/360039288114-Why-does-it-cost-26-XRP-to-create-a-Ripple-wallet-on-the-SALT-platform-',
    )
  }

  render() {
    let {title} = this.props
    if (!title) {
      title = this.props.route.params.title
    }
    const collateralChoice = title
    const collateralData = this.props.loanData.collaterals.filter(
      a => a?.currency === collateralChoice,
    )[0]
    const currencyType = cryptoNameMap.get(collateralData?.currency.toLowerCase())

    let showEthWarning = false
    if (['ETH', 'SALT', 'USDT', 'USDP', 'USDC'].includes(collateralData?.currency)) {
      showEthWarning = true
    }

    return (
      <Modal
        animationType="slide"
        visible={this.props.showDeposit && !this.props.showPinScreen}
        onRequestClose={() => ({})}>
        <Background />

        <View style={styles.DepositBox}>
          <View style={{width: 24, marginLeft: 18}} />
          {this.props.xrpInit ? (
            <TextReg style={{color: '#FFF', fontSize: 24}}>Reserve</TextReg>
          ) : (
            <TextReg style={{color: '#FFF', fontSize: 24}}>
              Deposit {cryptoNameMap.get(title.toLowerCase())}{' '}
            </TextReg>
          )}
          <TouchableOpacity
            onPress={() => {
              this.props.closeDeposit()
              this.setState({showCopiedButton: false})
            }}>
            <Image source={closeWithdrawImg} style={styles.DepositImg} />
          </TouchableOpacity>
        </View>

        {this.props.xrpInit ? (
          <View
            style={{
              flexDirection: 'column',
              alignSelf: 'stretch',
              justifyContent: 'center',
              alignItems: 'center',
              marginTop: 20,
            }}>
            <TextReg
              style={{
                color: '#FFF',
                width: 330,
                marginBottom: 26,
                textAlign: 'center',
                fontSize: 22,
              }}>
              To setup a XRP wallet, a reserve deposit of{' '}
              <TextReg style={{color: '#00ffc3'}}>26 XRP</TextReg> is needed
            </TextReg>

            <TextReg
              style={{
                color: '#FFF',
                width: 340,
                marginBottom: 32,
                textAlign: 'center',
                fontSize: 17,
              }}>
              (Reserve: <TextReg style={{color: '#00ffc3'}}>20 XRP</TextReg>, Multisig:{' '}
              <TextReg style={{color: '#00ffc3'}}>5 XRP</TextReg>, Fees:{' '}
              <TextReg style={{color: '#00ffc3'}}>1 XRP</TextReg>)
            </TextReg>

            <TouchableOpacity onPress={() => this.openReserveLink()}>
              <TextReg
                style={{
                  color: '#9C84F1',
                  width: 340,
                  marginBottom: 30,
                  textAlign: 'center',
                  fontSize: 17,
                }}>
                WHY DO WE NEED RESERVE?
              </TextReg>
            </TouchableOpacity>

            <View style={styles.depositQrBox}>
              <QRCode
                value={collateralData?.address || ' '}
                size={130}
                backgroundColor={'black'}
                color={'white'}
              />
            </View>

            <TextReg style={styles.depositWalletAddressText}>Wallet Address:</TextReg>

            <TextBold style={styles.depositAddressTextXrp}>{collateralData?.address}</TextBold>

            {this.state.showCopiedButton ? (
              <TouchableOpacity style={[styles.depositAddressCopyButtonApplied, {marginTop: 30}]}>
                <View style={styles.depositCopiedApplied}>
                  <Image source={checkMarkCopied} style={{height: 14, width: 18}} />
                  <TextBold style={{fontSize: 20, color: '#FFF', marginLeft: 20}}>
                    Address Copied
                  </TextBold>
                </View>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={[styles.depositAddressCopyButton, {height: 50, width: 260, marginTop: 30}]}
                onPress={() => this.copyAddress(collateralData?.address, title)}>
                <View style={[styles.depositCopyAddress, {height: 50, width: 260}]}>
                  <TextBold style={{fontSize: 20, color: '#008487'}}>Copy Reserve Address</TextBold>
                </View>
              </TouchableOpacity>
            )}
          </View>
        ) : (
          <View style={styles.depositDetailHeader}>
            <View style={styles.depositQrBox}>
              <QRCode
                value={collateralData?.address || ' '}
                size={130}
                backgroundColor={'black'}
                color={'white'}
              />
            </View>

            <TextReg style={styles.depositWalletAddressText}>Wallet Address:</TextReg>

            <TextBold style={styles.depositAddressText}>{collateralData?.address}</TextBold>

            {this.state.showCopiedButton ? (
              <TouchableOpacity style={styles.depositAddressCopyButtonApplied}>
                <View style={styles.depositCopiedApplied}>
                  <Image source={checkMarkCopied} style={{height: 14, width: 18}} />
                  <TextBold style={{fontSize: 20, color: '#FFF', marginLeft: 20}}>
                    Address Copied
                  </TextBold>
                </View>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={[styles.depositAddressCopyButton, {height: 50, width: 260}]}
                onPress={() => this.copyAddress(collateralData.address, title)}>
                <View style={[styles.depositCopyAddress, {height: 50, width: 260}]}>
                  <TextBold style={{fontSize: 20, color: '#000'}}>Copy Address</TextBold>
                </View>
              </TouchableOpacity>
            )}
            <View style={styles.collateralDisclaimerBox}>
              <TextReg style={[styles.depositDisclaimer, {marginBottom: 0}]}>
                This usually takes around a minute. We will notify you when the transation has been
                initiated and completed.
              </TextReg>
            </View>
            {showEthWarning && (
              <View style={styles.collateralDisclaimerBox}>
                <TextReg
                  style={[
                    styles.depositDisclaimer,
                    {marginBottom: 2, color: '#E5705A', marginLeft: -4},
                  ]}>
                  {`Please Note: This is an address on the Ethereum mainnet.  Sending anything other than ${collateralData?.currency} to this address may result in a loss of funds. ${collateralData?.currency} sent on other networks will not be recognized and may be unrecoverable.`}
                </TextReg>
              </View>
            )}
            {!showEthWarning && (
              <View style={styles.collateralDisclaimerBox}>
                <TextReg
                  style={[
                    styles.depositDisclaimer,
                    {marginBottom: 2, color: '#E5705A', marginLeft: -4},
                  ]}>
                  {`Please note: Sending anything other than ${collateralData?.currency} to this address may result in a loss of funds.`}
                </TextReg>
              </View>
            )}
          </View>
        )}
      </Modal>
    )
  }
}

const mapStateToProps = state => ({
  loanData: state.user.loanData,
  WebService: state.auth.WebService,
  showPinScreen: state.auth.pinScreen,
})

export default connect(mapStateToProps)(DepositScreen)
