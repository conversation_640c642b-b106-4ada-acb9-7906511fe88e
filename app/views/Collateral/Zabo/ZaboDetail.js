import React, { Component } from 'react'
import { View, ScrollView, Image, RefreshControl, TouchableOpacity, StatusBar } from 'react-native'
import { connect } from 'react-redux'
import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'
import { BackgroundHeader, TextReg, TextBold, Button } from '../../../components'
import { numberWithCommas } from '../../../util/helpers'
import LottieView from 'lottie-react-native'

import downArrow from '../../../imgs/zabo/downArrow.png'
import upArrow from '../../../imgs/zabo/upArrow.png'
import tradeArrow from '../../../imgs/zabo/tradeArrow.png'
import zaboDefault from '../../../imgs/zabo/zaboDefault.png'

class ZaboDetail extends Component {
  constructor() {
    super()
    this.state = {
      txs: [],
      txLoading: true,
      showSmallCap: false
    }
  }

  componentDidMount() {
    const zaboWallet = this.props.route.params?.wallet
    const id = zaboWallet.id

    this.props.WebService.getZaboTx(id)
      .then((res) => {
        this.setState({ txs: res.data, txLoading: false })
      })
      .catch((err) => {
        console.log('zabo wallets err', err)
        this.setState({ txs: [], txLoading: false })
      })
  }

  sortByValue = (a, b) => {
    if (a.fiat_value == '') {
      return 1
    }
    const aValue = Number(a.fiat_value)
    const bValue = Number(b.fiat_value)
    if (aValue > bValue) {
      return -1
    }
    if (aValue < bValue) {
      return 1
    }
    return 0
  }

  toggleShowSmallCap = () => {
    this.setState({ showSmallCap: !this.state.showSmallCap })
  }

  render() {
    const zaboWallet = this.props.route.params?.wallet
    const walletName = zaboWallet?.provider?.name
    let total = 0
    zaboWallet.balances.map((b) => {
      total += Number(b.fiat_value)
    })
    total = numberWithCommas(total.toFixed(2))

    const showTxs = this.state.txs.map((a, k) => {
      //console.log('txs a', a)
      let amount = Number(a.parts[0].fiat_value)
      amount = numberWithCommas(amount)
      let arrow = downArrow
      if (a?.transaction_type == 'withdrawal') {
        arrow = upArrow
      }
      if (a?.transaction_type == 'trade') {
        arrow = tradeArrow
      }
      return (
        <View
          key={k}
          style={{
            alignSelf: 'stretch',
            flexDirection: 'row',
            height: 44,
            alignItems: 'center',
            justifyContent: 'space-between',
            marginLeft: 20,
            marginRight: 20,
            paddingLeft: 10,
            paddingRight: 10,
            borderBottomWidth: 1,
            borderColor: '#777'
          }}
          onPress={() => this.gotoZaboDetail(a)}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Image
              source={arrow}
              style={{
                height: 20,
                width: 16,
                marginRight: 14
              }}
            />
            <TextReg style={{ color: '#FFF', fontSize: 16 }}>{a?.transaction_type}</TextReg>
          </View>
          <TextReg style={{ color: '#FFF', fontSize: 16 }}>${amount}</TextReg>
        </View>
      )
    })

    zaboWallet.balances.sort(this.sortByValue)

    let anySmallCap = false
    const showBalances = zaboWallet.balances.map((a, k) => {
      if (a.fiat_value == '') {
        anySmallCap = true
        if (!this.state.showSmallCap) {
          return null
        }
      }
      const formattedFiat = numberWithCommas(a.fiat_value)

      const tokenLogo = a.logo || null

      let formattedAmount = Number(a.amount).toFixed(8)

      if (formattedAmount > 100000000000000) {
        formattedAmount = Number.parseFloat(formattedAmount).toExponential(5)
      } else {
        formattedAmount = parseFloat(formattedAmount)
      }

      return (
        <View key={k} style={styles.zaboDetailRow}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            {tokenLogo ? (
              <Image source={{ uri: tokenLogo }} style={styles.zaboDetailLogo} />
            ) : (
              <Image source={zaboDefault} style={styles.zaboDetailLogo} />
            )}
            <View>
              <TextReg style={{ color: '#FFF', fontSize: 17 }}>{a.ticker}</TextReg>
              <TextReg style={{ color: '#AFAFAF', fontSize: 14 }}>{a.ticker}</TextReg>
            </View>
          </View>
          <View style={{ alignItems: 'flex-end' }}>
            <TextReg style={{ color: '#FFF', fontSize: 17 }}>{`$${formattedFiat}`}</TextReg>
            <TextReg style={{ color: '#AFAFAF', fontSize: 14 }}>{formattedAmount}</TextReg>
          </View>
        </View>
      )
    })

    const formattedName = walletName.charAt(0).toUpperCase() + walletName.slice(1)

    return (
      <View style={commonStyles.tileContainerDark}>
        <StatusBar barStyle="light-content" />
        <BackgroundHeader style={{ height: 300 }} title={formattedName} goBack={this.props.navigation.goBack} />
        <TextBold
          style={{
            fontSize: 42,
            marginTop: 2,
            color: '#fff',
            marginBottom: 10
          }}
        >{`$${total}`}</TextBold>
        <TextReg style={{ fontSize: 15, marginBottom: 26, color: '#fff' }}>
          *cannot be used as collateral or earn interest
        </TextReg>
        <ScrollView
          style={styles.swiperContainer}
          contentContainerStyle={{ alignItems: 'center' }}
          refreshControl={
            <RefreshControl
              refreshing={this.state.refreshing}
              onRefresh={this.getWalletData}
              colors={['#28283D']}
              tintColor={'#fff'}
            />
          }
        >
          {showBalances}
          {anySmallCap && (
            <TouchableOpacity onPress={() => this.toggleShowSmallCap()} style={styles.zaboDetailRow}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Image source={zaboDefault} style={styles.zaboDetailLogo} />
                <View>
                  <TextReg style={{ color: '#FFF', fontSize: 17 }}>
                    {this.state.showSmallCap ? 'Hide small cap tokens' : 'Show small cap tokens'}
                  </TextReg>
                </View>
              </View>
            </TouchableOpacity>
          )}
          <View
            style={{
              alignSelf: 'stretch',
              marginLeft: 10,
              marginRight: 10,
              marginTop: 12,
              backgroundColor: '#48485A',
              borderRadius: 14,
              marginBottom: 30
            }}
          >
            <TextReg
              style={{
                color: '#FFF',
                fontSize: 20,
                marginBottom: 10,
                marginLeft: 14,
                marginTop: 14
              }}
            >
              Transaction History
            </TextReg>
            {this.state.txLoading && (
              <View style={{ alignSelf: 'stretch', alignItems: 'center' }}>
                <LottieView
                  ref={(animation) => {
                    this.animation = animation
                  }}
                  style={{
                    width: 40,
                    height: 40,
                    opacity: 0.9,
                    marginTop: 0,
                    marginBottom: 8
                  }}
                  source={require('../../../imgs/lotti/loading-white-dots.json')}
                  autoPlay
                />
              </View>
            )}
            {showTxs}
          </View>
        </ScrollView>
      </View>
    )
  }
}

ZaboDetail.navigationOptions = ({ navigation }) => ({
  title: null,
  header: <View style={{ height: 40, position: 'absolute' }} />
})

const mapStateToProps = (state) => ({
  WebService: state.auth.WebService
})

export default connect(mapStateToProps)(ZaboDetail)
