import React, { Component } from 'react'
import { View, Dimensions, TouchableOpacity } from 'react-native'
import { connect } from 'react-redux'

import { WebView } from 'react-native-webview'

import config from '../../../config.json'

import { BackgroundHeader, TextReg } from '../../../components'
import { sendEvent } from '../../../store/analytics/analytics.actions'

const { width: ScreenWidth, height: ScreenHeight } = Dimensions.get('window')

import commonStyles from '../../../styles/commonStyles'

class ZaboAddWallet extends Component {
  constructor(props) {
    super(props)
    this.state = {
      zaboWallets: [],
      webViewHeight: 300,
    }
  }
  componentDidMount() {
    this.props.dispatch(sendEvent(`Zabo Add Wallet`))
  }

  find_dimesions = layout => {
    this.setState({ webViewHeight: layout.height })
  }

  render() {
    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader
          title={'Connect Account'}
          goBack={() => this.props.navigation.goBack()}
        />

        <View
          style={{
            width: ScreenWidth,
            flex: 1,
            position: 'relative',
            overflow: 'hidden',
            alignItems: 'center',
          }}
          onLayout={event => {
            this.find_dimesions(event.nativeEvent.layout)
          }}
        >
          <TextReg
            style={{
              marginBottom: 16,
              marginTop: 16,
              textAlign: 'center',
              width: 300,
            }}
          >
            SALT recommends using read-only api keys for connecting external
            accounts
          </TextReg>
          <WebView
            source={{
              uri: config.zabo.uri,
            }}
            style={{ height: this.state.webViewHeight, width: ScreenWidth }}
            scalesPageToFit
            useWebKit={false}
          />
          <TouchableOpacity
            onPress={() => this.props.navigation.goBack()}
            style={{
              top: 70,
              height: 38,
              right: 0,
              width: 30,
              zIndex: 50,
              position: 'absolute',
            }}
          />
        </View>
      </View>
    )
  }
}

/*
<TextReg
  style={{
    marginBottom: 16,
    marginTop: 20,
    textAlign: 'center',
    width: 300,
  }}
>
  SALT recommends using read-only api keys for connecting external
  accounts
</TextReg>
*/

/*
https://connect.zabo.com/connect?client_id=eodnwAdM3wFuDwTex4c511jJmjQlsUdYU8hG5HqGCQr73ZqRcvp3CAXciJzi7YS5&origin=localhost&zabo_env=sandbox&zabo_version=latest
*/

/*
https://connect.zabo.com/connect?client_id=aamFokCm3HmSCmsC9ZoFvgbEcHo6OwBZZCwHexKMFBwutFfPFzcnNAaJXtnRdDMc&origin=borrower.saltlending.com&zabo_env=live&zabo_version=latest
*/

ZaboAddWallet.navigationOptions = ({ navigation }) => ({
  title: 'Info',
  header: <View style={{ height: 40, position: 'absolute' }} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  tokenPrices: state.user.prices,
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(ZaboAddWallet)
