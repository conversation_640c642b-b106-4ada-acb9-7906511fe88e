import React from 'react'
import {
  Modal,
  View,
  ScrollView,
  Image,
  TouchableOpacity,
  Dimensions,
} from 'react-native'

const {height: ScreenHeight} = Dimensions.get('window')

import {Button} from '../../../components'

import styles from '../styles'

const TransferModal = props => (
  <Modal
    animationType="slide"
    transparent
    visible={props.showTransfer && !props.showPinScreen}
    onRequestClose={() => props.closeTransfer()}>
    <View
      style={{
        flex: 1,
        justifyContent: 'flex-end',
        alignItems: 'center',
        backgroundColor: 'rgba(74, 74, 74, 0.5)',
      }}>
      <View
        style={{
          height: ScreenHeight - 100,
          alignSelf: 'stretch',
          backgroundColor: '#FFF',
          borderTopLeftRadius: 8,
          borderTopRightRadius: 8,
        }}>
        <View style={{height: 40, alignSelf: 'stretch'}}>
          <TouchableOpacity
            style={styles.helpModalX}
            onPress={() => {
              props.closeTransfer()
            }}>
            <Image
              source={require('../../../imgs/closeX.png')}
              style={styles.closeModalImg}
            />
          </TouchableOpacity>
        </View>
        <ScrollView style={{}}>{props.showDepositAssets}</ScrollView>
      </View>
    </View>
  </Modal>
)

export default TransferModal
