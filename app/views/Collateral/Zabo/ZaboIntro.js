import React from 'react'
import {
  Modal,
  View,
  ScrollView,
  Image,
  TouchableOpacity,
  Dimensions,
} from 'react-native'

const { height: ScreenHeight } = Dimensions.get('window')

import { Button, TextReg, TextBold } from '../../../components'

import ToggleWallet from '../ToggleWallet'

import styles from '../styles'

const ZaboIntro = props => (
  <View
    style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      flex: 1,
      zIndex: 20,
      alignItems: 'center',
    }}
  >
    <View
      style={{
        height: 150,
        alignSelf: 'stretch',
        backgroundColor: 'rgba(74, 74, 74, 0.5)',
        justifyContent: 'flex-end',
        alignItems: 'center',
        paddingBottom: 6,
      }}
    >
      <ToggleWallet
        walletType={props.walletType}
        toggleWallet={props.toggleWalletIntro}
      />
    </View>
    <TouchableOpacity
      activeOpacity={1}
      onPress={() => props.toggleWalletIntro()}
      style={{
        flex: 1,
        alignSelf: 'stretch',
        backgroundColor: 'rgba(74, 74, 74, 0.5)',
      }}
    >
      <View
        style={{
          backgroundColor: '#00FFBD',
          alignSelf: 'stretch',
          marginLeft: 20,
          marginRight: 20,
          borderRadius: 14,
        }}
      >
        <View style={{ flexDirection: 'row' }}>
          <View style={{ flex: 1, justifyContent: 'center' }} />
          <View
            style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}
          >
            <View
              style={{
                height: 24,
                width: 24,
                backgroundColor: '#00FFBD',
                marginTop: -15,
                marginBottom: -15,
                marginLeft: -40,
                zIndex: 21,
                transform: [{ rotate: '45deg' }],
              }}
            />
          </View>
        </View>

        <TextBold
          style={{
            color: '#FFF',
            fontSize: 18,
            marginTop: 20,
            marginLeft: 20,
            marginRight: 20,
            zIndex: 22,
          }}
        >
          Simplify your Finances
        </TextBold>
        <TextReg
          style={{
            color: '#FFF',
            fontSize: 18,
            marginTop: 10,
            marginBottom: 20,
            marginLeft: 20,
            marginRight: 20,
          }}
        >
          View all of your crypto assets in one place by linking them to SALT
          platform
        </TextReg>
      </View>
    </TouchableOpacity>
  </View>
)

export default ZaboIntro
