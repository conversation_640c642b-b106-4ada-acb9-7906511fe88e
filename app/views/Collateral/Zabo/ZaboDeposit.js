import React, {Component} from 'react';
import {View, Image, TouchableOpacity, Animated, Clipboard} from 'react-native';
import {connect} from 'react-redux';
import QRCode from 'react-native-qrcode-svg';

import styles from '../styles';
import {TextReg, TextBold, BackgroundHeader} from '../../../components';
import {cryptoNameMap} from '../../../util/enumerables';
import commonStyles from '../../../styles/commonStyles';

import {
  screenView,
  sendEvent,
} from '../../../store/analytics/analytics.actions';
//import { updateUnread } from '../../../store/notifications/notifications.actions'
import closeWithdrawImg from '../../../imgs/notifClose.png';
import checkMarkCopied from '../../../imgs/checkMarkCopied.png';

class ZaboDeposit extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showCopiedButton: false,
    };
    this.popUpValue = new Animated.Value(-60);
  }

  componentDidMount() {
    const title = this.props.route.params.ticker;
    this.props.dispatch(sendEvent(`Zabo Deposit - ${title}`));
  }

  componentWillUnmount() {
    /*
    this.props.WebService.getNotifications(1).then(res => {
      const unread = res.data.unread
      this.props.dispatch(updateUnread(unread))
    })
    */
  }

  copyAddress = (address, title) => {
    //this.props.dispatch(sendEvent(`Copy Depoit Address - ${title}`))

    Clipboard.setString(address);
    this.setState({showCopiedButton: true});
  };

  /*
  openReserveLink = () => {
    Linking.openURL(
      'https://saltlending.zendesk.com/hc/en-us/articles/360039288114-Why-does-it-cost-26-XRP-to-create-a-Ripple-wallet-on-the-SALT-platform-'
    )
  }
  */

  render() {
    const title = this.props.route.params.ticker;

    const collateralChoice = title;
    const collateralData = this.props.loanData.collaterals.filter(
      a => a.currency === collateralChoice,
    )[0];

    const fullName = cryptoNameMap.get(title.toLowerCase());
    /*
    const currencyType = cryptoNameMap.get(
      collateralData.currency.toLowerCase()
    )
    */

    return (
      <View style={commonStyles.tileContainerDark}>
        <BackgroundHeader
          title={`Deposit ${fullName}`}
          goBack={() => this.props.navigation.goBack()}
        />

        <View style={styles.depositDetailHeader}>
          <View style={styles.depositQrBox}>
            <QRCode
              value={collateralData.address}
              size={130}
              backgroundColor={'black'}
              color={'white'}
            />
          </View>

          <TextReg style={styles.depositWalletAddressText}>
            Salt Wallet Address:
          </TextReg>

          <TextBold style={styles.depositAddressText}>
            {collateralData.address}
          </TextBold>

          {this.state.showCopiedButton ? (
            <TouchableOpacity style={styles.depositAddressCopyButtonApplied}>
              <View style={styles.depositCopiedApplied}>
                <Image
                  source={checkMarkCopied}
                  style={{height: 14, width: 18}}
                />
                <TextBold style={{fontSize: 20, color: '#FFF', marginLeft: 20}}>
                  Address Copied
                </TextBold>
              </View>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[
                styles.depositAddressCopyButton,
                {height: 50, width: 260},
              ]}
              onPress={() => this.copyAddress(collateralData.address, title)}>
              <View
                style={[styles.depositCopyAddress, {height: 50, width: 260}]}>
                <TextBold style={{fontSize: 20, color: '#008487'}}>
                  Copy Address
                </TextBold>
              </View>
            </TouchableOpacity>
          )}
          <View style={styles.collateralDisclaimerBox}>
            <TextReg style={[styles.depositDisclaimer, {marginBottom: 12}]}>
              This usually takes around a minute. We will notify you when the
              transation has been initiated and completed.
            </TextReg>
          </View>
        </View>
      </View>
    );
  }
}

ZaboDeposit.navigationOptions = ({navigation}) => ({
  title: 'ZaboDeposit',
  header: <View style={{height: 40, position: 'absolute'}} />,
});

const mapStateToProps = state => ({
  loanData: state.user.loanData,
  WebService: state.auth.WebService,
  showPinScreen: state.auth.pinScreen,
});

export default connect(mapStateToProps)(ZaboDeposit);
