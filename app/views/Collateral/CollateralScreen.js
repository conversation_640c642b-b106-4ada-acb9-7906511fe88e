import React, {Component} from 'react'
import {View, Text, ScrollView, StatusBar, RefreshControl} from 'react-native'
import {connect} from 'react-redux'
import AsyncStorage from '@react-native-async-storage/async-storage'

import commonStyles from '../../styles/commonStyles'
import styles from './styles'
import {BackgroundHeader, TextReg} from '../../components'
import {
  updateActiveTabListener,
  numberWithCommas,
  listPossibleWallets,
  collateralMarketCapSort,
} from '../../util/helpers'
import {getTokenPic} from '../../util/tokens'
import {screenView} from '../../store/analytics/analytics.actions'
import {updateUpholdHide, updateAllWallets} from '../../store/user/user.actions'

import {showNotifications} from '../../store/notifications/notifications.actions'
import {cryptoNameMap} from '../../util/enumerables'

import TotalCollateralValue from './TotalCollateralValue'
import WalletRow from './WalletRow'
import {log} from '@grpc/grpc-js/build/src/logging'

class CollateralScreen extends Component {
  constructor() {
    super()
    this.state = {
      refreshing: true,
      walletType: 'platform',
      zaboWallets: [],
      zaboLoading: false,
      showZaboIntro: false,
    }
  }

  componentDidMount() {
    updateActiveTabListener(this.props.navigation)
    this.props.dispatch(screenView('Collateral Screen'))
    this.setState({refreshing: false})
    //this.getZaboFirstTime()
    this.getAsyncUpholdPrefs()
    //this.getZaboWallets()
  }

  getZaboFirstTime = () => {
    AsyncStorage.getItem(`ZABO_INTRO_COMPLETE`).then(res => {
      if (res != 'true') {
        this.setState({showZaboIntro: true})
      }
    })
  }

  getAsyncUpholdPrefs = () => {
    AsyncStorage.getItem(`UpholdHide-${this.props.user.primaryEmail}`).then(res => {
      let upholdHideTokens = this.props.loanData.collaterals.map(a => {
        if (res && res.includes(a.currency)) {
          return a.currency
        }
        return null
      })

      upholdHideTokens = upholdHideTokens.filter(a => a)
      this.props.dispatch(updateUpholdHide(upholdHideTokens))
    })
  }

  getWalletData = async () => {
    this.setState({refreshing: true})

    let accountArr = this.props.user?.accounts || []
    let walletsRes = []

    await Promise.all(
      accountArr.map(async a => {
        await this.props.WebService.getWallets(a.ref).then(res => {
          //walletsRes.push(res.data);
          walletsRes[a.ref - 1] = res.data
          return true
        })
      }),
    )
    console.log('walletsRes', walletsRes)
    this.props.dispatch(updateAllWallets(walletsRes))
    this.setState({refreshing: false})
  }

  openCollateral = item => {
    let productRef = this.props?.route?.params?.ref
    item = {
      ...item,
      hideUphold: this.props.upholdHideTokens,
      productRef,
    }

    this.props.navigation.navigate('Detail', item)
  }

  toggleNotifications = () => {
    this.props.dispatch(showNotifications(true))
  }

  /*
	toggleWallet = (type) => {
	  this.setState({ walletType: type });
	}

	toggleWalletIntro = (type = this.state.walletType) => {
	  AsyncStorage.setItem('ZABO_INTRO_COMPLETE', 'true');
	  this.setState({ showZaboIntro: false, walletType: type });
	}
  */

  getZaboWallets = () => {
    console.log('getZaboWallets')
    this.setState({zaboLoading: true})
    this.props.WebService.getZaboWallets()
      .then(res => {
        this.setState({zaboWallets: res.data, zaboLoading: false})
      })
      .catch(err => {
        console.log('zabo wallets err', err)
        this.setState({zaboLoading: false})
      })
  }

  render() {
    let {user} = this.props
    let productRef = this.props?.route?.params?.ref
    let collaterals = user?.allWallets[productRef - 1]

    console.log('collateral - launchDarkly', this.props.launchDarkly)
    let launchDarkly = this.props.launchDarkly

    let banned = {
      BTC: launchDarkly['disable-btc-deposit'] || false,
      ETH: launchDarkly['disable-eth-deposit'] || false,
      BCH: launchDarkly['disable-bch-deposit'] || false,
      LTC: launchDarkly['disable-ltc-deposit'] || false,
      USDC: launchDarkly['disable-usdc-deposit'] || false,
      USDT: launchDarkly['disable-usdt-deposit'] || false,
      USDP: launchDarkly['disable-usdp-deposit'] || false,
      SALT: launchDarkly['disable-salt-deposit'] || false,
      TUSD: launchDarkly['disable-tusd-deposit'] || false,
      XRP: launchDarkly['disable-xrp-deposit'] || true,
      DASH: launchDarkly['disable-dash-deposit'] || true,
      DOGE: launchDarkly['disable-doge-deposit'] || true,
      PAXG: launchDarkly['disable-paxg-deposit'] || true,
    }

    /*
    const loanData = this.props.user?.accounts.filter(
      a => a.ref == productRef,
    )[0]?.loans[0];
    console.log('loanData', loanData);
    */
    //const collaterals = loanData?.collaterals || [];
    //console.log('wallets', wallets);

    let collateralValueCalc = 0
    collaterals.map(a => {
      collateralValueCalc += Number(a.value)
    })
    const displayCollateralValue = numberWithCommas(Number(collateralValueCalc).toFixed(2))

    let activeCurrencys = collaterals.map(a => a.currency)

    //include all

    let possibleWallets = listPossibleWallets()
    activeCurrencys.map(a => {
      possibleWallets = possibleWallets.filter(b => b != a)
    })
    activeCurrencys = activeCurrencys.concat(possibleWallets)

    let collataralInfoData = activeCurrencys.map((a, k) => {
      const returnObj = {
        title: a,
        price: `$0`,
        value: `$0`,
        key: `${k}`,
        balance: `0`,
        pic: getTokenPic(a),
        fullName: cryptoNameMap.get(a.toLowerCase()),
      }

      const hasCollateralData = collaterals.filter(b => b.currency === a)[0]

      if (hasCollateralData) {
        const formattedValue = Number(hasCollateralData.value).toFixed(2)
        const displayFormattedValue = numberWithCommas(formattedValue)

        returnObj.value = `$${displayFormattedValue}`
        returnObj.price = `$${hasCollateralData.price}`
        returnObj.balance = hasCollateralData.projectedBalance
      }
      return returnObj
    })

    //filter out balance 0 of dash doge xrp
    collataralInfoData = collataralInfoData.filter(a => {
      if (a.balance > 0) {
        return a
      }
      /*
      if (a.title != 'XRP' && a.title != 'DASH' && a.title != 'DOGE' && a.title != 'PAXG') {
        return a
      }
      */
      if (!banned[a.title]) {
        return a
      }
    })

    console.log('collataralInfoData', collataralInfoData)

    let collataralInfoDataWithBalance = collataralInfoData.filter(a => a.balance > 0)

    collataralInfoDataWithBalance = collataralInfoDataWithBalance.sort(collateralMarketCapSort)

    collataralInfoDataWithBalance = collataralInfoDataWithBalance.sort((a, b) => {
      let aValue = a.value?.substring(1).replace(',', '')
      let bValue = b.value?.substring(1).replace(',', '')
      return Number(bValue) - Number(aValue)
    })

    let collataralInfoDataWithoutBalance = collataralInfoData.filter(
      a => a.balance === '0' || a.balance < 0,
    )

    collataralInfoDataWithoutBalance =
      collataralInfoDataWithoutBalance.sort(collateralMarketCapSort)

    collataralInfoData = collataralInfoDataWithBalance.concat(collataralInfoDataWithoutBalance)

    const showWallet = collataralInfoData.map((a, k) => (
      <WalletRow
        key={k}
        token={a}
        openCollateral={this.openCollateral}
        tokenPrices={this.props.tokenPrices}
        tokenPrices24h={this.props.tokenPrices24h}
        hideUphold={this.state.upholdHideTokens}
      />
    ))

    const walletMainContent = (
      <React.Fragment>
        <TotalCollateralValue displayCollateralValue={displayCollateralValue} />
        {showWallet}
        <View style={styles.collateralDisclaimerBox}>
          <Text style={styles.collateralDisclaimer}>
            Total Collateral Value is provided for representative purposes only. Loan information
            may be delayed, inaccurate, or stale. Please refer to Borrower Portal.
          </Text>
        </View>
        <View style={{height: 160}} />
      </React.Fragment>
    )

    return (
      <View style={commonStyles.tileContainer}>
        <StatusBar barStyle="light-content" />
        <BackgroundHeader
          style={{height: 300}}
          notification
          title={'Wallets'}
          toggleNotifications={this.toggleNotifications}
          goBack={() => this.props.navigation.goBack()}
        />

        <ScrollView
          style={styles.swiperContainer}
          contentContainerStyle={{alignItems: 'center'}}
          refreshControl={
            <RefreshControl
              refreshing={this.state.refreshing}
              onRefresh={this.getWalletData}
              colors={['#28283D']}
              tintColor={'#fff'}
            />
          }>
          {walletMainContent}
        </ScrollView>
      </View>
    )
  }
}

CollateralScreen.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  loanData: state.user.loanData,
  tokenPrices: state.user.prices,
  tokenPrices24h: state.user.prices24h,
  accountRef: state.auth.account.ref,
  user: state.user.user,
  upholdHideTokens: state.user.upholdHide,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(CollateralScreen)
