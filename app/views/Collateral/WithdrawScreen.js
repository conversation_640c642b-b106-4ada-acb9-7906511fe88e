import React, {Component} from 'react'
import {
  View,
  Image,
  TouchableOpacity,
  Clipboard,
  TextInput,
  Keyboard,
  AppState,
} from 'react-native'
import {connect} from 'react-redux'
import commonStyles from '../../styles/commonStyles'
import styles from './styles'
import {
  dig,
  isValidETHAddress,
  isValidBTCAddress,
  isValidLTCAddress,
  isValidDOGEAddress,
  isValidDASHAddress,
  updateActiveTabListener,
} from '../../util/helpers'
import {cryptoNameMap} from '../../util/enumerables'
import {askingForPermissions} from '../../store/auth/auth.actions'
import {screenView} from '../../store/analytics/analytics.actions'
import switchPng from '../../imgs/switch.png'
import qRcodeScanner from '../../imgs/qRCodeScanner.png'
import WithdrawQRScanner from './Withdraw/WithdrawQRScanner'
import {<PERSON><PERSON>, TextReg} from '../../components'
import {BigNumber} from 'bignumber.js'

class WithdrawScreen extends Component {
  constructor(props) {
    super(props)
    this.state = {
      amountInput: '0.00',
      fiatInput: '0',
      appState: AppState.currentState,
      scannerModalVisable: false,
      toggleCryptoFiat: false,
      twoFactor: '',
      twoFactorModalVisible: false,
      withdrawAddressValid: true,
      withdrawButtonActive: false,
    }
    this.inputs = {}
  }

  componentDidMount() {
    this.focusNextField('amountInput')
    updateActiveTabListener(this.props.navigation)
    const {params} = this.props.route
    this.props.dispatch(screenView(`Collateral Withdraw - ${params.title}`))
    this.setState({withdrawType: params.title})
    AppState.addEventListener('change', this.handleAppStateChange)
  }

  componentWillUnmount() {
    //AppState.removeEventListener('change', this.handleAppStateChange)
    this.setState({scannerModalVisable: false, twoFactorModalVisible: false})
  }

  handleAppStateChange = async nextAppState => {
    if (
      this.state.appState.match(/inactive|background/) &&
      nextAppState === 'active'
    ) {
      const clipboardCopy = await Clipboard.getString()
      if (clipboardCopy.length === 6 && !isNaN(clipboardCopy)) {
        this.setState({twoFactor: clipboardCopy})
      }
    }
    this.setState({appState: nextAppState})
  }

  handleAmountInput = input => {
    input = input.replace(/[, ]+/g, '')
    const price = this.props.route.params.price.replace('$', '')
    this.state.toggleCryptoFiat
      ? this.setState({
          amountInput:
            Number(input) > 0
              ? new BigNumber(input).dividedBy(price).toFormat(8)
              : '0.00000000',
          fiatInput: input,
          withdrawButtonActive: Number(input) > 0,
        })
      : this.setState({
          amountInput: input,
          fiatInput:
            input > 0
              ? new BigNumber(input).multipliedBy(price).toFormat(2)
              : '0.00',
          withdrawButtonActive: Number(input) > 0,
        })
  }

  handleCryptoFiatToggle = () => {
    this.setState({
      toggleCryptoFiat: !this.state.toggleCryptoFiat,
    })
  }

  focusNextField = field => {
    if (this.inputs[field]) {
      this.inputs[field].focus()
    }
  }

  hideKeyboard = () => {
    Keyboard.dismiss()
  }

  handleAddressInput = text => {
    this.setState({withdrawAddress: text, withdrawAddressValid: true})
  }

  openQRScanner = () => {
    this.toggleScannerModal()
  }

  toggleScannerModal = () => {
    this.props.dispatch(askingForPermissions(!this.state.scannerModalVisable))
    this.setState({scannerModalVisable: !this.state.scannerModalVisable})
  }

  validateAddress = () => {
    const currency = this.props.route.params.title

    // make validator fn dynamic, takes in ticker

    if (currency === 'BTC') {
      if (!isValidBTCAddress(this.state.withdrawAddress)) {
        this.setState({withdrawAddressValid: false})
        return false
      }
    }

    if (
      currency === 'ETH' ||
      currency === 'SALT' ||
      currency === 'USDC' ||
      currency === 'TUSD' ||
      currency === 'USDP'
    ) {
      if (!isValidETHAddress(this.state.withdrawAddress)) {
        this.setState({withdrawAddressValid: false})
        return false
      }
    }

    if (currency === 'LTC') {
      if (!isValidLTCAddress(this.state.withdrawAddress)) {
        this.setState({withdrawAddressValid: false})
        return false
      }
    }

    if (currency === 'DOGE') {
      if (!isValidDOGEAddress(this.state.withdrawAddress)) {
        this.setState({withdrawAddressValid: false})
        return false
      }
    }

    if (currency === 'DASH') {
      if (!isValidDASHAddress(this.state.withdrawAddress)) {
        this.setState({withdrawAddressValid: false})
        return false
      }
    }

    return true
  }

  validateAndShowModal = () => {
    if (this.validateAddress()) {
      this.handleNext()
    }
  }

  updateAddressFromQR = address => {
    this.setState({withdrawAddress: address})
  }

  handleNext = () => {
    const price = this.props.route.params.price.replace('$', '')
    let amountPrice = Number(this.state.amountInput) * price
    amountPrice = amountPrice.toFixed(2)

    const withdrawData = {
      params: this.props.route.params,
      amount: this.state.amountInput,
      currency: this.state.withdrawType,
      toAddress: this.state.withdrawAddress,
      twoFactorCode: this.state.twoFactor,
      amountPrice,
    }

    this.props.navigation.navigate('WithdrawConfirm', withdrawData)
  }

  render() {
    const {params} = this.props.route
    const {amountInput, fiatInput, toggleCryptoFiat, withdrawButtonActive} =
      this.state

    //const price = this.props.dispatch(getTokenPrice(params.title))
    const titleUSD = `${params.title}-USD`
    const price = this.props.tokenPrices[titleUSD].price

    let amount = '0'
    if (this.props.loanData.collaterals && dig(params, 'title')) {
      amount = this.props.loanData.collaterals.filter(
        a => a.currency === params.title,
      )[0].balance
    }

    const withdrawalExceedsBalance =
      this.state.amountInput && Number(this.state.amountInput) > Number(amount)

    const showAmount = amountInput.replace(/[, ]+/g, '')
    const showFiat = fiatInput.replace(/[, ]+/g, '')

    return (
      <View style={commonStyles.container}>
        <WithdrawQRScanner
          toggleScannerModal={this.toggleScannerModal}
          scannerModalVisable={this.state.scannerModalVisable}
          showPinScreen={this.props.showPinScreen}
          updateAddressFromQR={this.updateAddressFromQR}
        />
        <View style={styles.withdrawHeader}>
          <TouchableOpacity onPress={() => this.focusNextField('amountInput')}>
            <View style={styles.withdrawAmountBox}>
              <TextInput
                style={styles.withdrawAmountInput}
                onChangeText={text => this.handleAmountInput(text)}
                ref={input => (this.inputs.amountInput = input)}
                value={
                  toggleCryptoFiat
                    ? showFiat >= 0
                      ? showFiat
                      : ''
                    : showAmount >= 0
                    ? showAmount
                    : ''
                }
                underlineColorAndroid="transparent"
                blurOnSubmit
                returnKeyType={'done'}
                onSubmitEditing={() => this.hideKeyboard()}
                onFocus={() => this.handleAmountInput('')}
                placeholder={'0.00'}
                selectionColor="#FFF"
                placeholderTextColor="rgba(255, 255, 255, 0.7)"
                keyboardType={'numeric'}
                keyboardAppearance="dark"
              />
              <TextReg
                style={
                  this.state.amountInput === '' || this.state.fiatInput === ''
                    ? styles.withdrawAmountTextDetailActive
                    : styles.withdrawAmountTextDetail
                }>
                {toggleCryptoFiat ? 'USD' : params.title}
              </TextReg>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={this.handleCryptoFiatToggle}
            style={styles.cryptoFiatToggle}>
            <Image
              source={switchPng}
              style={{
                height: 25,
                width: 25,
              }}
            />
          </TouchableOpacity>

          <View style={styles.withdrawPriceBox}>
            <TextReg style={styles.withdrawPriceText}>
              {toggleCryptoFiat
                ? `${amountInput !== '' ? amountInput : '0.00000000'} ${
                    params.title
                  }`
                : `$${fiatInput} USD`}
            </TextReg>
            <TextReg style={styles.balanceErrorText}>
              {withdrawalExceedsBalance
                ? 'Withdrawal Amount Exceeds Balance'
                : ''}
            </TextReg>
          </View>
          <View style={styles.withdrawPriceBalanceBox}>
            <TouchableOpacity
              onPress={() =>
                this.handleAmountInput(
                  toggleCryptoFiat
                    ? new BigNumber(amount)
                        .multipliedBy(price)
                        .multipliedBy(0.9999999)
                        .toFixed(2)
                    : (Math.floor(amount * 100000000) / 100000000).toString(),
                )
              }>
              <TextReg style={styles.withdrawPriceBalanceText}>
                {toggleCryptoFiat
                  ? `Available: $${new BigNumber(amount)
                      .multipliedBy(price)
                      .multipliedBy(0.9999999)
                      .toFormat(2)} USD`
                  : `Available: ${Math.floor(amount * 100000000) / 100000000} ${
                      params.title
                    }`}
              </TextReg>
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.withdrawMainBox}>
          <TextReg style={styles.withdrawAddressTitle}>
            Destination Address
          </TextReg>
          <View style={styles.withdrawAddressInputBox}>
            <TextInput
              onChangeText={text => this.handleAddressInput(text)}
              style={styles.withdrawAddressInput}
              underlineColorAndroid="transparent"
              blurOnSubmit
              value={this.state.withdrawAddress}
              multiline
              returnKeyType={'done'}
              onSubmitEditing={() => this.hideKeyboard()}
              keyboardAppearance="dark"
            />
            <TouchableOpacity onPress={() => this.openQRScanner()}>
              <Image
                source={qRcodeScanner}
                style={{
                  height: 20,
                  width: 20,
                  marginLeft: 12,
                  marginTop: 2,
                }}
              />
            </TouchableOpacity>
          </View>

          <TextReg style={styles.withdrawAddressDescription}>
            Make sure to use a {cryptoNameMap.get(params.title.toLowerCase())}{' '}
            address
          </TextReg>
          <Button
            style={styles.withdrawSubmitButton}
            onPress={this.validateAndShowModal}
            disabled={!withdrawButtonActive || withdrawalExceedsBalance}>
            Preview Withdrawal
          </Button>
          {!this.state.withdrawAddressValid && (
            <View style={styles.withdrawErrorBox}>
              <TextReg style={styles.withdrawErrorText}>
                {`The ${params.title} address you entered is not valid. Please try again.`}
              </TextReg>
            </View>
          )}
        </View>
      </View>
    )
  }
}

WithdrawScreen.navigationOptions = ({navigation}) => ({
  title: 'Withdraw',
  headerTintColor: '#FFF',
  headerStyle: {
    backgroundColor: '#05868e',
    borderBottomWidth: 0,
  },
  tabBarLabel: 'Wallets',
})

const mapStateToProps = state => ({
  loanData: state.user.loanData,
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
})

export default connect(mapStateToProps)(WithdrawScreen)
