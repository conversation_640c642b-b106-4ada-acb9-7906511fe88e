/* eslint-disable import/no-namespace */
/* eslint-disable no-undef */

import React from 'react'
import * as enzyme from 'enzyme'
import thunk from 'redux-thunk'
import configureMockStore from 'redux-mock-store'
import WithdrawScreen from './WithdrawScreen'
import initialState from '../../store/initialState'

import DeviceInfo from 'react-native-device-info'

const mockStore = configureMockStore([thunk])

const buildComponent = (renderType = enzyme.shallow, newProps = {}) => {
  const defaultProps = {
    dispatch: jest.fn(() => Promise.resolve({})),
    loanData: {
      ltv: '0',
      loanValue: '0',
      collateralValue: '0',
      loans: [],
      collaterals: [
        {
          currency: 'BTC',
          value: '0',
          price: '0',
          balance: '0',
        },
        {
          currency: 'ETH',
          value: '0',
          price: '0',
          balance: '0',
        },
        {
          currency: 'SALT',
          value: '0',
          price: '0',
          balance: '0',
        },
        {
          currency: 'LTC',
          value: '0',
          price: '0',
          balance: '0',
        },
        {
          currency: 'DOGE',
          value: '0',
          price: '0',
          balance: '0',
        },
      ],
    },
    navigation: {
      addListener: jest.fn(),
      setParams: jest.fn(),
      state: {
        params: {
          title: 'BTC',
        },
      },
    },
  }

  const props = { ...defaultProps, ...newProps }
  return renderType(
    <WithdrawScreen store={mockStore(initialState)} {...props} />
  )
}

describe('Testing WithdrawScreen', () => {
  it('renders component', () => {
    const wrapper = buildComponent().dive()
    expect(wrapper.length).toBe(1)
  })
})
