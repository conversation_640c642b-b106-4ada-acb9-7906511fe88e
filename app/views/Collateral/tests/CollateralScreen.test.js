/* eslint-disable import/no-namespace */
/* eslint-disable no-undef */

import React from 'react'
import * as enzyme from 'enzyme'
import thunk from 'redux-thunk'
import configureMockStore from 'redux-mock-store'
import CollateralScreen from './CollateralScreen'
import initialState from '../../store/initialState'

import DeviceInfo from 'react-native-device-info'

const mockStore = configureMockStore([thunk])

const buildComponent = (renderType = enzyme.shallow, newProps = {}) => {
  const defaultProps = {
    dispatch: jest.fn(() => Promise.resolve({})),
    navigation: {
      addListener: jest.fn(),
      setParams: jest.fn(),
    },
  }

  const props = { ...defaultProps, ...newProps }
  return renderType(
    <CollateralScreen store={mockStore(initialState)} {...props} />
  )
}

describe('Testing CollateralScreen', () => {
  it('renders component', () => {
    const wrapper = buildComponent().dive()
    expect(wrapper.length).toBe(1)
  })
})
