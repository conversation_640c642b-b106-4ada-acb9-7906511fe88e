import React, {Component} from 'react'
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  PermissionsAndroid,
  Platform,
  Share,
} from 'react-native'
import {connect} from 'react-redux'
import RNFS from 'react-native-fs'
import Papa from 'papaparse'
//import Share from 'react-native-share'
import <PERSON>NFetchBlob from 'rn-fetch-blob'
//import ReactNativeBlobUtil from 'react-native-blob-util'

import {numberWithCommas, dig, splitByCamelCase} from '../../../util/helpers'
import {TextReg, TextBold, Card} from '../../../components'

import styles from '../styles'

class Download extends Component {
  constructor(props) {
    super(props)
    this.state = {
      refreshing: true,
      transactions: [],
      error: false,
      showTxId: null,
      status: 'before',
    }
  }

  static getDerivedStateFromProps(props, state) {
    const transactions = props.loanData.collaterals.filter(a => a.currency === props.token)[0]
      ?.transactions
    if (!transactions) {
      return null
    }
    return {
      ...state,
      transactions,
    }
  }

  requestStoragePermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          {
            title: 'Storage Permission Required',
            message: 'This app needs access to your storage to save files',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        )
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Storage permission granted')
          return true
        } else {
          console.log('Storage permission denied')
          return false
        }
      } catch (err) {
        console.warn(err)
        return false
      }
    } else {
      // No need to request permission on iOS
      return true
    }
  }

  saveAndroid = async (data, title = 'data.csv') => {
    // Check for permission first
    const hasPermission = await this.requestStoragePermission()
    if (!hasPermission) {
      console.log('Permission denied')
      return
    }

    const csv = Papa.unparse(data)
    const path = `${RNFS.ExternalStorageDirectoryPath}/Documents/SALT/${title}`

    try {
      await RNFS.mkdir(`${RNFS.ExternalStorageDirectoryPath}/Documents/SALT/`)
      await RNFS.writeFile(path, csv, 'utf8')
      console.log(`File written to ${path}`)
    } catch (error) {
      console.error('Error saving or sharing CSV file', error)
    }
  }

  saveIOS = async (data, title = 'data.csv') => {
    const csv = Papa.unparse(data)
    const path = `${RNFS.DocumentDirectoryPath}/${title}`

    try {
      await RNFS.writeFile(path, csv, 'utf8')
      console.log(`File written to ${path}`)

      // Share the file
      const shareResponse = await Share.share({
        title: title,
        url: `file://${path}`,
        type: 'text/csv',
      })

      console.log('File shared:', shareResponse)
    } catch (error) {
      console.error('Error saving or sharing CSV file', error)
    }
  }

  cap = str => {
    return str.charAt(0).toUpperCase() + str.slice(1)
  }

  formatDate = inDate => {
    let date = new Date(inDate)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    })
  }

  getScannerUrl = (token, txId) => {
    //const token = this.props.route.params.title
    switch (token) {
      case 'BTC':
        return `https://live.blockcypher.com/btc/tx/${txId}`
      case 'BCH':
        return `https://explorer.bitcoin.com/bch/tx/${txId}`
      case 'LTC':
        return `https://live.blockcypher.com/ltc/tx/${txId}`
      case 'DASH':
        return `https://chainz.cryptoid.info/dash/tx.dws?${txId}.htm`
      case 'ETH':
        return `https://etherscan.io/tx/${txId}`
      case 'SALT':
        return `https://etherscan.io/tx/${txId}`
      case 'TUSD':
        return `https://etherscan.io/tx/${txId}`
      case 'USDC':
        return `https://etherscan.io/tx/${txId}`
      case 'DOGE':
        return `https://dogechain.info/tx/${txId}`
      case 'XRP':
        return `https://xrpscan.com/tx/${txId}`
      default:
        return `https://etherscan.io/tx/${txId}`
    }
  }

  down = async () => {
    let transactions = this.state.transactions.filter(a => a.reason !== 'commingle')

    transactions = transactions.sort(this.sortByDate)
    transactions = transactions.map(a => {
      if (a.reason == 'extraction') {
        a.reason = 'liquidation'
      }
      return {
        ...a,
        showId: a.id == this.state.showTxId,
        confirmed: a.status == 'confirmed',
      }
    })

    let downData = transactions?.map(a => {
      let dateMat = this.formatDate(a.date)

      let amtNum = Number(a.amount)
      if (a.reason == 'transfer' || a.reason == 'stabilization' || a.reason == 'withdrawal') {
        amtNum = amtNum * -1
      }
      amtNum = amtNum.toString()

      let confirmId = a.id?.toUpperCase()?.split('-')[0]

      if (a.reason == 'deposit' && a.txid) {
        confirmId = this.getScannerUrl(a.currency, a.txid)
      }

      return {
        Date: dateMat,
        Amount: amtNum,
        Status: this.cap(a.status),
        Reason: this.cap(a.reason),
        'Confirmation ID': confirmId,
      }
    })

    /*
    const exampleData = [
      {column1: 'data1', column2: 'data2'},
      {column1: 'data3', column2: 'data4'},
    ]
    */

    let title = `${this.props.token}-Tx-History.csv`
    if (Platform.OS === 'android') {
      this.saveAndroid(downData, title)
    } else {
      this.saveIOS(downData, title)
    }
  }

  amountXPrice = (amount, price = '0') => {
    if (price === 0) price = '0'
    price = price.toString()
    const newPrice = price.replace('$', '')
    return amount * newPrice
  }

  colorReason = item => {
    if (item.type === 'received') {
      return '#00FFBD'
    }
    return '#E5705A'
  }

  updatedStatusWord = text => (text === 'detected' ? 'unconfirmed' : text)

  sortByDate = (a, b) => {
    if (a.date > b.date) {
      return -1
    } else if (b.date > a.date) {
      return 1
    }
    return 0
  }

  removeUnderScore = string => string.replace('_', ' ')

  render() {
    let pricesArr = Object.entries(this.props.prices)

    let transactions = this.state.transactions.filter(a => a.reason !== 'commingle')

    transactions = transactions.sort(this.sortByDate)
    transactions = transactions.map(a => {
      if (a.reason == 'extraction') {
        a.reason = 'liquidation'
      }
      return {
        ...a,
        showId: a.id == this.state.showTxId,
        confirmed: a.status == 'confirmed',
      }
    })

    let showDown = true
    if (this.state.transactions.length === 0) {
      showDown = false
    }
    if (true) {
      //enable for ios or android later
      showDown = false
    }
    return (
      <View
        style={{
          alignSelf: 'stretch',
          height: 40,
          flexDirection: 'row',
          justifyContent: 'space-between',
          marginLeft: 14,
          marginRight: 14,
          marginBottom: 6,
        }}>
        <View style={{justifyContent: 'flex-start', borderRadius: 12, width: 200}}>
          <TextReg
            style={{fontSize: 17, marginTop: 12, marginLeft: 10}}>{`Transaction History`}</TextReg>
        </View>
        {showDown && (
          <TouchableOpacity
            onPress={() => this.down()}
            style={{
              height: 40,
              width: 40,
              borderRadius: 12,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Image
              source={require('../../../imgs/icons/cloud.png')}
              style={{height: 34, width: 34, marginTop: 8}}
            />
          </TouchableOpacity>
        )}
        {!showDown && <View style={{height: 40, width: 40}}></View>}
      </View>
    )
  }
}

const mapStateToProps = state => ({
  prices: state.user.prices,
  accountRef: state.auth.account.ref,
  loanData: state.user.loanData,
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(Download)
