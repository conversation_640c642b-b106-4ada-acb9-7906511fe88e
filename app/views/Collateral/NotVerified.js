import React from 'react'
import {Modal, View, Text, Image, TouchableOpacity} from 'react-native'

import {Button} from '../../components'

import styles from './styles'

const NotVerified = props => (
  <Modal
    animationType="fade"
    transparent
    visible={props.verifiedModalVisable && !props.showPinScreen}
    onRequestClose={() => props.toggleVerifiedModal()}>
    <View style={styles.helpModalBox}>
      <View style={styles.helpModalSquare}>
        <TouchableOpacity
          style={styles.helpModalX}
          onPress={() => {
            props.toggleVerifiedModal()
          }}>
          <Image
            source={require('../../imgs/closeX.png')}
            style={styles.closeModalImg}
          />
        </TouchableOpacity>
        <View style={styles.helpModalTitleBox}>
          <Text style={styles.helpModalTitle}>Not Verified</Text>
        </View>
        <View style={styles.helpModalDescriptionBox}>
          <Text style={styles.helpModalDescription}>
            You are not verified yet, please go verify your identification.
          </Text>
        </View>
        <Button
          style={{margin: 20, marginBottom: 30}}
          onPress={() => {
            props.openVerifyLink()
          }}>
          VERIFY
        </Button>
      </View>
    </View>
  </Modal>
)

export default NotVerified
