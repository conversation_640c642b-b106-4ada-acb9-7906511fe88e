import React, {Component} from 'react'
import {
  View,
  Image,
  TouchableOpacity,
  Linking,
  RefreshControl,
  Animated,
  Platform,
} from 'react-native'
import {connect} from 'react-redux'

import AsyncStorage from '@react-native-async-storage/async-storage'
import {dig, updateActiveTabListener, numberWithCommas} from '../../util/helpers'
import {screenView} from '../../store/analytics/analytics.actions'
import {
  clearPendingWithdrawal,
  updateWallets,
  updateWalletTransactions,
  updateAllWallets,
  updateLoansDrill,
  showSlide,
} from '../../store/user/user.actions'

import {showToast} from '../../store/notifications/notifications.actions'

import NotVerified from './NotVerified'
import ShowPendingWithdrawal from './ShowPendingWithdrawal'
import TransactionHistory from './TransactionHistory'

import {getTokenDetailLogo, getTokenDetailLogoSize} from '../../util/tokens'

import WalletDetailTransfer from '../../views/Collateral/WalletDetailTransfer'
import WithdrawStack from './Withdraw/WithdrawStack'
import DepositScreen from './DepositScreen'
import Uphold from './Uphold/Uphold'
import UpholdLink from './Uphold/UpholdLink'
import Download from './Tx/Download'

import {TextReg, BackgroundHeader, TextBold, Card, Toast} from '../../components'

import commonStyles from '../../styles/commonStyles'
import styles from './styles'

class CollateralDetail extends Component {
  constructor(props) {
    super(props)

    const {params} = props.route

    let title
    if (!params || !params.title) {
      title = 'SALT'
    } else {
      title = params.title
    }

    const tokenPic = getTokenDetailLogo(title)
    const logoStats = getTokenDetailLogoSize(title)
    const logoHeight = logoStats.logoHeight
    const logoWidth = logoStats.logoWidth

    this.state = {
      cancelPending: false,
      updatedTransactions: false,
      verifiedModalVisable: false,
      creatingWallet: false,
      refreshing: true,
      showWithdraw: false,
      showDeposit: false,
      showTxHistory: true,
      showHeader: true,
      showUphold: false,
      upholdToken: null,
      upholdBalance: '0.00',
      upholdCardId: null,
      logoHeight,
      logoWidth,
      tokenPic,
      displayValue: 0,
      displayValueFontSize: 48,
      displayAmount: 0,
      showPendingWithdrawal: false,
      disabledWithdrawal: false,
      pendingWithdrawalObj: {},
      currency: '',
      title,
      showPurchaseSaltStack: false,
      xrpInit: false,
      awaitingLiquidation: false,
      showDisabled: false,
    }

    this.upholdScroll = new Animated.Value(-200)
    this.txOpacity = new Animated.Value(1)
  }

  componentDidMount() {
    updateActiveTabListener(this.props.navigation)
    this.props.dispatch(screenView(`Collateral Detail - ${this.state.title}`))
    this.getTransactionHistory()
    if (this.props.route && this.props.route.params && this.props.route.params.depositActive) {
      this.setState({showDeposit: true})
    }

    // check for uphold token ( api or storage )
    AsyncStorage.getItem(`UpholdToken-${this.props.user.primaryEmail}`).then(res => {
      if (res == 'true') {
        this.setState({upholdToken: res})
      }
    })
    this.getUpholdCards()
    this.isXRPMultiSig()
    // if is one - get cards
    // maybe have to get cards from collateral screen part

    // get uphold closed (x) saved wallet email choice - give to component
  }

  static getDerivedStateFromProps(props, state) {
    const {params} = props.route
    let title
    if (!params) {
      title = 'SALT'
    } else if (params.title) {
      title = params.title
    }
    const {loanData} = props

    let productRef = props?.route?.params?.productRef
    let collaterals = props?.user?.allWallets[productRef - 1] || []

    const wallet = collaterals.filter(a => a.currency === title)[0]

    if (wallet) {
      const pendingWithdrawal = dig(wallet, 'pendingExtractions') || []
      let showPendingWithdrawal = false
      let pendingWithdrawalObj = {}
      let pendingWithdrawalStatus = null

      pendingWithdrawal.map(a => {
        if (a.reason === 'withdrawal') {
          pendingWithdrawalObj = a
          pendingWithdrawalStatus = a.status
          showPendingWithdrawal = a.id && true
        }
      })

      const amount = wallet?.projectedBalance || '0'

      const titleUSD = `${title}-USD`
      let price = props.tokenPrices[titleUSD].price

      if (title == 'PREF') {
        //get from obj / not prices
        price = Number(params?.price?.replace('$', '')?.replace(/,/g, ''))
        console.log('collateral detail', props, params)
      }

      let displayValue = amount * price
      displayValue = numberWithCommas(Number(displayValue).toFixed(2))
      displayValue = `$${displayValue}`

      let displayValueFontSize = 48

      if (displayValue.length > 13) {
        displayValueFontSize = 40
      }
      if (displayValue.length > 15) {
        displayValueFontSize = 38
      }

      let variableDisplayDepth = title === 'DOGE' ? 3 : 8
      if (title === 'USDC') {
        variableDisplayDepth = 2
      }

      const displayAmount = numberWithCommas(Number(amount).toFixed(variableDisplayDepth))

      const awaitingLiquidation = loanData?.awaitingLiquidation || false

      const liquidationThresh = loanData.thresholds?.liquidation

      const disabledWithdrawal = loanData.ltv > liquidationThresh

      //setState logoHeight logoWidth tokenPic displayValueFontSize displayAmount showPendingWithdrawal disabledButtons
      return {
        ...state,
        displayValueFontSize,
        displayAmount,
        showPendingWithdrawal,
        disabledWithdrawal,
        pendingWithdrawalObj,
        displayValue,
        price,
        currency: wallet.currency,
        awaitingLiquidation,
      }
    }
    return {
      ...state,
      displayValue: '$0.00',
      displayAmount: '0.0000000',
    }
  }

  /*
  componentDidUpdate = prevProps => {
    if (
      prevProps.externalSlideFn != this.props.externalSlideFn &&
      this.props.externalSlideFn?.response
    ) {
      console.log(
        'external slide toggle response',
        this.props.externalSlideFn?.response,
      );

      if (this.props.externalSlideFn?.response == 'withdrawal') {
        this.setState({showWithdraw: true});
      }
    }
  };
  */

  getUpholdCards = () => {
    this.props.WebService.getUpholdCards()
      .then(res => {
        const hasUphold = res.data.filter(a => a.currency == this.state.title)
        if (hasUphold.length < 1) {
          this.setState({upholdBalance: '0.00'})
        } else {
          this.setState({
            upholdBalance: hasUphold[0].available,
            upholdCardId: hasUphold[0].id,
          })
        }
        this.setState({upholdToken: 'true'})
      })
      .catch(err => {
        this.setState({upholdToken: null})
      })
  }

  isXRPMultiSig = () => {
    if (this.state.title !== 'XRP') return
    const xrpId = this.props.loanData.collaterals.filter(a => a.currency === 'XRP')[0]
    if (xrpId.id) {
      console.log('xrpId', xrpId.id)
      this.props.WebService.walletStatus(xrpId.id)
        .then(res => {
          if (res.data.multisig) {
            //wallet is normal xrp now
            this.setState({xrpInit: false})
          } else {
            // need to do the 26 deposit
            this.setState({xrpInit: true})
          }
        })
        .catch(err => {
          console.log('walletStatus err', err)
        })
    } else {
      //no xrp wallet yet
      this.setState({xrpInit: true})
    }
  }

  getTransactionHistory = () => {
    this.setState({refreshing: true})
    console.log('getTransactionHistory', this.state.title, this.props.accountRef)
    this.props.WebService.getTransactionHistory(this.state.title, this.props.accountRef)
      .then(res => {
        console.log('res tx h', res.data)
        this.setState({refreshing: false})
        this.props.dispatch(updateWalletTransactions(res.data, this.state.title))
      })
      .catch(err => {
        this.setState({refreshing: false, error: true})
        //show an error message?
      })
  }

  cancelWithdrawal = (id, virtual) => {
    this.setState({cancelPending: true})
    //const { params } = this.props.route

    const {loanData} = this.props
    const wallet = dig(loanData, 'collaterals')
      ? loanData.collaterals.filter(a => a.currency === this.state.title)[0]
      : {}
    if (virtual) {
      this.props.WebService.cancelVirtualTransaction(id)
        .then(res => {
          this.props.dispatch(clearPendingWithdrawal(wallet.id))
          this.setState({cancelPending: false})
          this.getTransactionHistory()
        })
        .catch(error => {
          //do something
          this.setState({cancelPending: false})
          this.getTransactionHistory()
        })
    } else {
      this.props.WebService.cancelWithdrawal(id)
        .then(res => {
          this.props.dispatch(clearPendingWithdrawal(wallet.id))
          this.setState({cancelPending: false})
          this.getTransactionHistory()
        })
        .catch(error => {
          //do something
          this.setState({cancelPending: false})
          this.getTransactionHistory()
        })
    }
  }

  toggleVerifiedModal = () => {
    this.setState({verifiedModalVisable: !this.state.verifiedModalVisable})
  }

  openVerifyLink = () => {
    this.props.navigation.navigate('IdentityVerification')
    this.toggleVerifiedModal()
  }

  refreshWallets = async () => {
    let accountArr = this.props.user?.accounts || []
    let walletsRes = []

    await Promise.all(
      accountArr.map(async a => {
        await this.props.WebService.getWallets(a.ref).then(res => {
          //walletsRes.push(res.data);
          walletsRes[a.ref - 1] = res.data
          return true
        })
      }),
    )
    this.props.dispatch(updateAllWallets(walletsRes))

    let loanWallets = walletsRes[this.props.accountRef - 1]
    let {loanData} = this.props
    loanData = {
      ...loanData,
      collaterals: loanWallets,
    }
    this.props.dispatch(updateLoansDrill(loanData))

    console.log('it updateing wallets', walletsRes)
    return true
  }

  handleDeposit = title => {
    //const { params } = this.props.route

    let currentlyDisabled = this.props.launchDarkly['disable-deposit'] || false
    if (currentlyDisabled) {
      this.setState({showDisabled: true})
      return
    }

    let ldDisableName = `disable-${title.toLowerCase()}-deposit`
    let ldSingleDisabled = this.props.launchDarkly[ldDisableName] || false
    if (ldSingleDisabled) {
      this.setState({singleDisabled: true})
      return
    }

    if (!this.props.user.mfaEnabled) {
      this.props.dispatch(showToast(true))
      return
    }

    const hasWallet = this.props.loanData.collaterals.filter(
      a => a.currency === this.state.title,
    )[0]

    const {amount, verification} = this.props.loanData
    let verified = this.props.user.walletsEnabled

    //if doesnt have a wallet
    if (!hasWallet?.address || !verified) {
      // create the wallet

      // if user has not finished signup
      if (verified) {
        this.setState({creatingWallet: true})
        this.props.WebService.createWallet(this.state.title, this.props.accountRef)
          .then(async res => {
            ///this.props.dispatch(updateWallets(res.data));
            await this.refreshWallets()
            this.setState({creatingWallet: false, showDeposit: true})
          })
          .catch(err => {
            console.log('err', err)
            this.setState({creatingWallet: false})
          })
      } else {
        //show Needs to verify ID
        this.setState({verifiedModalVisable: true})
      }
    } else {
      this.setState({showDeposit: true})
    }
  }

  handleWithdraw = () => {
    let currentlyDisabled = this.props.launchDarkly['disable-withdraw'] || false
    if (currentlyDisabled) {
      this.setState({showDisabled: true})
      return
    }

    if (!this.props.user.mfaEnabled) {
      this.props.dispatch(showToast(true))
      return
    }

    if (this.state.disabledWithdrawal) {
      return
    }
    /*
    const collateral = this.props.loanData.collaterals.find(
      collateral =>
        collateral.currency === this.props.route.params.title
    )
    */

    /*
    const data = {
      ...this.props.route.params,
      ...collateral,
    }
    */

    if (this.props.loanData.ltv > 0.9091) {
      return
    }

    //this.setState({showWithdraw: true});
    const hasWallet = this.props.loanData.collaterals.filter(
      a => a.currency === this.state.title,
    )[0]

    let address = hasWallet?.address
    let price = hasWallet?.price
    let currency = hasWallet?.currency
    let transferWalletFrom = hasWallet?.id
    let loanID = this.props.loanData?.id || 0
    let loanStatus = this.props.loanData?.status || ''
    let projectedBalance = hasWallet?.projectedBalance

    let accountName = this.props?.user?.accounts?.filter(a => a.ref == this.props.accountRef)[0]
    accountName = accountName?.name || ''

    this.props.dispatch(
      showSlide({
        action: 'withdrawal',
        address,
        currency,
        price,
        chosenRef: this.props.accountRef,
        transferWalletFrom,
        loanID,
        projectedBalance,
        accountName,
        loanStatus,
      }),
    )
  }

  closeWithdraw = () => {
    this.setState({showWithdraw: false, showPurchaseSaltStack: false})
    // now we want to refresh and look for pending
    //this.getWallets()
    this.getTransactionHistory()
  }

  /*
  getWallets = () => {
    this.setState({ refreshing: true })
    this.props.WebService.getWallets(this.props.accountRef)
      .then(res => {
        res.data.map(wallet => this.props.dispatch(updateWallets(wallet)))
        this.setState({ refreshing: false })
      })
      .catch(err => {
        this.setState({ refreshing: false })
      })
  }
  */

  closeDeposit = () => {
    this.setState({showDeposit: false})
  }

  purchaseSalt = () => {
    this.setState({showPurchaseSaltStack: true})
  }

  toggleUphold = () => {
    if (!this.state.upholdScrolled) {
      this.setState({upholdScrolled: !this.state.upholdScrolled}, () => {
        Animated.parallel([
          Animated.timing(this.txOpacity, {
            toValue: 0,
            duration: 100,
            useNativeDriver: true,
          }),
          Animated.timing(this.upholdScroll, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start()
      })
    } else {
      Animated.parallel([
        Animated.timing(this.upholdScroll, {
          toValue: -200,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(this.txOpacity, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start(() => {
        this.setState({upholdScrolled: !this.state.upholdScrolled})
      })
    }
  }

  showUpholdModal = () => {
    this.setState({showUphold: !this.state.showUphold})
  }

  sendToUphold = () => {
    Linking.openURL('https://uphold.com/')
  }
  goSupport = () => {
    Linking.openURL('mailto:<EMAIL>')
  }

  unlinkUphold = () => {
    AsyncStorage.removeItem(`UpholdToken-${this.props.user.primaryEmail}`, () => {
      this.removeApiUpholdToken()
    })
  }

  removeApiUpholdToken = () => {
    this.props.WebService.removeUphold()
      .then(res => {
        this.setState({upholdToken: null})
        this.toggleUphold()
      })
      .catch(err => {
        console.log('remove err api uphold', err)
      })
  }

  updateToken = token => {
    this.setState({upholdToken: token})
  }

  goToTwoFactor = () => {
    this.props.navigation.navigate('TwoFactor')
  }

  render() {
    let hideUphold = false
    if (this.props.route.params.hideUphold) {
      hideUphold = this.props.route.params.hideUphold.includes(this.state.title)
    }

    //disable uphold xrp
    if (this.state.title == 'XRP' || this.props?.user?.primaryEmail == '<EMAIL>') {
      hideUphold = true
    }

    //const { params } = this.props.route

    const pendingWithdrawalDisabled = !(
      this.state.pendingWithdrawalStatus === 'initiated' ||
      this.state.pendingWithdrawalStatus === 'uninitiated'
    )

    /*
      <View style={styles.GradiantHeaderBottomWhiteMargin} />
    */

    let amount = '0'
    if (this.props.loanData.collaterals && this.state.title) {
      amount = this.props.loanData.collaterals?.filter(a => a.currency === this.state.title)[0]
        ?.projectedBalance
    }

    const hasWallet = this.props.loanData.collaterals.filter(
      a => a.currency === this.state.title,
    )[0]

    const {
      logoHeight,
      logoWidth,
      displayValueFontSize,
      displayValue,
      displayAmount,
      tokenPic,
      showPendingWithdrawal,
      pendingWithdrawalObj,
      currency,
      disabledWithdrawal,
    } = this.state

    let productRef = this.props?.route?.params?.productRef
    let collaterals = this.props?.user?.allWallets[productRef - 1] || []

    const collateral = collaterals.find(collateral => collateral.currency === this.state.title)

    const isStabilized = this.props.user.accounts?.filter(a => a.ref == this.props.accountRef)[0]
      ?.product?.loan?.isStabilized

    let simplePage = currency == 'PREF'

    const handleSupport = () => {
      return (
        <TextReg
          onPress={() => this.goSupport()}
          style={{
            color: '#00FFBD',
            textAlign: 'center',
          }}>
          <EMAIL>
        </TextReg>
      )
    }

    if (simplePage) {
      return (
        <Animated.View style={[commonStyles.tileContainer, {paddingTop: 40}]}>
          <Toast goTo={this.goToTwoFactor} />
          <Animated.View
            style={[styles.GradiantHeaderCenterBox, {paddingTop: Platform.OS === 'ios' ? 26 : 0}]}>
            <TouchableOpacity
              style={{
                marginBottom: 190,
                width: 40,
                justifyContent: 'center',
                zIndex: 20,
                position: 'absolute',
                left: 6,
                top: Platform.OS === 'ios' ? 24 : 0,
              }}
              onPress={() => this.props.navigation.goBack()}>
              <Image
                source={require('../../imgs/backToSettings.png')}
                style={{
                  height: 24,
                  width: 30,
                  //opacity: 0.85,
                }}
              />
            </TouchableOpacity>

            <Image
              source={tokenPic}
              style={[styles.tokenPic, {height: logoHeight, width: logoWidth}]}
            />
            <TextBold style={[styles.displayValueText, {fontSize: displayValueFontSize}]}>
              {displayValue}
            </TextBold>
            <TextBold
              style={styles.displayAmountText}>{`${displayAmount} ${this.state.title}`}</TextBold>
          </Animated.View>
          <Animated.ScrollView
            style={[
              styles.swiperContainer,
              {
                marginTop: -20,
                opacity: this.txOpacity,
              },
            ]}
            contentContainerStyle={{alignItems: 'center'}}
            refreshControl={
              <RefreshControl
                refreshing={this.state.refreshing}
                onRefresh={this.getTransactionHistory}
                colors={['#28283D']}
                tintColor={'#fff'}
              />
            }>
            <Download token={this.state.title} />

            {!this.state.refreshing && <TransactionHistory token={this.state.title} />}
          </Animated.ScrollView>
        </Animated.View>
      )
    }

    return (
      <Animated.View style={[commonStyles.tileContainer, {paddingTop: 40}]}>
        <Toast goTo={this.goToTwoFactor} />
        {this.state.showHeader && (
          <Animated.View
            style={[styles.GradiantHeaderCenterBox, {paddingTop: Platform.OS === 'ios' ? 26 : 0}]}>
            <TouchableOpacity
              style={{
                marginBottom: 190,
                width: 40,
                justifyContent: 'center',
                zIndex: 20,
                position: 'absolute',
                left: 6,
                top: Platform.OS === 'ios' ? 24 : 0,
              }}
              onPress={() => this.props.navigation.goBack()}>
              <Image
                source={require('../../imgs/backToSettings.png')}
                style={{
                  height: 24,
                  width: 30,
                  //opacity: 0.85,
                }}
              />
            </TouchableOpacity>

            <Image
              source={tokenPic}
              style={[styles.tokenPic, {height: logoHeight, width: logoWidth}]}
            />
            <TextBold style={[styles.displayValueText, {fontSize: displayValueFontSize}]}>
              {displayValue}
            </TextBold>
            <TextBold
              style={styles.displayAmountText}>{`${displayAmount} ${this.state.title}`}</TextBold>

            {!simplePage && (
              <WalletDetailTransfer
                loanAmount={this.props.loanData?.amount}
                handleDeposit={this.handleDeposit}
                creatingWallet={this.state.creatingWallet}
                handleWithdraw={this.handleWithdraw}
                awaitingLiquidation={this.state.awaitingLiquidation}
                disabledWithdrawal={disabledWithdrawal}
                isStabilized={isStabilized}
                showPendingWithdrawal={showPendingWithdrawal}
                amount={amount}
                xrpInit={this.state.xrpInit}
                title={this.state.title}
                noAddress={!hasWallet?.address}
                user={this.props.user}
                loanData={this.props.loanData}
              />
            )}
          </Animated.View>
        )}
        {this.props.loanData?.scheduledPayments?.length > 0 && (
          <View style={{marginBottom: 40}}></View>
        )}
        <UpholdLink
          upholdLinked={this.state.upholdToken}
          toggleUphold={this.toggleUphold}
          balance={this.state.upholdBalance}
          title={this.state.title}
          updateToken={this.updateToken}
          hideUphold={hideUphold}
          upholdScrolled={this.state.upholdScrolled}
          getUpholdCards={this.getUpholdCards}
        />
        {this.state.upholdScrolled && (
          <View style={{overflow: 'hidden'}}>
            <Animated.View
              style={{
                height: 250,
                width: 300,
                zIndex: 100,
                transform: [{translateY: this.upholdScroll}],
                alignItems: 'center',
              }}>
              <TouchableOpacity
                disabled={this.state.upholdBalance == '0.00'}
                onPress={() => this.showUpholdModal()}>
                <Card
                  marginTop={10}
                  cardWidth={280}
                  style={{
                    backgroundColor: '#00FFBD',
                    opacity: this.state.upholdBalance == '0.00' ? 0.3 : 1,
                    flexDirection: 'row',
                  }}>
                  <Image
                    source={require('../../imgs/upholdTransfer.png')}
                    style={{
                      height: 40,
                      width: 40,
                      marginRight: 10,
                      marginLeft: 6,
                    }}
                  />
                  <TextBold
                    style={{
                      height: 30,
                      fontSize: 18,
                      marginTop: 5,
                      color: '#fff',
                    }}>
                    Transfer Assets To Salt
                  </TextBold>
                </Card>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => this.sendToUphold()}>
                <Card
                  marginTop={-4}
                  cardWidth={280}
                  style={{backgroundColor: '#00FFBD', flexDirection: 'row'}}>
                  <Image
                    source={require('../../imgs/upholdVisit.png')}
                    style={{
                      height: 40,
                      width: 40,
                      marginRight: 10,
                      marginLeft: 6,
                    }}
                  />
                  <TextBold
                    style={{
                      height: 30,
                      fontSize: 18,
                      marginTop: 5,
                      color: '#fff',
                    }}>
                    Go To Uphold Website
                  </TextBold>
                </Card>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => this.unlinkUphold()}>
                <Card
                  marginTop={-4}
                  cardWidth={280}
                  style={{backgroundColor: '#00FFBD', flexDirection: 'row'}}>
                  <Image
                    source={require('../../imgs/unlinkUphold.png')}
                    style={{
                      height: 24,
                      width: 24,
                      marginRight: 18,
                      marginLeft: 14,
                    }}
                  />
                  <TextBold
                    style={{
                      height: 30,
                      fontSize: 18,
                      marginTop: 5,
                      color: '#fff',
                    }}>
                    Unlink Uphold Account
                  </TextBold>
                </Card>
              </TouchableOpacity>
            </Animated.View>
          </View>
        )}
        {this.state.title == 'XRP' && (
          <View>
            <TextReg style={{textAlign: 'center', width: 300, marginTop: 10}}>
              Support for new deposits of XRP are paused. Please reach out to {handleSupport()} with
              any questions.
            </TextReg>
          </View>
        )}
        {this.state.title == 'DASH' && (
          <View>
            <TextReg style={{textAlign: 'center', width: 300, marginTop: 10}}>
              Support for new deposits of DASH are paused. Please reach out to {handleSupport()}{' '}
              with any questions.
            </TextReg>
          </View>
        )}
        {this.state.title == 'DOGE' && (
          <View>
            <TextReg style={{textAlign: 'center', width: 300, marginTop: 10}}>
              Support for new deposits of DOGE are paused. Please reach out to {handleSupport()}{' '}
              with any questions.
            </TextReg>
          </View>
        )}
        {this.state.title == 'PAXG' && (
          <View>
            <TextReg style={{textAlign: 'center', width: 300, marginTop: 10}}>
              Support for new deposits of PAXG are paused. Please reach out to {handleSupport()}{' '}
              with any questions.
            </TextReg>
          </View>
        )}
        {this.state.showDisabled && (
          <TextReg
            style={{
              color: '#fff',
              marginTop: 10,
              fontSize: 20,
              textAlign: 'center',
              paddingHorizontal: 16,
            }}>
            Withdrawals are currently disabled on this account. Please contact {handleSupport()} for
            more information.
          </TextReg>
        )}
        {this.state.singleDisabled && (
          <TextReg
            style={{
              color: '#fff',
              marginTop: 10,
              fontSize: 17,
              textAlign: 'center',
              width: 340,
            }}>
            Deposits for ${this.state.title} are currently unavailable. Please contact{' '}
            {handleSupport()} with any questions.
          </TextReg>
        )}
        {!this.state.upholdScrolled && this.state.showTxHistory && (
          <Animated.ScrollView
            style={[
              styles.swiperContainer,
              {
                marginTop: 10,
                opacity: this.txOpacity,
              },
            ]}
            contentContainerStyle={{alignItems: 'center'}}
            refreshControl={
              <RefreshControl
                refreshing={this.state.refreshing}
                onRefresh={this.getTransactionHistory}
                colors={['#28283D']}
                tintColor={'#fff'}
              />
            }>
            <NotVerified
              toggleVerifiedModal={this.toggleVerifiedModal}
              showPinScreen={this.props.showPinScreen}
              verifiedModalVisable={this.state.verifiedModalVisable}
              openVerifyLink={this.openVerifyLink}
            />
            <Download token={this.state.title} />
            {showPendingWithdrawal && (
              <ShowPendingWithdrawal
                pendingWithdrawalObj={pendingWithdrawalObj}
                currency={currency}
                cancelPending={this.state.cancelPending}
                cancelWithdrawal={this.cancelWithdrawal}
              />
            )}
            {!this.state.refreshing && <TransactionHistory token={this.state.title} />}
          </Animated.ScrollView>
        )}
        <WithdrawStack
          showWithdraw={this.state.showWithdraw || this.state.showPurchaseSaltStack}
          collateral={collateral}
          title={this.state.title}
          price={this.state.price}
          closeWithdraw={this.closeWithdraw}
          showPurchaseSaltStack={this.state.showPurchaseSaltStack}
        />
        <DepositScreen
          showDeposit={this.state.showDeposit}
          collateral={collateral}
          title={this.state.title}
          price={this.state.price}
          closeDeposit={this.closeDeposit}
          xrpInit={this.state.xrpInit}
        />
        <Uphold
          showUphold={this.state.showUphold}
          showPinScreen={this.props.showPinScreen}
          showUpholdModal={this.showUpholdModal}
          balance={this.state.upholdBalance}
          title={this.state.title}
          upholdCardId={this.state.upholdCardId}
          getUpholdCards={this.getUpholdCards}
        />
      </Animated.View>
    )
  }
}

CollateralDetail.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  //headerTintColor: '#FFF',
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    //shadowColor: 'transparent',
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  accountRef: state.auth.account.ref,
  loanData: state.user.loanData,
  user: state.user.user,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
  refreshCount: state.user.refreshCount,
  WebService: state.auth.WebService,
  launchDarkly: state.launchDarkly,
  externalSlideFn: state.user.externalSlideFn,
})

export default connect(mapStateToProps)(CollateralDetail)
