import React from 'react'
import { View, Image, TouchableOpacity } from 'react-native'

import { Card, TextReg } from '../../components'

import styles from './styles'

const ShowPendingWithdrawal = props => {
  let amount = ''
  if (props.pendingWithdrawalObj) {
    amount = props.pendingWithdrawalObj.outputs[0].amount
  }
  return (
    <Card>
      <View style={styles.showPendingWithdrawalBox}>
        <View style={{ flexDirection: 'row' }}>
          <TextReg>{`Pending  `}</TextReg>
          <TextReg>{`-${amount} $${props.currency}`}</TextReg>
        </View>

        {props.cancelPending ? (
          <Image
            source={require('../../imgs/loadingDots.gif')}
            style={styles.loadingDots}
          />
        ) : (
          <TouchableOpacity
            onPress={() =>
              props.cancelWithdrawal(
                props.pendingWithdrawalObj.id,
                !!props.pendingWithdrawalObj.txData?.isVirtual
              )
            }
          >
            <TextReg style={styles.cancelButtonText}>Cancel</TextReg>
          </TouchableOpacity>
        )}
      </View>
    </Card>
  )
}

export default ShowPendingWithdrawal
