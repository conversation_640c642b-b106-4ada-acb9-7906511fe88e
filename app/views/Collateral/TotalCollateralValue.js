import React from 'react'
import { View } from 'react-native'

import { TextReg, TextBold } from '../../components'

const TotalCollateralValue = props => (
  <View style={{ alignItems: 'center' }}>
    <TextBold
      style={{ fontSize: 42, marginTop: 2, color: '#fff', marginBottom: 10 }}
    >{`$${props.displayCollateralValue}`}</TextBold>
    <TextReg style={{ fontSize: 18, marginBottom: 26, color: '#fff' }}>
      Total Collateral Value
    </TextReg>
  </View>
)

export default TotalCollateralValue
