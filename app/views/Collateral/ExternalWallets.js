import React, { Component } from 'react'
import { View, ScrollView, Image, RefreshControl, TouchableOpacity, Dimensions, Animated } from 'react-native'
import { connect } from 'react-redux'

import SwiperFlatList from 'react-native-swiper-flatlist'
import <PERSON><PERSON><PERSON>iew from 'lottie-react-native'

import styles from './styles'
import { TextReg, TextBold, Button } from '../../components'
import { getTokenPic, getZaboAccountLogo } from '../../util/tokens'
import { cryptoNameMap } from '../../util/enumerables'
import { sendEvent } from '../../store/analytics/analytics.actions'

import { numberWithCommas } from '../../util/helpers'
import TransferModal from './Zabo/TransferModal'
import zaboDefault from '../../imgs/zabo/zaboDefault.png'

const { width: ScreenWidth } = Dimensions.get('window')

class ExternalWallets extends Component {
  constructor() {
    super()
    this.state = {
      slider: 'wallets',
      sliderState: 0,
      editView: false,
      showTransfer: false
    }
    this.xOffset = 0
    this.scrollValue = new Animated.Value(0)
  }

  componentDidMount() {
    this.props.dispatch(sendEvent(`External Wallets`))
  }

  beginTransferFlow = () => {
    this.setState({ showTransfer: true })
  }

  closeTransfer = () => {
    this.setState({ showTransfer: false })
  }

  gotoZaboDetail = (item) => {
    this.props.navigation.navigate('ZaboDetail', { wallet: item })
  }

  toggleSlider = (slider) => {
    if (slider == 'wallets') {
      this.refs.swiper._scrollToIndex(0)
      this.setState({ sliderState: 0 })
    } else {
      this.refs.swiper._scrollToIndex(1)
      this.setState({ sliderState: 1 })
    }
  }

  onScrollEnd = (index) => {
    this.setState({ sliderState: index })
    //this.showLessPayments(index)
  }

  onScroll = (e) => {
    this.xOffset = e.nativeEvent.contentOffset.x

    Animated.timing(this.scrollValue, {
      toValue: this.xOffset,
      duration: 0
    }).start()
  }

  addWalletFlow = () => {
    this.props.navigation.navigate('ZaboAddWallet')
  }

  toggleEditView = () => {
    if (!this.state.editView) {
      this.toggleSlider('wallets')
    }
    this.setState({ editView: !this.state.editView })
  }

  removeZaboWallet = (a) => {
    const id = a.id
    this.props.WebService.deleteZaboWallet(id)
      .then((res) => {
        console.log('zabo delete res', res)
        this.props.getZaboWallets()
      })
      .catch((err) => {
        console.log('zabo wallets err', err)
      })
  }

  goToDepositScreen = (ticker) => {
    this.setState({ showTransfer: false })
    this.props.navigation.navigate('ZaboDeposit', { ticker })
  }

  unusableTicker = (ticker) => 0

  sortByUsable = (a, b) => {
    const showTickerA = a[0].ticker
    const showTickerB = b[0].ticker

    const fullNameA = cryptoNameMap.get(showTickerA.toLowerCase()) || showTickerA
    const fullNameB = cryptoNameMap.get(showTickerB.toLowerCase()) || showTickerB

    if (fullNameA == showTickerA && fullNameB != showTickerB) {
      return 1
    }
    if (fullNameA != showTickerA && fullNameB == showTickerB) {
      return -1
    }
    return 0
  }

  render() {
    let zaboWallets = this.props.zaboWallets
    const assetsTicker = []
    const assets = []
    let showTotal = 0

    //for apple testers
    if (Object.keys(zaboWallets).length === 0) {
      zaboWallets = []
    }

    console.log('apple test 2', zaboWallets)
    const showExternals = zaboWallets.map((a, k) => {
      let total = 0
      a.balances.map((b) => {
        total += Number(b.fiat_value)
        const assetB = { ...b, wallet: a.provider.name }
        if (!assetsTicker.includes(b.ticker)) {
          assetsTicker.push(b.ticker)
          assets.push([assetB])
        } else {
          const assetIndex = assetsTicker.indexOf(b.ticker)
          assets[assetIndex].push(assetB)
        }
      })
      showTotal += total
      total = numberWithCommas(total.toFixed(2))
      let formattedName = a?.provider?.name
      formattedName = formattedName.charAt(0).toUpperCase() + formattedName.slice(1)

      return (
        <View key={k} style={{ flexDirection: 'row', alignSelf: 'stretch' }}>
          <TouchableOpacity
            style={{
              alignSelf: 'stretch',
              width: this.state.editView ? ScreenWidth - 90 : ScreenWidth - 20,
              borderRadius: 14,
              backgroundColor: '#48485A',
              flexDirection: 'row',
              height: 60,
              alignItems: 'center',
              justifyContent: 'space-between',
              paddingLeft: 20,
              paddingRight: 20,
              marginBottom: 12
            }}
            onPress={() => this.gotoZaboDetail(a)}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Image
                source={getZaboAccountLogo(a?.provider?.name)}
                style={{
                  backgroundColor: '#FFF',
                  height: 40,
                  width: 40,
                  borderRadius: 20,
                  marginRight: 10
                }}
              />
              <TextReg style={{ color: '#FFF', fontSize: 19 }}>{formattedName}</TextReg>
            </View>
            <TextReg style={{ color: '#FFF', fontSize: 19 }}>${total}</TextReg>
          </TouchableOpacity>
          {this.state.editView && (
            <TouchableOpacity onPress={() => this.removeZaboWallet(a)}>
              <View
                style={{
                  height: 60,
                  width: 60,
                  borderRadius: 14,
                  backgroundColor: '#00FFBD',
                  marginLeft: 10,
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <Image
                  source={require('../../imgs/zabo/trashWhite.png')}
                  style={{
                    height: 24,
                    width: 21
                  }}
                />
              </View>
            </TouchableOpacity>
          )}
        </View>
      )
    })
    const walletsNum = zaboWallets.length
    const noWallets = !(zaboWallets.length > 0)

    const assetsNum = assetsTicker.length

    const showAssets = assets.map((a, k) => {
      let assetTotal = 0
      let assetFiatTotal = 0
      let showTicker = ''
      const showLogo = a[0].logo || null
      a.map((b) => {
        showTicker = b.ticker
        assetTotal += Number(b.amount)
        assetFiatTotal += Number(b.fiat_value)
      })

      return (
        <TouchableOpacity
          key={k}
          style={{
            alignSelf: 'stretch',
            width: this.state.editView ? ScreenWidth - 90 : ScreenWidth - 20,
            borderRadius: 14,
            backgroundColor: '#48485A',
            flexDirection: 'row',
            height: 60,
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingLeft: 20,
            paddingRight: 20,
            marginBottom: 12
          }}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            {showLogo ? (
              <Image
                source={{ uri: showLogo }}
                style={{
                  backgroundColor: '#FFF',
                  height: 40,
                  width: 40,
                  borderRadius: 20,
                  marginRight: 10
                }}
              />
            ) : (
              <Image
                source={zaboDefault}
                style={{
                  backgroundColor: '#FFF',
                  height: 40,
                  width: 40,
                  borderRadius: 20,
                  marginRight: 10
                }}
              />
            )}

            <TextReg style={{ color: '#FFF', fontSize: 19 }}>{showTicker}</TextReg>
          </View>
          <View style={{ alignItems: 'flex-end' }}>
            <TextReg style={{ color: '#FFF', fontSize: 17 }}>
              {`$${numberWithCommas(assetFiatTotal.toFixed(2))}`}
            </TextReg>
            <TextReg style={{ color: '#AFAFAF', fontSize: 14 }}>{`${numberWithCommas(assetTotal.toFixed(8))}`}</TextReg>
          </View>
        </TouchableOpacity>
      )
    })

    const sortUsableAssets = assets.sort(this.sortByUsable)

    const showDepositAssets = sortUsableAssets.map((a, k) => {
      const showTicker = a[0].ticker
      let tokenPic = getTokenPic(showTicker.toLowerCase())
      let rowOpacity = 1
      const fullName = cryptoNameMap.get(showTicker.toLowerCase()) || showTicker
      let goToDeposit = this.goToDepositScreen
      if (fullName == showTicker) {
        //return null
        tokenPic = { uri: a[0].logo }
        rowOpacity = 0.3
        goToDeposit = this.unusableTicker
      }

      return (
        <TouchableOpacity
          key={k}
          style={{
            alignSelf: 'stretch',
            backgroundColor: '#FFF',
            height: 70,
            flexDirection: 'row',
            marginLeft: 30,
            marginRight: 30,
            borderBottomWidth: 1,
            borderColor: '#000',
            alignItems: 'center',
            opacity: rowOpacity
          }}
          onPress={() => goToDeposit(showTicker)}
        >
          <Image source={tokenPic} style={{ height: 44, width: 44, marginLeft: 16 }} />
          <TextBold style={{ marginRight: 6, marginLeft: 16, fontSize: 22 }}>{fullName}</TextBold>
          <TextReg style={{ fontSize: 22, color: '#999' }}>{showTicker}</TextReg>
        </TouchableOpacity>
      )
    })

    let accountsPlural = 'ACCOUNTS'
    if (walletsNum == 1) {
      accountsPlural = 'ACCOUNT'
    }
    const walletsLength = walletsNum * 72
    const assetsLength = assetsNum * 72
    const heightInterpolated = this.scrollValue.interpolate({
      inputRange: [0, ScreenWidth],
      outputRange: [walletsLength, assetsLength]
    })

    const xInterpolated = this.scrollValue.interpolate({
      inputRange: [0, ScreenWidth],
      outputRange: [0, ScreenWidth / 2]
    })

    const editOpacity = this.scrollValue.interpolate({
      inputRange: [0, ScreenWidth],
      outputRange: [1, 0]
    })

    const editHeight = this.scrollValue.interpolate({
      inputRange: [0, ScreenWidth],
      outputRange: [36, 0]
    })

    showTotal = numberWithCommas(showTotal.toFixed(2))

    return (
      <View style={{ alignSelf: 'stretch', alignItems: 'center' }}>
        <TransferModal
          showTransfer={this.state.showTransfer}
          closeTransfer={this.closeTransfer}
          showPinScreen={this.props.showPinScreen}
          goToDepositScreen={this.goToDepositScreen}
          showDepositAssets={showDepositAssets}
        />
        <ScrollView
          style={styles.swiperContainer}
          contentContainerStyle={{ alignSelf: 'stretch', alignItems: 'center' }}
          refreshControl={
            <RefreshControl
              refreshing={this.props.zaboLoading}
              onRefresh={this.props.getZaboWallets}
              colors={['#28283D']}
              tintColor={['#777']}
            />
          }
        />
        <View
          style={{
            alignItems: 'center',
            alignSelf: 'stretch',
            marginLeft: 10,
            marginRight: 10
          }}
        >
          <TextBold
            style={{
              fontSize: 42,
              marginTop: 2,
              color: '#fff',
              marginBottom: 10
            }}
          >{`$${showTotal}`}</TextBold>
          <TextReg style={{ fontSize: 15, marginBottom: 26, color: '#fff' }}>
            *cannot be used as collateral or earn interest
          </TextReg>
          {!noWallets && (
            <Button
              onPress={() => this.beginTransferFlow()}
              style={{
                alignSelf: 'stretch',
                marginLeft: 10,
                marginRight: 10,
                marginBottom: 12
              }}
            >
              <TextBold style={{ color: '#FFF' }}>DEPOSIT TO SALT</TextBold>
            </Button>
          )}
        </View>

        {this.props.zaboLoading ? (
          <View
            style={{
              alignSelf: 'stretch',
              alignItems: 'center',
              marginTop: 20
            }}
          >
            <LottieView
              ref={(animation) => {
                this.animation = animation
              }}
              style={{
                width: 40,
                height: 40,
                opacity: 0.9,
                marginTop: 0,
                marginBottom: 8
              }}
              source={require('../../imgs/lotti/loading-white-dots.json')}
              autoPlay
            />
          </View>
        ) : (
          <>
            {noWallets ? (
              <View
                style={{
                  backgroundColor: '#48485A',
                  borderRadius: 14,
                  margin: 10,
                  alignItems: 'center'
                }}
              >
                <Image
                  source={require('../../imgs/zabo/wallet.png')}
                  style={{
                    height: 40,
                    width: 40,
                    marginBottom: 18,
                    marginTop: 20
                  }}
                />
                <TextBold
                  style={{
                    color: '#FFF',
                    fontSize: 17,
                    marginBottom: 4
                  }}
                >
                  {`What's your cumulative wealth?`}
                </TextBold>
                <TextReg
                  style={{
                    color: '#FFF',
                    fontSize: 17,
                    textAlign: 'center',
                    marginBottom: 18,
                    marginLeft: 20,
                    marginRight: 20
                  }}
                >
                  View all of your crypto assets in one place by linking them to the SALT platform.
                </TextReg>
                <Button
                  onPress={() => this.addWalletFlow()}
                  style={{
                    alignSelf: 'stretch',
                    marginLeft: 10,
                    marginRight: 10,
                    marginBottom: 10
                  }}
                >
                  <TextBold style={{ color: '#FFF' }}>CONNECT ACCOUNTS</TextBold>
                </Button>
              </View>
            ) : (
              <>
                <View
                  style={{
                    alignSelf: 'stretch',
                    borderBottomWidth: 1,
                    borderColor: '#00FFBD',
                    marginTop: 30,
                    marginBottom: 8
                  }}
                >
                  <View
                    style={{
                      alignSelf: 'stretch',
                      flexDirection: 'row'
                    }}
                  >
                    <TouchableOpacity
                      style={{
                        flex: 1,
                        alignItems: 'center',
                        paddingBottom: 12
                      }}
                      onPress={() => this.toggleSlider('wallets')}
                    >
                      <TextReg style={{ color: '#FFF', fontSize: 20 }}>{`${walletsNum} ${accountsPlural}`}</TextReg>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={{
                        flex: 1,
                        alignItems: 'center',
                        paddingBottom: 12
                      }}
                      onPress={() => this.toggleSlider('assets')}
                    >
                      <TextReg style={{ color: '#FFF', fontSize: 20 }}>{`${assetsNum} ASSETS`}</TextReg>
                    </TouchableOpacity>
                  </View>
                  <Animated.View
                    style={{
                      alignSelf: 'stretch',
                      flexDirection: 'row',
                      height: 4,
                      transform: [{ translateX: xInterpolated }]
                    }}
                  >
                    <View
                      style={{
                        height: 4,
                        backgroundColor: '#00FFBD',
                        width: ScreenWidth / 2
                      }}
                    />
                    <View style={{ height: 4, width: ScreenWidth / 2 }} />
                  </Animated.View>
                </View>

                <Animated.View
                  style={{
                    alignSelf: 'stretch',
                    alignItems: 'flex-end',
                    opacity: editOpacity,
                    height: editHeight,
                    justifyContent: 'center'
                  }}
                >
                  <TouchableOpacity onPress={() => this.toggleEditView()}>
                    <TextReg
                      style={{
                        color: '#FFF',
                        fontSize: 18,
                        marginRight: 10,
                        marginBottom: -4
                      }}
                    >
                      Edit
                    </TextReg>
                  </TouchableOpacity>
                </Animated.View>

                <Animated.View
                  style={{
                    height: heightInterpolated,
                    marginBottom: 16
                  }}
                >
                  <SwiperFlatList
                    ref="swiper"
                    onMomentumScrollEnd={this.onScrollEnd}
                    index={0}
                    scrollEventThrottle={16}
                    onScroll={this.onScroll}
                  >
                    <View
                      style={{
                        flex: 1,
                        width: ScreenWidth - 20,
                        marginLeft: 10,
                        marginRight: 10,
                        marginTop: 10,
                        marginBottom: 8
                      }}
                    >
                      {showExternals}
                    </View>
                    <View
                      style={{
                        flex: 1,
                        width: ScreenWidth - 20,
                        marginLeft: 10,
                        marginRight: 10,
                        marginTop: 10,
                        marginBottom: 8
                      }}
                    >
                      {showAssets}
                    </View>
                  </SwiperFlatList>
                </Animated.View>
                <Button
                  onPress={() => this.addWalletFlow()}
                  style={{
                    alignSelf: 'stretch',
                    marginLeft: 10,
                    marginRight: 10,
                    marginBottom: 30
                  }}
                >
                  <TextBold style={{ color: '#FFF' }}>CONNECT ACCOUNT</TextBold>
                </Button>
              </>
            )}
          </>
        )}
      </View>
    )
  }
}

ExternalWallets.navigationOptions = ({ navigation }) => ({
  title: null,
  header: <View style={{ height: 40, position: 'absolute' }} />
})

const mapStateToProps = (state) => ({
  WebService: state.auth.WebService,
  loanData: state.user.loanData,
  accountRef: state.auth.account.ref,
  user: state.user.user,
  showPinScreen: state.auth.pinScreen
})

export default connect(mapStateToProps)(ExternalWallets)
