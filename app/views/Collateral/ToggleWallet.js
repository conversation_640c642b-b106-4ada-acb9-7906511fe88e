import React from 'react'
import { View, TouchableOpacity } from 'react-native'

import { TextReg } from '../../components'

const ToggleWallet = props => (
  <View
    style={{
      flexDirection: 'row',
      width: 250,
      justifyContent: 'space-between',
      marginTop: 6,
      marginBottom: 18,
    }}
  >
    <TouchableOpacity
      onPress={() => props.toggleWallet('platform')}
      style={{
        borderWidth: 1,
        borderColor: '#FFF',
        borderRadius: 14,
        width: 120,
        height: 32,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: props.walletType == 'platform' ? '#FFF' : '#28283D',
      }}
    >
      <TextReg
        style={{
          color: props.walletType == 'platform' ? '#28283D' : '#FFF',
        }}
      >
        On Platform
      </TextReg>
    </TouchableOpacity>
    <TouchableOpacity
      onPress={() => props.toggleWallet('external')}
      style={{
        borderWidth: 1,
        borderColor: '#FFF',
        borderRadius: 14,
        width: 120,
        height: 32,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: props.walletType == 'external' ? '#FFF' : '#28283D',
      }}
    >
      <TextReg
        style={{
          color: props.walletType == 'external' ? '#28283D' : '#FFF',
        }}
      >
        External
      </TextReg>
    </TouchableOpacity>
  </View>
)

export default ToggleWallet
