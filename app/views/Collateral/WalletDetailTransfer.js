import React from 'react'
import {View} from 'react-native'

import {Card, Button, TextReg} from '../../components'

import styles from './styles'

const WalletDetailTransfer = props => {
  console.log('WalletDetailTransfer', props.loanData)

  const bannedToken =
    props.title === 'XRP' ||
    props.title === 'DASH' ||
    props.title === 'DOGE' ||
    props.title === 'PAXG'

  let withdrawalDisabled =
    props.showPendingWithdrawal ||
    props.amount === '0' ||
    !props.amount ||
    props.awaitingLiquidation ||
    props.isStabilized ||
    !props.user.walletsEnabled //|| props.disabledWithdrawal;

  if (props.loanData?.scheduledPayments?.length > 0) {
    withdrawalDisabled = true
  }

  let depositDisabled = bannedToken || props.awaitingLiquidation || !props.user.walletsEnabled

  return (
    <>
      <View
        style={{
          backgroundColor: 'transparent',
          padding: 5,
        }}>
        <Card style={styles.GradiantHeaderCard}>
          <View style={styles.collateralDetailButtonsBox}>
            <Button
              disabled={depositDisabled}
              isLoading={props.creatingWallet}
              onPress={() => props.handleDeposit(props.title)}
              style={styles.depositButton}>
              Deposit
            </Button>
            <Button
              disabled={withdrawalDisabled}
              onPress={() => props.handleWithdraw()}
              style={styles.withdrawButton}>
              Send
            </Button>
          </View>
        </Card>
      </View>
      {props.loanData?.scheduledPayments?.length > 0 && (
        <TextReg style={{color: '#E5705A', marginTop: -14, textAlign: 'center', width: 280}}>
          {`Due to scheduled ACH payment, withdrawals are currently disabled.`}
        </TextReg>
      )}
    </>
  )
}

export default WalletDetailTransfer
