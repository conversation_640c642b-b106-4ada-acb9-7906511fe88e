import React, {Component} from 'react'
import {View, Image, TouchableOpacity, Linking, Platform, Animated} from 'react-native'
import {connect} from 'react-redux'

import AsyncStorage from '@react-native-async-storage/async-storage'

import {TextBold, Card} from '../../../components'
import config from '../../../config.json'
import {askingForPermissions} from '../../../store/auth/auth.actions'
import {updateUpholdHide} from '../../../store/user/user.actions'
import {showToast} from '../../../store/notifications/notifications.actions'

class UpholdLink extends Component {
  constructor(props) {
    super(props)
    this.state = {
      showCopiedButton: false,
      upholdState: null,
      hideUphold: false,
    }
    this.spinValue = new Animated.Value(0)
  }

  componentDidMount() {
    const {title} = this.props
    Linking.addEventListener('url', this.handleOpenURL)
  }

  componentWillUnmount() {
    //Linking.removeEventListener('url', this.handleOpenURL);
  }

  handleOpenURL = event => {
    let code = event.url.split('&')[0]
    code = code.split('=')[1]
    const state = event.url.split('=')[2]
    if (this.state.upholdState == state) {
      //if android
      let os = 'android'
      if (Platform.OS === 'ios') {
        os = 'ios'
      }
      console.log('handleOpenURL', code, os)
      this.props.WebService.requestUpholdToken({code, os})
        .then(res => {
          //const upholdToken = res.data.token
          AsyncStorage.setItem(`UpholdToken-${this.props.user.primaryEmail}`, 'true')
          this.props.updateToken(true)
          this.props.getUpholdCards()
        })
        .catch(err => {
          console.log('uphold token err', err)
        })
    }
  }

  loginUphold = () => {
    this.props.WebService.getUpholdState()
      .then(res => {
        const {state} = res.data
        this.setState({upholdState: state})
        this.openUpholdSite(state)
      })
      .catch(err => {
        this.setState({error: 'Connection problem with servers.'})
      })
  }

  openUpholdSite = state => {
    if (!this.props.user.mfaEnabled) {
      this.props.dispatch(showToast(true))
      return
    }

    this.props.dispatch(askingForPermissions(true))

    let clientId = config.uphold.android.id
    if (Platform.OS === 'ios') {
      clientId = config.uphold.ios.id
    }

    Linking.openURL(`https://uphold.com/authorize/${clientId}?scope=cards:read%20transactions:withdraw%20user:read&state=${state}`)
  }

  closeUphold = () => {
    //get string
    let upholdClosedList = ''
    AsyncStorage.getItem(`UpholdHide-${this.props.user.primaryEmail}`).then(res => {
      if (res && res.includes(this.props.title)) {
        //remove from string
        const upholdClosedList = res.replace(`-${this.props.title}`, '')
      } else {
        //add to string
        if (res) {
          upholdClosedList = res + `-${this.props.title}`
        } else {
          upholdClosedList = res + `-${this.props.title}`
        }
      }

      let upholdHideTokens = this.props.loanData.collaterals.map(a => {
        if (upholdClosedList.includes(a.currency)) {
          return a.currency
        }
        return null
      })

      upholdHideTokens = upholdHideTokens.filter(a => a)

      //save string
      AsyncStorage.setItem(`UpholdHide-${this.props.user.primaryEmail}`, upholdClosedList)

      this.setState({hideUphold: true})
      this.props.dispatch(updateUpholdHide(upholdHideTokens))
    })
  }

  rotateArrow = () => {
    Animated.timing(this.spinValue, {
      toValue: this.props.upholdScrolled ? 0 : 1,
      duration: 300,
      useNativeDriver: true,
    }).start()
  }

  toggleUphold = () => {
    this.rotateArrow()
    this.props.toggleUphold()
  }

  render() {
    const {title} = this.props

    const fixedBalance = Number(this.props.balance).toFixed(7)

    if (
      !(title === 'BTC' || title === 'LTC' || title === 'BCH' || title === 'ETH' || title === 'XRP' || title === 'DASH') ||
      this.state.hideUphold ||
      this.props.hideUphold
    ) {
      return <View style={{height: 40, width: 40}} />
    }

    const spin = this.spinValue.interpolate({
      inputRange: [0, 1],
      outputRange: ['0deg', '90deg'],
    })

    return (
      <Animated.View
        style={{
          flexDirection: 'row',
          alignSelf: 'stretch',
          alignItems: 'flex-end',
          justifyContent: 'center',
          zIndex: 102,
        }}>
        {!this.props.upholdLinked && <View style={{width: 40, height: 40}} />}
        <Animated.View>
          <Card marginTop={50} cardWidth={this.props.upholdLinked ? 226 : 260}>
            <TouchableOpacity
              style={{
                height: 45,
                width: this.props.upholdLinked ? 224 : 234,
                marginTop: -5,
                marginBottom: -5,
              }}
              onPress={() => (this.props.upholdLinked ? this.toggleUphold() : this.loginUphold())}>
              {this.props.upholdLinked ? (
                <View
                  style={{
                    height: 45,
                    width: this.props.upholdLinked ? 224 : 234,
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  <Image
                    source={require('../../../imgs/upholdLinkedLogo.png')}
                    style={{
                      height: 45,
                      width: 55,
                      //opacity: 0.85,
                    }}
                  />

                  <TextBold
                    style={{
                      fontSize: 18,
                      width: 140,
                      textAlign: 'center',
                      opacity: fixedBalance == '0.0000000' ? 0.7 : 1,
                    }}>{`${fixedBalance} ${this.props.title}`}</TextBold>
                  <Animated.Image
                    source={require('../../../imgs/rightArrow.png')}
                    style={{
                      height: 20,
                      width: 20,
                      transform: [{rotate: spin}],
                    }}
                  />
                </View>
              ) : (
                <Image
                  source={require('../../../imgs/upholdLong.png')}
                  style={{
                    height: 45,
                    width: 234,
                    //opacity: 0.85,
                  }}
                />
              )}
            </TouchableOpacity>
          </Card>
        </Animated.View>
        {!this.props.upholdLinked && (
          <TouchableOpacity onPress={() => this.closeUphold()}>
            <View
              style={{
                width: 40,
                height: 40,
                marginBottom: 21,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Image
                source={require('../../../imgs/closeX.png')}
                style={{
                  height: 20,
                  width: 20,
                  opacity: 0.7,
                }}
              />
            </View>
          </TouchableOpacity>
        )}
      </Animated.View>
    )
  }
}

const mapStateToProps = state => ({
  loanData: state.user.loanData,
  WebService: state.auth.WebService,
  showPinScreen: state.auth.pinScreen,
  user: state.user.user,
})

export default connect(mapStateToProps)(UpholdLink)
