import React, {Component} from 'react'
import {View, Image, TouchableOpacity, Modal, Linking, TextInput} from 'react-native'
import {connect} from 'react-redux'
import {TextReg, TextBold, HeaderButtons} from '../../../components'
import {askingForPermissions} from '../../../store/auth/auth.actions'

import {BigNumber} from 'bignumber.js'

class Uphold extends Component {
  constructor(props) {
    super(props)
    this.state = {
      showCopiedButton: false,
      upholdState: null,
      confirmTransfer: false,
      loading: false,
      success: false,
      transferAmount: '',
      rateLimitError: false,
    }
  }

  visitUpholdSite = () => {
    this.props.dispatch(askingForPermissions(true))
    Linking.openURL(`https://uphold.com/`)
  }

  handleAmount = transferAmount => {
    this.setState({transferAmount})
  }

  handle2fa = text => {
    this.setState({totp: text})
  }

  handleSubmitAmount = () => {
    if (Number(this.state.transferAmount) > 0 && Number(this.state.transferAmount) < Number(this.props.balance)) {
      this.setState({confirmTransfer: true, error: false, success: false})
    }
  }

  editAmount = () => {
    this.setState({confirmTransfer: false, error: false, success: false})
  }

  handleSubmitTransfer = () => {
    if (this.state.loading || this.state.success || this.state.rateLimitError) return
    this.setState({
      loading: true,
      error: false,
      success: false,
      rateLimitError: false,
    })

    const data = {
      totp: this.state.totp,
      currency: this.props.title,
      cardId: this.props.upholdCardId,
      amount: this.state.transferAmount,
    }
    this.props.WebService.upholdTransaction(data)
      .then(res => {
        this.setState({success: true, loading: false})
        setTimeout(() => {
          this.props.getUpholdCards()
          this.closeModal()
        }, 7000)
      })
      .catch(err => {
        if (err.data.body.error === 'Rate limit reached') {
          this.setState(
            {
              setupLoading: false,
              rateLimitError: true,
              timer: 30,
              loading: false,
            },
            () => (this.intervalHandle = setInterval(this.tick, 1000)),
          )
        } else {
          this.setState({error: true, loading: false})
        }
      })
  }

  tick = () => {
    if (this.state.timer === 1) {
      this.setState({timer: 0, rateLimitError: false})
      clearInterval(this.intervalHandle)
    } else {
      this.setState({timer: this.state.timer - 1})
    }
  }

  closeModal = () => {
    //clear form first
    this.setState({transferAmount: '', totp: '', confirmTransfer: false})
    this.props.showUpholdModal()
  }

  render() {
    const showBalance = Number(this.props.balance).toFixed(8)

    const showTransferAmount = Number(Number(this.state.transferAmount).toFixed(7)) //converts back to number to remove trailing 0s

    const upholdFees = {
      BTC: {
        networkFee: '0.0003',
        min: '0.00001',
      },
      LTC: {
        networkFee: '0.003',
        min: '0.25',
      },
      ETH: {
        networkFee: '0.005',
        min: '0.001',
      },
      BCH: {
        networkFee: '0.0003',
        min: '0.00001',
      },
      DASH: {
        networkFee: '0.0013',
        min: '0.0001',
      },
      XRP: {
        networkFee: '0.1',
        min: '0.001',
      },
    }

    const titleUSD = `${this.props.title}-USD`
    const price = this.props.tokenPrices[titleUSD].price

    let showUpholdFee = new BigNumber(3).dividedBy(price).toFormat(7)
    if (this.props.title === 'XRP') {
      showUpholdFee = 0
    }
    let showNetworkFee = 0
    //let showMinAmount = 0

    let addedFees
    let maxWithdrawal

    if (
      this.props.title == 'BTC' ||
      this.props.title == 'LTC' ||
      this.props.title == 'ETH' ||
      this.props.title == 'BCH' ||
      this.props.title == 'XRP' ||
      this.props.title == 'DASH'
    ) {
      showNetworkFee = upholdFees[this.props.title].networkFee

      addedFees = Number(showNetworkFee) + Number(showUpholdFee)
      addedFees = addedFees.toFixed(7)

      maxWithdrawal = Number(showBalance - addedFees).toFixed(8)

      if (maxWithdrawal < Number(upholdFees[this.props.title].min)) {
        maxWithdrawal = false
      }
    }

    const validAmount =
      Number(this.state.transferAmount) < maxWithdrawal && Number(this.state.transferAmount) >= Number(upholdFees[this.props.title].min)

    return (
      <Modal animationType="slide" visible={this.props.showUphold && !this.props.showPinScreen} onRequestClose={() => this.closeModal()}>
        <HeaderButtons dark title={'Uphold Transfer'} close={() => this.closeModal()} />
        <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
          <View style={{width: 244, marginTop: -45}}>
            <Image
              source={require('../../../imgs/upholdLogoBig.png')}
              style={{
                height: 31,
                width: 22,
              }}
            />
          </View>
          {this.state.confirmTransfer ? (
            <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
              <TextReg
                style={{
                  width: 240,
                  marginTop: 40,
                  marginBottom: 30,
                  textAlign: 'center',
                }}>
                Are you sure you want to transfer assets to SALT?
              </TextReg>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginBottom: 14,
                }}>
                <View
                  style={{
                    width: 130,
                    height: 80,
                    borderWidth: 1,
                    alignItems: 'center',
                    paddingTop: 16,
                    borderRadius: 14,
                    backgroundColor: '#555',
                    borderColor: '#555',
                  }}>
                  <Image
                    source={require('../../../imgs/upholdWide.png')}
                    style={{
                      height: 25,
                      width: 77,
                      marginBottom: 6,
                    }}
                  />

                  <TextBold
                    style={{
                      color: '#fff',
                    }}>{`${showTransferAmount} ${this.props.title}`}</TextBold>
                </View>
                <Image
                  source={require('../../../imgs/rightArrowPurple.png')}
                  style={{
                    height: 30,
                    width: 30,
                    marginLeft: 4,
                    marginRight: 4,
                  }}
                />

                <View
                  style={{
                    width: 130,
                    height: 80,
                    borderWidth: 1,
                    alignItems: 'center',
                    paddingTop: 16,
                    borderRadius: 14,
                    backgroundColor: '#555',
                    borderColor: '#555',
                  }}>
                  <TextBold
                    style={{
                      marginBottom: 6,
                      color: '#fff',
                      fontSize: 18,
                    }}>{`SALT`}</TextBold>
                  <TextBold
                    style={{
                      color: '#fff',
                    }}>{`${this.props.title} Wallet`}</TextBold>
                </View>
              </View>
              <View style={{width: 300, marginTop: 30}}>
                <TextReg>SALT Two-Factor</TextReg>
                <TextInput
                  style={{
                    height: 50,
                    borderRadius: 6,
                    width: 300,
                    backgroundColor: '#fff',
                    borderColor: '#00FFBD',
                    borderWidth: 1,
                    marginTop: 4,
                    padding: 6,
                    paddingLeft: 14,
                    fontFamily: 'Europa-Regular',
                    fontSize: 20,
                    color: '#e6e6e6',
                  }}
                  keyboardType={'numeric'}
                  onChangeText={this.handle2fa}
                  returnKeyType={'done'}
                  value={this.state.totp}
                  onSubmitEditing={this.handleSubmitTransfer}
                  keyboardAppearance="dark"
                />
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  marginTop: 14,
                  marginBottom: 24,
                }}>
                <TouchableOpacity onPress={() => this.editAmount()}>
                  <View
                    style={{
                      marginTop: 20,
                      borderRadius: 14,
                      borderColor: '#00FFBD',
                      borderWidth: 2,
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignSelf: 'center',
                      zIndex: 5,
                      minWidth: 120,
                      height: 50,
                      fontSize: 22,
                      paddingVertical: 8,
                      paddingHorizontal: 21,
                      backgroundColor: '#fff',
                      opacity: this.state.success ? 0.7 : 1,
                    }}>
                    <TextBold style={{color: '#00FFBD', fontSize: 19}}>Back</TextBold>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => this.handleSubmitTransfer()}>
                  <View
                    style={{
                      marginTop: 20,
                      marginLeft: 10,
                      borderRadius: 14,
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignSelf: 'center',
                      zIndex: 5,
                      minWidth: 120,
                      height: 50,
                      fontSize: 22,
                      paddingVertical: 8,
                      paddingHorizontal: 21,
                      backgroundColor: this.state.rateLimitError ? '#777' : '#00FFBD',
                      opacity: this.state.success || this.state.rateLimitError ? 0.7 : 1,
                    }}>
                    <TextBold style={{color: '#fff', fontSize: 19}}>Confirm</TextBold>
                  </View>
                </TouchableOpacity>
              </View>
              {this.state.error && (
                <TextReg
                  style={{
                    fontSize: 17,
                    color: '#e5705a',
                  }}>{`Error with Uphold transfer`}</TextReg>
              )}
              {this.state.rateLimitError && (
                <View
                  style={{
                    alignSelf: 'stretch',
                    justifyContent: 'center',
                    marginBottom: 20,
                    alignItems: 'center',
                  }}>
                  <TextReg
                    style={{
                      color: '#de4a2e',
                      fontSize: 17,
                      textAlign: 'center',
                      width: 300,
                    }}>
                    {`Timeout due to 3 invalid attempts, please try again in ${this.state.timer} seconds.`}
                  </TextReg>
                </View>
              )}
              {this.state.success && (
                <View
                  style={{
                    backgroundColor: '#34e89e',
                    height: 60,
                    width: 300,
                    flexDirection: 'row',
                    borderRadius: 14,
                    alignItems: 'center',
                    paddingLeft: 10,
                    paddingRight: 10,
                    marginTop: 10,
                  }}>
                  <Image
                    source={require('../../../imgs/check-box.png')}
                    style={{
                      height: 30,
                      width: 30,
                      marginRight: 10,
                    }}
                  />
                  <TextReg
                    style={{
                      fontSize: 18,
                      color: '#fff',
                    }}>{`Successfully initiated transfer`}</TextReg>
                </View>
              )}
            </View>
          ) : (
            <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
              <TextReg style={{width: 280, marginTop: 30, textAlign: 'center'}}>
                Transfer assets from your Uphold wallet to our internal wallet, the easy way
              </TextReg>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  marginTop: 40,
                }}>
                <TextBold
                  style={{
                    fontSize: 18,
                    width: 140,
                    textAlign: 'right',
                    marginRight: 5,
                  }}>{`Current Balance:`}</TextBold>
                <TextBold
                  style={{
                    fontSize: 19,
                    width: 160,
                  }}>{`${showBalance} ${this.props.title}`}</TextBold>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                  opacity: 0.75,
                }}>
                <TextBold
                  style={{
                    fontSize: 16,
                    width: 140,
                    textAlign: 'right',
                    marginRight: 5,
                  }}>{`Network Fees:`}</TextBold>
                <TextBold
                  style={{
                    fontSize: 16,
                    width: 160,
                  }}>{`${showNetworkFee} ${this.props.title}`}</TextBold>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                  opacity: 0.75,
                }}>
                <TextBold
                  style={{
                    fontSize: 16,
                    width: 140,
                    textAlign: 'right',
                    marginRight: 5,
                  }}>{`Uphold Fees:`}</TextBold>
                <TextBold
                  style={{
                    fontSize: 16,
                    width: 160,
                  }}>{`${showUpholdFee} ${this.props.title}`}</TextBold>
              </View>
              <View
                style={{
                  width: 180,
                  height: 12,
                  borderColor: '#777',
                  borderBottomWidth: 1,
                }}
              />
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  marginTop: 10,
                }}>
                <TextBold
                  style={{
                    fontSize: 18,
                    width: 140,
                    textAlign: 'right',
                    marginRight: 5,
                  }}>{`Max Withdrawl:`}</TextBold>
                <TextBold
                  style={{
                    fontSize: 18,
                    width: 160,
                    color: !maxWithdrawal ? '#e5705a' : 'black',
                  }}>
                  {!maxWithdrawal ? `Insufficient Funds` : `${maxWithdrawal} ${this.props.title}`}
                </TextBold>
              </View>

              <View style={{marginTop: 40, width: 280}}>
                <TextReg>{`How much ${this.props.title} do you want to transfer?`}</TextReg>
                <TextInput
                  style={{
                    height: 50,
                    borderRadius: 6,
                    width: 280,
                    backgroundColor: '#fff',
                    borderColor: '#00FFBD',
                    borderWidth: 1,
                    marginTop: 4,
                    padding: 6,
                    paddingLeft: 14,
                    fontFamily: 'Europa-Regular',
                    fontSize: 20,
                    color: '#e6e6e6',
                  }}
                  onChangeText={this.handleAmount}
                  keyboardType={'numeric'}
                  returnKeyType={'done'}
                  value={this.state.transferAmount}
                  onSubmitEditing={() => {
                    if (validAmount) {
                      this.handleSubmitAmount()
                    }
                  }}
                  keyboardAppearance="dark"
                />
              </View>

              <TouchableOpacity disabled={!validAmount} onPress={this.handleSubmitAmount}>
                <View
                  style={{
                    marginTop: 20,
                    borderRadius: 14,
                    justifyContent: 'center',
                    alignItems: 'center',
                    alignSelf: 'center',
                    zIndex: 5,
                    minWidth: 220,
                    height: 50,
                    fontSize: 22,
                    paddingVertical: 8,
                    paddingHorizontal: 21,
                    backgroundColor: validAmount ? '#00FFBD' : '#777',
                    opacity: validAmount ? 1 : 0.5,
                  }}>
                  <TextBold style={{color: '#fff', fontSize: 19}}>Next</TextBold>
                </View>
              </TouchableOpacity>
            </View>
          )}

          <View />
        </View>
      </Modal>
    )
  }
}

const mapStateToProps = state => ({
  loanData: state.user.loanData,
  WebService: state.auth.WebService,
  showPinScreen: state.auth.pinScreen,
  user: state.user.user,
  tokenPrices: state.user.prices,
})

export default connect(mapStateToProps)(Uphold)
