import React, {Component} from 'react'
import {View, Text, FlatList, Image, TouchableOpacity, Linking, Animated} from 'react-native'
import {connect} from 'react-redux'
import {numberWithCommas, dig, splitByCamelCase} from '../../util/helpers'
import {TextReg, TextBold, Card} from '../../components'
import {askingForPermissions} from '../../store/auth/auth.actions'

import styles from './styles'

class TransactionHistory extends Component {
  constructor(props) {
    super(props)
    this.state = {
      refreshing: true,
      transactions: [],
      error: false,
      showTxId: null,
    }

    this.internalTxIdHeight = new Animated.Value(-40)
  }

  static getDerivedStateFromProps(props, state) {
    const transactions = props.loanData.collaterals.filter(a => a.currency === props.token)[0]
      ?.transactions
    if (!transactions) {
      return {
        ...state,
        transactions: [],
      }
    }
    return {
      ...state,
      transactions,
    }
  }

  amountXPrice = (amount, price = '0') => {
    if (price === 0) price = '0'
    price = price.toString()
    const newPrice = price.replace('$', '')
    return amount * newPrice
  }

  colorReason = item => {
    if (item.type === 'received') {
      return '#00FFBD'
    }
    return '#E5705A'
  }

  formatDate = oldDate => {
    const newDate = new Date(oldDate)
    const options = {
      hour: '2-digit',
      minute: '2-digit',
    }
    const localDate = newDate.toLocaleDateString('en-US', options)
    return localDate.replace(',', ' -')
  }

  updatedStatusWord = text => (text === 'detected' ? 'unconfirmed' : text)

  openTransactionScanner = item => {
    console.log('openTransactionScanner', item)
    if (item?.txid) {
      const scannerUrl = this.getScannerUrl(this.props.token, item.txid)
      Linking.canOpenURL(scannerUrl).then(supported => {
        if (supported) {
          this.props.dispatch(askingForPermissions(true))
          Linking.openURL(scannerUrl)
        }
      })
    } else if (item?.id) {
      if (this.state.showTxId == item.id) {
        this.setState({showTxId: null})
      } else {
        this.setState({showTxId: item.id})
      }

      /*
      let txNumber = null
      this.state.transactions.map((a, k) => {
        if (a.id == item.id) {
          txNumber = k
        }
      })
      console.log('txNumber', txNumber)
      */

      /*
      if (this.internalTxIdHeight.__getValue() == 0) {
        Animated.timing(this.internalTxIdHeight, {
          toValue: -40,
          duration: 100,
          useNativeDriver: true,
        }).start()
      } else {
        Animated.timing(this.internalTxIdHeight, {
          toValue: 0,
          duration: 100,
          useNativeDriver: true,
        }).start()
      }
      */

      //this.setState({ showVirtualTx: item.id })
    }
  }

  getScannerUrl = (token, txId) => {
    console.log('getScannerUrl', token, txId)
    //const token = this.props.route.params.title
    switch (token) {
      case 'BTC':
        return `https://live.blockcypher.com/btc/tx/${txId}`
      case 'BCH':
        return `https://explorer.bitcoin.com/bch/tx/${txId}`
      case 'LTC':
        return `https://live.blockcypher.com/ltc/tx/${txId}`
      case 'DASH':
        return `https://chainz.cryptoid.info/dash/tx.dws?${txId}.htm`
      case 'ETH':
        return `https://etherscan.io/tx/${txId}`
      case 'SALT':
        return `https://etherscan.io/tx/${txId}`
      case 'TUSD':
        return `https://etherscan.io/tx/${txId}`
      case 'USDC':
        return `https://etherscan.io/tx/${txId}`
      case 'DOGE':
        return `https://dogechain.info/tx/${txId}`
      case 'XRP':
        return `https://xrpscan.com/tx/${txId}`
      default:
        return `https://etherscan.io/tx/${txId}`
    }
  }

  sortByDate = (a, b) => {
    if (a.date > b.date) {
      return -1
    } else if (b.date > a.date) {
      return 1
    }
    return 0
  }

  getPosNeg = item => {
    if (item.type == 'received') {
      return ''
    }
    if (
      item.reason === 'withdrawal' ||
      item.reason === 'liquidation' ||
      item.reason === 'extraction'
    ) {
      return '-'
    }

    return ''
  }

  removeUnderScore = string => string.replace('_', ' ')

  render() {
    const pricesArr = Object.entries(this.props.prices)
    const tokenPrice = pricesArr.filter(a => a[0].includes(this.props.token))[0][1].price

    if (this.state.transactions.length === 0) {
      return (
        <Card marginTop={14}>
          <TextReg
            style={{
              margin: 12,
              textAlign: 'center',
              fontSize: 16,
              width: 280,
            }}>
            {`No transaction history available. Please make a deposit.`}
          </TextReg>
        </Card>
      )
    }

    let transactions = this.state.transactions.filter(a => a.reason !== 'commingle')

    transactions = transactions.sort(this.sortByDate)
    transactions = transactions.map(a => {
      if (a.reason == 'extraction') {
        a.reason = 'liquidation'
      }
      return {
        ...a,
        showId: a.id == this.state.showTxId,
        confirmed: a.status == 'confirmed',
      }
    })

    const showTxs = transactions.map((item, k) => (
      <TouchableOpacity key={k} onPress={() => this.openTransactionScanner(item)}>
        <View
          style={[
            styles.transactionHistoryItemRow,
            {
              zIndex: 51,
              backgroundColor: '#3D3D50',
              borderBottomWidth: k == transactions?.length - 1 ? 0 : 0.5,
            },
          ]}>
          <View style={styles.transactionHistoryLeft}>
            <TextReg style={[styles.transactionHistoryTitle, {color: this.colorReason(item)}]}>
              {this.removeUnderScore(splitByCamelCase(item.reason))}
            </TextReg>
            <TextReg style={styles.transactionHistorySubTitle}>
              {this.formatDate(item.date)}
            </TextReg>
          </View>
          <View style={styles.transactionHistoryRight}>
            {item.showId ? (
              <View style={{marginTop: 2, alignItems: 'center'}}>
                <TextBold style={{fontSize: 18, color: '#00FFBD'}}>
                  {item.id.substring(0, 8).toUpperCase()}
                </TextBold>
                <TextReg>{item.confirmed ? 'Confirmed' : 'Unconfirmed'}</TextReg>
              </View>
            ) : (
              <View style={styles.transactionHistoryRightInfo}>
                <TextBold style={[styles.transactionHistoryTitle, {color: this.colorReason(item)}]}>
                  {this.getPosNeg(item)}

                  {numberWithCommas(parseFloat(Number(item.amount).toFixed(8)).toString())}
                </TextBold>
              </View>
            )}
            <View style={styles.transactionArrowBox}>
              <Image
                source={require('../../imgs/rightArrow.png')}
                style={styles.rightArrowIconTxHistory}
              />
            </View>
          </View>
        </View>
      </TouchableOpacity>
    ))

    return (
      <Card style={{paddingBottom: -8, paddingTop: 0}}>
        {this.state.error && (
          <View style={styles.errorBox}>
            <Text style={styles.errorText}>Error Getting Transactions</Text>
          </View>
        )}
        {!dig(this.state.transactions, 'length') && !this.state.refreshing ? (
          <View style={styles.noNotifsBox}>
            <Text style={styles.noNotifsText}>No Transactions</Text>
          </View>
        ) : (
          <View style={styles.collateralDetailList}>{showTxs}</View>
        )}
      </Card>
    )
  }
}

/*
<Animated.View
  style={[
    {
      zIndex: 50,
      alignSelf: 'stretch',
      height: 40,
      borderWidth: 1,
      borderColor: 'blue',
      alignItems: 'center',
      transform: [{ translateY: this.internalTxIdHeight }],
    },
  ]}
>
  <TextReg>{`id: ${item.id}`}</TextReg>
</Animated.View>
*/

const mapStateToProps = state => ({
  prices: state.user.prices,
  accountRef: state.auth.account.ref,
  loanData: state.user.loanData,
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(TransactionHistory)
