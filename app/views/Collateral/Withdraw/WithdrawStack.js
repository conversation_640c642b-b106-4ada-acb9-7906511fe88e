import React, { Component } from 'react';
import { Platform, Modal, Animated, Dimensions, ScrollView, AppState } from 'react-native';
import { connect } from 'react-redux';

import { cryptoNameMap } from '../../../util/enumerables';
import { dig } from '../../../util/helpers';

import { Background, HeaderButtons } from '../../../components';
import WithdrawInput from './WithdrawInput';
import WithdrawAddress from './WithdrawAddress';
import ConfirmWithdraw from './ConfirmWithdraw';
import WithdrawSuccess from './WithdrawSuccess';
import PurchaseSaltAddress from './PurchaseSaltAddress';
import PurchseSaltDeposit from './PurchseSaltDeposit';

const { width: ScreenWidth } = Dimensions.get('window');

class WithdrawStack extends Component {
  constructor (props) {
    super(props);
    this.state = {
      amount: '',
      value: '',
      toAddress: '',
      btcAddress: '',
      btcAmount: '',
      feeEstimate: null,
      withdrawError: false,
      withdrawLoading: false,
      addressType: 'Personal',
      addressError: false,
      //withdrawButtonActive,
      //withdrawalExceedsBalance,
      page: 0,
      appState: AppState.currentState,
      rateLimitError: false,
      timer: 0
    };
    this.scrollValueArr = [];
    this.scrollValueArr[1] = new Animated.Value(ScreenWidth);
    this.scrollValueArr[2] = new Animated.Value(ScreenWidth);
    this.scrollValueArr[3] = new Animated.Value(ScreenWidth);
    this.scrollValueArr[4] = new Animated.Value(ScreenWidth);
    this.scrollValueArr[5] = new Animated.Value(ScreenWidth);

    AppState.addEventListener('change', this.handleAppStateChange);
  }

	handleAppStateChange = async (nextAppState) => {
	  if (this.state.appState.match(/inactive|background/) && nextAppState === 'active') {
	    for (let i = 1; i <= this.state.page; i++) {
	      this.showScreen(i);
	    }
	  }
	  this.setState({ appState: nextAppState });
	}

	showScreen = (num) => {
	  Animated.timing(this.scrollValueArr[num], {
	    toValue: 0,
	    duration: 300,
	    useNativeDriver: true
	  }).start(() => {
	    this.setState({ page: num });
	  });
	}

	hideScreen = (num) => {
	  Animated.timing(this.scrollValueArr[num], {
	    toValue: ScreenWidth,
	    duration: 300,
	    useNativeDriver: true
	  }).start(() => {
	    this.setState({ page: num - 1 });
	  });
	}

	updateAmount = (amount) => {
	  this.setState({ amount });
	}

	updateValue = (value) => {
	  this.setState({ value });
	}

	updateAddress = (toAddress) => {
	  this.setState({ toAddress });
	}

	getGasCost = () => {
	  const token = this.props.collateral.currency;
	  this.setState({ addressError: false });

	  this.props.WebService.getGasCost(token).then((res) => {
	    if (res.data.amount) {
	      this.setState({ feeEstimate: res.data.amount });
	    }
	  });
	  this.showScreen(1);
	}

	submitWithdrawal = (twoFactor) => {
	  this.setState({
	    withdrawError: false,
	    withdrawLoading: true,
	    rateLimitError: false
	  });

	  const withdrawData = {
	    amount: this.state.amount,
	    currency: this.props.title,
	    toAddress: this.state.toAddress,
	    twoFactorCode: twoFactor
	  };

	  this.props.WebService.withdraw(withdrawData)
	    .then((res) => {
	      //this.setState({ withdrawButtonActive: true, withdrawProcessing: false })

	      const successData = {
	        //params: this.props.route.params,
	        //amountPrice: params.amountPrice,
	        ...withdrawData
	      };
	      this.setState({ withdrawLoading: false });
	      this.showScreen(3);

	      //this.props.navigation.navigate('WithdrawSuccess', successData)
	    })

	    .catch((err) => {
	      if (err.data.body.error === 'Invalid 2-Step Verification Code.') {
	        this.setState({
	          withdrawLoading: false,
	          withdrawError: 'Invalid 2FA Code'
	        });
	      } else if (err.data.body.error === 'Rate limit reached') {
	        this.setState(
	          {
	            rateLimitError: true,
	            timer: 30,
	            withdrawLoading: false
	          },
	          () => (this.intervalHandle = setInterval(this.tick, 1000))
	        );
	      } else {
	        this.setState({
	          withdrawError: 'Withdrawal Error',
	          withdrawLoading: false
	        });
	      }
	    });
	}

	tick = () => {
	  if (this.state.timer === 1) {
	    this.setState({ timer: 0, rateLimitError: false });
	    clearInterval(this.intervalHandle);
	  } else {
	    this.setState({ timer: this.state.timer - 1 });
	  }
	}

	updateSaltSendAddress = (btcAddress, btcAmount) => {
	  this.setState({ btcAddress, btcAmount });
	}

	render () {
	  const { showWithdraw, collateral, title, price, closeWithdraw, showPinScreen, showPurchaseSaltStack } = this.props;

	  const token = cryptoNameMap.get(title.toLowerCase());
	  let withdrawTokentitle = `Withdraw ${token}`;
	  if (showPurchaseSaltStack) {
	    withdrawTokentitle = `Buy SALT`;
	  }

	  return (
	    <Modal animationType="slide" visible={showWithdraw && !showPinScreen} onRequestClose={() => ({})}>
	      <Background />
	      <ScrollView
	        style={{
	          alignSelf: 'stretch',
	          flexDirection: 'column',
	          paddingTop: Platform.OS === 'ios' ? 20 : 0
	        }}
	        contentContainerStyle={{
	          justifyContent: 'flex-start',
	          alignItems: 'center'
	        }}
	      >
	        <HeaderButtons title={withdrawTokentitle} close={closeWithdraw} />
	        <WithdrawAddress
	          token={token}
	          tokenTitle={title}
	          title={`Withdraw ${token}`}
	          close={() => closeWithdraw()}
	          next={() => this.getGasCost()}
	          amount={this.state.amount}
	          value={this.state.value}
	          updateAddress={this.updateAddress}
	          toAddress={this.state.toAddress}
	          addressError={this.state.addressError}
	        />
	        <WithdrawInput
	          scrollValue={this.scrollValueArr[1]}
	          showWithdraw={showWithdraw}
	          collateral={collateral}
	          title={`Withdraw ${token}`}
	          tokenTitle={title}
	          price={price}
	          amount={this.state.amount}
	          close={() => closeWithdraw()}
	          next={() => this.showScreen(2)}
	          updateAmount={this.updateAmount}
	          updateValue={this.updateValue}
	          showPurchaseSaltStack={showPurchaseSaltStack}
	          goBack={() => this.hideScreen(1)}
	          feeEstimate={this.state.feeEstimate}
	        />

	        <ConfirmWithdraw
	          scrollValue={this.scrollValueArr[2]}
	          title={`Confirm Withdrawal`}
	          close={() => closeWithdraw()}
	          goBack={() => this.hideScreen(2)}
	          next={this.submitWithdrawal}
	          withdrawError={this.state.withdrawError}
	          token={token}
	          tokenTitle={title}
	          amount={this.state.amount}
	          value={this.state.value}
	          toAddress={this.state.toAddress}
	          withdrawLoading={this.state.withdrawLoading}
	          addressType={this.state.addressType}
	          feeEstimate={this.state.feeEstimate}
	          rateLimitError={this.state.rateLimitError}
	          timer={this.state.timer}
	        />
	        <WithdrawSuccess
	          scrollValue={this.scrollValueArr[3]}
	          title={withdrawTokentitle}
	          close={closeWithdraw}
	          withdrawButtonActive={false}
	          withdrawalExceedsBalance={false}
	          tokenTitle={title}
	          amount={this.state.amount}
	          value={this.state.value}
	          toAddress={this.state.toAddress}
	        />
	        <PurchaseSaltAddress
	          scrollValue={this.scrollValueArr[4]}
	          token={token}
	          tokenTitle={title}
	          title={withdrawTokentitle}
	          close={() => closeWithdraw()}
	          goBack={() => this.hideScreen(4)}
	          next={() => this.showScreen(5)}
	          amount={this.state.amount}
	          value={this.state.value}
	          withdrawButtonActive
	          updateAddress={this.updateAddress}
	          updateSaltSendAddress={this.updateSaltSendAddress}
	        />
	        <PurchseSaltDeposit
	          scrollValue={this.scrollValueArr[5]}
	          token={token}
	          tokenTitle={title}
	          title={withdrawTokentitle}
	          close={closeWithdraw}
	          goBack={() => this.hideScreen(5)}
	          amount={this.state.amount}
	          value={this.state.value}
	          withdrawButtonActive
	          updateAddress={this.updateAddress}
	          btcAddress={this.state.btcAddress}
	          btcAmount={this.state.btcAmount}
	        />
	      </ScrollView>
	    </Modal>
	  );
	}
}

const mapStateToProps = (state) => ({
  loanData: state.user.loanData,
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen
});

export default connect(mapStateToProps)(WithdrawStack);
