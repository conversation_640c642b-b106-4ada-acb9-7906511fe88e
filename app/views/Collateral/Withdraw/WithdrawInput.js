import React, {Component} from 'react'
import {
  View,
  Image,
  TouchableOpacity,
  Clipboard,
  TextInput,
  Keyboard,
  AppState,
  Dimensions,
  Animated,
} from 'react-native'
import {connect} from 'react-redux'
import styles from '../styles'
import {validateTokenAddress} from '../../../util/helpers'
import {askingForPermissions} from '../../../store/auth/auth.actions'
import {screenView} from '../../../store/analytics/analytics.actions'
import switchPng from '../../../imgs/switch.png'
import infoImg from '../../../imgs/info.png'
import {
  TextReg,
  Keypad,
  Button,
  TextBold,
  Background,
  HeaderButtons,
} from '../../../components'
import {BigNumber} from 'bignumber.js'

const {height: ScreenHeight} = Dimensions.get('window')

class WithdrawInput extends Component {
  constructor(props) {
    super(props)
    this.state = {
      amountInput: '' || props.amount,
      fiatInput: '0',
      appState: AppState.currentState,
      scannerModalVisable: false,
      toggleCryptoFiat: false,
      twoFactor: '',
      twoFactorModalVisible: false,
      withdrawAddressValid: true,
      withdrawButtonActive: false,
    }
    this.inputs = {}
  }

  componentDidMount() {
    const {tokenTitle} = this.props
    this.props.dispatch(screenView(`Collateral Withdraw - ${tokenTitle}`))
    this.setState({withdrawType: tokenTitle})
    AppState.addEventListener('change', this.handleAppStateChange)

    if (this.state.amountInput !== '') {
      this.handleAmountInput(this.props.amount)
    }
  }

  componentWillUnmount() {
    //AppState.removeEventListener('change', this.handleAppStateChange)
    this.setState({scannerModalVisable: false, twoFactorModalVisible: false})
  }

  handleAppStateChange = async nextAppState => {
    if (
      this.state.appState.match(/inactive|background/) &&
      nextAppState === 'active'
    ) {
      const clipboardCopy = await Clipboard.getString()
      if (clipboardCopy.length === 6 && !isNaN(clipboardCopy)) {
        this.setState({twoFactor: clipboardCopy})
      }
    }
    this.setState({appState: nextAppState})
  }

  handleAmountInput = input => {
    input = input.toString()
    input = input.replace(/[, ]+/g, '')
    let price = this.props.price || '0'
    price = price.replace('$', '')
    this.state.toggleCryptoFiat
      ? this.setState({
          amountInput:
            Number(input) > 0
              ? new BigNumber(input).dividedBy(price).toFormat(8)
              : '0.00000000',
          fiatInput: input,
          withdrawButtonActive: Number(input) > 0,
        })
      : this.setState({
          amountInput: input,
          fiatInput:
            input > 0
              ? new BigNumber(input).multipliedBy(price).toFormat(2)
              : '0.00',
          withdrawButtonActive: Number(input) > 0,
        })
  }

  handleCryptoFiatToggle = () => {
    this.setState({
      toggleCryptoFiat: !this.state.toggleCryptoFiat,
    })
  }

  focusNextField = field => {
    if (this.inputs[field]) {
      this.inputs[field].focus()
    }
  }

  hideKeyboard = () => {
    Keyboard.dismiss()
  }

  handleAddressInput = text => {
    this.setState({withdrawAddress: text, withdrawAddressValid: true})
  }

  openQRScanner = () => {
    this.toggleScannerModal()
  }

  toggleScannerModal = () => {
    this.props.dispatch(askingForPermissions(!this.state.scannerModalVisable))
    this.setState({scannerModalVisable: !this.state.scannerModalVisable})
  }

  validateAddress = withdrawAddress => {
    const token = this.props.tokenTitle
    if (validateTokenAddress(token, this.state.withdrawAddress)) {
      this.setState({withdrawAddressValid: false})
      return false
    }
    return true
  }

  validateAndShowModal = () => {
    if (this.validateAddress()) {
      this.handleNext()
    }
  }

  updateAddressFromQR = address => {
    this.setState({withdrawAddress: address})
  }

  handleNext = () => {
    let price = this.props.price || '0'
    price = price.replace('$', '')
    let amountPrice = Number(this.state.amountInput) * price
    amountPrice = amountPrice.toFixed(2)

    const withdrawData = {
      //params: this.props.params,
      title: this.props.tokenTitle,
      price: this.props.price,
      amount: this.state.amountInput,
      currency: this.state.withdrawType,
      toAddress: this.state.withdrawAddress,
      twoFactorCode: this.state.twoFactor,
      amountPrice,
    }

    this.props.navigation.navigate('WithdrawConfirm', withdrawData)
  }

  handleEnterText = text => {
    const {amountInput, fiatInput, toggleCryptoFiat} = this.state

    let currentText = toggleCryptoFiat
      ? fiatInput >= 0
        ? fiatInput
        : ''
      : amountInput >= 0
      ? amountInput
      : ''

    if (text === 'back' && currentText > 0) {
      currentText = currentText.substring(0, currentText.length - 1)
    } else {
      currentText = `${currentText}${text}`
    }

    this.handleAmountInput(currentText)
  }

  render() {
    const {tokenTitle} = this.props
    const {amountInput, fiatInput, toggleCryptoFiat} = this.state

    //const price = this.props.dispatch(getTokenPrice(params.title))
    const titleUSD = `${tokenTitle}-USD`
    const price = this.props.tokenPrices[titleUSD].price

    let amount = '0'
    if (this.props.loanData.collaterals && tokenTitle) {
      amount = this.props.loanData.collaterals.filter(
        a => a.currency === tokenTitle,
      )[0].projectedBalance
    }

    if (tokenTitle === 'XRP' && amount > 25) {
      amount -= 25
    }

    let withdrawalExceedsBalance =
      this.state.amountInput && Number(this.state.amountInput) > Number(amount)

    const withdrawalLess15 =
      this.state.amountInput &&
      Number(this.state.amountInput) * price < Number(15)

    if (this.props.showPurchaseSaltStack) {
      withdrawalExceedsBalance = false
    }

    const showAmount = amountInput.replace(/[, ]+/g, '')
    const showFiat = fiatInput.replace(/[, ]+/g, '')

    return (
      <Animated.View
        style={{
          position: 'absolute',
          height: ScreenHeight - 52,
          left: 0,
          right: 0,
          transform: [{translateX: this.props.scrollValue}],
          zIndex: 190,
        }}>
        <Background />
        <HeaderButtons
          goBack={this.props.goBack}
          title={this.props.title}
          close={this.props.close}
        />
        <View style={{flex: 1, justifyContent: 'space-between'}}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              paddingLeft: 25,
            }}>
            <View style={styles.withdrawHeader}>
              <TouchableOpacity
                onPress={() => this.focusNextField('amountInput')}>
                <View style={styles.withdrawAmountBox}>
                  <TextInput
                    style={styles.withdrawAmountInput}
                    value={
                      toggleCryptoFiat
                        ? showFiat >= 0
                          ? showFiat
                          : ''
                        : showAmount >= 0
                        ? showAmount
                        : ''
                    }
                    underlineColorAndroid="transparent"
                    blurOnSubmit
                    returnKeyType={'done'}
                    onSubmitEditing={() => this.hideKeyboard()}
                    onFocus={() => this.hideKeyboard()}
                    placeholder={'0.00'}
                    selectionColor="#FFF"
                    placeholderTextColor="rgba(255, 255, 255, 0.7)"
                    keyboardAppearance="dark"
                  />
                  <TextReg
                    style={
                      this.state.amountInput === '' ||
                      this.state.fiatInput === ''
                        ? styles.withdrawAmountTextDetailActive
                        : styles.withdrawAmountTextDetail
                    }>
                    {toggleCryptoFiat ? 'USD' : tokenTitle}
                  </TextReg>
                </View>
              </TouchableOpacity>
              <View style={styles.withdrawPriceBox}>
                <TextReg style={styles.withdrawPriceText}>
                  {toggleCryptoFiat
                    ? `${
                        amountInput !== '' ? amountInput : '0.00'
                      } ${tokenTitle}`
                    : `$${fiatInput} USD`}
                </TextReg>
                <TextReg style={styles.balanceErrorText}>
                  {withdrawalExceedsBalance
                    ? 'Withdrawal Amount Exceeds Balance'
                    : ''}
                  {withdrawalLess15 && 'Withdrawal Amount Less Than $15'}
                </TextReg>
              </View>

              <View style={styles.withdrawPriceBalanceBox}>
                <TouchableOpacity
                  onPress={() =>
                    this.handleAmountInput(
                      toggleCryptoFiat
                        ? new BigNumber(amount)
                            .multipliedBy(price)
                            .multipliedBy(0.9999999)
                            .toFixed(2)
                        : (
                            Math.floor(amount * 100000000) / 100000000
                          ).toString(),
                    )
                  }
                  style={{flexDirection: 'row', alignItems: 'center'}}>
                  <TextReg style={styles.withdrawPriceBalanceText}>
                    {toggleCryptoFiat
                      ? `Available: $${new BigNumber(amount)
                          .multipliedBy(price)
                          .multipliedBy(0.9999999)
                          .toFormat(2)} USD`
                      : `Available: ${
                          Math.floor(amount * 100000000) / 100000000
                        } ${tokenTitle}`}
                  </TextReg>
                  <Image
                    source={infoImg}
                    style={{
                      height: 14,
                      width: 14,
                      marginLeft: 4,
                    }}
                  />
                </TouchableOpacity>
              </View>
            </View>
            <TouchableOpacity
              onPress={this.handleCryptoFiatToggle}
              style={styles.cryptoFiatToggle}>
              <Image
                source={switchPng}
                style={{
                  height: 25,
                  width: 25,
                }}
              />
            </TouchableOpacity>
          </View>
          <View>
            <Keypad handleEnterText={this.handleEnterText} />
            <Button
              style={{backgroundColor: styles.mintGreen, marginTop: 20}}
              onPress={() => {
                this.props.updateAmount(showAmount)
                this.props.updateValue(showFiat)
                if (
                  !withdrawalExceedsBalance &&
                  !withdrawalLess15 &&
                  this.state.amountInput > 0
                ) {
                  this.props.next()
                }
              }}>
              <TextBold style={{color: '#000', fontSize: 17}}>NEXT</TextBold>
            </Button>
          </View>
        </View>
      </Animated.View>
    )
  }
}

const mapStateToProps = state => ({
  loanData: state.user.loanData,
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
})

export default connect(mapStateToProps)(WithdrawInput)
