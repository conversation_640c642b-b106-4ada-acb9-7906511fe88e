import React, { Component } from 'react'
import { View, Animated, Dimensions, Image } from 'react-native'
import { connect } from 'react-redux'

import WithdrawQRScanner from './WithdrawQRScanner'
const { width: ScreenWidth, height: ScreenHeight } = Dimensions.get('window')

import { But<PERSON>, TextReg, Background, HeaderButtons, TextBold } from '../../../components'
import checkmarkImg from '../../../imgs/checkmark.png'
import { numberWithCommas } from '../../../util/helpers'

import styles from '../styles'

class WithdrawSuccess extends Component {
  constructor(props) {
    super(props)
    this.state = {
      amountInput: '0.00',
      fiatInput: '0',
      scannerModalVisable: false,
      toggleCryptoFiat: false,
      twoFactor: '',
      twoFactorModalVisible: false,
      withdrawAddressValid: true,
      withdrawButtonActive: false
    }
    this.scrollValue = new Animated.Value(ScreenWidth)
  }

  handleUpdateTwoFactor = (text) => {
    this.setState({ twoFactor: text })
  }

  render() {
    const showValue = numberWithCommas(this.props.value)

    return (
      <Animated.View
        style={{
          position: 'absolute',
          height: ScreenHeight,
          left: 0,
          right: 0,
          backgroundColor: '#fff',
          transform: [{ translateX: this.props.scrollValue }],
          zIndex: 196
        }}
      >
        <Background />
        <HeaderButtons goBack={this.props.goBack} title={'Withdrawal Submitted'} close={this.props.close} />

        <WithdrawQRScanner
          toggleScannerModal={this.toggleScannerModal}
          scannerModalVisable={this.state.scannerModalVisable}
          showPinScreen={this.props.showPinScreen}
          updateAddressFromQR={this.updateAddressFromQR}
        />

        <View
          style={{
            alignItems: 'center',
            backgroundColor: '#28283D',
            alignSelf: 'stretch',
            flex: 1
          }}
        >
          <View style={{ alignItems: 'center', marginTop: 30, marginBottom: 40 }}>
            <Image
              source={checkmarkImg}
              style={{
                height: 40,
                width: 40,
                marginBottom: 20
              }}
            />
            <TextBold style={{ color: '#e6e6e6', fontSize: 20, marginBottom: 8 }}>Withdrawal Submitted</TextBold>
            <TextReg
              style={{
                color: '#e6e6e6',
                fontSize: 16,
                width: 300,
                marginBottom: 16
              }}
            >
              We have received your withdrawal request and will notify you within 24 hours via email. Go to your desktop
              dashboard for more information about your withdraw status.
            </TextReg>
            <View style={{ width: ScreenWidth - 80 }}>
              <View style={styles.WithdrawSubmittedRow}>
                <TextReg style={{ color: '#e6e6e6', fontSize: 18 }}>{`Amount (${this.props.tokenTitle})`}</TextReg>
                <TextReg style={{ color: '#e6e6e6', fontSize: 18 }}>
                  {`${this.props.amount} ${this.props.tokenTitle}`}
                </TextReg>
              </View>
              <View style={styles.WithdrawSubmittedRow}>
                <TextReg style={{ color: '#e6e6e6', fontSize: 18 }}>{`Amount (USD)`}</TextReg>
                <TextReg style={{ color: '#e6e6e6', fontSize: 18 }}>{`$${showValue}`}</TextReg>
              </View>
              <View style={styles.WithdrawSubmittedRow}>
                <TextReg style={{ color: '#e6e6e6', fontSize: 18 }}>{`Address`}</TextReg>
                <TextReg
                  style={{
                    color: '#e6e6e6',
                    fontSize: 14,
                    width: 200,
                    textAlign: 'right'
                  }}
                >
                  {`${this.props.toAddress}`}
                </TextReg>
              </View>
            </View>
          </View>

          <Button style={{ backgroundColor: '#00FFBD' }} onPress={() => this.props.close()}>
            <TextBold
              style={{
                color: '#000',
                fontSize: 17,
                marginBottom: -2,
                marginTop: -2
              }}
            >
              RETURN TO WALLET
            </TextBold>
          </Button>
        </View>
      </Animated.View>
    )
  }
}

const mapStateToProps = (state) => ({
  loanData: state.user.loanData,
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen
})

export default connect(mapStateToProps)(WithdrawSuccess)
