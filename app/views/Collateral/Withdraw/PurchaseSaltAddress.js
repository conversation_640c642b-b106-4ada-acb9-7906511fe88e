import React, { Component } from 'react'
import { View, Animated, Dimensions, TouchableOpacity, TextInput, Image, Keyboard } from 'react-native'
import { connect } from 'react-redux'

import { validateTokenAddress } from '../../../util/helpers'

import { updateWallets } from '../../../store/user/user.actions'
import { askingForPermissions } from '../../../store/auth/auth.actions'
import qRcodeScanner from '../../../imgs/qRCodeScannerBlack.png'
import WithdrawQRScanner from './WithdrawQRScanner'

import { Button, TextReg, Background, HeaderButtons, TextBold } from '../../../components'

const { width: ScreenWidth, height: ScreenHeight } = Dimensions.get('window')

import styles from '../styles'

class PurchaseSaltAddress extends Component {
  constructor(props) {
    super(props)
    this.state = {
      amountInput: '0.00',
      fiatInput: '0',
      scannerModalVisable: false,
      toggleCryptoFiat: false,
      twoFactor: '',
      twoFactorModalVisible: false,
      withdrawAddressValid: true,
      withdrawButtonActive: false,
      withdrawAddress: ''
    }
    this.scrollValue = new Animated.Value(ScreenWidth)
  }

  handleAddressInput = (text) => {
    this.setState({ withdrawAddress: text, withdrawAddressValid: true })
  }

  openQRScanner = () => {
    this.toggleScannerModal()
  }

  toggleScannerModal = () => {
    this.props.dispatch(askingForPermissions(!this.state.scannerModalVisable))
    this.setState({ scannerModalVisable: !this.state.scannerModalVisable })
  }

  validateAddress = () => {
    const token = this.props.title
    if (validateTokenAddress(token, this.state.withdrawAddress)) {
      this.setState({ withdrawAddressValid: false })
      return false
    }
    return true
  }

  validateAndShowModal = () => {
    if (this.validateAddress()) {
      this.props.next()
    }
  }

  updateAddressFromQR = (address) => {
    this.setState({ withdrawAddress: address })
  }

  handlePurchaseAddress = async (address) => {
    this.setState({ showLoading: true })

    // check for salt wallet
    const hasWallet = this.props.loanData.collaterals.filter((a) => a.currency === 'SALT')[0]

    //if doesnt have a wallet
    if (!hasWallet.id) {
      // create the wallet

      this.setState({ creatingWallet: true })
      await this.props.WebService.createWallet('SALT')
        .then((res) => {
          this.props.dispatch(updateWallets(res.data))
          this.setState({ creatingWallet: false })
          /*
            this.props.navigation.navigate(
              'Deposit',
              this.props.route.params
            )
            */
        })
        .catch((err) => {
          //console.log('createwallet err', err)
          this.setState({ creatingWallet: false })
        })
    }

    this.props.WebService.saltPurchase(address)
      .then((res) => {
        this.setState({
          purchaseAddress: res.data.address,
          saltPrice: res.data.saltPriceUsd,
          feeRate: res.data.feeRate,
          cryptoPrice: res.data.cryptoPriceUsd,
          refundAddress: res.data.refundAddress,
          showLoading: false
        })
        const btcAmount = (this.props.amount * res.data.saltPriceUsd) / res.data.cryptoPriceUsd

        this.props.updateSaltSendAddress(res.data.address, btcAmount)

        this.props.next()
      })
      .catch((err) => {
        console.log('saltPurchase err', err)
        if (err.data.body.error === 'no SALT wallet exists for this account id') {
          //we gotta make a salt wallet and then retry purchase
        }
        this.setState({ showLoading: false })
      })
  }

  render() {
    return (
      <Animated.View
        style={{
          position: 'absolute',
          height: ScreenHeight,
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
          transform: [{ translateX: this.props.scrollValue }],
          zIndex: 200
        }}
      >
        <Background />
        <HeaderButtons goBack={this.props.goBack} title={this.props.title} close={this.props.close} />

        <WithdrawQRScanner
          toggleScannerModal={this.toggleScannerModal}
          scannerModalVisable={this.state.scannerModalVisable}
          showPinScreen={this.props.showPinScreen}
          updateAddressFromQR={this.updateAddressFromQR}
        />

        <View
          style={{
            marginTop: 20,
            height: ScreenHeight
          }}
        >
          <TextReg style={styles.withdrawAddressTitle}>Refund Address (Bitcoin)</TextReg>
          <View style={styles.withdrawAddressInputBox}>
            <TextInput
              onChangeText={(text) => this.handleAddressInput(text)}
              style={styles.withdrawAddressInput}
              underlineColorAndroid="transparent"
              blurOnSubmit
              value={this.state.withdrawAddress}
              multiline
              returnKeyType={'done'}
              onSubmitEditing={() => Keyboard.dismiss()}
              keyboardAppearance="dark"
            />
            <TouchableOpacity onPress={() => this.openQRScanner()}>
              <Image source={qRcodeScanner} style={styles.purchasesSaltQrImg} />
            </TouchableOpacity>
          </View>

          <TextReg style={styles.withdrawAddressDescription}>Make sure to use a Bitcoin address</TextReg>

          <View style={{ alignItems: 'center', marginTop: 10, marginBottom: 40 }}>
            <TextReg style={styles.purchaseSaltAddressDescription}>
              In the case we are unable to process your SALT purchase for any reason, please provide a Bitcoin address
              to refund your payment to.
            </TextReg>
          </View>

          <View>
            {!this.state.withdrawAddressValid && (
              <View style={styles.withdrawErrorBox}>
                <TextReg style={styles.withdrawErrorText}>
                  {`The ${this.props.title} address you entered is not valid. Please try again.`}
                </TextReg>
              </View>
            )}

            <Button
              style={{ backgroundColor: styles.mintGreen }}
              disabled={!this.props.withdrawButtonActive || this.props.withdrawalExceedsBalance}
              onPress={() => {
                this.props.updateAddress(this.state.withdrawAddress)
                this.handlePurchaseAddress(this.state.withdrawAddress)
              }}
              isLoading={this.state.showLoading}
              theme={'secondary'}
            >
              <TextBold style={styles.purcahseSaltNextButton}>NEXT</TextBold>
            </Button>
          </View>
        </View>
      </Animated.View>
    )
  }
}

const mapStateToProps = (state) => ({
  loanData: state.user.loanData,
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen
})

export default connect(mapStateToProps)(PurchaseSaltAddress)
