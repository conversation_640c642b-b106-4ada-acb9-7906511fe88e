import React, { Component } from 'react';
import { View, Animated, Dimensions, TouchableOpacity, Image, Clipboard } from 'react-native';
import { connect } from 'react-redux';

import QRCode from 'react-native-qrcode-svg';

import { validateTokenAddress } from '../../../util/helpers';

import { askingForPermissions } from '../../../store/auth/auth.actions';
import checkMarkCopied from '../../../imgs/checkMarkCopied.png';

import WithdrawQRScanner from './WithdrawQRScanner';

import { TextReg, Background, HeaderButtons, TextBold } from '../../../components';

const { width: ScreenWidth } = Dimensions.get('window');

import styles from '../styles';

class PurchaseSaltDeposit extends Component {
  constructor (props) {
    super(props);
    this.state = {
      amountInput: '0.00',
      fiatInput: '0',
      scannerModalVisable: false,
      toggleCryptoFiat: false,
      twoFactor: '',
      twoFactorModalVisible: false,
      withdrawAddressValid: true,
      withdrawButtonActive: false
    };
    this.scrollValue = new Animated.Value(ScreenWidth);
  }

	handleAddressInput = (text) => {
	  this.setState({ withdrawAddress: text, withdrawAddressValid: true });
	}

	openQRScanner = () => {
	  this.toggleScannerModal();
	}

	toggleScannerModal = () => {
	  this.props.dispatch(askingForPermissions(!this.state.scannerModalVisable));
	  this.setState({ scannerModalVisable: !this.state.scannerModalVisable });
	}

	validateAddress = () => {
	  const token = this.props.title;
	  if (validateTokenAddress(token, this.state.withdrawAddress)) {
	    this.setState({ withdrawAddressValid: false });
	    return false;
	  }
	  return true;
	}

	validateAndShowModal = () => {
	  if (this.validateAddress()) {
	    this.props.next();
	  }
	}

	updateAddressFromQR = (address) => {
	  this.setState({ withdrawAddress: address });
	}

	copyAddress = (address) => {
	  Clipboard.setString(address);
	  this.setState({ showCopiedButton: true });
	}

	render () {
	  return (
	    <Animated.View
	      style={{
	        position: 'absolute',
	        left: 0,
	        right: 0,
	        top: 0,
	        bottom: 0,
	        transform: [{ translateX: this.props.scrollValue }],
	        zIndex: 210
	      }}
	    >
	      <Background />
	      <HeaderButtons goBack={this.props.goBack} title={this.props.title} close={this.props.close} />

	      <WithdrawQRScanner
	        toggleScannerModal={this.toggleScannerModal}
	        scannerModalVisable={this.state.scannerModalVisable}
	        showPinScreen={this.props.showPinScreen}
	        updateAddressFromQR={this.updateAddressFromQR}
	      />

	      <View style={styles.purchaseSaltSendBox}>
	        <TextReg style={styles.purchaseSaltSendText}>Please send:</TextReg>
	        <TextBold style={styles.purchaseSaltAmountText}>{`${this.props.btcAmount} BTC`}</TextBold>
	        <TextBold style={{ color: '#FFF', fontSize: 32 }}>{`(${this.props.amount} SALT)`}</TextBold>
	        <View style={{ alignItems: 'center', marginTop: 20, marginBottom: 40 }}>
	          <View style={styles.purchaseSaltQRBox} />
	          <TextReg style={styles.purcahseSaltAddressTitle}>Wallet Address:</TextReg>
	          <TextBold style={styles.purcahseSaltAddressText}>{this.props.btcAddress}</TextBold>
	        </View>

	        {this.state.showCopiedButton ? (
	          <TouchableOpacity style={styles.depositAddressCopyButtonApplied}>
	            <View style={styles.purchaseSaltCopyBox}>
	              <Image
	                source={checkMarkCopied}
	                style={{
	                  height: 14,
	                  width: 18
	                }}
	              />
	              <TextBold style={{ fontSize: 20, color: '#FFF', marginLeft: 20 }}>Address Copied</TextBold>
	            </View>
	          </TouchableOpacity>
	        ) : (
	          <TouchableOpacity
	            style={[styles.depositAddressCopyButton, { height: 50, width: 260 }]}
	            onPress={() => this.copyAddress(this.props.btcAddress)}
	          >
	            <View style={styles.purchaseSaltCopyButton}>
	              <TextBold style={{ fontSize: 20, color: '#008487' }}>Copy Address</TextBold>
	            </View>
	          </TouchableOpacity>
	        )}
	      </View>
	    </Animated.View>
	  );
	}
}

const mapStateToProps = (state) => ({
  loanData: state.user.loanData,
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen
});

export default connect(mapStateToProps)(PurchaseSaltDeposit);
