import React, {Component} from 'react'
import {Modal, View, Text, TouchableOpacity} from 'react-native'

import QRCodeScanner from 'react-native-qrcode-scanner'

import styles from '../styles'

class WithdrawQRScanner extends Component {
  onSuccess(e) {
    //Linking.openURL(e.data).catch(err => console.error('An error occured', err))
    if (e.data.includes(':')) {
      e.data = e.data.split(':')[1]
    }
    this.props.updateAddressFromQR(e.data)
    this.props.toggleScannerModal()
  }

  render() {
    return (
      <Modal
        animationType="fade"
        transparent
        visible={this.props.scannerModalVisable && !this.props.showPinScreen}
        onRequestClose={() => this.props.toggleScannerModal()}>
        <View style={styles.scannerModalBox}>
          <View style={styles.scannerModalSquare}>
            <QRCodeScanner onRead={this.onSuccess.bind(this)} />
            <View style={styles.scannerpModalTitleBox}>
              <Text style={styles.scannerModalTitle}>Hold over QR Code</Text>
            </View>
            <TouchableOpacity
              style={styles.scannerModalButton}
              onPress={() => {
                this.props.toggleScannerModal()
              }}>
              <Text style={styles.scannerModalButtonText}>Back</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    )
  }
}

export default WithdrawQRScanner
