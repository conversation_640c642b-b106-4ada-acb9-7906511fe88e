import React, { Component } from 'react'
import { View, Animated, Dimensions, TouchableOpacity, TextInput, Image, Keyboard } from 'react-native'
import { connect } from 'react-redux'

import { validateTokenAddress } from '../../../util/helpers'

import { askingForPermissions } from '../../../store/auth/auth.actions'
import qRcodeScanner from '../../../imgs/qRCodeScanner.png'
import WithdrawQRScanner from './WithdrawQRScanner'

import { TextReg, TextBold } from '../../../components'

const { width: ScreenWidth, height: ScreenHeight } = Dimensions.get('window')

import styles from '../styles'

class WithdrawAddress extends Component {
  constructor(props) {
    super(props)
    this.state = {
      amountInput: '0.00',
      fiatInput: '0',
      scannerModalVisable: false,
      toggleCryptoFiat: false,
      twoFactor: '',
      twoFactorModalVisible: false,
      withdrawAddressValid: true,
      withdrawButtonActive: false,
      addressError: false,
      withdrawAddress: '' || props.toAddress
    }
    this.scrollValue = new Animated.Value(ScreenWidth)
  }

  handleAddressInput = (text) => {
    this.setState({ withdrawAddress: text, withdrawAddressValid: true })
    this.props.updateAddress(text)
  }

  openQRScanner = () => {
    this.toggleScannerModal()
  }

  toggleScannerModal = () => {
    this.props.dispatch(askingForPermissions(!this.state.scannerModalVisable))
    this.setState({ scannerModalVisable: !this.state.scannerModalVisable })
  }

  validateAddress = (withdrawAddress) => {
    const token = this.props.tokenTitle
    if (!validateTokenAddress(token, withdrawAddress)) {
      this.setState({ withdrawAddressValid: false })
      return false
    }
    return true
  }

  validateAndNext = (address) => {
    this.setState({ addressError: false })

    const validAddr = this.validateAddress(address)

    if (validAddr) {
      this.props.next()
    } else {
      this.setState({ addressError: true })
    }
  }

  updateAddressFromQR = (address) => {
    this.setState({ withdrawAddress: address })
    this.props.updateAddress(address)
  }

  render() {
    return (
      <View
        style={{
          zIndex: 180,
          height: ScreenHeight - 150,
          alignSelf: 'stretch'
        }}
      >
        <WithdrawQRScanner
          toggleScannerModal={this.toggleScannerModal}
          scannerModalVisable={this.state.scannerModalVisable}
          showPinScreen={this.props.showPinScreen}
          updateAddressFromQR={this.updateAddressFromQR}
        />

        <View
          style={{
            marginTop: 20,
            flexDirection: 'column',
            justifyContent: 'space-between',
            flex: 1
          }}
        >
          <View>
            <TextReg style={styles.withdrawAddressTitle}>Destination Address</TextReg>
            <View style={styles.withdrawAddressInputBox}>
              <TextInput
                onChangeText={(text) => this.handleAddressInput(text)}
                style={styles.withdrawAddressInput}
                underlineColorAndroid="transparent"
                blurOnSubmit
                value={this.state.withdrawAddress}
                multiline
                returnKeyType={'done'}
                onSubmitEditing={() => Keyboard.dismiss()}
                keyboardAppearance="dark"
              />
              <TouchableOpacity onPress={() => this.openQRScanner()}>
                <Image source={qRcodeScanner} style={styles.withdrawAddressQRImg} />
              </TouchableOpacity>
            </View>

            <TextReg style={styles.withdrawAddressDescription}>Make sure to use a {this.props.token} address</TextReg>
          </View>

          <View>
            {(!this.state.withdrawAddressValid || this.props.addressError) && (
              <View style={styles.withdrawErrorBox}>
                <TextReg style={styles.withdrawErrorText}>
                  {`The ${this.props.token} address you entered is not valid. Please try again.`}
                </TextReg>
              </View>
            )}
            <TouchableOpacity
              style={styles.withdrawAddressValidateButton}
              onPress={() => {
                this.validateAndNext(this.state.withdrawAddress)
              }}
              disabled={
                false
                //!this.props.withdrawButtonActive ||
                //this.props.withdrawalExceedsBalance
              }
            >
              <TextBold style={styles.withdrawAddressPreviewText}>NEXT</TextBold>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    )
  }
}

const mapStateToProps = (state) => ({
  loanData: state.user.loanData,
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen
})

export default connect(mapStateToProps)(WithdrawAddress)
