import React, { Component } from 'react'
import { View, Animated, Dimensions, TextInput, ScrollView, TouchableOpacity, Image } from 'react-native'
import { connect } from 'react-redux'

import WithdrawQRScanner from './WithdrawQRScanner'

import { Button, TextReg, Background, HeaderButtons, TextBold } from '../../../components'
import { numberWithCommas } from '../../../util/helpers'

import agreedYes from '../../../imgs/agreedYes.png'
import agreedNo from '../../../imgs/agreedNo.png'

import styles from '../styles'

const { width: ScreenWidth, height: ScreenHeight } = Dimensions.get('window')

class ConfirmWithdraw extends Component {
  constructor(props) {
    super(props)
    this.state = {
      amountInput: '0.00',
      fiatInput: '0',
      scannerModalVisable: false,
      toggleCryptoFiat: false,
      twoFactor: '',
      twoFactorModalVisible: false,
      withdrawAddressValid: true,
      withdrawButtonActive: false,
      clickedAgree: false
    }
    this.scrollValue = new Animated.Value(ScreenWidth)
    this.myScroll = null
  }

  handleUpdateTwoFactor = (text) => {
    this.setState({ twoFactor: text })
  }

  scrollUp = () => {
    //scroll up like 100px so 2fa can be seen
    this.myScroll.scrollTo(100)
  }

  toggleAgree = () => {
    this.setState({ clickedAgree: !this.state.clickedAgree })
  }

  render() {
    const contractAgreed = this.props.addressType == 'Personal' || this.state.clickedAgree
    const toggleButton = (
      <TouchableOpacity onPress={this.toggleAgree}>
        <Image
          source={this.state.clickedAgree ? agreedYes : agreedNo}
          style={{
            height: 14,
            width: 14,
            marginRight: 6
          }}
        />
      </TouchableOpacity>
    )
    const showValue = numberWithCommas(this.props.value)
    return (
      <Animated.View
        style={{
          position: 'absolute',
          left: 0,
          right: 0,
          backgroundColor: '#28283D',
          transform: [{ translateX: this.props.scrollValue }],
          zIndex: 194
        }}
      >
        <Background />
        <HeaderButtons goBack={this.props.goBack} title={this.props.title} close={this.props.close} />

        <WithdrawQRScanner
          toggleScannerModal={this.toggleScannerModal}
          scannerModalVisable={this.state.scannerModalVisable}
          showPinScreen={this.props.showPinScreen}
          updateAddressFromQR={this.updateAddressFromQR}
        />
        <ScrollView
          ref={(ref) => (this.myScroll = ref)}
          style={{
            backgroundColor: '#28283D',
            paddingBottom: 100,
            height: ScreenHeight - 80
          }}
          contentContainerStyle={{ alignItems: 'center' }}
        >
          <View style={styles.confirmWithdrawHeader}>
            <TextReg style={styles.confirmWithdrawAmount}>Withdrawal Amount</TextReg>
            <TextBold style={styles.confirmWithdrawTitle}>
              {`${this.props.amount} `}
              <TextBold style={styles.confirmWithdrawTitleToken}>{`${this.props.tokenTitle}`}</TextBold>
            </TextBold>

            <View style={{ width: ScreenWidth - 80, marginTop: 30 }}>
              <View style={styles.WithdrawSubmittedRow}>
                <TextReg style={{ color: '#e6e6e6', fontSize: 16 }}>{`USD`}</TextReg>
                <TextReg style={{ color: '#e6e6e6', fontSize: 16 }}>{`$${showValue}`}</TextReg>
              </View>

              <View style={styles.WithdrawSubmittedRow}>
                <TextReg style={{ color: '#e6e6e6', fontSize: 16 }}>{`Address`}</TextReg>
                <TextReg
                  style={{
                    color: '#e6e6e6',
                    fontSize: 14,
                    width: 200,
                    textAlign: 'right'
                  }}
                >
                  {`${this.props.toAddress}`}
                </TextReg>
              </View>
            </View>
          </View>

          <View style={{ width: 300 }}>
            <TextReg style={styles.confirmWithdraw2FATitle}>Two-Factor</TextReg>
            <TextInput
              style={styles.confirmWithdraw2FA}
              onChangeText={(text) => this.handleUpdateTwoFactor(text)}
              value={this.state.twoFactor}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'done'}
              placeholder={''}
              keyboardType={'numeric'}
              onTouchStart={() => this.scrollUp()}
              keyboardAppearance="dark"
            />
          </View>

          {this.props.addressType === 'Contract' && (
            <View style={{ width: 300, marginBottom: 12, marginTop: 10 }}>
              <View style={{ flexDirection: 'row' }}>
                {toggleButton}
                <TextReg>{`WARNING: If you are sending ERC-20`}</TextReg>
              </View>
              <TextReg>
                {`tokens (including [SALT, USDC, TUSD]) to smart contract, you must ensure that the receiving address can support such tokens and that you will have the ability to access such tokens after moving from the SALT platform. SALT Lending is not able to confirm or ensure that the mart contract address will support the tokens or that you will have access to such tokes after transfer. If you have any questions, please contact <NAME_EMAIL>`}
              </TextReg>
            </View>
          )}

          <TextReg style={{ width: 300, marginBottom: 20 }}>
            {`Please allow up to 1 business day for your funds to be sent from SALT’s cold storage wallets.`}
          </TextReg>

          {this.props.feeEstimate && (
            <TextReg style={{ width: 300, marginBottom: 20 }}>
              {`We recommend having at least ${this.props.feeEstimate} Ethereum in your wallet in order to complete this withdrawal.`}
            </TextReg>
          )}
          <View style={{ alignSelf: 'stretch', height: 10 }} />

          <Button
            disabled={this.props.rateLimitError}
            style={{ backgroundColor: contractAgreed ? '#00FFBD' : '#7E7E89' }}
            onPress={() => {
              if (contractAgreed) {
                this.props.next(this.state.twoFactor)
              }
            }}
            isLoading={this.props.withdrawLoading}
          >
            <TextBold style={styles.confirmWithdrawButton}>CONFIRM WITHDRAWAL</TextBold>
          </Button>

          {this.props.withdrawError && (
            <TextReg style={styles.confirmWithdrawError}>{`${this.props.withdrawError}`}</TextReg>
          )}

          {this.props.rateLimitError && (
            <View
              style={{
                alignSelf: 'stretch',
                justifyContent: 'center',
                marginBottom: 20,
                alignItems: 'center',
                marginTop: 10
              }}
            >
              <TextReg
                style={{
                  color: '#de4a2e',
                  fontSize: 17,
                  textAlign: 'center',
                  width: 300
                }}
              >
                {`Timeout due to 3 invalid attempts, please try again in ${this.props.timer} seconds.`}
              </TextReg>
            </View>
          )}

          <View style={{ height: 200 }} />
        </ScrollView>
      </Animated.View>
    )
  }
}

const mapStateToProps = (state) => ({
  loanData: state.user.loanData,
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen
})

export default connect(mapStateToProps)(ConfirmWithdraw)
