import {StyleSheet, PixelRatio, Dimensions} from 'react-native';
const {width: ScreenWidth} = Dimensions.get('window');

const styles = StyleSheet.create({
  // Collateral Screen
  collateralScrollContainer: {
    flex: 1,
    flexDirection: 'column',

    backgroundColor: '#FFF', //'#FFFFFF', //#F5FCFF
  },
  collateralScreenView: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFF', //'#FFFFFF', //#F5FCFF
  },
  collateralHeader: {
    height: 140,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  collateralHeaderTitle: {
    marginBottom: 10,
    textAlign: 'center',
    color: '#e6e6e6',
    fontSize: 18,
  },
  collateralHeaderAmount: {
    textAlign: 'center',
    color: '#e6e6e6',
    fontSize: 28,
    fontWeight: '600',
  },
  collateralList: {
    flex: 1,
    alignSelf: 'stretch',
  },
  collateralListItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'stretch',
    height: 64,
    borderWidth: 0.5,
    borderColor: '#eef0f0',
    paddingLeft: 16,
    paddingRight: 16,
  },
  collateralListItemSalt: {
    borderTopWidth: 3,
  },
  collateralListItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  collateralListItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  collateralListItemTitle: {
    fontSize: 17,
    color: '#e6e6e6',
  },
  collateralListItemPrice: {
    fontSize: 20,
    color: '#FFF',
  },

  collateralDisclaimerBox: {
    marginLeft: 32,
    marginRight: 32,
    marginTop: 16,
    alignSelf: 'flex-end',
  },
  collateralDisclaimer: {
    fontSize: 9,
    color: '#e6e6e6',
    textAlign: 'left',
  },
  walletIcon: {
    height: 34,
    width: 34,
    borderRadius: 15,
    marginRight: 12,
    opacity: 0.9,
  },
  rightArrowIcon: {
    height: 18,
    width: 18,
    borderRadius: 0,
    marginLeft: 8,
  },
  rightArrowIconTxHistory: {
    height: 18,
    width: 18,
    borderRadius: 0,
    marginLeft: 12,
  },

  // Collateral Detail

  GradiantHeaderCenterBox: {
    height: 240,
    width: ScreenWidth,
    alignItems: 'center',
    zIndex: 17,
    position: 'relative',
  },
  tokenPic: {
    marginBottom: 14,
  },
  displayValueText: {
    color: '#FFF',
    marginBottom: 8,
  },
  displayAmountText: {
    color: '#FFF',
    fontSize: 20,
    marginBottom: 16,
  },

  collateralDetailContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: '#28283D',
  },
  collateralDetailHeader: {
    flexDirection: 'column',
    alignSelf: 'stretch',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  depositDetailHeader: {
    flexDirection: 'column',
    alignSelf: 'stretch',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    marginTop: 40,
  },
  collateralDetailHeaderTitle: {
    marginTop: 16,
    marginBottom: 10,
    textAlign: 'center',
    color: '#e6e6e6',
    fontSize: 18,
  },
  collateralDetailHeaderAmount: {
    textAlign: 'center',
    color: '#e6e6e6',
    fontSize: 28,
    fontWeight: '600',
  },
  collateralDetailList: {
    flex: 1,
    alignSelf: 'stretch',
  },
  collateralDetailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'stretch',
    height: 60,
    borderWidth: 0.5,
    borderColor: '#eef0f0',
    paddingLeft: 16,
    paddingRight: 16,
  },
  transactionHistoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'stretch',
    height: 60,
    borderWidth: 0.5,
    borderColor: '#eef0f0',
    paddingLeft: 16,
    paddingRight: 16,
  },
  transactionHistoryItemTop: {
    alignSelf: 'stretch',

    borderTopWidth: 3,
    borderColor: '#eef0f0',
  },
  cancelButtonText: {
    color: '#05868e',
    fontSize: 18,
  },
  loadingDots: {
    height: 60,
    width: 45,
    marginTop: 0,
    opacity: 0.6,
  },
  centerLoadingDots: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
  },

  // Deposit

  depositHeader: {
    marginTop: 16,
    marginBottom: 20,
    textAlign: 'center',
    color: '#e6e6e6',
    fontSize: 18,
  },
  depositAddressBox: {
    height: 50,
    width: 300,
    marginLeft: 10,
    marginRight: 10,
    marginTop: 20,
    borderRadius: 2,
    borderColor: '#05868e',
    borderWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  depositAddressText: {
    color: '#fff',
    textAlign: 'center',
    alignSelf: 'stretch',
    fontSize: 20,
    margin: 10,
    marginTop: 14,
    marginBottom: 20,
    paddingLeft: 10,
    paddingRight: 10,
  },
  depositAddressTextXrp: {
    color: '#fff',
    textAlign: 'center',
    fontSize: 18,
    width: 280,
  },
  depositAddressCopyButton: {
    height: 50,
    width: 50,
    backgroundColor: '#00FFBD',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 14,
  },
  depositAddressCopyButtonApplied: {
    height: 50,
    width: 50,
    backgroundColor: '#5A33E3',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 14,
  },
  depositDisclaimer: {
    fontSize: 12,
    color: '#fff',
    textAlign: 'center',
    marginBottom: 10,
  },
  depositCopyPopup: {
    height: 56,
    position: 'absolute',
    alignSelf: 'stretch',
    width: '100%',
    backgroundColor: '#85c884',
    zIndex: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  depositCopyPopupText: {
    color: '#FFF',
    fontSize: 18,
  },

  //Withdraw
  withdrawHeader: {
    //backgroundColor: '#05868e',
    alignSelf: 'stretch',
    flexDirection: 'column',
    alignItems: 'center',
    paddingTop: 6,
    width: 300,
  },
  withdrawAmountInput: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    maxWidth: 225,
    minWidth: 95,
    color: '#FFF',
    fontSize: 42 / PixelRatio.getFontScale(),
    flexWrap: 'wrap',
    height: 53,
    textAlignVertical: 'bottom',
    paddingBottom: 0,
    textAlign: 'right',
    paddingRight: 8,
  },
  withdrawAmountTextDetail: {
    color: '#FFF',
    fontSize: 28,
    paddingBottom: 5,
    marginLeft: 5,
  },
  withdrawAmountTextDetailActive: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 28,
    paddingBottom: 5,
    marginLeft: 5,
  },
  withdrawAmountBox: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 5,
    borderBottomWidth: 2,
    borderBottomColor: '#81808c',
    width: 250,
    justifyContent: 'center',
    textAlign: 'center',
  },
  cryptoFiatToggle: {
    height: 25,
    width: 25,
    marginTop: -64,
    marginLeft: -10,
  },
  withdrawPriceText: {
    color: '#FFF',
    fontSize: 24,
    height: 28,
    opacity: 0.7,
  },
  withdrawPriceBox: {
    alignItems: 'center',
    marginBottom: 4,
    maxWidth: 250,
    flexWrap: 'wrap',
  },
  withdrawPriceBalanceText: {
    color: '#FFF',
    fontSize: 14,
  },
  withdrawPriceBalanceBox: {
    marginBottom: 30,
  },
  balanceErrorText: {
    color: '#E5705A',
    fontSize: 14,
    height: 18,
    marginTop: 10,
  },
  withdrawMainBox: {
    flexDirection: 'column',
    alignSelf: 'stretch',
    paddingTop: 18,
  },
  withdrawAddressInputScroll: {
    overflow: 'hidden',
  },
  withdrawAddressInput: {
    color: '#e6e6e6',
    fontSize: 16 / PixelRatio.getFontScale(),
    textAlign: 'center',
    marginLeft: 8,
    marginRight: 8,
    flex: 1,
  },
  withdrawAddressInputBox: {
    borderColor: '#cbcbcb',
    borderWidth: 1,
    borderRadius: 14,
    marginLeft: 30,
    marginRight: 30,
    marginBottom: 6,
    alignSelf: 'stretch',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: 12,
    paddingRight: 12,
    paddingBottom: 12,
    paddingTop: 8,
    backgroundColor: '#28283D',
    opacity: 0.8,
  },
  withdrawAddressTitle: {
    color: '#fff',
    fontSize: 16,
    marginLeft: 30,
    marginBottom: 8,
  },
  withdrawAddressDescription: {
    color: '#fff',
    fontSize: 12,
    marginLeft: 30,
    marginBottom: 40,
  },
  withdrawSubmitButton: {
    marginBottom: 14,
    marginTop: 12,
    backgroundColor: '#FFF',
  },
  withdrawAddressQRImg: {
    height: 20,
    width: 20,
    marginLeft: 12,
    marginTop: 2,
  },
  withdrawAddressValidateButton: {
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    zIndex: 5,
    minWidth: 220,
    height: 50,
    fontSize: 22,
    paddingVertical: 9,
    paddingHorizontal: 21,
    backgroundColor: '#00ffc3',
  },
  withdrawAddressPreviewText: {
    color: '#000',
    fontSize: 17,
    marginBottom: -2,
    marginTop: -2,
  },

  //Withdraw Confirm
  withdrawConfirmDescription: {
    margin: 30,
    color: '#e6e6e6',
    fontSize: 16,
  },
  totpButton: {
    borderRadius: 14,
    borderColor: '#05868e',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 12,
  },
  totpText: {
    color: '#05868e',
    fontSize: 16,
  },
  scrollViewContainer: {
    backgroundColor: '#FFF',
    alignSelf: 'stretch',
  },
  cancelText: {
    color: '#e6e6e6',
    fontSize: 14,
  },
  //Withdraw Success
  withdrawSuccessContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: '#28283D',
    paddingBottom: 20,
  },
  withdrawSuccessDescription: {
    marginLeft: 30,
    marginRight: 30,
    marginBottom: 30,
    color: '#e6e6e6',
    fontSize: 16,
    textAlign: 'center',
  },
  withdrawSuccessAmountBox: {
    alignSelf: 'stretch',
    borderTopWidth: 1,
    borderColor: '#eef0f0',
    borderBottomWidth: 1,
    flexDirection: 'column',
    alignItems: 'center',
    padding: 16,
  },
  withdrawSuccessAmountInnerBox: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  withdrawSuccessText: {
    color: '#e6e6e6',
    fontSize: 16,
    marginBottom: 4,
  },
  withdrawSuccessTextBig: {
    color: '#e6e6e6',
    fontSize: 38,
    marginBottom: 4,
    marginRight: 4,
  },
  withdrawSuccessTextBigDesc: {
    paddingBottom: 9,
    color: '#e6e6e6',
    fontSize: 16,
  },
  withdrawSuccessTextMed: {
    color: '#e6e6e6',
    fontSize: 26,
    marginBottom: 4,
    marginRight: 3,
  },
  withdrawSuccessTextMedDesc: {
    paddingBottom: 6,
    color: '#e6e6e6',
    fontSize: 16,
  },
  withdrawSuccessAddressBox: {
    borderColor: '#eef0f0',
    borderBottomWidth: 1,
    alignSelf: 'stretch',
    flexDirection: 'column',
    alignItems: 'center',
    padding: 16,
    marginBottom: 6,
  },
  withdrawSuccessAddressText: {
    color: '#e6e6e6',
    fontSize: 16,
    marginLeft: 20,
    marginRight: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  withdrawErrorBox: {
    alignSelf: 'stretch',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 15,
  },
  withdrawErrorText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
    width: 270,
  },
  confirmWithdrawError: {
    color: '#fb1f63',
    fontSize: 16,
    alignSelf: 'stretch',
    textAlign: 'center',
    marginTop: 10,
  },
  confirmWithdrawHeader: {
    alignItems: 'center',
    marginTop: 30,
    marginBottom: 30,
  },
  confirmWithdrawAmount: {
    color: '#e6e6e6',
    fontSize: 20,
    marginBottom: 8,
  },
  confirmWithdrawTitle: {
    fontSize: 38,
    color: '#e6e6e6',
    marginBottom: 8,
  },
  confirmWithdrawTitleToken: {
    fontSize: 32,
    color: '#e6e6e6',
    marginBottom: 8,
  },
  confirmWithdrawValue: {
    fontSize: 32,
    color: '#e6e6e6',
    marginBottom: 20,
  },
  confirmWithdrawAddressTitle: {
    color: '#e6e6e6',
    fontSize: 20,
    marginBottom: 8,
  },
  confirmWithdraw2FATitle: {
    color: '#e6e6e6',
    fontSize: 15,
    marginBottom: 6,
  },

  input: {
    margin: 15,
    height: 40,
    borderWidth: 1,
    marginLeft: 20,
    marginRight: 20,
    marginTop: 10,
    borderRadius: 3,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    width: '80%',
  },

  // TransactionHistory
  transactionHistoryItemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    alignSelf: 'stretch',
    paddingLeft: 4,
    paddingRight: 4,
    paddingBottom: 8,
    paddingTop: 10,
    borderColor: '#f0f0f0',
  },
  transactionHistoryLeft: {
    flexDirection: 'column',
  },
  transactionHistoryRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  transactionHistoryRightInfo: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  transactionHistoryTitle: {
    fontSize: 17,
    color: '#e6e6e6',
  },
  transactionHistoryAmount: {
    fontSize: 14,
    color: '#e6e6e6',
  },
  transactionHistorySubTitle: {
    fontSize: 14,
    color: '#e6e6e6',
    marginBottom: 4,
  },
  transactionHistorySubTitleStatus: {
    fontSize: 14,
    color: '#bcc6c6',
  },
  swiperContainer: {
    flex: 1,
    alignSelf: 'stretch',
    flexDirection: 'column',
  },
  transactionArrowBox: {
    height: 44,
    justifyContent: 'center',
  },

  //No Notifs
  noNotifsBox: {
    alignSelf: 'stretch',
    marginTop: 100,
  },
  noNotifsText: {
    color: '#e6e6e6',
    fontSize: 18,
    textAlign: 'center',
  },

  //Scanner Modal Withdraws
  scannerModalBox: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  scannerModalSquare: {
    flex: 1,
    alignSelf: 'stretch',
    backgroundColor: '#000',
    borderRadius: 8,
    flexDirection: 'column',
    alignItems: 'center',
    padding: 20,
  },
  scannerModalX: {
    position: 'absolute',
    top: 4,
    right: 4,
  },
  scannerModalTitleBox: {
    marginTop: 24,
    marginBottom: 14,
  },
  scannerModalTitle: {
    fontSize: 24,
    color: '#e6e6e6',
  },
  scannerModalButton: {
    backgroundColor: '#00FFBD',
    borderRadius: 6,
    width: 160,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 30,
    marginBottom: 30,
  },
  scannerModalButtonText: {
    fontSize: 22,
    color: '#000',
  },
  scannerModalDescriptionBox: {
    marginLeft: 30,
    marginRight: 30,
  },
  scannerModalDescription: {
    color: '#e6e6e6',
    textAlign: 'center',
    fontSize: 16,
  },

  //Purcahse Salt
  purchaseSaltRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'stretch',
    height: 80,
    borderWidth: 0.5,
    borderColor: '#eef0f0',
    paddingLeft: 16,
    paddingRight: 16,
  },
  purchaseSaltButton: {
    alignSelf: 'stretch',
    flex: 1,
    marginTop: 10,
    marginBottom: 10,
  },
  purchaseSaltText: {
    color: '#05868e',
    fontSize: 22,
  },
  purcahseSaltRefundParagraph: {
    color: '#e6e6e6',
    fontSize: 16,
    marginTop: 16,
    marginRight: 24,
    marginLeft: 24,
    marginBottom: 16,
  },
  purcahseSaltRefundAddressBox: {
    flexDirection: 'column',
    alignSelf: 'stretch',
  },
  purcahseSaltPurchaseAddressBox: {
    flexDirection: 'column',
    alignItems: 'center',
    alignSelf: 'stretch',
  },
  purcahseSaltRefundTitle: {
    color: '#e6e6e6',
    fontSize: 16,
    marginBottom: 10,
    marginLeft: 24,
  },
  purchaseSaltRefundInput: {
    borderColor: '#cbcbcb',
    borderWidth: 1,
    borderRadius: 14,
    marginLeft: 24,
    marginRight: 24,
    marginBottom: 6,
    paddingLeft: 12,
    paddingRight: 12,
    paddingBottom: 12,
    paddingTop: 12,
    textAlignVertical: 'center',
    color: '#e6e6e6',
  },
  purchaseSaltRefundDescription: {
    color: '#e6e6e6',
    fontSize: 12,
    marginBottom: 18,
    marginLeft: 24,
    marginRight: 24,
  },
  purchaseSaltRefundButton: {
    marginBottom: 30,
    marginTop: 12,
  },
  purchaseSaltPurchaseDescription: {
    color: '#e6e6e6',
    fontSize: 12,
    marginBottom: 12,
    marginLeft: 38,
    marginRight: 38,
  },
  purchaseSaltPurchaseParagraph: {
    marginBottom: 10,
    color: '#e6e6e6',
    fontSize: 12,
  },
  purchaseSaltPurchaseAddress: {
    color: '#e6e6e6',
    fontSize: 12,
    marginBottom: 12,
    marginLeft: 26,
    marginRight: 26,
    textAlign: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  purchaseSaltPriceBox: {
    borderColor: '#05868e',
    borderWidth: 1,
    borderRadius: 14,
    //height: 50,
    alignSelf: 'stretch',
    flexDirection: 'column',
    alignItems: 'center',
    marginLeft: 24,
    marginRight: 24,
    marginTop: 12,
    paddingTop: 12,
    paddingBottom: 12,
  },
  purchaseSaltPriceTextTop: {
    color: '#e6e6e6',
    fontSize: 16,
  },
  purchaseSaltPriceTextMiddle: {
    color: '#e6e6e6',
    fontSize: 32,
  },
  purchaseSaltPriceTextBottom: {
    color: '#e6e6e6',
    fontSize: 16,
  },
  purcahseSaltLockPrice: {
    position: 'absolute',
    top: 6,
    right: 6,
    height: 19,
    width: 17,
  },
  purcahseSaltTimerText: {
    color: '#e6e6e6',
    fontSize: 16,
    marginTop: 8,
    marginRight: 24,
    marginLeft: 24,
    alignSelf: 'stretch',
    textAlign: 'center',
  },
  purchaseSaltQRCodeBox: {
    flexDirection: 'row',
    alignSelf: 'stretch',
    justifyContent: 'center',
  },
  purchaseSaltCopyButton: {
    height: 50,
    width: 260,
    borderRadius: 14,
    backgroundColor: '#FFF',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  purchaseSaltCopyBox: {
    height: 50,
    width: 260,
    borderRadius: 14,
    backgroundColor: '#2fcf8b',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  purcahseSaltAddressText: {
    color: '#fff',
    fontSize: 17,
    marginBottom: 5,
    width: 300,
    textAlign: 'center',
  },
  purcahseSaltAddressTitle: {
    color: '#fff',
    fontSize: 20,
    marginBottom: 8,
    marginTop: 20,
  },
  purchaseSaltQRBox: {
    overflow: 'hidden',
    backgroundColor: '#FFF',
    padding: 10,
    borderRadius: 14,
  },
  purchaseSaltAmountText: {
    color: '#FFF',
    fontSize: 26,
    width: 260,
    textAlign: 'center',
  },
  purchaseSaltSendText: {
    color: '#fff',
    fontSize: 18,
    marginBottom: 8,
  },
  purchaseSaltSendBox: {
    alignItems: 'center',
    marginTop: 20,
    alignSelf: 'stretch',
  },

  //not verirified modal
  helpModalBox: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(74, 74, 74, 0.5)',
  },
  helpModalSquare: {
    width: '86%',
    backgroundColor: '#FFF',
    borderRadius: 8,
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: 30,
  },
  helpModalX: {
    position: 'absolute',
    top: 4,
    right: 4,
  },
  helpModalTitleBox: {
    marginTop: 24,
    marginBottom: 14,
  },
  helpModalTitle: {
    fontSize: 24,
    color: '#e6e6e6',
  },
  helpModalButton: {
    marginVertical: 30,
  },
  helpModalDescriptionBox: {
    marginLeft: 30,
    marginRight: 30,
  },
  helpModalDescription: {
    color: '#e6e6e6',
    textAlign: 'center',
    fontSize: 16,
  },
  closeModalImg: {
    height: 30,
    width: 30,
    margin: 4,
  },
  errorBox: {
    alignSelf: 'stretch',
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
  },
  errorText: {
    color: '#E5705A',
    fontSize: 18,
  },

  // Withdraw& Deposit Box
  collateralDetailButtonsBox: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    paddingTop: 18,
    marginHorizontal: 2,
    backgroundColor: '#3D3D50',
  },
  depositButton: {
    minWidth: 'auto',
    maxWidth: 180,
    marginBottom: 20,
    marginHorizontal: 8,
    fontSize: 16,
    flex: 1,
    backgroundColor: '#00ffc3',
  },
  depositButtonDisabled: {
    minWidth: 'auto',
    maxWidth: 200,
    marginBottom: 20,
    marginHorizontal: 5,
    fontSize: 16,
    flex: 1,
    opacity: 0.5,
    backgroundColor: '#00ffc3',
  },
  withdrawButton: {
    minWidth: 'auto',
    maxWidth: 180,
    marginBottom: 20,
    marginHorizontal: 8,
    fontSize: 16,
    flex: 1,
    backgroundColor: '#00FFBD',
  },
  withdrawButtonDisabled: {
    minWidth: 'auto',
    maxWidth: 200,
    marginBottom: 20,
    marginHorizontal: 5,
    fontSize: 16,
    flex: 1,
    opacity: 0.5,
    backgroundColor: '#b3b5b7',
  },
  GradiantHeaderCard: {
    width: ScreenWidth - 28,
    height: 90,
    zIndex: 20,
  },
  WithdrawSubmittedRow: {
    borderBottomColor: '#f0f0f0',
    borderBottomWidth: 0.5,
    alignSelf: 'stretch',
    height: 46,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
  },
  DepositBox: {
    marginTop: 42,
    marginBottom: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignSelf: 'stretch',
  },
  DepositImg: {
    height: 24,
    width: 24,
    marginRight: 18,
    marginTop: 4,
  },
  depositQrBox: {
    overflow: 'hidden',
    backgroundColor: '#FFF',
    padding: 10,
    borderRadius: 14,
  },
  depositWalletAddressText: {
    alignSelf: 'stretch',
    textAlign: 'center',
    fontSize: 16,
    color: '#FFF',
    marginTop: 20,
  },
  depositCopiedApplied: {
    height: 50,
    width: 260,
    borderRadius: 14,
    backgroundColor: '#5A33E3',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  depositCopyAddress: {
    height: 50,
    width: 260,
    borderRadius: 14,
    backgroundColor: '#00FFBD',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  showPendingWithdrawalBox: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'stretch',
    margin: 10,
  },
  walletRowBox: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 50,
    alignSelf: 'stretch',
  },
  walletRowLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 4,
  },
  walletRowRight: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    paddingRight: 8,
  },
  confirmWithdrawBox: {
    position: 'absolute',
    left: 0,
    right: 0,
    backgroundColor: '#fff',
  },
  confirmWithdrawAddress: {
    fontSize: 16,
    color: '#FFF',
    width: 300,
    textAlign: 'center',
  },
  confirmWithdraw2FA: {
    height: 40,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    fontSize: 16,
    marginBottom: 20,
    color: '#e6e6e6',
  },
  confirmWithdrawButton: {
    color: '#000',
    fontSize: 17,
    marginBottom: -2,
    marginTop: -2,
  },
  purchasesSaltQrImg: {
    height: 20,
    width: 20,
    marginLeft: 12,
    marginTop: 2,
  },
  purchaseSaltAddressDescription: {
    color: '#fff',
    fontSize: 20,
    marginBottom: 8,
    width: 300,
  },
  purcahseSaltNextButton: {
    color: '#000',
    fontSize: 17,
    marginBottom: -2,
    marginTop: -2,
  },
  //zabo
  zaboDetailRow: {
    alignSelf: 'stretch',
    borderRadius: 14,
    backgroundColor: '#48485A',
    flexDirection: 'row',
    height: 60,
    alignItems: 'center',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    paddingLeft: 18,
    paddingRight: 18,
    marginBottom: 12,
  },
  zaboDetailLogo: {
    backgroundColor: '#FFF',
    height: 40,
    width: 40,
    borderRadius: 20,
    marginRight: 10,
  },
});

styles.mintGreen = '#00ffc3';

export default styles;
