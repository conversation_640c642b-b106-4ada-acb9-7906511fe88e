/* eslint-disable import/no-namespace */
/* eslint-disable no-undef */

import React from 'react'
import * as enzyme from 'enzyme'
import thunk from 'redux-thunk'
import configureMockStore from 'redux-mock-store'
import DeviceInfo from 'react-native-device-info'

import AuthScreen from './AuthScreen'
import initialState from '../../store/initialState'

const mockStore = configureMockStore([thunk])

const buildComponent = (renderType = enzyme.shallow, newProps = {}) => {
  const defaultProps = {
    dispatch: jest.fn(() => Promise.resolve({})),
    navigation: {
      addListener: jest.fn(),
    },
  }

  const props = { ...defaultProps, ...newProps }
  return renderType(<AuthScreen store={mockStore(initialState)} {...props} />)
}

describe('Testing AuthScreen', () => {
  it('renders component', () => {
    const wrapper = buildComponent()
    expect(wrapper.length).toBe(1)
  })
})
