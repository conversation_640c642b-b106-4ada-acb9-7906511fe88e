import {StyleSheet, Dimensions, PixelRatio, Platform} from 'react-native'

const {width, height} = Dimensions.get('window')

const SignUpPageHeight = height - 60

const styles = StyleSheet.create({
  authContainerSpaceBetween: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-between',
    alignSelf: 'stretch',
  },
  authContainerFlexStart: {
    flexDirection: 'column',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-start',
    alignSelf: 'stretch',
  },
  loginFormBox: {
    justifyContent: 'flex-start',
    alignSelf: 'stretch',
    zIndex: 5,
    marginBottom: 12,
  },
  loginFormBox2FA: {
    justifyContent: 'flex-start',
    alignSelf: 'stretch',
    zIndex: 5,
    marginBottom: 14,
    marginTop: 20,
  },
  keyboardAvoidingView: {
    alignSelf: 'stretch',
    zIndex: 5,
  },
  toggleAcknowledgeView: {
    height: 40,
    width: 40,
    borderRadius: 6,
    backgroundColor: '#3D3D50',
    borderWidth: 1,
    borderColor: '#efefef',
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 6,
  },
  unit21ToggleCheckImg: {
    height: 40,
    width: 40,
    borderRadius: 6,
  },
  input: {
    margin: 15,
    height: 58,
    marginLeft: 30,
    marginRight: 30,
    marginTop: 4,
    borderRadius: 14,
    paddingLeft: 12,
    fontFamily: 'Europa-Regular',
    fontSize: 17 / PixelRatio.getFontScale(),
    backgroundColor: '#fff',
    color: '#FFF',
    letterSpacing: 0.75,
    backgroundColor: '#3D3D50',
    borderColor: '#fff',
    borderWidth: 1,
  },
  inputLabel: {
    marginLeft: 30,
    marginBottom: 4,
  },
  inputLabel2FA: {
    width: 380,
  },
  inputLabelPassword: {
    marginTop: 14,
    marginLeft: 30,
    marginBottom: 4,
  },
  inputLabelText: {
    color: '#FFF',
    fontSize: 15,
  },
  inputLabelText2FA: {
    color: '#FFF',
    fontSize: 20,
    marginLeft: 30,
  },
  totpButton: {
    borderRadius: 14,
    borderColor: '#05868e',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 2,
    marginBottom: 2,
  },
  totpText: {
    color: '#00ffc3',
    fontSize: 18,
  },
  loginButton: {
    marginTop: 10,
    alignSelf: 'stretch',
    marginLeft: 30,
    marginRight: 30,
    borderRadius: 14,
  },
  twofaButton: {
    marginTop: 10,
    alignSelf: 'stretch',
    marginLeft: 30,
    marginRight: 30,
    borderRadius: 14,
    marginBottom: 20,
  },
  backButton: {
    marginTop: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backText: {
    color: '#FFF',
    fontSize: 14,
  },
  signUpLink: {
    marginTop: 20,
    marginBottom: 50,
    zIndex: 80,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'stretch',
    borderWidth: 1,
    borderColor: '#00FFBD',
    marginLeft: 30,
    marginRight: 30,
    height: 50,
    borderRadius: 14,
  },
  forgotPasswordText: {
    color: '#fff',
    opacity: 0.8,
    fontSize: 16,
  },
  signUpLinkText: {
    color: '#00ffc3',
    fontSize: 17,
  },
  forgotPasswordLink: {
    marginTop: -6,
    alignSelf: 'flex-end',
    marginRight: 30,
  },
  loginErrorBox: {
    marginTop: 20,
  },
  loginErrorText: {
    color: '#FFF',
    fontSize: 17,
  },
  saltLogo: {
    height: 36,
    width: 180,
    borderRadius: 0,
    marginTop: 70,
    marginBottom: 50,
    alignSelf: 'center',
  },
  saltDelta: {
    height: 36,
    width: 34.54,
    borderRadius: 0,
    alignSelf: 'center',
    opacity: 0.2,
  },
  loadingDots: {
    height: 64,
    width: 80,
    marginTop: 14,
    opacity: 0.6,
  },
  versionInfo: {
    position: 'absolute',
    bottom: 10,
    right: 14,
    color: '#FFF',
    zIndex: 10,
    opacity: 0.6,
    fontSize: 14,
  },
  versionInfoUpdating: {
    position: 'absolute',
    bottom: 3,
    right: 12,
    color: '#f7d955',
    zIndex: 10,
    opacity: 0.9,
    fontSize: 14,
  },
  versionInfoPin: {
    position: 'absolute',
    bottom: 3,
    right: 6,
    color: '#FFF',
    zIndex: 10,
    opacity: 0.5,
  },
  prodButton: {
    position: 'absolute',
    bottom: 3,
    right: 6,
    height: 50,
    width: 70,
    zIndex: 10,
  },
  backgroundBox: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    flex: 1,
    borderRadius: 0,
    zIndex: -1,
  },
  backgroundImg: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    flex: 1,
    borderRadius: 0,
    zIndex: -1,
  },

  //Pin
  pinContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'absolute',
    //backgroundColor: '#FFF', //'#128a98',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 100,
    paddingBottom: 10,
  },
  pinTopBox: {
    flexDirection: 'column',
    alignItems: 'center',
    paddingTop: 20,
    //opacity: 0,
  },
  pinBottomBox: {},
  pinInputBox: {
    flexDirection: 'column',
    justifyContent: 'flex-end',
    alignSelf: 'stretch',
  },
  pinInputRow: {
    alignSelf: 'stretch',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  pinInputRowLast: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingLeft: 90,
  },
  pinInputButton: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: 80,
    width: 90,
    borderRadius: 14,
  },
  pinInputNum: {
    fontSize: 22,
    color: '#FFF',
    alignSelf: 'center',
    borderRadius: 140,
    height: 26,
    width: 40,
    textAlign: 'center',
    marginBottom: 2,
  },
  pinInputNumDelete: {
    fontSize: 14,
    color: '#FFF',
    alignSelf: 'center',
    borderRadius: 140,
    height: 22,
    width: 80,
    textAlign: 'center',
    paddingTop: 5,
  },
  pinInputDetails: {
    fontSize: 12,
    color: '#FFF',
    alignSelf: 'center',
    width: 40,
    height: 14,
    textAlign: 'center',
    opacity: 0.7,
  },
  pinTitle: {
    fontSize: 20,
    marginTop: 30,
    color: '#FFF',
  },
  pinDone: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 24,
    marginLeft: 20,
    marginRight: 20,
    marginTop: 14,
    color: '#FFF',
  },
  pinBubbleBox: {
    flexDirection: 'row',
    marginTop: 40,
  },
  pinBubbleActive: {
    height: 20,
    width: 20,
    marginLeft: 8,
    marginRight: 8,
    backgroundColor: '#00ffc3',
    borderRadius: 10,
  },
  pinBubble: {
    height: 20,
    width: 20,
    marginLeft: 8,
    marginRight: 8,
    backgroundColor: '#ffffff30',
    borderRadius: 10,
  },
  saltLogoWhite: {
    height: 30,
    width: 150,
    marginTop: 50,
  },
  pinForgot: {
    color: '#FFF',
    fontSize: 14,
    marginBottom: 0,
  },
  modalText: {
    marginBottom: 15,
  },
  modalButton: {
    marginTop: 15,
    marginBottom: 25,
  },
  timoutNoticeText: {
    color: '#FFF',
    fontSize: 22,
    marginBottom: 10,
    marginTop: 40,
    textAlign: 'center',
  },
  timoutNoticeTextBottom: {
    color: '#FFF',
    fontSize: 18,
    textAlign: 'center',
  },

  // SignUp
  signUpContainer: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: '#28283D',
  },
  signUpPageContainer: {
    flex: 1,
    width,
    flexDirection: 'column',
    alignItems: 'center',
  },
  signUpPageContainerKeyboardAvoid: {
    flex: 1,
    width,
    flexDirection: 'column',
  },
  nextButtonContainer: {
    flex: 1,
    width,
  },
  signUpHeader: {
    alignSelf: 'stretch',
    height: 40,
    marginBottom: 20,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'flex-end',
    marginTop: 40,
  },
  signUpHeaderText: {
    fontSize: 20,
    color: '#FFF',
    fontFamily: 'Europa-Regular',
  },
  signUpHeader2FA: {
    position: 'absolute',
    zIndex: 200,
    alignSelf: 'stretch',
    height: 70,
    marginBottom: 20,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'flex-end',
    marginTop: -144,
  },
  paginationDots: {
    height: 10,
    width: 10,
    marginLeft: 5,
    marginRight: 5,
    marginBottom: 6,
  },
  signUpTitleLead: {
    fontSize: 18,
    color: '#FFF',
    fontFamily: 'Europa-Regular',
    alignSelf: 'stretch',
    textAlign: 'left',
  },
  signUpTextResend: {
    fontSize: 18,
    color: '#FFF',
    marginBottom: 4,
    fontFamily: 'Europa-Regular',
    marginTop: 30,
    fontSize: 16,
  },
  signUpTitlePactSafe: {
    fontSize: 18,
    color: '#FFF',
    marginBottom: 6,
    fontFamily: 'Europa-Regular',
    textAlign: 'center',
    alignSelf: 'stretch',
  },
  signUpTitleItalics: {
    fontSize: 16,
    color: '#FFF',
    fontStyle: 'italic',
    marginBottom: 14,
    fontFamily: 'Europa-Regular',
    alignSelf: 'stretch',

    textAlign: 'left',
  },
  signUpTitle: {
    fontSize: 26,
    color: '#FFF',
    fontFamily: 'Europa-Bold',
  },
  signUpTitleSmall: {
    fontSize: 20,
    color: '#FFF',
    fontFamily: 'Europa-Regular',
  },
  signUpTextInput: {
    height: 58,
    borderRadius: 14,
    alignSelf: 'stretch',
    marginTop: 10,
    padding: 6,
    paddingLeft: 12,
    fontFamily: 'Europa-Regular',
    fontSize: 20,
    color: '#FFF',
    backgroundColor: '#3D3D50',
    borderColor: '#fff',
    borderWidth: 1,
  },
  signUpEmailIntroTextInput: {
    height: 50,
    borderRadius: 6,
    width: 300,
    backgroundColor: '#fff',
    padding: 6,
    paddingLeft: 14,
    fontFamily: 'Europa-Regular',
    fontSize: 20,
    color: '#e6e6e6',
    marginBottom: 18,
  },
  signUpEmailIntroCodeInput: {
    height: 50,
    borderRadius: 6,
    width: 300,
    backgroundColor: '#fff',
    padding: 6,
    paddingLeft: 14,
    fontFamily: 'Europa-Regular',
    fontSize: 20,
    color: '#e6e6e6',
    marginBottom: 4,
  },
  IntroEmailTextInput: {
    height: 50,
    borderRadius: 6,
    width: 280,
    padding: 6,
    paddingLeft: 14,
    fontFamily: 'Europa-Regular',
    fontSize: 17,
    color: '#FFF',
    borderColor: '#FFF',
    borderBottomWidth: 1,
    marginTop: 30,
  },
  signUpTextInputLower: {
    height: 50,
    borderRadius: 6,
    width: 300,
    backgroundColor: '#fff',
    padding: 6,
    paddingLeft: 14,
    fontFamily: 'Europa-Regular',
    fontSize: 20,
    color: '#e6e6e6',
    marginTop: 20,
  },
  backToLoginButton: {
    position: 'absolute',
    left: 18,
    top: 14,
    height: 24,
    width: 28,
  },
  backToLoginImg: {
    height: 24,
    width: 28,
    opacity: 0.85,
  },
  signUpPasswordConfirmText: {
    fontSize: 18,
    color: '#FFF',
    fontFamily: 'Europa-Regular',
    marginTop: 20,
    alignSelf: 'flex-start',
  },
  signUpTextInputEmailVerificationCode: {
    opacity: 0,
    marginTop: -20,
    alignSelf: 'stretch',
    backgroundColor: 'blue',
    height: 200,
    position: 'absolute',
    zIndex: 50,
    left: -100,
    top: 0,
    width: 430,
  },
  signUpVerifcationBlocksBox: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    alignSelf: 'stretch',
    marginTop: 4,
  },
  signUpVerifcationBlocksBox2FA: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
  },
  signUpVerifcationBlock: {
    borderRadius: 6,
    backgroundColor: '#3D3D50',
    borderWidth: 1,
    borderColor: '#AAA',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 4,
    height: 46,
    width: 46,
  },
  signUpVerifcationBlockActive: {
    borderWidth: 2,
    borderColor: '#00ffc3',
  },
  signUpVerifcationBlockText: {
    fontSize: 20,
    color: '#FFF',
  },
  signUpGoogleAuthBox: {
    marginTop: 20,
    flexDirection: 'column',
    alignItems: 'center',
    zIndex: 10,
  },
  signUpGoogleAuthBoxLower: {
    marginTop: 30,
    flexDirection: 'column',
    alignItems: 'center',
  },
  signUpCopyCodeBox: {
    backgroundColor: '#fff',
    borderRadius: 6,
    height: 46,
    width: 300,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 16,
    marginTop: 20,
    zIndex: 12,
    overflow: 'hidden',
  },
  signUpCopyCodeBoxButton: {
    backgroundColor: '#FFF',
    height: 46,
    width: 46,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: '#00ffc3',
  },
  signUpCopyCodeBoxButtonActive: {
    backgroundColor: '#05868e',
    height: 46,
    width: 46,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#FFF',
  },
  signUpCopyCodeText: {
    color: '#e6e6e6',
    width: 238,
    fontSize: 14,
    overflow: 'scroll',
    height: 46,
    paddingTop: 5,
    marginLeft: -6,
    textAlign: 'center',
  },
  verifyCaseSensitive: {
    color: '#EEE',
    margin: 10,
    fontSize: 15,
    alignSelf: 'stretch',
    marginTop: 2,
  },
  signUpLocationBox: {
    marginBottom: 10,
    marginTop: 10,
    alignSelf: 'stretch',
  },
  signUpLocationDropdown: {
    height: 54,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    backgroundColor: '#3D3D50',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 18,
  },
  signUpErrorBox: {
    borderRadius: 6,
    padding: 8,
    width: 260,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
    textAlign: 'center',
  },
  introEmailErrorBox: {
    borderRadius: 6,
    width: 260,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 0,
    textAlign: 'center',
    marginBottom: 4,
  },
  signUpJumioErrorBox: {
    backgroundColor: '#fff',
    borderRadius: 6,
    padding: 8,
    width: 300,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
    textAlign: 'center',
  },
  signUpErrorText: {
    color: '#F00',
    fontSize: 16,
    textAlign: 'center',
  },
  signUpAgreeText: {
    color: '#FFF',
    fontSize: 14,
    width: 260,
    textAlign: 'center',
  },
  signUpBeginJumioButton: {
    marginTop: 30,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    zIndex: 5,
    minWidth: 200,
    height: 60,
    fontSize: 22,
    paddingVertical: 9,
    paddingHorizontal: 21,
    backgroundColor: '#FFF',
  },
  signUpLastEndButton: {
    marginTop: 40,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    zIndex: 5,
    minWidth: 220,
    height: 50,
    fontSize: 22,
    paddingVertical: 9,
    paddingHorizontal: 21,
    backgroundColor: '#00FFBD',
  },
  signUpAccountButton: {
    flexDirection: 'row',
    marginTop: 12,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    zIndex: 5,
    minWidth: 260,
    height: 90,
    fontSize: 22,
    backgroundColor: '#28283D',
    opacity: 0.4,
    borderWidth: 2,
    borderColor: '#fff',
  },
  signUpAccountButtonActive: {
    flexDirection: 'row',
    marginTop: 12,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    zIndex: 5,
    minWidth: 260,
    height: 90,
    fontSize: 22,
    backgroundColor: '#28283D',
    borderWidth: 2,
    borderColor: '#00FFBD',
  },
  signUpErrorRetryButton: {
    marginTop: 20,
    borderRadius: 14,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    zIndex: 5,
    minWidth: 120,
    height: 40,
    fontSize: 22,
    paddingVertical: 8,
    paddingHorizontal: 21,
    backgroundColor: '#FFF',
  },
  signUpNextButton: {
    marginBottom: 30,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 5,
    alignSelf: 'stretch',
    marginLeft: 30,
    marginRight: 30,
    height: 50,
    fontSize: 22,
    backgroundColor: '#00ffc3',
  },
  signUpNextButtonInactive: {
    marginBottom: 30,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    zIndex: 5,
    alignSelf: 'stretch',
    marginLeft: 30,
    marginRight: 30,
    height: 50,
    fontSize: 22,
    backgroundColor: '#515151',
  },
  signUpSpaceBetween: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
  },
  jumioErrorText: {
    width: 280,
    marginTop: 16,
    color: '#FFF',
    fontSize: 16,
    textAlign: 'center',
  },
  signupTextComplete: {
    width: 280,
    marginTop: 10,
    color: '#FFF',
    fontSize: 16,
    textAlign: 'center',
  },
  authCustomModal: {
    position: 'absolute',
    zIndex: 30,
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  },
  accountNameText: {
    marginTop: 14,
    fontSize: 16,
    marginBottom: 4,
    color: '#fff',
  },
  accountTypeText: {
    marginTop: 18,
    fontSize: 16,
    color: '#fff',
  },
  accountInput: {
    fontSize: 19,
    width: 260,
    height: 50,
    borderRadius: 14,
    backgroundColor: '#28283D',
    borderWidth: 1,
    borderColor: '#FFF',
    padding: 6,
    paddingLeft: 14,
    fontFamily: 'Europa-Regular',
    color: '#e6e6e6',
    marginTop: 6,
  },
  introResendButton: {
    marginTop: 10,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    zIndex: 5,
    minWidth: 250,
    height: 44,
    fontSize: 22,
    paddingVertical: 9,
    paddingHorizontal: 21,
    backgroundColor: '#FFF',
  },
  introLocationSelect: {
    height: 50,
    borderRadius: 6,
    width: 300,
    backgroundColor: '#fff',
    padding: 6,
    paddingLeft: 14,
    fontFamily: 'Europa-Regular',
    fontSize: 20,
    color: '#e6e6e6',
    marginBottom: 18,
  },
})

export default styles
