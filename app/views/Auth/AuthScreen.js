import React, {Component} from 'react'
import {View, StatusBar, Linking, AppState, Clipboard, Animated, Platform, SafeAreaView, Dimensions} from 'react-native'
import {connect} from 'react-redux'
import AsyncStorage from '@react-native-async-storage/async-storage'
import DeviceInfo from 'react-native-device-info'
import * as Keychain from 'react-native-keychain'
import firebase from '@react-native-firebase/app'

const model = DeviceInfo.getModel()
const manufacturer = DeviceInfo.getManufacturer()
const systemName = DeviceInfo.getSystemName()
const systemVersion = DeviceInfo.getSystemVersion()
//import SafariView from 'react-native-safari-view'
import remoteConfig from '@react-native-firebase/remote-config'
const {width: ScreenWidth, height: ScreenHeight} = Dimensions.get('window')

import packageJson from '../../../package.json'
import {
  login,
  isAuthed,
  authenticating,
  pauseAuth,
  updatePin,
  updateFCMToken,
  showPinScreen,
  updateDeviceId,
  reviewerLogin,
} from '../../store/auth/auth.actions'
import {screenView, sendEvent} from '../../store/analytics/analytics.actions'
import {getMe, updateAccount} from '../../store/user/user.actions'
import {askingForPermissions} from '../../store/auth/auth.actions'

import {Button, Background, TextBold, TextReg} from '../../components'
import commonStyles from '../../styles/commonStyles'
import LoginScreen from './Login/LoginScreen'
import styles from './styles'
import config from '../../config.json'

class AuthScreen extends Component {
  constructor(props) {
    super(props)
    this.state = {
      step: 0,
      email: '',
      pass: '',
      twoFactor: '',
      cognitoUser: null,
      loginError: 0,
      appState: AppState.currentState,
      clipboard: '',
      showFadeWall: true,
      loginRes: 'e',
      forgotPassword: false,
      justUpdated: false,
      jwtToken: null,
      cognitoId: null,
      introColors: ['red', 'green', 'yellow'],
    }
    this.fadeValue = new Animated.Value(1)
  }

  componentDidMount() {
    this.props.dispatch(screenView('Login Screen'))

    this.checkIsUpdate()
    this.getRemoteConfig()

    Animated.timing(this.fadeValue, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      this.setState({showFadeWall: false})
    })

    //AppState.addEventListener('change', this.handleAppStateChange)

    return AsyncStorage.getItem('LOGIN_EMAIL').then(res => {
      this.setState({email: res})
    })
  }

  componenWillUnmount() {
    //AppState.removeEventListener('change', this.handleAppStateChange)
  }

  sendFcmToken = () => {
    firebase
      .messaging()
      .getToken()
      .then(fcmToken => {
        if (fcmToken && fcmToken !== this.props.fcmToken) {
          this.props.WebService.updateFCMTokenAPI(fcmToken).then(res => {
            this.props.dispatch(updateFCMToken(fcmToken))
          })
        }
      })
  }

  closeRegistrationModal = () => {
    this.setState({showRegistrationModal: false})
    this.backToStage1()
  }

  prefetchData = async () => {
    const user = await this.props.dispatch(getMe())
    const account = await this.props.WebService.getSaltAccount()
    this.props.dispatch(updateAccount(account?.data))

    const registrationSnapshot = this.props.user.registrationSnapshot

    // double check this for 2fa update
    // if user has not finished signup
    if (registrationSnapshot && (!registrationSnapshot.infoEntered || !registrationSnapshot.membershipAgreementSigned)) {
      this.props.dispatch(pauseAuth())
      this.setState({step: 0})
      console.log('user.data', user.data)
      this.openSignUpLink({
        email: user.data.primaryEmail,
        firstName: user.data.firstName,
        lastName: user.data.lastName,
        countryCode: user.data.address.countryCode,
        regionCode: user.data.address.province,
        resume: true,
        emailVerify: true,
        idVerified: this.props.user.idVerificationStatus,
        membershipAgreementSigned: registrationSnapshot.membershipAgreementSigned,
        infoEntered: registrationSnapshot.infoEntered,
        account: account.data,
      })
    } else {
      if (account.data.length === 0) {
        this.setState({step: 0})
        this.openSignUpLink({
          email: user.data.primaryEmail,
          firstName: user.data.firstName,
          lastName: user.data.lastName,
          countryCode: user.data.address.countryCode,
          regionCode: user.data.address.province,
          resume: true,
          emailVerify: true,
          idVerified: this.props.user.idVerificationStatus,
          membershipAgreementSigned: registrationSnapshot.membershipAgreementSigned,
          infoEntered: registrationSnapshot.infoEntered,
          account: account.data,
        })
        return
      }
      const email = this.state.email.toLowerCase()

      /*
	    const accountRef = await AsyncStorage.getItem(`REF-${email}`);
	    const setRef = accountRef || '0';
	    AsyncStorage.setItem(`REF-${email}`, setRef);
	    this.props.dispatch(showPinScreen(false));
	    this.props.dispatch(isAuthed({ email: this.state.email, ref: setRef }));
      */

      AsyncStorage.setItem(`REF-${email}`, '0')
      this.props.dispatch(showPinScreen(false))
      this.props.dispatch(isAuthed({email: this.state.email, ref: '0'}))
    }
  }

  checkSignupEmailVerified = () => {
    AsyncStorage.getItem(`SignUpComplete-${this.state.email.toLowerCase()}`).then(res => {
      if (res) {
        const resStep = res.split('-')[0]
        if (resStep === '1') {
          this.setState({step: 0, loginError: 0})
          this.openSignUpLink({
            email: this.state.email,
            resume: true,
            emailVerify: false,
          })
        }
      }
    })
  }

  completeNewPasswordRes = user => {
    const jwtToken = user.signInUserSession.accessToken.jwtToken
    const cognitoId = user.username
    this.props.WebService.loginToSalt(
      {
        model,
        manufacturer,
        osVersion: systemVersion,
        platform: systemName,
        deviceId: this.props.deviceId || undefined,
      },
      jwtToken,
    )
      .then(async res => {
        console.log('completeNewPasswordRes res', res)

        this.successfulLogin(res)
      })
      .catch(err => {
        console.log('completeNewPasswordRes err', err)
      })
  }

  handleLogin = () => {
    if (!this.state.email || !this.state.pass) {
      return
    }
    this.setState({loginError: 0})
    if (this.state.step === 0) {
      this.checkSignupEmailVerified()
      this.props.dispatch(authenticating())
      // test reviewer login
      if (this.state.email === '827581' && this.state.pass === '728127') {
        // disable webService & load placeholder data
        this.props.dispatch(updatePin('1233'))
        this.props.dispatch(showPinScreen(false))
        this.props.dispatch(reviewerLogin())
      } else {
        // real login
        this.props.dispatch(sendEvent(`Login - Email`))
        this.props
          .dispatch(login(this.state.email, this.state.pass))
          .then(res => {
            console.log('login res', res)
            if (res?.challengeName == 'NEW_PASSWORD_REQUIRED') {
              console.log('step to  new password flow-')
              this.props.dispatch(pauseAuth())
              this.props.navigation.navigate('ResetPassword', {
                user: res,
                completeNewPasswordRes: this.completeNewPasswordRes,
              })
              return
            }
            const jwtToken = res.signInUserSession.accessToken.jwtToken
            const cognitoId = res.username
            this.setState({jwtToken, cognitoId})

            this.props.WebService.loginToSalt(
              {
                model,
                manufacturer,
                osVersion: systemVersion,
                platform: systemName,
                deviceId: this.props.deviceId || undefined,
              },
              jwtToken,
            )
              .then(async res => {
                this.successfulLogin(res)
                return
              })
              .catch(err => {
                if (err.data && err.data?.body?.error === 'Missing 2FA code') {
                  this.props.dispatch(pauseAuth())
                  this.setState({
                    step: 1,
                    loginError: 0,
                  })
                  //this.focusNextField('twoFactor');
                }
                if (err.data && err.data?.body?.error === 'Device not found') {
                  AsyncStorage.removeItem('DEVICEID', () => {
                    this.props.dispatch(updateDeviceId(null))
                    this.handleLogin()
                  })
                }
              })
          })
          .catch(err => {
            console.log('login err', err)

            this.props.dispatch(pauseAuth())
            if (err?.message?.includes('User is not confirmed')) {
              console.log('resume')
              let params = {
                email: this.state.email,
                password: this.state.pass,
                resume: 'verify',
              }
              this.props.navigation.navigate('SignUp', params)
            } else {
              this.setState({step: 0, loginError: 1})
            }
          })
      }
    } else if (this.state.step === 1) {
      this.props.dispatch(authenticating())
      this.props.dispatch(sendEvent(`Login - 2FA`))

      AsyncStorage.setItem('LOGIN_EMAIL', this.state.email)

      this.props.WebService.loginToSalt(
        {
          model,
          manufacturer,
          osVersion: systemVersion,
          platform: systemName,
          deviceId: this.props.deviceId || undefined,
          verificationCode: this.state.twoFactor,
        },
        this.state.jwtToken,
      )
        .then(async res => {
          this.successfulLogin(res)
        })
        .catch(err => {
          if (err.data && err.data?.body?.error === 'Invalid 2FA code') {
            this.props.dispatch(pauseAuth())
            this.setState({step: 1, loginError: 2, twoFactor: ''})
          }
          if (err.data && err.data?.body?.error === 'Device not found') {
            AsyncStorage.removeItem('DEVICEID', () => {
              this.props.dispatch(updateDeviceId(null))
              this.handleLogin()
            })
          } else {
            this.props.dispatch(pauseAuth())
            this.setState({loginRes: err.toString()})
          }
        })
    }
  }

  successfulLogin = async res => {
    AsyncStorage.setItem('LOGIN_EMAIL', this.state.email)
    const newSid = res.headers['sess-auth']
    const {deviceId} = res.data

    let pin
    const lowerCaseEmail = this.state.email.toLowerCase()
    try {
      pin = await Keychain.getInternetCredentials(`pin-${lowerCaseEmail}`)

      if (pin) {
        this.props.dispatch(updatePin(pin.password))
      }
    } catch (error) {
      this.props.dispatch(updatePin('0'))
    }

    AsyncStorage.setItem('DEVICEID', deviceId)
    this.props.dispatch(updateDeviceId(deviceId))
    await Keychain.setInternetCredentials('token', 'user', newSid, {
      securityLevel: Keychain.SECURITY_LEVEL.ANY,
      storage: Keychain.STORAGE_TYPE.AES,
    })

    this.props.WebService.updateSID(newSid)
    this.setState({loginRes: res.toString()})
    await this.prefetchData()
    this.sendFcmToken()
  }

  checkIsUpdate = async () => {
    const version = packageJson.version

    const latestVersion = await AsyncStorage.getItem('LATEST_VERSION')

    if (latestVersion) {
      //and compare with what you are on  - if what you have now is higher - show the updated signal for 5 seconds
      if (this.compareVersions(latestVersion, version)) {
        AsyncStorage.setItem('LATEST_VERSION', version)
        this.setState({justUpdated: true}, () => {
          setTimeout(() => {
            this.setState({justUpdated: false})
          }, 7000)
        })
      }
    } else {
      AsyncStorage.setItem('LATEST_VERSION', version)
    }
  }

  compareVersions = (one, two) => {
    //1.2.3, 1.2.4
    const a = one.split('.').map(a => Number(a))
    const b = two.split('.').map(a => Number(a))

    //return true if two is bigger
    if (a[0] > b[0]) return false
    if (a[0] < b[0]) return true
    if (a[1] > b[1]) return false
    if (a[1] < b[1]) return true
    if (a[2] > b[2]) return false
    if (a[2] < b[2]) return true
    return false
  }

  /*
  handleAppStateChange = async nextAppState => {
    if (this.state.appState.match(/inactive|background/) && nextAppState === 'active') {
      const clipboardCopy = await Clipboard.getString()
      if (clipboardCopy.length === 6 && !isNaN(clipboardCopy)) {
        //this.setState({ twoFactor: clipboardCopy });
      }
    }
    this.setState({appState: nextAppState})
  }
  */

  updateEmail = text => {
    let noSpace = text?.replace(' ', '')
    this.setState({email: noSpace, loginError: 0})
  }

  updatePassword = text => {
    this.setState({pass: text, loginError: 0})
  }

  updateTwoFactor = text => {
    this.setState({twoFactor: text, loginError: 0}, () => {
      if (text.length === 6) {
        this.handleLogin()
      }
    })
  }

  openTotp = () => {
    this.props.dispatch(askingForPermissions(true))
    if (Platform.OS === 'ios') {
      Linking.openURL('totp://').catch(err => {
        Linking.openURL('authy://app')
      })
    } else {
      Linking.openURL('otpauth://')
    }
  }

  openSignUpLink = continueRegisteringParams => {
    this.props.navigation.navigate('SignUp', continueRegisteringParams)
  }

  forgotPasswordLink = () => {
    this.props.dispatch(askingForPermissions(true))

    if (Platform.OS === 'ios') {
      /*
      SafariView.show({
        url: 'https://borrower.saltlending.com/forgot-password',
      })
      */
      Linking.openURL('https://borrower.saltlending.com/forgot-password')
    } else {
      Linking.canOpenURL('https://borrower.saltlending.com/forgot-password').then(supported => {
        if (supported) {
          Linking.openURL('https://borrower.saltlending.com/forgot-password')
        }
      })
    }
  }

  backToStage1 = () => {
    this.setState({loginError: 0, step: 0, twoFactor: ''})
  }

  getRemoteConfig = async () => {
    await remoteConfig().fetch(0)

    remoteConfig()
      .setDefaults({
        awesome_new_feature: 'disabled',
      })
      .then(() => remoteConfig().fetchAndActivate())
      .then(fetchedRemotely => {
        if (fetchedRemotely) {
          const parameters = remoteConfig().getAll()
          //console.log('remote config params', parameters)
          const introColors = [parameters.introTest1._value, parameters.introTest2._value, 'yellow']
          //console.log('introColors', introColors)
          this.setState({introColors})
        } else {
          console.log('No configs were fetched from the backend, and the local configs were already activated')
        }
      })
  }

  render() {
    return (
      <View style={commonStyles.tileContainer}>
        {this.state.showFadeWall && (
          <Animated.View
            style={[
              styles.authCustomModal,
              {
                opacity: this.fadeValue,
              },
            ]}>
            <Background />
          </Animated.View>
        )}
        <StatusBar barStyle="light-content" />

        <SafeAreaView style={[styles.authContainerSpaceBetween, {height: ScreenHeight}]}>
          <LoginScreen
            updateEmail={this.updateEmail}
            updatePassword={this.updatePassword}
            handleLogin={this.handleLogin}
            email={this.state.email}
            pass={this.state.pass}
            twoFactor={this.state.twoFactor}
            updateTwoFactor={this.updateTwoFactor}
            openTotp={this.openTotp}
            backToStage1={this.backToStage1}
            isAuthenticating={this.props.isAuthenticating}
            loginError={this.state.loginError}
            isDisabled={this.props.isDisabled}
            step={this.state.step}
            openSignUpLink={this.openSignUpLink}
            forgotPasswordLink={this.forgotPasswordLink}
          />

          {this.state.justUpdated ? (
            <TextReg style={styles.versionInfoUpdating}>{`updating...`}</TextReg>
          ) : (
            <TextReg style={styles.versionInfo}>
              {config.env !== 'production' && `${config.env} - `}
              {packageJson.version}
            </TextReg>
          )}
        </SafeAreaView>

        <Background />
      </View>
    )
  }
}

AuthScreen.navigationOptions = {
  header: null,
}

const mapStateToProps = state => ({
  isAuthenticating: state.auth.isAuthenticating,
  WebService: state.auth.WebService,
  isDisabled: state.auth.disabled,
  deviceId: state.auth.deviceId,
  user: state.user.user,
})

export default connect(mapStateToProps)(AuthScreen)
