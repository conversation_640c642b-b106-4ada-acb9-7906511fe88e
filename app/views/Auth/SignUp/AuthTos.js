import React, {useEffect, useState, useRef} from 'react'
import {StyleSheet, View, Platform, TouchableOpacity, Image, ScrollView} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {WebView} from 'react-native-webview'
import <PERSON>tie<PERSON>ie<PERSON> from 'lottie-react-native'

import {TextReg, TextBold, BackgroundHeader, Button} from '../../../components'
import {useDispatch, useSelector} from 'react-redux'

import {decode} from 'html-entities'

import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'
import config from '../../../config.json'

let AuthTos = ({navigation, route}) => {
  let dispatch = useDispatch()
  let WebService = useSelector(state => state.auth.WebService)
  let flow = useSelector(state => state.auth.flow)

  let [contract, setContract] = useState({body: null})
  let [signed, setSigned] = useState(false)
  let [loaded, setLoaded] = useState(false)
  let [loading, setLoading] = useState(false)

  let [boxHeight, setBoxHeight] = useState(360)
  let [boxWidth, setBoxWidth] = useState(350)

  let onLayout = event => {
    let {height, width} = event.nativeEvent.layout
    setBoxHeight(height - 300)
    setBoxWidth(width - 60)
  }

  let lottieRef = useRef(null)

  useEffect(() => {
    getAgreement()
  }, [])

  let getAgreement = async () => {
    WebService.getTos()
      .then(res => {
        //this.setState({contract: res.data, loaded: true})
        let tosData = res.data
        tosData.body = decode(tosData.body)
          .replace(/rgb\(62, 62, 62\)/g, 'rgb(155, 155, 155)')
          .replace(/11pt/g, '30pt')

        setContract(tosData)
        setLoaded(true)
      })
      .catch(err => {
        console.log('getUnsignedAgreement err', err)
      })
  }

  let closeFn = () => {
    navigation.navigate('Auth')
  }

  let next = () => {
    navigation.goBack()
  }

  let prefetchData = async userEmail => {
    const account = await WebService.getSaltAccount()
    const ref = account.data[0].ref.toString()
    AsyncStorage.setItem('REF', ref)
    AsyncStorage.setItem('LOGIN_EMAIL', userEmail)
    dispatch(showPinScreen(false))
    dispatch(isAuthed({email: userEmail, ref}))
  }

  let sendFcmToken = () => {
    firebase
      .messaging()
      .getToken()
      .then(fcmToken => {
        if (fcmToken && fcmToken !== flow.fcmToken) {
          WebService.updateFCMTokenAPI(fcmToken)
            .then(res => {
              console.log('updateFcmToken res', res)
              dispatch(updateFCMToken(fcmToken))
            })
            .catch(err => {
              console.log('updateFCMToken err', err)
            })
        }
      })
  }

  const bodyDecoded = decode(contract.body)
  const contractStyling = `<style>
  div{
    overflow-x: hidden;
    background-color: #535363;
  }
  .ps-section{
    margin-top: 10px;
    margin-bottom: 30px;
  }
  .ps-contract-body, .ps-contract-body * {
    color: white !important;
  }
  </style>`

  return (
    <View style={localStyles.box} onLayout={onLayout}>
      <View>
        <BackgroundHeader title={'Sign Up'} goBack={() => navigation.goBack()} />
        <View style={{alignSelf: 'stretch', flexDirection: 'row', justifyContent: 'center', marginTop: 10, marginBottom: 10, fontSize: 18}}>
          <TextReg>{`Referral Programs Terms & Conditions`}</TextReg>
        </View>
        <View
          style={{
            width: boxWidth + 16,
            position: 'absolute',
            top: 130,
            zIndex: 20,
            backgroundColor: '#535363',
            padding: 10,
          }}>
          <View
            style={{
              height: boxHeight,
              width: boxWidth,
              borderRadius: 14,
            }}>
            {loaded ? (
              <WebView
                source={{
                  html: `<div class="ps-contract-full">${contractStyling} <div class="ps-contract-body ps-contract">${bodyDecoded}</div></div>`,
                }}
                style={{height: boxHeight, width: boxWidth, backgroundColor: 'transparent'}}
                scalesPageToFit
                useWebKit={false}
              />
            ) : (
              <View
                style={{
                  width: 350,
                  height: 360,
                  flexDirection: 'column',
                  alignItems: 'center',
                }}>
                <LottieView
                  ref={lottieRef}
                  style={{
                    width: 60,
                    height: 60,
                    opacity: 0.9,
                  }}
                  source={require('../../../imgs/lotti/loading-white-dots.json')}
                  autoPlay
                />
              </View>
            )}
          </View>
        </View>
      </View>
      <TouchableOpacity style={{...commonStyles.button, alignSelf: 'stretch', marginBottom: 20}} onPress={() => next()}>
        <TextReg style={{color: '#000', letterSpacing: 1.2, fontSize: 20}}>BACK</TextReg>
      </TouchableOpacity>
    </View>
  )
}

export default AuthTos

const localStyles = StyleSheet.create({
  box: {
    flex: 1,
    padding: 20,
    backgroundColor: '#28283D',
    position: 'relative',
    justifyContent: 'space-between',
  },
})
