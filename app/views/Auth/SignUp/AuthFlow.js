import React, {Component} from 'react'
import {View, StatusBar, TouchableOpacity, Image, AppState, Clipboard, Dimensions} from 'react-native'
import {connect} from 'react-redux'
import {updateAuthFlow} from '../../../store/auth/auth.actions'

class AuthFlow extends Component {
  constructor(props) {
    super(props)
    let completedPage = 0
    if (this.props?.before?.resume == 'verify') {
      completedPage = 1
    } else if (this.props?.before?.infoEntered === false) {
      completedPage = 2
    } else if (this.props?.before?.membershipAgreementSigned === false) {
      completedPage = 3
    }

    console.log('completedPage', completedPage)
    this.state = {
      completedPage,
    }
  }

  componentDidMount() {
    this.initFlow()
    this.handleNavigation()
  }

  componentDidUpdate(prevProps, prevState) {
    this.handleNavigation()
  }

  initFlow = () => {
    console.log('this.props.before', this.props.before)
    let {email, password, countryCode, regionCode, resume, firstName, lastName} = this.props.before || {}
    let flow = {
      email: email || '',
      password: password || '',
      //appState: AppState.currentState,
      countryCode: countryCode || '',
      regionCode: regionCode || '',
      resume: resume || false,
      firstName: firstName || '',
      lastName: lastName || '',
    }
    this.props.dispatch(updateAuthFlow(flow))
  }

  handleNavigation = () => {
    let {completedPage} = this.state
    if (completedPage == 0) {
      this.props.navigation.navigate('Auth1')
      return
    }
    if (completedPage == 1) {
      this.props.navigation.navigate('Auth2')
      return
    }
    if (completedPage == 2) {
      this.props.navigation.navigate('Auth3')
      return
    }
    if (completedPage == 3) {
      this.props.navigation.navigate('Auth4')
      return
    }
  }

  render() {
    return <View />
  }
}

AuthFlow.navigationOptions = {
  header: null,
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
})

export default connect(mapStateToProps)(AuthFlow)
