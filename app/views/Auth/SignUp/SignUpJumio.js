/*
import React, { Component } from 'react'
import {
  View,
  TouchableOpacity,
  NativeModules,
  NativeEventEmitter,
} from 'react-native'
import { connect } from 'react-redux'
import Lot<PERSON>View from 'lottie-react-native'
const { JumioMobileSDKNetverify } = NativeModules
import iso from 'iso-3166-1'

import styles from '../styles'
import { TextReg, TextBold } from '../../../components'
import { getMe } from '../../../store/user/user.actions'

class SignUpJumio extends Component {
  constructor() {
    super()
    this.state = {
      loading: false,
      jumioUrl: '',
      providerId: '',
      jumioSecret: '',
      jumioToken: '',
      readyToCont: false,
      error: false,
      errorMessage: '',
      rejected: false,
    }
    this.polling = null
    const emitterNetverify = new NativeEventEmitter(JumioMobileSDKNetverify)
    emitterNetverify.addListener('EventDocumentData', EventDocumentData => {
      const scanIdRef = EventDocumentData.scanReference

      this.props.WebService.jumioInit({ providerId: scanIdRef })
        .then(res => {
          this.postJumioSDK(scanIdRef)
        })
        .catch(err => {
          this.setState({
            error: true,
            errorMessage: `Jumio Verification Error`,
          })
        })
    })
    emitterNetverify.addListener('EventError', EventError => {
      // user quit jumio
      this.setState({
        loading: false,
        errorMessage: `Please Restart Verification`,
      })
    })
  }

  componentDidMount() {
    this.props.WebService.getJumioCreds().then(res => {
      this.setState({
        jumioSecret: res.data.secret,
        jumioToken: res.data.token,
      })
    })
  }

  componentDidUpdate(prevProps) {
    const { idVerificationStatus } = this.props.user
    if (
      this.props.user &&
      prevProps.user &&
      prevProps.user.idVerificationStatus !== idVerificationStatus
    ) {
      if (
        idVerificationStatus === 'pending' ||
        idVerificationStatus === 'approved' ||
        idVerificationStatus === 'retry' ||
        idVerificationStatus === 'rejected'
      ) {
        this.setState({ loading: false, readyToCont: true })
        clearInterval(this.polling)
        this.props.completePage(5)
      }
    }
  }

  componentWillUnmount() {
    clearInterval(this.polling)
  }

  initiateJumio = () => {
    this.setState({ error: false, errorMessage: '' })
    this.initJumioSDK()
    JumioMobileSDKNetverify.startNetverify()
  }

  postJumioSDK = scanIdRef => {
    this.props.WebService.jumioFormComplete({
      jumioId: scanIdRef,
    })
      .then(res => {
        this.setState({ loading: true })
        this.polling = setInterval(this.props.getMe, 20000)
      })
      .catch(err => {
        this.setState({
          error: true,
          loading: false,
          errorMessage: 'Network Error',
        })
      })
  }

  initJumioSDK = () => {
    const alpha3 = iso.whereAlpha2(this.props.countryCode).alpha3

    JumioMobileSDKNetverify.initNetverify(
      this.state.jumioToken,
      this.state.jumioSecret,
      'US',
      {
        requireVerification: true,
        customerId: this.props.userID,
        preselectedCountry: alpha3,
        cameraPosition: 'BACK',
        documentTypes: ['DRIVER_LICENSE', 'PASSPORT', 'IDENTITY_CARD', 'VISA'],
      }
    )
  }

  render() {
    return (
      <View
        style={[
          styles.signUpSpaceBetween,
          { opacity: this.props.visible ? 1 : 0.3 },
        ]}
      >
        <View style={styles.signUpPageContainer}>
          <TextReg style={styles.signUpTitleLead}>ID Verification</TextReg>

          {this.state.loading ? (
            <View
              style={{
                flexDirection: 'column',
                alignItems: 'center',
                marginTop: 16,
                textAlign: 'center',
              }}
            >
              <LottieView
                ref={animation => {
                  this.animation = animation
                }}
                style={{ width: 90, height: 90 }}
                source={require('../../../imgs/lotti/verify-id.json')}
                autoPlay
              />
              <TextReg
                style={styles.jumioErrorText}
              >{`Please wait 30 seconds for ID Verification to initiate`}</TextReg>
            </View>
          ) : (
            <View>
              {this.state.rejected ? (
                <View style={{ marginTop: 40 }}>
                  <TextReg
                    style={styles.jumioErrorText}
                  >{`ID Verification was rejected`}</TextReg>
                  <TouchableOpacity onPress={this.props.closeSignUp}>
                    <View style={styles.signUpBeginJumioButton}>
                      <TextReg style={{ color: '#05868e', fontSize: 22 }}>
                        Close
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                </View>
              ) : (
                <View>
                  {this.state.readyToCont ? (
                    <TextReg
                      style={styles.jumioErrorText}
                    >{`ID Verification successful`}</TextReg>
                  ) : (
                    <TouchableOpacity onPress={this.initiateJumio}>
                      <View style={styles.signUpBeginJumioButton}>
                        <TextReg style={{ color: '#05868e', fontSize: 22 }}>
                          Begin
                        </TextReg>
                      </View>
                    </TouchableOpacity>
                  )}
                </View>
              )}
            </View>
          )}
          {this.state.error && (
            <View style={styles.signUpJumioErrorBox}>
              <TextReg style={styles.signUpErrorText}>
                {this.state.errorMessage}
              </TextReg>
            </View>
          )}
        </View>
        <TouchableOpacity
          onPress={
            this.state.readyToCont ? () => this.props.completePage(5) : null
          }
          activeOpacity={this.state.readyToCont ? 0.2 : 1}
        >
          <View
            style={
              this.state.readyToCont
                ? styles.signUpNextButton
                : styles.signUpNextButtonInactive
            }
          >
            <TextBold style={{ color: '#05868e', fontSize: 20 }}>Next</TextBold>
          </View>
        </TouchableOpacity>
      </View>
    )
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
})

export default connect(
  mapStateToProps,
  { getMe }
)(SignUpJumio)
*/
