import React, { Component } from 'react'
import { View, TextInput, TouchableOpacity } from 'react-native'
import { connect } from 'react-redux'

import styles from '../styles'
import { TextReg, TextBold } from '../../../components'

class SignUpNames extends Component {
  constructor() {
    super()
    this.state = {
      email: '',
      readyToCont: false
    }
    this.inputs = []
  }

  handleSubmitNames = () => {
    if (this.state.readyToCont) {
      this.props.completePage(3)
    }
  }

  componentWillReceiveProps(nextProps) {
    console.log('nextProps')
    if (nextProps.firstName.length > 0 && nextProps.lastName.length > 0) {
      this.setState({ readyToCont: true })
    }
  }

  render() {
    return (
      <View style={[styles.signUpSpaceBetween, { opacity: this.props.visible ? 1 : 0.3 }]}>
        <View style={styles.signUpPageContainer}>
          <TextReg style={styles.signUpTitleLead}>{`Now let's get your`}</TextReg>
          <TextReg style={styles.signUpTitle}>First & Last Name</TextReg>
          <TextInput
            style={styles.signUpTextInput}
            onChangeText={this.props.updateFirstName}
            ref={(input) => (this.inputs.firstName = input)}
            value={this.props.firstName}
            returnKeyType={'next'}
            onSubmitEditing={() => this.inputs.lastName.focus()}
            placeholder={'First Name'}
            keyboardAppearance="dark"
          />
          <TextInput
            style={styles.signUpTextInputLower}
            onChangeText={this.props.updateLastName}
            ref={(input) => (this.inputs.lastName = input)}
            value={this.props.lastName}
            returnKeyType={'done'}
            onSubmitEditing={() => this.handleSubmitNames()}
            placeholder={'Last Name'}
            keyboardAppearance="dark"
          />
        </View>
        <TouchableOpacity onPress={this.handleSubmitNames}>
          <View style={this.state.readyToCont ? styles.signUpNextButton : styles.signUpNextButtonInactive}>
            <TextBold style={{ color: '#05868e', fontSize: 20 }}>Next</TextBold>
          </View>
        </TouchableOpacity>
      </View>
    )
  }
}

const mapStateToProps = (state) => ({
  WebService: state.auth.WebService,
  user: state.user.user
})

export default connect(mapStateToProps)(SignUpNames)
