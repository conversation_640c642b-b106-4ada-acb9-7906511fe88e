import React, {useEffect, useState, useRef} from 'react'
import {StyleSheet, View, Platform, TouchableOpacity, Image, ScrollView, TextInput, Keyboard} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {TextReg, TextBold, BackgroundHeader, Button, LocationSelect} from '../../../components'
import {useDispatch, useSelector} from 'react-redux'
import SignUpProgress from './SignUpProgress'

import iso3166 from 'iso-3166-2'
import countryCodes from '../../../util/countryCodes'

import {updateAuthFlow} from '../../../store/auth/auth.actions'
import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'

let Auth3 = ({navigation, route}) => {
  let dispatch = useDispatch()
  let WebService = useSelector(state => state.auth.WebService)
  let flow = useSelector(state => state.auth.flow)

  let [first, setFirst] = useState('')
  let [last, setLast] = useState('')
  let [firstErr, setFirstErr] = useState(false)
  let [lastErr, setLastErr] = useState(false)

  let inputFirst = useRef(null)
  let inputLast = useRef(null)
  let scrollRef = useRef(null)

  let [countryCodesArr, setCountryCodes] = useState([])
  let [countrySubs, setCountrySubs] = useState([])
  let [selectedCountry, setSelectedCountry] = useState('')
  let [countryCode, setCountryCode] = useState(null)
  let [pickableCountrySubs, setPickableCountrySubs] = useState([])
  let [subArr, setSubArr] = useState([])
  let [selectedProvince, setSelectedProvince] = useState(null)
  let [regionCode, setRegionCode] = useState(null)

  //android
  const [isKeyboardVisible, setKeyboardVisible] = useState(false)

  useEffect(() => {
    inputFirst.current.focus()
    setup()

    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => setKeyboardVisible(true))
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => setKeyboardVisible(false))
    return () => {
      keyboardDidShowListener.remove()
      keyboardDidHideListener.remove()
    }
  }, [])

  useEffect(() => {
    setFirstErr(false)
  }, [first])

  useEffect(() => {
    setLastErr(false)
  }, [last])

  let hideAndroidCont = Platform.OS !== 'ios' && isKeyboardVisible

  let setup = () => {
    let pickableCountryCodes = countryCodes().map(a => a.name)
    pickableCountryCodes = ['United States'].concat(pickableCountryCodes)
    setCountryCodes(pickableCountryCodes)
  }

  let [cont, setCont] = useState(false)

  let closeFn = () => {
    navigation.navigate('Auth')
  }

  let checkText = num => {
    console.log('checkText', num)
    let ok = false
    if (num == 1) {
      if (first?.length > 1) {
        ok = true
      }
      setFirstErr(!ok)
    } else {
      if (last?.length > 1) {
        ok = true
      }
      setLastErr(!ok)
    }
  }

  let next = allContinue => {
    if (!allContinue) return

    WebService.updateMe({
      userInfo: {
        countryCode: countryCode,
        firstName: first,
        lastName: last,
        province: regionCode,
      },
    }).then(res => {
      navigation.navigate('Auth4')
    })
  }

  let onSelect = selectedCountry => {
    const countryCode = countryCodes().filter(a => a.name == selectedCountry)[0].code
    const isoCountry = iso3166.country(countryCode)
    let pickableCountrySubs = []
    const subArr = Object.values(isoCountry.sub)
    pickableCountrySubs = subArr.map(a => a.name)
    pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
      if (a < b) return -1
      if (b < a) return 1
      return 0
    })
    setSelectedCountry(selectedCountry)
    setCountryCode(countryCode)
    setPickableCountrySubs(pickableCountrySubs)
    setSelectedProvince(null)
    setRegionCode(null)
    /*
    this.setState({
      selectedCountry,
      countryCode,
      pickableCountrySubs,
      selectedProvince: null,
      regionCode: null,
    })
    */
  }

  let onProvinceSelect = selectedProvince => {
    const subData = iso3166.subdivision(countryCode, selectedProvince)
    const regionCode = subData.regionCode
    setSelectedProvince(selectedProvince)
    setRegionCode(regionCode)
    /*
    this.setState({
      selectedProvince,
      regionCode,
    })
    */
    //props
    //this.props.updateLocationCodes(countryCode, regionCode)
    dispatch(updateAuthFlow({countryCode, regionCode})) //countryCode, regionCode
    setCountryCode(subData.countryCode)
    setRegionCode(subData.regionCode)
    //this.setState({provinceComplete: true, countryCode: subData.countryCode, regionCode: subData.regionCode})
  }

  let allContinue = first?.length > 1 && last?.length > 1 && countryCode && regionCode

  return (
    <View style={localStyles.box}>
      <BackgroundHeader title={'Sign Up'} close closeFn={closeFn} />
      <SignUpProgress complete={3} />
      <KeyboardAwareScrollView ref={scrollRef} style={{flex: 1, alignSelf: 'stretch'}} enableResetScrollToCoords={true}>
        <View style={localStyles.boxTop}>
          <TextReg style={styles.signUpTitleLead}>First Name</TextReg>
          <TextInput
            style={styles.signUpTextInput}
            onChangeText={setFirst}
            ref={inputFirst}
            value={first}
            returnKeyType={'next'}
            onSubmitEditing={() => inputLast.current.focus()}
            placeholder={'First Name'}
            keyboardAppearance="dark"
            onBlur={() => checkText(1)}
          />
          {firstErr && (
            <View style={{...styles.signUpErrorBox, width: 330}}>
              <TextReg style={styles.signUpErrorText}>First Name must be at least 2 characters</TextReg>
            </View>
          )}
          <TextReg style={styles.signUpPasswordConfirmText}>Last Name</TextReg>
          <TextInput
            style={styles.signUpTextInput}
            onChangeText={setLast}
            ref={inputLast}
            value={last}
            returnKeyType={'next'}
            onSubmitEditing={() => Keyboard.dismiss()}
            placeholder={'Last Name'}
            keyboardAppearance="dark"
            onBlur={() => checkText(2)}
          />
          {lastErr && (
            <View style={{...styles.signUpErrorBox, width: 330}}>
              <TextReg style={styles.signUpErrorText}>Last Name must be at least 2 characters</TextReg>
            </View>
          )}

          <TextReg style={styles.signUpPasswordConfirmText}>{`Location`}</TextReg>
          <View style={styles.signUpLocationBox}>
            <View style={{height: 2}} />
            <LocationSelect
              options={countryCodesArr}
              onSelect={onSelect}
              placeholder={selectedCountry ? selectedCountry : 'Country'}
              style={styles.signUpLocationDropdown}
            />
            <View style={{height: 6}} />
            <LocationSelect
              onSelect={onProvinceSelect}
              options={pickableCountrySubs}
              placeholder={selectedProvince ? selectedProvince : 'State / Province'}
              style={styles.signUpLocationDropdown}
            />
          </View>
        </View>
      </KeyboardAwareScrollView>
      <>
        {!hideAndroidCont && (
          <TouchableOpacity
            style={{
              ...commonStyles.button,
              alignSelf: 'stretch',
              marginBottom: 10,
              opacity: allContinue ? 1 : 0.3,
            }}
            onPress={() => next(allContinue)}>
            <TextReg style={{color: '#000', letterSpacing: 1.2, fontSize: 20}}>CONTINUE</TextReg>
          </TouchableOpacity>
        )}
      </>
    </View>
  )
}

export default Auth3

const localStyles = StyleSheet.create({
  box: {
    flex: 1,
    padding: 24,
    backgroundColor: '#28283D',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingBottom: 30,
  },
  boxTop: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    alignSelf: 'stretch',
  },
})
