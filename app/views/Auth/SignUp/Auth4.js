import React, {useEffect, useState, useRef} from 'react'
import {StyleSheet, View, Platform, TouchableOpacity, Image, ScrollView} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {WebView} from 'react-native-webview'
import <PERSON><PERSON><PERSON>ie<PERSON> from 'lottie-react-native'
import AsyncStorage from '@react-native-async-storage/async-storage'
import firebase from '@react-native-firebase/app'
import axios from 'axios'

import {TextReg, TextBold, BackgroundHeader, Button} from '../../../components'
import {useDispatch, useSelector} from 'react-redux'
import SignUpProgress from './SignUpProgress'

import {decode} from 'html-entities'
import * as Keychain from 'react-native-keychain'

import commonStyles from '../../../styles/commonStyles'
import {showPinScreen, isAuthed, updateFCMToken, updatePin} from '../../../store/auth/auth.actions'
import styles from '../styles'
import config from '../../../config.json'

let Auth4 = ({navigation, route}) => {
  let dispatch = useDispatch()
  let WebService = useSelector(state => state.auth.WebService)
  let flow = useSelector(state => state.auth.flow)

  let [contract, setContract] = useState({body: null})
  let [signed, setSigned] = useState(false)
  let [loaded, setLoaded] = useState(false)
  let [loading, setLoading] = useState(false)

  let [boxHeight, setBoxHeight] = useState(360)
  let [boxWidth, setBoxWidth] = useState(350)

  let onLayout = event => {
    let {height, width} = event.nativeEvent.layout
    setBoxHeight(height - 340)
    setBoxWidth(width - 60)
  }

  let lottieRef = useRef(null)

  useEffect(() => {
    if (flow.countryCode != '' && flow.regionCode != '') {
      getAgreement()
    }
  }, [flow.countryCode, flow.regionCode])

  let getAgreement = async () => {
    WebService.getUnsignedAgreement('membership-agreement', flow.countryCode, flow.regionCode, config.pactSafe.accessId)
      .then(res => {
        console.log('getAgreement res', res)
        //this.setState({contract: res.data, loaded: true})

        let tosData = res.data
        tosData.body = decode(tosData.body)
          .replace(/rgb\(62, 62, 62\)/g, 'rgb(155, 155, 155)')
          .replace(/11pt/g, '30pt')

        setContract(tosData)
        setLoaded(true)
      })
      .catch(err => {
        console.log('getUnsignedAgreement err', err)
      })
  }

  let closeFn = () => {
    navigation.navigate('Auth')
  }

  let next = () => {
    console.log('next')
  }

  let agreePactSafe = async () => {
    console.log('agreePactSafe')
    if (!loaded) return
    setSigned(true)
    setLoading(true)
    WebService.signAgreement({
      contractId: contract?.contract?.toString(),
      versionId: contract?.id,
      siteId: config.pactSafe.accessId,
    })
      .then(async res => {
        //this.setState({loading: false})
        setLoading(false)
        dispatch(updatePin('0'))
        sendFcmToken()
        let email = flow.email
        prefetchData(email)
        //this.props.completePage(4);
      })
      .catch(err => {
        this.setState({loading: false})
      })
  }

  let prefetchData = async userEmail => {
    const account = await WebService.getSaltAccount()
    const ref = account.data[0].ref.toString()
    AsyncStorage.setItem('REF', ref)
    AsyncStorage.setItem('LOGIN_EMAIL', userEmail)
    dispatch(showPinScreen(false))
    dispatch(isAuthed({email: userEmail, ref}))
  }

  let sendFcmToken = () => {
    firebase
      .messaging()
      .getToken()
      .then(fcmToken => {
        if (fcmToken && fcmToken !== flow.fcmToken) {
          WebService.updateFCMTokenAPI(fcmToken)
            .then(res => {
              console.log('updateFcmToken res', res)
              dispatch(updateFCMToken(fcmToken))
            })
            .catch(err => {
              console.log('updateFCMToken err', err)
            })
        }
      })
  }

  const bodyDecoded = decode(contract.body)
  const contractStyling = `<style>
  div{
    overflow-x: hidden;
    background-color: #535363;
  }
  .ps-section{
    margin-top: 10px;
    margin-bottom: 30px;
  }
  .ps-contract-body, .ps-contract-body * {
    color: white !important;
  }
  </style>`

  return (
    <View style={localStyles.box} onLayout={onLayout}>
      <View>
        <BackgroundHeader title={'Sign Up'} close closeFn={closeFn} />
        <SignUpProgress complete={4} />
        <View
          style={{
            width: boxWidth + 10,
            marginBottom: 20,
            position: 'absolute',
            top: 140,
            zIndex: 20,
            backgroundColor: '#535363',
            padding: 10,
          }}>
          <View
            style={{
              height: boxHeight,
              width: boxWidth,
              borderRadius: 14,
            }}>
            {loaded ? (
              <WebView
                source={{
                  html: `<div>${contractStyling} <div class="ps-contract-body ps-contract-full ps-contract">${bodyDecoded}</div></div>`,
                }}
                style={{height: boxHeight, width: boxWidth, backgroundColor: 'transparent'}}
                scalesPageToFit
                useWebKit={false}
              />
            ) : (
              <View
                style={{
                  width: 350,
                  flexDirection: 'column',
                  alignItems: 'center',
                  height: boxHeight,
                }}>
                <LottieView
                  ref={lottieRef}
                  style={{
                    width: 60,
                    height: 60,
                    opacity: 0.9,
                  }}
                  source={require('../../../imgs/lotti/loading-white-dots.json')}
                  autoPlay
                />
              </View>
            )}
          </View>
        </View>
        <View style={{height: boxHeight, width: 350}} />

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: 310,
            height: 120,
            borderRadius: 14,
            marginTop: Platform.OS === 'ios' ? 0 : 40,
          }}>
          <TouchableOpacity onPress={agreePactSafe}>
            {signed ? (
              <Image source={require('../../../imgs/check-box.png')} style={{height: 40, width: 40}} />
            ) : (
              <Image source={require('../../../imgs/blank-check-box.png')} style={{height: 40, width: 40}} />
            )}
          </TouchableOpacity>
          {loading ? (
            <View
              style={{
                width: 260,
                flexDirection: 'column',
                alignItems: 'center',
                height: 60,
              }}>
              <LottieView
                ref={lottieRef}
                style={{
                  width: 60,
                  height: 60,
                  opacity: 0.9,
                }}
                source={require('../../../imgs/lotti/loading-white-dots.json')}
                autoPlay
              />
            </View>
          ) : (
            <TextReg style={styles.signUpAgreeText}>
              {`I have read, understand, and consent to the language and authorzations outlined in SALT's Terms of Use.`}
            </TextReg>
          )}
        </View>
      </View>
    </View>
  )
}

export default Auth4

const localStyles = StyleSheet.create({
  box: {
    flex: 1,
    padding: 24,
    backgroundColor: '#28283D',
    position: 'relative',
  },
})
