import React, {Component} from 'react';
import {View, TouchableOpacity, TextInput, Keyboard} from 'react-native';
import {connect} from 'react-redux';

import iso3166 from 'iso-3166-2';
import countryCodes from '../../../util/countryCodes';

import styles from '../styles';
import {TextReg, TextBold, LocationSelect} from '../../../components';

class SignUpLocation extends Component {
  constructor(props) {
    super(props);
    let pickableCountryCodes = countryCodes().map(a => a.name);
    pickableCountryCodes = ['United States'].concat(pickableCountryCodes);

    this.state = {
      countryCodes: pickableCountryCodes,
      countrySubs: [],
      selectedCountry: null,
      countryCode: null,
      pickableCountrySubs: [],
      subArr: [],
      selectedProvince: null,
      regionCode: null,
      noAutoScroll: false,
      email: '',
      provinceComplete: false,
    };
    this.inputs = [];
  }

  onSelect = selectedCountry => {
    const countryCode = countryCodes().filter(a => a.name == selectedCountry)[0].code;
    const isoCountry = iso3166.country(countryCode);
    let pickableCountrySubs = [];
    const subArr = Object.values(isoCountry.sub);
    pickableCountrySubs = subArr.map(a => a.name);
    pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
      if (a < b) return -1;
      if (b < a) return 1;
      return 0;
    });
    this.setState({
      selectedCountry,
      countryCode,
      pickableCountrySubs,
      selectedProvince: null,
      regionCode: null,
    });
  };

  onProvinceSelect = selectedProvince => {
    const subData = iso3166.subdivision(this.state.countryCode, selectedProvince);
    const regionCode = subData.regionCode;
    this.setState({
      selectedProvince,
      regionCode,
    });
    this.props.updateLocationCodes(this.state.countryCode, regionCode);
    this.setState({provinceComplete: true, countryCode: subData.countryCode, regionCode: subData.regionCode});
  };

  goNext = () => {
    this.props.WebService.updateMe({
      userInfo: {
        countryCode: this.state.countryCode,
        firstName: this.props.firstName,
        lastName: this.props.lastName,
        province: this.state.regionCode,
      },
    }).then(res => {
      this.props.completePage(3);
    });
  };

  render() {
    let readyToCont = this.state.provinceComplete && this.props.firstName != '' && this.props.lastName != '';
    return (
      <View style={[styles.signUpSpaceBetween, {opacity: this.props.visible ? 1 : 0.3}]}>
        <View style={styles.signUpPageContainer}>
          <TextReg style={styles.signUpTitleLead}>{`First & Last Name`}</TextReg>
          <TextInput
            style={styles.signUpTextInput}
            onChangeText={this.props.updateFirstName}
            ref={input => (this.inputs.firstName = input)}
            value={this.props.firstName}
            returnKeyType={'next'}
            onSubmitEditing={() => this.inputs.lastName.focus()}
            placeholder={'First Name'}
            placeholderTextColor={'#777'}
            keyboardAppearance="dark"
          />
          <TextInput
            style={styles.signUpTextInput}
            onChangeText={this.props.updateLastName}
            ref={input => (this.inputs.lastName = input)}
            value={this.props.lastName}
            returnKeyType={'done'}
            onSubmitEditing={() => Keyboard.dismiss()}
            placeholder={'Last Name'}
            placeholderTextColor={'#777'}
            keyboardAppearance="dark"
          />

          <TextReg style={styles.signUpTitleLead}>{`Location`}</TextReg>
          <View style={styles.signUpLocationBox}>
            <LocationSelect
              options={this.state.countryCodes}
              onSelect={this.onSelect}
              placeholder={this.state.selectedCountry ? this.state.selectedCountry : 'Country'}
              style={styles.signUpLocationDropdown}
            />
            <LocationSelect
              onSelect={this.onProvinceSelect}
              options={this.state.pickableCountrySubs}
              placeholder={this.state.selectedProvince ? this.state.selectedProvince : 'State / Province'}
              style={styles.signUpLocationDropdown}
            />
          </View>
        </View>
        <TouchableOpacity
          style={readyToCont ? styles.signUpNextButton : styles.signUpNextButtonInactive}
          onPress={readyToCont ? () => this.goNext() : null}>
          <TextBold style={{color: '#000', fontSize: 19}}>CONTINUE</TextBold>
        </TouchableOpacity>
      </View>
    );
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
});

export default connect(mapStateToProps)(SignUpLocation);
