import React, {useEffect, useState, useRef} from 'react'
import {StyleSheet, View, Platform, TouchableOpacity, Image, ScrollView, TextInput, Keyboard, Linking, Dimensions} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import Heap from '@heap/react-native-heap'
import LottieView from 'lottie-react-native'
import {TextReg, TextBold, BackgroundHeader, Button} from '../../../components'
import {useDispatch, useSelector} from 'react-redux'
import SignUpProgress from './SignUpProgress'

import agreedYes from '../../../imgs/agreedYes.png'
import arrowUp from '../../../imgs/arrowUpGreen.png'
import arrowDown from '../../../imgs/arrowDownGreen.png'
import infoPng from '../../../imgs/info.png'
import closeX from '../../../imgs/closeX.png'

import {updateAuth<PERSON>low} from '../../../store/auth/auth.actions'
import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'
import config from '../../../config.json'

let {width: ScreenWidth, height: ScreenHeight} = Dimensions.get('window')

let Auth1 = ({navigation, route}) => {
  let dispatch = useDispatch()
  let WebService = useSelector(state => state.auth.WebService)
  let AuthService = useSelector(state => state.auth.AuthService)
  let flow = useSelector(state => state.auth.flow)

  let [email, setEmail] = useState('')
  let [pass1, setPass1] = useState('')
  let [pass2, setPass2] = useState('')

  let [cont1, setCont1] = useState(false)
  let [cont2, setCont2] = useState(false)

  let [error1, setError1] = useState(false)
  let [error2, setError2] = useState(false)

  let [loading, setLoading] = useState(false)
  let [info, setInfo] = useState(false)
  let [red, setRed] = useState(false)

  let [code, setCode] = useState('')
  let [show, setShow] = useState(false)
  let [checked, setChecked] = useState(false)
  let [codePassed, setCodePassed] = useState(false)
  let [showRefErr, setShowRefErr] = useState(false)
  let [matchErr, setMatchErr] = useState(false)
  let [passTotal, setPassTotal] = useState([])

  //android
  const [isKeyboardVisible, setKeyboardVisible] = useState(false)

  useEffect(() => {
    //inputEmail.current.focus()
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => setKeyboardVisible(true))
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => setKeyboardVisible(false))
    return () => {
      keyboardDidShowListener.remove()
      keyboardDidHideListener.remove()
    }
  }, [])

  let hideAndroidCont = Platform.OS !== 'ios' && isKeyboardVisible

  useEffect(() => {
    if (show) {
      //codeRef.current.focus()
    }
  }, [show])

  let checkReferral = async (text = code) => {
    setShowRefErr(false)
    setCodePassed(false)
    //let upperCode = text.toUpperCase()
    try {
      let res = await WebService.checkReferral(text)
      setCodePassed(true)
    } catch (err) {
      setCodePassed(false)
      setShowRefErr(true)
    }
  }

  let closeFn = () => {
    navigation.navigate('Auth')
  }

  let next = async allContinue => {
    if (!allContinue) return

    setError2(false)

    let emailLower = email.trim().toLowerCase()
    setLoading(true)
    let UTMCodes = ''
    if (Platform.OS === 'ios') {
      UTMCodes += '&platform=ios'
    } else {
      UTMCodes += '&platform=android'
    }
    try {
      let user = await AuthService.signUp(emailLower, pass1)

      dispatch(updateAuthFlow({cognitoUser: user, email: emailLower, password: pass1}))

      console.log('show', show, code)

      let res2 = await WebService.createUser({
        cognitoId: user.userSub,
        UTMCodes,
        referredByCode: show ? code : '',
      })
      console.log('res2', res2?.data)
      Heap.identify(res2.data?.id)
    } catch (err) {
      console.log('err2', err)
      setError2(true)
      setLoading(false)
      return
    }

    navigation.navigate('Auth2')
  }

  let isValidEmail = email => {
    const re = /^(([^<>()\[\]\.,;:\s@\"]+(\.[^<>()\[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i
    return re.test(email)
  }

  let handleEmail = text => {
    setEmail(text)
    setError1(false)
    setError2(false)
    if (isValidEmail(text)) {
      setCont1(true)
    } else {
      setCont1(false)
    }
  }

  let handleSubmitEmail = async () => {
    console.log('handleEmail')
  }

  let termsLink = () => {
    //Linking.openURL('https://saltlending.com/terms-of-use/')
    navigation.navigate('AuthTos')
    setShowRefErr(false)
    setCodePassed(false)
  }

  useEffect(() => {
    const validPassword = validatePasswords()
    if (validPassword.length > 0) {
      setPassTotal(validPassword)
      setCont2(false)
      if (pass1?.length > 7 && pass2?.length > 7 && validPassword?.filter(a => a.message == 'Passwords must match.')?.length > 0) {
        setMatchErr(true)
      } else {
        setMatchErr(false)
      }
    } else {
      setPassTotal(validPassword)
      setCont2(true)
    }
  }, [pass1, pass2])

  let submitPass = async () => {
    console.log('submitPass')
  }

  let validatePasswords = () => {
    //validation
    const lowercase = {
      test: text => /[a-z]+/.test(text),
      message: 'Password requires at least one lowercase letter.',
    }
    const uppercase = {
      test: text => /[A-Z]+/.test(text),
      message: 'Password requires at least one uppercase letter.',
    }
    const number = {
      test: text => /[0-9]+/.test(text),
      message: 'Password requires at least one number.',
    }
    const special = {
      test: text => /[-!$%^&*@#()_+|~`{}\[\]:";'<>?,.\/]/.test(text),
      message: 'Password requires at least one special character.',
    }
    const same = {
      test: text => text === pass2,
      message: 'Passwords must match.',
    }
    const length = {
      test: text => text.length >= 8,
      message: 'Password must be eight characters.',
    }
    const validations = [lowercase, uppercase, number, special, length, same]
    const results = validations.filter(validation => !validation.test(pass1))
    return results
  }

  const inputEmail = useRef(null)
  const inputPass1 = useRef(null)
  const inputPass2 = useRef(null)
  const codeRef = useRef(null)
  const scrollRef = useRef(null)
  const lottieRef = useRef(null)

  let codeCont = true
  if (show && (!checked || !codePassed)) {
    codeCont = false
  }

  let allContinue = cont1 && cont2 && pass1 == pass2 && codeCont

  let total1 = passTotal?.filter(a => a.message == 'Password must be eight characters.')?.length < 1
  let total2 = passTotal?.filter(a => a.message == 'Password requires at least one number.')?.length < 1
  let total3 = passTotal?.filter(a => a.message == 'Password requires at least one special character.')?.length < 1
  let total4 = passTotal?.filter(a => a.message == 'Password requires at least one lowercase letter.')?.length < 1
  let total5 = passTotal?.filter(a => a.message == 'Password requires at least one uppercase letter.')?.length < 1

  let checkRed = () => {
    console.log('passTotal', passTotal)
    if (passTotal?.length <= 1) {
      if (passTotal?.length == 1) {
        if (passTotal?.filter(a => a.message == 'Passwords must match.')?.length > 0) {
          setRed(false)
        } else {
          setRed(true)
        }
      } else {
        setRed(false)
      }
    } else {
      setRed(true)
    }
  }

  if (passTotal?.length == 1 && passTotal?.filter(a => a.message == 'Passwords must match.')?.length > 0) {
    red = false
  }
  if (passTotal?.length == 0) {
    red = false
  }

  let red2 = pass1 && pass2 && matchErr

  return (
    <View style={localStyles.box}>
      <BackgroundHeader title={'Sign Up'} close closeFn={closeFn} />
      <SignUpProgress complete={1} />
      <KeyboardAwareScrollView ref={scrollRef} style={{flex: 1, alignSelf: 'stretch'}} enableResetScrollToCoords={true}>
        <View style={localStyles.boxTop}>
          <TextReg style={styles.signUpTitleLead}>Email Address</TextReg>
          <TextInput
            style={styles.signUpTextInput}
            onChangeText={handleEmail}
            ref={inputEmail}
            returnKeyType={'next'}
            value={email}
            onSubmitEditing={() => inputPass1.current.focus()}
            selectionColor={'#FFF'}
            keyboardAppearance="dark"
          />
          {error1 && (
            <View style={styles.signUpErrorBox}>
              <TextReg style={styles.signUpErrorText}>Incorrect Email Format</TextReg>
            </View>
          )}
          {error2 && (
            <View style={styles.signUpErrorBox}>
              <TextReg style={styles.signUpErrorText}>An account with the given email already exists.</TextReg>
            </View>
          )}
          <TouchableOpacity onPress={() => setInfo(!info)} style={{flexDirection: 'row', alignSelf: 'baseline', alignItems: 'center'}}>
            <TextReg style={styles.signUpPasswordConfirmText}>Password</TextReg>
            <Image source={infoPng} style={{height: 18, width: 18, marginLeft: 6, marginTop: 20, opacity: 0.7}} />
          </TouchableOpacity>
          {info && (
            <View
              style={{
                height: 140,
                width: ScreenWidth - 60,
                borderRadius: 14,
                borderColor: '#523fe0',
                borderWidth: 1,
                padding: 10,
                marginTop: 10,
                marginBottom: 10,
                position: 'relative',
              }}>
              <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: 2}}>
                {total1 ? (
                  <View
                    style={{height: 20, width: 20, borderRadius: 10, borderColor: '#00FFBD', borderWidth: 1, backgroundColor: '#00FFBD'}}
                  />
                ) : (
                  <View style={{height: 20, width: 20, borderRadius: 10, borderColor: '#00FFBD', borderWidth: 1}} />
                )}
                <TextReg style={{marginLeft: 6, fontSize: 17, color: '#00FFBD'}}>{`8 characters min`}</TextReg>
              </View>
              <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: 2}}>
                {total2 ? (
                  <View
                    style={{height: 20, width: 20, borderRadius: 10, borderColor: '#00FFBD', borderWidth: 1, backgroundColor: '#00FFBD'}}
                  />
                ) : (
                  <View style={{height: 20, width: 20, borderRadius: 10, borderColor: '#00FFBD', borderWidth: 1}} />
                )}
                <TextReg style={{marginLeft: 6, fontSize: 17, color: '#00FFBD'}}>{`1 number`}</TextReg>
              </View>
              <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: 2}}>
                {total3 ? (
                  <View
                    style={{height: 20, width: 20, borderRadius: 10, borderColor: '#00FFBD', borderWidth: 1, backgroundColor: '#00FFBD'}}
                  />
                ) : (
                  <View style={{height: 20, width: 20, borderRadius: 10, borderColor: '#00FFBD', borderWidth: 1}} />
                )}
                <TextReg style={{marginLeft: 6, fontSize: 17, color: '#00FFBD'}}>{`1 special character`}</TextReg>
              </View>
              <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: 2}}>
                {total4 ? (
                  <View
                    style={{height: 20, width: 20, borderRadius: 10, borderColor: '#00FFBD', borderWidth: 1, backgroundColor: '#00FFBD'}}
                  />
                ) : (
                  <View style={{height: 20, width: 20, borderRadius: 10, borderColor: '#00FFBD', borderWidth: 1}} />
                )}
                <TextReg style={{marginLeft: 6, fontSize: 17, color: '#00FFBD'}}>{`1 lowercase`}</TextReg>
              </View>
              <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: 2}}>
                {total5 ? (
                  <View
                    style={{height: 20, width: 20, borderRadius: 10, borderColor: '#00FFBD', borderWidth: 1, backgroundColor: '#00FFBD'}}
                  />
                ) : (
                  <View style={{height: 20, width: 20, borderRadius: 10, borderColor: '#00FFBD', borderWidth: 1}} />
                )}
                <TextReg style={{marginLeft: 6, fontSize: 17, color: '#00FFBD'}}>{`1 uppercase`}</TextReg>
              </View>
              <TouchableOpacity onPress={() => setInfo(false)} style={{position: 'absolute', top: 0, right: 0}}>
                <Image source={closeX} style={{height: 24, width: 24, opacity: 0.9, margin: 10}} />
              </TouchableOpacity>
            </View>
          )}
          <TextInput
            style={{...styles.signUpTextInput, borderColor: red ? '#F00' : '#fff'}}
            onChangeText={setPass1}
            value={pass1}
            ref={inputPass1}
            returnKeyType={'next'}
            onSubmitEditing={() => inputPass2.current.focus()}
            secureTextEntry
            selectionColor={'#FFF'}
            keyboardAppearance="dark"
            onBlur={() => checkRed()}
          />
          <TextReg style={styles.signUpPasswordConfirmText}>Confirm Password</TextReg>
          <TextInput
            style={{...styles.signUpTextInput, borderColor: red2 ? '#F00' : '#fff'}}
            onChangeText={setPass2}
            value={pass2}
            ref={inputPass2}
            returnKeyType={'done'}
            onSubmitEditing={() => Keyboard.dismiss()}
            secureTextEntry
            selectionColor={'#FFF'}
            keyboardAppearance="dark"
            onBlur={() => checkRed()}
          />
          {pass1 && pass2 && matchErr && (
            <View style={styles.signUpErrorBox}>
              <TextReg style={styles.signUpErrorText}>Passwords must match</TextReg>
            </View>
          )}
          <TouchableOpacity
            style={{flexDirection: 'row', alignItems: 'center', marginTop: 20}}
            onPress={() => {
              if (show) {
                setShowRefErr(false)
                setCode('')
              }
              setShow(!show)
            }}>
            <TextReg style={{color: '#00FFBD', fontSize: 18}}>{'Have an invite code?'}</TextReg>
            <Image source={show ? arrowUp : arrowDown} style={{height: 18, width: 26, marginLeft: 10}} />
          </TouchableOpacity>
          {show && (
            <>
              <TextReg style={styles.signUpPasswordConfirmText}>Referral Code</TextReg>
              <TextInput
                style={styles.signUpTextInput}
                onChangeText={text => {
                  setCode(text)
                  setShowRefErr(false)
                }}
                value={code}
                ref={codeRef}
                returnKeyType={'done'}
                onSubmitEditing={() => {
                  Keyboard.dismiss()
                }}
                onBlur={() => checkReferral()}
                selectionColor={'#FFF'}
                keyboardAppearance="dark"
                placeholder={'00000000'}
              />
              {!codePassed && showRefErr && (
                <View style={styles.signUpErrorBox}>
                  <TextReg style={styles.signUpErrorText}>Incorrect Referral Code</TextReg>
                </View>
              )}
              <View style={{flexDirection: 'row', alignItems: 'center', alignSelf: 'stretch', marginTop: 20}}>
                <TouchableOpacity style={styles.toggleAcknowledgeView} onPress={() => setChecked(!checked)} activeOpacity={1}>
                  <Image
                    source={agreedYes}
                    style={[
                      styles.unit21ToggleCheckImg,
                      {
                        opacity: checked ? 1 : 0,
                      },
                    ]}
                  />
                </TouchableOpacity>
                <View style={{flexDirection: 'row'}}>
                  <TextReg style={styles.unit21AcknowledgeTitle}>{'I accept the'}</TextReg>
                  <TouchableOpacity style={{marginLeft: 4}} onPress={() => termsLink()} activeOpacity={1}>
                    <TextBold style={{color: '#00FFBD'}}>{'Terms and Conditions'}</TextBold>
                  </TouchableOpacity>
                </View>
              </View>
              <View style={{height: 30, width: 30}} />
            </>
          )}
        </View>
      </KeyboardAwareScrollView>
      {loading ? (
        <View
          style={{
            flexDirection: 'column',
            alignItems: 'center',
          }}>
          <LottieView
            ref={lottieRef}
            style={{
              width: 60,
              height: 60,
              opacity: 0.9,
            }}
            source={require('../../../imgs/lotti/loading-white-dots.json')}
            autoPlay
          />
        </View>
      ) : (
        <>
          {!hideAndroidCont && (
            <TouchableOpacity
              style={{
                ...commonStyles.button,
                alignSelf: 'stretch',
                marginBottom: 10,
                opacity: allContinue ? 1 : 0.3,
              }}
              onPress={() => next(allContinue)}>
              <TextReg style={{color: '#000', letterSpacing: 1.2, fontSize: 20}}>CONTINUE</TextReg>
            </TouchableOpacity>
          )}
        </>
      )}
    </View>
  )
}

export default Auth1

const localStyles = StyleSheet.create({
  box: {
    flex: 1,
    padding: 24,
    backgroundColor: '#28283D',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  boxTop: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    alignSelf: 'stretch',
  },
})
