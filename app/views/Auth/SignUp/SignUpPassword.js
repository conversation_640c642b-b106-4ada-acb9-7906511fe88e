import React, {Component} from 'react'
import {View, TextInput, TouchableOpacity, Platform} from 'react-native'
import {connect} from 'react-redux'
import Heap from '@heap/react-native-heap'
import LottieView from 'lottie-react-native'

import styles from '../styles'
import {TextReg, TextBold} from '../../../components'

class SignUpPassword extends Component {
  constructor() {
    super()
    this.state = {
      error: null,
      errorMessage: null,
      loading: false,
      showLoginButton: false,
      readyToCont: false,
      password: '',
      passwordConfirm: '',
    }
    this.inputs = {}
  }

  componentDidUpdate(prevProps) {
    if (this.props.cleanErrors != prevProps.cleanErrors) {
      this.setState({error: null, errorMessage: null})
    }
  }

  handlePassword = text => {
    if (this.props.completedPage > 1) {
      return
    }
    this.setState({password: text}, () => {
      const validPassword = this.validatePasswords()
      if (validPassword.length > 0) {
        this.setState({readyToCont: false})
      } else {
        this.setState({readyToCont: true})
      }
    })
  }

  handlePasswordConfirm = text => {
    if (this.props.completedPage > 1) {
      return
    }
    this.setState({passwordConfirm: text}, () => {
      const validPassword = this.validatePasswords()
      if (validPassword.length > 0) {
        this.setState({readyToCont: false})
      } else {
        this.setState({readyToCont: true})
      }
    })
  }

  validatePasswords = () => {
    //validation
    const lowercase = {
      test: text => /[a-z]+/.test(text),
      message: 'Password requires at least one lowercase letter.',
    }
    const uppercase = {
      test: text => /[A-Z]+/.test(text),
      message: 'Password requires at least one uppercase letter.',
    }
    const number = {
      test: text => /[0-9]+/.test(text),
      message: 'Password requires at least one number.',
    }
    const special = {
      test: text => /[-!$%^&*@#()_+|~`{}\[\]:";'<>?,.\/]/.test(text),
      message: 'Password requires at least one special character.',
    }
    const same = {
      test: text => text === this.state.passwordConfirm,
      message: 'Passwords must match.',
    }
    const length = {
      test: text => text.length >= 8,
      message: 'Password must be eight characters.',
    }
    const validations = [lowercase, uppercase, number, special, length, same]
    const results = validations.filter(validation => !validation.test(this.state.password))
    return results
  }

  handleSubmitPassword = () => {
    this.setState({
      error: null,
      errorMessage: null,
      showLoginButton: false,
    })

    const validPassword = this.validatePasswords()

    if (validPassword.length > 0) {
      this.setState({
        error: true,
        errorMessage: validPassword[0].message,
      })
    } else {
      const email = this.props.email.trim().toLowerCase()
      this.setState({loading: true})
      let UTMCodes = ''
      if (Platform.OS === 'ios') {
        UTMCodes += '&platform=ios'
      } else {
        UTMCodes += '&platform=android'
      }
      this.props.updatePassword(this.state.password)
      this.props.AuthService.signUp(email, this.state.password)
        .then(user => {
          console.log('after signup user', user)
          this.props.updateCognitoUser(user)
          return this.props.WebService.createUser({
            cognitoId: user.userSub,
            UTMCodes,
          })
        })
        .then(res => {
          console.log('after create user', res)
          //indentify heap
          Heap.identify(res.data?.id)
          this.setState({loading: false})
          this.props.completePage(1)
        })
        .catch(async err => {
          let errorMessage = err.message
          let showLoginButton = false

          if (err.code === 'UsernameExistsException') {
            // try to sign in with username & password

            try {
              const user = await this.props.AuthService.login(email, this.state.password)
              this.props.updateCognitoUser(user)
              //we got a user that means we have passed step 3 (email verification)
              if (user.challengeName === 'MFA_SETUP') {
                // go to step 4
                const payload = {
                  sessionToken: user.Session,
                  cognitoId: user.username,
                  challengeName: user.challengeName,
                }
                this.props.WebService.checkMFA(payload)
                  .then(res => {
                    this.props.update2FASecret(res.data.secret)
                    this.props.updateSession(res.data.session)
                    this.setState({loading: false})
                    this.props.completePage(1)
                  })
                  .catch(err => {
                    //console.log('WebService.checkMFA err', err)
                  })

                return
              } else if (user.challengeName === 'SOFTWARE_TOKEN_MFA') {
                // have user sign in on login page
                errorMessage = 'Account already exists, login to continue registering'
                showLoginButton = true
                //clear out email & password fields
                this.setState({password: '', passwordConfirm: ''})
              }
            } catch (signInErr) {
              if (signInErr.code === 'UserNotConfirmedException') {
                // they still have to do checkMFA - continue with signUp
                this.props.updatePassword(this.state.password)
                this.setState({loading: false})
                this.props.completePage(1)
                return
              } else if (signInErr.code === 'Password Incorrect') {
                // this is if userNotConfirmed but also password is different, no feedback from cognito that password is different
                // I guess they can always change it later
              } else {
                this.setState({loading: false})
              }
            }
          }

          this.setState({
            error: true,
            errorMessage,
            loading: false,
            showLoginButton,
          })
        })
    }
  }

  render() {
    return (
      <View style={[styles.signUpSpaceBetween, {opacity: this.props.visible ? 1 : 0.3}]}>
        <View style={styles.signUpPageContainer}>
          <TextReg style={styles.signUpTitleLead}>Create a Password</TextReg>
          <TextInput
            style={[styles.signUpTextInput, {marginTop: 10}]}
            onChangeText={this.handlePassword}
            value={this.state.password}
            ref={input => (this.inputs.password = input)}
            returnKeyType={'next'}
            onSubmitEditing={() => this.inputs.passwordConfirm.focus()}
            secureTextEntry
            selectionColor={'#FFF'}
            keyboardAppearance="dark"
          />
          <TextReg style={styles.signUpPasswordConfirmText}>Confirm Password</TextReg>
          <TextInput
            style={[styles.signUpTextInput, {marginTop: 16}]}
            onChangeText={this.handlePasswordConfirm}
            value={this.state.passwordConfirm}
            ref={input => (this.inputs.passwordConfirm = input)}
            returnKeyType={'done'}
            onSubmitEditing={this.handleSubmitPassword}
            secureTextEntry
            selectionColor={'#FFF'}
            keyboardAppearance="dark"
          />
          {this.state.error && (
            <View>
              <View style={styles.signUpErrorBox}>
                <TextReg style={styles.signUpErrorText}>{this.state.errorMessage}</TextReg>
              </View>

              {this.state.showLoginButton ? (
                <TouchableOpacity onPress={this.props.closeSignUp}>
                  <View style={styles.signUpErrorRetryButton}>
                    <TextReg style={{color: '#05868e', fontSize: 18}}>Login</TextReg>
                  </View>
                </TouchableOpacity>
              ) : (
                <TouchableOpacity onPress={this.handleSubmitPassword}>
                  <View style={styles.signUpErrorRetryButton}>
                    <TextReg style={{color: '#05868e', fontSize: 18}}>Retry</TextReg>
                  </View>
                </TouchableOpacity>
              )}
            </View>
          )}

          {this.state.loading && (
            <LottieView
              ref={animation => {
                this.animation = animation
              }}
              style={{width: 60, height: 60, opacity: 0.9, marginTop: 10}}
              source={require('../../../imgs/lotti/loading-white-dots.json')}
              autoPlay
            />
          )}
        </View>
        <TouchableOpacity
          style={this.state.readyToCont ? styles.signUpNextButton : styles.signUpNextButtonInactive}
          onPress={!this.state.loading ? () => this.handleSubmitPassword() : null}>
          <TextBold style={{color: '#000', fontSize: 20}}>CONTINUE</TextBold>
        </TouchableOpacity>
      </View>
    )
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  AuthService: state.auth.AuthService,
  user: state.user.user,
})

export default connect(mapStateToProps)(SignUpPassword)
