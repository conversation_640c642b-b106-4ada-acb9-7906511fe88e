import React from 'react'
import {View} from 'react-native'

import {TextBold, TextReg} from '../../components'

import styles from '../styles'

const SignUpProgress = props => {
  let {complete} = props
  let total = 4
  let progressArr = Array.from(Array(total).keys())
  let showProgress = progressArr.map((a, k) => {
    return (
      <View
        key={k}
        style={{
          alignSelf: 'stretch',
          height: 10,
          backgroundColor: k <= complete - 1 ? '#00FFBD' : '#3D3D50',
          borderRadius: 0,
          flex: 1,
          marginRight: k == total - 1 ? 0 : 1,
        }}
      />
    )
  })
  return (
    <View
      style={{
        alignSelf: 'stretch',
        height: 10,
        backgroundColor: '#3D3D50', //'#00FFBD',
        borderRadius: 4,
        margin: 20,
        marginLeft: 2,
        marginRight: 2,
        flexDirection: 'row',
        overflow: 'hidden',
        marginTop: 14,
      }}>
      {showProgress}
    </View>
  )
}
export default SignUpProgress
