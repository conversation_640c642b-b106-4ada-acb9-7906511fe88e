import React, {Component} from 'react'
import {View, StatusBar, TouchableOpacity, Image, AppState, Clipboard, Dimensions} from 'react-native'
import {connect} from 'react-redux'

import AsyncStorage from '@react-native-async-storage/async-storage'
import SwiperFlatList from 'react-native-swiper-flatlist'

import SignUpEmail from './SignUpEmail'
import SignUpPassword from './SignUpPassword'
import SignUpEmailVerify from './SignUpEmailVerify'
import SignUpNames from './SignUpNames'
import SignUpQuestions from './SignUpQuestions'
import SignUpLocation from './SignUpLocation'
import SignUpPactSafe from './SignUpPactSafe'
import SignUpComplete from './SignUpComplete'
import {unAuth} from '../../../store/auth/auth.actions'
import AuthFlow from './AuthFlow'

import styles from '../styles'
import {TextReg, Background} from '../../../components'
const {width: ScreenWidth, height: ScreenHeight} = Dimensions.get('window')

class SignUpIndex extends Component {
  constructor(props) {
    super(props)
    let {email, password, countryCode, regionCode, resume, firstName, lastName, infoEntered, membershipAgreementSigned} = props.route.params
    console.log(
      'signupIndex',
      email,
      password,
      countryCode,
      regionCode,
      resume,
      firstName,
      lastName,
      infoEntered,
      membershipAgreementSigned,
    )
    this.state = {
      checkPage: null,
      signUpPage: 0,
      email: email || '',
      password: password || '',
      userID: '',
      appState: AppState.currentState,
      completedPage: 0,
      countryCode: countryCode || '',
      regionCode: regionCode || '',
      resume: resume || false,
      newSid: null,
      firstName: firstName || '',
      lastName: lastName || '',
      cleanErrors: 0,
    }

    //  AppState.addEventListener('change', this.handleAppStateChange)
  }

  componentDidMount() {
    //  this.props.dispatch(unAuth())
    /*
    let {resume, emailVerify, infoEntered, membershipAgreementSigned, account} = this.props.route.params
    console.log('signup index mount', resume, emailVerify, infoEntered, membershipAgreementSigned, account)
    return
    if (this.props.route.params.resume) {
      console.log('this.props.route.params', this.props.route.params)
      if (!emailVerify) {
        this.completePage(2)
        console.log('1')
      } else if (!infoEntered) {
        this.completePage(2)
        console.log('2')
      } else if (!membershipAgreementSigned) {
        this.completePage(3)
        console.log('3')
      } else if (account.length === 0) {
        this.completePage(4)
        console.log('4')
      }
    }
    */
  }
  /*
  handleAppStateChange = async nextAppState => {
    if (this.state.appState.match(/inactive|background/) && nextAppState === 'active') {
      const clipboardCopy = await Clipboard.getString()
      if (clipboardCopy.length === 6 && !isNaN(clipboardCopy)) {
        this.setState({twoFACode: clipboardCopy})
      }
    }
    this.setState({appState: nextAppState})
  }

  updateEmail = email => {
    let {cleanErrors} = this.state
    this.setState({email, cleanErrors: cleanErrors + 1})
  }

  updatePassword = password => {
    this.setState({password})
  }

  goBackToLogin = () => {
    this.props.navigation.goBack()
  }

  setSid = newSid => {
    this.setState({newSid})
  }

  scrollToPage = num => {
    console.log('scrollToPage', num)
    //this.setState({signUpPage: num}, this.refs.swiper._scrollToIndex(num))
  }

  onScrollEnd = index => {
    let dontGoBack = false
    if (this.state.completedPage < index) {
      this.scrollToPage(index - 1)
      this.setState({checkPage: index - 1})
    } else if (this.state.completedPage > index) {
      if (this.state.completedPage === 2 && index <= 1) {
        dontGoBack = true
      }
      if (this.state.completedPage === 3 && index <= 2) {
        dontGoBack = true
      }
      if (this.state.completedPage === 4 && index <= 5) {
        dontGoBack = true
      }
      if (dontGoBack) {
        this.scrollToPage(this.state.completedPage)
      }
    } else {
      this.setState({signUpPage: index})
    }
  }

  clearCheckPage = () => {
    this.setState({checkPage: null})
  }

  completePage = page => {
    if (page < 3) {
      this.emailVerifyChecks(page)
    }
    let completedPage = page + 1
    this.setState({completedPage}, this.scrollToPage(completedPage))
  }

  emailVerifyChecks = page => {
    const timestamp = new Date().getTime().toString()
    const saveSignupProgression = page + '-' + timestamp

    AsyncStorage.getItem(`SignUpComplete-${this.state.email.toLowerCase()}`).then(res => {
      if (res) {
        const resStep = res.split('-')[0]
        const resTimestamp = res.split('-')[1]
        console.log('emailVerifyChecks', resStep, resTimestamp)

        if (resStep === '1') {
          console.log('user got to verify email step and then stopped')
        }

        if (page > resStep) {
          AsyncStorage.setItem(`SignUpComplete-${this.state.email.toLowerCase()}`, saveSignupProgression)
        }
      }
    })
  }

  completePageNoScroll = page => {
    this.setState({completedPage: page + 1})
  }

  updateCognitoUser = user => {
    this.setState({cognitoUser: user})
  }

  update2FASecret = secret => {
    this.setState({secret})
  }

  updateUserID = id => {
    this.setState({userID: id})
  }

  updateSession = session => {
    const cognitoUser = this.state.cognitoUser
    cognitoUser.Session = session
    this.setState({cognitoUser})
  }

  updateLocationCodes = (countryCode, regionCode) => {
    this.setState({countryCode, regionCode})
  }

  updateFirstName = text => {
    this.setState({firstName: text})
  }

  updateLastName = text => {
    this.setState({lastName: text})
  }

  closeSignUp = (goBack = false) => {
    this.setState(
      {
        signUpPage: 0,
        email: '',
        password: '',
        userID: '',
        completedPage: 0,
        countryCode: '',
        regionCode: '',
      },
      () => {
        if (goBack) {
          this.props.navigation.goBack()
        }
      },
    )
  }

  goBackAStep = () => {
    if (this.state.signUpPage === 0) {
      this.goBackToLogin()
    } else {
      this.scrollToPage(this.state.signUpPage - 1)
    }
  }
  */

  render() {
    /*
    let {completedPage} = this.state
    let stackDepth = 1
    if (completedPage == 0) {
      //this.props.navigation.navigate('Auth1', {stackDepth})
      //return
    }
    */

    let {email, password, countryCode, regionCode, resume, firstName, lastName, infoEntered, membershipAgreementSigned} =
      this.props.route.params
    let before = {email, password, countryCode, regionCode, resume, firstName, lastName, infoEntered, membershipAgreementSigned}

    return (
      <View style={styles.signUpContainer}>
        <AuthFlow navigation={this.props.navigation} before={before} />
      </View>
    )
  }
}

/*
<View style={styles.signUpHeader}>
  <TouchableOpacity style={styles.backToLoginButton} onPress={() => this.goBackAStep()}>
    <Image source={require('../../../imgs/backToSettings.png')} style={styles.backToLoginImg} />
  </TouchableOpacity>
  <TextReg style={styles.signUpHeaderText}>Sign Up</TextReg>
</View>
<View
  style={{
    marginBottom: 30,
    height: ScreenHeight - 110,
  }}>
  <SwiperFlatList
    ref="swiper"
    onMomentumScrollEnd={this.onScrollEnd}
    index={0}
    scrollEventThrottle={16}
    showPagination
    paginationDefaultColor={'#7E7E89'}
    paginationActiveColor={'#00ffc3'}
    paginationStyle={{marginBottom: -2}}
    paginationStyleItem={styles.paginationDots}>
    <SignUpEmail
      visible={this.state.completedPage <= 1}
      completePage={this.completePage}
      checkPage={this.state.checkPage}
      clearCheckPage={this.clearCheckPage}
      completePageNoScroll={this.completePageNoScroll}
      updateEmail={this.updateEmail}
      email={this.state.email}
      resume={this.state.resume}
      signUpPage={this.state.signUpPage}
      completedPage={this.state.completedPage}
    />
    {this.state.completedPage >= 0 && (
      <SignUpPassword
        visible={this.state.completedPage <= 1}
        completePage={this.completePage}
        updatePassword={this.updatePassword}
        email={this.state.email}
        updateCognitoUser={this.updateCognitoUser}
        update2FASecret={this.update2FASecret}
        updateSession={this.updateSession}
        closeSignUp={this.closeSignUp}
        completedPage={this.state.completedPage}
        cleanErrors={this.state.cleanErrors}
      />
    )}
    {this.state.completedPage >= 1 && (
      <SignUpEmailVerify
        visible={this.state.completedPage === 2}
        completePage={this.completePage}
        email={this.state.email}
        password={this.state.password}
        updateCognitoUser={this.updateCognitoUser}
        update2FASecret={this.update2FASecret}
        updateSession={this.updateSession}
        signUpPage={this.state.signUpPage}
        completedPage={this.state.completedPage}
      />
    )}
    {this.state.completedPage >= 2 && (
      <SignUpLocation
        visible={this.state.completedPage === 3}
        completePage={this.completePage}
        updateLocationCodes={this.updateLocationCodes}
        firstName={this.state.firstName}
        lastName={this.state.lastName}
        regionCode={this.state.regionCode}
        countryCode={this.state.countryCode}
        signUpPage={this.state.signUpPage}
        updateFirstName={this.updateFirstName}
        updateLastName={this.updateLastName}
      />
    )}
    {this.state.completedPage >= 3 && (
      <SignUpPactSafe
        visible={this.state.completedPage === 4}
        email={this.state.email}
        completePage={this.completePage}
        userID={this.state.userID}
        countryCode={this.state.countryCode}
        regionCode={this.state.regionCode}
        newSid={this.state.newSid}
      />
    )}
  </SwiperFlatList>
</View>
<Background />
*/

SignUpIndex.navigationOptions = {
  header: null,
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
})

export default connect(mapStateToProps)(SignUpIndex)
