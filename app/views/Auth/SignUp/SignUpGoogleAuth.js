import React, { Component } from 'react'
import { View, TextInput, TouchableOpacity, Image, Clipboard, Keyboard } from 'react-native'
import { connect } from 'react-redux'
import AsyncStorage from '@react-native-async-storage/async-storage'
import DeviceInfo from 'react-native-device-info'
import LottieView from 'lottie-react-native'
import * as Keychain from 'react-native-keychain'

import styles from '../styles'
import { TextReg, TextBold } from '../../../components'
import { updateDeviceId } from '../../../store/auth/auth.actions'

import { updateUser } from '../../../store/user/user.actions'

import copyButtonImg from '../../../imgs/copyButtonGreen.png'
import checkMarkCopiedImg from '../../../imgs/checkMarkCopied.png'

const model = DeviceInfo.getModel()
const manufacturer = DeviceInfo.getManufacturer()
const systemName = DeviceInfo.getSystemName()
const systemVersion = DeviceInfo.getSystemVersion()

class SignUpGoogleAuth extends Component {
  constructor() {
    super()
    this.state = {
      code: '',
      textInputActive: false,
      loading: false,
      showCopied: false,
      readyToCont: false,
      error: 0
    }
    this.inputs = {}
  }

  componentDidUpdate(prevProps) {
    if (this.props.twoFACode !== prevProps.twoFACode) {
      this.setState({ code: this.props.twoFACode })
      this.verifyCode(this.props.twoFACode)
    }
  }

  showBlock = (num) => {
    if (this.state.code.length > num) {
      return this.state.code[num]
    }
    return ''
  }

  handleVerificationCode = (text = this.state.code) => {
    if (text.length > 6) {
      const newChar = text[text.length - 1]
      this.setState({ code: newChar })
    }
    if (text.length === 6) {
      this.setState({ code: text })
      this.verifyCode(text)
      return
    }
    this.setState({ readyToCont: false })
    if (text.length < 6) {
      this.setState({ code: text })
    }
  }

  verifyCode = (text = this.state.code) => {
    this.setState({ loading: true })
    this.props.AuthService.verifyTotpToken(this.props.cognitoUser, text)
      .then((res) => {
        Keyboard.dismiss()
        const { jwtToken } = res.accessToken

        this.props.WebService.loginToSalt(
          {
            cognitoId: res.accessToken.payload.username,
            model,
            manufacturer,
            osVersion: systemVersion,
            platform: systemName,
            deviceId: this.props.deviceId || undefined
          },
          jwtToken
        )
          .then(async (res) => {
            const newSid = res.headers['sess-auth']
            const { deviceId } = res.data
            AsyncStorage.setItem('DEVICEID', deviceId)
            this.props.dispatch(updateDeviceId(deviceId))
            this.props.setSid(newSid)
            //await Keychain.setInternetCredentials('token', 'user', newSid)
            this.props.WebService.updateSID(newSid)
            return this.props.WebService.getSaltUser()
          })
          .then((res) => {
            this.setState({ loading: false, readyToCont: true })
            updateUser(res.data)
            this.props.updateUserID(res.data.id)
            this.props.completePage(3)
          })
      })
      .catch((err) => {
        //
        this.setState({ loading: false, code: '', error: 1 })
      })
  }

  copy2FACode = () => {
    Clipboard.setString(this.props.secret)
    this.setState({ showCopied: true }, () => {
      setTimeout(() => {
        this.setState({ showCopied: false })
      }, 1200)
    })
  }

  focusTextInput = () => {
    this.setState({ textInputActive: true })
  }

  blurTextInput = () => {
    this.setState({ textInputActive: false })
  }

  render() {
    const highlightInput = this.state.textInputActive ? this.state.code.length : false
    return (
      <View style={[styles.signUpSpaceBetween, { opacity: this.props.visible ? 1 : 0.3 }]}>
        <View style={styles.signUpPageContainer}>
          <TextReg style={styles.signUpTitleLead}>
            Using the Google Authenticator app, set up your 2-Factor Authentication.
          </TextReg>
          <View style={styles.signUpGoogleAuthBox}>
            <TextReg style={styles.signUpTitleSmall}>Copy this code into Authenticator</TextReg>
            <View style={styles.signUpCopyCodeBox}>
              <TextReg style={styles.signUpCopyCodeText}>{this.props.secret}</TextReg>
              <TouchableOpacity onPress={this.copy2FACode}>
                {this.state.showCopied ? (
                  <View style={styles.signUpCopyCodeBoxButtonActive}>
                    <Image
                      source={checkMarkCopiedImg}
                      style={{
                        height: 16,
                        width: 20
                      }}
                    />
                  </View>
                ) : (
                  <View style={styles.signUpCopyCodeBoxButton}>
                    <Image
                      source={copyButtonImg}
                      style={{
                        height: 20,
                        width: 20
                      }}
                    />
                  </View>
                )}
              </TouchableOpacity>
            </View>
          </View>
          <View style={styles.signUpGoogleAuthBoxLower}>
            <TextReg style={styles.signUpTitleSmall}>Enter 6 digit code</TextReg>

            <TouchableOpacity activeOpacity={1} onPress={() => this.inputs.verificationInput.focus()}>
              <View style={styles.signUpVerifcationBlocksBox2FA}>
                <View
                  style={[styles.signUpVerifcationBlock, highlightInput === 0 && styles.signUpVerifcationBlockActive]}
                >
                  <TextReg style={styles.signUpVerifcationBlockText}>{this.showBlock(0)}</TextReg>
                </View>
                <View
                  style={[styles.signUpVerifcationBlock, highlightInput === 1 && styles.signUpVerifcationBlockActive]}
                >
                  <TextReg style={styles.signUpVerifcationBlockText}>{this.showBlock(1)}</TextReg>
                </View>
                <View
                  style={[styles.signUpVerifcationBlock, highlightInput === 2 && styles.signUpVerifcationBlockActive]}
                >
                  <TextReg style={styles.signUpVerifcationBlockText}>{this.showBlock(2)}</TextReg>
                </View>
                <View
                  style={[styles.signUpVerifcationBlock, highlightInput === 3 && styles.signUpVerifcationBlockActive]}
                >
                  <TextReg style={styles.signUpVerifcationBlockText}>{this.showBlock(3)}</TextReg>
                </View>
                <View
                  style={[styles.signUpVerifcationBlock, highlightInput === 4 && styles.signUpVerifcationBlockActive]}
                >
                  <TextReg style={styles.signUpVerifcationBlockText}>{this.showBlock(4)}</TextReg>
                </View>
                <View
                  style={[styles.signUpVerifcationBlock, highlightInput === 5 && styles.signUpVerifcationBlockActive]}
                >
                  <TextReg style={styles.signUpVerifcationBlockText}>{this.showBlock(5)}</TextReg>
                </View>
              </View>
            </TouchableOpacity>
            {this.state.loading && (
              <LottieView
                ref={(animation) => {
                  this.animation = animation
                }}
                style={{ width: 70, height: 70, opacity: 0.9, marginTop: 10 }}
                source={require('../../../imgs/lotti/loading-white-dots.json')}
                autoPlay
              />
            )}
            <TextInput
              style={styles.signUpTextInputEmailVerificationCode}
              onChangeText={this.handleVerificationCode}
              value={this.state.code}
              returnKeyType={'done'}
              ref={(input) => (this.inputs.verificationInput = input)}
              onSubmitEditing={() => this.verifyCode()}
              keyboardType={'numeric'}
              onFocus={this.focusTextInput}
              onBlur={this.blurTextInput}
              keyboardAppearance="dark"
            />
          </View>
        </View>
        <View>
          {this.state.error == 1 && (
            <TextReg
              style={{
                fontSize: 20,
                color: '#fb1f63',
                width: 300,
                height: 70,
                alignSelf: 'center',
                marginTop: 20
              }}
            >
              {`Your 2-Step Verification Code was incorrect. Please try again.`}
            </TextReg>
          )}
        </View>
        <TouchableOpacity onPress={!this.state.loading ? () => this.handleVerificationCode() : null}>
          <View style={this.state.readyToCont ? styles.signUpNextButton : styles.signUpNextButtonInactive}>
            <TextBold style={{ color: '#05868e', fontSize: 20 }}>Next</TextBold>
          </View>
        </TouchableOpacity>
      </View>
    )
  }
}

const mapStateToProps = (state) => ({
  WebService: state.auth.WebService,
  AuthService: state.auth.AuthService,
  user: state.user.user
})

export default connect(mapStateToProps)(SignUpGoogleAuth)
