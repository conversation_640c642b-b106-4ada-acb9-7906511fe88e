import React, {Component} from 'react'
import {View, TouchableOpacity, ScrollView, Image} from 'react-native'
import {connect} from 'react-redux'
import {WebView} from 'react-native-webview'
import LottieView from 'lottie-react-native'
import AsyncStorage from '@react-native-async-storage/async-storage'
import firebase from '@react-native-firebase/app'

import {decode} from 'html-entities'
import * as Keychain from 'react-native-keychain'

import {showPinScreen, isAuthed, updateFCMToken, updatePin} from '../../../store/auth/auth.actions'
import styles from '../styles'
import {TextReg} from '../../../components'
import config from '../../../config.json'

class SignUpPactSafe extends Component {
  constructor() {
    super()
    this.state = {
      contract: {body: null},
      signed: false,
      loading: false,
      loaded: false,
    }
    this.polling = null
  }

  componentDidMount() {
    if (this.props.countryCode != '' && this.props.regionCode != '') {
      this.getAgreement()
    }
  }

  componentDidUpdate(prevProps) {
    if (this.props.countryCode != '' && this.props.regionCode != '' && !(prevProps.countryCode != '' && prevProps.regionCode != '')) {
      this.getAgreement()
    }
  }

  getAgreement = () => {
    this.props.WebService.getUnsignedAgreement(
      'membership-agreement',
      this.props.countryCode,
      this.props.regionCode,
      config.pactSafe.accessId,
    )
      .then(res => {
        console.log('getAgreement res', res)
        this.setState({contract: res.data, loaded: true})
      })
      .catch(err => {
        console.log('getUnsignedAgreement err', err)
      })
  }

  agreePactSafe = async () => {
    if (!this.state.loaded) return
    if (this.props.newSid) {
      await Keychain.setInternetCredentials('token', 'user', this.props.newSid, {
        securityLevel: Keychain.SECURITY_LEVEL.ANY,
        storage: Keychain.STORAGE_TYPE.AES,
      })
    }
    this.setState({signed: true, loading: true})
    this.props.WebService.signAgreement({
      contractId: this.state.contract?.contract?.toString(),
      versionId: this.state.contract?.id,
      siteId: config.pactSafe.accessId,
    })
      .then(async res => {
        this.setState({loading: false})
        this.props.dispatch(updatePin('0'))
        this.sendFcmToken()
        let userEmail = this.props.email
        this.prefetchData(userEmail)
        //this.props.completePage(4);
      })
      .catch(err => {
        this.setState({loading: false})
      })
  }

  prefetchData = async userEmail => {
    const account = await this.props.WebService.getSaltAccount()
    const ref = account.data[0].ref.toString()
    AsyncStorage.setItem('REF', ref)
    AsyncStorage.setItem('LOGIN_EMAIL', userEmail)
    this.props.dispatch(showPinScreen(false))
    this.props.dispatch(isAuthed({email: userEmail, ref}))
  }

  sendFcmToken = () => {
    firebase
      .messaging()
      .getToken()
      .then(fcmToken => {
        if (fcmToken && fcmToken !== this.props.fcmToken) {
          this.props.WebService.updateFCMTokenAPI(fcmToken)
            .then(res => {
              this.props.dispatch(updateFCMToken(fcmToken))
            })
            .catch(err => {
              //console.log('updateFCMTokenAPI err', err)
            })
        }
      })
  }

  render() {
    const bodyDecoded = decode(this.state.contract.body)
    const contractStyling = `<style>
    div{
      overflow-x: hidden;
    }
    .ps-contract-body{
      margin-top: -6px;
    }
    .ps-section{
      margin-top: 10px;
      margin-bottom: 10px;
    }
    </style>`
    return (
      <View style={[styles.signUpPageContainer, {opacity: this.props.visible ? 1 : 0.3}]}>
        <TextReg style={styles.signUpTitlePactSafe}>Membership Agreement</TextReg>
        <ScrollView
          style={{
            width: 350,
            marginBottom: 20,
            position: 'absolute',
            top: 40,
            zIndex: 20,
          }}>
          <View
            style={{
              height: 320,
              width: 350,
              borderRadius: 14,
            }}>
            {this.state.loaded ? (
              <WebView
                source={{
                  html: `<div>${contractStyling} <div class="ps-contract-body ps-contract-full ps-contract">${bodyDecoded}</div></div>`,
                }}
                style={{height: 360, width: 350}}
                scalesPageToFit
                useWebKit={false}
              />
            ) : (
              <View
                style={{
                  width: 350,
                  flexDirection: 'column',
                  alignItems: 'center',
                  height: 320,
                }}>
                <LottieView
                  ref={animation => {
                    this.animation = animation
                  }}
                  style={{
                    width: 60,
                    height: 60,
                    opacity: 0.9,
                  }}
                  source={require('../../../imgs/lotti/loading-white-dots.json')}
                  autoPlay
                />
              </View>
            )}
          </View>
        </ScrollView>
        <View style={{height: 320, width: 350}} />

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: 310,
            height: 120,
            borderRadius: 14,
          }}>
          <TouchableOpacity onPress={this.agreePactSafe}>
            {this.state.signed ? (
              <Image source={require('../../../imgs/check-box.png')} style={{height: 40, width: 40}} />
            ) : (
              <Image source={require('../../../imgs/blank-check-box.png')} style={{height: 40, width: 40}} />
            )}
          </TouchableOpacity>
          {this.state.loading ? (
            <View
              style={{
                width: 260,
                flexDirection: 'column',
                alignItems: 'center',
                height: 60,
              }}>
              <LottieView
                ref={animation => {
                  this.animation = animation
                }}
                style={{
                  width: 60,
                  height: 60,
                  opacity: 0.9,
                }}
                source={require('../../../imgs/lotti/loading-white-dots.json')}
                autoPlay
              />
            </View>
          ) : (
            <TextReg style={styles.signUpAgreeText}>
              {`I have read, understand, and consent to the language and authorzations outlined in SALT's Membership Agreement.`}
            </TextReg>
          )}
        </View>
      </View>
    )
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
})

export default connect(mapStateToProps)(SignUpPactSafe)
