import React, {useEffect, useState, useRef} from 'react'
import {StyleSheet, View, Platform, TouchableOpacity, Image, ScrollView, TextInput, Keyboard} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {<PERSON>R<PERSON>, TextBold, BackgroundHeader, Button} from '../../../components'
import {useDispatch, useSelector} from 'react-redux'
import SignUpProgress from './SignUpProgress'

import AsyncStorage from '@react-native-async-storage/async-storage'
import DeviceInfo from 'react-native-device-info'
import LottieView from 'lottie-react-native'
import firebase from '@react-native-firebase/app'
import * as Keychain from 'react-native-keychain'

import {updateFCMToken, updateDeviceId} from '../../../store/auth/auth.actions'
import {updateAuthFlow} from '../../../store/auth/auth.actions'
import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'

const model = DeviceInfo.getModel()
const manufacturer = DeviceInfo.getManufacturer()
const systemName = DeviceInfo.getSystemName()
const systemVersion = DeviceInfo.getSystemVersion()

let Auth2 = ({navigation, route}) => {
  let dispatch = useDispatch()
  let WebService = useSelector(state => state.auth.WebService)
  let AuthService = useSelector(state => state.auth.AuthService)
  let deviceId = useSelector(state => state.auth.deviceId)
  let flow = useSelector(state => state.auth.flow)

  let [code, setCode] = useState('')
  let [cont, setCont] = useState(false)
  let [error, setError] = useState(false)
  let [loading, setLoading] = useState(false)
  let [active, setActive] = useState(false)
  let [sent, setSent] = useState(false)

  let inputCode = useRef(null)
  let refAnimation = useRef(null)

  useEffect(() => {
    //inputCode.current.focus()
    setActive(true)
  }, [])

  let closeFn = () => {
    navigation.navigate('Auth')
  }

  let next = allContinue => {
    //if (!allContinue) return
    //navigation.navigate('Auth3')
  }

  //import fmcToken
  let sendFcmToken = () => {
    firebase
      .messaging()
      .getToken()
      .then(fcmToken => {
        //if (fcmToken && fcmToken !== this.props.fcmToken) {
        WebService.updateFCMTokenAPI(fcmToken).then(res => {
          dispatch(updateFCMToken(fcmToken))
        })
        //}
      })
  }

  let update2FACheck = user => {
    const jwtToken = user.signInUserSession.accessToken.jwtToken
    WebService.loginToSalt(
      {
        model,
        manufacturer,
        osVersion: systemVersion,
        platform: systemName,
        deviceId: deviceId || undefined,
      },
      jwtToken,
    )
      .then(async res => {
        const newSid = res.headers['sess-auth']
        const {deviceId} = res.data
        AsyncStorage.setItem('DEVICEID', deviceId)
        dispatch(updateDeviceId(deviceId))
        await Keychain.setInternetCredentials('token', 'user', newSid, {
          securityLevel: Keychain.SECURITY_LEVEL.ANY,
          storage: Keychain.STORAGE_TYPE.AES,
        })
        WebService.updateSID(newSid)
        setLoading(false)
        navigation.navigate('Auth3')

        //this.setState({readyToCont: true})
        //this.props.completePage(2)
      })
      .catch(err => {
        console.log('login2salt err', err)
      })
  }

  let handleVerificationCode = (text = '') => {
    /*
    if (this.props.completed) {
      this.props.completePage(2)
      return
    }
    */
    if (text.length > 6) {
      setCont(false)
      return
    }
    setCode(text)
    if (text.length === 6) {
      const pin = text
      //get email
      let email = flow.email
      let password = flow.password

      setLoading(true)
      setError(false)
      WebService.verifyEmail({pin, email})
        .then(res => {
          Keyboard.dismiss()
          return AuthService.login(email, password)
        })
        .then(user => {
          dispatch(updateAuthFlow({cognitoUser: user}))
          update2FACheck(user)
        })
        .catch(err => {
          setLoading(false)
          setError('Error verifying code')
          setCode('')
        })
    }
  }

  let showBlock = num => {
    if (code.length > num) {
      return code[num]
    }
    return ''
  }

  let resendVerifyEmail = () => {
    //this.setState({loading: true, error: false, errorMessage: ''})
    setLoading(true)
    setError(false)
    //const email = this.props.email.trim().toLowerCase()
    let email = flow.email
    WebService.resendVerifyEmail(email)
      .then(res => {
        setLoading(false)
        setSent(true)
        setTimeout(() => {
          setSent(false)
        }, 2000)
      })
      .catch(err => {
        setLoading(false)
        setError('Error resending email verification')
      })
  }

  let allContinue = false //code.length == 6 //cont
  let highlightInput = active ? code.length : false

  return (
    <View style={localStyles.box}>
      <BackgroundHeader title={'Sign Up'} close closeFn={closeFn} />
      <SignUpProgress complete={2} />
      <View style={localStyles.boxTop}>
        <TouchableOpacity activeOpacity={1} onPress={() => inputCode.current.focus()} style={{alignSelf: 'stretch'}}>
          <TextReg style={styles.signUpTitleLead}>We sent an email to</TextReg>
          <TextReg style={styles.signUpTitleItalics}>{`${flow.email}`}</TextReg>

          <TextReg style={styles.signUpTitleLead}>Enter Verification Code</TextReg>

          <TextInput
            style={styles.signUpTextInputEmailVerificationCode}
            onChangeText={handleVerificationCode}
            returnKeyType={'done'}
            value={code}
            ref={inputCode}
            onFocus={() => setActive(true)}
            onBlur={() => setActive(false)}
            autoCapitalize={'none'}
            keyboardAppearance="dark"
          />
          <View style={{alignSelf: 'stretch', marginLeft: -2}}>
            <View style={styles.signUpVerifcationBlocksBox}>
              <View style={[styles.signUpVerifcationBlock, highlightInput === 0 && styles.signUpVerifcationBlockActive]}>
                <TextReg style={styles.signUpVerifcationBlockText}>{showBlock(0)}</TextReg>
              </View>
              <View style={[styles.signUpVerifcationBlock, highlightInput === 1 && styles.signUpVerifcationBlockActive]}>
                <TextReg style={styles.signUpVerifcationBlockText}>{showBlock(1)}</TextReg>
              </View>
              <View style={[styles.signUpVerifcationBlock, highlightInput === 2 && styles.signUpVerifcationBlockActive]}>
                <TextReg style={styles.signUpVerifcationBlockText}>{showBlock(2)}</TextReg>
              </View>
              <View style={[styles.signUpVerifcationBlock, highlightInput === 3 && styles.signUpVerifcationBlockActive]}>
                <TextReg style={styles.signUpVerifcationBlockText}>{showBlock(3)}</TextReg>
              </View>
              <View style={[styles.signUpVerifcationBlock, highlightInput === 4 && styles.signUpVerifcationBlockActive]}>
                <TextReg style={styles.signUpVerifcationBlockText}>{showBlock(4)}</TextReg>
              </View>
              <View style={[styles.signUpVerifcationBlock, highlightInput === 5 && styles.signUpVerifcationBlockActive]}>
                <TextReg style={styles.signUpVerifcationBlockText}>{showBlock(5)}</TextReg>
              </View>
            </View>
          </View>
          <TextReg style={styles.verifyCaseSensitive}>Case Sensitive</TextReg>
        </TouchableOpacity>

        {error && (
          <View style={styles.signUpErrorBox}>
            <TextReg style={styles.signUpErrorText}>{error}</TextReg>
          </View>
        )}

        <View style={{alignSelf: 'stretch', flexDirection: 'column', alignItems: 'center'}}>
          {loading ? (
            <LottieView
              ref={refAnimation}
              style={{width: 70, height: 70, opacity: 0.9, marginTop: 10}}
              source={require('../../../imgs/lotti/loading-white-dots.json')}
              autoPlay
            />
          ) : (
            <>
              <TextReg style={styles.signUpTextResend}>Didn't recieve email?</TextReg>
              <TouchableOpacity onPress={resendVerifyEmail}>
                <TextBold style={{color: '#00FFBD', fontSize: 18}}>RESEND</TextBold>
              </TouchableOpacity>
              {sent && <TextReg style={{fontSize: 16, marginTop: 16, opacity: 0.5}}>{`- SENT -`}</TextReg>}
            </>
          )}
        </View>
      </View>
    </View>
  )
}

export default Auth2

const localStyles = StyleSheet.create({
  box: {
    flex: 1,
    padding: 24,
    backgroundColor: '#28283D',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingBottom: 30,
  },
  boxTop: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    alignSelf: 'stretch',
    position: 'relative',
  },
})
