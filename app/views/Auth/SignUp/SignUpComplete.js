import React, {Component} from 'react';
import {View, TouchableOpacity, Image, TextInput, Keyboard, Platform} from 'react-native';
import {connect} from 'react-redux';

import AsyncStorage from '@react-native-async-storage/async-storage';
import firebase from '@react-native-firebase/app';

import styles from '../styles';
import {TextReg, TextBold} from '../../../components';
import {sendEvent} from '../../../store/analytics/analytics.actions';

import {showPinScreen, isAuthed, updateFCMToken, updatePin} from '../../../store/auth/auth.actions';

class SignUpComplete extends Component {
  constructor(props) {
    super(props);
    this.state = {
      email: '',
      error: false,
      accountType: 'Personal',
      accountName: null,
    };
    this.inputs = [];
  }

  prefetchData = async userEmail => {
    //const account = await this.props.WebService.getSaltAccount()
    //const ref = account.data[0].ref.toString()
    //AsyncStorage.setItem('REF', ref)
    AsyncStorage.setItem('LOGIN_EMAIL', userEmail);
    this.props.dispatch(showPinScreen(false));
    this.props.dispatch(isAuthed({email: userEmail, ref}));
  };

  sendFcmToken = () => {
    firebase
      .messaging()
      .getToken()
      .then(fcmToken => {
        if (fcmToken && fcmToken !== this.props.fcmToken) {
          this.props.WebService.updateFCMTokenAPI(fcmToken)
            .then(res => {
              this.props.dispatch(updateFCMToken(fcmToken));
            })
            .catch(err => {
              //console.log('updateFCMTokenAPI err', err)
            });
        }
      });
  };

  continueToApp = () => {
    const userEmail = this.props.email;

    let eventName = 'Register-Old-Complete-Android';
    if (Platform.OS === 'ios') {
      eventName = 'Register-Old-Complete-iOS';
    }
    this.props.dispatch(sendEvent(eventName));
    this.props.dispatch(updatePin('0'));

    this.props.WebService.createSaltAccount({
      name: this.state.accountName != null ? this.state.accountName : `${this.state.accountType} Account`,
      type: this.state.accountType.toLowerCase(),
    })
      .then(res => {
        const lowerCaseEmail = userEmail.toLowerCase();
        //AsyncStorage.setItem(`REF-${lowerCaseEmail}`, res.data.ref.toString())
        this.sendFcmToken();
        this.prefetchData(userEmail);
      })
      .catch(err => {
        console.log('createSaltAccount err', err);
      });
  };

  updateAccountName = accountName => {
    this.setState({accountName});
  };

  render() {
    const accountNameValue = this.state.accountName == null ? `${this.state.accountType} Account` : this.state.accountName;
    return (
      <View style={[styles.signUpPageContainer, {opacity: this.props.visible ? 1 : 0.3}]}>
        <TextReg
          style={{
            width: 280,
            marginTop: 10,
            color: '#FFF',
            fontSize: 16,
            textAlign: 'center',
          }}>{`Choose the account type that matches your needs.`}</TextReg>

        <View style={{marginTop: 12}}>
          <TextReg style={styles.accountNameText}>Account Name:</TextReg>
          <TextInput
            autoCorrect={false}
            onChangeText={text => this.updateAccountName(text)}
            onSubmitEditing={() => Keyboard.dismiss()}
            onBlur={() => Keyboard.dismiss()}
            ref={input => (this.inputs.accountName = input)}
            returnKeyType={'next'}
            textContentType="none"
            underlineColorAndroid="transparent"
            value={accountNameValue}
            onFocus={() => this.setState({accountName: ''})}
            style={styles.accountInput}
            placeholder={`${this.props.firstName}'s ${this.state.accountType} Account`}
            keyboardAppearance="dark"
          />
          <TextReg style={styles.accountTypeText}>Account Type:</TextReg>
        </View>

        <TouchableOpacity onPress={() => this.setState({accountType: 'Personal'})}>
          <View style={this.state.accountType === 'Personal' ? styles.signUpAccountButtonActive : styles.signUpAccountButton}>
            {this.state.accountType === 'Personal' ? (
              <Image
                source={require('../../../imgs/checkmarkCircle.png')}
                style={{
                  height: 36,
                  width: 36,
                  marginRight: 20,
                  marginLeft: 16,
                }}
              />
            ) : (
              <Image
                source={require('../../../imgs/accountPersonal.png')}
                style={{
                  height: 36,
                  width: 36,
                  marginRight: 20,
                  marginLeft: 16,
                }}
              />
            )}
            <View style={{width: 170}}>
              <TextBold style={{color: '#00FFBD', fontSize: 17, marginBottom: 4}}>Personal Loan</TextBold>
              <TextReg>For indivduals leveraging their crypto</TextReg>
            </View>
          </View>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => this.setState({accountType: 'Business'})}>
          <View style={this.state.accountType === 'Business' ? styles.signUpAccountButtonActive : styles.signUpAccountButton}>
            {this.state.accountType === 'Business' ? (
              <Image
                source={require('../../../imgs/checkmarkCircle.png')}
                style={{
                  height: 36,
                  width: 36,
                  marginRight: 20,
                  marginLeft: 16,
                }}
              />
            ) : (
              <Image
                source={require('../../../imgs/accountBusiness.png')}
                style={{
                  height: 50,
                  width: 30,
                  marginRight: 20,
                  marginLeft: 12,
                }}
              />
            )}
            <View style={{width: 170}}>
              <TextBold style={{color: '#00FFBD', fontSize: 17, marginBottom: 4}}>Business Loan</TextBold>
              <TextReg style={{width: 140}}>Startups or companies that need growth</TextReg>
            </View>
          </View>
        </TouchableOpacity>

        <TouchableOpacity onPress={this.continueToApp}>
          <View style={styles.signUpLastEndButton}>
            <TextReg style={{color: '#000', fontSize: 22}}>Next</TextReg>
          </View>
        </TouchableOpacity>
      </View>
    );
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
});

export default connect(mapStateToProps)(SignUpComplete);
