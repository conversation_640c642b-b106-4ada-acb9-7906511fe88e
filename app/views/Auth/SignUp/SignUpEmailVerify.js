import React, {Component} from 'react'
import {View, Text, TextInput, TouchableOpacity, Keyboard} from 'react-native'
import {connect} from 'react-redux'

import AsyncStorage from '@react-native-async-storage/async-storage'
import DeviceInfo from 'react-native-device-info'
import <PERSON><PERSON>View from 'lottie-react-native'
import firebase from '@react-native-firebase/app'
import * as Keychain from 'react-native-keychain'

import {updateFCMToken, updateDeviceId} from '../../../store/auth/auth.actions'

import styles from '../styles'
import {TextReg, TextBold} from '../../../components'

const model = DeviceInfo.getModel()
const manufacturer = DeviceInfo.getManufacturer()
const systemName = DeviceInfo.getSystemName()
const systemVersion = DeviceInfo.getSystemVersion()

class SignUpEmailVerify extends Component {
  constructor() {
    super()
    this.state = {
      code: '',
      loading: false,
      textInputActive: false,
      error: false,
      errorMessage: '',
      readyToCont: false,
      completed: false,
    }
    this.inputs = {}
    this.polling = null
  }

  componentWillUnmount() {
    clearInterval(this.polling)
  }

  static getDerivedStateFromProps(props, state) {
    let newCompleted = state.completed
    if (props.completedPage > 2 || state.completed) {
      newCompleted = true
    }
    return {
      ...state,
      completed: newCompleted,
    }
  }

  updateUser = user => {
    this.props.updateCognitoUser(user)
  }

  sendFcmToken = () => {
    firebase
      .messaging()
      .getToken()
      .then(fcmToken => {
        if (fcmToken && fcmToken !== this.props.fcmToken) {
          this.props.WebService.updateFCMTokenAPI(fcmToken).then(res => {
            this.props.dispatch(updateFCMToken(fcmToken))
          })
        }
      })
  }

  update2FACheck = user => {
    const jwtToken = user.signInUserSession.accessToken.jwtToken

    this.props.WebService.loginToSalt(
      {
        model,
        manufacturer,
        osVersion: systemVersion,
        platform: systemName,
        deviceId: this.props.deviceId || undefined,
      },

      jwtToken,
    ).then(async res => {
      const newSid = res.headers['sess-auth']
      const {deviceId} = res.data
      AsyncStorage.setItem('DEVICEID', deviceId)
      this.props.dispatch(updateDeviceId(deviceId))
      await Keychain.setInternetCredentials('token', 'user', newSid, {
        securityLevel: Keychain.SECURITY_LEVEL.ANY,
        storage: Keychain.STORAGE_TYPE.AES,
      })
      this.props.WebService.updateSID(newSid)

      this.setState({readyToCont: true})
      this.props.completePage(2)
    })
  }

  handleVerificationCode = (text = '') => {
    if (this.props.completed) {
      this.props.completePage(2)
      return
    }
    if (text.length > 6) {
      this.setState({readyToCont: false})
      return
    }
    this.setState({code: text})
    if (text.length === 6) {
      const pin = text
      const email = this.props.email.trim().toLowerCase()
      this.setState({loading: true, error: false, errorMessage: ''})
      this.props.WebService.verifyEmail({pin, email})
        .then(res => {
          Keyboard.dismiss()
          return this.props.AuthService.login(email, this.props.password)
        })
        .then(user => {
          this.updateUser(user)
          this.update2FACheck(user)
        })
        .catch(err => {
          this.setState({
            error: true,
            errorMessage: 'Error verifying code',
            loading: false,
            code: '',
          })
        })
    }
  }

  showBlock = num => {
    if (this.state.code.length > num) {
      return this.state.code[num]
    }
    return ''
  }

  focusTextInput = () => {
    this.setState({textInputActive: true})
  }

  blurTextInput = () => {
    this.setState({textInputActive: false})
  }

  resendVerifyEmail = () => {
    this.setState({loading: true, error: false, errorMessage: ''})
    const email = this.props.email.trim().toLowerCase()
    this.props.WebService.resendVerifyEmail(email)
      .then(res => {
        this.setState({loading: false})
      })
      .catch(err => {
        this.setState({
          loading: false,
          error: true,
          errorMessage: 'Error resending email verification',
        })
      })
  }

  render() {
    const highlightInput = this.state.textInputActive ? this.state.code.length : false

    return (
      <View style={[styles.signUpSpaceBetween, {opacity: this.props.visible ? 1 : 0.3}]}>
        <View style={styles.signUpPageContainer}>
          <Text style={styles.signUpTitleLead}>We sent an email to</Text>
          <Text style={styles.signUpTitleItalics}>{this.props.email}</Text>

          <Text style={styles.signUpTitleLead}>Enter Verification Code</Text>

          <TextInput
            style={styles.signUpTextInputEmailVerificationCode}
            onChangeText={this.handleVerificationCode}
            returnKeyType={'done'}
            value={this.state.code}
            ref={input => (this.inputs.verificationInput = input)}
            onSubmitEditing={this.handleSubmitEmailVerification}
            onFocus={this.focusTextInput}
            onBlur={this.blurTextInput}
            autoCapitalize={'none'}
            keyboardAppearance="dark"
          />
          <TouchableOpacity
            style={{alignSelf: 'stretch', marginLeft: 27}}
            activeOpacity={1}
            onPress={() => this.inputs.verificationInput.focus()}>
            <View style={styles.signUpVerifcationBlocksBox}>
              <View style={[styles.signUpVerifcationBlock, highlightInput === 0 && styles.signUpVerifcationBlockActive]}>
                <TextReg style={styles.signUpVerifcationBlockText}>{this.showBlock(0)}</TextReg>
              </View>
              <View style={[styles.signUpVerifcationBlock, highlightInput === 1 && styles.signUpVerifcationBlockActive]}>
                <TextReg style={styles.signUpVerifcationBlockText}>{this.showBlock(1)}</TextReg>
              </View>
              <View style={[styles.signUpVerifcationBlock, highlightInput === 2 && styles.signUpVerifcationBlockActive]}>
                <TextReg style={styles.signUpVerifcationBlockText}>{this.showBlock(2)}</TextReg>
              </View>
              <View style={[styles.signUpVerifcationBlock, highlightInput === 3 && styles.signUpVerifcationBlockActive]}>
                <TextReg style={styles.signUpVerifcationBlockText}>{this.showBlock(3)}</TextReg>
              </View>
              <View style={[styles.signUpVerifcationBlock, highlightInput === 4 && styles.signUpVerifcationBlockActive]}>
                <TextReg style={styles.signUpVerifcationBlockText}>{this.showBlock(4)}</TextReg>
              </View>
              <View style={[styles.signUpVerifcationBlock, highlightInput === 5 && styles.signUpVerifcationBlockActive]}>
                <TextReg style={styles.signUpVerifcationBlockText}>{this.showBlock(5)}</TextReg>
              </View>
            </View>
          </TouchableOpacity>
          <TextReg style={styles.verifyCaseSensitive}>Case Sensitive</TextReg>
          {this.state.error && (
            <View style={styles.signUpErrorBox}>
              <TextReg style={styles.signUpErrorText}>{this.state.errorMessage}</TextReg>
            </View>
          )}

          {this.state.loading ? (
            <LottieView
              ref={animation => {
                this.animation = animation
              }}
              style={{width: 70, height: 70, opacity: 0.9, marginTop: 10}}
              source={require('../../../imgs/lotti/loading-white-dots.json')}
              autoPlay
            />
          ) : (
            <>
              <Text style={[styles.signUpTitleLead, {marginTop: 30, fontSize: 16}]}>Didn't recieve email?</Text>
              <View style={{alignSelf: 'stretch', marginLeft: 30}}>
                <TouchableOpacity onPress={this.resendVerifyEmail}>
                  <TextBold style={{color: '#00FFBD', fontSize: 18}}>RESEND</TextBold>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
        <TouchableOpacity
          style={this.state.readyToCont ? styles.signUpNextButton : styles.signUpNextButtonInactive}
          onPress={!this.state.loading ? () => this.handleVerificationCode() : null}>
          <TextBold style={{color: '#000', fontSize: 20}}>CONTINUE</TextBold>
        </TouchableOpacity>
      </View>
    )
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  AuthService: state.auth.AuthService,
  user: state.user.user,
})

export default connect(mapStateToProps)(SignUpEmailVerify)
