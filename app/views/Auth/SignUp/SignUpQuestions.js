import React, { Component } from 'react'
import { View, TouchableOpacity, ScrollView, Image } from 'react-native'
import { connect } from 'react-redux'

import styles from '../styles'
import { TextReg } from '../../../components'

class SignUpQuestions extends Component {
  constructor() {
    super()
    this.state = {}
  }

  render() {
    return (
      <View
        style={[
          styles.signUpPageContainer,
          { opacity: this.props.visible ? 1 : 0.3 },
        ]}
      >
        <TextReg style={styles.signUpTitlePactSafe}>
          Crypto Questionnaire
        </TextReg>
        <View style={{ alignSelf: 'stretch', alignItems: 'center' }}>
          <View style={{ width: 320 }}>
            <TextReg style={{ color: '#FFF' }}>
              What is your primary collateral type?
            </TextReg>
            <TextReg style={{ color: '#FFF' }}>
              Who is <PERSON><PERSON>?
            </TextReg>
            <TextReg style={{ color: '#FFF' }}>
              How quickly do you need your loan funded?
            </TextReg>
          </View>
        </View>
      </View>
    )
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
})

export default connect(mapStateToProps)(SignUpQuestions)
