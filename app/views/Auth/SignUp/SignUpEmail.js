import React, { Component } from 'react'
import { View, Text, TextInput, TouchableOpacity, Platform } from 'react-native'
import { connect } from 'react-redux'

import styles from '../styles'
import { TextReg, TextBold } from '../../../components'
import { sendEvent } from '../../../store/analytics/analytics.actions'

class SignUpEmail extends Component {
  constructor(props) {
    super(props)
    this.state = {
      email: props.email || '',
      error: false,
      readyToCont: false
    }
    this.inputs = []
  }

  componentDidMount() {
    console.log('did load signup old')
  }

  componentDidUpdate(prevProps) {
    if (this.props.checkPage !== prevProps.checkPage && this.props.checkPage === 0) {
      this.setState({ error: true })
      this.props.clearCheckPage()
    }
  }

  handleEmail = (text) => {
    if (this.props.completedPage > 1) {
      return
    }
    this.setState({ email: text, error: false })

    if (this.isValidEmail(text)) {
      this.props.updateEmail(text)
      this.props.completePageNoScroll(0)
      this.setState({ readyToCont: true })
    } else {
      this.props.completePageNoScroll(-1)
      this.setState({ readyToCont: false })
    }
  }

  handleSubmitEmail = () => {
    if (this.isValidEmail(this.state.email)) {
      this.props.updateEmail(this.state.email)

      let eventName = 'Register-Old-Start-Android'
      if (Platform.OS === 'ios') {
        eventName = 'Register-Old-Start-iOS'
      }
      this.props.dispatch(sendEvent(eventName))

      this.props.completePage(0)
    } else {
      this.setState({ error: true })
    }
  }

  isValidEmail = (email) => {
    const re =
      /^(([^<>()\[\]\.,;:\s@\"]+(\.[^<>()\[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i
    return re.test(email)
  }

  render() {
    return (
      <View style={[styles.signUpSpaceBetween, { opacity: this.props.visible ? 1 : 0.3 }]}>
        <View style={styles.signUpPageContainer}>
          <Text style={styles.signUpTitleLead}>Enter your Email Address</Text>
          <TextInput
            style={styles.signUpTextInput}
            onChangeText={this.handleEmail}
            ref={(input) => (this.inputs.email = input)}
            returnKeyType={'done'}
            value={this.state.email}
            onSubmitEditing={this.handleSubmitEmail}
            selectionColor={'#FFF'}
            keyboardAppearance="dark"
          />
          {this.state.error && (
            <View style={styles.signUpErrorBox}>
              <TextReg style={styles.signUpErrorText}>Incorrect Email Format</TextReg>
            </View>
          )}
        </View>
        <TouchableOpacity
          style={this.state.readyToCont ? styles.signUpNextButton : styles.signUpNextButtonInactive}
          onPress={this.handleSubmitEmail}
        >
          <TextBold style={{ color: '#000', fontSize: 19 }}>CONTINUE</TextBold>
        </TouchableOpacity>
      </View>
    )
  }
}

const mapStateToProps = (state) => ({
  WebService: state.auth.WebService,
  user: state.user.user
})

export default connect(mapStateToProps)(SignUpEmail)
