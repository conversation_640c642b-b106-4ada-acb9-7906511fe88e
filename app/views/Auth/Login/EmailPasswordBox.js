import React, {useRef} from 'react'
import {View, TextInput, TouchableOpacity} from 'react-native'
import {TextReg, TextBold} from '../../../components'
import styles from '../styles'

const EmailPasswordBox = props => {
  let inputEmail = useRef(null)
  let inputPassword = useRef(null)
  return (
    <View style={styles.loginFormBox}>
      <View style={styles.inputLabel}>
        <TextReg style={styles.inputLabelText}>Email</TextReg>
      </View>
      <TextInput
        autoCapitalize="none"
        blurOnSubmit={false}
        onChangeText={text => props.updateEmail(text)}
        onSubmitEditing={() => inputPassword.current.focus()}
        ref={inputEmail}
        returnKeyType={'next'}
        style={styles.input}
        textContentType="none"
        underlineColorAndroid="transparent"
        value={props.email}
        keyboardAppearance="dark"
      />
      <View style={styles.inputLabelPassword}>
        <TextReg style={styles.inputLabelText}>Password</TextReg>
      </View>

      <TextInput
        onChangeText={text => props.updatePassword(text)}
        onSubmitEditing={() => props.handleLogin()}
        ref={inputPassword}
        returnKeyType={'done'}
        secureTextEntry
        style={styles.input}
        textContentType="password"
        underlineColorAndroid="transparent"
        value={props.pass}
        keyboardAppearance="dark"
      />
      <TouchableOpacity onPress={() => props.forgotPasswordLink()} style={styles.forgotPasswordLink}>
        <TextBold style={styles.forgotPasswordText}>FORGOT PASSWORD?</TextBold>
      </TouchableOpacity>
    </View>
  )
}

export default EmailPasswordBox
