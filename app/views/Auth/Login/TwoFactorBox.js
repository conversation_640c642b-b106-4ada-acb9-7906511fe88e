import React, {useEffect} from 'react'
import {View, Platform, TouchableOpacity, Image} from 'react-native'
import styles from '../styles'
import {TextReg, TextBold, VerificationInput} from '../../../components'

let TwoFactorBox = props => {
  useEffect(() => {
    //console.log('2fa mount');
  }, [])

  return (
    <View style={styles.loginFormBox2FA}>
      <View style={styles.signUpHeader2FA}>
        <TouchableOpacity style={styles.backToLoginButton} onPress={() => props.backToStage1()}>
          <Image source={require('../../../imgs/backToSettings.png')} style={styles.backToLoginImg} />
        </TouchableOpacity>
      </View>

      <View style={styles.inputLabel2FA}>
        <TextReg style={styles.inputLabelText2FA}>2-Factor Authentication</TextReg>
      </View>

      <VerificationInput updateTwoFactor={props.updateTwoFactor} handleLogin={props.handleLogin} code={props.twoFactor} />

      {props.error && (
        <TextReg
          style={{
            fontSize: 20,
            color: '#fb1f63',
            width: 300,
            height: 70,
            alignSelf: 'center',
          }}>
          Your 2-Step Verification Code was incorrect. Please try again.
        </TextReg>
      )}

      {Platform.OS === 'ios' && (
        <TouchableOpacity style={styles.totpButton} onPress={props.openTotp}>
          <TextBold style={styles.totpText}>OPEN AUTHENTICATOR APP</TextBold>
        </TouchableOpacity>
      )}
    </View>
  )
}

export default TwoFactorBox
