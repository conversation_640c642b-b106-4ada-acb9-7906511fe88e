import React, {Component} from 'react';
import {View, StatusBar, TouchableOpacity, Image, TextInput} from 'react-native';
import {connect} from 'react-redux';

import {Button, Background, TextBold, TextReg} from '../../../components';
import commonStyles from '../../../styles/commonStyles';
import styles from '../styles';
import config from '../../../config.json';

class ResetPassword extends Component {
  constructor(props) {
    super(props);
    console.log('constructor props', props);
    this.state = {
      password: '',
      passwordConfirm: '',
      readyToCont: false,
      loading: false,
    };
    this.inputs = [];
  }

  componentDidMount() {
    console.log('ResetPassword');
  }

  handlePassword = text => {
    this.setState({password: text}, () => {
      const validPassword = this.validatePasswords();
      if (validPassword.length > 0) {
        this.setState({readyToCont: false});
      } else {
        this.setState({readyToCont: true});
      }
    });
  };

  handlePasswordConfirm = text => {
    this.setState({passwordConfirm: text}, () => {
      const validPassword = this.validatePasswords();
      if (validPassword.length > 0) {
        this.setState({readyToCont: false});
      } else {
        this.setState({readyToCont: true});
      }
    });
  };

  handleSubmitPassword = () => {
    this.setState({
      error: null,
      loading: true,
    });

    const validPassword = this.validatePasswords();

    if (validPassword.length > 0) {
      this.setState({
        error: validPassword[0].message,
      });
    } else {
      this.setState({loading: true});

      let {password} = this.state;
      let {user, oldPass, completeNewPasswordRes} = this.props.route?.params;
      this.props.AuthService.completeNewPassword(user, password)
        .then(user => {
          console.log('changePassword res', user);
          completeNewPasswordRes(user);
        })
        .catch(err => {
          console.log('changePassword err', err);
          this.setState({loading: false});
        });
    }
  };

  validatePasswords = () => {
    //validation
    const lowercase = {
      test: text => /[a-z]+/.test(text),
      message: 'Password requires at least one lowercase letter.',
    };
    const uppercase = {
      test: text => /[A-Z]+/.test(text),
      message: 'Password requires at least one uppercase letter.',
    };
    const number = {
      test: text => /[0-9]+/.test(text),
      message: 'Password requires at least one number.',
    };
    const special = {
      test: text => /[-!$%^&*@#()_+|~`{}\[\]:";'<>?,.\/]/.test(text),
      message: 'Password requires at least one special character.',
    };
    const same = {
      test: text => text === this.state.passwordConfirm,
      message: 'Passwords must match.',
    };
    const length = {
      test: text => text.length >= 8,
      message: 'Password must be eight characters.',
    };
    const validations = [lowercase, uppercase, number, special, length, same];
    const results = validations.filter(validation => !validation.test(this.state.password));
    return results;
  };

  render() {
    let {error} = this.state;
    return (
      <View style={commonStyles.tileContainer}>
        <StatusBar barStyle="light-content" />
        <View style={styles.signUpHeader}>
          <TouchableOpacity style={styles.backToLoginButton} onPress={() => this.props.navigation?.goBack()}>
            <Image source={require('../../../imgs/backToSettings.png')} style={styles.backToLoginImg} />
          </TouchableOpacity>
          <TextReg style={styles.signUpHeaderText}>Reset Password</TextReg>
        </View>
        <View style={{flex: 1, alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'space-between', marginBottom: 30}}>
          <View style={{flexDirection: 'column'}}>
            <TextReg style={styles.signUpTitleLead}>New Password</TextReg>
            <TextInput
              style={[styles.signUpTextInput, {marginTop: 10}]}
              onChangeText={this.handlePassword}
              value={this.state.password}
              ref={input => (this.inputs.password = input)}
              returnKeyType={'next'}
              onSubmitEditing={() => this.inputs.passwordConfirm.focus()}
              secureTextEntry
              selectionColor={'#FFF'}
              keyboardAppearance="dark"
            />
            <TextReg style={styles.signUpPasswordConfirmText}>Confirm New Password</TextReg>
            <TextInput
              style={[styles.signUpTextInput, {marginTop: 16}]}
              onChangeText={this.handlePasswordConfirm}
              value={this.state.passwordConfirm}
              ref={input => (this.inputs.passwordConfirm = input)}
              returnKeyType={'done'}
              onSubmitEditing={this.handleSubmitPassword}
              secureTextEntry
              selectionColor={'#FFF'}
              keyboardAppearance="dark"
            />
            {error && (
              <View style={styles.signUpErrorBox}>
                <TextReg style={styles.signUpErrorText}>{error}</TextReg>
              </View>
            )}
          </View>

          <Button
            isLoading={this.state.loading}
            disabled={!this.state.readyToCont}
            style={{alignSelf: 'stretch', margin: 30}}
            onPress={() => this.handleSubmitPassword()}>
            <TextReg style={{color: '#000', fontSize: 18}}>CONTINUE</TextReg>
          </Button>
        </View>
      </View>
    );
  }
}

ResetPassword.navigationOptions = {
  header: null,
};

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  AuthService: state.auth.AuthService,
});

export default connect(mapStateToProps)(ResetPassword);
