import React, {Component} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Keyboard,
  Dimensions,
  KeyboardAvoidingView,
  Image,
} from 'react-native';
import {connect} from 'react-redux';
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view';

import {Button, TextBold, TextReg} from '../../../components';
import EmailPasswordBox from './EmailPasswordBox';
import TwoFactorBox from './TwoFactorBox';

import styles from '../styles';
const {width: ScreenWidth, height: ScreenHeight} = Dimensions.get('window');

/*
  <KeyboardAvoidingView
    style={styles.keyboardAvoidingView}
    behavior="position"
    enabled>
    */

class LoginScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    return (
      <View>
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            Keyboard.dismiss();
          }}>
          <View
            style={[
              styles.authContainerFlexStart,
              {
                alignSelf: 'stretch',
                width: ScreenWidth,
              },
            ]}>
            <KeyboardAvoidingView
              style={styles.keyboardAvoidingView}
              behavior="position"
              enabled>
              <Image
                source={require('../../../imgs/logos/salt-White.png')}
                style={styles.saltLogo}
              />

              {this.props.step === 0 && (
                <EmailPasswordBox
                  updateEmail={this.props.updateEmail}
                  updatePassword={this.props.updatePassword}
                  focusNextField={this.props.focusNextField}
                  handleLogin={this.props.handleLogin}
                  email={this.props.email}
                  pass={this.props.pass}
                  forgotPasswordLink={this.props.forgotPasswordLink}
                />
              )}

              {this.props.step === 1 && (
                <TwoFactorBox
                  twoFactor={this.props.twoFactor}
                  updateTwoFactor={this.props.updateTwoFactor}
                  handleLogin={this.props.handleLogin}
                  openTotp={this.props.openTotp}
                  backToStage1={this.props.backToStage1}
                  error={this.props.loginError === 2}
                />
              )}
            </KeyboardAvoidingView>

            {this.props.loginError === 1 && (
              <View style={styles.loginErrorBox}>
                <TextReg style={styles.loginErrorText}>
                  Invalid Credentials
                </TextReg>
              </View>
            )}

            {this.props.loginError === 3 && (
              <View style={styles.loginErrorBox}>
                <TextReg style={styles.loginErrorText}>
                  Login Session Timeout
                </TextReg>
              </View>
            )}

            {this.props.isDisabled && (
              <View>
                <Text>Cannot Connect To Servers</Text>
              </View>
            )}
          </View>

          {!this.props.isDisabled && (
            <Button
              isLoading={this.props.isAuthenticating}
              onPress={this.props.handleLogin}
              style={
                this.props.step === 1 ? styles.twofaButton : styles.loginButton
              }
              theme={'secondary'}>
              <TextBold style={{color: '#000', fontSize: 17}}>
                {this.props.step === 1 ? 'AUTHENTICATE' : 'LOG IN'}
              </TextBold>
            </Button>
          )}

          {this.props.step === 0 && (
            <TouchableOpacity
              isDisabled={this.props.isAuthenticating}
              onPress={() =>
                this.props.openSignUpLink({
                  resume: false,
                  infoEntered: null,
                  idVerified: null,
                  membershipAgreementSigned: null,
                })
              }
              style={styles.signUpLink}>
              <TextBold style={styles.signUpLinkText}>SIGN UP</TextBold>
            </TouchableOpacity>
          )}
        </TouchableOpacity>
      </View>
    );
  }
}

LoginScreen.navigationOptions = {
  header: null,
};

const mapStateToProps = state => ({
  isAuthenticating: state.auth.isAuthenticating,
  WebService: state.auth.WebService,
  isDisabled: state.auth.disabled,
  deviceId: state.auth.deviceId,
  user: state.user.user,
});

export default connect(mapStateToProps)(LoginScreen);
