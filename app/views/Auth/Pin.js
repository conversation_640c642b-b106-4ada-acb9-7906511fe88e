import React, {Component} from 'react'
import {View, TouchableOpacity, TouchableHighlight, Image, Keyboard} from 'react-native'
import {connect} from 'react-redux'

import AsyncStorage from '@react-native-async-storage/async-storage'
import * as Keychain from 'react-native-keychain'
import packageJson from '../../../package.json'

import {updateAuthed, showPinScreen, unAuth, updatePin, logout} from '../../store/auth/auth.actions'
import {Background, TextReg} from '../../components'

import styles from './styles'

class Pin extends Component {
  constructor(props) {
    super(props)
    this.state = {
      pin: '',
      error: false,
      activePin: -1,
      currentAttempts: 0,
      timer: 0,
      pinAttempts: '0',
      backgroundLoaded: false,
    }
  }

  componentDidMount() {
    Keyboard.dismiss()
    this.isTimoutStillActive()
    this.getStorageAttempts()
  }

  getStorageAttempts = () => {
    AsyncStorage.getItem('PINATTEMPTS').then(res => {
      const pinAttempts = res || 0
      this.setState({pinAttempts})
    })
  }

  isTimoutStillActive = () => {
    AsyncStorage.getItem('TIMEOUT').then(res => {
      const timestamp = new Date().getTime().toString()
      if (parseInt(res) + 30 * 1000 > timestamp) {
        let difference = (timestamp - parseInt(res)) / 1000
        difference = Math.floor(difference)
        difference = 30 - difference
        this.setState(
          {
            timeout: true,
            currentAttempts: 0,
            pin: '',
            activePin: 0,
            timer: difference,
          },
          () => (this.intervalHandle = setInterval(this.tick, 1000)),
        )
      }
    })
  }

  handlePin = pin => {
    if (pin.toString() === this.props.pin) {
      this.props.dispatch(showPinScreen(false))
      this.props.dispatch(updateAuthed(true))
    }
  }

  resetPin = async () => {
    const lowerCaseEmail = this.props.account.email.toLowerCase()
    await Keychain.setInternetCredentials(`pin-${lowerCaseEmail}`, 'user', '0', {
      securityLevel: Keychain.SECURITY_LEVEL.ANY,
      storage: Keychain.STORAGE_TYPE.AES,
    })

    // this.props.dispatch(updatePin('0'))
    this.props.dispatch(unAuth())
  }

  handleEnterPin = num => {
    let pin = this.state.pin
    if (num === 'back' && pin.length > 0) {
      pin = pin.substring(0, pin.length - 1)
      this.setState({pin, error: false, activePin: pin.length})
    } else if (num !== 'back' && pin.length < 3) {
      pin = `${pin}${num}`
      this.setState({pin, error: false, activePin: pin.length})
    } else if (pin.length === 3) {
      this.setState({activePin: 4}, () => {
        pin = `${pin}${num}`
        this.handleFinishPin(pin)
      })
    }
  }

  wipe = async () => {
    await this.savePin('0')
    this.props.dispatch(logout())
  }

  async savePin(pin) {
    const {account} = this.props
    const lowerCaseEmail = account.email.toLowerCase()
    await Keychain.setInternetCredentials(`pin-${lowerCaseEmail}`, 'user', pin, {
      securityLevel: Keychain.SECURITY_LEVEL.ANY,
      storage: Keychain.STORAGE_TYPE.AES,
    })
  }

  handleFinishPin = async pin => {
    if (pin.toString() === this.props.pin) {
      this.setState({pinAttempts: 0})
      await AsyncStorage.setItem('PINATTEMPTS', '0')
      this.props.dispatch(showPinScreen(false))
      this.props.dispatch(updateAuthed(true))
    } else {
      // set storage attempts
      const newPinAttemps = Number(this.state.pinAttempts) + 1
      if (newPinAttemps >= 10) {
        await AsyncStorage.setItem('PINATTEMPTS', '0')
        this.wipe()
        return
      }
      this.setState({pinAttempts: newPinAttemps})
      AsyncStorage.setItem('PINATTEMPTS', newPinAttemps.toString())
      //show error and remove pin
      const currentAttempts = this.state.currentAttempts + 1
      if (currentAttempts >= 5) {
        this.setState(
          {
            timeout: true,
            currentAttempts: 0,
            pin: '',
            activePin: 0,
            timer: 30,
          },
          () => (this.intervalHandle = setInterval(this.tick, 1000)),
        )
        const timestamp = new Date().getTime().toString()
        AsyncStorage.setItem('TIMEOUT', timestamp)
      } else {
        this.setState({
          error: true,
          pin: '',
          currentAttempts,
          activePin: 0,
        })
      }
    }
  }

  tick = () => {
    if (this.state.timer === 1) {
      this.setState({timer: 0, timeout: false})
      clearInterval(this.intervalHandle)
    } else {
      this.setState({timer: this.state.timer - 1})
    }
  }

  onBackgroundLoad = () => {
    console.log('loaded pin bg')
    this.setState({backgroundLoaded: true})
  }

  handleLogout = () => {
    this.props.dispatch(logout())
  }

  render() {
    const underlayColor = '#ffffff30'

    return (
      <View style={[styles.pinContainer, {paddingTop: 20}]}>
        <View style={styles.pinTopBox}>
          <Image source={require('../../imgs/saltLogoWhite.png')} style={styles.saltLogoWhite} />
          <TextReg style={styles.pinTitle}>Enter PIN</TextReg>
          <View style={styles.pinBubbleBox}>
            <View style={this.state.activePin > 0 ? styles.pinBubbleActive : styles.pinBubble} />
            <View style={this.state.activePin > 1 ? styles.pinBubbleActive : styles.pinBubble} />
            <View style={this.state.activePin > 2 ? styles.pinBubbleActive : styles.pinBubble} />
            <View style={this.state.activePin > 3 ? styles.pinBubbleActive : styles.pinBubble} />
          </View>
          {this.state.error && <TextReg style={styles.pinTitle}>Error</TextReg>}
          {this.state.timeout && (
            <View>
              <TextReg style={styles.timoutNoticeText}>Application Locked</TextReg>
              <TextReg style={styles.timoutNoticeTextBottom}>{`Try again in ${this.state.timer} seconds`}</TextReg>
            </View>
          )}
        </View>
        {!this.state.timeout && (
          <View style={styles.pinBottomBox}>
            <View style={styles.pinInputBox}>
              <View style={styles.pinInputRow}>
                <TouchableHighlight style={styles.pinInputButton} onPress={() => this.handleEnterPin(1)} underlayColor={underlayColor}>
                  <View>
                    <TextReg style={styles.pinInputNum}>1</TextReg>
                    <TextReg style={styles.pinInputDetails} />
                  </View>
                </TouchableHighlight>
                <TouchableHighlight style={styles.pinInputButton} onPress={() => this.handleEnterPin(2)} underlayColor={underlayColor}>
                  <View>
                    <TextReg style={styles.pinInputNum}>2</TextReg>
                    <TextReg style={styles.pinInputDetails}>ABC</TextReg>
                  </View>
                </TouchableHighlight>
                <TouchableHighlight style={styles.pinInputButton} onPress={() => this.handleEnterPin(3)} underlayColor={underlayColor}>
                  <View>
                    <TextReg style={styles.pinInputNum}>3</TextReg>
                    <TextReg style={styles.pinInputDetails}>DEF</TextReg>
                  </View>
                </TouchableHighlight>
              </View>
              <View style={styles.pinInputRow}>
                <TouchableHighlight style={styles.pinInputButton} onPress={() => this.handleEnterPin(4)} underlayColor={underlayColor}>
                  <View>
                    <TextReg style={styles.pinInputNum}>4</TextReg>
                    <TextReg style={styles.pinInputDetails}>GHI</TextReg>
                  </View>
                </TouchableHighlight>
                <TouchableHighlight style={styles.pinInputButton} onPress={() => this.handleEnterPin(5)} underlayColor={underlayColor}>
                  <View>
                    <TextReg style={styles.pinInputNum}>5</TextReg>
                    <TextReg style={styles.pinInputDetails}>JKL</TextReg>
                  </View>
                </TouchableHighlight>
                <TouchableHighlight style={styles.pinInputButton} onPress={() => this.handleEnterPin(6)} underlayColor={underlayColor}>
                  <View>
                    <TextReg style={styles.pinInputNum}>6</TextReg>
                    <TextReg style={styles.pinInputDetails}>MNO</TextReg>
                  </View>
                </TouchableHighlight>
              </View>
              <View style={styles.pinInputRow}>
                <TouchableHighlight style={styles.pinInputButton} onPress={() => this.handleEnterPin(7)} underlayColor={underlayColor}>
                  <View>
                    <TextReg style={styles.pinInputNum}>7</TextReg>
                    <TextReg style={styles.pinInputDetails}>PQRS</TextReg>
                  </View>
                </TouchableHighlight>
                <TouchableHighlight style={styles.pinInputButton} onPress={() => this.handleEnterPin(8)} underlayColor={underlayColor}>
                  <View>
                    <TextReg style={styles.pinInputNum}>8</TextReg>
                    <TextReg style={styles.pinInputDetails}>TUV</TextReg>
                  </View>
                </TouchableHighlight>
                <TouchableHighlight style={styles.pinInputButton} onPress={() => this.handleEnterPin(9)} underlayColor={underlayColor}>
                  <View>
                    <TextReg style={styles.pinInputNum}>9</TextReg>
                    <TextReg style={styles.pinInputDetails}>WXYZ</TextReg>
                  </View>
                </TouchableHighlight>
              </View>
              <View style={styles.pinInputRow}>
                <TouchableHighlight style={styles.pinInputButton} onPress={() => this.handleLogout()} underlayColor={underlayColor}>
                  <View>
                    <TextReg style={styles.pinInputNumDelete}>{'Log out'}</TextReg>
                    <TextReg style={styles.pinInputDetails} />
                  </View>
                </TouchableHighlight>
                <TouchableHighlight style={styles.pinInputButton} onPress={() => this.handleEnterPin(0)} underlayColor={underlayColor}>
                  <View>
                    <TextReg style={styles.pinInputNum}>0</TextReg>
                    <TextReg style={styles.pinInputDetails} />
                  </View>
                </TouchableHighlight>
                <TouchableHighlight style={styles.pinInputButton} onPress={() => this.handleEnterPin('back')} underlayColor={underlayColor}>
                  <View>
                    <TextReg style={styles.pinInputNumDelete}>{'Delete'}</TextReg>
                    <TextReg style={styles.pinInputDetails} />
                  </View>
                </TouchableHighlight>
              </View>
            </View>
            <View style={styles.pinDone}>
              <TouchableOpacity onPress={() => this.resetPin()}>
                <TextReg style={styles.pinForgot}>Forgot PIN?</TextReg>
              </TouchableOpacity>
            </View>
          </View>
        )}
        <TextReg style={styles.versionInfoPin}>{packageJson.version}</TextReg>
        <Background onLoad={this.onBackgroundLoad} />
      </View>
    )
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  pin: state.auth.pin,
  account: state.auth.account,
})

export default connect(mapStateToProps)(Pin)
