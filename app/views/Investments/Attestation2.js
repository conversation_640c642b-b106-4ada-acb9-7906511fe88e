import React, {useEffect, useState} from 'react'
import {StyleSheet, View, Dimensions, TouchableOpacity, Image, ScrollView} from 'react-native'
import {TextReg, TextBold, BackgroundHeader, Button} from '../../components'
import {useDispatch, useSelector} from 'react-redux'

let Attestation2 = ({navigation, route}) => {
  let dispatch = useDispatch()
  let WebService = useSelector(state => state.auth.WebService || {})
  let {height: ScreenHeight} = Dimensions.get('window')
  let [seen, setSeen] = useState(false)

  console.log('Attestation1', navigation, route)

  let close = () => {
    navigation.pop(stackDepth)
  }

  let next = () => {
    console.log('next')
    navigation.navigate('AttestationLetter')
  }

  const handleScroll = event => {
    const {layoutMeasurement, contentOffset, contentSize} = event.nativeEvent
    const paddingToBottom = 20 // You can adjust this value as needed
    const isBottomReached = layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom

    if (isBottomReached) {
      console.log('seen')
      setSeen(true)
    }
  }

  return (
    <View style={{...localStyles.box, paddingTop: 10}}>
      <BackgroundHeader title={'Attestation2'} goBack={() => navigation.goBack()} />

      <ScrollView style={{...localStyles.box}} onScroll={handleScroll} scrollEventThrottle={16}>
        <View style={{flex: 1, marginLeft: 20, marginRight: 20}}>
          <TextReg>{`Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum `}</TextReg>
          <TextReg>{`Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum `}</TextReg>
        </View>
      </ScrollView>
      <View style={{alignSelf: 'stretch', marginTop: 10, marginBottom: 26, paddingLeft: 20, paddingRight: 20}}>
        <Button
          isLoading={false}
          disabled={!seen}
          style={{
            alignSelf: 'stretch',
          }}
          onPress={() => next()}
          theme={'secondary'}>
          <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
        </Button>
        <Button
          isLoading={false}
          disabled={false}
          style={{
            alignSelf: 'stretch',
            backgroundColor: '#28283D',
          }}
          onPress={() => navigation.goBack()}
          theme={'secondary'}>
          <TextReg style={{color: '#FFF', fontSize: 18}}>BACK</TextReg>
        </Button>
      </View>
    </View>
  )
}

export default Attestation2

const localStyles = StyleSheet.create({
  box: {
    flex: 1,
    backgroundColor: '#28283D',
  },
})
