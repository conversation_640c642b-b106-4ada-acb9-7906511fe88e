import React, {useEffect, useMemo, useRef, useState} from 'react'
import {
  View,
  TouchableOpacity,
  ScrollView,
  Image,
  Dimensions,
  SafeAreaView,
  PixelRatio,
  Platform,
} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import _ from 'lodash'
import {useNavigation} from '@react-navigation/native'

import {TextReg, TextBold, Card, LocationSelect, Button} from '../../components'
import styles from './styles'
import {formatCrypto, getNextMonthDate} from '../../util/helpers'
import {FlatList, TextInput} from 'react-native-gesture-handler'
import BigNumber from 'bignumber.js'
import {getInvestments} from '../../store/investments/investments.actions'
import moment from 'moment'

const {width: screenWidth, height: screenHeight} = Dimensions.get('window')

const EarningDestinationOption = props => {
  const [step, setStep] = useState(0)
  const [selectedAccount, setSelectedAccount] = useState(null)
  // const [details, setDetails] = useState(null)
  // const [searchValue, setSearchValue] = useState('')
  const [selection, setSelection] = useState(false)
  const [walletOptions, setWalletOptions] = useState([])
  const [borrowOptions, setBorrowOptions] = useState([])
  const [exchangeColletral, setExchangeColletral] = useState(0)
  const [walletId, setWalletId] = useState('')
  const [exchangeWallet, setExchangeWallet] = useState(null)
  const [optionsLoading, setOptionsLoading] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  let investments = useSelector(state => state.investments)
  let accounts = useSelector(state => state.user?.user?.accounts || [])
  let WebService = useSelector(state => state.auth.WebService || {})
  const investment = investments.byId[props.route?.params?.id] || {}

  const navigation = useNavigation()
  const dispatch = useDispatch()
  let scroll = useRef()

  function loanStatus(loan, account) {
    const thresholds = loan?.thresholds ? loan.thresholds : 0
    const ltv = loan?.awaitingStabilization
      ? loan?.thresholds.liquidation
      : loan?.ltv
      ? loan.ltv
      : 0
    const isStabilized = account?.product?.loan?.isStabilized

    if (isStabilized || new BigNumber(ltv).gte(new BigNumber(thresholds.liquidation))) {
      return require('../../imgs/loanHealth/lifePreserver.png')
    }
    if (new BigNumber(ltv).gte(new BigNumber(thresholds.marginCall))) {
      return require('../../imgs/loanHealth/alertLoan.png')
    }
    if (new BigNumber(ltv).gte(new BigNumber(thresholds.warning))) {
      return require('../../imgs/loanHealth/warningLoan.png')
    }
    return require('../../imgs/checkmark.png')
  }

  const sortedAccountsWithLoans = useMemo(() => {
    const filteredAccounts = accounts.filter(acc => {
      return (
        (acc.productType !== 'investment' &&
          acc.product?.loan?.collateralAssetTypes?.includes(investment.investmentAsset)) ||
        acc.productType == 'exchange'
      )
    })
    return (
      filteredAccounts?.sort((a, b) => (a === b ? 0 : a.loans[0]?.status === 'active' ? -1 : 1)) ||
      []
    )
  }, [accounts])

  const fitlerOption = async () => {
    const borrowOptions = []

    const walletOptions = []

    // Process each item in the input data
    const promise = sortedAccountsWithLoans?.map(async item => {
      const loan = item.loans[0] // Assuming each item has one loan
      const account = item
      console.log('loan: ', loan)
      // Define the common loan data
      const data = {
        value: account.accountId,
        label: account.name,
        productType: account.productType,
        icon: loanStatus(loan?.ltv ? loan.ltv : 0, loan?.thresholds ? loan?.thresholds : 0),
        amount: loan?.awaitingStabilization
          ? loan?.thresholds.liquidation
          : loan?.ltv
          ? loan.ltv
          : 0,
        thresholds: loan?.thresholds ? loan.thresholds : 0, // Use loan.ltv if available
        isDisabled: false,
      }
      console.log('account', account)

      // Check account type and push to the respective options array
      if (account.productType == 'exchange') {
        const isActive = exchangeWallet?.data.filter(val => {
          if (val.id == investment?.payoutWalletId) {
            return true
          } else {
            return false
          }
        })
        console.log('exchangeWallet: ', exchangeWallet.data, investment?.payoutWalletId, isActive)
        walletOptions.push({
          ...data,
          amount: `$${exchangeColletral.toFixed(2)}`,
          isDisabled: !!isActive?.length,
        })
      } else {
        const collateral = await WebService.getWallets(account.ref)
        const isActive = collateral?.data?.filter(val => {
          if (val.id == investment?.payoutWalletId) {
            return true
          } else {
            return false
          }
        })
        borrowOptions.push({...data, isDisabled: !!isActive?.length})
      }
    })
    await Promise.all(promise)

    if (walletOptions.length > 0) {
      setWalletOptions(walletOptions)
    }
    setBorrowOptions(borrowOptions)
    setOptionsLoading(false)
  }

  const getExchangeColletral = async () => {
    sortedAccountsWithLoans.map(async item => {
      if (item.productType == 'exchange') {
        const exchangeColletral = await WebService.getWallets(item.ref)
        const sumColletral = exchangeColletral.data.reduce((acc, curr) => {
          return (acc += +curr.value)
        }, 0)
        setExchangeColletral(sumColletral)
        setExchangeWallet(exchangeColletral)
      }
    })
  }

  useEffect(() => {
    setOptionsLoading(true)
    if (exchangeWallet?.data?.length > 0) {
      fitlerOption()
    }
  }, [exchangeWallet])

  useEffect(() => {
    getExchangeColletral()
  }, [])

  const setAccountValue = value => {
    setSelectedAccount(value)
    setSelection(false)
    setStep(2)
  }

  const getAccountType = type => {
    if (type === 'exchange') {
      return 'Wallet'
    } else if (type === 'loan') {
      return 'Borrow'
    } else if (type === 'investment') {
      return 'Investment'
    }
  }

  useEffect(() => {
    const selectWallet = async () => {
      const walletObj = await Promise.all(
        sortedAccountsWithLoans.map(async item => {
          if (item.accountId == selectedAccount?.value) {
            if (item.productType == 'loan') {
              const colletral = await WebService.getWallets(item.ref)
              return colletral.data.filter(val => val.currency == investment.investmentAsset)
            }

            if (item.productType == 'exchange') {
              return exchangeWallet?.data.filter(val => val.currency == investment.investmentAsset)
            }
          }
        }),
      )
      const getWallet = walletObj.filter(item => item)[0]
      if (getWallet) {
        setWalletId(getWallet[0]?.id)
      }
    }
    selectWallet()
  }, [selectedAccount])

  // useEffect(() => {
  //   if (searchValue) {
  //     const filteredWallets = borrowOptions.filter(item => {
  //       return item.label.toLowerCase().includes(searchValue.toLowerCase())
  //     })
  //     setWalletOptions(filteredWallets)
  //   }

  //   return () => {
  //     setSearchValue('')
  //   }
  // }, [searchValue])

  const changeDestinationHandler = async () => {
    setIsLoading(true)
    await WebService.updatePayout(investment?.id, {
      payoutWalletType: 'other_internal_wallet',
      payoutWalletId: walletId,
    })
    await dispatch(getInvestments())
    setIsLoading(false)
    navigation.navigate('LendDashboard', {id: investment.id})
  }

  console.log('selectedAccount: ', selectedAccount)

  return (
    <SafeAreaView style={styles.box}>
      <View
        style={{
          flexDirection: 'row',
          alignSelf: 'stretch',
          justifyContent: 'space-between',
          marginTop: 5,
        }}>
        <TouchableOpacity
          onPress={() => (step == 2 ? setStep(1) : step == 1 ? setStep(0) : navigation?.goBack())}
          style={{height: 40, width: 40, marginLeft: 20}}>
          <Image
            source={require('../../imgs/backToSettings.png')}
            style={{height: 25, width: 20}}
          />
        </TouchableOpacity>
        <TextReg style={{color: '#FFF', fontSize: 18}}>Earning Destination Option</TextReg>
        <View style={{height: 40, width: 40, marginRight: 20}} />
      </View>
      {step === 0 && (
        <View style={{width: screenWidth - 70}}>
          <TextBold style={{fontSize: 24, marginBottom: 14, marginTop: 22}}>
            Select a destination option for your Earnings:
          </TextBold>
          <TextReg style={{lineHeight: 22, fontSize: 16, textAlign: 'center'}}>Choose one:</TextReg>
          <View>
            <TouchableOpacity disabled={true}>
              <Card
                style={{
                  borderRadius: 12,
                  padding: 24,
                  alignItems: 'flex-start',
                  gap: 22,
                  flexDirection: 'row',
                  marginTop: 14,
                  opacity: 0.7,
                }}
                cardWidth={screenWidth - 70}>
                <Image
                  source={require('../../imgs/projectedEarnings.png')}
                  style={{width: 46, height: 46}}
                />
                <View style={{position: 'absolute', bottom: 16, left: 24}}>
                  <TextBold style={{fontSize: 12}}>COMING</TextBold>
                  <TextBold style={{fontSize: 12}}>SOON!</TextBold>
                </View>
                <View style={{gap: 17, flex: 1}}>
                  <TextBold style={{fontSize: 16}}>Existing LEND Account</TextBold>
                  <TextReg style={{fontSize: 12}}>
                    Your earnings will be reinvested back into your LEND Account, so you can
                    increase your earnings over time.
                  </TextReg>
                </View>
              </Card>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => setStep(1)}>
              <Card
                style={{
                  borderRadius: 12,
                  padding: 24,
                  alignItems: 'flex-start',
                  gap: 22,
                  flexDirection: 'row',
                  borderColor: '#00FFBD',
                  borderWidth: 1,
                }}
                cardWidth={screenWidth - 70}>
                <Image
                  source={require('../../imgs/unit21/wallet.png')}
                  style={{width: 46, height: 46}}
                />
                <View style={{gap: 17, flex: 1}}>
                  <TextBold style={{fontSize: 16}}>Internal Account</TextBold>
                  <TextReg style={{fontSize: 12}}>
                    Your earnings will be transferred to your an existing WALLET or BORROW account
                    to be used how you’d like.
                  </TextReg>
                </View>
              </Card>
            </TouchableOpacity>

            <TouchableOpacity disabled={true}>
              <Card
                style={{
                  borderRadius: 12,
                  padding: 24,
                  alignItems: 'flex-start',
                  gap: 22,
                  flexDirection: 'row',
                  opacity: 0.7,
                }}
                cardWidth={screenWidth - 70}>
                <Image
                  source={require('../../imgs/banking/bankManual.png')}
                  style={{width: 46, height: 46}}
                />
                <View style={{position: 'absolute', bottom: 16, left: 24}}>
                  <TextBold style={{fontSize: 12}}>COMING</TextBold>
                  <TextBold style={{fontSize: 12}}>SOON!</TextBold>
                </View>
                <View style={{gap: 17, flex: 1}}>
                  <TextBold style={{fontSize: 16}}>Whitelisted Address</TextBold>
                  <TextReg style={{fontSize: 12}}>
                    Your earnings will be deposited to an off SALT platform address of your choice
                    for a slight fee.
                  </TextReg>
                </View>
              </Card>
            </TouchableOpacity>
          </View>
        </View>
      )}
      {step === 1 && (
        <View style={{width: screenWidth - 32}}>
          <TextBold style={{fontSize: 24, marginBottom: 30, marginTop: 24, textAlign: 'center'}}>
            Earnings Destination Account
          </TextBold>
          <TextReg style={{lineHeight: 22, fontSize: 16, textAlign: 'center', marginBottom: 16}}>
            Select which WALLET or BORROW Account would you’d like as the destination option for
            your Earnings:
          </TextReg>
          <TextReg style={{fontSize: 16, marginBottom: 10}} onPress={() => setSelection(true)}>
            Select your Internal Account
          </TextReg>
          <TouchableOpacity
            onPress={() => setSelection(true)}
            style={{
              height: 60,
              backgroundColor: '#3D3D50',
              borderColor: '#EFEFEF',
              borderWidth: 0.5,
              borderRadius: 15,
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                margin: 20,
              }}>
              <TextReg style={{fontSize: 14, color: '#fff'}}>
                {selectedAccount ? selectedAccount?.label : 'Select from your Internal Accounts'}
              </TextReg>
              <Image
                source={require('../../imgs/downArrow.png')}
                style={{width: 13, height: 7.5, tintColor: '#fff'}}
              />
            </View>
          </TouchableOpacity>
          {selection && (
            <View
              style={{
                position: 'absolute',
                top: Platform.OS === 'ios' ? -50 : 0,
                backgroundColor: '#28283D',
                height: screenHeight,
                width: '100%',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  gap: 12,
                  alignItems: 'center',
                  justifyContent: 'flex-end',
                }}>
                {/* <TextInput
                  style={{
                    height: 38,
                    borderWidth: 1,
                    borderRadius: 8,
                    borderColor: '#AFAFAF',
                    paddingLeft: 10,
                    alignSelf: 'stretch',
                    width: '90%',
                    color: '#fff',
                    fontSize: 16 / PixelRatio.getFontScale(),
                  }}
                  onChangeText={text => {
                    setSearchValue(text)
                  }}
                  value={searchValue}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={'Type something...'}
                  keyboardType={'default'}
                  keyboardAppearance="dark"
                  placeholderTextColor={'#AFAFAF'}
                /> */}
                <TouchableOpacity onPress={() => setSelection(false)}>
                  <Image
                    source={require('../../imgs/notifClose.png')}
                    style={{width: 26, height: 26}}
                  />
                </TouchableOpacity>
              </View>
              {optionsLoading ? (
                <Image
                  source={require('../../imgs/loadingDots.gif')}
                  style={{height: 80, width: 80, marginTop: -10, opacity: 0.6}}
                />
              ) : (
                <View
                  style={{
                    height: screenHeight - 200,
                  }}>
                  {/* <View
                    style={{
                      backgroundColor: '#AFAFAF',
                      height: 1,
                      marginTop: 12,
                      marginBottom: 26,
                    }}
                  /> */}
                  <TextReg style={{fontSize: 16, padding: 10}}>WALLET Accounts</TextReg>
                  {walletOptions?.map(item => {
                    return (
                      <TouchableOpacity
                        style={{
                          width: '90%',
                          marginLeft: 30,
                          marginVertical: 10,
                          flexDirection: 'row',
                          opacity: item.isDisabled ? 0.5 : 1,
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}
                        disabled={item.isDisabled}
                        onPress={() => {
                          setAccountValue(item)
                        }}
                        key={item.name}>
                        <View>
                          <TextReg style={{fontSize: 16}}>
                            {item.label === 'Custody Account' ? 'WALLET Account' : item.label}
                          </TextReg>
                          <TextReg style={{fontSize: 16}}>{item.amount} Bal</TextReg>
                        </View>
                        {item.isDisabled && <TextReg>Currently active</TextReg>}
                      </TouchableOpacity>
                    )
                  })}
                  {borrowOptions?.length > 0 && (
                    <>
                      <TextReg style={{fontSize: 16, padding: 10}}>BORROW Accounts</TextReg>
                      <FlatList
                        style={{height: '58%', flexGrow: 0}}
                        data={borrowOptions}
                        renderItem={({item}) => {
                          return (
                            <TouchableOpacity
                              style={{
                                width: '90%',
                                marginLeft: 30,
                                marginVertical: 10,
                                opacity: item.isDisabled ? 0.5 : 1,
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                              }}
                              disabled={item.isDisabled}
                              onPress={() => {
                                setAccountValue(item)
                              }}
                              key={item.name}>
                              <View>
                                <TextBold style={{fontSize: 16}}>{item.label}</TextBold>
                                <View style={{flexDirection: 'row', gap: 10, alignItems: 'center'}}>
                                  <Image source={item.icon} style={{width: 15, height: 15}} />
                                  <TextReg style={{fontSize: 16}}>
                                    {new BigNumber(item.amount).multipliedBy(100).toFixed(2)}% LTV
                                  </TextReg>
                                </View>
                              </View>
                              {item.isDisabled && <TextReg>Currently active</TextReg>}
                            </TouchableOpacity>
                          )
                        }}
                      />
                    </>
                  )}
                  <View style={{flexDirection: 'row', gap: 4, marginTop: 'auto'}}>
                    <TextBold>Note:</TextBold>
                    <TextReg style={{flex: 1}}>
                      To see the account, create {investment?.investmentAsset} wallet under the
                      desired account
                    </TextReg>
                  </View>
                </View>
              )}
            </View>
          )}
        </View>
      )}
      {step === 2 && selectedAccount && (
        <ScrollView
          ref={ref => {
            scroll = ref
          }}
          style={{
            backgroundColor: '#28283D',
            alignSelf: 'stretch',
          }}
          contentContainerStyle={{alignItems: 'center'}}>
          <View style={{width: screenWidth - 64, alignItems: 'center', paddingTop: 24}}>
            <Image
              source={require('../../imgs/unit21/docMagnify.png')}
              style={{width: 84, height: 89}}
            />
            <TextBold style={{fontSize: 24, marginBottom: 30, marginTop: 24, textAlign: 'center'}}>
              Confirm Details for Your Earnings Destination
            </TextBold>
            <TextReg style={{lineHeight: 22, fontSize: 16, textAlign: 'center'}}>
              Your next Earnings payout will be deposited to your existing{' '}
              {getAccountType(selectedAccount.productType)} Account named {selectedAccount.label}{' '}
              per your selections.
            </TextReg>
            <View
              style={{height: 0.5, width: '100%', backgroundColor: '#FFFFFF', marginVertical: 16}}
            />
            <View>
              <View
                style={{
                  flexDirection: 'row',
                  width: '100%',
                  justifyContent: 'space-between',
                  marginBottom: 16,
                }}>
                <TextReg style={{fontSize: 16, width: '60%'}}>Amount to be Deposited:</TextReg>
                <TextReg style={{fontSize: 16}}>
                  {formatCrypto(
                    investment?.upcomingProjectedEarnings[0]?.payoutAmount,
                    investment?.investmentAsset,
                  )}{' '}
                  {investment?.investmentAsset}
                </TextReg>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  width: '100%',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                <TextReg style={{fontSize: 16, width: '60%'}}>Next Earnings Deposit Date:</TextReg>
                <TextReg style={{fontSize: 16}}>
                  {moment(investment?.upcomingProjectedEarnings[0]?.payoutDate).format('ll')}
                </TextReg>
              </View>
            </View>

            <TextReg
              style={{
                color: '#FFF',
                fontSize: 10,
                flexShrink: 1,
                flexWrap: 'wrap',
                marginTop: 30,
                marginBottom: 72,
              }}>
              By clicking “CONFIRM” below, you are authorizing SALT Lending to deposit your monthly
              Earnings Payout back into your existing LEND account. This will remain the destination
              for each month going forward, unless you come back and change the Earnings Destination
              option.
            </TextReg>
            <View
              style={{
                alignItems: 'center',
                marginBottom: 20,
                marginTop: 14,
                alignSelf: 'stretch',
              }}>
              <Button
                style={{alignSelf: 'stretch', marginLeft: 20, marginRight: 20}}
                isLoading={isLoading}
                disabled={!selectedAccount && !walletId}
                onPress={() => changeDestinationHandler()}>
                <TextReg
                  style={{
                    fontSize: 18,
                    letterSpacing: 0.7,
                    color: '#000',
                    alignSelf: 'stretch',
                  }}>
                  CONFIRM
                </TextReg>
              </Button>
              <Button
                style={{
                  alignSelf: 'stretch',
                  marginBottom: 80,
                  marginHorizontal: 14,
                  backgroundColor: 'transparent',
                  height: 60,
                }}
                onPress={() => setStep(0)}>
                <TextReg style={{color: '#00FFBD', fontSize: 18, letterSpacing: 2}}>CANCEL</TextReg>
              </Button>
            </View>
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  )
}

EarningDestinationOption.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

export default EarningDestinationOption
