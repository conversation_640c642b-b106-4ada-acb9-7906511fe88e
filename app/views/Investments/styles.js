import {StyleSheet, Dimensions, PixelRatio} from 'react-native'

const {width, height} = Dimensions.get('window')

const lendRequestBox = StyleSheet.create({
  lendRequestTermBox: {
    height: 108,
    flexDirection: 'column',
    alignSelf: 'stretch',
    backgroundColor: '#28283D',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 4,
    paddingTop: 12,
    paddingBottom: 12,
    position: 'relative',
  },
  lendRequestTermBoxActive: {
    height: 108,
    alignSelf: 'stretch',
    backgroundColor: '#28283D',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#00FFBD',
    paddingTop: 12,
    paddingBottom: 12,
    position: 'relative',
  },
})

const styles = StyleSheet.create({
  swiperContainer: {
    flex: 1,
    alignSelf: 'stretch',
    flexDirection: 'column',
  },
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
  statusBox: {
    flexDirection: 'row',
    alignSelf: 'stretch',
    justifyContent: 'space-between',
  },
  statusTitle: {
    fontSize: 20,
    marginBottom: 10,
    paddingLeft: 10,
    marginTop: 4,
  },
  statusImg: {
    height: 38,
    width: 38,
    marginRight: 10,
  },
  statusImgWarning: {
    height: 34,
    width: 42,
    marginRight: 10,
  },
  statusImgApple: {
    height: 38,
    width: 32,
    marginRight: 10,
  },
  statusCover: {
    height: 8,
    alignSelf: 'stretch',
    marginTop: -10,
    marginLeft: -10,
    marginRight: -10,
    marginBottom: 10,
  },
  statusMain: {
    paddingLeft: 10,
    marginTop: 6,
    fontSize: 15,
    marginBottom: 10,
  },
  statusButtonBox: {
    marginTop: 12,
    marginBottom: 18,
    alignSelf: 'stretch',
    marginLeft: 10,
    marginRight: 10,
  },
  statusCure: {
    paddingLeft: 10,
    fontSize: 16,
    color: '#00FFBD',
    marginBottom: 16,
  },
  statusDate: {
    paddingLeft: 10,
    fontSize: 15,
    marginBottom: 2,
    textDecorationLine: 'underline',
    textDecorationStyle: 'solid',
    textDecorationColor: '#00ffc3',
  },
  statusPrevent: {
    paddingLeft: 10,
    fontSize: 16,
    color: '#00FFBD',
    marginBottom: 16,
    marginTop: 6,
  },
  portfolioBox: {
    height: 60,
    borderBottomColor: '#f0f0f0',
    borderBottomWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  portfolioImg: {
    height: 94,
    width: 94,
    borderRadius: 15,
    marginRight: 12,
    opacity: 0.9,
  },
  portfolioValue: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 4,
  },
  portfolioTitle: {
    fontSize: 20,
    marginBottom: 16,
    paddingLeft: 10,
    marginTop: 4,
  },
  portfolioFooter: {
    alignSelf: 'stretch',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 12,
    marginBottom: 8,
  },
  //LoanRequestDetails
  loanDetailBox: {
    alignSelf: 'stretch',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 6,
    marginLeft: 8,
    marginRight: 8,
    borderColor: '#888',
    borderBottomWidth: 1,
    paddingBottom: 6,
  },
  loanRequestInputTitle: {
    color: '#FFF',
    fontSize: 16,
  },
  promoTermsText: {
    color: '#00FFBD',
    fontSize: 14,
    marginTop: -14,
    marginBottom: 10,
  },
  lendRequestInput: {
    height: 60,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#fff',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 6,
  },
  loanRequestInputError: {
    height: 40,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#E5705A',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#FFF',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 20,
  },
  loanRequestLTVBox: {
    height: 58,
    minWidth: 58,
    flex: 1,
    backgroundColor: '#28283D',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 14,
  },
  loanRequestLTVBoxActive: {
    height: 58,
    minWidth: 58,
    flex: 1,
    backgroundColor: '#28283D',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 14,
    borderWidth: 2,
    borderColor: '#00FFBD',
  },
  loanRequestLTVText: {
    fontSize: 16,
    color: '#FFF',
  },
  loanRequestLTVTextActive: {
    fontSize: 48,
    color: '#00FFBD',
  },
  lendRequestTermBox: lendRequestBox.lendRequestTermBox,

  lendRequestTermBoxActive: lendRequestBox.lendRequestTermBoxActive,
  lendRequestAssetBox: {...lendRequestBox.lendRequestTermBox, borderRadius: 12},
  lendRequestAssetBoxActive: {...lendRequestBox.lendRequestTermBoxActive, borderRadius: 12},
  // lendRequestTermBox: {
  //   height: 58,
  //   flex: 1,
  //   flexDirection: 'column',
  //   alignSelf: 'stretch',
  //   backgroundColor: '#28283D',
  //   alignItems: 'center',
  //   justifyContent: 'center',
  //   borderRadius: 4,
  //   paddingTop: '12px',
  //   paddingBottom: '12px',
  // },
  // lendRequestTermBoxActive: {
  //   height: 58,
  //   flex: 1,
  //   alignSelf: 'stretch',
  //   backgroundColor: '#28283D',
  //   alignItems: 'center',
  //   justifyContent: 'center',
  //   borderRadius: 4,
  //   borderWidth: 2,
  //   borderColor: '#00FFBD',
  //   paddingTop: '12px',
  //   paddingBottom: '12px',
  // },
  loanRequestRepaymentBox: {
    height: 58,
    width: 146,
    backgroundColor: '#28283D',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 14,
  },
  loanRequestRepaymentBoxActive: {
    height: 58,
    width: 146,
    backgroundColor: '#28283D',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 14,
    borderWidth: 2,
    borderColor: '#00FFBD',
  },
  loanChecklistCompleteImg: {
    height: 36,
    width: 36,
    opacity: 1,
    marginRight: 6,
    marginLeft: 6,
  },
  loanChecklistCompleteText: {
    fontSize: 17,
    marginBottom: 10,
    paddingLeft: 8,
    marginTop: 10,
    //textDecorationLine: 'line-through',
    //textDecorationStyle: 'solid',
    opacity: 0.7,
  },
  loanChecklistIncompleteImg: {
    height: 36,
    width: 36,
    borderRadius: 14,
    backgroundColor: '#e2e5ed',
    marginRight: 6,
    marginLeft: 6,
  },
  loanChecklistText: {
    fontSize: 17,
    marginBottom: 10,
    paddingLeft: 8,
    marginTop: 10,
  },
  loanRequestCollateralImg: {
    height: 30,
    width: 30,
    opacity: 0.9,
    marginLeft: 14,
    marginRight: 14,
    marginBottom: 15,
  },
  loanRequestStackwiseRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
    marginTop: 30,
    width: 260,
    borderBottomWidth: 0.5,
    borderColor: '#AFAFAF',
    paddingBottom: 20,
  },
  loanRequestStackwiseRowLast: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
    width: 260,
    marginBottom: 36,
  },
  loanRequestSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
    marginTop: 16,
    width: '100%',
    borderBottomWidth: 0.5,
    borderColor: '#AFAFAF',
    paddingBottom: 12,
  },
  loanRequestSummaryText: {
    fontSize: 19,
    color: '#FFF',
  },
  loanRequestStackwiseText: {
    fontSize: 19,
    color: '#000',
  },
  //errors
  showErrorBox: {
    marginTop: 20,
    marginBottom: 10,
    flexDirection: 'column',
    alignSelf: 'stretch',
    alignItems: 'center',
  },
  showErrorText: {
    fontSize: 18,
    color: '#e5705a',
    textAlign: 'center',
    marginLeft: 10,
    marginRight: 10,
  },

  //qrCode
  depositQrBox: {
    overflow: 'hidden',
    padding: 6,
    borderRadius: 14,
  },

  //LoanPayout
  payoutOptionCard: {
    justifyContent: 'center',
    height: 112,
    borderWidth: 2,
    backgroundColor: '#48485A',
  },
  payoutOptionCardBox: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: -16,
    marginBottom: 4,
  },
  oneTimeOptionImg: {
    height: 40,
    width: 40,
    marginRight: 14,
  },
  payoutOptionTitle: {
    fontSize: 18,
    marginBottom: 4,
    marginTop: 3,
    color: '#FFF',
  },
  loadingDots: {
    height: 40,
    width: 64,
    opacity: 0.6,
    alignSelf: 'center',
    zIndex: 5,
    marginBottom: 8,
    marginTop: 4,
  },
  convertLoadingDots: {
    height: 40,
    width: 40,
    opacity: 0.6,
    alignSelf: 'center',
    zIndex: 5,
  },
  stablecoinInput: {
    width: 250,
    height: 40,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#000',
    backgroundColor: '#fff',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 6,
    marginRight: 10,
  },
  verifyStablecoinInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#000',
    backgroundColor: '#fff',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 6,
    marginRight: 10,
  },
  stablecoinInputTitle: {
    color: '#FFF',
    fontSize: 15,
    marginBottom: 6,
  },
  banksEditUploadButton: {
    backgroundColor: '#fff',
    borderColor: '#00FFBD',
    borderWidth: 3,
    marginTop: 16,
    marginBottom: 20,
  },
  banksEditLoadingDots: {
    height: 40,
    width: 64,
    opacity: 0.6,
    alignSelf: 'center',
    zIndex: 5,
    marginBottom: 12,
  },
  allAccountsTitle: {
    fontSize: 20,
    marginBottom: 14,
    marginTop: 4,
  },
  allAccountsRow: {
    alignSelf: 'stretch',
    alignItems: 'flex-start',
    marginLeft: 10,
    marginTop: 10,
    marginBottom: 10,
    marginRight: 10,
  },
  allAccountsActive: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'stretch',
    justifyContent: 'space-between',
  },
  allAccountsStatusImg: {
    height: 20,
    width: 20,
    marginRight: 14,
  },
  allAccountsWarningImg: {
    height: 20,
    width: 24,
    marginRight: 12,
    marginLeft: -2,
  },
  allAccountsBox: {
    alignSelf: 'stretch',
    marginLeft: 42,
    marginRight: 42,
    marginTop: 10,
    marginBottom: 10,
  },
  allAccountsAddButton: {
    marginTop: 10,
    alignSelf: 'stretch',
    marginLeft: 16,
    marginRight: 16,
  },
  logoutBox: {
    paddingTop: 10,
    width,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'stretch',
    height: 50,
  },
  logoutText: {
    color: '#00FFBD',
    fontSize: 17,
  },
  convertTokenRow: {
    alignSelf: 'stretch',
    height: 64,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    margin: -10,
  },
  convertTokenBox1: {
    width: 240,
    height: 38,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 20,
    paddingRight: 10,
  },
  convertTokenBox2: {
    width: 120,
    height: 38,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
    paddingRight: 30,
    justifyContent: 'flex-end',
  },
  convertCollateralsBox: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginLeft: 20,
    marginRight: 20,
    height: 70,
    borderRadius: 14,
    backgroundColor: '#3D3D50',
    marginBottom: 10,
    paddingLeft: 14,
    paddingRight: 14,
  },
  convertCollateralsImg: {
    height: 30,
    width: 30,
    marginRight: 8,
  },
  convertTrashRightBox: {
    height: 64,
    backgroundColor: '#ff502f',
    marginLeft: 10,
    marginTop: -10,
    borderRadius: 14,
    justifyContent: 'center',
  },
  convertTrashRightImg: {
    height: 31,
    width: 26,
    marginLeft: 24,
  },
  convertTokenBox1Img: {
    height: 34,
    width: 34,
    marginRight: 10,
  },
  convertLayout: {
    alignSelf: 'stretch',
    margin: 6,
    marginLeft: 10,
    marginRight: 10,
  },
  convertUSDCBox: {
    alignItems: 'center',
    padding: 20,
    paddingLeft: 20,
    paddingRight: 20,
  },
  convertUSDCImg: {
    height: 44,
    width: 44,
    marginBottom: 16,
  },
  convertUSDCAmountTitle: {
    fontSize: 18,
    marginBottom: 4,
    color: '#fff',
  },
  convertUSDCNumber: {
    fontSize: 26,
    marginBottom: 6,
    color: '#00FFBD',
  },
  convertPleaseNoteText: {
    fontSize: 12,
    marginTop: 12,
    textAlign: 'center',
    marginBottom: 12,
    color: '#fff',
  },
  convertTotalBox: {
    alignSelf: 'stretch',
    marginTop: 14,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingRight: 28,
    marginBottom: 18,
  },
  convertTotalText: {
    fontSize: 16,
    color: '#fff',
    letterSpacing: 2,
  },
  convertDefaultedBox: {
    alignSelf: 'stretch',
    paddingLeft: 28,
    marginTop: -4,
  },
  convertAddCurrencyBox: {
    alignSelf: 'stretch',
    alignItems: 'center',
    marginTop: 32,
  },
  convertAddCurrencyText: {
    fontSize: 18,
    color: '#00FFBD',
    letterSpacing: 2,
  },
  convertNextButton: {
    marginTop: 26,
    alignSelf: 'stretch',
    margin: 14,
  },
  convertConfirmRow: {
    marginLeft: 10,
    marginRight: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 50,
    borderBottomWidth: 0.5,
    borderColor: '#969696',
  },
  convertConfirmRowNoBorder: {
    marginLeft: 10,
    marginRight: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 50,
  },
  convertReceivablesRow: {
    marginLeft: 10,
    marginRight: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 50,
    borderBottomWidth: 0.5,
    borderColor: '#969696',
  },
  convertErrorBox: {
    marginBottom: 10,
    flexDirection: 'column',
    alignSelf: 'stretch',
    alignItems: 'center',
  },
  confirmTokenImg: {
    height: 30,
    width: 30,
    marginRight: 3,
  },
  confirmButton: {
    marginTop: 20,
    alignSelf: 'stretch',
    margin: 14,
  },

  //Unit 21
  unit21InfoInputTitle: {
    color: '#FFF',
    marginBottom: 6,
    width: 260,
  },
  unit21InfoInputTitleError: {
    color: '#E6705B',
    marginBottom: 6,
    width: 260,
  },
  unit21InfoInputTitleBusiness: {
    color: '#FFF',
    marginBottom: 6,
    flexWrap: 'wrap',
  },
  unit21InfoInputTitleBusinessError: {
    color: '#E6705B',
    marginBottom: 6,
    width: 340,
    marginTop: 12,
  },
  unit21AcknowledgeTitle: {
    color: '#FFF',
    width: 260,
  },
  unit21AcknowledgeTitleMilitary: {
    color: '#FFF',
    flexWrap: 'wrap',
    flex: 1,
  },
  unit21InfoInput: {
    height: 54,
    borderWidth: 1,
    borderRadius: 6,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#FFF',
    backgroundColor: '#474756',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 18,
  },
  unit21InfoInputHalf: {
    height: 54,
    borderWidth: 1,
    borderRadius: 6,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#FFF',
    backgroundColor: '#474756',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 18,
  },
  unit21InfoInputSmall: {
    width: 80,
    height: 54,
    borderWidth: 1,
    borderRadius: 6,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    marginRight: 10,
    color: '#FFF',
    backgroundColor: '#474756',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 18,
  },
  unit21InfoInputDate: {
    flex: 1,
    height: 54,
    borderWidth: 1,
    borderRadius: 6,
    borderColor: '#cbcbcb',
    marginRight: 6,
    color: '#FFF',
    backgroundColor: '#474756',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 18,
    textAlign: 'center',
    fontSize: 17,
  },
  unit21InfoInputDateError2: {
    flex: 1,
    height: 54,
    borderWidth: 1,
    borderRadius: 6,
    borderColor: '#E6705B',
    marginRight: 6,
    color: '#FFF',
    backgroundColor: '#474756',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 18,
    textAlign: 'center',
    fontSize: 17,
  },
  unit21InfoInputError: {
    height: 40,
    borderWidth: 3,
    borderRadius: 14,
    borderColor: '#E6705B',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    backgroundColor: '#FFF',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 4,
  },
  unit21InfoInputDateError: {
    height: 54,
    borderWidth: 1,
    borderRadius: 6,
    borderColor: '#E6705B',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#FFF',
    backgroundColor: '#474756',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 18,
  },

  personalInfoInputTitle: {
    color: '#FFF',
    fontSize: 15,
    marginBottom: 6,
  },
  personalInfoInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 14,
  },
  withdrawErrorBox: {
    alignSelf: 'stretch',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 15,
  },
  withdrawErrorText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
    width: 270,
  },
  unit21MilitaryBox: {
    height: 50,
    alignSelf: 'stretch',
    borderRadius: 25,
    backgroundColor: '#FFF',
    marginTop: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 14,
    overflow: 'hidden',
  },
  toggleAcknowledgeButton: {
    flexDirection: 'row',
    marginTop: 14,
    alignItems: 'center',
    paddingRight: 10,
  },
  toggleAcknowledgeView: {
    height: 40,
    width: 40,
    borderRadius: 6,
    backgroundColor: '#3D3D50',
    borderWidth: 1,
    borderColor: '#efefef',
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 6,
  },
  unit21ShowFilesBox: {
    alignSelf: 'stretch',
    height: 40,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  unit21ShowFilesName: {
    alignSelf: 'stretch',
    height: 40,
    backgroundColor: '#FFF',
    borderRadius: 14,
    alignItems: 'center',
    paddingLeft: 4,
    justifyContent: 'center',
    paddingRight: 4,
    width: 300,
  },
  unit21IDVerificationHeaderBox: {
    alignSelf: 'stretch',
    paddingLeft: 30,
    paddingRight: 30,
    flex: 1,
    justifyContent: 'space-between',
  },
  unit21SelfieText: {
    color: '#FFF',
    fontSize: 20,
    marginBottom: 14,
    marginTop: 10,
  },
  unit21IDClickBox: {
    height: 200,
    borderRadius: 14,
    backgroundColor: '#48485A',
    alignSelf: 'stretch',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#DDD',
    borderStyle: 'dashed',
  },
  unit21PersonalImg: {
    height: 50,
    width: 50,
    marginBottom: 14,
  },
  unit21ClickBoxDetails: {
    color: '#FFF',
    fontSize: 12,
    marginTop: 10,
    marginBottom: -4,
  },
  unit21IDArrowImg: {
    height: 12,
    width: 22,
    position: 'absolute',
    top: 14,
    right: 14,
    opacity: 0.9,
  },
  unit21ToggleCheckImg: {
    height: 40,
    width: 40,
    borderRadius: 6,
  },

  //info toggle military active
  infoToggleContainer: {
    alignSelf: 'stretch',
    height: 46,
    flexDirection: 'row',
  },
  infoToggleOption: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoToggleOptionBackground: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoToggleText: {
    color: '#05868e',
    fontSize: 16,
  },
  infoToggleTextActive: {
    color: '#05868e',
    fontSize: 18,
    fontWeight: 'bold',
  },
  infoToggleTextBox: {
    flex: 1,
    alignSelf: 'stretch',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 30,
    marginRight: 30,
  },
  infoToggleTextBoxActive: {
    flex: 1,
    alignSelf: 'stretch',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 30,
    marginRight: 30,
    borderColor: '#eef0f0',
    borderBottomColor: '#05868e',
    borderBottomWidth: 2,
  },
  beamerBox: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(74, 74, 74, 0.5)',
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 50,
  },
  beamerSquare: {
    width: 340,
    backgroundColor: '#FFF',
    borderRadius: 8,
    flexDirection: 'column',
    alignItems: 'center',
    marginTop: 70,
    height: height - 250,
    overflow: 'hidden',
  },
  whiteDotsAnimationWallet: {
    width: 44,
    height: 44,
    marginLeft: 20,
    opacity: 0.9,
    alignSelf: 'center',
    zIndex: 5,
  },
  l1ReviewBox: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignSelf: 'stretch',
    borderBottomWidth: 1,
    borderColor: '#fff',
    paddingBottom: 10,
    marginTop: 15,
  },
})

export default styles
