import React, {useState} from 'react'
import {View} from 'react-native'
import {interpolate, useSharedValue, useDerivedValue, runOnJS} from 'react-native-reanimated'
import {TextBold, TextReg} from '../../components'
import {formatCrypto} from '../../util/helpers'

let BarHeader = ({translation, graphWidth, prices, datesArr, investment}) => {
  let [money, setMoney] = useState('$0')
  let [dateString, setDateString] = useState('')
  let [earningString, setEarningString] = useState('')
  let eachWidth = graphWidth / datesArr.length

  //rate limit for slow devices
  let lastUpdate = useSharedValue(0)
  let updateInterval = 100

  useDerivedValue(() => {
    const now = Date.now()

    if (now - lastUpdate.value < updateInterval) {
      return // Skip this update
    }
    lastUpdate.value = now
    let modDate = Math.floor(translation.x.value / eachWidth)
    let dateString = datesArr[modDate === 0 ? 0 : modDate - 1][0]
    let dateNumeric = datesArr[modDate === 0 ? 0 : modDate - 1][1]
    const earningString = dateNumeric > prices.changePoint ? 'Projected' : 'Received'
    // Interpolate the price based on translation.x.value
    const priceIndex = interpolate(
      translation.x.value,
      [0, graphWidth], // Input range (start to end of graph)
      [0, prices.prices.length - 1], // Output range (index in prices array)
    )
    // Interpolate to get the price between the closest two prices
    const price = interpolate(
      priceIndex,
      [Math.floor(priceIndex), Math.ceil(priceIndex)],
      [
        parseFloat(prices.prices[Math.floor(priceIndex)][0]),
        parseFloat(prices.prices[Math.ceil(priceIndex)][0]),
      ],
    )

    runOnJS(setEarningString)(earningString)
    runOnJS(setDateString)(dateString)
    runOnJS(setMoney)(price)
  })

  return (
    <View style={{gap: 4, marginBottom: 35}}>
      <View style={{flexDirection: 'row', gap: 4, alignItems: 'center', marginBottom: 4}}>
        <View style={{width: 10, height: 10, backgroundColor: '#00FFBD', borderRadius: 5}} />
        <TextReg style={{color: '#00FFBD', fontSize: 12, letterSpacing: 1.2}}>{dateString}</TextReg>
      </View>
      <View style={{flexDirection: 'row', justifyContent: 'space-between', width: '100%', gap: 20}}>
        <TextBold style={{fontSize: 16}}>
          {formatCrypto(money, investment?.investmentAsset) + ' ' + investment?.investmentAsset}
        </TextBold>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            gap: 6,
          }}>
          <View
            style={{
              borderColor: '#00FFBD',
              borderTopWidth: 2,
              width: 28,
              borderStyle: 'solid',
            }}
          />
          <TextReg style={{fontSize: 12}}>Received</TextReg>
        </View>
      </View>
      <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
        <TextReg style={{fontSize: 12}}>{earningString} earnings</TextReg>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            gap: 6,
          }}>
          <View style={{flexDirection: 'row', gap: 4}}>
            <View
              style={{
                borderColor: '#00FFBD',
                borderTopWidth: 2,
                width: 6,
                borderStyle: 'solid',
              }}
            />
            <View
              style={{
                borderColor: '#00FFBD',
                borderTopWidth: 2,
                width: 6,
                borderStyle: 'solid',
              }}
            />
            <View
              style={{
                borderColor: '#00FFBD',
                borderTopWidth: 2,
                width: 6,
                borderStyle: 'solid',
              }}
            />
          </View>
          <TextReg style={{fontSize: 12}}>Projected</TextReg>
        </View>
      </View>
    </View>
  )
}
export default BarHeader
