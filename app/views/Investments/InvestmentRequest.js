import React, {useEffect, useRef, useState} from 'react'
import {
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Image,
  Platform,
  Dimensions,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native'
import {connect, useDispatch, useSelector} from 'react-redux'
import _ from 'lodash'

import {TextReg, TextBold, Card, Button} from '../../components'
import commonStyles from '../../styles/commonStyles'
import styles from './styles'
import {formatCrypto, numberWithCommas} from '../../util/helpers'
import {getInvestments, saveInvestmentRequest} from '../../store/investments/investments.actions'
import {sendEvent} from '../../store/analytics/analytics.actions'
import {cryptoMeta} from '../../util/enumerables'
import BigNumber from 'bignumber.js'
import {useNavigation} from '@react-navigation/native'
import {increaseRefreshDataCount} from '../../store/user/user.actions'
import {navigateInvestments} from './helpers'

const {width: screenWidth} = Dimensions.get('window')

const InvestmentRequest = props => {
  const [apy, setApy] = useState(new BigNumber(0))
  const [monthlyEarnings, setMonthlyEarnings] = useState(new BigNumber(0))
  const [depositAmount, setDepositAmount] = useState('')
  const [termLength, setTermLength] = useState()
  const [assetType, setAssetType] = useState('')
  const [submitLoading, setSubmitLoading] = useState(false)
  const [submitError, setSubmitError] = useState('')
  const [totalCumulativeEarnings, setTotalCumulativeEarnings] = useState(new BigNumber(0))
  const [termOptions, setTermOptions] = useState([])
  const [collateralOptions, setCollateralOptions] = useState([])

  const dispatch = useDispatch()
  const navigation = useNavigation()

  let WebService = useSelector(state => state.auth.WebService || {})

  const {investmentRates, investments, account, tokenPrices, user, dataLoaded} = props

  useEffect(() => {
    let eventName = 'LendRequest-Start-Android'
    if (Platform.OS === 'ios') {
      eventName = 'LendRequest-Start-iOS'
    }
    dispatch(sendEvent(eventName))
  }, [dispatch])

  const submitInvestmentRequest = async () => {
    setSubmitLoading(true)
    const lockUpPeriod =
      investmentRates?.find(rate => rate.term === termLength && rate.currency === assetType)
        ?.lockUpPeriod || 12

    const requestData = {
      investmentType: 'iba',
      interestRate: apy.times(0.01).toNumber(),
      term: termLength.toString(),
      proposedAmount: new BigNumber(depositAmount).toNumber(),
      startingAmount: new BigNumber(depositAmount).toNumber(),
      investmentAsset: assetType,
      accountId: account.accountId,
      monthlyEarning: monthlyEarnings.toNumber(),
      cumulativeEarning: totalCumulativeEarnings.toNumber(),
      lockUpPeriod: Number(lockUpPeriod),
    }

    await WebService.submitInvestmentRequest(requestData)
      .then(async res => {
        const investment = res.data
        await WebService.setAccountName(account.accountId, account.ref, {
          assetType,
        })
        //analytics
        let eventName = 'LendRequest-Complete-Android'
        if (Platform.OS === 'ios') {
          eventName = 'LendRequest-Complete-iOS'
        }
        dispatch(sendEvent(eventName))

        dispatch(saveInvestmentRequest(investment))
        dispatch(getInvestments())
        dispatch(increaseRefreshDataCount())
        setSubmitLoading(false)
        navigateInvestments(investment, account, user, navigation)
      })
      .catch(err => {
        console.log('submit err', err)
        let submitError = 'Lend Error'
        if (typeof err?.data?.body?.error === 'string') {
          submitError = err.data.body.error
        }

        setSubmitLoading(false)
        setSubmitError(submitError)
      })
  }

  useEffect(() => {
    calculateValues()
  }, [assetType, termLength, investmentRates, depositAmount])

  useEffect(() => {
    getCollateralOptions()
    getTermOptions(assetType)
  }, [termLength, assetType, investments, investmentRates])

  const getCollateralOptions = () => {
    const activeRates = investmentRates?.filter(rate => rate.isActive)
    const activeCurrencies = ['BTC', 'ETH', 'USDT', 'USDC']
      .map(asset => (activeRates.some(rate => rate.currency === asset) ? asset : null))
      ?.filter(asset => asset)

    const options = Array.from(cryptoMeta)
      .filter(([key, value]) => key && activeCurrencies?.includes(key))
      .map(([key, value]) => {
        const active =
          investments?.length > 0
            ? investments?.find(investment => investment.investmentAsset === value.currency)
              ? true
              : false
            : false

        return {
          label: value.name,
          value: value.currency,
          icon: value.icon,
          alt: key,
          active,
        }
      })

    setCollateralOptions(options)
  }

  const getTermOptions = currency => {
    const activeRates = investmentRates?.filter(rate => rate.isActive)
    const currencyRates = currency
      ? activeRates?.filter(rate => rate.currency === currency)
      : _.uniqBy(activeRates, 'term')

    const hasTerm = currencyRates?.find(rate => +rate.term === termLength)
    if (currency && !hasTerm) {
      const lastTerm = currencyRates?.sort((a, b) => b.term - a.term)[0]?.term
      setTermLength(lastTerm)
    }

    const options = currencyRates?.map(rate => {
      return {label: rate.term, value: +rate.term}
    })

    options?.sort((a, b) => {
      return +a.value - +b.value
    })

    setTermOptions(options)
  }

  const calculateValues = () => {
    const rate = investmentRates?.find(rate => {
      return rate.currency === assetType && +rate.term === +termLength
    })
    if (!rate) {
      return
    }

    const apy = new BigNumber(rate?.earnRate)
    const monthlyRate = apy.div(12)
    const displayApy = apy.times(100)
    setApy(displayApy)
    const monthlyEarnings = monthlyRate.times(depositAmount)
    setMonthlyEarnings(monthlyEarnings)
    const totalCumulativeEarnings = monthlyEarnings.times(termLength)
    setTotalCumulativeEarnings(totalCumulativeEarnings)
  }

  let scroll = useRef()
  const selectedPrice = assetType ? tokenPrices[`${assetType}-USD`] : null
  const amountNeeded = new BigNumber(10000)
  const amount = amountNeeded
    .dividedBy(selectedPrice?.price)
    .toFixed(cryptoMeta.get(assetType)?.balanceDigits)
  const selectedValue = assetType ? BigNumber.max(amount, 0) : null

  const updateLendAmount = (text = depositAmount) => {
    var letterRegExp = /[a-zA-Z]/g
    if (letterRegExp.test(text)) {
      return
    }
    let parsedText = text.replace('$', '')
    parsedText = parsedText.split(' ').join('')
    parsedText = parsedText.split(',').join('')

    if (parsedText == '.') {
      return
    }
    setDepositAmount(parsedText)
  }

  return (
    <SafeAreaView style={styles.box}>
      <View
        style={{
          flexDirection: 'row',
          alignSelf: 'stretch',
          justifyContent: 'space-between',
          marginTop: 5,
          marginBottom: 5,
        }}>
        <TouchableOpacity
          onPress={() => navigation?.navigate('Home')}
          style={{height: 40, width: 40, marginLeft: 20}}>
          <Image
            source={require('../../imgs/backToSettings.png')}
            style={{height: 25, width: 20}}
          />
        </TouchableOpacity>
        <TextReg style={{color: '#FFF', fontSize: 18}}>{'Open LEND Account'}</TextReg>
        <View style={{height: 40, width: 40, marginRight: 20}} />
      </View>
      <View style={commonStyles.tileContainer}>
        {!dataLoaded ? (
          <ActivityIndicator size="large" color="#fff" style={{marginVertical: 10}} />
        ) : (
          <ScrollView
            ref={ref => {
              scroll = ref
            }}
            style={{
              backgroundColor: '#28283D',
              alignSelf: 'stretch',
            }}
            contentContainerStyle={{alignItems: 'center'}}>
            <Card
              style={{
                borderRadius: 4,
                paddingTop: 28,
                paddingBottom: 28,
                paddingLeft: 12,
                paddingRight: 12,
                alignItems: 'flex-start',
              }}
              cardWidth={screenWidth - 16}>
              <TextBold style={{...styles.loanRequestInputTitle, fontSize: 24, textAlign: 'left'}}>
                LEND Calculator
              </TextBold>
              <TextReg style={{...styles.loanRequestInputTitle, fontSize: 16, marginTop: 25}}>
                Use this tool to customize the perfect LEND Account for you.{' '}
              </TextReg>
              <View style={{marginTop: 32}}>
                <TextReg style={{...styles.loanRequestInputTitle, marginBottom: 6}}>
                  What asset amount will you be LENDing?
                </TextReg>
                <TextInput
                  style={styles.lendRequestInput}
                  onChangeText={text => {
                    updateLendAmount(text)
                    calculateValues()
                  }}
                  value={numberWithCommas(depositAmount)}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={'Enter amount'}
                  keyboardType={'numeric'}
                  keyboardAppearance="dark"
                />

                {selectedValue && (
                  <TextReg style={{fontSize: 16, color: '#AFAFAF'}}>
                    The minimum deposit amount is {formatCrypto(selectedValue, assetType)}{' '}
                    {assetType}
                  </TextReg>
                )}

                <TextReg style={[styles.loanRequestInputTitle, {marginBottom: 12, marginTop: 32}]}>
                  How long do you want your LENDing duration to be?
                </TextReg>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignSelf: 'center',
                    flexWrap: 'wrap',
                    height: 98,
                    gap: 8,
                  }}>
                  {termOptions?.map(option => (
                    <TouchableOpacity
                      style={{flex: 2}}
                      onPress={() => {
                        setTermLength(option.value)
                      }}>
                      <View
                        style={
                          termLength === option.value
                            ? {...styles.lendRequestAssetBoxActive, borderRadius: 12}
                            : {...styles.lendRequestAssetBox, borderRadius: 12}
                        }>
                        <TextBold style={styles.loanRequestLTVTextActive}>{option.label}</TextBold>
                        <TextBold style={styles.loanRequestLTVText}>Months</TextBold>
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
                <View style={{marginTop: 32}}>
                  <TextReg style={{...styles.loanRequestInputTitle, marginBottom: 12}}>
                    What asset do you plan on LENDing?
                  </TextReg>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignSelf: 'stretch',
                      flexWrap: 'wrap',
                      height: 108,
                      gap: 8,
                    }}>
                    {collateralOptions?.map(option => (
                      <TouchableOpacity
                        style={{flex: 2}}
                        disabled={option.disabled || option.active}
                        onPress={() => {
                          setAssetType(option.value)
                        }}>
                        <View
                          style={
                            option.disabled
                              ? {...styles.lendRequestTermBox, opacity: 0.6}
                              : option.active
                              ? {
                                  ...styles.lendRequestTermBox,
                                  borderColor: '#6DE168',
                                  borderWidth: 1,
                                  backgroundColor: 'transparent',
                                }
                              : assetType === option.value
                              ? styles.lendRequestTermBoxActive
                              : styles.lendRequestTermBox
                          }>
                          {option.disabled ? (
                            <View
                              style={{
                                textAlign: 'center',
                                borderColor: '#6991E6',
                                borderWidth: 1,
                                borderRadius: 4,
                                padding: 2,
                                alignItems: 'center',
                                position: 'absolute',
                                top: 2,
                              }}>
                              <TextBold style={{fontSize: 9, color: '#6991E6', letterSpacing: 2}}>
                                COMING
                              </TextBold>
                              <TextBold style={{fontSize: 9, color: '#6991E6', letterSpacing: 2}}>
                                SOON!
                              </TextBold>
                            </View>
                          ) : option.active ? (
                            <View
                              style={{
                                textAlign: 'center',
                                borderColor: '#6DE168',
                                borderWidth: 1,
                                borderRadius: 4,
                                padding: 2,
                                alignItems: 'center',
                                position: 'absolute',
                                top: 4,
                              }}>
                              <TextBold
                                style={{
                                  fontSize: 9,
                                  color: '#6DE168',
                                  letterSpacing: 2,
                                  fontWeight: 900,
                                }}>
                                ACTIVE
                              </TextBold>
                            </View>
                          ) : null}
                          <Image
                            source={option.icon}
                            style={{width: 40, height: 40, marginBottom: 10, marginTop: 16}}
                          />
                          <TextReg style={styles.loanRequestLTVText}>{option.label}</TextReg>
                        </View>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              </View>
            </Card>
            <Card
              style={{
                borderRadius: 4,
                paddingTop: 12,
                paddingLeft: 12,
                paddingRight: 12,
              }}
              cardWidth={screenWidth - 16}>
              <Image
                source={require('../../imgs/investmentCalculator.png')}
                style={{width: screenWidth - 40, height: 90, alignSelf: 'center'}}
              />
              <View style={{...styles.loanRequestSummaryRow, marginTop: 22}}>
                <TextBold style={{fontSize: 36, color: '#00FFBD'}}>APY</TextBold>
                <TextBold style={{fontSize: 36, color: '#00FFBD'}}>
                  {apy ? `${apy}%` : '0.00 %'}
                </TextBold>
              </View>
              <View style={styles.loanRequestSummaryRow}>
                <TextReg style={{...styles.loanRequestSummaryText, color: '#00FFBD'}}>
                  Monthly Earnings
                </TextReg>
                {monthlyEarnings && (
                  <TextBold style={{...styles.loanRequestSummaryText, color: '#00FFBD'}}>
                    {formatCrypto(monthlyEarnings, assetType)} {assetType}
                  </TextBold>
                )}
              </View>

              <View style={styles.loanRequestSummaryRow}>
                <TextReg style={styles.loanRequestSummaryText}>Deposit Amount</TextReg>
                {depositAmount && (
                  <TextBold style={styles.loanRequestSummaryText}>
                    {formatCrypto(depositAmount, assetType)} {assetType}
                  </TextBold>
                )}
              </View>
              <View style={styles.loanRequestSummaryRow}>
                <TextReg style={styles.loanRequestSummaryText}>Term Duration</TextReg>
                <TextBold style={styles.loanRequestSummaryText}>
                  {termLength ? termLength + ' Months' : ''}
                </TextBold>
              </View>
              <View style={styles.loanRequestSummaryRow}>
                <TextReg style={styles.loanRequestSummaryText}>Asset Type</TextReg>
                <TextBold style={styles.loanRequestSummaryText}>
                  {assetType ? assetType : 'TBD'}
                </TextBold>
              </View>
              <View style={{...styles.loanRequestSummaryRow, flexWrap: 'wrap', gap: 10}}>
                <View>
                  <TextReg style={styles.loanRequestSummaryText}>Total Cumulative Earnings</TextReg>
                  <TextReg
                    style={{...styles.loanRequestSummaryText, fontSize: 12, color: '#AFAFAF'}}>
                    For the full period only.
                  </TextReg>
                </View>
                {totalCumulativeEarnings && (
                  <TextBold style={styles.loanRequestSummaryText}>
                    {formatCrypto(totalCumulativeEarnings, assetType)} {assetType}
                  </TextBold>
                )}
              </View>

              <View
                style={{
                  alignItems: 'center',
                  marginBottom: 20,
                  marginTop: 14,
                  alignSelf: 'stretch',
                }}>
                <Button
                  style={{alignSelf: 'stretch', marginLeft: 20, marginRight: 20}}
                  disabled={
                    depositAmount == 0 ||
                    termLength == 0 ||
                    assetType == '' ||
                    new BigNumber(depositAmount || 0).isLessThan(selectedValue) ||
                    !account
                  }
                  isLoading={submitLoading}
                  onPress={submitInvestmentRequest}>
                  <TextReg
                    style={{
                      fontSize: 18,
                      letterSpacing: 0.7,
                      color: '#000',
                      alignSelf: 'stretch',
                    }}>{`CONTINUE`}</TextReg>
                </Button>
                {submitError && (
                  <View style={styles.showErrorBox}>
                    <TextReg style={styles.showErrorText}>{submitError}</TextReg>
                  </View>
                )}
              </View>
            </Card>
          </ScrollView>
        )}
      </View>
    </SafeAreaView>
  )
}

InvestmentRequest.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

const mapStateToProps = state => {
  const accounts = state.user.user.accounts
  const investmentAccounts = accounts?.filter(acc => acc.productType === 'investment')
  const account = accounts.find(acc => acc.ref === state.auth.account.ref)
  const forPersonal = account?.type === 'personal'
  const activeInvestments = investmentAccounts
    ?.filter(acc =>
      forPersonal
        ? acc.type === 'personal'
        : acc.type === 'business' && acc?.entityProfile?.id === account?.entityProfile?.id,
    )
    ?.map(acc => (acc?.product?.investment?.errorMessage ? null : acc?.product?.investment))
    ?.filter(acc => acc)

  return {
    investments: activeInvestments,
    dataLoaded: state.user.dataLoaded,
    account,
    user: state.user.user,
    tokenPrices: state.user.prices,
    investmentRates: state.investments?.investmentRates,
  }
}

export default connect(mapStateToProps)(InvestmentRequest)
