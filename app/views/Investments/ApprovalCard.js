import React from 'react'
import {View, Image, Linking, Dimensions} from 'react-native'
import {TextBold, TextReg, Button} from '../../components'

const {width: ScreenWidth} = Dimensions.get('window')

const ApprovalCard = props => {
  const contactUs = () => {
    Linking.openURL(`mailto:<EMAIL>?subject=${props.subject}`)
  }

  return (
    <View
      style={{
        marginHorizontal: 14,
        alignSelf: 'stretch',
        alignItems: 'center',
        marginTop: 80,
        marginBottom: 60,
        width: ScreenWidth - 28,
      }}>
      <Image source={props.icon} style={{height: 57, width: 57}} resizeMode="contain" />
      <TextBold style={{fontSize: 20, marginVertical: 34, textAlign: 'center', width: ScreenWidth - 80}}>{props.title}</TextBold>
      <TextReg style={{fontSize: 16, marginBottom: 80, textAlign: 'center', width: ScreenWidth - 80}}>{props.description}</TextReg>
      <TextReg style={{fontSize: 16, marginBottom: 34, textAlign: 'center', width: ScreenWidth - 140}}>
        Have a question in the meantime? Get it touch with us.
      </TextReg>
      <Button
        style={{
          alignSelf: 'stretch',
          height: 60,
        }}
        onPress={() => contactUs()}>
        <TextBold style={{color: '#000', fontSize: 18, letterSpacing: 2}}>CONTACT SALES</TextBold>
      </Button>
    </View>
  )
}

export default ApprovalCard
