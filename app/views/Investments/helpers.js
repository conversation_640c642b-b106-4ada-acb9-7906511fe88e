export const navigateInvestments = (investment, account, user, navigation) => {
  const product = account?.product
  const isInvestmentEmpty = product?.investment?.errorMessage

  const status = investment?.status
  const verification = investment?.verification
  let deposits = user?.allWallets[account.ref - 1]?.filter(
    w => w.currency == investment?.investmentAsset,
  )
  const totalValue = investment?.startingAmount

  const collateralTotalWithSalt =
    deposits?.reduce(
      (sum, collateral) => parseFloat(sum) + parseFloat(collateral.projectedBalance),
      0,
    ) || 0
  let totalRemaining = totalValue - collateralTotalWithSalt
  const depositCompleted = totalRemaining <= 0

  const isApproved = verification?.lendSummaryStatus === 'passed'
  const identityVerificationStatus =
    investment?.verification?.identityVerificationStatus == 'passed' ||
    investment?.verification?.identityVerificationStatus == 'completed_prior' ||
    investment?.verification?.identityVerificationStatus == 'not_available'

  const photoIdVerificationStatus =
    investment?.verification?.photoIdVerificationStatus == 'passed' ||
    investment?.verification?.photoIdVerificationStatus == 'completed_prior'

  if (!identityVerificationStatus) {
    navigation.push('FlowL1PhoneNumber', {forInvestment: true, id: investment.id})
  } else if (!photoIdVerificationStatus) {
    navigation.push('JumioVerification', {forInvestment: true, id: investment.id})
  } else if (!depositCompleted) {
    navigation.push('DepositAssets', {id: investment.id})
  } else if (depositCompleted && !isApproved) {
    navigation.push('ConfirmDetails', {id: investment.id})
  } else if (
    depositCompleted &&
    isApproved &&
    status !== 'pending_signatures' &&
    status !== 'active' &&
    status !== 'pending_closure' &&
    status !== 'closed'
  ) {
    navigation.push('PendingApproval', {id: investment.id})
  } else if (depositCompleted && isApproved && status === 'pending_signatures') {
    navigation.push('AwaitingSignatures', {id: investment.id})
  } else if (
    !isInvestmentEmpty &&
    (status === 'active' || status === 'pending_closure' || status === 'closed')
  ) {
    navigation.push('LendDashboard', {id: investment.id})
  }
}
