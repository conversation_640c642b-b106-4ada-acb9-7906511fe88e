import React, {Component} from 'react'
import {View, TouchableOpacity, ScrollView, Image, Dimensions, SafeAreaView} from 'react-native'
import {connect} from 'react-redux'
import {BigNumber} from 'bignumber.js'
import moment from 'moment'

import {TextReg, TextBold, Card, Button} from '../../components'
import commonStyles from '../../styles/commonStyles'
import styles from './styles'
import {addMonths, formatCrypto, nthNumber} from '../../util/helpers'
import {
  clearCancelledInvestmentRequest,
  getInvestments,
} from '../../store/investments/investments.actions'
import {increaseRefreshDataCount} from '../../store/user/user.actions'

const {width: screenWidth} = Dimensions.get('window')

class ConfirmDetails extends Component {
  constructor(props) {
    super(props)

    this.state = {
      isLoading: false,
      cancelling: false,
      checked: false,
    }
    this.inputs = {}
  }

  updateInvestmentStatus = async () => {
    const {lendData} = this.props
    this.setState({isLoading: true})
    try {
      await this.props.WebService.acknowledgeInvestmentRequest(lendData?.id)
      await this.props.dispatch(getInvestments())
      await this.props.dispatch(increaseRefreshDataCount())

      this.props.navigation.navigate('PendingApproval')
      this.setState({isLoading: false})
    } catch (err) {
      console.log(err)
      this.setState({isLoading: false})
    }
  }

  handleCancelRequest = async () => {
    const {lendData} = this.props
    this.setState({cancelling: true})
    try {
      await this.props.WebService.cancelInvestmentRequest(lendData?.id)
      await this.props.dispatch(increaseRefreshDataCount())

      await this.props.dispatch(getInvestments())
      await this.props.dispatch(clearCancelledInvestmentRequest(lendData?.id))
      this.setState({cancelling: false})
      this.props.navigation.navigate('CreateInvestment')
    } catch (err) {
      throw err
    }
    this.setState({cancelling: false})
  }

  render() {
    const {lendData} = this.props
    const {checked, cancelling, isLoading} = this.state
    const depositValue = new BigNumber(lendData?.startingAmount)
    const apy = new BigNumber(lendData?.interestRate)
    const monthlyRate = apy.div(12)
    const monthlyEarnings = monthlyRate.times(lendData?.startingAmount)
    const totalCumulativeEarnings = monthlyEarnings.times(lendData.term)
    const payoutDate = lendData?.upcomingProjectedEarnings[0]?.payoutDate
    const originationDate = lendData?.originationDate
    const maturityDate = lendData?.maturityDate

    const summary = [
      {
        title: 'Deposit',
        value:
          formatCrypto(depositValue, lendData?.investmentAsset) + ' ' + lendData?.investmentAsset,
      },
      {
        title: 'Interest Rate',
        value: new BigNumber(lendData?.interestRate).times(100).toString() + '%',
      },
      {
        title: 'Duration',
        value: lendData?.term + ' Months',
      },
      {
        title: 'Monthly Earning Payment (Est.)',
        value:
          formatCrypto(monthlyEarnings, lendData?.investmentAsset) +
          ' ' +
          lendData?.investmentAsset,
      },
      {
        title: 'Total Cumulative Earnings (Est.)',
        value:
          formatCrypto(totalCumulativeEarnings, lendData?.investmentAsset) +
          ' ' +
          lendData?.investmentAsset,
      },
      {
        title: 'Origination Date (Est.)',
        value: moment(originationDate).format('ll'),
      },
      {
        title: 'First Payout Date (Est.)',
        value: moment(new Date(payoutDate)).format('ll'),
      },
      {
        title: 'Monthly Payout Date',
        value: lendData?.paymentDayOfMonth + nthNumber(lendData.paymentDayOfMonth),
      },
      {
        title: 'Maturity Date (Est.)',
        value: moment(maturityDate).format('ll'),
      },
      {
        title: 'Withdrawal Fee',
        value: new BigNumber(lendData?.earlyClosingRate).times(100).toString() + '%',
      },
      {
        title: 'Lock Up Period',
        value: `${lendData?.lockUpPeriod} ${
          Number(lendData?.lockUpPeriod) > 1 ? 'Months' : 'Month'
        }`,
      },
    ]

    return (
      <SafeAreaView style={styles.box}>
        <View
          style={{
            flexDirection: 'row',
            alignSelf: 'stretch',
            justifyContent: 'space-between',
            marginTop: 5,
            marginBottom: 5,
          }}>
          <TouchableOpacity
            onPress={() => this.props.navigation?.navigate('Home')}
            style={{height: 40, width: 40, marginLeft: 20}}>
            <Image
              source={require('../../imgs/backToSettings.png')}
              style={{height: 25, width: 20}}
            />
          </TouchableOpacity>
          <TextReg style={{color: '#FFF', fontSize: 18}}>{'Open LEND Account'}</TextReg>
          <View style={{height: 40, width: 40, marginRight: 20}} />
        </View>
        <View style={commonStyles.tileContainer}>
          <ScrollView
            style={{
              backgroundColor: '#28283D',
              alignSelf: 'stretch',
            }}
            contentContainerStyle={{alignItems: 'center'}}>
            <View
              style={{
                alignSelf: 'stretch',
                alignItems: 'flex-start',
                marginHorizontal: 14,
                marginVertical: 24,
              }}>
              <TextBold
                style={{
                  color: '#FFF',
                  fontSize: 24,
                  marginBottom: 24,
                  fontWeight: 700,
                }}>
                Ready to Start Earning?
              </TextBold>
              <TextReg
                style={{
                  color: '#FFF',
                  fontSize: 16,
                }}>
                Review your <TextBold>{lendData?.account?.name}</TextBold> LEND Account terms below.
              </TextReg>
            </View>
            <Card
              style={{
                borderRadius: 15,
                paddingTop: 12,
                paddingBottom: 28,
                paddingLeft: 12,
                paddingRight: 12,
              }}
              cardWidth={screenWidth - 16}>
              {summary.map((item, index) => (
                <View style={{...styles.loanRequestSummaryRow, flexWrap: 'wrap'}}>
                  <TextReg style={styles.loanRequestSummaryText}>{item.title}</TextReg>
                  <TextBold style={styles.loanRequestSummaryText}>{item.value}</TextBold>
                </View>
              ))}
            </Card>
            <TouchableOpacity onPress={() => this.setState({checked: !checked})}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'stretch',
                  alignSelf: 'stretch',
                  marginTop: 14,
                  marginBottom: 14,
                  width: screenWidth - 28,
                }}>
                {checked ? (
                  <Image
                    source={require('../../imgs/referrals/checkedBox.png')}
                    style={{height: 28, width: 28, marginRight: 14}}
                  />
                ) : (
                  <Image
                    source={require('../../imgs/referrals/unchecked.png')}
                    style={{height: 28, width: 28, marginRight: 14}}
                  />
                )}
                <TextReg style={{color: '#FFF', fontSize: 16, width: 295}}>
                  By clicking CONFIRM here, you are agreeing to the terms displayed and are
                  acknowledging that you agree with having LEND documents created and sent to you
                  via email.
                </TextReg>
              </View>
            </TouchableOpacity>
            <View
              style={{
                alignItems: 'center',
                marginBottom: 20,
                marginTop: 14,
                alignSelf: 'stretch',
              }}>
              <Button
                style={{alignSelf: 'stretch', marginLeft: 20, marginRight: 20}}
                disabled={!checked}
                isLoading={isLoading}
                onPress={() => this.updateInvestmentStatus()}>
                <TextReg
                  style={{
                    fontSize: 18,
                    letterSpacing: 0.7,
                    color: '#000',
                    alignSelf: 'stretch',
                  }}>
                  CONFIRM
                </TextReg>
              </Button>
              <Button
                style={{
                  alignSelf: 'stretch',
                  marginBottom: 80,
                  marginHorizontal: 14,
                  backgroundColor: 'transparent',
                  height: 60,
                }}
                isLoading={cancelling}
                onPress={() => this.handleCancelRequest()}>
                <TextReg style={{color: '#00FFBD', fontSize: 18, letterSpacing: 2}}>
                  CANCEL LEND REQUEST
                </TextReg>
              </Button>
            </View>
          </ScrollView>
        </View>
      </SafeAreaView>
    )
  }
}

ConfirmDetails.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

const mapStateToProps = (state, props) => {
  const lendData = state.investments.byId[props.route?.params?.id] || {}

  return {
    WebService: state.auth.WebService,
    lendData,
    accountRef: state.auth.account.ref,
    user: state.user.user,
  }
}

export default connect(mapStateToProps)(ConfirmDetails)
