import React from 'react'
import {View, SafeAreaView} from 'react-native'
import {connect} from 'react-redux'

import commonStyles from '../../styles/commonStyles'
import styles from './styles'
import ApprovalCard from './ApprovalCard'
import docMagnify from '../../imgs/unit21/docMagnify.png'

const PendingApproval = () => {
  return (
    <SafeAreaView style={styles.box}>
      <View style={commonStyles.tileContainer}>
        <ApprovalCard
          icon={docMagnify}
          title="Your LEND Account is now pending approval."
          subject="LEND Approval Inquiry."
          description="SALT will perform a review of the assets deposited and your terms selected. Then, we will then email you your LEND documents once everything looks good."
        />
      </View>
    </SafeAreaView>
  )
}

PendingApproval.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

export default PendingApproval
