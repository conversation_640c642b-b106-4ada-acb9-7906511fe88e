import React from 'react'
import {View, SafeAreaView} from 'react-native'

import commonStyles from '../../styles/commonStyles'
import styles from './styles'
import ApprovalCard from './ApprovalCard'
import docMagnify from '../../imgs/openLetter.png'

const AwaitingSignatures = () => {
  return (
    <SafeAreaView style={styles.box}>
      <View style={commonStyles.tileContainer}>
        <ApprovalCard
          icon={docMagnify}
          title="Please sign your LEND Account documents."
          subject="LEND Document Inquiry."
          description="SALT has completed your LEND Account review, and you have been emailed your LEND documents. Once you have completed and returned the documents back to SALT, we will review, and you will then be notified via email of your status."
        />
      </View>
    </SafeAreaView>
  )
}

AwaitingSignatures.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

export default AwaitingSignatures
