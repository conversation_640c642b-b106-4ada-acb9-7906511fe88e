import React, {useEffect, useState} from 'react'
import {StyleSheet, View, Dimensions, TouchableOpacity, Image, ScrollView, Linking} from 'react-native'
import {TextReg, TextBold, BackgroundHeader, Button} from '../../components'
import {useDispatch, useSelector} from 'react-redux'

let Proof = ({navigation, route}) => {
  let dispatch = useDispatch()
  let WebService = useSelector(state => state.auth.WebService || {})
  let {height: ScreenHeight, width: ScreenWidth} = Dimensions.get('window')

  console.log('Proof', navigation, route)

  let close = () => {
    navigation.pop(stackDepth)
  }

  let next = () => {
    console.log('next')
    navigation.navigate('Attestation1')
  }

  let link = () => {
    Linking.openURL('https://www.sec.gov/education/capitalraising/building-blocks/accredited-investor').catch(err =>
      console.error('An error occurred', err),
    )
  }

  return (
    <View style={localStyles.box}>
      <BackgroundHeader title={'Proof of being an accredited investor'} goBack={() => navigation.goBack()} />

      <ScrollView style={{...localStyles.box}}>
        <View style={{flex: 1, marginLeft: 20, marginRight: 20}}>
          <TextBold style={{fontSize: 20, marginTop: 20}}>{`LEND is only available for Accredited Investors at this time.`}</TextBold>
          <TextReg
            style={{
              marginTop: 10,
              color: '#E5705A',
            }}>{`If you are not an Accredited Investor, we will not be able to process your application.`}</TextReg>
          <TextBold style={{fontSize: 18, marginTop: 16}}>{`Accredited Investors `}</TextBold>
          <TextReg
            style={{
              marginTop: 10,
            }}>{`You must either complete SALT’s attestation form (with supporting documents) or present a 3rd Party Verification letter that demonstrates you are an Accredited Investor.`}</TextReg>
          <View
            style={{
              borderColor: '#00FFBD',
              borderWidth: 1,
              padding: 10,
              borderRadius: 6,
              backgroundColor: '#04053E',
              marginTop: 10,
              marginTop: 20,
            }}>
            <View style={{flexDirection: 'row'}}>
              <View style={{width: 50, paddingLeft: 5}}>
                <Image source={require('../../imgs/graphics/info.png')} style={{height: 30, width: 30}} />
              </View>
              <View style={{flexDirection: 'column'}}>
                <TextBold
                  style={{
                    width: ScreenWidth - 100,
                  }}>{`If you are unsure if you are an Accredited Investor, please see the definition provided at:`}</TextBold>
                <TextReg
                  style={{color: '#00FFBD', width: ScreenWidth - 110}}
                  onPress={() => link()}>{`https://www.sec.gov/education/capitalraising/building-blocks/accredited-investor`}</TextReg>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
      <View style={{alignSelf: 'stretch', marginTop: 10, marginBottom: 26, paddingLeft: 20, paddingRight: 20}}>
        <Button
          isLoading={false}
          disabled={false}
          style={{
            alignSelf: 'stretch',
          }}
          onPress={() => next()}
          theme={'secondary'}>
          <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
        </Button>
        <Button
          isLoading={false}
          disabled={false}
          style={{
            alignSelf: 'stretch',
            backgroundColor: '#28283D',
          }}
          onPress={() => navigation.goBack()}
          theme={'secondary'}>
          <TextReg style={{color: '#FFF', fontSize: 18}}>BACK</TextReg>
        </Button>
      </View>
    </View>
  )
}

export default Proof

const localStyles = StyleSheet.create({
  box: {
    flex: 1,
    backgroundColor: '#28283D',
  },
})
