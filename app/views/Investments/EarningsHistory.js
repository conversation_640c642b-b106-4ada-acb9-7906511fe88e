import React, {useEffect, useRef, useState} from 'react'
import {
  View,
  TouchableOpacity,
  ScrollView,
  Image,
  Platform,
  Dimensions,
  SafeAreaView,
  Share,
  Clipboard,
  ActivityIndicator,
  PermissionsAndroid,
} from 'react-native'
import {useSelector} from 'react-redux'
import _ from 'lodash'
import Papa from 'papaparse'
import RNFS from 'react-native-fs'

import {TextReg, TextBold, Card} from '../../components'
import styles from './styles'
import {capitalizeFirstLetter, formatCrypto, notificationFormatDate} from '../../util/helpers'
import BigNumber from 'bignumber.js'
import {useNavigation} from '@react-navigation/native'
import moment from 'moment'
import {RefreshControl} from 'react-native-gesture-handler'

const {width: screenWidth} = Dimensions.get('window')

const EarningsHistory = props => {
  const [payoutHistory, setPayoutHistory] = useState([])
  const [refreshing, setRefreshing] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [showCopiedButton, setShowCopiedButton] = useState(false)
  const [details, setDetails] = useState(null)

  let investments = useSelector(state => state.investments)
  let WebService = useSelector(state => state.auth.WebService || {})
  const investment = investments.byId[props.route?.params?.id] || {}

  const navigation = useNavigation()

  useEffect(() => {
    payout()
  }, [])

  const payout = async () => {
    setIsLoading(true)
    const earningHistory = await WebService.getPayout(investment?.id)
    setPayoutHistory(earningHistory.data)
    setIsLoading(false)
  }
  const refresh = async () => {
    setRefreshing(true)
    const earningHistory = await WebService.getPayout(investment?.id)
    setPayoutHistory(earningHistory.data)
    setRefreshing(false)
  }

  const walletType = walletType => {
    if (walletType == 'current_account_wallet') {
      return 'Account'
    }
    if (walletType == 'other_internal_wallet') {
      return 'Wallet'
    }
    if (walletType == 'external_whitelisted_wallet') {
      return 'Whitelist Wallet'
    }
  }

  const isTx = tx => {
    return tx.date !== undefined
  }

  const isOutboundTx = tx => {
    return tx.createdAt !== undefined
  }

  const sortTxs = txArray => {
    if (txArray.length) {
      if (isTx(txArray[0])) {
        return txArray.sort((a, b) => (moment(a.date).isBefore(b.date) ? 1 : -1))
      }
      if (isOutboundTx(txArray[0])) {
        return txArray.sort((a, b) => (moment(a.createdAt).isBefore(b.createdAt) ? 1 : -1))
      }
      if (!isTx(txArray[0]) && !isOutboundTx(txArray[0])) {
        return txArray
      }
    } else return txArray
  }

  const sortedPayoutHistory = sortTxs(payoutHistory)?.filter(history => +history.amount > 0)

  const requestStoragePermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          {
            title: 'Storage Permission Required',
            message: 'This app needs access to your storage to save files',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        )
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Storage permission granted')
          return true
        } else {
          console.log('Storage permission denied')
          return false
        }
      } catch (err) {
        console.warn(err)
        return false
      }
    } else {
      // No need to request permission on iOS
      return true
    }
  }

  const saveAndroid = async (data, title = 'data.csv') => {
    // Check for permission first
    const hasPermission = await requestStoragePermission()
    if (!hasPermission) {
      console.log('Permission denied')
      return
    }

    const csv = Papa.unparse(data)
    const path = `${RNFS.ExternalStorageDirectoryPath}/Documents/SALT/${title}`

    try {
      await RNFS.mkdir(`${RNFS.ExternalStorageDirectoryPath}/Documents/SALT/`)
      await RNFS.writeFile(path, csv, 'utf8')
      console.log(`File written to ${path}`)
    } catch (error) {
      console.error('Error saving or sharing CSV file', error)
    }
  }

  const saveIOS = async (data, title = 'data.csv') => {
    const csv = Papa.unparse(data)
    const path = `${RNFS.DocumentDirectoryPath}/${title}`

    try {
      await RNFS.writeFile(path, csv, 'utf8')
      console.log(`File written to ${path}`)

      // Share the file
      const shareResponse = await Share.share({
        title: title,
        url: `file://${path}`,
        type: 'text/csv',
      })

      console.log('File shared:', shareResponse)
    } catch (error) {
      console.error('Error saving or sharing CSV file', error)
    }
  }

  const down = () => {
    let downData = sortedPayoutHistory?.map(transaction => {
      return {
        Date: moment(transaction?.createdAt).format('L'),
        Amount: transaction?.amount,
        Earning: new BigNumber(transaction?.interestRate).multipliedBy('100').toFormat(),
        Reason: capitalizeFirstLetter(transaction?.reason || ''),
        'Confirmation ID': transaction?.confirmationId,
        Destination:
          transaction?.destination?.toLowerCase() == 'custody'
            ? 'WALLET'
            : transaction?.destination,
      }
    })

    let title = `Earnings-History.csv`
    if (Platform.OS === 'android') {
      saveAndroid(downData, title)
    } else {
      saveIOS(downData, title)
    }
  }

  const copyConfirmationId = confirmationId => {
    Clipboard.setString(confirmationId)
    setShowCopiedButton(true)
  }

  useEffect(() => {
    if (showCopiedButton) {
      setTimeout(() => {
        setShowCopiedButton(false)
      }, 1400)
    }
  }, [showCopiedButton])

  let scroll = useRef()

  return (
    <SafeAreaView style={styles.box}>
      <View
        style={{
          flexDirection: 'row',
          alignSelf: 'stretch',
          justifyContent: 'space-between',
          marginTop: 5,
        }}>
        <TouchableOpacity
          onPress={() => (details ? setDetails(null) : navigation?.goBack())}
          style={{height: 40, width: 40, marginLeft: 20}}>
          <Image
            source={require('../../imgs/backToSettings.png')}
            style={{height: 25, width: 20}}
          />
        </TouchableOpacity>
        <TextReg style={{color: '#FFF', fontSize: 18}}>
          {details ? 'Transaction Details' : 'Earnings History'}
        </TextReg>
        <View style={{height: 40, width: 40, marginRight: 20}} />
      </View>

      <ScrollView
        ref={ref => {
          scroll = ref
        }}
        style={{
          backgroundColor: '#28283D',
          paddingTop: 16,
          alignSelf: 'stretch',
        }}
        contentContainerStyle={{alignItems: 'center'}}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={() => refresh()}
            colors={['#28283D']}
            tintColor={'#fff'}
          />
        }>
        {details ? (
          <Card
            style={{
              borderRadius: 12,
              padding: 16,
              alignItems: 'flex-start',
            }}
            cardWidth={screenWidth - 28}>
            <View>
              <View
                style={{
                  flexDirection: 'row',
                  width: '100%',
                  justifyContent: 'space-between',
                  paddingVertical: 4,
                }}>
                <TextReg style={{fontSize: 16}}>Date</TextReg>
                <TextReg style={{fontSize: 16}}>{notificationFormatDate(details.payoutAt)}</TextReg>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  width: '100%',
                  justifyContent: 'space-between',
                  paddingVertical: 4,
                }}>
                <TextReg style={{fontSize: 16}}>Destination</TextReg>
                <TextReg style={{fontSize: 16, color: '#00FFBD'}}>
                  {details.destination?.toLowerCase() == 'custody'
                    ? 'WALLET'
                    : details.destination.toUpperCase()}
                </TextReg>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  width: '100%',
                  justifyContent: 'space-between',
                  paddingVertical: 4,
                }}>
                <TextReg style={{fontSize: 16}}>Amount</TextReg>
                <TextReg style={{fontSize: 16}}>
                  {formatCrypto(details.amount, investment.investmentAsset)}{' '}
                  {investment.investmentAsset}
                </TextReg>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  width: '100%',
                  justifyContent: 'space-between',
                  paddingVertical: 4,
                }}>
                <TextReg style={{fontSize: 16}}>Earnings Rate</TextReg>
                <TextReg style={{fontSize: 16}}>
                  {new BigNumber(details.interestRate).times(100).toString()}%
                </TextReg>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  width: '100%',
                  justifyContent: 'space-between',
                  paddingTop: 4,
                  paddingBottom: 20,
                }}>
                <TextReg style={{fontSize: 16}}>Reason</TextReg>
                <TextReg style={{fontSize: 16}}>{capitalizeFirstLetter(details.reason)}</TextReg>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  width: '100%',
                  justifyContent: 'space-between',
                  paddingTop: 16,
                  borderTopColor: '#6e7c6e',
                  borderTopWidth: 0.5,
                }}>
                <TextReg style={{fontSize: 16}}>Confirmation ID</TextReg>
                <View
                  style={{
                    flexDirection: 'row',
                    gap: 8,
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}>
                  <TextBold style={{fontSize: 16, letterSpacing: 1.92, color: '#00FFBD'}}>
                    COPY
                  </TextBold>
                  {showCopiedButton ? (
                    <Image
                      style={{height: 16, width: 16}}
                      source={require('../../imgs/checkmark.png')}
                    />
                  ) : (
                    <TouchableOpacity onPress={() => copyConfirmationId(details.confirmationId)}>
                      <Image
                        style={{height: 16, width: 16, tintColor: '#00FFBD'}}
                        source={require('../../imgs/referrals/copy.png')}
                      />
                    </TouchableOpacity>
                  )}
                </View>
              </View>
              <TextReg style={{fontSize: 14, color: '#AFAFAF'}}>{details.confirmationId}</TextReg>
            </View>
          </Card>
        ) : isLoading ? (
          <ActivityIndicator size="large" color="#fff" style={{marginBottom: 10}} />
        ) : sortedPayoutHistory?.length > 0 ? (
          <Card
            style={{
              borderRadius: 12,
              padding: 16,
              alignItems: 'flex-start',
            }}
            cardWidth={screenWidth - 28}>
            <View
              style={{
                flexDirection: 'row',
                width: '100%',
                justifyContent: 'flex-start', //space-between
                alignItems: 'center',
                marginBottom: 10,
              }}>
              <TextBold style={{fontSize: 16}}>Earnings History</TextBold>
              {false && (
                <TouchableOpacity style={{padding: 8}} onPress={() => down()}>
                  <Image
                    source={require('../../imgs/icons/cloud.png')}
                    style={{height: 28, width: 28}}
                  />
                </TouchableOpacity>
              )}
            </View>
            {sortedPayoutHistory.length > 0 &&
              sortedPayoutHistory.map(history => {
                console.log('history', history)
                return (
                  <>
                    <View
                      style={{
                        height: 0.5,
                        backgroundColor: '#FFF',
                        width: '100%',
                        marginVertical: 10,
                      }}
                    />
                    <TouchableOpacity onPress={() => setDetails(history)}>
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '100%',
                          justifyContent: 'space-between',
                        }}>
                        <View>
                          <TextReg style={{fontSize: 16}}>
                            Earnings at{' '}
                            <TextReg style={{color: '#00FFBD'}}>
                              {new BigNumber(history.interestRate).times(100).toString()}%
                            </TextReg>
                          </TextReg>
                          <TextReg style={{fontSize: 12, marginTop: 3}}>
                            {notificationFormatDate(history.payoutAt)}
                          </TextReg>
                        </View>
                        <View style={{alignItems: 'flex-end'}}>
                          <TextBold style={{fontSize: 16}}>
                            {formatCrypto(history.amount, investment.investmentAsset)}{' '}
                            {investment.investmentAsset}
                          </TextBold>
                          <TextReg style={{fontSize: 12, marginTop: 5, color: '#00FFBD'}}>
                            {capitalizeFirstLetter(
                              history.destination.toLowerCase() == 'custody'
                                ? 'WALLET'
                                : history?.destination?.toLowerCase(),
                            )}
                          </TextReg>
                        </View>
                      </View>
                    </TouchableOpacity>
                  </>
                )
              })}
          </Card>
        ) : (
          <TextReg style={{color: '#FFF', fontSize: 16, marginTop: 20}}>
            No Earnings History
          </TextReg>
        )}
      </ScrollView>
    </SafeAreaView>
  )
}

EarningsHistory.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

export default EarningsHistory
