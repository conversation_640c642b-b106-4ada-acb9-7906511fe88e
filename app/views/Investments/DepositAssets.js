import React, {Component} from 'react'
import {
  View,
  Image,
  TouchableOpacity,
  Dimensions,
  Clipboard,
  ScrollView,
  RefreshControl,
  SafeAreaView,
} from 'react-native'
import {connect} from 'react-redux'
import LottieView from 'lottie-react-native'
import QRCode from 'react-native-qrcode-svg'
import BigNumber from 'bignumber.js'

import {Card, TextReg, TextBold, Button} from '../../components'
import {formatCrypto, numberWithCommas} from '../../util/helpers'
import styles from './styles'
import {getTokenPic} from '../../util/tokens'
import {
  increaseRefreshDataCount,
  updateAccountPlus,
  updateAllWallets,
} from '../../store/user/user.actions'
import {
  clearCancelledInvestmentRequest,
  getInvestments,
} from '../../store/investments/investments.actions'

const {height: ScreenHeight, width: ScreenWidth} = Dimensions.get('window')

class DepositAssets extends Component {
  constructor(props) {
    super(props)

    this.state = {
      barWidth: 0,
      creatingWallet: false,
      wallet: null,
      cancelling: false,
      startAnimation: false,
    }
    this.inputs = {}
    this.polling = null
  }

  componentDidMount() {
    this.startWalletsPolling()
    this.checkForWallet()
  }

  // removed animation for now
  // componentDidUpdate() {
  //   let {user, lendData, accountRef} = this.props
  //   const totalValue = lendData.startingAmount
  //   let productRef = accountRef
  //   let deposits = user?.allWallets[productRef - 1]?.filter(
  //     w => w.currency == lendData?.investmentAsset,
  //   )

  //   const alreadyDeposited =
  //     deposits?.reduce(
  //       (sum, collateral) => parseFloat(sum) + parseFloat(collateral.projectedBalance),
  //       0,
  //     ) || 0
  //   let totalRemaining = totalValue - alreadyDeposited

  //   if (totalRemaining < 0 && !this.state.startAnimation) {
  //     this.setState({startAnimation: true})
  //   }
  // }

  componentWillUnmount() {
    clearInterval(this.polling)
  }

  startWalletsPolling = () => {
    this.polling = setInterval(this.getWalletData, 30000)
  }

  loadingCreateWallet = trueFalse => {
    this.setState({creatingWallet: trueFalse})
  }

  checkForWallet = () =>
    new Promise((resolve, reject) => {
      const hasWallet = this.props.lendData?.payoutWalletId
      const {verification, investmentAsset} = this.props.lendData

      let verified = false
      if (
        verification?.photoIdVerificationStatus == 'passed' ||
        verification?.photoIdVerificationStatus == 'completed_prior'
      ) {
        verified = true
      }
      if (verification?.identityVerificationStatus == 'passed') {
        verified = true
      }
      //if doesnt have a wallet
      if (!hasWallet || !verified) {
        // create the wallet

        // if user has not finished signup
        if (verified) {
          this.loadingCreateWallet(true)
          this.props.WebService.createWallet(investmentAsset)
            .then(res => {
              this.props.dispatch(getInvestments())
              this.loadingCreateWallet(false)
              this.getWalletData()
              resolve()
            })
            .catch(err => {
              this.setState({creatingWallet: false})
              reject()
            })
        } else {
          //show Needs to verify ID
          resolve('idVerify')
        }
      } else {
        resolve()
      }
    })

  copyAddress = address => {
    Clipboard.setString(address)
  }

  getBarWidth = layout => {
    const {x, y, width, height} = layout
    this.setState({barWidth: width})
  }

  continue = async () => {
    await this.props.dispatch(getInvestments())
    await this.props.dispatch(increaseRefreshDataCount())
    this.props.navigation.navigate('ConfirmDetails', {id: this.props.lendData.id})
  }

  handleCancelRequest = async () => {
    this.setState({cancelling: true})
    try {
      await this.props.WebService.cancelInvestmentRequest(this.props.lendData?.id)
      await this.props.dispatch(increaseRefreshDataCount())
      await this.props.dispatch(getInvestments())
      await this.props.dispatch(clearCancelledInvestmentRequest(this.props.lendData?.id))
      this.props.navigation.navigate('CreateInvestment')
    } catch (err) {
      throw err
    }
    this.setState({cancelling: false})
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    this.props.navigation.pop(stackDepth)
  }

  getWalletData = async () => {
    this.setState({refreshing: true})
    let accountArr = this.props.user?.accounts || []
    let walletsRes = []

    await Promise.all(
      accountArr.map(async a => {
        await this.props.WebService.getWallets(a.ref).then(res => {
          walletsRes[a.ref - 1] = res.data
          return true
        })
      }),
    )
    this.props.dispatch(updateAllWallets(walletsRes))
    this.setState({refreshing: false})
  }

  goToTwoFactorSetup = () => {
    this.props.navigation.navigate('TwoFactor')
  }

  render() {
    let {user, lendData, accountRef} = this.props
    const totalValue = lendData.startingAmount
    const showTotalValue = formatCrypto(totalValue, lendData?.investmentAsset)
    let productRef = accountRef
    let deposits = user?.allWallets[productRef - 1]?.filter(
      w => w.currency == lendData?.investmentAsset,
    )

    const collateralTotalWithSalt =
      deposits?.reduce(
        (sum, collateral) => parseFloat(sum) + parseFloat(collateral.projectedBalance),
        0,
      ) || 0
    const displayDeposited = formatCrypto(collateralTotalWithSalt, lendData?.investmentAsset)
    let totalRemaining = totalValue - collateralTotalWithSalt
    if (totalRemaining < 0) {
      totalRemaining = 0
    }
    const showTotalRemaining = formatCrypto(totalRemaining, lendData?.investmentAsset)
    const barFillPercent = collateralTotalWithSalt / totalValue

    const barFill = (this.state.barWidth / 100) * (barFillPercent * 100)
    const showDeposit = () => {
      if (
        lendData?.investmentAsset == 'XRP' ||
        lendData?.investmentAsset == 'DOGE' ||
        lendData?.investmentAsset == 'DASH' ||
        lendData?.investmentAsset == 'PAXG'
      ) {
        return
      }
      const wallet = user?.allWallets[productRef - 1]?.find(
        w => w.currency == lendData?.investmentAsset,
      )
      const showImg = getTokenPic(lendData?.investmentAsset)
      const priceTicker = `${lendData?.investmentAsset}-USD`
      const price = this.props.tokenPrices[priceTicker].price
      const remainingAmount = formatCrypto(totalRemaining, lendData?.investmentAsset)

      return !this.props.user?.mfaEnabled ? (
        <View
          style={{
            marginTop: 14,
            alignSelf: 'stretch',
            alignItems: 'center',
          }}>
          <View
            style={{
              alignSelf: 'stretch',
              paddingTop: 18,
              alignItems: 'center',
            }}>
            <TextReg
              style={{
                fontSize: 16,
                textAlign: 'center',
                marginBottom: 14,
                width: 280,
                color: '#FFF',
              }}>
              Currently your account doesnt not have 2FA enabled. Please enable 2FA to deposit
              assets.
            </TextReg>
            <Button onPress={() => this.goToTwoFactorSetup()}>SETUP 2FA</Button>
          </View>
        </View>
      ) : (
        <>
          <TextReg
            style={{
              width: ScreenWidth - 100,
              textAlign: 'center',
              paddingTop: 25,
              paddingBottom: 25,
              fontSize: 16,
            }}>
            To deposit {remainingAmount} {lendData?.investmentAsset}, please send it to the
            following address:
          </TextReg>
          <View
            marginTop={-4}
            style={{
              backgroundColor: 'transparent',
              borderColor: '#FFF',
              borderWidth: 1,
              borderRadius: 8,
              width: ScreenWidth - 28,
            }}>
            <View style={{marginTop: 14, alignItems: 'center'}}>
              <View
                style={{
                  height: 160,
                  width: '100%',
                  borderRadius: 14,
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  marginBottom: 16,
                  flexDirection: 'row',
                  padding: 16,
                  flex: 1,
                }}>
                <View style={{paddingLeft: 10, alignItems: 'center'}}>
                  <TextReg
                    style={{
                      fontSize: 24,
                      color: '#FFF',
                    }}>
                    {wallet?.currency}
                  </TextReg>
                  <Image style={{height: 94, width: 94}} source={showImg} />
                </View>
                <Image
                  source={require('../../imgs/rightArrow.png')}
                  style={{
                    height: 22,
                    width: 19,
                    marginRight: 4,
                    marginTop: 2,
                  }}
                />
                <View style={styles.depositQrBox}>
                  {wallet?.address ? (
                    <QRCode
                      value={wallet.address}
                      size={158}
                      backgroundColor={'#FFF'}
                      color={'#28283D'}
                    />
                  ) : (
                    <View
                      style={{
                        height: 158,
                        width: 158,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <LottieView
                        ref={animation => {
                          this.animation = animation
                        }}
                        style={{...styles.whiteDotsAnimationWallet, marginLeft: 0}}
                        source={require('../../imgs/lotti/loading-white-dots.json')}
                        autoPlay
                      />
                    </View>
                  )}
                </View>
              </View>
            </View>
          </View>
          <View
            style={{
              width: ScreenWidth - 28,
              alignItems: 'center',
              marginHorizontal: 14,
            }}>
            {wallet?.address && (
              <>
                <TextBold
                  style={{
                    textAlign: 'center',
                    flex: 1,
                    flexWrap: 'wrap',
                    fontSize: 16,
                    marginVertical: 25,
                    width: ScreenWidth - 80,
                  }}>
                  {wallet?.address}
                </TextBold>
                <Button
                  style={{
                    alignSelf: 'stretch',
                    marginTop: 0,
                  }}
                  onPress={() => this.copyAddress(wallet?.address)}>
                  <TextReg style={{color: '#000', fontSize: 18, letterSpacing: 2}}>
                    COPY ADDRESS
                  </TextReg>
                </Button>
              </>
            )}
          </View>
        </>
      )
    }

    return (
      <SafeAreaView style={styles.box}>
        <View
          style={{
            flexDirection: 'row',
            alignSelf: 'stretch',
            justifyContent: 'space-between',
            marginTop: 5,
            marginBottom: 5,
          }}>
          <TouchableOpacity
            onPress={() => this.props.navigation?.navigate('Home')}
            style={{height: 40, width: 40, marginLeft: 20}}>
            <Image
              source={require('../../imgs/backToSettings.png')}
              style={{height: 25, width: 20}}
            />
          </TouchableOpacity>
          <TextReg style={{color: '#FFF', fontSize: 18}}>{'Open LEND Account'}</TextReg>
          <View style={{height: 40, width: 40, marginRight: 20}} />
        </View>
        <View
          style={{
            backgroundColor: '#28283D',
            height: ScreenHeight,
            alignSelf: 'stretch',
          }}>
          <ScrollView
            style={{
              alignSelf: 'stretch',
              marginBottom: 120,
            }}
            contentContainerStyle={{
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
            refreshControl={
              <RefreshControl
                refreshing={this.state.refreshing}
                onRefresh={this.getWalletData}
                colors={['#28283D']}
                tintColor={'#fff'}
              />
            }>
            <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
              <View
                style={{
                  alignSelf: 'stretch',
                  alignItems: 'flex-start',
                  marginHorizontal: 14,
                  marginVertical: 24,
                }}>
                <TextBold
                  style={{
                    color: '#FFF',
                    fontSize: 24,
                    marginBottom: 24,
                    fontWeight: 700,
                  }}>
                  Deposit Assets
                </TextBold>
                <TextReg
                  style={{
                    color: '#FFF',
                    fontSize: 16,
                  }}>
                  Deposit {lendData?.investmentAsset} to your{' '}
                  <TextBold>{lendData?.account?.name}</TextBold> LEND Account to start earning. Feel
                  free to deposit additional assets if you’d like.
                </TextReg>
              </View>

              <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
                <Card
                  cardMarginBottom={18}
                  style={{backgroundColor: '#48485A', position: 'relative'}}>
                  {/* removed animation for now
                  {this.state.startAnimation && (
                    <LottieView
                      ref={animation => {
                        this.animation = animation
                      }}
                      style={{
                        width: 140,
                        height: 140,
                        marginTop: 10,
                        position: 'absolute',
                        top: -20,
                        right: 80,
                        zIndex: 999,
                      }}
                      source={require('../../imgs/lotti/confetti-animation.json')}
                      autoPlay
                    />
                  )} */}
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignSelf: 'stretch',
                      marginBottom: 18,
                      marginTop: 2,
                      marginLeft: 4,
                      marginRight: 4,
                    }}>
                    <View>
                      <TextReg style={{marginBottom: 4, color: '#FFF'}}>Already Deposited</TextReg>
                      <TextBold
                        style={{
                          fontSize: 17,
                          color: '#FFF',
                        }}>
                        {displayDeposited}
                      </TextBold>
                    </View>
                    <View style={{alignItems: 'flex-end'}}>
                      <TextReg style={{marginBottom: 4, color: '#FFF'}}>Additional Needed</TextReg>
                      <TextBold
                        style={{
                          fontSize: 17,
                          color: '#FFF',
                        }}>
                        {showTotalRemaining}
                      </TextBold>
                    </View>
                  </View>
                  <View style={{alignSelf: 'stretch', position: 'relative'}}>
                    <View
                      onLayout={event => {
                        this.getBarWidth(event.nativeEvent.layout)
                      }}
                      style={{
                        alignSelf: 'stretch',
                        height: 30,
                        backgroundColor: '#e8e8e8',
                        borderRadius: 14,
                        marginLeft: 4,
                        marginRight: 4,
                        marginBottom: 6,
                        overflow: 'hidden',
                      }}>
                      <View
                        style={{
                          backgroundColor: '#00FFBD',
                          width: barFill || 0,
                          height: 30,
                          zIndex: 20,
                          position: 'absolute',
                        }}
                      />
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      marginLeft: 4,
                      marginRight: 4,
                      alignSelf: 'stretch',
                      justifyContent: 'space-between',
                    }}>
                    <TextReg style={{fontSize: 17, color: '#9b9b9b'}}>0</TextReg>
                    <TextReg
                      style={{
                        fontSize: 17,
                        color: '#9b9b9b',
                      }}>
                      {showTotalValue}
                    </TextReg>
                  </View>
                </Card>
                {totalRemaining !== 0 && showDeposit()}
                <View style={{width: ScreenWidth - 28, marginVertical: 40, alignItems: 'center'}}>
                  <TextReg style={{fontSize: 16, color: '#E5705A', textAlign: 'center'}}>
                    Please note: Sending anything other than {lendData?.investmentAsset} to this
                    address may result in a loss of funds.
                  </TextReg>
                  <TextReg
                    style={{fontSize: 16, color: '#E5705A', textAlign: 'center', paddingTop: 25}}>
                    Warning: This address was generated for this specific payment. Once completed,
                    do not send future payments to this address. Any additional payments sent to
                    this address will result in a delay in accounting and recognition in our system.
                  </TextReg>
                </View>
              </View>
            </View>
            <Button
              disabled={totalRemaining > 0}
              style={{
                alignSelf: 'stretch',
                marginTop: 20,
                marginHorizontal: 14,
                height: 60,
              }}
              onPress={() => this.continue()}>
              <TextReg style={{color: '#000', fontSize: 18, letterSpacing: 2}}>CONTINUE</TextReg>
            </Button>
            <Button
              style={{
                alignSelf: 'stretch',
                marginBottom: 80,
                marginHorizontal: 14,
                backgroundColor: 'transparent',
                height: 60,
              }}
              isLoading={this.state.cancelling}
              onPress={() => this.handleCancelRequest()}>
              <TextReg style={{color: '#00FFBD', fontSize: 18, letterSpacing: 2}}>
                CANCEL LEND REQUEST
              </TextReg>
            </Button>
          </ScrollView>
        </View>
      </SafeAreaView>
    )
  }
}

DepositAssets.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
  gesturesEnabled: false,
})

const mapStateToProps = (state, props) => {
  const lendData = state.investments.byId[props.route?.params?.id] || {}
  return {
    accountRef: state.auth.account.ref,
    lendData,
    tokenPrices: state.user.prices,
    WebService: state.auth.WebService,
    user: state.user.user,
    launchDarkly: state.launchDarkly,
  }
}

export default connect(mapStateToProps)(DepositAssets)
