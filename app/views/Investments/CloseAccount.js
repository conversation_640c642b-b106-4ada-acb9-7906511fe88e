import React, {Component} from 'react'
import {View, TouchableOpacity, ScrollView, Image, Dimensions, SafeAreaView} from 'react-native'
import BigNumber from 'bignumber.js'

import {TextReg, TextBold, Button} from '../../components'
import styles from './styles'
import {formatCrypto} from '../../util/helpers'
import {connect} from 'react-redux'
import {getInvestments} from '../../store/investments/investments.actions'
import {increaseRefreshDataCount} from '../../store/user/user.actions'

const {width: screenWidth} = Dimensions.get('window')

class CloseAccount extends Component {
  constructor(props) {
    super(props)

    this.state = {
      isClosing: false,
    }
  }

  closeAccount = async () => {
    try {
      const {investment} = this.props
      this.setState({isClosing: true})
      await this.props.WebService.requestClosure(investment.id)
      await this.props.dispatch(getInvestments())
      await this.props.dispatch(increaseRefreshDataCount())
      this.setState({isClosing: false})
      this.props.navigation.navigate('LendDashboard', {id: investment.id})
    } catch (err) {
      console.log('closeAccount err', err)
    }
  }

  render() {
    const {investment} = this.props
    const {isClosing} = this.state
    const assetAmount = investment?.startingAmount
    const withdrawalFee = new BigNumber(investment.earlyClosingRate)

    return (
      <SafeAreaView style={styles.box}>
        <View
          style={{
            flexDirection: 'row',
            alignSelf: 'stretch',
            justifyContent: 'space-between',
            marginTop: 5,
          }}>
          <TouchableOpacity
            onPress={() => this.props.navigation?.goBack()}
            style={{height: 40, width: 40, marginLeft: 20}}>
            <Image
              source={require('../../imgs/backToSettings.png')}
              style={{height: 25, width: 20}}
            />
          </TouchableOpacity>
          <TextReg style={{color: '#FFF', fontSize: 18}}>Close Account</TextReg>
          <View style={{height: 40, width: 40, marginRight: 20}} />
        </View>
        <ScrollView
          ref={ref => {
            scroll = ref
          }}
          style={{
            backgroundColor: '#28283D',
            alignSelf: 'stretch',
          }}
          contentContainerStyle={{alignItems: 'center'}}>
          <View
            style={{
              marginTop: 8,
              alignItems: 'center',
              width: screenWidth - 32,
              justifyContent: 'center',
            }}>
            <Image source={require('../../imgs/walletClose.png')} style={{width: 84, height: 84}} />
            <TextBold style={{fontSize: 24, marginBottom: 60, marginTop: 28}}>
              Close Your LEND Account
            </TextBold>
            <View
              style={{
                marginBottom: 12,
                flexDirection: 'row',
                width: '92%',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <TextReg style={{fontSize: 16}}>
                {formatCrypto(new BigNumber(assetAmount), investment?.investmentAsset)}{' '}
                {investment?.investmentAsset}
              </TextReg>
              <TextReg style={{fontSize: 16}}>Asset Amount</TextReg>
            </View>
            <View
              style={{
                paddingBottom: 12,
                flexDirection: 'row',
                width: '92%',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <TextReg style={{fontSize: 16}}>
                -{' '}
                {formatCrypto(
                  new BigNumber(assetAmount).multipliedBy(new BigNumber(withdrawalFee)),
                  investment?.investmentAsset,
                )}{' '}
                {investment?.investmentAsset}
              </TextReg>
              <TextReg style={{fontSize: 16}}>
                Withdrawal Fee (
                {formatCrypto(
                  new BigNumber(withdrawalFee).multipliedBy(100),
                  investment?.investmentAsset,
                )}
                %)
              </TextReg>
            </View>
            <View
              style={{
                marginBottom: 12,
                flexDirection: 'row',
                width: '92%',
                justifyContent: 'space-between',
                alignItems: 'center',
                borderTopColor: '#FFF',
                borderTopWidth: 0.5,
                paddingTop: 12,
              }}>
              <TextBold style={{fontSize: 16}}>
                ={' '}
                {formatCrypto(
                  new BigNumber(assetAmount).minus(
                    new BigNumber(assetAmount).multipliedBy(new BigNumber(withdrawalFee)),
                  ),
                  investment?.investmentAsset,
                )}{' '}
                {investment?.investmentAsset}
              </TextBold>
              <TextBold style={{fontSize: 16, textAlign: 'right'}}>
                Net Withdrawal{'\n'}Amount
              </TextBold>
            </View>
            <View style={{marginTop: 28}}>
              <TextReg
                style={{
                  color: '#FFF',
                  fontSize: 10,
                  flexShrink: 1,
                  flexWrap: 'wrap',
                  width: screenWidth - 50,
                }}>
                By clicking “CLOSE ACCOUNT” below, you are authorizing SALT Lending to deduct the
                Withdrawal Fee from your LEND Account balance and deposit it into SALT’s treasury
                wallet address. Then, SALT will move the Balance Remaining for Withdrawal amount to
                your wallet on the SALT platform.
              </TextReg>
              <TextBold
                style={{
                  color: '#00FFBD',
                  fontSize: 12,
                  flexShrink: 1,
                  flexWrap: 'wrap',
                  marginTop: 14,
                }}>
                NOTE: This request could take 5-7 business days to complete.
              </TextBold>
            </View>
          </View>
          <View
            style={{
              alignItems: 'center',
              marginBottom: 20,
              marginTop: 60,
              alignSelf: 'stretch',
            }}>
            <Button
              style={{alignSelf: 'stretch', marginLeft: 20, marginRight: 20}}
              isLoading={isClosing}
              onPress={() => this.closeAccount()}>
              <TextReg
                style={{
                  fontSize: 18,
                  letterSpacing: 0.7,
                  color: '#000',
                  alignSelf: 'stretch',
                }}>
                CLOSE ACCOUNT
              </TextReg>
            </Button>
            <Button
              style={{
                alignSelf: 'stretch',
                marginBottom: 80,
                marginHorizontal: 14,
                backgroundColor: 'transparent',
                height: 60,
              }}
              onPress={() => this.props.navigation.goBack()}>
              <TextReg style={{color: '#00FFBD', fontSize: 18, letterSpacing: 2}}>CANCEL</TextReg>
            </Button>
          </View>
        </ScrollView>
      </SafeAreaView>
    )
  }
}

CloseAccount.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

const mapStateToProps = (state, props) => {
  const investment = state.investments.byId[props.route?.params?.id] || {}

  return {
    WebService: state.auth.WebService,
    investment,
  }
}

export default connect(mapStateToProps)(CloseAccount)
