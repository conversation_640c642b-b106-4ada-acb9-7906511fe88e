import React, {useEffect, useRef, useState} from 'react'
import {
  View,
  TouchableOpacity,
  ScrollView,
  Image,
  Platform,
  Dimensions,
  SafeAreaView,
  ImageBackground,
  Linking,
  Pressable,
} from 'react-native'
import {connect, useDispatch, useSelector} from 'react-redux'
import {AnimatedCircularProgress} from 'react-native-circular-progress'
import {Svg, Circle, Polygon} from 'react-native-svg'
import _ from 'lodash'
import BigNumber from 'bignumber.js'
import {useNavigation} from '@react-navigation/native'
import moment from 'moment'
import {RefreshControl} from 'react-native-gesture-handler'

import {TextReg, TextBold, Card, Button} from '../../components'
import {
  calculateMaturityDate,
  formatCrypto,
  isMaturityDate,
  isMonthAndDayLessThanCurrent,
  isMonthCurrent,
  numberWithCommas,
} from '../../util/helpers'
import {sendEvent} from '../../store/analytics/analytics.actions'
import {cryptoMeta} from '../../util/enumerables'
import ProjectedBar from './ProjectedBar'
import {getInvestments} from '../../store/investments/investments.actions'

import commonStyles from '../../styles/commonStyles'
import styles from './styles'

const {width: screenWidth, height: screenHeight} = Dimensions.get('window')

const LendDashboard = props => {
  const [investData, setInvestData] = useState({})
  const [earningPaid, setEarningPaid] = useState(0)
  const [projectEarning, setProjectEarning] = useState(0)
  const [terminationTooltip, setTerminationTooltip] = useState(false)
  const [refreshing, setRefreshing] = useState(false)
  const [boxOpen, setBoxOpen] = useState()
  const [totalBalanceEarningPaid, setTotalBalanceEarningPaid] = useState(0)

  let allInvestments = useSelector(state => state.investments)
  let WebService = useSelector(state => state.auth.WebService || {})
  const investment = allInvestments.byId[props.route?.params?.id] || {}
  const dispatch = useDispatch()
  const navigation = useNavigation()

  const {user, accountRef, launchDarkly, tokenPrices} = props
  const showLendGraphs = launchDarkly['mobileLendGraphs'] || false

  useEffect(() => {
    let eventName = 'LendRequest-Start-Android'
    if (Platform.OS === 'ios') {
      eventName = 'LendRequest-Start-iOS'
    }
    dispatch(sendEvent(eventName))
  }, [dispatch])

  useEffect(() => {
    getAllData()
  }, [])

  const getAllData = async () => {
    await dispatch(getInvestments())
    setRefreshing(true)
    const invest = user?.allWallets[accountRef - 1]?.find(
      w => w.currency == investment?.investmentAsset,
    )

    calculateProjectEarning()
    setInvestData(invest)
    setRefreshing(false)
  }

  const payoutSchedule = investment?.upcomingProjectedEarnings
    ? investment?.upcomingProjectedEarnings?.filter((_, i) => i <= +investment.term)
    : []

  const projectHistory = investment?.payoutHistory
    ? investment?.payoutHistory?.sort(
        (a, b) => new Date(a.payoutAt).getTime() - new Date(b.payoutAt).getTime(),
      )
    : []

  const sumPayoutHistory = data => {
    let cumulativeSum = 0
    const dateSums = {}
    data.map(item => {
      const currentDate = new Date(item.payoutAt).toISOString().split('T')[0]
      // If the date is already present, update the accumulated amount
      if (dateSums[currentDate]) {
        dateSums[currentDate] += parseFloat(item.amount)
      } else {
        dateSums[currentDate] = parseFloat(item.amount)
      }

      // Update cumulative sum
      cumulativeSum += parseFloat(item.amount)
      dateSums[currentDate] = cumulativeSum
    })
    return dateSums
  }

  function fillMissingMonths(data) {
    const result = [...data] // Initialize result with existing data
    const firstDate = moment(data[0]?.payoutAt)
    const lastDate = moment(data[data.length - 1]?.payoutAt)

    const currentDate = firstDate.clone().startOf('month')

    // Iterate month by month, from firstDate to lastDate
    while (currentDate.isBefore(lastDate) || currentDate.isSame(lastDate)) {
      // Check if there's data for the current month
      const hasDataForMonth = data.some(item => moment(item.payoutAt).isSame(currentDate, 'month'))

      if (!hasDataForMonth) {
        // Find the closest previous data entry
        const closestData = data.filter(item => moment(item.payoutAt).isBefore(currentDate)).pop() // Get the last item from the filtered list (most recent)

        // Check if we found a closest entry
        if (closestData) {
          const newItem = {
            payoutAt: currentDate.clone().format('YYYY-MM-DD'), // Set date to the first day of the missing month
            amount: closestData.amount, // Use the amount from the closest entry
          }
          result.push(newItem) // Add new item to result
        }
      }

      // Move to the next month
      currentDate.add(1, 'month')
    }

    // Sort the final result by createdAt before returning
    result.sort((a, b) => moment(a.payoutAt).diff(moment(b.payoutAt)))

    return result
  }

  function convertToArrayOfObjects(data) {
    return Object.entries(data).map(([date, value]) => ({
      payoutAt: date,
      amount: value,
    }))
  }

  const cummulativePayoutHistory = sumPayoutHistory(projectHistory)
  const restuctObject = convertToArrayOfObjects(cummulativePayoutHistory)
  const filledMissingMonths = fillMissingMonths(restuctObject)

  const payoutHistoryWithSum = filledMissingMonths.map(e => {
    return {
      payoutAt: e.payoutAt,
      amount: e.amount,
    }
  })

  const projectedChart = [...payoutHistoryWithSum, ...payoutSchedule]?.map((item, i) => {
    const date = item?.payoutDate ? item?.payoutDate : item?.payoutAt
    if (
      isMonthAndDayLessThanCurrent(date) &&
      isMonthAndDayLessThanCurrent(
        [...payoutHistoryWithSum, ...payoutSchedule][i + 1]?.payoutDate
          ? [...payoutHistoryWithSum, ...payoutSchedule][i + 1]?.payoutDate
          : [...payoutHistoryWithSum, ...payoutSchedule][i + 1]?.payoutAt,
      )
    ) {
      return {
        date: item.payoutDate
          ? new Date(item.payoutDate).getTime()
          : new Date(item.payoutAt).getTime(),
        'Received Earnings': item?.totalPayoutEarned ? item.totalPayoutEarned : item.amount,
        'Projected Earnings': null,
        amount: item?.totalPayoutEarned ? item.totalPayoutEarned : item.amount,
        currency: investment?.investmentAsset,
        asset: investment?.investmentAsset,
        isProjected: false,
      }
    }
    if (!isMonthAndDayLessThanCurrent(date) && !isMonthCurrent(date)) {
      return {
        date: item.payoutDate
          ? new Date(item.payoutDate).getTime()
          : new Date(item.payoutAt).getTime(),
        'Received Earnings': null,
        'Projected Earnings': item?.totalPayoutEarned ? item.totalPayoutEarned : item.amount,
        amount: item?.totalPayoutEarned ? item.totalPayoutEarned : item.amount,
        currency: investment?.investmentAsset,

        asset: investment?.investmentAsset,
        isProjected: true,
      }
    }
    if (
      !isMonthAndDayLessThanCurrent(
        [...payoutHistoryWithSum, ...payoutSchedule][i + 1]?.payoutDate
          ? [...payoutHistoryWithSum, ...payoutSchedule][i + 1]?.payoutDate
          : [...payoutHistoryWithSum, ...payoutSchedule][i + 1]?.payoutAt,
      ) &&
      (isMonthCurrent(date) || isMonthAndDayLessThanCurrent(date))
    ) {
      return {
        date: item?.payoutDate
          ? new Date(item.payoutDate).getTime()
          : new Date(item.payoutAt).getTime(),
        'Received Earnings': item.totalPayoutEarned ? item.totalPayoutEarned : item.amount,
        'Projected Earnings': item.totalPayoutEarned ? item.totalPayoutEarned : item.amount,
        amount: item.totalPayoutEarned ? item.totalPayoutEarned : item.amount,
        currency: investment?.investmentAsset,
        asset: investment?.investmentAsset,
        isProjected: false,
      }
    }
  })

  const getPayoutEarning = async () => {
    const earningsPaid = await projectHistory.reduce((acc, curr, i) => {
      return acc + +curr.amount
    }, 0)
    setEarningPaid(earningsPaid)
  }

  const getTotalBalanceEarning = async () => {
    const earningsPaid = await projectHistory.reduce((acc, curr, i) => {
      if (curr.payoutWalletType == 'current_account_wallet') {
        return acc + +curr.amount
      } else {
        return acc + 0
      }
    }, 0)
    setTotalBalanceEarningPaid(earningsPaid)
  }

  useEffect(() => {
    getTotalBalanceEarning()
    getPayoutEarning()
  }, [projectHistory])

  const payoutGraphData = [
    {
      'Received Earnings': 0,
      'Projected Earnings': 0,
      isProjected: false,
      date: new Date(moment(projectedChart[0]?.date).subtract(1, 'months')).getTime(),
    },
  ]
  projectedChart?.map((curr, i) => {
    const graphData = {
      'Received Earnings': Number(curr['Received Earnings'])
        ? Number(curr['Received Earnings'])
        : null,
      'Projected Earnings': Number(curr['Projected Earnings'])
        ? curr['Received Earnings'] !== null && projectedChart[i + 1]['Received Earnings'] === null
          ? Number(curr['Received Earnings'])
          : Number(curr['Projected Earnings'])
        : null,
      isProjected: curr.isProjected,
      date: curr.date,
    }

    payoutGraphData.push(graphData)
  })

  const changePoint =
    payoutGraphData.findIndex(data => data.isProjected) === -1
      ? 0
      : payoutGraphData.findIndex(data => data.isProjected) - 1

  const graphData = {
    prices: payoutGraphData.map(g => [
      g['Received Earnings'] ? g['Received Earnings'] : g['Projected Earnings'],
      g.date,
    ]),
    changePoint: payoutGraphData[changePoint]?.date,
  }

  const calculateProjectEarning = () => {
    const projectEarning = payoutGraphData[payoutGraphData.length - 1]['Projected Earnings']
    setProjectEarning(+projectEarning)
  }

  const dashes = () => {
    const segments = Number(investment?.term) // Number of segments
    const dashLength = 4 // Length of the dashes in pixels
    const radius = 75 // Radius of the circle
    const circumference = 2 * Math.PI * radius // Circumference of the circle
    const gapLength = (circumference - segments * dashLength) / segments // Length of each gap
    const dashArray = `${dashLength} ${gapLength}` // Dash pattern

    // Calculate the offset to start exactly at 12 o'clock
    const segmentAngle = 360 / segments // Angle between each segment in degrees
    const dashOffset = -(circumference * (segmentAngle / 360))

    return (
      <Svg height="170" width="170" viewBox="0 0 170 170">
        <Circle
          cx="85" // center of the circle (half of width/height)
          cy="85" // center of the circle (half of width/height)
          r={radius} // radius = half of width/height - (thickness / 2)
          stroke="#000" // color of the dashes
          strokeWidth="20" // thickness of the ring
          fill="none" // no fill, to create a ring effect
          strokeDasharray={dashArray} // dash pattern with dash length and gap length
          strokeDashoffset={dashOffset} // offset to start exactly at 12 o'clock
          opacity="1" // full opacity for the dashes
          transform="rotate(-90 85 85)" // rotate the circle to start from 12 o'clock
        />
      </Svg>
    )
  }

  let scroll = useRef()
  const eachSectionValue = 100 / Number(investment?.term)
  const remainingMonths = calculateMaturityDate(
    investment?.originationDate,
    investment,
  ).remainingMonths

  const contactLegal = () => {
    Linking.openURL('mailto:<EMAIL>')
  }

  const investmentPrice = tokenPrices[`${investment?.investmentAsset}-USD`]?.price

  return (
    <SafeAreaView style={styles.box}>
      {terminationTooltip && (
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => setTerminationTooltip(false)}
          style={{
            position: 'absolute',
            zIndex: 999,
            top: 60,
            width: screenWidth,
            backgroundColor: 'transparent',
            height: '100%',
          }}
        />
      )}
      <View
        style={{
          flexDirection: 'row',
          alignSelf: 'stretch',
          justifyContent: 'space-between',
          marginTop: 5,
        }}>
        <TouchableOpacity
          onPress={() => navigation?.goBack()}
          style={{height: 40, width: 40, marginLeft: 20}}>
          <Image
            source={require('../../imgs/backToSettings.png')}
            style={{height: 25, width: 20}}
          />
        </TouchableOpacity>
        <TextReg style={{color: '#FFF', fontSize: 18}}>LEND</TextReg>
        <View style={{height: 40, width: 40, marginRight: 20}} />
      </View>
      <View style={commonStyles.tileContainer}>
        {investment?.status === 'pending_closure' ? (
          <View style={{width: screenWidth - 32, alignItems: 'center'}}>
            <Image
              source={require('../../imgs/clock.png')}
              style={{height: 152, width: 152, marginVertical: 94}}
            />
            <TextBold style={{fontSize: 30, textAlign: 'center'}}>
              Account Closure Request Under Review
            </TextBold>
            <TextReg
              style={{fontSize: 18, textAlign: 'center', marginTop: 14, width: screenWidth - 60}}>
              We're in the process of closing your account. This process could take 5-7 business
              days to complete.
            </TextReg>
            <TextReg
              style={{
                fontSize: 18,
                textAlign: 'center',
                marginTop: 20,
              }}>
              Reach out to{' '}
              <TextReg style={{fontSize: 18, color: '#00FFBD'}} onPress={contactLegal}>
                <EMAIL>
              </TextReg>{' '}
              if you have any questions in the meantime.
            </TextReg>
          </View>
        ) : investment?.status === 'closed' ? (
          <View style={{width: screenWidth - 32, alignItems: 'center'}}>
            <Image
              source={require('../../imgs/graphics/marketLend.png')}
              style={{height: 203, width: 182, marginVertical: 74, marginRight: 20}}
              resizeMode="stretch"
            />
            <TextBold style={{fontSize: 30, textAlign: 'center'}}>Start earning today!</TextBold>
            <TextReg style={{fontSize: 18, textAlign: 'center', marginTop: 14, marginBottom: 60}}>
              Set up your LEND account and build wealth by earning fixed returns on idle assets.
            </TextReg>
            <Button
              style={{
                alignSelf: 'stretch',
                borderRadius: 4,
              }}
              onPress={() => navigation.navigate('CreateInvestment')}>
              <TextBold style={{fontSize: 18, letterSpacing: 2.16, color: '#3D3D50'}}>
                SET UP ACCOUNT
              </TextBold>
            </Button>
          </View>
        ) : (
          <ScrollView
            ref={ref => {
              scroll = ref
            }}
            style={{
              backgroundColor: '#28283D',
              alignSelf: 'stretch',
            }}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={() => getAllData()}
                colors={['#28283D']}
                tintColor={'#fff'}
              />
            }
            contentContainerStyle={{alignItems: 'center'}}>
            <View
              style={{
                backgroundColor: '#5A33E3',
                borderRadius: 8,
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginVertical: 24,
                width: screenWidth - 28,
              }}>
              <View style={{marginLeft: 20, marginVertical: 16}}>
                <TextReg style={{fontSize: 16}}>
                  Your Balance ({investment?.investmentAsset})
                </TextReg>
                <TextBold style={{fontSize: 32}}>
                  {formatCrypto(
                    new BigNumber(investment.startingAmount).plus(totalBalanceEarningPaid),
                    investment.investmentAsset,
                  )}
                </TextBold>
                <TextReg style={{fontSize: 12, marginTop: 6}}>
                  Your Next Monthly Earnings ({investment.investmentAsset})
                </TextReg>
                <TextBold style={{fontSize: 20, color: '#00FFBD'}}>
                  {formatCrypto(investment?.nextMonthlyEarning, investment?.investmentAsset)}
                </TextBold>
              </View>
              <Image
                source={require('../../imgs/investmentDashboard.png')}
                style={{height: 140, width: 126, borderRadius: 8}}
              />
            </View>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                width: screenWidth - 28,
                marginBottom: 24,
              }}>
              <TextReg style={{fontSize: 20}}>Your Earnings</TextReg>
              <TouchableOpacity
                onPress={() => navigation?.navigate('EarningsHistory', {id: investment?.id})}
                style={{flexDirection: 'row', gap: 8, alignItems: 'flex-end'}}>
                <Image
                  source={require('../../imgs/moneyBackGuarantee.png')}
                  style={{height: 18, width: 18}}
                />
                <TextBold style={{fontSize: 16, color: '#00FFBD', letterSpacing: 2}}>
                  HISTORY
                </TextBold>
              </TouchableOpacity>
            </View>
            {showLendGraphs && (
              <Card
                style={{
                  borderRadius: 4,
                  paddingTop: 16,
                  alignItems: 'flex-start',
                  alignSelf: 'stretch',
                }}
                cardWidth={screenWidth - 28}>
                <ProjectedBar
                  graphWidth={screenWidth - 50}
                  projectedGraph={graphData}
                  investment={investment}
                />
              </Card>
            )}
            <View style={{flexDirection: 'column', width: screenWidth - 28}}>
              <View style={{flexDirection: 'row', gap: 8, justifyContent: 'space-between'}}>
                <Pressable onPressIn={() => setBoxOpen(1)} onPressOut={() => setBoxOpen(null)}>
                  <Card
                    style={{
                      borderRadius: 8,
                      paddingVertical: 16,
                      alignItems: 'center',
                    }}
                    cardMarginBottom={8}
                    cardWidth={screenWidth / 2 - 18}>
                    <Image
                      source={require('../../imgs/deposit.png')}
                      style={{height: 42, width: 42}}
                    />
                    <TextBold
                      style={{fontSize: 20, marginTop: 20, marginBottom: 6}}
                      numberOfLines={boxOpen === 1 ? 2 : 1}>
                      {formatCrypto(investment?.startingAmount, investment?.investmentAsset)}{' '}
                      {investment?.investmentAsset}
                    </TextBold>
                    <TextReg style={{fontSize: 14, color: '#AFAFAF'}}>Deposit</TextReg>
                  </Card>
                </Pressable>
                <Card
                  style={{
                    borderRadius: 8,
                    paddingVertical: 16,
                    margin: 0,
                    alignItems: 'center',
                  }}
                  cardMarginBottom={8}
                  cardWidth={screenWidth / 2 - 18}>
                  <Image
                    source={require('../../imgs/interestRate.png')}
                    style={{height: 44, width: 42}}
                  />
                  <TextBold style={{fontSize: 20, marginTop: 20, marginBottom: 6}}>
                    {new BigNumber(investment?.interestRate).times(100).toFixed()}%
                  </TextBold>
                  <TextReg style={{fontSize: 14, color: '#AFAFAF'}}>Interest Rate</TextReg>
                </Card>
              </View>
              <View style={{flexDirection: 'row', gap: 8, justifyContent: 'space-between'}}>
                <Pressable onPressIn={() => setBoxOpen(2)} onPressOut={() => setBoxOpen(null)}>
                  <Card
                    style={{
                      borderRadius: 8,
                      paddingVertical: 16,
                      margin: 0,
                      alignItems: 'center',
                    }}
                    cardMarginBottom={8}
                    cardWidth={screenWidth / 2 - 18}>
                    <Image
                      source={require('../../imgs/earningsPaid.png')}
                      style={{height: 44, width: 42}}
                    />
                    <TextBold
                      style={{fontSize: 20, marginTop: 20, marginBottom: 6}}
                      numberOfLines={boxOpen === 2 ? 2 : 1}>
                      {formatCrypto(earningPaid, investment?.investmentAsset)}{' '}
                      {investment?.investmentAsset}
                    </TextBold>
                    <TextReg style={{fontSize: 14, color: '#AFAFAF'}}>Earning Paid</TextReg>
                  </Card>
                </Pressable>
                <Pressable onPressIn={() => setBoxOpen(3)} onPressOut={() => setBoxOpen(null)}>
                  <Card
                    style={{
                      borderRadius: 8,
                      paddingVertical: 16,
                      margin: 0,
                      alignItems: 'center',
                    }}
                    cardMarginBottom={8}
                    cardWidth={screenWidth / 2 - 18}>
                    <Image
                      source={require('../../imgs/projectedEarnings.png')}
                      style={{height: 44, width: 42}}
                    />
                    <TextBold
                      style={{fontSize: 20, marginTop: 20, marginBottom: 6}}
                      numberOfLines={boxOpen === 3 ? 2 : 1}>
                      {formatCrypto(projectEarning, investment?.investmentAsset)}{' '}
                      {investment?.investmentAsset}
                    </TextBold>
                    <TextReg style={{fontSize: 14, color: '#AFAFAF'}}>Projected Earnings</TextReg>
                  </Card>
                </Pressable>
              </View>
              <TouchableOpacity
                onPress={() => navigation.navigate('AssetWallet', {id: investment?.id})}>
                <Card
                  style={{
                    borderRadius: 15,
                    marginTop: 8,
                    alignItems: 'flex-start',
                  }}
                  cardMarginBottom={16}
                  cardWidth={screenWidth - 28}>
                  <View style={{flexDirection: 'row', gap: 4, alignItems: 'center', width: '100%'}}>
                    <View
                      style={{
                        backgroundColor: '#28283D',
                        padding: 12,
                        borderRadius: 8,
                        marginRight: 16,
                      }}>
                      <Image
                        source={require('../../imgs/unit21/wallet.png')}
                        style={{width: 22, height: 22}}
                      />
                    </View>
                    <View
                      style={{flexDirection: 'row', justifyContent: 'space-between', width: '70%'}}>
                      <View>
                        <TextReg style={{fontSize: 16}}>Assets</TextReg>
                        <TextReg style={{fontSize: 12, color: '#AFAFAF', marginTop: 7}}>
                          {investment?.investmentAsset}
                        </TextReg>
                      </View>
                      <View>
                        <TextBold style={{fontSize: 16}}>
                          {new BigNumber(investData?.projectedBalance).isNaN()
                            ? ''
                            : `$${numberWithCommas(
                                new BigNumber(investData?.projectedBalance)
                                  .multipliedBy(investmentPrice)
                                  .toFixed(2),
                              )}`}
                        </TextBold>
                      </View>
                    </View>
                    <Image
                      source={require('../../imgs/rightArrow.png')}
                      style={{width: 20, height: 22, marginLeft: 'auto'}}
                    />
                  </View>
                </Card>
              </TouchableOpacity>
              <View
                style={{
                  backgroundColor: '#3D3D50',
                  justifyContent: 'flex-end',
                  alignItems: 'center',
                  borderRadius: 8,
                  marginBottom: 30,
                }}>
                <TextReg style={{fontSize: 20, marginTop: 14}}>Investment Terms</TextReg>
                <View
                  style={{
                    backgroundColor: '#181822',
                    width: 205,
                    height: 205,
                    borderRadius: 102.5,
                    justifyContent: 'center',
                    alignItems: 'center',
                    zIndex: 99,
                    position: 'absolute',
                    top: 50,
                  }}>
                  <AnimatedCircularProgress
                    size={170}
                    width={18}
                    fill={remainingMonths * eachSectionValue}
                    rotation={0}
                    tintTransparency={false}
                    tintColor="#00FFBD"
                    onAnimationComplete={() => console.log('onAnimationComplete')}
                    backgroundColor="#777784">
                    {fill => (
                      <View style={{alignItems: 'center'}}>
                        <TextBold>
                          {remainingMonths}/{Number(investment?.term)}
                        </TextBold>
                        <TextReg>Months</TextReg>
                      </View>
                    )}
                  </AnimatedCircularProgress>
                  <View style={{position: 'absolute', opacity: 1}}>{dashes()}</View>
                </View>
                <ImageBackground
                  source={require('../../imgs/backgrounds/investmentTerms.png')}
                  resizeMode="stretch"
                  style={{
                    width: screenWidth - 28,
                    height: 350,
                    marginTop: 130,
                    paddingHorizontal: 15,
                    justifyContent: 'flex-end',
                    paddingVertical: 14,
                  }}
                  imageStyle={{
                    borderRadius: 8,
                  }}>
                  <View style={{marginTop: 40}}>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        borderBottomColor: '#E3E6ED',
                        borderBottomWidth: 1,
                        paddingVertical: 12,
                      }}>
                      <View style={{flexDirection: 'row', gap: 16, alignItems: 'center'}}>
                        <TextReg style={{fontSize: 14}}>Earnings Destination</TextReg>
                        <TouchableOpacity
                          onPress={() =>
                            navigation.navigate('EarningDestinationOption', {id: investment?.id})
                          }>
                          <Image
                            source={require('../../imgs/icons/edit.png')}
                            style={{height: 16, width: 16}}
                            resizeMode="contain"
                          />
                        </TouchableOpacity>
                      </View>
                      <TextBold style={{fontSize: 14}}>
                        {investment?.destination.toLowerCase() == 'custody'
                          ? 'WALLET'
                          : investment?.destination}
                      </TextBold>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        borderBottomColor: '#E3E6ED',
                        borderBottomWidth: 1,
                        paddingVertical: 12,
                      }}>
                      <TextReg style={{fontSize: 14}}>Maturity Date</TextReg>
                      <TextBold style={{fontSize: 14}}>
                        {calculateMaturityDate(investment?.originationDate, investment).nextDate}
                      </TextBold>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        borderBottomColor: '#E3E6ED',
                        borderBottomWidth: 1,
                        paddingVertical: 12,
                      }}>
                      <TextReg style={{fontSize: 14}}>Next Earnings Date</TextReg>
                      <TextBold style={{fontSize: 14}}>
                        {moment(investment?.upcomingProjectedEarnings[0]?.payoutDate).format('ll')}
                      </TextBold>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        paddingTop: 12,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: 8,
                          position: 'relative',
                        }}>
                        <TextReg style={{fontSize: 14}}>Early Termination Fee</TextReg>
                        <TouchableOpacity
                          onPress={() => setTerminationTooltip(!terminationTooltip)}>
                          <View
                            style={{
                              justifyContent: 'center',
                              alignItems: 'center',
                              borderWidth: 1,
                              borderColor: '#00FFBD',
                              width: 16,
                              height: 16,
                              borderRadius: 8,
                            }}>
                            <TextBold style={{fontSize: 12, color: '#00FFBD'}}>?</TextBold>
                          </View>
                        </TouchableOpacity>
                        {terminationTooltip && (
                          <View
                            style={{
                              position: 'absolute',
                              zIndex: 9999,
                              top: -66,
                              left: 0,
                              alignItems: 'center',
                              width: screenWidth - 60,
                            }}>
                            <View
                              style={{
                                backgroundColor: '#5A33E3',
                                padding: 10,
                                borderRadius: 8,
                                width: '100%',
                              }}>
                              <TextBold>
                                SALT charges a fee for closing the LEND account early
                              </TextBold>
                            </View>
                            <Svg
                              height="20"
                              width="30"
                              viewBox="0 0 40 20"
                              style={{marginTop: -6, marginRight: 38}}>
                              <Polygon points="0,0 20,20 40,0" fill="#5A33E3" />
                            </Svg>
                          </View>
                        )}
                      </View>
                      <TextBold style={{fontSize: 14}}>
                        {new BigNumber(investment?.earlyTerminationFee).toFormat()}{' '}
                        {investment?.investmentAsset}
                      </TextBold>
                    </View>
                  </View>
                  <Button
                    disabled={
                      !isMaturityDate(investment?.originationDate, investment?.lockUpPeriod)
                    }
                    style={{
                      alignSelf: 'stretch',
                      backgroundColor: 'transparent',
                      borderColor: '#E5705A',
                      borderWidth: 2,
                      borderRadius: 4,
                      marginTop: 32,
                    }}
                    onPress={() => navigation.navigate('CloseAccount', {id: investment?.id})}>
                    <TextBold style={{fontSize: 18, letterSpacing: 2.16, color: '#E5705A'}}>
                      CLOSE ACCOUNT
                    </TextBold>
                  </Button>
                </ImageBackground>
              </View>
            </View>
          </ScrollView>
        )}
      </View>
    </SafeAreaView>
  )
}

LendDashboard.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
  user: state.user.user,
  launchDarkly: state.launchDarkly,
  tokenPrices: state.user.prices,
})

export default connect(mapStateToProps)(LendDashboard)
