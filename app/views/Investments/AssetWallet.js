import React, {useEffect, useState} from 'react'
import {
  View,
  TouchableOpacity,
  ScrollView,
  Image,
  Dimensions,
  SafeAreaView,
  Linking,
  PermissionsAndroid,
  Share,
  Platform,
} from 'react-native'
import {connect, useDispatch} from 'react-redux'
import {BigNumber} from 'bignumber.js'
import moment from 'moment'
import Papa from 'papaparse'
import RNFS from 'react-native-fs'

import {TextReg, TextBold, Card} from '../../components'
import commonStyles from '../../styles/commonStyles'
import styles from './styles'
import {capitalizeFirstLetter, formatCrypto, numberWithCommas} from '../../util/helpers'
import {getInvestments} from '../../store/investments/investments.actions'
import {cryptoMeta} from '../../util/enumerables'
import {RefreshControl} from 'react-native-gesture-handler'
import {askingForPermissions} from '../../store/auth/auth.actions'

const {width: screenWidth} = Dimensions.get('window')

const AssetWallet = props => {
  const [transactions, setTransactions] = useState([])
  const [refreshing, setRefreshing] = useState(false)
  const [showTxId, setShowTxId] = useState()

  const dispatch = useDispatch()

  const {investment, accountRef, user, tokenPrices} = props

  const wallet = user?.allWallets[accountRef - 1]?.find(
    w => w.currency == investment?.investmentAsset,
  )

  const getAllData = async () => {
    dispatch(getInvestments())
    setRefreshing(true)
    const res = await props.WebService.getTxAll(accountRef)
    const tx = res.data
      .map(item => {
        if (item.currency == investment?.investmentAsset) {
          return item.transactions
        }
      })
      .filter(val => val)[0]
    setTransactions(tx)
    setRefreshing(false)
  }

  useEffect(() => {
    getAllData()
  }, [])

  const getAssetIcon = () => {
    const icon = cryptoMeta.get(investment?.investmentAsset)?.icon
    return <Image source={icon} style={{width: 35, height: 35}} />
  }

  const isTx = tx => {
    return tx.date !== undefined
  }

  const isOutboundTx = tx => {
    return tx.createdAt !== undefined
  }

  const sortTxs = txArray => {
    if (txArray.length) {
      if (isTx(txArray[0])) {
        return txArray.sort((a, b) => (moment(a.date).isBefore(b.date) ? 1 : -1))
      }
      if (isOutboundTx(txArray[0])) {
        return txArray.sort((a, b) => (moment(a.createdAt).isBefore(b.createdAt) ? 1 : -1))
      }
      return txArray
    } else return txArray
  }

  const getScannerUrl = (token, txId) => {
    switch (token) {
      case 'BTC':
        return `https://live.blockcypher.com/btc/tx/${txId}`
      case 'BCH':
        return `https://explorer.bitcoin.com/bch/tx/${txId}`
      case 'LTC':
        return `https://live.blockcypher.com/ltc/tx/${txId}`
      case 'DASH':
        return `https://chainz.cryptoid.info/dash/tx.dws?${txId}.htm`
      case 'ETH':
        return `https://etherscan.io/tx/${txId}`
      case 'SALT':
        return `https://etherscan.io/tx/${txId}`
      case 'TUSD':
        return `https://etherscan.io/tx/${txId}`
      case 'USDC':
        return `https://etherscan.io/tx/${txId}`
      case 'DOGE':
        return `https://dogechain.info/tx/${txId}`
      case 'XRP':
        return `https://xrpscan.com/tx/${txId}`
      default:
        return `https://etherscan.io/tx/${txId}`
    }
  }

  const openTransactionScanner = item => {
    if (item?.txid) {
      const scannerUrl = getScannerUrl(investment?.investmentAsset, item.txid)
      Linking.canOpenURL(scannerUrl).then(supported => {
        if (supported) {
          dispatch(askingForPermissions(true))
          Linking.openURL(scannerUrl)
        }
      })
    } else if (item?.id) {
      if (showTxId == item.id) {
        setShowTxId(null)
      } else {
        setShowTxId(item.id)
      }
    }
  }

  const colorReason = item => {
    console.log('item', item)
    if (item.type?.toLowerCase() === 'received') {
      return '#00FFBD'
    }
    if (item.type?.toLowerCase() === 'send') {
      return '#E5705A'
    }
    return '#E5705A'
  }

  let sortedTransactions = sortTxs(transactions)?.map(a => {
    return {
      ...a,
      showId: a.id == showTxId,
      confirmed: a.status == 'confirmed',
    }
  })

  const requestStoragePermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          {
            title: 'Storage Permission Required',
            message: 'This app needs access to your storage to save files',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        )
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Storage permission granted')
          return true
        } else {
          console.log('Storage permission denied')
          return false
        }
      } catch (err) {
        console.warn(err)
        return false
      }
    } else {
      // No need to request permission on iOS
      return true
    }
  }

  const saveAndroid = async (data, title = 'data.csv') => {
    // Check for permission first
    const hasPermission = await requestStoragePermission()
    if (!hasPermission) {
      console.log('Permission denied')
      return
    }

    const csv = Papa.unparse(data)
    const path = `${RNFS.ExternalStorageDirectoryPath}/Documents/SALT/${title}`

    try {
      await RNFS.mkdir(`${RNFS.ExternalStorageDirectoryPath}/Documents/SALT/`)
      await RNFS.writeFile(path, csv, 'utf8')
      console.log(`File written to ${path}`)
    } catch (error) {
      console.error('Error saving or sharing CSV file', error)
    }
  }

  const saveIOS = async (data, title = 'data.csv') => {
    const csv = Papa.unparse(data)
    const path = `${RNFS.DocumentDirectoryPath}/${title}`

    try {
      await RNFS.writeFile(path, csv, 'utf8')
      console.log(`File written to ${path}`)

      // Share the file
      const shareResponse = await Share.share({
        title: title,
        url: `file://${path}`,
        type: 'text/csv',
      })

      console.log('File shared:', shareResponse)
    } catch (error) {
      console.error('Error saving or sharing CSV file', error)
    }
  }

  const cap = str => {
    return str.charAt(0).toUpperCase() + str.slice(1)
  }

  const investmentPrice = tokenPrices[`${investment?.investmentAsset}-USD`]?.price

  const down = async () => {
    let transactions = sortedTransactions.filter(a => a.reason !== 'commingle')

    let downData = transactions?.map(tx => {
      let dateMat = moment(tx.date).format('MM/DD/YYYY h:mm A')

      let amtNum = new BigNumber(tx.amount).multipliedBy(investmentPrice).toFixed(2)

      if (tx.reason == 'transfer' || tx.reason == 'withdrawal') {
        amtNum = `-$${amtNum}`
      } else amtNum = `$${amtNum}`

      let confirmId = tx.id?.toUpperCase()?.split('-')[0]

      if (tx.reason == 'deposit' && tx.txid) {
        confirmId = getScannerUrl(tx.currency, tx.txid)
      }

      return {
        Date: dateMat,
        Amount: tx.amount + ' ' + investment?.investmentAsset,
        'Dollar Amount': amtNum,
        Status: cap(tx.status),
        Reason: cap(tx.reason),
        'Confirmation ID': confirmId,
      }
    })

    let title = `Lend-${investment?.account?.name}-Tx-History.csv`
    if (Platform.OS === 'android') {
      saveAndroid(downData, title)
    } else {
      saveIOS(downData, title)
    }
  }

  let maxHeight = sortedTransactions?.length * 50 + 70
  if (sortedTransactions?.length < 1) {
    maxHeight = 0
  }

  return (
    <SafeAreaView style={styles.box}>
      <View
        style={{
          flexDirection: 'row',
          alignSelf: 'stretch',
          justifyContent: 'space-between',
          marginTop: 5,
        }}>
        <TouchableOpacity
          onPress={() => props.navigation?.goBack()}
          style={{height: 40, width: 40, marginLeft: 20}}>
          <Image
            source={require('../../imgs/backToSettings.png')}
            style={{height: 25, width: 20}}
          />
        </TouchableOpacity>
        <TextReg style={{color: '#FFF', fontSize: 18}}>Assets</TextReg>
        <View style={{height: 40, width: 40, marginRight: 20}} />
      </View>
      <View
        style={{
          ...commonStyles.tileContainer,
          backgroundColor: '#1C1C1C',
          paddingTop: 20,
        }}>
        <View
          style={{
            ...commonStyles.tileContainer,
            width: '100%',
            marginHorizontal: 16,
            width: screenWidth - 32,
            backgroundColor: 'transparent',
          }}>
          {getAssetIcon()}
          <TextBold style={{fontSize: 35, marginTop: 11}}>
            $
            {numberWithCommas(
              new BigNumber(wallet?.projectedBalance).multipliedBy(investmentPrice).toFixed(2),
            )}
          </TextBold>
          <TextReg style={{fontSize: 16, color: '#E3E6ED', marginBottom: 60}}>
            {formatCrypto(wallet?.projectedBalance, investment?.investmentAsset)}{' '}
            {investment?.investmentAsset}
          </TextReg>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: 8,
              width: '100%',
            }}>
            <TextReg
              style={{
                fontSize: 16,
                marginLeft: 20,
              }}>
              Transaction History
            </TextReg>
            {false && (
              <TouchableOpacity
                onPress={() => down()}
                style={{
                  height: 40,
                  width: 40,
                  borderRadius: 12,
                  marginRight: 20,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Image
                  source={require('../../imgs/icons/cloud.png')}
                  style={{height: 34, width: 34}}
                />
              </TouchableOpacity>
            )}
          </View>
          <View
            style={{
              borderRadius: 15,
              padding: 30,
              backgroundColor: '#3D3D50',
              borderRadius: 14,
              flex: 1,
              maxHeight: maxHeight,
            }}
            cardWidth={screenWidth - 32}>
            <ScrollView
              style={{flexShrink: 1, flex: 1}}
              contentContainerStyle={{alignItems: 'center'}}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={() => getAllData()}
                  colors={['#28283D']}
                  tintColor={'#fff'}
                />
              }>
              {sortedTransactions?.length > 0 ? (
                sortedTransactions.map((tx, idx) => {
                  const isLast = sortedTransactions.length - 1 === idx
                  return (
                    <TouchableOpacity key={idx} onPress={() => openTransactionScanner(tx)}>
                      <View
                        key={idx}
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          width: '100%',
                          borderBottomColor: isLast ? 'none' : '#FFF',
                          borderBottomWidth: isLast ? 0 : 0.5,
                          paddingBottom: isLast ? 0 : 11,
                          marginBottom: isLast ? 0 : 11,
                          height: 50,
                        }}>
                        <View>
                          <TextReg style={{fontSize: 16, color: colorReason(tx)}}>
                            {capitalizeFirstLetter(tx.reason)}
                          </TextReg>
                          <TextReg style={{fontSize: 12}}>
                            {' '}
                            {moment(tx.date).format('MM/DD/YYYY h:mm A')}
                          </TextReg>
                        </View>
                        {tx.showId ? (
                          <View style={{marginTop: 2, alignItems: 'center'}}>
                            <TextBold style={{fontSize: 18, color: '#00FFBD'}}>
                              {tx.id.substring(0, 8).toUpperCase()}
                            </TextBold>
                            <TextReg>{tx.confirmed ? 'Confirmed' : 'Unconfirmed'}</TextReg>
                          </View>
                        ) : (
                          <View style={{alignItems: 'flex-end'}}>
                            <TextBold style={{fontSize: 16, color: colorReason(tx)}}>
                              {tx.reason === 'withdrawal' ? '-' : ''}$
                              {numberWithCommas(
                                new BigNumber(tx.amount).multipliedBy(investmentPrice).toFixed(2),
                              )}
                            </TextBold>
                            <TextReg style={{fontSize: 12}}>
                              {tx.amount} {investment?.investmentAsset}
                            </TextReg>
                          </View>
                        )}
                      </View>
                    </TouchableOpacity>
                  )
                })
              ) : (
                <TextReg>No transactions yet</TextReg>
              )}
            </ScrollView>
          </View>
        </View>
      </View>
    </SafeAreaView>
  )
}

AssetWallet.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

const mapStateToProps = (state, props) => {
  const investment = state.investments.byId[props.route?.params?.id] || {}

  return {
    WebService: state.auth.WebService,
    investment,
    accountRef: state.auth.account.ref,
    tokenPrices: state.user.prices,
    user: state.user.user,
  }
}

export default connect(mapStateToProps)(AssetWallet)
