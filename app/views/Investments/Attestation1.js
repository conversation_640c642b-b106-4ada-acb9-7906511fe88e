import React, {useEffect, useState} from 'react'
import {StyleSheet, View, Dimensions, TouchableOpacity, Image, ScrollView} from 'react-native'
import {TextReg, TextBold, BackgroundHeader, Button} from '../../components'
import {useDispatch, useSelector} from 'react-redux'

let Attestation1 = ({navigation, route}) => {
  let dispatch = useDispatch()
  let [pick, setPick] = useState(null)
  let WebService = useSelector(state => state.auth.WebService || {})
  let {height: ScreenHeight, width: ScreenWidth} = Dimensions.get('window')

  console.log('Attestation1', navigation, route)

  let close = () => {
    navigation.pop(stackDepth)
  }

  let next = () => {
    if (pick == 1) {
      navigation.navigate('Attestation2')
    } else if (pick == 2) {
      navigation.navigate('AttestationForm')
    }
  }

  let box = (num, text, img) => {
    return (
      <TouchableOpacity
        onPress={() => {
          setPick(num)
        }}
        style={{
          flexDirection: 'row',
          backgroundColor: '#535363',
          paddingBottom: 10,
          borderRadius: 10,
          marginBottom: 20,
          borderColor: pick == num ? '#00FFBD' : '#535363',
          borderWidth: 1,
        }}>
        <View style={{width: 53, marginLeft: 14, marginTop: 10}}>{img}</View>
        <View style={{flexDirection: 'column'}}>
          <TextBold style={{marginTop: 10, width: ScreenWidth - 114}}>{text.title}</TextBold>
          <TextReg
            style={{
              marginTop: 14,
              width: ScreenWidth - 114,
            }}>
            {text.first}
          </TextReg>
          <TextReg
            style={{
              marginTop: 14,
              width: ScreenWidth - 114,
            }}>
            {text.second}
          </TextReg>
        </View>
      </TouchableOpacity>
    )
  }

  return (
    <View style={localStyles.box}>
      <BackgroundHeader title={'Attestation1'} goBack={() => navigation.goBack()} />

      <ScrollView style={{...localStyles.box}}>
        <View style={{flex: 1, marginLeft: 20, marginRight: 20}}>
          <TextReg style={{marginTop: 10, marginBottom: 10, fontSize: 17}}>{`Choose one of the following:`}</TextReg>
          {box(
            1,
            {
              title: 'Complete SALT’s Attestation Form',
              first:
                'If you are an Accredited Investor and DO NOT have a 3rd Party Verification Letter, you may complete a letter of attestation packet.',
              second: `Please Note: The failure to complete the Attestation Form in full and provide required supporting documentation set forth therein will result in a denial of your application.`,
            },
            <Image source={require('../../imgs/graphics/letter.png')} style={{height: 42, width: 40}} />,
          )}
          {box(
            2,
            {
              title: 'Upload a Signed 3rd Party Verification Letter',
              first:
                'Generally, an SEC registered broker or investment advisor, licensed attorney, or certified public accountant may provide a 3rd party verification letter, which certifies they took steps to confirm your status as an accredited investor within the last three (3) months.',
              second: `Please Note: Verification letters are subject to SALT diligence and approval. If you have a signed 3rd Party Verification letter, click this option.`,
            },
            <Image source={require('../../imgs/graphics/paper2.png')} style={{height: 50, width: 40}} />,
          )}
        </View>
      </ScrollView>
      <View style={{alignSelf: 'stretch', marginTop: 10, marginBottom: 26, paddingLeft: 20, paddingRight: 20}}>
        <Button
          isLoading={false}
          disabled={!pick}
          style={{
            alignSelf: 'stretch',
          }}
          onPress={() => next()}
          theme={'secondary'}>
          <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
        </Button>
        <Button
          isLoading={false}
          disabled={false}
          style={{
            alignSelf: 'stretch',
            backgroundColor: '#28283D',
          }}
          onPress={() => navigation.goBack()}
          theme={'secondary'}>
          <TextReg style={{color: '#FFF', fontSize: 18}}>BACK</TextReg>
        </Button>
      </View>
    </View>
  )
}

export default Attestation1

const localStyles = StyleSheet.create({
  box: {
    flex: 1,
    backgroundColor: '#28283D',
  },
})
