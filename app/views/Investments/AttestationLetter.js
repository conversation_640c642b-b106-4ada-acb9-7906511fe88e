import React, {useEffect, useState} from 'react'
import {StyleSheet, View, Dimensions, TouchableOpacity, Image, ScrollView} from 'react-native'
import {TextReg, TextBold, BackgroundHeader, Button} from '../../components'
import {useDispatch, useSelector} from 'react-redux'

let AttestationLetter = ({navigation, route}) => {
  let dispatch = useDispatch()
  let WebService = useSelector(state => state.auth.WebService || {})
  let {height: ScreenHeight} = Dimensions.get('window')

  console.log('AttestationLetter', navigation, route)

  let close = () => {
    navigation.pop(stackDepth)
  }

  let next = () => {
    console.log('next')
    navigation.navigate('Attestation2')
  }

  return (
    <View style={localStyles.box}>
      <BackgroundHeader title={'Attestation Form'} goBack={() => navigation.goBack()} />
      <ScrollView style={{...localStyles.box, backgroundColor: 'blue'}}>
        <View style={{flex: 1, marginLeft: 20, marginRight: 20}}>
          <TextReg>{`Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum `}</TextReg>
        </View>
      </ScrollView>
      <View style={{alignSelf: 'stretch', marginTop: 10, marginBottom: 26, paddingLeft: 20, paddingRight: 20}}>
        <Button
          isLoading={false}
          disabled={false}
          style={{
            alignSelf: 'stretch',
          }}
          onPress={() => next()}
          theme={'secondary'}>
          <TextReg style={{color: '#000', fontSize: 18}}>CONTINUE</TextReg>
        </Button>
        <Button
          isLoading={false}
          disabled={false}
          style={{
            alignSelf: 'stretch',
            backgroundColor: '#28283D',
          }}
          onPress={() => navigation.goBack()}
          theme={'secondary'}>
          <TextReg style={{color: '#FFF', fontSize: 18}}>BACK</TextReg>
        </Button>
      </View>
    </View>
  )
}

export default AttestationLetter

const localStyles = StyleSheet.create({
  box: {
    flex: 1,
    backgroundColor: '#28283D',
  },
})
