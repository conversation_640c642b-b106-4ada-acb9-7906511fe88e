import React from 'react'
import {View} from 'react-native'
import Svg, {Path, Line, Text, ClipPath, Defs, Rect} from 'react-native-svg'
import {Gesture, GestureDetector} from 'react-native-gesture-handler'
import {useVector} from 'react-native-redash'
import * as shape from 'd3-shape'
import {scaleLinear} from 'd3-scale'
import {parse} from 'react-native-redash'

import Dot from '../Home/Shell/Dot'
import BarHeader from './BarHeader'
import moment from 'moment'
import {TextReg} from '../../components'
import {formatCrypto, dynamicRoundUp} from '../../util/helpers'

const POINTS = 12

const buildGraphForLend = (datapoints, label, graphWidth, gradiant = false, transitionDate) => {
  const priceList = datapoints.prices?.slice(0, POINTS)
  const formattedValues = priceList?.map(price => [parseFloat(price[0]), price[1]])
  const prices = formattedValues?.map(value => value[0])
  const dates = formattedValues?.map(value => value[1])
  const scaleX = scaleLinear()
    .domain([Math.min(...dates), Math.max(...dates)])
    .range([0, graphWidth])
  const minPrice = Math.min(...prices)
  const maxPrice = Math.max(...prices)
  const ceilingPrice = dynamicRoundUp(maxPrice)
  const midPrice = dynamicRoundUp(ceilingPrice / 1.9)

  const scaleY = scaleLinear().domain([minPrice, maxPrice]).range([200, 0])

  // Find the index for the transition from dashed to solid
  const transitionIndex = formattedValues.findIndex(([, date]) => date >= transitionDate)
  const transitionX = scaleX(formattedValues[transitionIndex][1])

  let yRange = maxPrice - minPrice
  let timeRange = yRange / 200

  return {
    label,
    minPrice,
    maxPrice,
    transitionX,
    ceilingPrice,
    midPrice,
    path: parse(
      shape
        .line()
        .x(([, x]) => scaleX(x))
        .y(([y]) => scaleY(y))
        .curve(shape.curveBasis)(formattedValues),
    ),
    area: shape
      .area()
      .x(([, x]) => scaleX(x))
      .y(([y]) => scaleY(y - gradiant * timeRange))
      .curve(shape.curveBasis)(formattedValues),
  }
}

export const graphsLend = (graphWidth, prices30, changePoint, gradiant = false) => {
  let data = buildGraphForLend(prices30, 'Last Hour', graphWidth, gradiant, changePoint)
  return [
    {
      label: '1H',
      value: 0,
      data,
    },
  ]
}

const ProjectedBar = ({graphWidth, projectedGraph, investment}) => {
  const shareX = useVector(graphWidth)

  const graphData = graphsLend(graphWidth, projectedGraph, projectedGraph?.changePoint)[0].data
  const graphPath = graphData?.path
  const graphArea = graphData?.area
  const transitionX = graphData?.transitionX
  const ceilingPrice = formatCrypto(graphData?.ceilingPrice, investment?.investmentAsset)
  const midPrice = formatCrypto(graphData?.midPrice, investment?.investmentAsset)

  const gesturePan = Gesture.Pan().onUpdate(event => {
    shareX.x.value = event.x
    if (shareX.x.value < 0) {
      shareX.x.value = 0
    }
    if (shareX.x.value > graphWidth) {
      shareX.x.value = graphWidth
    }
  })
  const minimumSpacing = 22 // Define minimum spacing in pixels
  let lastXPosition = 0
  const datesLength = projectedGraph?.prices?.length

  const verticalLines = Array.from({length: projectedGraph?.prices?.length}).map((_, index) => {
    // const xPosition = (graphWidth / (projectedGraph?.prices?.length - 1)) * index
    // Assuming `projectedGraph.prices` contains objects like { date: 'YYYY-MM-DD', value: number }

    const firstDate = new Date(projectedGraph?.prices[0][1]).getTime()
    const lastDate = new Date(projectedGraph?.prices[datesLength - 1][1]).getTime()
    const totalTimeRange = lastDate - firstDate

    const xPosition =
      ((new Date(projectedGraph?.prices[index][1]).getTime() - firstDate) / totalTimeRange) *
      graphWidth

    const showDate = xPosition - lastXPosition
    const showAlternating = datesLength > 8
    const shouldDisplayDate = !showAlternating || index % 2 === 0

    if (lastXPosition === 0 || xPosition - lastXPosition > minimumSpacing) {
      lastXPosition = xPosition
    }

    const renderDate = (
      <Text
        x={
          index === 0
            ? xPosition + 14
            : index === projectedGraph?.prices?.length - 1
            ? xPosition - 14
            : xPosition
        }
        y={210} // Position slightly below the graph
        fill="#FFF"
        fontSize="10"
        textAnchor="middle" // Center the text
        alignmentBaseline="hanging" // Adjust text baseline
      >
        {new Date(projectedGraph?.prices[index][1]).toLocaleDateString('en-US', {
          month: 'numeric',
          year: '2-digit',
        })}
      </Text>
    )

    return (
      <React.Fragment key={`v-line-${index}`}>
        <Line
          x1={xPosition}
          y1={0}
          x2={xPosition}
          y2={200}
          stroke="rgba(255, 255, 255, 0.1)"
          strokeWidth="1"
        />
        {showDate > minimumSpacing || showDate === 0
          ? shouldDisplayDate
            ? renderDate
            : null
          : null}
      </React.Fragment>
    )
  })

  const horizontalLines = [0, 100, 200].map((yValue, index) => (
    <React.Fragment>
      <Line
        key={`h-line-${index}`}
        x1={0}
        y1={yValue}
        x2={graphWidth}
        y2={yValue}
        stroke={yValue === 200 ? 'rgba(255, 255, 255, 1)' : 'rgba(255, 255, 255, 0.1)'}
        strokeWidth="1"
        strokeDasharray={yValue === 200 ? '0' : '4'} // This makes the line dashed
      />
      {yValue !== 0 && (
        <View
          style={{
            backgroundColor: 'rgba(40, 40, 61, 0.60)',
            paddingVertical: 2,
            paddingHorizontal: 4,
            borderRadius: 4,
            position: 'absolute',
            top: yValue - 110,
          }}>
          <TextReg>
            {yValue === 200 ? midPrice : ceilingPrice} {investment?.investmentAsset}
          </TextReg>
        </View>
      )}
    </React.Fragment>
  ))

  const gestureTap = Gesture.Tap().onTouchesDown(event => {
    'worklet'
    const xCo = event.allTouches[0]?.x
    shareX.x.value = xCo
    if (shareX.x.value < 0) {
      shareX.x.value = 0
    }
    if (shareX.x.value > graphWidth) {
      shareX.x.value = graphWidth
    }
  })

  const arr = projectedGraph['prices']

  const getDatesBetween = (startDate, endDate) => {
    let dates = []
    let currentDate = new Date(startDate)

    while (currentDate <= endDate) {
      dates.push([moment(currentDate).format('MM/DD/YYYY'), moment(currentDate).valueOf()])
      currentDate.setDate(currentDate.getDate() + 1)
    }

    return dates
  }
  const headerDates = getDatesBetween(arr?.[0][1], arr?.[arr.length - 1][1])

  return (
    <View style={{flex: 1}}>
      <GestureDetector gesture={gestureTap}>
        <GestureDetector gesture={gesturePan}>
          <View style={{height: 320, width: graphWidth}}>
            <View style={{flexDirection: 'row'}}>
              <BarHeader
                translation={shareX}
                graphWidth={graphWidth}
                prices={projectedGraph}
                datesArr={headerDates}
                dataObj={graphData}
                investment={investment}
              />
            </View>
            <View>
              <Svg width={graphWidth} height={320}>
                {verticalLines}
                {horizontalLines}
                <Defs>
                  {/* Clip path for the dashed section */}
                  <ClipPath id="dashedClip">
                    <Rect x="0" y="0" width={transitionX} height={320} />
                  </ClipPath>

                  {/* Clip path for the solid section */}
                  <ClipPath id="solidClip">
                    <Rect x={transitionX} y="0" width={graphWidth - transitionX} height={320} />
                  </ClipPath>
                </Defs>

                {/* Full path, clipped to the dashed section */}
                <Path
                  d={graphArea}
                  stroke="#00FFBD"
                  strokeWidth="2"
                  strokeDasharray="6, 8"
                  fill="none"
                  clipPath="url(#solidClip)"
                />

                {/* Full path, clipped to the solid section */}
                <Path
                  d={graphArea}
                  stroke="#00FFBD"
                  strokeWidth="2"
                  fill="none"
                  clipPath="url(#dashedClip)"
                />
              </Svg>
              <Dot
                xAxis={shareX}
                dataPath={graphPath}
                color={'#00FFBD'}
                withVerticalLine
                graphHeight={200}
              />
            </View>
          </View>
        </GestureDetector>
      </GestureDetector>
    </View>
  )
}

let local = {
  toggleChart: {
    height: 33,
    width: 33,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: '#888',
    borderWidth: 1,
    borderRadius: 4,
    marginRight: 5,
  },
}

export default ProjectedBar
