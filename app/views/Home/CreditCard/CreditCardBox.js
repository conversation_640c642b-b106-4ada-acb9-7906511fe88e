import React from 'react'
import { View, Image } from 'react-native'

import { TextBold, TextReg, Card, Button } from '../../../components'

import styles from '../styles'

const CreditCardBox = props => (
  <Card
    marginTop={0}
    cardMarginBottom={0}
    style={{ backgroundColor: '#00FFBD' }}
  >
    <View
      style={{
        alignSelf: 'stretch',
        marginBottom: 12,
        alignItems: 'center',
        paddingTop: 4,
        position: 'relative',
      }}
    >
      {false && (
        <View style={{ alignSelf: 'flex-end' }}>
          <Image
            source={require('../../../imgs/closeX.png')}
            style={{
              height: 24,
              width: 24,
            }}
          />
        </View>
      )}
      <TextReg style={styles.statusTitleCC}>
        Buy anything with your cypto,
      </TextReg>
      <TextBold style={styles.statusTitleCC}>{`don't spend it`}</TextBold>
      <View style={{ flexDirection: 'row', paddingLeft: 10, marginTop: 8 }}>
        <Image
          source={require('../../../imgs/CreditCard/threeCards.png')}
          style={{
            height: 300,
            width: 381,
            marginTop: -64,
            marginBottom: -46,
          }}
        />
      </View>
      <Button
        style={{
          alignSelf: 'stretch',
          backgroundColor: '#FFF',
          marginLeft: 20,
          marginRight: 20,
          marginBottom: 6,
        }}
        onPress={() => props.openCreditCardPush()}
      >
        <TextReg style={{ fontSize: 18 }}>Learn More</TextReg>
      </Button>
    </View>
  </Card>
)

export default CreditCardBox
