import React, {Component} from 'react'
import {View, TextInput, TouchableOpacity, ScrollView, Image, Platform, Linking} from 'react-native'
import {connect} from 'react-redux'
import AsyncStorage from '@react-native-async-storage/async-storage'
import LottieView from 'lottie-react-native'

import {TextReg, TextBold, BackgroundHeader, LocationSelect, Card, Button} from '../../components'
import commonStyles from '../../styles/commonStyles'
import styles from './styles'
import {getTokenPic} from '../../util/tokens'
import iso3166 from 'iso-3166-2'
import countryCodes from '../../util/countryCodes'
import {
  numberWithCommas,
  getBaseRate,
  getPayment,
  getRewardRateMap,
  createMiniAmSchedule,
  getTotalInterest,
  getTotalRewards,
  getAprForRewards,
  getApr,
  listPossibleWallets,
  getOriginationFeeAmount,
  getFeeRateWithDiscount,
  getOriginationFeeRate,
  getLoanAmount,
} from '../../util/helpers'
import {
  increaseRefreshDataCount,
  pauseUnit21,
  updateLoansDrill,
  updateLoansInOneUser,
  afterNewLoan,
} from '../../store/user/user.actions'
import {askingForPermissions} from '../../store/auth/auth.actions'

import {sendEvent} from '../../store/analytics/analytics.actions'
import areaArrow from '../../imgs/areaArrow.png'
import areaArrow2 from '../../imgs/areaArrow2.png'
import NonLendableSuccess from './LoanReq/NonLendableSuccess'
import Discounts from './LoanReq/Discounts'
import BigNumber from 'bignumber.js'

class LoanRequest extends Component {
  constructor(props) {
    super(props)
    let pickableCountryCodes = countryCodes().map(a => a.name)
    pickableCountryCodes = ['United States'].concat(pickableCountryCodes)

    this.state = {
      loanAmount: '',
      selectedLTV: 0.7,
      selectedRepayment: 'principal',
      countryCodes: pickableCountryCodes,
      countrySubs: [],
      selectedCountry: null,
      countryCode: null,
      pickableCountrySubs: [],
      selectedProvince: null,
      agreeRatesAndFees: false,
      regionCode: null,
      termLength: 12,
      saltMax: 0,
      redeemedSalt: 0,
      jurisdictionId: null,
      submitPayment: null,
      displayLoan: {
        payment: '0',
        loanAmount: '0',
        apr: '0',
        amountPlusFee: '0',
        loanTotal: '0',
        interest: '0',
        collateralNeeded: '0',
        saltNeeded: '0',
        lastPayment: '0',
        decimalApr: '0',
        interestRate: '0',
        showApr: '0',
        loanFee: '0',
      },
      contraintAmount: {min: 1000, max: 25000000000},
      contraintTerm: {min: 0, max: 93312000000},
      contraintApr: {min: 0, max: 1},
      maxFee: {min: 0, max: 1},
      submitLoading: false,
      submitError: false,
      instaErrorAmount: false,
      instaErrorTerm: false,
      affiliateCode: '',
      codeLoading: false,
      loading: false,
      rateDiscount: 0,
      feeDiscount: 0,
      codeError: false,
      showSalt: true,
      lendableArea: true,
      usRestricted: false,
      aprFloors: null,
      baseRates: null,
      showRewardRate: 0.0,
      showTotalRewards: 0,
      totalRewards: 0,
      monthlyPayment: 0,
      showMonthly: '',
      adminFee: 0,
      realAdminFee: 0,
    }
    this.inputs = {}
    this.scroll = null
    const msPerDay = 24 * 60 * 60 * 1000
    this.MS_PER_MONTH = msPerDay * 30

    let countryName
    let pickableCountrySubs = []
    if (this.props.user?.address?.countryCode) {
      countryName = countryCodes().filter(a => a.code == this.props.user?.address?.countryCode)[0]
        .name
      this.state.countryCode = this.props.user?.address?.countryCode
      this.state.selectedCountry = countryName
      const isoCountry = iso3166.country(this.props.user?.address?.countryCode)
      const subArr = Object.values(isoCountry.sub)
      pickableCountrySubs = subArr.map(a => a.name)
      pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
        if (a < b) return -1
        if (b < a) return 1
        return 0
      })
      this.state.pickableCountrySubs = pickableCountrySubs
    }

    if (this.props.user?.address?.province) {
      const regionCode = this.props.user?.address?.province

      console.log('regionCode', this.state.countryCode, regionCode)

      const subData = iso3166.subdivision(this.state.countryCode, regionCode)
      console.log('subData', subData)
      const selectedProvince = subData?.name

      this.state.selectedProvince = selectedProvince
      this.state.regionCode = regionCode

      this.jurisdictionContrains(this.state.countryCode, regionCode)
    }
  }

  componentDidMount() {
    let eventName = 'LoanRequest-Start-Android'
    if (Platform.OS === 'ios') {
      eventName = 'LoanRequest-Start-iOS'
    }
    this.props.dispatch(sendEvent(eventName))
    this.getLoanRatesMaps()
    this.adminFeeGet()
  }

  componentDidUpdate(prevProps) {
    //console.log('prevProps',prevProps);
  }

  adminFeeGet = async () => {
    this.props.WebService.getAdminFee()
      .then(res => {
        let adminFee = Number(res.data?.originationFee || '0.0')
        this.setState({adminFee, realAdminFee: adminFee})
      })
      .catch(err => {
        //
        console.log('fee err', err)
      })
  }

  getLoanRatesMaps = () => {
    this.props.WebService.getLoanRatesMaps()
      .then(res => {
        let show1Year = res.data.baseRates['12'] ? true : false
        let show3Year = res.data.baseRates['36'] ? true : false
        let show5Year = res.data.baseRates['60'] ? true : false

        let selectedLTV = 0.7
        if (show1Year) {
          const keys = Object.keys(res.data.baseRates['12'])
          if (keys.length === 1) {
            selectedLTV = keys[0]
          }
        } else if (show3Year) {
          const keys = Object.keys(res.data.baseRates['36'])
          if (keys.length === 1) {
            selectedLTV = keys[0]
          }
        } else if (show5Year) {
          const keys = Object.keys(res.data.baseRates['60'])
          if (keys.length === 1) {
            selectedLTV = keys[0]
          }
        }
        selectedLTV = Number(selectedLTV)

        console.log('getLoanRatesMaps', res.data)

        this.setState({
          aprFloors: res.data.aprFloors,
          baseRates: res.data.baseRates,
          rewardRates: res.data.rewardRates || null,
          rewardCeilings: res.data.rewardCeilings || null,
          show1Year,
          show3Year,
          show5Year,
          selectedLTV,
        })
      })
      .catch(err => {
        console.log('getLoanRatesMaps err', err)
      })
  }

  get getFeeRateWithDiscount() {
    const startFeeAmount = getOriginationFeeAmount(
      new BigNumber(this.state.loanAmount).toNumber(),
      0.01,
    )

    if (new BigNumber(startFeeAmount).isLessThan(new BigNumber(this.state.maxFee))) {
      if (+this.state.feeDiscount !== 0) {
        const feeRate = getFeeRateWithDiscount(
          new BigNumber(this.state.adminFee).toNumber(),
          new BigNumber(this.state.feeDiscount).toNumber(),
        )
        return new BigNumber(feeRate).isLessThan(0) ? 0 : feeRate
      } else {
        return this.state.adminFee
      }
    } else {
      if (+this.state.feeDiscount !== 0) {
        const feeRate = getFeeRateWithDiscount(
          +getOriginationFeeRate(
            new BigNumber(this.state.loanAmount).toNumber(),
            new BigNumber(this.state.maxFee).toNumber(),
          ),
          +this.state.feeDiscount,
        )
        return new BigNumber(feeRate).isLessThan(0) ? 0 : feeRate
      } else {
        return getOriginationFeeRate(
          new BigNumber(this.state.loanAmount).toNumber(),
          new BigNumber(this.state.maxFee).toNumber(),
        )
      }
    }
  }

  get feeAmount() {
    const feeAmount = new BigNumber(
      getOriginationFeeAmount(
        new BigNumber(this.state.loanAmount).toNumber(),
        new BigNumber(this.getFeeRateWithDiscount).toNumber(),
      ),
    )

    return feeAmount.isNaN() ? 0 : feeAmount.toNumber()
  }

  updateLoanAmount = (text = this.state.loanAmount) => {
    var letterRegExp = /[a-zA-Z]/g
    if (letterRegExp.test(text)) {
      return
    }
    let parsedText = text.replace('$', '')
    parsedText = parsedText.split(' ').join('')
    parsedText = parsedText.split(',').join('')

    if (parsedText == '.') {
      return
    }

    this.setState({loanAmount: parsedText}, () => {
      this.updatePayment()
      // Only run these operations when user has finished typing
      if (this.inputTimeout) clearTimeout(this.inputTimeout)
      this.inputTimeout = setTimeout(() => {
        this.setState({loading: true}, () => {
          Promise.all([
            this.jurisdictionContrains(this.state.countryCode, this.state.regionCode),
            this.checkIllegal(),
          ]).finally(() => {
            this.setState({loading: false})
          })
        })
      }, 1000) // 1 second delay after typing stops
    })
  }

  checkAmountWithContraints = () => {
    const loanAmount = this.state.loanAmount
    const min = this.state.contraintAmount.min
    const max = this.state.contraintAmount.max

    if (loanAmount < min) {
      //|| loanAmount > max
      this.setState({instaErrorAmount: true})
    } else {
      this.setState({instaErrorAmount: false})
    }
  }

  onSelect = selectedCountry => {
    const countryCode = countryCodes().filter(a => a.name == selectedCountry)[0].code
    const isoCountry = iso3166.country(countryCode)
    let pickableCountrySubs = []
    const subArr = Object.values(isoCountry.sub)
    pickableCountrySubs = subArr.map(a => a.name)
    pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
      if (a < b) return -1
      if (b < a) return 1
      return 0
    })
    this.setState({
      selectedCountry,
      countryCode,
      pickableCountrySubs,
      subArr,
      instaErrorAmount: false,
      selectedProvince: null,
      regionCode: null,
      submitError: false,
      usRestricted: false,
    })
  }

  onProvinceSelect = selectedProvince => {
    const subData = iso3166.subdivision(this.state.countryCode, selectedProvince)
    const regionCode = subData.regionCode
    this.setState({
      selectedProvince,
      regionCode,
      submitError: false,
      usRestricted: false,
    })
    this.jurisdictionContrains(this.state.countryCode, regionCode)
  }

  getLendableAreas = (country, province) =>
    this.props.WebService.getLendableAreas()
      .then(res => {
        if (country != 'us') {
          province = 'default'
        }
        let lendableRes = res.data[country]?.[province] || 'non_lendable'

        const {accountRef} = this.props
        const accounts = this.props?.user?.accounts
        let accountType = ''
        if (accounts) {
          accountType = this.props?.user?.accounts?.filter(a => a.ref == accountRef)[0]?.type || ''
        }

        if (accountType == 'personal') {
          if (lendableRes == 'lendable') {
            this.setState({lendableArea: true})
          } else {
            this.setState({lendableArea: false})
          }
        } else if (accountType == 'business') {
          if (lendableRes == 'lendable' || lendableRes == 'business_only') {
            this.setState({lendableArea: true})
          } else {
            this.setState({lendableArea: false})
          }
        }
      })
      .catch(err => {
        console.log('getLendableAreas err', country, err)
      })

  jurisdictionContrains = (countryCode = this.state.countryCode, regionCode) => {
    const {accountRef} = this.props
    const accounts = this.props?.user?.accounts
    let accountType
    if (accounts) {
      accountType = this.props?.user?.accounts?.filter(a => a.ref == accountRef)[0]?.type
    }

    const data = {
      type: accountType,
      countryCode,
      province: regionCode,
    }
    this.props.WebService.fetchJurisdictionInfo(data)
      .then(res => {
        this.setState({jurisdictionId: res.data.id})
      })
      .catch(err => {
        console.log('fetchJurisdictionInfo err', err)
      })

    const constraintData = queryType => ({
      type: accountType,
      country: this.state.countryCode.toLowerCase(),
      province: regionCode.toLowerCase(),
      ...(!new BigNumber(this.state.displayLoan.apr).isNaN() &&
        queryType !== 'apr' && {apr: this.state.displayLoan.apr.toString()}),
      ...(queryType !== 'amount' &&
        this.state.loanAmount && {
          amount: this.state.loanAmount.toString(),
        }),
      ...(this.state.selectedRepayment && {
        loanInterestType: this.state.selectedRepayment === 'principal' ? 'pni' : 'io',
      }),
    })
    this.findConstraint('amount', constraintData('amount'))
    this.findConstraint('term', constraintData('term'))
    this.findConstraint('fee', constraintData('fee'))
    this.findConstraint('apr', constraintData('apr'))
    this.checkIllegal(regionCode)

    // should wait for all 3 promises to resolve - then check
    //this.checkAmountWithContraints()
  }

  findConstraint = (query, constraintData) => {
    this.props.WebService.fetchLoanConstraints(query, constraintData)
      .then(res => {
        const constraint = {min: 1000, max: 2500000}
        if (query == 'term') {
          //just chose 1 of them
          this.getLendableAreas(constraintData.country, constraintData.province)
        }

        if (res.data) {
          res.data.map(a => {
            if (a.constraint == 'GTE') {
              constraint.min = Number(a.value)
            }
            if (a.constraint == 'GT') {
              constraint.min = Number(a.value) + (query == 'apr' ? 0 : 1)
            }
            if (a.constraint == 'LTE') {
              constraint.max = Number(a.value)
            }
            if (a.constraint == 'LT') {
              constraint.min = Number(a.value) - (query == 'apr' ? 0 : 1)
            }
          })

          if (query == 'fee') {
            this.setState({maxFee: constraint.max}, () => {
              const maxFee = constraint.max
              const startFeeAmount = new BigNumber(
                getOriginationFeeAmount(new BigNumber(this.state.loanAmount).toNumber(), 0.01),
              )
              let startFeeRate
              if (new BigNumber(startFeeAmount).isLessThan(new BigNumber(maxFee))) {
                if (+this.state.feeDiscount !== 0) {
                  startFeeRate = getFeeRateWithDiscount(
                    new BigNumber(this.state.realAdminFee).toNumber(),
                    new BigNumber(this.state.feeDiscount).toNumber(),
                  )
                } else {
                  startFeeRate = this.state.realAdminFee
                }

                this.setState({maxFee, adminFee: startFeeRate}, () => {
                  this.updatePayment()
                })
              } else {
                const loanAmount = new BigNumber(this.state.loanAmount).toNumber()
                const maxFeePercentage = new BigNumber(this.state.maxFee).dividedBy(loanAmount)

                // Use the smaller of the two: originationFee or maxFeePercentage
                this.setState(
                  {
                    maxFee,
                    adminFee: BigNumber.min(
                      new BigNumber(this.state.adminFee),
                      maxFeePercentage,
                    ).toString(),
                  },
                  () => {
                    this.updatePayment()
                  },
                )
              }
            })
          }
        } else {
          if (query === 'fee')
            this.setState({maxFee: 0, adminFee: 0}, () => {
              this.updatePayment()
            })
        }
        if (query == 'amount') {
          let max = res.data?.filter(a => a.constraint == 'LTE')[0]?.value || 2500000
          let min = res.data?.filter(a => a.constraint == 'GTE')[0]?.value || 1000
          let contraint = {min: Number(min), max: Number(max)}
          this.setState({contraintAmount: constraint}, () => {
            if (this.state.loanAmount != '') {
              this.checkAmountWithContraints()
            }
          })
        }
        if (query == 'apr') {
          this.setState({contraintApr: constraint})
        }
        if (query == 'term') {
          this.setState({contraintTerm: constraint})
        }
      })
      .catch(err => {
        console.log('fetchLoanConstraints err', err)
      })
  }

  selectTerm = termLength => {
    const termInMs = ********** * termLength
    if (termInMs < this.state.contraintTerm.min || termInMs > this.state.contraintTerm.max) {
      this.setState({instaErrorTerm: true})
    }
    this.setState({termLength}, () => {
      this.updateLoanAmount()
    })
  }

  selectLTV = ltv => {
    this.setState({selectedLTV: ltv}, () => {
      this.updateLoanAmount()
    })
  }

  selectRepayment = type => {
    this.setState({selectedRepayment: type}, () => {
      this.updateLoanAmount()
    })
  }

  newRate = (periods, payment, present, future, type, guess) => {
    guess = guess === undefined ? 0.01 : guess
    future = future === undefined ? 0 : future
    type = type === undefined ? 0 : type

    // Set maximum epsilon for end of iteration
    var epsMax = 1e-10

    // Set maximum number of iterations
    var iterMax = 10

    // Implement Newton's method
    var y,
      y0,
      y1,
      x0,
      x1 = 0,
      f = 0,
      i = 0
    var rate = guess
    if (Math.abs(rate) < epsMax) {
      y = present * (1 + periods * rate) + payment * (1 + rate * type) * periods + future
    } else {
      f = Math.exp(periods * Math.log(1 + rate))
      y = present * f + payment * (1 / rate + type) * (f - 1) + future
    }
    y0 = present + payment * periods + future
    y1 = present * f + payment * (1 / rate + type) * (f - 1) + future
    i = x0 = 0
    x1 = rate
    while (Math.abs(y0 - y1) > epsMax && i < iterMax) {
      rate = (y1 * x0 - y0 * x1) / (y1 - y0)
      x0 = x1
      x1 = rate
      if (Math.abs(rate) < epsMax) {
        y = present * (1 + periods * rate) + payment * (1 + rate * type) * periods + future
      } else {
        f = Math.exp(periods * Math.log(1 + rate))
        y = present * f + payment * (1 / rate + type) * (f - 1) + future
      }
      y0 = y1
      y1 = y
      ++i
    }
    return rate
  }

  updatePayment = () => {
    if (this.state.loanAmount == '' || this.state.loanAmount <= 0) {
      return
    }
    let {
      baseRates,
      rewardRates,
      termLength,
      selectedLTV,
      loanAmount,
      adminFee,
      feeDiscount,
      rateDiscount,
    } = this.state

    const isInterestOnly = this.state.selectedRepayment == 'interest'
    const monthsToMilli = ********** * termLength
    loanAmount = Number(loanAmount)

    let baseRate = getBaseRate(selectedLTV, baseRates, termLength)
    baseRate = Math.round(baseRate * 1000000) / 1000000

    let isLeveraged = this.props?.user?.accounts?.filter(a => a.ref == this.props.accountRef)[0]
      ?.isLeveraged

    let ldNon = this.props.launchDarkly['non-leveraged-loans'] || false
    if (!ldNon) {
      isLeveraged = true
    }

    console.log('test baseRate', baseRate)
    baseRate = baseRate - rateDiscount

    //bake in admin fee-
    adminFee = this.getFeeRateWithDiscount
    // getFeeRateWithDiscount(adminFee, feeDiscount)
    // adminFee - feeDiscount
    let loanPreFee = loanAmount
    let loanFee = this.feeAmount
    // loanAmount / (1 - adminFee) - loanAmount
    // loanFee = Number((Math.ceil(loanFee * 100) / 100).toFixed(2))
    loanAmount = getLoanAmount(loanAmount, adminFee)

    let decimalApr = getApr(loanAmount, selectedLTV, termLength, baseRate)
    console.log('decimalApr', decimalApr)

    const aprTo6 = decimalApr.toFixed(6)
    //payment
    let payment = 0
    if (isInterestOnly) {
      payment = getPayment(aprTo6, loanAmount, monthsToMilli, isInterestOnly)
    } else {
      console.log(
        'getPayment(baseRate, loanAmount, monthsToMilli, isInterestOnly)',
        baseRate,
        loanAmount,
        monthsToMilli,
        isInterestOnly,
      )
      payment = getPayment(baseRate, loanAmount, monthsToMilli, isInterestOnly)
    }

    //new adminFee aprs

    /*
    getSimpleInterest = (amountPlusFee, baseRate, term) => {
       const interestOnLoan = loanAmount * baseRate * (term / 12)
       return Math.round(interestOnLoan * 1000000) / 1000000
     }
 */
    let adminAPR = 0
    if (isInterestOnly) {
      let totalInterest = loanAmount * baseRate * (termLength / 12)
      totalInterest = Math.round(totalInterest * 1000000) / 1000000
      adminAPR = ((totalInterest + loanFee + 0) / loanAmount) * (12 / termLength)
      adminAPR = Math.round(adminAPR * 1000000) / 1000000
      //adminAPR = decimalApr + adminFee
    } else {
      console.log(
        'this.newRate(termLength, payment * -1, loanPreFee)',
        termLength,
        payment * -1,
        loanPreFee,
      )
      adminAPR = this.newRate(termLength, payment * -1, loanPreFee)
      adminAPR = Math.round(adminAPR * 1000000 * 12) / 1000000
    }
    adminAPR = adminAPR.toFixed(6)

    console.log('adminAPR', adminAPR)

    /*
    if (!isLeveraged) {
      payment = getPayment(0.0999, loanAmount, monthsToMilli, isInterestOnly)
    }
    */

    let sendBackendPayment = getPayment(baseRate, loanAmount, monthsToMilli, isInterestOnly)
    sendBackendPayment = sendBackendPayment.toFixed(2)
    this.setState({monthlyPayment: payment})
    payment = payment.toFixed(2)

    let loanTotal
    let interest
    let lastPayment

    if (isInterestOnly) {
      loanTotal = payment * termLength + loanAmount
      interest = (payment * termLength).toFixed(2)
      const lastPaymentMath = (Number(loanTotal) - Number(interest) + Number(payment)).toFixed(2)
      lastPayment = numberWithCommas(lastPaymentMath)
    } else {
      loanTotal = payment * termLength
      interest = (loanTotal - loanAmount).toFixed(2)
      lastPayment = '0'
    }

    //loanTotal + fee
    loanTotal = loanTotal + loanFee
    loanTotal = loanTotal.toFixed(2)
    let amountPlusFee = loanAmount.toFixed(2)
    const collateralNeeded = (loanAmount / selectedLTV).toFixed(2)

    //showApr
    const apr = Number(adminAPR).toFixed(6)
    const showApr = Number(apr * 100)
      .toFixed(3)
      .slice(0, -1)

    let showIR = (baseRate * 100)?.toFixed(2)
    const displayLoan = {
      payment: numberWithCommas(payment),
      loanAmount: numberWithCommas(loanPreFee),
      amountPlusFee: numberWithCommas(amountPlusFee),
      apr: Number(adminAPR),
      loanTotal: numberWithCommas(loanTotal),
      interest: numberWithCommas(interest),
      collateralNeeded: numberWithCommas(collateralNeeded),
      saltNeeded: 0,
      lastPayment,
      decimalApr,
      interestRate: baseRate,
      showIR,
      showApr,
      loanFee: numberWithCommas(loanFee),
    }
    this.setState({displayLoan, submitPayment: sendBackendPayment}, () => {
      this.checkIllegal()
    })
  }

  submitLoan = async () => {
    this.setState({submitLoading: true})

    let isTestAcc = this.props?.user?.primaryEmail == '<EMAIL>'
    if (isTestAcc) {
      this.props.navigation.goBack()
      return
    }

    let monthsToMilli = ********** * this.state.termLength
    monthsToMilli = monthsToMilli.toString()

    // first check legality - checkLoanLegality

    let accountType = this.props.user?.accounts?.filter(a => a.ref == this.props.accountRef)[0]
      ?.type
    let isLeveraged = this.props?.user?.accounts?.filter(a => a.ref == this.props.accountRef)[0]
      ?.isLeveraged
    const data = {
      type: accountType,
      countryCode: this.state.countryCode.toLowerCase(),
      province: this.state.regionCode.toLowerCase(),
      term: monthsToMilli,
    }

    console.log('checkLoanLegality', data)
    this.props.WebService.checkLoanLegality(data)
      .then(res => {
        console.log('checkLoanLegality res', res, data)
      })
      .catch(err => {
        console.log('checkLoanLegality err', err, data)
      })

    console.log('after')

    //if not legal - create non-lendable loan req

    console.log('xdd', this.props.tokenPrices)
    const saltPriceTimeStamp = this.props.tokenPrices['SALT-USD'].time
    console.log('1', saltPriceTimeStamp)

    let rewardRate = parseFloat((this.state.showRewardRate / 100).toFixed(6)).toString()
    console.log('1.1')

    let ldNon = this.props.launchDarkly['non-leveraged-loans'] || false
    if (!isLeveraged && ldNon) {
      rewardRate = '0'
    }
    console.log('1.2')

    let submitAmount = this.state.displayLoan.amountPlusFee.toString()?.split(',')?.join('') || '0'
    let submitFeeAmount = this.state.displayLoan.loanFee?.toString()?.split(',')?.join('') || '0'
    let submitAmountAfterFee = this.state.loanAmount?.toString()?.split(',')?.join('') || '0'
    console.log('2')

    //rate or fee discount

    let sendRate = this.state.displayLoan.interestRate
    /*
    console.log('sendRate', sendRate)
    if (this.state.rateDiscount) {
      sendRate = sendRate - this.state.rateDiscount
    }
    console.log('sendRate after - ', sendRate)
    */

    let cleanFloat = Number((sendRate + Number.EPSILON).toFixed(15))
    console.log('apr: ', this.state.displayLoan)
    let requestData = {
      apr: this.state.displayLoan.apr?.toFixed(6),
      interestRate: cleanFloat.toString(),
      amount: submitAmount,
      baseLTV: this.state.selectedLTV.toString(),
      interestOnly: this.state.selectedRepayment == 'interest',
      loanAsset: 'USD',
      payment: this.state.submitPayment,
      saltStaked: 0,
      saltRedeemed: this.state.redeemedSalt,
      saltPriceTimeStamp,
      term: this.state.termLength,
      jurisdictionId: this.state.jurisdictionId,
      affiliateCode: this.state.codeError ? '' : this.state.affiliateCode,
      originationFeeRate: new BigNumber(this.getFeeRateWithDiscount).dp(6) || 0,
      originationFeeAmount: submitFeeAmount,
      loanAmountAfterFee: submitAmountAfterFee,
    }

    console.log('requestData', requestData)

    this.props.WebService.submitLoanRequest(requestData)
      .then(res => {
        this.setState({submitLoading: false})

        //turn on unit21 just incase
        AsyncStorage.removeItem('PAUSE_UNIT_21')
        this.props.dispatch(pauseUnit21(false))

        //analytics
        let eventName = 'LoanRequest-Complete-Android'
        if (Platform.OS === 'ios') {
          eventName = 'LoanRequest-Complete-iOS'
        }
        this.props.dispatch(sendEvent(eventName))

        //refresh to home
        let {accountRef} = this.props
        this.props.dispatch(updateLoansDrill(res.data))
        this.props.dispatch(updateLoansInOneUser(res.data, accountRef))
        this.props.navigation.goBack()
        this.props.dispatch(afterNewLoan())
      })
      .catch(err => {
        console.log('submit err', err)
        let submitError = 'Loan Error'
        if (typeof err.data.body.error === 'string') {
          submitError = err.data.body.error
        }
        //
        this.setState({
          submitLoading: false,
          submitError,
        })
      })
  }

  nonLendableLoanReq = () => {
    const accounts = this.props?.user?.accounts
    let accountType
    if (accounts) {
      accountType = this.props?.user?.accounts?.filter(a => a.ref == this.props.accountRef)[0]?.type
    }
    const data = {
      amount: this.state.loanAmount.toString(),
      type: accountType,
      countryCode: this.state.countryCode,
      province: this.state.regionCode,
      shouldNotify: true,
    }
    this.props.WebService.nonLendableLoanReq(data)
      .then(res => {
        this.props.dispatch(increaseRefreshDataCount())
        this.props.navigation.popToTop()
      })
      .catch(err => {
        console.log('nonLendableLoanReq err', err)
      })
  }

  submitCode = () => {
    this.setState({codeLoading: true, codeError: false})
    setTimeout(() => {
      this.props.WebService.validateAffiliate(this.state.affiliateCode)
        .then(res => {
          console.log('validateAffiliate', res.data)
          let rateDiscount = Number(res.data.rateDiscount)
          console.log('rateDiscount', rateDiscount)
          let feeDiscount = Number(res.data.originationFeeDiscount)
          console.log('feeDiscount', feeDiscount)

          this.setState(
            {
              codeLoading: false,
              rateDiscount,
              feeDiscount,
              redeemedSalt: 0,
              showSalt: false,
            },
            () => {
              this.updateLoanAmount()
            },
          )
        })
        .catch(err => {
          console.log('validateAffiliate err', err)
          this.setState({
            codeLoading: false,
            codeError: true,
            rateDiscount: 0,
          })
        })
    }, 1000)
  }

  updateCode = text => {
    text = text.replace(' ', '')
    this.setState({affiliateCode: text})
  }

  removeCode = () => {
    let showSalt = true
    if (this.state.selectedLTV == 0.7) {
      showSalt = false
    }
    this.setState(
      {
        affiliateCode: '',
        codeLoading: false,
        rateDiscount: 0,
        feeDiscount: 0,
        codeError: false,
        showSalt,
      },
      () => {
        this.updateLoanAmount()
      },
    )
  }

  closeToHome = () => {
    this.props.dispatch(increaseRefreshDataCount())
    this.props.navigation.popToTop()
  }

  openPromoTerms = () => {
    this.props.dispatch(askingForPermissions(true))
    Linking.openURL(`https://saltlending.com/promo-code-terms-and-conditions/`)
  }

  submitNonLendable = async () => {
    this.setState({nonLendError: false})
    let {loanAmount, countryCode, regionCode} = this.state
    let {accountRef} = this.props
    let accountType = this.props?.user?.accounts?.filter(a => a.ref == accountRef)[0]?.type
    let payload = {
      amount: loanAmount || 5000,
      type: accountType,
      countryCode: countryCode,
      province: regionCode,
      shouldNotify: false,
    }
    this.props.WebService.submitNonLendable(payload)
      .then(res => {
        this.setState({showNonLendableSuccess: true})
      })
      .catch(err => {
        this.setState({nonLendError: true})
        console.log('err non lendable', err)
      })
  }

  checkIllegal = async (regionCode = this.state.regionCode) => {
    let {loanAmount, displayLoan, selectedRepayment, countryCode, termLength} = this.state
    let {accountRef} = this.props
    let loanInterestType = selectedRepayment == 'interest' ? 'io' : 'pni'
    let monthsToMilli = ********** * termLength
    let accountType = this.props?.user?.accounts?.filter(a => a.ref == accountRef)[0]?.type
    let payload = {
      amount: loanAmount,
      apr: displayLoan?.apr?.toString(),
      country: countryCode?.toLowerCase(),
      province: regionCode?.toLowerCase(),
      loanInterestType,
      term: `${monthsToMilli}`,
      type: accountType,
      fee: this.feeAmount.toString(),
    }
    console.log('checkIllegal', payload)
    this.props.WebService.checkIllegal({loan: payload})
      .then(res => {
        if (res?.data === null) {
          this.setState({illegalModal: true})
        } else {
          this.setState({illegalModal: false})
        }
      })
      .catch(err => {
        console.log('checkIllegal err', err)
      })
  }

  goLink = () => {
    this.props.dispatch(askingForPermissions(true))
    Linking.openURL('https://saltlending.com/rates-and-fees').catch(err =>
      console.error('An error occurred', err),
    )
  }

  render() {
    const {accountRef} = this.props
    const accounts = this.props?.user?.accounts
    let accountType
    let isLeveraged
    if (accounts) {
      accountType = this.props?.user?.accounts?.filter(a => a.ref == accountRef)[0]?.type
      isLeveraged = this.props?.user?.accounts?.filter(a => a.ref == accountRef)[0]?.isLeveraged
    }

    let launchDarkly = this.props.launchDarkly

    let banned = {
      BTC: launchDarkly['disable-btc-deposit'] || false,
      ETH: launchDarkly['disable-eth-deposit'] || false,
      BCH: launchDarkly['disable-bch-deposit'] || false,
      LTC: launchDarkly['disable-ltc-deposit'] || false,
      USDC: launchDarkly['disable-usdc-deposit'] || false,
      USDT: launchDarkly['disable-usdt-deposit'] || false,
      USDP: launchDarkly['disable-usdp-deposit'] || false,
      SALT: launchDarkly['disable-salt-deposit'] || false,
      TUSD: launchDarkly['disable-tusd-deposit'] || false,
      XRP: launchDarkly['disable-xrp-deposit'] || true,
      DASH: launchDarkly['disable-dash-deposit'] || true,
      DOGE: launchDarkly['disable-doge-deposit'] || true,
      PAXG: launchDarkly['disable-paxg-deposit'] || true,
    }

    let showAllCollaterals = listPossibleWallets()
    showAllCollaterals = showAllCollaterals?.filter(a => a != 'PAXG' && a != 'SALT' && !banned[a])
    let customOrder = ['BTC', 'ETH', 'USDT', 'USDC', 'LTC', 'BCH', 'USDP', 'TUSD']

    showAllCollaterals = showAllCollaterals.filter(a => !banned[a])

    showAllCollaterals = showAllCollaterals.sort((a, b) => {
      const indexA = customOrder.indexOf(a) !== -1 ? customOrder.indexOf(a) : 999
      const indexB = customOrder.indexOf(b) !== -1 ? customOrder.indexOf(b) : 999
      if (indexA !== 999 || indexB !== 999) {
        return indexA - indexB
      }
      if (a < b) {
        return -1
      }
      if (a > b) {
        return 1
      }
      return 0
    })
    showAllCollaterals = showAllCollaterals.map((a, k) => {
      const showImg = getTokenPic(a)
      return <Image key={k} style={styles.loanRequestCollateralImg} source={showImg} />
    })

    const constrainMin = numberWithCommas(this.state.contraintAmount.min)
    const constrainMax = numberWithCommas(this.state.contraintAmount.max)

    let constrainTermMin = numberWithCommas(this.state.contraintTerm.min / **********)
    constrainTermMin = Math.round(constrainTermMin)
    let constrainTermMax = numberWithCommas(this.state.contraintTerm.max / **********)
    constrainTermMax = Math.round(constrainTermMax)
    if (constrainTermMax == 0) {
      constrainTermMax = constrainTermMin
      constrainTermMin = '0'
    }

    let redeemSaltHeight = 40
    if (Platform.OS === 'ios') {
      redeemSaltHeight = 30
    }

    let {
      selectedRepayment,
      termLength,
      monthlyPayment,
      loanAmount,
      lendableArea,
      showNonLendableSuccess,
      illegalModal,
      show1Year,
      show3Year,
      show5Year,
      baseRates,
    } = this.state

    const isInterestOnly = selectedRepayment == 'interest'
    const monthsToMilli = ********** * termLength
    loanAmount = Number(loanAmount)

    //v1

    let showPaymentMinusReward = 0

    if (isInterestOnly) {
      showPaymentMinusReward =
        getPayment(this.state.displayLoan.apr, loanAmount, monthsToMilli, isInterestOnly) || 0
    } else {
      showPaymentMinusReward = getPayment('0.0999', loanAmount, monthsToMilli, isInterestOnly) || 0
    }

    showPaymentMinusReward = numberWithCommas(showPaymentMinusReward.toFixed(2))
    if (this.props.launchDarkly['disable-stack-wise']) {
      showPaymentMinusReward = this.state.displayLoan?.payment
    }

    let disclaimerStackText = `*Monthly payment is calculated using a 9.99% interest rate.`
    let disclaimerWidth = 230
    if (this.state.selectedRepayment == 'interest' && this.state.selectedLTV !== 0.7) {
      disclaimerWidth = 280
      disclaimerStackText = `*Monthly payment assumes StackWise rewards applied to loan payment.`
    }

    let ldNon = this.props.launchDarkly['non-leveraged-loans'] || false
    if (!ldNon) {
      isLeveraged = true
    }

    let showFeePercent = new BigNumber(this.getFeeRateWithDiscount).multipliedBy(100).toFixed(2)

    let referralCode = this.props.user?.referredByCode
    let isCel = referralCode == 'CELREFI23'

    let show20 = false
    let show30 = false
    let show50 = false
    let show70 = false
    if (show1Year) {
      if (baseRates[12][0.2]) {
        show20 = true
      }
      if (baseRates[12][0.3]) {
        show30 = true
      }
      if (baseRates[12][0.5]) {
        show50 = true
      }
      if (baseRates[12][0.7]) {
        show70 = true
      }
    } else if (show3Year) {
      if (baseRates[36][0.2]) {
        show20 = true
      }
      if (baseRates[36][0.3]) {
        show30 = true
      }
      if (baseRates[36][0.5]) {
        show50 = true
      }
      if (baseRates[36][0.7]) {
        show70 = true
      }
    } else if (show5Year) {
      if (baseRates[60][0.2]) {
        show20 = true
      }
      if (baseRates[60][0.3]) {
        show30 = true
      }
      if (baseRates[60][0.5]) {
        show50 = true
      }
      if (baseRates[60][0.7]) {
        show70 = true
      }
    }

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader
          title={'Loan Request'}
          goBack={showNonLendableSuccess ? null : this.props.navigation.goBack}
        />
        {showNonLendableSuccess ? (
          <NonLendableSuccess closeToHome={this.closeToHome} isCel={isCel} />
        ) : (
          <ScrollView
            ref={ref => {
              this.scroll = ref
            }}
            style={{
              backgroundColor: '#28283D',
              alignSelf: 'stretch',
            }}
            contentContainerStyle={{alignItems: 'center'}}>
            <Card style={{backgroundColor: '#3D3D50'}}>
              <View
                style={{
                  alignSelf: 'stretch',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginTop: 20,
                }}
              />
              <View style={{width: 300}}>
                <TextReg style={styles.loanRequestInputTitle}>How much money do you need?</TextReg>
                <TextInput
                  style={
                    this.state.instaErrorAmount
                      ? styles.loanRequestInputError
                      : styles.loanRequestInput
                  }
                  onChangeText={text => this.updateLoanAmount(text)}
                  ref={input => (this.inputs.loanAmount = input)}
                  value={`$ ${numberWithCommas(this.state.loanAmount)}`}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={'$'}
                  onBlur={this.checkAmountWithContraints}
                  onSubmitEditing={this.checkAmountWithContraints}
                  keyboardType={'numeric'}
                  keyboardAppearance="dark"
                />
                {this.state.instaErrorAmount && (
                  <TextReg
                    style={{
                      marginTop: -14,
                      textAlign: 'center',
                      color: '#E5705A',
                      marginBottom: 10,
                    }}>
                    {`Amount needs to be between $${constrainMin} - $${constrainMax}`}
                  </TextReg>
                )}
                <TextReg style={styles.loanRequestInputTitle}>
                  {accountType == 'personal'
                    ? 'Where do you live?'
                    : 'Where is your business located?'}
                </TextReg>
                <LocationSelect
                  options={this.state.countryCodes}
                  onSelect={this.onSelect}
                  placeholder={this.state.selectedCountry ? this.state.selectedCountry : 'Country'}
                />
                <LocationSelect
                  onSelect={this.onProvinceSelect}
                  options={this.state.pickableCountrySubs}
                  placeholder={
                    this.state.selectedProvince ? this.state.selectedProvince : 'State / Province'
                  }
                />
                <View style={{height: 4, widht: 300}} />
                {lendableArea && (
                  <>
                    <TextReg style={[styles.loanRequestInputTitle, {marginBottom: 10}]}>
                      How long do you need to pay back?
                    </TextReg>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignSelf: 'stretch',
                      }}>
                      {show1Year && (
                        <TouchableOpacity
                          style={{flex: 1, marginRight: show3Year || show5Year ? 8 : 0}}
                          onPress={() => {
                            this.selectTerm(12)
                          }}>
                          <View
                            style={
                              this.state.termLength === 12
                                ? styles.loanRequestTermBoxActive
                                : styles.loanRequestTermBox
                            }>
                            {this.state.termLength === 12 ? (
                              <TextBold style={styles.loanRequestLTVTextActive}>1 year</TextBold>
                            ) : (
                              <TextReg style={styles.loanRequestLTVText}>1 year</TextReg>
                            )}
                          </View>
                        </TouchableOpacity>
                      )}
                      {this.state.show3Year && (
                        <TouchableOpacity
                          style={{flex: 1, marginRight: show5Year ? 8 : 0}}
                          onPress={() => {
                            this.selectTerm(36)
                          }}>
                          <View
                            style={
                              this.state.termLength === 36
                                ? styles.loanRequestTermBoxActive
                                : styles.loanRequestTermBox
                            }>
                            {this.state.termLength === 36 ? (
                              <TextBold style={styles.loanRequestLTVTextActive}>3 years</TextBold>
                            ) : (
                              <TextReg style={styles.loanRequestLTVText}>3 years</TextReg>
                            )}
                          </View>
                        </TouchableOpacity>
                      )}
                      {this.state.show5Year && (
                        <TouchableOpacity
                          style={{flex: 1}}
                          onPress={() => {
                            this.selectTerm(60)
                          }}>
                          <View
                            style={
                              this.state.termLength === 60
                                ? styles.loanRequestTermBoxActive
                                : styles.loanRequestTermBox
                            }>
                            {this.state.termLength === 60 ? (
                              <TextBold style={styles.loanRequestLTVTextActive}>5 years</TextBold>
                            ) : (
                              <TextReg style={styles.loanRequestLTVText}>5 years</TextReg>
                            )}
                          </View>
                        </TouchableOpacity>
                      )}
                    </View>

                    {this.state.instaErrorTerm && (
                      <TextReg
                        style={{
                          marginTop: 14,
                          textAlign: 'center',
                          color: '#E5705A',
                        }}>
                        {`Term needs to be between ${constrainTermMin} - ${constrainTermMax} months`}
                      </TextReg>
                    )}
                    <View style={{height: 14, widht: 300}} />
                    <TextReg style={styles.loanRequestInputTitle}>Loan-to-Value (LTV)</TextReg>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignSelf: 'stretch',
                      }}>
                      {isLeveraged && show20 && (
                        <TouchableOpacity
                          style={{flex: 1, marginRight: show30 || show50 || show70 ? 8 : 0}}
                          onPress={() => {
                            this.selectLTV(0.2)
                          }}>
                          <View
                            style={
                              this.state.selectedLTV === 0.2
                                ? styles.loanRequestLTVBoxActive
                                : styles.loanRequestLTVBox
                            }>
                            {this.state.selectedLTV === 0.2 ? (
                              <TextBold style={styles.loanRequestLTVTextActive}>20%</TextBold>
                            ) : (
                              <TextReg style={styles.loanRequestLTVText}>20%</TextReg>
                            )}
                          </View>
                        </TouchableOpacity>
                      )}
                      {isLeveraged && show30 && (
                        <TouchableOpacity
                          style={{flex: 1, marginRight: show50 || show70 ? 8 : 0}}
                          onPress={() => {
                            this.selectLTV(0.3)
                          }}>
                          <View
                            style={
                              this.state.selectedLTV === 0.3
                                ? styles.loanRequestLTVBoxActive
                                : styles.loanRequestLTVBox
                            }>
                            {this.state.selectedLTV === 0.3 ? (
                              <TextBold style={styles.loanRequestLTVTextActive}>30%</TextBold>
                            ) : (
                              <TextReg style={styles.loanRequestLTVText}>30%</TextReg>
                            )}
                          </View>
                        </TouchableOpacity>
                      )}
                      {show50 && (
                        <TouchableOpacity
                          style={{flex: 1, marginRight: show70 ? 8 : 0}}
                          onPress={() => {
                            this.selectLTV(0.5)
                          }}>
                          <View
                            style={
                              this.state.selectedLTV === 0.5
                                ? styles.loanRequestLTVBoxActive
                                : styles.loanRequestLTVBox
                            }>
                            {this.state.selectedLTV === 0.5 ? (
                              <TextBold style={styles.loanRequestLTVTextActive}>50%</TextBold>
                            ) : (
                              <TextReg style={styles.loanRequestLTVText}>50%</TextReg>
                            )}
                          </View>
                        </TouchableOpacity>
                      )}
                      {!isLeveraged && (
                        <View
                          style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: 240,
                            opacity: 0.7,
                          }}>
                          <TextReg>
                            Non-Leveraged loan account types only have access to a 50% LTV
                          </TextReg>
                        </View>
                      )}
                      {isLeveraged && show70 && (
                        <TouchableOpacity
                          style={{flex: 1}}
                          onPress={() => {
                            this.selectLTV(0.7)
                          }}>
                          <View
                            style={
                              this.state.selectedLTV === 0.7
                                ? styles.loanRequestLTVBoxActive
                                : styles.loanRequestLTVBox
                            }>
                            {this.state.selectedLTV === 0.7 ? (
                              <TextBold style={styles.loanRequestLTVTextActive}>70%</TextBold>
                            ) : (
                              <TextReg style={styles.loanRequestLTVText}>70%</TextReg>
                            )}
                          </View>
                        </TouchableOpacity>
                      )}
                    </View>
                  </>
                )}
              </View>
              {lendableArea && (
                <>
                  <View style={{height: 20, widht: 300}} />
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      width: 300,
                      marginBottom: 8,
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        this.selectRepayment('principal')
                      }}>
                      <View
                        style={
                          this.state.selectedRepayment === 'principal'
                            ? styles.loanRequestRepaymentBoxActive
                            : styles.loanRequestRepaymentBox
                        }>
                        {this.state.selectedRepayment === 'principal' ? (
                          <TextBold style={{fontSize: 15, color: '#00FFBD'}}>
                            Principal & Interest
                          </TextBold>
                        ) : (
                          <TextReg style={{fontSize: 15, color: '#FFF'}}>
                            Principal & Interest
                          </TextReg>
                        )}
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        this.selectRepayment('interest')
                      }}>
                      <View
                        style={
                          this.state.selectedRepayment === 'interest'
                            ? styles.loanRequestRepaymentBoxActive
                            : styles.loanRequestRepaymentBox
                        }>
                        {this.state.selectedRepayment === 'interest' ? (
                          <TextBold style={{fontSize: 15, color: '#00FFBD'}}>
                            Interest Only
                          </TextBold>
                        ) : (
                          <TextReg style={{fontSize: 15, color: '#FFF'}}>Interest Only</TextReg>
                        )}
                      </View>
                    </TouchableOpacity>
                  </View>
                  <View style={{width: 300, marginTop: 20}}>
                    <TextReg style={styles.loanRequestInputTitle}>Promo Code</TextReg>
                    <View
                      style={[
                        this.state.codeError
                          ? styles.loanRequestInputError
                          : styles.loanRequestInput,
                        {
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          height: 46,
                          opacity: this.state.rateDiscount != '' ? 0.7 : 1,
                        },
                      ]}>
                      <TextInput
                        style={{
                          width: 200,
                          height: 46,
                          fontSize: 16,
                          opacity: this.state.rateDiscount != '' ? 0.6 : 1,
                          color: '#fff',
                        }}
                        onChangeText={text => this.updateCode(text)}
                        ref={input => (this.inputs.updateCode = input)}
                        value={`${this.state.affiliateCode}`}
                        underlineColorAndroid="transparent"
                        blurOnSubmit
                        returnKeyType={'next'}
                        placeholder={'012345'}
                        onBlur={this.submitCode}
                        onSubmitEditing={this.submitCode}
                        keyboardAppearance="dark"
                      />
                      {this.state.codeLoading && (
                        <LottieView
                          ref={animation => {
                            this.animation = animation
                          }}
                          style={{
                            width: 30,
                            height: 30,
                            opacity: 0.8,
                            zIndex: 5,
                            marginRight: 6,
                            alignSelf: 'center',
                            marginTop: -2,
                          }}
                          source={require('../../imgs/lotti/loading-white-dots.json')}
                          autoPlay
                        />
                      )}
                      {(this.state.rateDiscount != '' ||
                        this.state.feeDiscount != '' ||
                        this.state.codeError) && (
                        <TouchableOpacity onPress={this.removeCode}>
                          <Image
                            source={require('../../imgs/closeX.png')}
                            style={{height: 22, width: 22, marginRight: 6}}
                          />
                        </TouchableOpacity>
                      )}
                    </View>
                    <TouchableOpacity onPress={() => this.openPromoTerms()}>
                      <TextReg style={styles.promoTermsText}>{`TERMS & CONDITIONS`}</TextReg>
                    </TouchableOpacity>

                    <Discounts
                      rateDiscount={this.state.rateDiscount}
                      feeDiscount={this.state.feeDiscount}
                    />
                  </View>
                </>
              )}
              {lendableArea === false && (
                <View
                  style={{width: 300, borderRadius: 14, backgroundColor: '#3c535d', padding: 10}}>
                  <View style={{flexDirection: 'row'}}>
                    <Image
                      source={areaArrow}
                      style={{height: 30, width: 23, marginLeft: 6, marginTop: 4}}
                    />
                    <View style={{flexDirection: 'column', marginLeft: 4, marginRight: 4}}>
                      <TextBold style={{width: 240, marginLeft: 10}}>
                        {`We are currently unable to lend in your area.`}
                        <TextReg>{` However, we prioritize adding new locations by demand, so please still submit your information. We’ll notify you when we can service your area.`}</TextReg>
                      </TextBold>
                    </View>
                  </View>
                </View>
              )}
            </Card>
            {illegalModal && lendableArea && (
              <View
                style={{
                  alignSelf: 'stretch',
                  backgroundColor: '#28283D',
                  alignItems: 'center',
                  paddingTop: 4,
                }}>
                <Card style={{backgroundColor: '#3D3D50', marginBottom: 20}}>
                  <Image
                    source={areaArrow2}
                    style={{height: 50, width: 50, marginTop: 10, marginBottom: 10}}
                  />
                  <TextBold
                    style={{
                      textAlign: 'center',
                      width: 280,
                      fontSize: 18,
                      marginBottom: 24,
                    }}>{`We have pricing restrictions in your area. Try adjusting your loan amount, repayment type, Loan-to-Value (LTV), and/or redeeming more SALT.`}</TextBold>
                </Card>
              </View>
            )}
            {!illegalModal && lendableArea && (
              <>
                <View style={{height: 20, widht: 300}} />
                <View
                  style={{
                    alignSelf: 'stretch',
                    backgroundColor: '#28283D',
                    alignItems: 'center',
                    paddingTop: 10,
                  }}>
                  <Card style={{backgroundColor: '#3D3D50'}}>
                    <View style={styles.loanRequestSummaryRow}>
                      <TextReg style={styles.loanRequestSummaryText}>Monthly Payment</TextReg>
                      <TextBold
                        style={
                          styles.loanRequestSummaryText
                        }>{`$${showPaymentMinusReward}`}</TextBold>
                    </View>

                    {this.state.selectedRepayment == 'interest' && (
                      <View style={styles.loanRequestSummaryRow}>
                        <TextReg style={styles.loanRequestSummaryText}>Last Payment Amount</TextReg>
                        <TextBold
                          style={
                            styles.loanRequestSummaryText
                          }>{`$${this.state.displayLoan.lastPayment}`}</TextBold>
                      </View>
                    )}
                    <View style={styles.loanRequestSummaryRow}>
                      <TextReg style={styles.loanRequestSummaryText}>Interest</TextReg>
                      <TextBold style={styles.loanRequestSummaryText}>{`${
                        this.state.displayLoan.showIR || '0'
                      }%`}</TextBold>
                    </View>
                    <View style={styles.loanRequestSummaryRow}>
                      <TextReg style={styles.loanRequestSummaryText}>Origination Fee</TextReg>
                      <TextBold
                        style={styles.loanRequestSummaryText}>{`${showFeePercent}%`}</TextBold>
                    </View>
                    <View style={styles.loanRequestSummaryRow}>
                      <TextReg style={styles.loanRequestSummaryText}>APR</TextReg>
                      <TextBold
                        style={
                          styles.loanRequestSummaryText
                        }>{`${this.state.displayLoan.showApr}%`}</TextBold>
                    </View>
                    <View style={{height: 40, alignSelf: 'stretch'}}></View>
                    <View style={styles.loanRequestSummaryRow}>
                      <TextReg style={styles.loanRequestSummaryText}>Loan Amount</TextReg>
                      <TextBold
                        style={
                          styles.loanRequestSummaryText
                        }>{`$${this.state.displayLoan.amountPlusFee}`}</TextBold>
                    </View>
                    <View style={styles.loanRequestSummaryRow}>
                      <TextReg style={styles.loanRequestSummaryText}>Origination Fee</TextReg>
                      <TextBold
                        style={
                          styles.loanRequestSummaryText
                        }>{`-$${this.state.displayLoan.loanFee}`}</TextBold>
                    </View>
                    <View style={styles.loanRequestSummaryRowLast}>
                      <TextBold style={styles.loanRequestSummaryText}>Amount Received</TextBold>
                      <TextBold
                        style={
                          styles.loanRequestSummaryText
                        }>{`$${this.state.displayLoan.loanAmount}`}</TextBold>
                    </View>
                    <TextReg
                      style={{
                        width: 294,
                        marginTop: 20,
                        marginBottom: 50,
                      }}>{`*We must estimate the finance charge, total of payments, the payment schedule and the annual percentage rate because we do not know exactly when the applicable loan proceeds will be disbursed. Our estimate is based on a loan origination on the 15th calendar day of the month.`}</TextReg>

                    {!this.props.launchDarkly[`disable-stack-wise`] && (
                      <TextReg
                        style={{
                          width: disclaimerWidth,
                          textAlign: 'center',
                          fontSize: 12,
                          opacity: 0.7,
                          marginTop: -10,
                          marginBottom: -10,
                        }}>
                        {disclaimerStackText}
                      </TextReg>
                    )}

                    <TextReg
                      style={{
                        fontSize: 19,
                        marginTop: !this.props.launchDarkly[`disable-stack-wise`] ? 26 : 8,
                        marginBottom: 4,
                        color: '#FFF',
                      }}>
                      Collateral Needed
                    </TextReg>
                    <TextBold style={{fontSize: 19, marginBottom: 6, color: '#FFF'}}>
                      {`$${this.state.displayLoan.collateralNeeded} of USD`}
                    </TextBold>
                    <TextReg style={{marginBottom: 10, color: '#FFF'}}>worth of:</TextReg>
                    <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
                      <View
                        style={{
                          flexDirection: 'row',
                          flexWrap: 'wrap',
                          width: 250,
                          justifyContent: 'center',
                        }}>
                        {showAllCollaterals}
                      </View>
                    </View>

                    <TouchableOpacity
                      onPress={() =>
                        this.setState({agreeRatesAndFees: !this.state.agreeRatesAndFees})
                      }>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          alignSelf: 'stretch',
                          marginTop: 14,
                          marginBottom: 14,
                        }}>
                        {this.state.agreeRatesAndFees ? (
                          <Image
                            source={require('../../imgs/referrals/checkedBox.png')}
                            style={{height: 20, width: 20, marginRight: 10}}
                          />
                        ) : (
                          <Image
                            source={require('../../imgs/referrals/unchecked.png')}
                            style={{height: 20, width: 20, marginRight: 10}}
                          />
                        )}
                        <TextReg style={{color: '#FFF'}}>I have read and agree to the </TextReg>
                        <TouchableOpacity onPress={() => this.goLink()}>
                          <TextReg style={{color: '#00FFBD'}}>Rates and Fees</TextReg>
                        </TouchableOpacity>
                      </View>
                    </TouchableOpacity>
                    <View
                      style={{
                        alignItems: 'center',
                        marginBottom: 20,
                        marginTop: 14,
                        alignSelf: 'stretch',
                      }}>
                      <Button
                        style={{alignSelf: 'stretch', marginLeft: 20, marginRight: 20}}
                        disabled={
                          this.state.loanAmount == '' ||
                          this.state.countryCode == null ||
                          this.state.regionCode == null ||
                          this.state.instaErrorAmount ||
                          this.state.usRestricted ||
                          !this.state.lendableArea ||
                          !this.state.agreeRatesAndFees ||
                          this.state.loading
                        }
                        isLoading={this.state.submitLoading}
                        onPress={this.submitLoan}>
                        <TextReg
                          style={{
                            fontSize: 18,
                            letterSpacing: 0.7,
                            color: '#000',
                            alignSelf: 'stretch',
                          }}>{`SUBMIT REQUEST`}</TextReg>
                      </Button>
                      {this.state.submitError && (
                        <View style={styles.showErrorBox}>
                          <TextReg style={styles.showErrorText}>{this.state.submitError}</TextReg>
                        </View>
                      )}
                      {!this.state.lendableArea && (
                        <View style={styles.showErrorBox}>
                          <TextReg style={styles.showErrorText}>{'Non-lendable Area'}</TextReg>
                        </View>
                      )}
                    </View>
                  </Card>
                </View>
              </>
            )}
            {lendableArea === false && (
              <>
                <Card
                  style={{
                    backgroundColor: '#3D3D50',
                    borderWidth: 1,
                    borderColor: '#00FFBD',
                    marginBottom: 30,
                  }}>
                  <View
                    style={{
                      alignSelf: 'stretch',
                      paddingLeft: 16,
                      paddingRight: 16,
                      paddingTop: 10,
                      paddingBottom: 10,
                    }}>
                    <Image source={areaArrow2} style={{height: 50, width: 50}} />
                    <TextBold
                      style={{
                        fontSize: 21,
                        width: 270,
                        marginTop: 10,
                      }}>{`Thank you for letting us know you're looking to borrow in this jurisdiction.`}</TextBold>
                    <TextReg
                      style={{
                        marginTop: 10,
                      }}>{`We'll keep your info on file and reach out as soon as we can lend in your jurisdiction.`}</TextReg>
                    <Button
                      disabled={loanAmount == '' || loanAmount < 1000}
                      style={{
                        alignSelf: 'stretch',
                        marginTop: 20,
                        marginBottom: 10,
                        opacity: loanAmount == '' || loanAmount < 1000 ? 0.5 : 1,
                      }}
                      onPress={() => this.submitNonLendable()}>
                      <TextBold
                        style={{
                          fontSize: 18,
                          letterSpacing: 0.8,
                          color: '#000',
                          alignSelf: 'stretch',
                        }}>{`SUBMIT REQUEST`}</TextBold>
                    </Button>
                    {loanAmount != '' && loanAmount < 1000 && (
                      <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
                        <TextReg
                          style={{
                            ...styles.showErrorText,
                            marginBottom: 4,
                            marginTop: 4,
                            width: 200,
                          }}>{`Loan amount needs to be at least $1000.`}</TextReg>
                      </View>
                    )}
                    {this.state.nonLendError && (
                      <TextReg
                        style={{
                          ...styles.showErrorText,
                          marginBottom: 4,
                          marginTop: 4,
                        }}>{`We have received your previous request to open up your jurisdiction for lending and will notify you when lending is available.`}</TextReg>
                    )}
                  </View>
                </Card>
              </>
            )}
          </ScrollView>
        )}
      </View>
    )
  }
}

LoanRequest.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  loanData: state.user.loanData || {},
  accountRef: state.auth.account.ref,
  user: state.user.user,
  accountRef: state.auth.account.ref,
  account: state.auth.account,
  launchDarkly: state.launchDarkly,
  tokenPrices: state.user.prices,
})

export default connect(mapStateToProps)(LoanRequest)
