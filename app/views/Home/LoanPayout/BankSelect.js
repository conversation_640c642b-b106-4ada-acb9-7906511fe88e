import React, {Component} from 'react'
import {View, TouchableOpacity, ScrollView, Image, Dimensions} from 'react-native'
import {connect} from 'react-redux'

import {Card, TextReg, Button, BackgroundHeader} from '../../../components'
import {
  updateBanks,
  increaseUnit21Refresh,
  increaseRefreshDataCount,
} from '../../../store/user/user.actions'
import {navigateRefinanceLoan} from '../../Loans/Refinance/helpers'

const {height: ScreenHeight} = Dimensions.get('window')

import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'

class BankSelect extends Component {
  constructor(props) {
    super(props)
    this.state = {
      refreshing: false,
      selectedBank: null,
      loading: false,
    }
  }

  componentDidMount() {
    this.getBanks()
  }

  getBanks = () => {
    this.setState({refreshing: true})
    this.props.WebService.getBank(this.props.loanData.id)
      .then(res => {
        console.log('getBank', res)
        this.props.dispatch(updateBanks(res.data))
        this.setState({
          refreshing: false,
          selectedBank: null,
        })
      })
      .catch(err => {
        console.log('get banks', err)
        this.setState({refreshing: false})
      })
  }

  pickBank = (bankId, name, accountNumber) => {
    if (this.state.selectedBank == bankId) {
      this.setState({
        selectedBank: null,
        bankName: null,
        bankAccountNumber: null,
      })
    } else {
      this.setState({
        selectedBank: bankId,
        bankName: name,
        bankAccountNumber: accountNumber,
      })
    }
  }

  submit = () => {
    /*
    this.props.navigation.navigate('BankTransferConfirm', {
      bankId: this.state.selectedBank,
      bankName: this.state.bankName,
      bankAccountNumber: this.state.bankAccountNumber,
    })
    */
    this.setState({loading: true})
    this.props.WebService.connectBankToLoan({
      purpose: 'deposit',
      loanId: this.props.loanData.id,
      referenceId: this.state.selectedBank,
      referenceType: 'bank_account',
    })
      .then(async res => {
        console.log('connectBankToLoan res', res)
        if (this.props?.route?.params?.forRefi) {
          await this.props.dispatch(increaseRefreshDataCount())
          await this.props.WebService.getPendingRefinanceLoanRef(this.props?.currentAccount?.ref)
            .then(async res => {
              this.setState({loading: false})
              navigateRefinanceLoan(
                this.props.currentAccount,
                this.props.user,
                this.props.navigation,
                res?.data,
              )
            })
            .catch(err => {
              console.log('err', err)
            })
        } else {
          this.setState({loading: false})
          this.props.dispatch(increaseUnit21Refresh())
        }
      })
      .catch(err => {
        console.log('error connecting bank ', err)
        this.setState({loading: false})
      })
  }

  goToBanks = () => {
    this.props.navigation.navigate('Banking', {flow: 'unit21'})
  }

  render() {
    const showBanks = this.props.banksArr.map((a, k) => {
      if (a.isPlaid) {
        return null
      }
      return (
        <TouchableOpacity
          key={k}
          onPress={() => this.pickBank(a.id, a.name, a.accountNumber)}
          style={{
            borderWidth: 2,
            borderColor: this.state.selectedBank === a.id ? '#00FFBD' : '#48485A',
            alignSelf: 'stretch',
            borderRadius: 14,
            height: 50,
            backgroundColor: '#48485A',
            marginBottom: 14,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <TextReg style={{textAlign: 'center', fontSize: 18, color: '#FFF'}}>
            {a.name} - ********{a.accountNumber}
          </TextReg>
        </TouchableOpacity>
      )
    })

    return (
      <View
        style={{
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader title={'Loan Payout'} goBack={this.props.navigation.goBack} />
        <View
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
            justifyContent: 'space-between',
            paddingBottom: 62,
          }}>
          <ScrollView
            style={{alignSelf: 'stretch', flex: 1}}
            contentContainerStyle={{alignItems: 'center'}}>
            <TextReg
              style={{
                fontSize: 17,
                marginTop: 40,
                marginBottom: 30,
                width: 260,
                textAlign: 'center',
                color: '#FFF',
              }}>
              Choose the bank account you would like your loan paid out to:
            </TextReg>
            {this.state.refreshing && (
              <Image source={require('../../../imgs/loadingDots.gif')} style={styles.loadingDots} />
            )}

            {showBanks}
            <TouchableOpacity
              onPress={() => this.goToBanks()}
              style={{
                alignSelf: 'stretch',
                height: 50,
                backgroundColor: '#48485A',
                borderRadius: 14,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <TextReg style={{color: '#00FFBD', fontSize: 18, marginTop: 2}}>
                ADD NEW BANK ACCOUNT
              </TextReg>
            </TouchableOpacity>
          </ScrollView>
          <Button
            isLoading={this.state.loading}
            style={{alignSelf: 'stretch'}}
            disabled={!this.state.selectedBank}
            onPress={() => this.submit()}>
            CONTINUE
          </Button>
          <View
            style={{
              alignSelf: 'stretch',
              height: 50,
            }}
          />
        </View>
      </View>
    )
  }
}

BankSelect.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  //headerTintColor: '#FFF',
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    //shadowColor: 'transparent',
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
})

const mapStateToProps = (state, props) => {
  const user = state?.user?.user
  const currentAccount = user?.accounts?.find(a => a.ref == state.auth.account.ref) || {}
  const forRefi = props?.route?.params?.forRefi
  const loanData = forRefi
    ? state.user?.loanData?.refinanceLoan || props?.route?.params?.refinanceLoan
    : state.user.loanData || {}

  return {
    currentAccount,
    loanData,
    user,
    tokenPrices: state.user.prices,
    showPinScreen: state.auth.pinScreen,
    refreshCount: state.user.refreshCount,
    WebService: state.auth.WebService,
    banksArr: state.user.banks,
  }
}

export default connect(mapStateToProps)(BankSelect)
