import React, {Component} from 'react'
import {View, Linking, Platform, Dimensions} from 'react-native'
import {connect} from 'react-redux'

import AsyncStorage from '@react-native-async-storage/async-storage'

import {TextReg, Button, BackgroundHeader} from '../../../components'
import {askingForPermissions} from '../../../store/auth/auth.actions'
import config from '../../../config.json'
import {navigateRefinanceLoan} from '../../Loans/Refinance/helpers'

import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'
import {increaseRefreshDataCount} from '../../../store/user/user.actions'
import {showToast} from '../../../store/notifications/notifications.actions'

const {height: ScreenHeight} = Dimensions.get('window')

class UpholdSelect extends Component {
  constructor(props) {
    super(props)
    this.state = {
      upholdBalance: null,
      upholdCardId: null,
      loading: false,
    }
  }

  componentDidMount() {
    Linking.addEventListener('url', this.handleOpenURL)
  }

  componentWillUnmount() {
    //Linking.removeEventListener('url', this.handleOpenURL);
  }

  handleOpenURL = event => {
    let code = event.url.split('&')[0]
    code = code.split('=')[1]
    const state = event.url.split('=')[2]
    if (this.state.upholdState == state) {
      //if android
      let os = 'android'
      if (Platform.OS === 'ios') {
        os = 'ios'
      }
      console.log('handleOpenURL payout', code, os)
      this.props.WebService.requestUpholdToken({code, os})
        .then(res => {
          //const upholdToken = res.data.token
          AsyncStorage.setItem(`UpholdToken-${this.props.user.primaryEmail}`, 'true')
          this.connectUpholdToLoan()
        })
        .catch(err => {
          if (err.data.body.error == 'This user has already setup Uphold integration') {
            AsyncStorage.setItem(`UpholdToken-${this.props.user.primaryEmail}`, 'true')
            this.connectUpholdToLoan()
          } else {
            console.log('uphold token err', err)
          }
        })
    }
  }

  connectUpholdToLoan = () => {
    this.setState({loading: true})
    this.props.WebService.connectBankToLoan({
      purpose: 'deposit',
      loanId: this.props.loanData.id,
      referenceId: this.props.user.id,
      referenceType: 'uphold_account',
    })
      .then(async res => {
        console.log('connectBankToLoan uphold res', res)
        if (this.props?.route?.params?.forRefi) {
          await this.props.dispatch(increaseRefreshDataCount())
          await this.props.WebService.getPendingRefinanceLoanRef(this.props?.currentAccount?.ref)
            .then(async res => {
              this.setState({loading: false})
              navigateRefinanceLoan(
                this.props.currentAccount,
                this.props.user,
                this.props.navigation,
                res?.data,
              )
            })
            .catch(err => {
              console.log('err', err)
            })
        } else {
          this.setState({loading: false})
          this.props.dispatch(increaseRefreshDataCount())
          this.props.navigation.popToTop()
        }
      })
      .catch(err => {
        console.log('error connecting bank ', err)
        this.setState({loading: false})
      })
  }

  loginUphold = () => {
    if (!this.props.user.mfaEnabled) {
      this.props.dispatch(showToast(true))
      return
    }
    this.setState({loading: true})
    this.props.WebService.getUpholdState()
      .then(res => {
        const {state} = res.data
        this.setState({upholdState: state, loading: false})
        this.openUpholdSite(state)
      })
      .catch(err => {
        this.setState({
          error: 'Connection problem with servers.',
          loading: false,
        })
      })
  }

  openUpholdSite = state => {
    if (!this.props.user.mfaEnabled) {
      this.props.dispatch(showToast(true))
      return
    }

    this.props.dispatch(askingForPermissions(true))

    let clientId = config.uphold.android.id
    if (Platform.OS === 'ios') {
      clientId = config.uphold.ios.id
    }

    Linking.openURL(
      `https://uphold.com/authorize/${clientId}?scope=cards:read%20transactions:withdraw%20user:read&state=${state}`,
    )
  }

  render() {
    const hasConnectedUphold = this.props.user?.hasConnectedUphold || false
    return (
      <View
        style={{
          flex: 1,
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader title={'Uphold Connect'} goBack={this.props.navigation.goBack} />

        <View
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingBottom: 62,
          }}>
          {hasConnectedUphold ? (
            <TextReg
              style={{
                fontSize: 17,
                marginTop: 40,
                marginBottom: 20,
                width: 260,
                textAlign: 'center',
                color: '#FFF',
              }}>
              Uphold is already connected to this account - please continue to select this payout
              option
            </TextReg>
          ) : (
            <TextReg
              style={{
                fontSize: 17,
                marginTop: 40,
                marginBottom: 20,
                width: 260,
                textAlign: 'center',
                color: '#FFF',
              }}>
              Connect your Uphold account
            </TextReg>
          )}

          <>
            {hasConnectedUphold ? (
              <Button
                style={{alignSelf: 'stretch'}}
                isLoading={this.state.loading}
                onPress={() => this.connectUpholdToLoan()}>
                Select
              </Button>
            ) : (
              <Button
                style={{alignSelf: 'stretch'}}
                isLoading={this.state.loading}
                onPress={() => this.loginUphold()}>
                Connect
              </Button>
            )}
            <View
              style={{
                alignSelf: 'stretch',
                height: 50,
              }}
            />
          </>
        </View>
      </View>
    )
  }
}

UpholdSelect.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  //headerTintColor: '#FFF',
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    //shadowColor: 'transparent',
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
})

const mapStateToProps = (state, props) => {
  const user = state?.user?.user
  const currentAccount = user?.accounts?.find(a => a.ref == state.auth.account.ref) || {}
  const forRefi = props?.route?.params?.forRefi
  const loanData = forRefi
    ? state.user?.loanData?.refinanceLoan || props?.route?.params?.refinanceLoan
    : state.user.loanData || {}

  return {
    currentAccount,
    loanData,
    user,
    tokenPrices: state.user.prices,
    showPinScreen: state.auth.pinScreen,
    refreshCount: state.user.refreshCount,
    WebService: state.auth.WebService,
    banksArr: state.user.banks,
  }
}

export default connect(mapStateToProps)(UpholdSelect)
