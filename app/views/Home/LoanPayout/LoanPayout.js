import React, {Component} from 'react'
import {View, TouchableOpacity, Image, Dimensions} from 'react-native'
import {increaseUnit21Refresh} from '../../../store/user/user.actions'
import {connect} from 'react-redux'

import AsyncStorage from '@react-native-async-storage/async-storage'

import {Card, TextReg, TextBold, BackgroundHeader, Button} from '../../../components'
import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'
import {updateLoans, pauseUnit21} from '../../../store/user/user.actions'
import {derivedStatusMap} from '../../../util/enumerables'
import {dig} from '../../../util/helpers'
import {getTokenPic} from '../../../util/tokens'

const {height: ScreenHeight} = Dimensions.get('window')

class LoanPayout extends Component {
  constructor(props) {
    super(props)
    this.state = {
      optionSelect: '',
      loading: false,
    }
  }

  componentDidMount() {
    let payoutEdit = this.props?.route?.params?.edit
    let stableAddr = this.props.loanData?.depositBankAccount?.address
    let stableCurrency = this.props.loanData?.depositBankAccount?.currency
    let verifiedAt = this.props.loanData?.depositBankAccount?.verifiedAt
    if (payoutEdit && stableAddr && !verifiedAt) {
      //route to fill stablecoin
      this.props.navigation.navigate('StablecoinSelect', {
        ...this.props.route?.params,
        fillAddr: stableAddr,
        fillType: stableCurrency,
        noBack: true,
      })
    }
  }

  routeToFill = () => {
    let payoutEdit = this.props?.route?.params?.edit
    let stableAddr = this.props.loanData?.depositBankAccount?.address
    let stableCurrency = this.props.loanData?.depositBankAccount?.currency
    let verifiedAt = this.props.loanData?.depositBankAccount?.verifiedAt
    this.props.navigation.navigate('StablecoinSelect', {
      ...this.props.route?.params,
      fillAddr: stableAddr,
      fillType: stableCurrency,
    })
  }

  selectOption = (selection, payoutOptionChosen = false) => {
    if (payoutOptionChosen) return
    if (this.state.optionSelect === selection) {
      this.setState({optionSelect: null})
    } else {
      this.setState({optionSelect: selection})
    }
  }

  next = () => {
    let payoutEdit = this.props?.route?.params?.edit
    let verifiedAt = this.props.loanData?.depositBankAccount?.verifiedAt

    if (payoutEdit && verifiedAt && this.state.optionSelect == 'Stablecoin') {
      //next part of loan
      this.props.dispatch(increaseUnit21Refresh())
      return
    }
    const params = this.props.route?.params

    switch (this.state.optionSelect) {
      case 'Stablecoin':
        this.props.navigation.navigate('StablecoinSelect', params)
        break
      case 'Bank':
        this.props.navigation.navigate('BankSelect', params)
        break
      case 'Uphold':
        this.props.navigation.navigate('UpholdSelect', params)
        break
    }
  }

  switchPayout = (depositBankAccount = false, stablecoin = false, uphold = false) => {
    let referenceType = 'bank_account'
    if (stablecoin) {
      referenceType = 'withdrawal_address'
    }
    if (uphold) {
      referenceType = 'uphold_account'
    }
    this.setState({loading: true}, () => {
      this.props.WebService.removeBankFromLoan({
        purpose: 'deposit',
        loanId: this.props.loanData?.id,
        referenceId: this.props.loanData?.depositBankAccount?.id,
        referenceType,
      })
        .then(res => {
          console.log('removeBankFromLoan res', res)
          this.getLoans()
          this.setState({loading: false})
        })
        .catch(err => {
          console.log('error removing bank ', err)
          this.setState({loading: false})
        })
    })
  }

  getLoans = () => {
    this.props.WebService.getLoans().then(res => {
      const loanStatus = derivedStatusMap.get(dig(res.data[0], 'status'))
      if (res.data.length > 0 && loanStatus === 'active') {
        this.props.dispatch(updateLoans(res.data[0]))
      } else if (loanStatus === 'pending') {
        this.props.dispatch(updateLoans(res.data[0]))
      }
      if (res.data.length === 0) {
        this.props.dispatch(updateLoans({status: 'none'}))
      }
    })
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    this.props.navigation.pop(stackDepth)
  }

  goBackFix = () => {
    //this fixes black screen on super fast double pop - not sure exactly why it black screens - maybe race condition?
    setTimeout(() => {
      this.props.navigation.popToTop()
    }, 200)
  }

  render() {
    let payoutEdit = this.props?.route?.params?.edit
    console.log('payoutEdit', payoutEdit)
    const depositBankAccount = this.props.loanData?.depositBankAccount?.name
    const bankAccountShow = true
    let bankAccountName = null
    let isPlaidAccount = null
    let bankAccountNumber = null
    let stableCoinShow = true
    let upholdShow = true
    if (depositBankAccount) {
      stableCoinShow = false
      upholdShow = false
      bankAccountName = this.props.loanData?.depositBankAccount?.name
      isPlaidAccount = this.props.loanData?.depositBankAccount?.isPlaid
      bankAccountNumber = this.props.loanData?.depositBankAccount?.accountNumber
    }

    const stableCoinCurrency = this.props.loanData?.depositBankAccount?.currency
    let stablecoinImg = null
    let stablecoinAddress = null
    if (stableCoinCurrency) {
      stablecoinImg = getTokenPic(stableCoinCurrency)
      stablecoinAddress = this.props.loanData?.depositBankAccount?.address
    }
    const upholdOption = this.props.loanData?.depositBankAccount?.upholdId || false

    let payoutOptionChosen = false
    if (!payoutEdit && (depositBankAccount || stableCoinCurrency || upholdOption)) {
      payoutOptionChosen = true
    }

    const goBackDisabled = this.props.route?.params?.goBackDisabled

    let noBankAllowed = this.props.launchDarkly['Disable-USD-payout'] || false
    let verifiedAt = this.props.loanData?.depositBankAccount?.verifiedAt

    let firstFive = stablecoinAddress?.slice(0, 10)
    let lastFour = stablecoinAddress?.slice(-8)

    let tusdPax = true
    if (this.props?.launchDarkly['disable-tusd-pax-payout']) {
      tusdPax = false
    }

    let stableText = 'Coins supported include: USDCoin and USDT.'
    if (tusdPax) {
      stableText = 'Coins supported include: USDCoin, USDT, TrueUSD, and Paxos.'
    }

    return (
      <View
        style={{
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader
          title={'Loan Payout'}
          goBack={goBackDisabled ? false : this.goBackFix}
          close
          closeFn={this.closeUnit21}
        />
        <View
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
            justifyContent: 'space-between',
            paddingBottom: 62,
          }}>
          <View style={{alignItems: 'center'}}>
            <TextBold
              style={{
                fontSize: 20,
                marginTop: 20,
                marginBottom: !payoutOptionChosen ? 8 : 18,
                color: '#FFF',
              }}>
              {!payoutOptionChosen ? `How do you want your loan paid out?` : `Loan payout method`}
            </TextBold>
            {!payoutOptionChosen && (
              <TextReg style={{fontSize: 17, marginBottom: 18, color: '#FFF'}}>Choose one:</TextReg>
            )}
            {(!payoutOptionChosen || depositBankAccount) && !noBankAllowed && (
              <TouchableOpacity onPress={() => this.selectOption('Bank', payoutOptionChosen)}>
                <Card
                  cardMarginBottom={12}
                  style={[
                    styles.payoutOptionCard,
                    {
                      borderColor: this.state.optionSelect === 'Bank' ? '#00FFBD' : '#28283D',
                    },
                  ]}>
                  <View style={styles.payoutOptionCardBox}>
                    <Image
                      source={require('../../../imgs/unit21/bank.png')}
                      style={styles.oneTimeOptionImg}
                    />
                    <View style={{marginLeft: 8}}>
                      <TextBold style={styles.payoutOptionTitle}>Bank Transfer (USD)</TextBold>
                      <TextReg style={{width: 220, color: '#FFF'}}>
                        {depositBankAccount
                          ? `${bankAccountName} - ****${bankAccountNumber}`
                          : `This requires you to connect bank information.`}
                      </TextReg>
                    </View>
                  </View>
                </Card>
              </TouchableOpacity>
            )}
            {(!payoutOptionChosen || stableCoinCurrency) && (
              <TouchableOpacity onPress={() => this.selectOption('Stablecoin', payoutOptionChosen)}>
                <Card
                  cardMarginBottom={12}
                  style={[
                    styles.payoutOptionCard,
                    {
                      borderColor: this.state.optionSelect === 'Stablecoin' ? '#00FFBD' : '#28283D',
                    },
                  ]}>
                  <View style={styles.payoutOptionCardBox}>
                    {!stableCoinCurrency ? (
                      <Image
                        source={require('../../../imgs/unit21/wallet.png')}
                        style={styles.oneTimeOptionImg}
                      />
                    ) : (
                      <Image
                        source={stablecoinImg}
                        style={{height: 40, width: 40, marginRight: 14}}
                      />
                    )}
                    <View style={{marginLeft: 8, flexDirection: 'row'}}>
                      <View>
                        <TextBold style={styles.payoutOptionTitle}>Stablecoin Wallet</TextBold>
                        {stableCoinCurrency && (
                          <TextReg style={{marginBottom: 2}}>{`${stableCoinCurrency}`}</TextReg>
                        )}
                        <TextReg style={{width: 220, color: '#FFF'}}>
                          {!stableCoinCurrency ? stableText : `${firstFive}...${lastFour}`}
                        </TextReg>
                        {verifiedAt && (
                          <View style={{flexDirection: 'row', marginTop: 4}}>
                            <Image
                              source={require('../../../imgs/checkmark.png')}
                              style={{height: 18, width: 18, marginRight: 4}}
                            />
                            <TextReg style={{fontSize: 16}}>{`Verified`}</TextReg>
                          </View>
                        )}
                      </View>
                      {verifiedAt && (
                        <TouchableOpacity
                          onPress={() => {
                            this.routeToFill()
                          }}
                          style={{
                            marginLeft: -34,
                            marginRight: -20,
                            height: 60,
                            width: 60,
                            borderRadius: 10,
                            borderColor: '#00FFBD',
                            borderWidth: 2,
                            alignItems: 'center',
                            justifyContent: 'center',
                            marginTop: 14,
                          }}>
                          <TextBold>EDIT</TextBold>
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                </Card>
              </TouchableOpacity>
            )}
          </View>

          <View>
            {!payoutOptionChosen ? (
              <Button
                style={{alignSelf: 'stretch'}}
                disabled={!this.state.optionSelect}
                onPress={() => this.next()}
                isLoading={this.state.loading}>
                CONTINUE
              </Button>
            ) : (
              <Button
                style={{alignSelf: 'stretch'}}
                onPress={() =>
                  this.switchPayout(depositBankAccount, stableCoinCurrency, upholdOption)
                }
                isLoading={this.state.loading}>
                Switch Payout
              </Button>
            )}
            <View
              style={{
                alignSelf: 'stretch',
                height: 50,
              }}
            />
          </View>
        </View>
      </View>
    )
  }
}

LoanPayout.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
  gesturesEnabled: false,
})

const mapStateToProps = (state, props) => {
  const user = state?.user?.user
  const currentAccount = user?.accounts?.find(a => a.ref == state.auth.account.ref) || {}
  const forRefi = props?.route?.params?.forRefi
  const loanData = forRefi
    ? state.user?.loanData?.refinanceLoan || props?.route?.params?.refinanceLoan
    : state.user.loanData || {}

  return {
    loanData,
    WebService: state.auth.WebService,
    launchDarkly: state.launchDarkly,
    currentAccount,
    user,
  }
}

export default connect(mapStateToProps)(LoanPayout)
