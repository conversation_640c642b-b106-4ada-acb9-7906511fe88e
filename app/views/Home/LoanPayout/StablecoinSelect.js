import React, {Component} from 'react'
import {
  View,
  TouchableOpacity,
  ScrollView,
  Image,
  TextInput,
  Dimensions,
  AppState,
} from 'react-native'
import AsyncStorage from '@react-native-async-storage/async-storage'
import {connect} from 'react-redux'

import {Card, TextReg, Button, BackgroundHeader, TextBold} from '../../../components'
import {increaseRefreshDataCount, increaseUnit21Refresh} from '../../../store/user/user.actions'
import {askingForPermissions} from '../../../store/auth/auth.actions'
import {validateTokenAddress} from '../../../util/helpers'

import {cryptoNameMap} from '../../../util/enumerables'
import {getTokenPic} from '../../../util/tokens'

import WithdrawQRScanner from '../../Collateral/Withdraw/WithdrawQRScanner'

const {height: ScreenHeight} = Dimensions.get('window')

import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'
import {navigateRefinanceLoan} from '../../Loans/Refinance/helpers'

class StablecoinSelect extends Component {
  constructor(props) {
    super(props)
    this.state = {
      refreshing: false,
      selectedCoin: '',
      loading: false,
      address: '',
      verify: false,
      code: '',
      id: null,
      scannerModalVisable: false,
      withdrawAddressValid: true,
      appState: AppState.currentState,
    }
    AppState.addEventListener('change', this.handleAppStateChange)
  }

  componentDidMount() {
    this.checkPending()
    console.log('stable select mount ', this.props?.route?.params)
    if (this.props?.route?.params?.fillAddr) {
      this.setState({
        address: this.props?.route?.params?.fillAddr,
        selectedCoin: this.props?.route?.params?.fillType,
        message: 'Confirm or update your payout address below:',
      })
    }
  }

  handleAppStateChange = async nextAppState => {
    if (this.state.appState.match(/inactive|background/) && nextAppState === 'active') {
      this.checkPending()
    }
    this.setState({appState: nextAppState})
  }

  checkPending = async () => {
    let loanId = this.props.loanData?.id
    let timestamp = new Date().getTime().toString()

    let pendingInfo = await AsyncStorage.getItem(`stablePayout-${loanId}`)
    if (pendingInfo?.length > 3) {
      //over {}
      pendingInfo = JSON.parse(pendingInfo)

      //check if timestamp < 30min
      let timeDiff = timestamp - pendingInfo?.timestamp
      if (timeDiff < 30 * 60000) {
        let token = pendingInfo?.token
        let address = pendingInfo?.address
        let id = pendingInfo?.id
        this.setState({selectedCoin: token, id, address, verify: true})
      }
    }
  }

  selectCoin = a => {
    if (this.state.selectedCoin == a) {
      this.setState({
        selectedCoin: '',
      })
    } else {
      this.setState({
        selectedCoin: a,
      })
    }
  }

  handleUpdateAddress = text => {
    this.setState({address: text})
  }

  handleUpdateCode = text => {
    this.setState({code: text, verifyErr: false})
  }

  verify = () => {
    this.setState({loading: true, verifyErr: false})
    this.props.WebService.verifyWithdrawalAddress(this.state.id, {
      pin: this.state.code,
    })
      .then(async res => {
        //clear local pending
        let loanId = this.props.loanData?.id
        await AsyncStorage.setItem(`stablePayout-${loanId}`, '{}')
        this.connectBankToLoan(this.state.id)
      })
      .catch(err => {
        console.log('verifyWithdrawalAddress err ', err)
        this.setState({loading: false, verifyErr: true})
      })
  }

  connectBankToLoan = id => {
    this.props.WebService.connectBankToLoan({
      purpose: 'deposit',
      loanId: this.props.loanData.id,
      referenceId: id,
      referenceType: 'withdrawal_address',
    })
      .then(async res => {
        if (this.props?.route?.params?.forRefi) {
          await this.props.dispatch(increaseRefreshDataCount())
          await this.props.WebService.getPendingRefinanceLoanRef(this.props?.currentAccount?.ref)
            .then(async res => {
              this.setState({loading: false})
              navigateRefinanceLoan(
                this.props.currentAccount,
                this.props.user,
                this.props.navigation,
                res?.data,
              )
            })
            .catch(err => {
              console.log('err', err)
            })
        } else {
          this.setState({loading: false})
          this.props.dispatch(increaseUnit21Refresh())
        }
      })
      .catch(err => {
        console.log('error connecting bank ', err)
        this.setState({loading: false})
      })
  }

  resend = () => {
    this.setState({loading: true, verifyErr: false})
    this.props.WebService.resendWithdrawalAddress(this.state.id)
      .then(res => {
        this.setState({loading: false})
      })
      .catch(err => {
        console.log('resendWithdrawalAddress err ', err)
        this.setState({loading: false})
      })
  }

  validateAddress = withdrawAddress => {
    if (!validateTokenAddress('ETH', this.state.address)) {
      this.setState({withdrawAddressValid: false})
      return false
    }
    return true
  }

  submit = () => {
    this.setState({withdrawAddressValid: true, loading: true})

    if (!this.validateAddress()) {
      this.setState({loading: false})
      return
    }

    this.props.WebService.createWithdrawalAddress({
      address: this.state.address,
      currency: this.state.selectedCoin,
      protocol: 'ETH',
    })
      .then(async res => {
        if (res.data.verifiedAt) {
          this.connectBankToLoan(res.data.id)()
        } else {
          this.setState({loading: false, verify: true, id: res.data.id})

          //save to pending local
          let loanId = this.props.loanData?.id
          let timestamp = new Date().getTime().toString()
          let obj = JSON.stringify({
            timestamp,
            id: res.data.id,
            address: this.state.address,
            token: this.state.selectedCoin,
          })
          await AsyncStorage.setItem(`stablePayout-${loanId}`, obj)
        }
      })
      .catch(err => {
        console.log('createWithdrawalAddress err ', err)
        this.setState({loading: false})
      })
  }

  openQRScanner = () => {
    this.toggleScannerModal()
  }

  toggleScannerModal = () => {
    this.props.dispatch(askingForPermissions(!this.state.scannerModalVisable))
    this.setState({scannerModalVisable: !this.state.scannerModalVisable})
  }

  updateAddressFromQR = address => {
    this.setState({address})
  }

  goBack = async () => {
    if (this.state.verify) {
      this.setState({verify: false, selectCoin: '', address: '', id: null})
      let loanId = this.props.loanData?.id
      await AsyncStorage.setItem(`stablePayout-${loanId}`, '{}')
    } else {
      this.props.navigation.goBack()
    }
  }

  closeUnit21 = () => {
    this.props.navigation.pop(1000)
  }

  render() {
    console.log('launchDarkly', this.props.launchDarkly)
    let tusdPax = true
    if (this.props?.launchDarkly['disable-tusd-pax-payout']) {
      tusdPax = false
    }

    const stableCoins = tusdPax ? ['USDC', 'TUSD', 'USDP', 'USDT'] : ['USDC', 'USDT']
    const showCoins = stableCoins.map((a, k) => {
      const name = cryptoNameMap.get(a.toLowerCase())
      const showImg = getTokenPic(a)
      return (
        <TouchableOpacity
          key={k}
          onPress={() => this.selectCoin(a)}
          style={{
            height: 112,
            width: 154,
            borderRadius: 14,
            backgroundColor: '#48485A',
            margin: 6,
            borderWidth: 2,
            borderColor: this.state.selectedCoin === a ? '#00FFBD' : '#48485A',
            justifyContent: 'center',
            marginBottom: 4,
          }}>
          <View
            style={{
              alignSelf: 'stretch',
              alignItems: 'center',
              paddingTop: 6,
            }}>
            <Image
              style={{
                height: 40,
                width: 40,
                borderRadius: 15,
                opacity: 0.9,
              }}
              source={showImg}
            />
            <TextReg style={{marginTop: 12, alignSelf: 'center', color: '#FFF'}}>{name}</TextReg>
          </View>
        </TouchableOpacity>
      )
    })

    let noBack = this.props?.route?.params?.noBack

    return (
      <View
        style={{
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader
          title={'Loan Payout'}
          goBack={noBack ? null : () => this.goBack()}
          close={noBack}
          closeFn={noBack ? this.closeUnit21 : null}
        />
        <WithdrawQRScanner
          toggleScannerModal={this.toggleScannerModal}
          scannerModalVisable={this.state.scannerModalVisable}
          showPinScreen={this.props.showPinScreen}
          updateAddressFromQR={this.updateAddressFromQR}
        />
        <View
          style={{
            alignSelf: 'stretch',
            flex: 1,
            justifyContent: 'space-between',
            paddingBottom: 62,
          }}>
          {this.state.verify || this.state.id ? (
            <View
              style={{
                alignSelf: 'stretch',
                alignItems: 'center',
              }}>
              <Card marginTop={40}>
                <TextBold
                  style={{
                    fontSize: 18,
                    marginBottom: 10,
                  }}>{`Verify ${this.state.selectedCoin} Address`}</TextBold>
                <TextReg
                  style={{
                    marginBottom: 20,
                    opacity: 0.7,
                    fontSize: 20,
                    textAlign: 'center',
                    width: 300,
                  }}>{`${this.state.address}`}</TextReg>
                <TextReg
                  style={{
                    fontSize: 16,
                    textAlign: 'center',
                    marginBottom: 20,
                  }}>
                  We sent an email with a verification code. Enter the code below:
                </TextReg>
                <View style={{width: 300}}>
                  <TextReg style={styles.stablecoinInputTitle}>{`Verification Code`}</TextReg>
                  <TextInput
                    style={styles.verifyStablecoinInput}
                    onChangeText={text => this.handleUpdateCode(text)}
                    value={this.state.code}
                    underlineColorAndroid="transparent"
                    blurOnSubmit
                    placeholder={''}
                    autoCapitalize={'none'}
                    keyboardAppearance="dark"
                  />
                  <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
                    <TextReg>case-sensitive</TextReg>
                  </View>
                </View>
                {this.state.verifyErr && (
                  <TextReg
                    style={{
                      marginTop: 10,
                      color: '#E5705A',
                    }}>{`Verification code is incorrect, please try again`}</TextReg>
                )}
                <Button
                  isLoading={this.state.loading}
                  style={{marginTop: 16, marginBottom: 14}}
                  disabled={!this.state.code}
                  onPress={() => this.verify()}>
                  Verify
                </Button>
                <TouchableOpacity style={{marginBottom: 20}} onPress={() => this.resend()}>
                  <TextBold style={{fontSize: 18, color: '#00FFBD'}}>RESEND</TextBold>
                </TouchableOpacity>
              </Card>
            </View>
          ) : (
            <ScrollView
              style={{alignSelf: 'stretch'}}
              contentContainerStyle={{alignItems: 'center'}}>
              <TextReg
                style={{
                  fontSize: 17,
                  marginTop: 20,
                  marginBottom: 20,
                  width: 260,
                  textAlign: 'center',
                  color: '#FFF',
                }}>
                Select the stablecoin you would like to use:
              </TextReg>
              <View style={{alignItems: 'center'}}>
                <View
                  style={{flexDirection: 'row', flexWrap: 'wrap', width: 332, marginBottom: 10}}>
                  {showCoins}
                </View>

                {this.state.message && (
                  <View style={{width: 326, alignItems: 'center', marginBottom: 10, marginTop: 10}}>
                    <TextReg style={{width: 200, textAlign: 'center'}}>
                      {this.state.message}
                    </TextReg>
                  </View>
                )}
                <View
                  style={{
                    backgroundColor: '#48485A',
                    width: 326,
                    alignItems: 'center',
                    borderRadius: 14,
                    paddingTop: 8,
                    paddingBottom: 8,
                  }}>
                  <View style={{width: 300}}>
                    <TextReg
                      style={
                        styles.stablecoinInputTitle
                      }>{`${this.state.selectedCoin} Address`}</TextReg>
                    <View style={{flexDirection: 'row'}}>
                      <TextInput
                        style={styles.stablecoinInput}
                        onChangeText={text => this.handleUpdateAddress(text)}
                        value={this.state.address}
                        underlineColorAndroid="transparent"
                        blurOnSubmit
                        placeholder={''}
                        keyboardAppearance="dark"
                      />
                      <TouchableOpacity onPress={() => this.openQRScanner()}>
                        <Image
                          style={{
                            height: 40,
                            width: 40,
                            borderRadius: 14,
                          }}
                          source={require('../../../imgs/qrCodeSymbol.png')}
                        />
                      </TouchableOpacity>
                    </View>
                  </View>
                  {this.state.address != '' && (
                    <TextReg
                      style={{
                        textAlign: 'center',
                        marginTop: 14,
                        marginBottom: 10,
                        fontSize: 16,
                        width: 220,
                        color: '#FFF',
                      }}>
                      {this.state.address}
                    </TextReg>
                  )}
                </View>
              </View>
            </ScrollView>
          )}
          <View style={{width: 326, alignSelf: 'center'}}>
            {!this.state.withdrawAddressValid && (
              <View style={styles.withdrawErrorBox}>
                <TextReg style={styles.withdrawErrorText}>
                  {`The address you entered is not a valid ETH address. Please try again.`}
                </TextReg>
              </View>
            )}
            {!this.state.verify && (
              <Button
                isLoading={this.state.loading}
                style={{alignSelf: 'stretch'}}
                disabled={!this.state.selectedCoin}
                onPress={() => this.submit()}>
                SUBMIT
              </Button>
            )}
            <View
              style={{
                alignSelf: 'stretch',
                height: 50,
              }}
            />
          </View>
        </View>
      </View>
    )
  }
}

StablecoinSelect.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  //headerTintColor: '#FFF',
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    //shadowColor: 'transparent',
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
})

const mapStateToProps = (state, props) => {
  const user = state?.user?.user
  const currentAccount = user?.accounts?.find(a => a.ref == state.auth.account.ref) || {}
  const forRefi = props?.route?.params?.forRefi
  const loanData = forRefi
    ? state.user?.loanData?.refinanceLoan || props?.route?.params?.refinanceLoan
    : state.user.loanData || {}

  return {
    accountRef: state.auth.account.ref,
    currentAccount,
    loanData,
    user: state.user.user,
    tokenPrices: state.user.prices,
    showPinScreen: state.auth.pinScreen,
    refreshCount: state.user.refreshCount,
    WebService: state.auth.WebService,
    banksArr: state.user.banks,
    launchDarkly: state.launchDarkly,
  }
}

export default connect(mapStateToProps)(StablecoinSelect)
