import React, { Component } from 'react';
import { View, Image, TouchableOpacity, Clipboard, Share } from 'react-native';
import { WebView } from 'react-native-webview';

import { TextBold, TextReg, Card, Button } from '../../components';

import styles from './styles';

class BeamerView extends Component {
  constructor (props) {
    super(props);
    this.state = {};
  }

  render () {
    return (
      <TouchableOpacity style={styles.beamerBox} onPress={() => this.props.toggleBeamer()}>
        <TouchableOpacity activeOpacity={1} style={styles.beamerSquare}>
          <TouchableOpacity
            onPress={() => {
              this.props.toggleBeamer();
            }}
            style={{ height: 42, width: 42, position: 'absolute', zIndex: 60, top: 10, right: 0 }}
          />
          <WebView
            source={{
              uri: 'https://app.getbeamer.com/news?app_id=vERWaFbA11357&language=en&api=true&filterByUrl=false'
            }}
            style={{ width: 350 }}
            scalesPageToFit
            useWebKit={false}
          />
        </TouchableOpacity>
      </TouchableOpacity>
    );
  }
}

export default BeamerView;
