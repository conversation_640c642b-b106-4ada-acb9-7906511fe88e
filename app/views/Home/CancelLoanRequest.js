import React, {Component} from 'react'
import {View, Image} from 'react-native'
import {connect} from 'react-redux'

import {TextBold, TextReg, Card, Button} from '../../components'
import {increaseRefreshDataCount, updateLoansDrill} from '../../store/user/user.actions'

import styles from './styles'

class CancelLoanRequest extends Component {
  constructor(props) {
    super(props)
    this.state = {
      loading: false,
    }
  }

  cancelRequest = () => {
    this.setState({loading: true})
    this.props.WebService.deleteLoanRequest(this.props.id)
      .then(res => {
        console.log('deleteLoanRequest res', res)
        //this.setState({loading: false});
        ////this.props.goBack();
        /*
        let newLoansData = {
          ...this.props.loanData,
          status: 'none',
        };
        */
        //this.props.dispatch(updateLoansDrill(newLoansData));
        this.props.dispatch(increaseRefreshDataCount())
      })
      .catch(err => {
        console.log('deleteLoanRequest err', err)
        this.setState({loading: false})
      })
  }

  render() {
    if (this.props.loanData?.depositBankAccountType == 'merchant_account') {
      return null
    }

    console.log('loanData', this.props.loanData)

    let thisAccount = this.props.accountRef
    let thisUser = this.props.user?.accounts?.find(a => a.ref == thisAccount)
    let thisLoan = thisUser?.loans[0]

    console.log('thisUser', thisUser)
    console.log('thisLoan', thisLoan)
    let status = thisLoan?.status

    //status == 'inReview'
    let text1 = 'Loan Application in Review'
    let text2 =
      'We are currently reviewing your inquiry. Our loan specialists will contact you soon.'
    if (this.props.loanData?.status == 'in_progress') {
      text1 = 'Loan Application in Progress'
      text2 = 'Please continue with your loan application above, or cancel below.'
    }
    if (status == 'under_review') {
      text1 = 'Loan Application under Review'
      text2 = 'Please allow up to 48 business hours for your review to be completed.'
    }
    if (status == 'pending_signatures') {
      text1 = 'Loan Application pending signatures'
      text2 =
        'Please check your email inbox for loan documents to be signed electronically to complete this next step.'
    }
    if (status == 'awaiting_collateral') {
      text1 = 'Loan Application awaiting Collateral'
      text2 = 'Please continue with your collateral deposit above, or cancel below.'
    }
    if (status == 'awaiting_funding') {
      text1 = 'Loan Application awaiting Funding'
      text2 = 'Please allow up to 48 business hours for your funding to be completed.'
    }

    return (
      <Card marginTop={0} cardMarginBottom={10}>
        <View style={{alignSelf: 'stretch'}}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginTop: 6,
              marginBottom: 18,
            }}>
            <View>
              <TextBold style={{fontSize: 20, paddingLeft: 10, marginBottom: 12}}>{text1}</TextBold>

              <TextReg style={{paddingLeft: 10, width: 270}}>{text2}</TextReg>
            </View>
            <Image
              style={{
                width: 42,
                height: 45,
                marginRight: 8,
                marginTop: -8,
              }}
              source={require('../../imgs/graphics/loanPending.png')}
            />
          </View>
          <Button
            isLoading={this.state.loading}
            style={{
              marginBottom: 10,
              alignSelf: 'stretch',
              marginLeft: 10,
              marginRight: 10,
              borderColor: '#F7D956',
              borderWidth: 2,
              backgroundColor: '#3D3D50',
            }}
            onPress={() => this.cancelRequest()}>
            <TextBold style={{color: '#F7D956', fontSize: 18}}>CANCEL REQUEST</TextBold>
          </Button>
        </View>
      </Card>
    )
  }
}

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
  user: state.user.user,
})

export default connect(mapStateToProps)(CancelLoanRequest)
