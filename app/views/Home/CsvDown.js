import React from 'react'
import {View, TouchableOpacity, PermissionsAndroid, Share} from 'react-native'
import RNFS from 'react-native-fs'
import Papa from 'papaparse'
//import Share from 'react-native-share'

let CsvDown = ({}) => {
  // Function to request storage permission only on Android
  const requestStoragePermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, {
          title: 'Storage Permission Required',
          message: 'This app needs access to your storage to save files',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        })
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Storage permission granted')
          return true
        } else {
          console.log('Storage permission denied')
          return false
        }
      } catch (err) {
        console.warn(err)
        return false
      }
    } else {
      // No need to request permission on iOS
      return true
    }
  }

  const saveAndShareCSV = async data => {
    // Check for permission first
    const hasPermission = await requestStoragePermission()
    if (!hasPermission) {
      console.log('Permission denied')
      return
    }

    const csv = Papa.unparse(data)
    const path = `${RNFS.DocumentDirectoryPath}/data.csv`

    try {
      await RNFS.writeFile(path, csv, 'utf8')
      console.log(`File written to ${path}`)

      // Share the file
      const shareResponse = await Share.open({
        title: 'Share CSV File',
        url: `file://${path}`,
        type: 'text/csv',
      })

      console.log('File shared:', shareResponse)
    } catch (error) {
      console.error('Error saving or sharing CSV file', error)
    }
  }

  let save = async () => {
    // Example data
    const exampleData = [
      {column1: 'data1', column2: 'data2'},
      {column1: 'data3', column2: 'data4'},
    ]

    // Save and share example data to CSV
    console.log('save', exampleData)

    saveAndShareCSV(exampleData)
  }

  const read = async () => {
    const path = `${RNFS.DocumentDirectoryPath}/data.csv`

    try {
      const content = await RNFS.readFile(path, 'utf8')
      console.log('File content:', content)
      return content
    } catch (error) {
      console.error('Error reading CSV file', error)
      return null
    }
  }

  return (
    <View>
      {false && (
        <>
          <TouchableOpacity style={{height: 50, width: 50, backgroundColor: 'blue', marginTop: 80}} onPress={() => save()} />
          <TouchableOpacity style={{height: 50, width: 50, backgroundColor: 'red', marginTop: 80}} onPress={() => read()} />
        </>
      )}
    </View>
  )
}

export default CsvDown
