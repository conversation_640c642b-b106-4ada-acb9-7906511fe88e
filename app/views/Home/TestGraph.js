import React, {useState} from 'react'
import {View, Text, Dimensions, TouchableOpacity} from 'react-native'
import Animated, {useSharedValue, useAnimatedProps} from 'react-native-reanimated'
import Svg, {Path, G} from 'react-native-svg'
import {graphs} from './Shell/Model'
import {prices30, prices30Variant1, prices30Variant2} from './Shell/Data'

const AnimatedPath = Animated.createAnimatedComponent(Path)
const {width} = Dimensions.get('window')
const graphWidth = width - 40 // Assuming 20px padding on each side
const height = 150

const createGraph = (prices, color) => {
  const {data} = graphs(graphWidth, prices)[0]
  const area1 = graphs(graphWidth, prices, 1)[0]?.data.area
  const path = useSharedValue(area1 || '')

  const animatedProps = useAnimatedProps(() => ({
    d: path.value,
  }))
  return (
    <AnimatedPath animatedProps={animatedProps} fill="transparent" stroke={color} strokeWidth={2} />
  )
}

export default function TestGraph() {
  const [active, setActive] = useState(1)

  const graphs = [
    createGraph(prices30, 'blue'),
    createGraph(prices30Variant1, 'green'),
    createGraph(prices30Variant2, 'red'),
  ]

  return (
    <View style={{alignSelf: 'stretch'}}>
      <Text>TestGraph</Text>
      <Svg width={width} height={height}>
        <G transform="translate(20,0)">{graphs[active - 1]}</G>
      </Svg>
      <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
        {[1, 2, 3].map(num => (
          <TouchableOpacity key={num} onPress={() => setActive(num)}>
            <Text style={{color: active === num ? '#FFF' : '#999'}}>{num}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  )
}
