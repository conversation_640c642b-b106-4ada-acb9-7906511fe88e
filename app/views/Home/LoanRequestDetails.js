import React, {Component} from 'react'
import {View} from 'react-native'
import {connect} from 'react-redux'

import {TextBold, TextReg, Card} from '../../components'
import PayoutPreference from './PayoutPreference'

import {
  numberWithCommas,
  getBaseRate,
  getPayment,
  getRewardRateMap,
  createMiniAmSchedule,
  getTotalInterest,
  getTotalRewards,
  getAprForRewards,
  getApr,
} from '../../util/helpers'

import styles from './styles'

class LoanRequestDetails extends Component {
  constructor(props) {
    super(props)
    this.state = {
      baseRates: null,
      rewardRates: null,
      countryCode: '',
      province: '',
      adminFee: 0.0,
    }
  }

  componentDidMount() {
    this.getLoanRatesMaps()
    this.getJuris()
    this.adminFeeGet()
  }

  adminFeeGet = async () => {
    this.props.WebService.getAdminFee()
      .then(res => {
        let adminFee = Number(res.data?.originationFee || '0.0')
        this.setState({adminFee})
      })
      .catch(err => {
        //
        console.log('fee err', err)
      })
  }

  getLoanRatesMaps = () => {
    this.props.WebService.getLoanRatesMaps()
      .then(res => {
        this.setState({
          baseRates: res.data.baseRates || null,
          rewardRates: res.data.rewardRates || null,
        })
      })
      .catch(err => {
        console.log('getLoanRatesMaps err', err)
      })
  }

  getJuris = () => {
    this.props.WebService.getLoans().then(res => {
      let province = res.data[0]?.jurisdiction?.province
      let countryCode = res.data[0]?.jurisdiction?.countryCode
      this.setState({province, countryCode})
    })
  }

  render() {
    const {props} = this

    //let displayApr = props.loanData.apr ? props.loanData.apr : props.loanData.interestOnly

    let {baseRates, rewardRates, adminFee} = this.state
    let termLength = this.props.loanData?.term
    let selectedLTV = this.props.loanData?.baseLTV
    let isInterestOnly = this.props.loanData?.interestOnly

    const monthsToMilli = 2592000000 * termLength
    let loanAmount = Number(this.props.loanData?.amount)
    let baseRate = getBaseRate(selectedLTV, baseRates, termLength)
    baseRate = Math.round(baseRate * 1000000) / 1000000

    const saltRemptionValue = (Number(props.loanData.saltRedeemed) * 0.15).toFixed(2)

    let apr = props.loanData?.apr
    let showApr = Number(apr * 100)
      .toFixed(3)
      .slice(0, -1)

    let loanPreFee = loanAmount
    let loanFee = loanAmount / (1 - adminFee) - loanAmount
    loanFee = Number((Math.ceil(loanFee * 100) / 100).toFixed(2))
    loanAmount = loanAmount + loanFee

    //let decimalApr = getApr(loanAmount, selectedLTV, termLength, baseRate)

    let payment = 0
    const aprTo6 = Number(this.props.loanData?.interestRate || 0)?.toFixed(6)
    if (isInterestOnly) {
      payment = getPayment(aprTo6, loanPreFee, monthsToMilli, isInterestOnly)
    } else {
      payment = getPayment(baseRate, loanPreFee, monthsToMilli, isInterestOnly)
    }

    let showPayment = numberWithCommas(payment?.toFixed(2))

    let calcPayment = showPayment?.split(',')?.join('') || '0'

    let totalLoanCost = props.loanData.interestOnly
      ? Number(calcPayment) * Number(props.loanData.term) + Number(props.loanData.amount) + Number(saltRemptionValue)
      : Number(calcPayment) * Number(props.loanData.term) + Number(saltRemptionValue)

    totalLoanCost = totalLoanCost.toFixed(2)

    const interest = (totalLoanCost - Number(props.loanData.amount) - Number(saltRemptionValue)).toFixed(2)

    /*
    if (isInterestOnly) {
      loanTotal = payment * termLength + loanAmount
    } else {
      loanTotal = payment * termLength

    }
    */

    let collateralNeeded = Number(props.loanData.amount) / Number(props.loanData.baseLTV)
    collateralNeeded = collateralNeeded.toFixed(2)

    let showTotal = numberWithCommas(totalLoanCost.toString())

    let {province, countryCode} = this.state

    let jurisdiction = `${province}, ${countryCode}`

    //fix NaN load
    if (showPayment == 'NaN') {
      showPayment = '--'
    } else {
      showPayment = `$${showPayment}`
    }
    if (showTotal == 'NaN') {
      showTotal = '--'
    } else {
      showTotal = `$${showTotal}`
    }

    return (
      <Card marginTop={0} cardMarginBottom={10}>
        <View style={{alignSelf: 'stretch'}}>
          <TextBold style={[styles.statusTitle, {marginBottom: 18}]}>Loan Request Details</TextBold>
          <View style={styles.loanDetailBox}>
            <TextReg>LOAN AMOUNT</TextReg>
            <TextReg>{`$${numberWithCommas(props.loanData.amount)}`}</TextReg>
          </View>
          <View style={styles.loanDetailBox}>
            <TextReg>MONTHLY PAYMENT</TextReg>
            <TextReg>{`${showPayment}`}</TextReg>
          </View>
          <View style={styles.loanDetailBox}>
            <TextReg>APR</TextReg>
            <TextReg>{`${showApr}%`}</TextReg>
          </View>
          <View style={styles.loanDetailBox}>
            <TextReg>TOTAL LOAN COST</TextReg>
            <TextReg>{`${showTotal}`}</TextReg>
          </View>
          <View style={styles.loanDetailBox}>
            <TextReg>COLLATERAL NEEDED</TextReg>
            <TextReg>{`$${numberWithCommas(collateralNeeded)} (USD value)`}</TextReg>
          </View>
          <View style={styles.loanDetailBox}>
            <TextReg>CURRENCY</TextReg>
            <TextReg>{`${props.loanData.loanAsset}`}</TextReg>
          </View>
          <View style={styles.loanDetailBox}>
            <TextReg>TERM</TextReg>
            <TextReg>{`${props.loanData.term}  Months`}</TextReg>
          </View>
          <View style={styles.loanDetailBox}>
            <TextReg>STARTING LTV</TextReg>
            <TextReg>{`${Number(props.loanData.baseLTV) * 100}%`}</TextReg>
          </View>
          <View style={{...styles.loanDetailBox, borderBottomWidth: 0}}>
            <TextReg>JURISDICTION</TextReg>
            <TextReg>{jurisdiction}</TextReg>
          </View>
          <PayoutPreference navigation={this.props.navigation} />
        </View>
      </Card>
    )
  }
}

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  WebService: state.auth.WebService,
  launchDarkly: state.launchDarkly,
  user: state.user.user,
  accountRef: state.auth.account.ref,
})

export default connect(mapStateToProps)(LoanRequestDetails)
