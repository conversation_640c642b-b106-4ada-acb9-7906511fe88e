import React, { Component } from 'react'
import {
  View,
  Image,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
  ScrollView,
} from 'react-native'
import { connect } from 'react-redux'

import { WebView } from 'react-native-webview'

import { BackgroundHeader, TextReg, Button } from '../../../components'

import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'

class ConnectZabo extends Component {
  constructor(props) {
    super(props)
    this.state = {
      zaboWallets: [],
    }
  }

  componentDidMount() {
    //console.log('connect zabo')
    this.getZaboWallets()
  }

  getZaboWallets = () => {
    console.log('getZaboWallets')
    this.props.WebService.getZaboWallets()
      .then(res => {
        console.log('zabo wallets res', res)
        this.setState({ zaboWallets: res.data })
      })
      .catch(err => {
        console.log('zabo wallets err', err)
      })
  }

  deleteZaboWallet = id => {
    this.props.WebService.deleteZaboWallet(id)
      .then(res => {
        console.log('zabo delete res', res)
      })
      .catch(err => {
        console.log('zabo wallets err', err)
      })
  }

  getZaboTx = id => {
    this.props.WebService.getZaboTx(id)
      .then(res => {
        console.log('zabo tx res', res)
      })
      .catch(err => {
        console.log('zabo wallets err', err)
      })
  }

  toManualCompliance = () => {
    this.props.navigation.navigate('Unit21DepositCollateral')
    //this.props.navigation.navigate('ManualCompliance')
  }

  render() {
    const showZabos = this.state.zaboWallets.map((a, k) => (
      <View key={k}>
        <TextReg>{a.id}</TextReg>
        <TextReg>{a.provider.name}</TextReg>
        <Button onPress={() => this.deleteZaboWallet(a.id)}>
          <TextReg style={{ color: '#FFF' }}>Delete</TextReg>
        </Button>
        <Button onPress={() => this.getZaboTx(a.id)}>
          <TextReg style={{ color: '#FFF' }}>Tx</TextReg>
        </Button>
      </View>
    ))
    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader
          title={'Zabo'}
          goBack={() => this.props.navigation.goBack()}
        />

        <ScrollView>
          <View>{showZabos}</View>

          <View style={{ marginTop: 20, height: 650, width: 360 }}>
            <WebView
              source={{
                uri: `https://connect.zabo.com/connected?client_id=eodnwAdM3wFuDwTex4c511jJmjQlsUdYU8hG5HqGCQr73ZqRcvp3CAXciJzi7YS5&origin=localhost&zabo_env=sandbox&zabo_version=latest&redirect_uri=saltApp%3A%2F%2Fzabo2`,
              }}
              style={{ height: 650, width: 360 }}
              scalesPageToFit
              useWebKit={false}
            />
          </View>
        </ScrollView>
      </View>
    )
  }
}

/*
https://connect.zabo.com/connect?client_id=ccqq19Iq5EsU7sMR4hiWfNzs0zuw6gDqhZ2GnM30lGXoEXjoFJ9fp2V2qPgGxXOo&origin=localhost&zabo_env=sandbox&zabo_version=latest&redirect_uri
*/

/*
https://connect.zabo.com/connected?client_id=ccqq19Iq5EsU7sMR4hiWfNzs0zuw6gDqhZ2GnM30lGXoEXjoFJ9fp2V2qPgGxXOo&origin=localhost&zabo_env=sandbox&zabo_version=latest&redirect_uri
*/

ConnectZabo.navigationOptions = ({ navigation }) => ({
  title: 'Info',
  header: <View style={{ height: 40, position: 'absolute' }} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  tokenPrices: state.user.prices,
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(ConnectZabo)
