import React, {Component} from 'react';
import {View, ScrollView, TouchableOpacity, Image, Dimensions} from 'react-native';
import {connect} from 'react-redux';

import {TextBold, Card, TextReg, Button, CreateAccountModal} from '../../components';
import {logout} from '../../store/auth/auth.actions';
import {dig, numberWithCommas} from '../../util/helpers';

const {height: ScreenHeight} = Dimensions.get('window');

import styles from './styles';

class AccountSelection extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showCreateAccount: false,
      collatIsEnough: [false, false, false, false, false, false, false, false],
    };
  }

  sortByLoans = (a, b) => {
    if (a.loans[0]?.status == 'active') {
      return -1;
    }
    return 1;
  };

  toggleCreateAccount = () => {
    this.setState({showCreateAccount: !this.state.showCreateAccount});
  };

  closeAccountModal = () => {
    this.toggleCreateAccount();
    this.props.fetchApiData();
  };

  handleLogout = () => {
    this.props.dispatch(logout());
  };

  isCollatUnderTotal = async (a, loan) => {
    const collatShouldBe = Number(loan.amount) / Number(loan.baseLTV);

    let value = 0;
    const ref = a.account.accountRef;
    this.props.WebService.getWallets(ref)
      .then(res => {
        res.data.map(a => {
          value += Number(a.value);
        });
        const {collatIsEnough} = this.state;
        if (value >= collatShouldBe && !collatIsEnough[ref]) {
          collatIsEnough[ref] = true;
          this.setState({collatIsEnough});
        }
      })
      .catch(err => {
        console.log('getLoans err', err);
      });
  };

  render() {
    let sortedAccounts = null;
    let showAccounts = null;
    if (this.props.user.accounts) {
      const refAccounts = this.props.allAccounts.map(a => ({
        ...a,
        account: {
          ...a.account,
          accountRef: this.props.user.accounts.filter(b => b.name == a.account.name)[0]?.ref,
        },
      }));
      sortedAccounts = refAccounts.sort(this.sortByLoans);
      showAccounts = sortedAccounts.map((a, k) => {
        const loan = a.loans[0];
        let ltv = null;
        let collateralValue = null;
        let currentBalance = null;
        let showThreshold = require('../../imgs/checkmark.png');
        let imageStyle = styles.allAccountsStatusImg;
        let applicationStep = <TextReg style={{color: '#00FFBD'}}>requesting a loan.</TextReg>;
        if (loan?.status && loan?.status != 'active') {
          const hasLoanBankingInfo =
            dig(loan, 'depositBankAccount', 'documents', 'length') > 0 ||
            dig(loan, 'depositBankAccount', 'verifiedAt') ||
            (dig(loan, 'depositBankAccount', 'upholdId') && !dig(loan, 'depositBankAccount', 'document', 'rejectedAt'));
          if (!this.props.personalProfileComplete) {
            applicationStep = <TextReg style={{color: '#00FFBD'}}>completing your profile.</TextReg>;
          } else if (a.account.type == 'business' && !a.account.businessProfile.isComplete) {
            applicationStep = <TextReg style={{color: '#00FFBD'}}>completing your business profile.</TextReg>;
          } else if (!this.props.idVerificationStatus) {
            applicationStep = <TextReg style={{color: '#00FFBD'}}>completing ID verification.</TextReg>;
          } else if (!hasLoanBankingInfo) {
            applicationStep = <TextReg style={{color: '#00FFBD'}}>choosing a payout option.</TextReg>;
          } else {
            applicationStep = <TextReg style={{color: '#00FFBD'}}>awaiting review.</TextReg>;
            this.isCollatUnderTotal(a, loan);
            if (!this.state.collatIsEnough[a.account.accountRef]) {
              applicationStep = <TextReg style={{color: '#00FFBD'}}>depositing collateral.</TextReg>;
            }
          }
        }
        if (loan?.status == 'active') {
          ltv = (Number(loan.ltv) * 100).toFixed(2);
          collateralValue = Number(loan.collateralValue).toFixed(0);
          currentBalance = Number(loan.amortizationInfo?.currentBalance).toFixed(0);
          currentBalance = numberWithCommas(currentBalance);
          collateralValue = numberWithCommas(collateralValue);
        }
        if (loan?.thresholds) {
          const warning = Number(loan?.thresholds.warning) * 100;
          const marginCall = Number(loan?.thresholds.marginCall) * 100;
          const liquidation = Number(loan?.thresholds.liquidation) * 100;
          if (ltv > warning) {
            showThreshold = require('../../imgs/loanHealth/warningLoan.png');
            imageStyle = styles.allAccountsWarningImg;
          }
          if (ltv > marginCall) {
            showThreshold = require('../../imgs/loanHealth/alertLoan.png');
            imageStyle = styles.allAccountsStatusImg;
          }
          if (ltv > liquidation) {
            showThreshold = require('../../imgs/loanHealth/alertLoan.png');
            imageStyle = styles.allAccountsStatusImg;
          }
          if (a.account.isStabilized || loan?.awaitingLiquidation) {
            showThreshold = require('../../imgs/loanHealth/lifePreserver.png');
            imageStyle = styles.allAccountsStatusImg;
          }
        }
        return (
          <TouchableOpacity key={k} onPress={() => this.props.pickAccount(a.account.accountRef)}>
            <Card marginTop={0} cardMarginBottom={0}>
              <View style={styles.allAccountsRow}>
                <TextBold style={{fontSize: 18, marginBottom: 10}}>{a.account.name}</TextBold>
                {loan?.status == 'active' ? (
                  <View style={styles.allAccountsActive}>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                      <Image source={showThreshold} style={imageStyle} />
                      <View>
                        <TextReg>LTV</TextReg>
                        <TextBold style={{fontSize: 16}}>{`${ltv}%`}</TextBold>
                      </View>
                    </View>
                    <View>
                      <TextReg>Balance</TextReg>
                      <TextBold style={{fontSize: 16}}>{`$${currentBalance}`}</TextBold>
                    </View>
                    <View>
                      <TextReg>Collateral Value</TextReg>
                      <TextBold style={{fontSize: 16}}>{`$${collateralValue}`}</TextBold>
                    </View>
                  </View>
                ) : (
                  <View>
                    <TextReg style={{marginBottom: 6}}>This account has no active loans.</TextReg>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignContent: 'flex-start',
                        flexWrap: 'wrap',
                      }}>
                      <TextReg>Continue completing the approval process by</TextReg>
                      {applicationStep}
                    </View>
                  </View>
                )}
              </View>
            </Card>
          </TouchableOpacity>
        );
      });
    }
    return (
      <ScrollView style={{alignSelf: 'stretch', paddingTop: 20}} contentContainerStyle={{alignItems: 'center'}}>
        {this.state.showCreateAccount && <CreateAccountModal close={() => this.closeAccountModal()} />}
        <View style={styles.allAccountsBox}>
          <TextBold style={styles.allAccountsTitle}>{`Welcome${this.props.firstName ? `, ` + this.props.firstName : `to SALT`}!`}</TextBold>
          <TextReg style={{marginBottom: 8}}>Please select an account to see more details.</TextReg>
        </View>
        {showAccounts}
        {this.props.user?.accounts?.length < 7 && (
          <Button style={styles.allAccountsAddButton} onPress={() => this.toggleCreateAccount()}>
            <TextBold style={{color: '#000', fontSize: 17}}>ADD NEW ACCOUNT</TextBold>
          </Button>
        )}
        <TouchableOpacity onPress={this.handleLogout}>
          <View style={styles.logoutBox}>
            <TextBold style={styles.logoutText}>Log Out</TextBold>
          </View>
        </TouchableOpacity>
        <View style={{height: 180, alignSelf: 'stretch'}} />
      </ScrollView>
    );
  }
}

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  WebService: state.auth.WebService,
  user: state.user.user,
});

export default connect(mapStateToProps)(AccountSelection);
