import React from 'react';
import {View, Image} from 'react-native';

import {TextBold, TextReg, Card} from '../../components';

import styles from './styles';

const NextPayment = props => (
  <Card marginTop={0} cardMarginBottom={0}>
    <View style={{alignSelf: 'stretch', marginBottom: 12}}>
      <TextBold style={styles.statusTitle}>Next Payment</TextBold>
      <View style={{flexDirection: 'row', paddingLeft: 10, marginTop: 8}}>
        <TextReg style={{fontSize: 16}}>Autopay:</TextReg>
        <TextBold style={{fontSize: 16, marginLeft: 6, marginTop: 1}}>{props.automatedPaymentType ? 'On' : 'Off'}</TextBold>
        {props.automatedPaymentType && (
          <Image
            source={require('../../imgs/checkmark.png')}
            style={{
              height: 24,
              width: 24,
              marginLeft: 6,
              marginTop: -2,
            }}
          />
        )}
      </View>
      <TextBold style={{fontSize: 16, paddingLeft: 10, marginTop: 8}}>
        ${props.nextPaymentAmount} is due on {props.nextPaymentDate}
      </TextBold>
      {props.automatedPaymentType ? (
        <TextReg style={{paddingLeft: 10, marginTop: 8}}>Payment scheduled on {props.nextPaymentDate} at 4:00 PM</TextReg>
      ) : (
        <TextReg style={{paddingLeft: 10, marginTop: 8}}>No payment scheduled</TextReg>
      )}
    </View>
  </Card>
);

export default NextPayment;
