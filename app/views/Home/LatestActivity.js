import React, { Component } from 'react'
import { View, Image, TouchableOpacity } from 'react-native'
import { connect } from 'react-redux'

import { TextBold, Card, TextReg } from '../../components'

class LatestActivity extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  toTitleCase = (phrase) =>
    phrase
      .toLowerCase()
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')

  goToWallet = (token) => {
    const item = { title: token }
    this.props.navigation.navigate('Detail', item)
  }

  render() {
    const images = [
      {
        type: 'deposit',
        source: require('../../imgs/homeActivities/deposit.png'),
        width: 20,
        height: 22
      },
      {
        type: 'refund',
        source: require(`../../imgs/homeActivities/refund.png`),
        width: 23,
        height: 28
      },
      {
        type: 'monthly_replacement',
        source: require(`../../imgs/homeActivities/repayment.png`),
        width: 22,
        height: 18
      },
      {
        type: 'withdrawal',
        amount: '$3,917.70',
        source: require(`../../imgs/homeActivities/withdrawal.png`),
        width: 20,
        height: 22
      },
      {
        type: 'purchase',
        source: require(`../../imgs/homeActivities/purchase.png`),
        width: 24,
        height: 18
      },
      {
        type: 'wallet_initialization',
        source: require(`../../imgs/homeActivities/walletInit.png`),
        width: 22,
        height: 22
      },
      {
        type: 'gas_payment',
        source: require(`../../imgs/homeActivities/walletInit.png`),
        width: 22,
        height: 22
      },
      {
        type: 'unknown',
        source: require(`../../imgs/homeActivities/walletInit.png`),
        width: 22,
        height: 22
      },
      {
        type: 'commingle',
        source: require(`../../imgs/homeActivities/walletInit.png`),
        width: 22,
        height: 22
      }
    ]

    let latestTransactions = this.props.latestTransactions || []

    latestTransactions = latestTransactions.filter((a) => a.reason !== 'commingle')

    const formatedTransactions = latestTransactions.map((a) => {
      let amount = Number(a.amount)
      if (a.currency === 'XRP') {
        amount = amount.toFixed(6)
      } else {
        amount = amount.toFixed(8)
      }
      let showName = a.reason.replace('_', ' ')
      if (showName) {
        showName = this.toTitleCase(showName)
      }
      return {
        showName,
        amount,
        image: images.filter((b) => a.reason === b.type)[0] || {
          type: 'Unknown',
          source: require(`../../imgs/homeActivities/walletInit.png`),
          width: 22,
          height: 22
        },
        currency: a.currency
      }
    })

    const showActivityRows = formatedTransactions.map((a, k) => (
      <TouchableOpacity key={k} onPress={() => this.goToWallet(a.currency)}>
        <View
          style={{
            height: 60,
            borderBottomColor: '#f0f0f0',
            borderBottomWidth: 1,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingRight: 8
          }}
        >
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingLeft: 4,
              paddingRight: 4
            }}
          >
            <Image
              style={{
                height: a.image.height,
                width: a.image.width,
                marginRight: 34 - a.image.width,
                opacity: 0.8
              }}
              source={a.image.source}
            />
            <TextReg style={{ fontSize: 16 }}>{a.showName}</TextReg>
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <TextBold style={{ fontSize: 17, marginRight: 5 }}>{a.amount}</TextBold>
            <TextReg style={{ fontSize: 14, marginTop: 1 }}>{a.currency}</TextReg>
          </View>
        </View>
      </TouchableOpacity>
    ))

    return (
      <Card marginTop={0}>
        <View style={{ alignSelf: 'stretch' }}>
          <TextBold
            style={{
              fontSize: 20,
              marginBottom: 10,
              paddingLeft: 10,
              marginTop: 4
            }}
          >
            Latest Activities
          </TextBold>
          {showActivityRows}
        </View>
      </Card>
    )
  }
}

LatestActivity.navigationOptions = ({ navigation }) => ({
  title: null,
  header: <View style={{ height: 40, position: 'absolute' }} />
})

const mapStateToProps = (state) => ({
  tokenPrices: state.user.prices,
  WebService: state.auth.WebService
})

export default connect(mapStateToProps)(LatestActivity)
