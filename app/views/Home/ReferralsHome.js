import React, { Component } from 'react'
import { View, Image, TouchableOpacity, Clipboard, Share } from 'react-native'

import { TextBold, TextReg, Card, Button } from '../../components'

import styles from './styles'

class ReferralsHome extends Component {
  constructor(props) {
    super(props)
    this.state = {
      showCopied: false
    }
  }

  goToReferrals = () => {
    this.props.navigation.navigate('Referrals')
  }

  copyCode = (code) => {
    Clipboard.setString(code)
    this.setState({ showCopied: true }, () => {
      setTimeout(() => {
        this.setState({ showCopied: false })
      }, 1400)
    })
  }

  onShare = async () => {
    try {
      const result = await Share.share({
        url: `https://borrower.saltlending.com/register?referralCode=${this.props.referralCode}`
      })
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
        } else {
          // shared
        }
      } else if (result.action === Share.dismissedAction) {
        // dismissed
      }
    } catch (error) {
      console.log('error')
    }
  }

  render() {
    return (
      <Card marginTop={0} cardMarginBottom={0} style={{ backgroundColor: '#5a33e3' }}>
        <View style={{ alignSelf: 'stretch', alignItems: 'center' }}>
          <Image
            source={require('../../imgs/graphics/noLoanImg.png')}
            style={{ height: 36, width: 36, marginBottom: 14, marginTop: 4 }}
          />
          <TextBold style={{ marginTop: -8, fontSize: 20, color: '#FFF' }}>Refer Friends to SALT</TextBold>
          <TextReg style={{ color: '#FFF', fontSize: 16, marginTop: 8 }}>Get $50 in Bitcoin each</TextReg>
          {this.props.signed ? (
            <View style={{ flexDirection: 'row' }}>
              <TouchableOpacity
                onPress={() => this.copyCode(this.props.referralCode)}
                style={{
                  borderRadius: 14,
                  backgroundColor: this.state.showCopied ? '#00ffc1' : '#FFF',
                  width: 148,
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginBottom: 18,
                  marginTop: 20
                }}
              >
                <TextBold
                  style={{
                    color: '#000',
                    width: 80,
                    marginLeft: 16,
                    marginRight: 8,
                    textAlign: 'center',
                    height: 40,
                    paddingTop: 11
                  }}
                >
                  {this.state.showCopied ? this.props.referralCode : `Copy Code`}
                </TextBold>
                <View
                  style={{
                    height: 36,
                    width: 36,
                    borderRadius: 14,
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {this.state.showCopied ? (
                    <Image style={{ height: 36, width: 36 }} source={require('../../imgs/checkmark.png')} />
                  ) : (
                    <Image style={{ height: 20, width: 20 }} source={require('../../imgs/referrals/copy.png')} />
                  )}
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => this.onShare()}
                style={{
                  flexDirection: 'row',
                  borderRadius: 14,
                  backgroundColor: '#FFF',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: 40,
                  marginTop: 20,
                  marginLeft: 12,
                  width: 120
                }}
              >
                <TextBold style={{ color: '#000', marginRight: 10 }}>Share</TextBold>
                <Image
                  style={{ height: 19, width: 19, opacity: 0.9 }}
                  source={require('../../imgs/referrals/share.png')}
                />
              </TouchableOpacity>
            </View>
          ) : (
            <Button
              style={{
                alignSelf: 'stretch',
                marginTop: 14,
                backgroundColor: '#0A2E67',
                borderColor: '#FFF',
                borderWidth: 1,
                marginLeft: 10,
                marginRight: 10,
                marginBottom: 10
              }}
              onPress={() => this.goToReferrals()}
            >
              <TextBold
                style={{
                  fontSize: 17,
                  color: '#FFF'
                }}
              >
                GET STARTED
              </TextBold>
            </Button>
          )}
        </View>
      </Card>
    )
  }
}

export default ReferralsHome
