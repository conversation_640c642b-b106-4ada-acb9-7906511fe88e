import React, {Component} from 'react'
import {View, Image, TouchableOpacity} from 'react-native'
import {connect} from 'react-redux'

import {TextBold, Card, TextReg} from '../../components'
import {numberWithCommas} from '../../util/helpers'
import {cryptoNameMap} from '../../util/enumerables'
import {getTokenPic} from '../../util/tokens'

import styles from './styles'

class CollateralPortfolio extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  goToCollateral = token => {
    const item = {title: token}
    this.props.navigation.navigate('Detail', item)
  }

  goToWallets = () => {
    this.props.navigation.navigate('Collateral')
  }

  sortPriceTimesBalance = (a, b) => {
    if (a.projectedBalance * a?.price > b?.projectedBalance * b?.price) {
      return -1
    } else if (a.projectedBalance * a?.price <= b?.projectedBalance * b?.price) {
      return 1
    }
    return 0
  }

  render() {
    const {collaterals} = this.props.loanData
    const sortedCollaterals = collaterals.sort(this.sortPriceTimesBalance)
    const top5Collaterals = sortedCollaterals.slice(0, 5)
    const showCollaterals = top5Collaterals.map((a, k) => {
      let showValue = Number(a.value).toFixed(2)
      showValue = numberWithCommas(showValue)
      const showImg = getTokenPic(a.currency)
      const showName = cryptoNameMap.get(a.currency.toLowerCase())
      return (
        <TouchableOpacity key={k} onPress={() => this.goToCollateral(a.currency)}>
          <View style={styles.portfolioBox}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <Image style={styles.portfolioImg} source={showImg} />
              <TextReg style={{fontSize: 16}}>{`${showName} (${a.currency})`}</TextReg>
            </View>
            <View style={styles.portfolioValue}>
              <TextReg style={{fontSize: 16}}>{`$ ${showValue}`}</TextReg>
              <Image
                source={require('../../imgs/rightArrow.png')}
                style={{
                  height: 18,
                  width: 18,
                  borderRadius: 0,
                  marginLeft: 12,
                }}
              />
            </View>
          </View>
        </TouchableOpacity>
      )
    })
    return (
      <Card marginTop={0}>
        <View style={{alignSelf: 'stretch'}}>
          <TextBold style={styles.portfolioTitle}>Collateral Portfolio</TextBold>
          {showCollaterals}
          <TouchableOpacity onPress={() => this.goToWallets()}>
            <View style={styles.portfolioFooter}>
              <TextBold style={{color: '#00FFBD', fontSize: 14}}>SEE MORE</TextBold>
            </View>
          </TouchableOpacity>
        </View>
      </Card>
    )
  }
}

CollateralPortfolio.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(CollateralPortfolio)
