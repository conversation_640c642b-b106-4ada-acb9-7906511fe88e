import React, { Component } from 'react'
import { View } from 'react-native'
import { connect } from 'react-redux'

import { TextBold, Card, LoanHealthBar, ShowThresholds } from '../../components'

import styles from './styles'

class LoanHealth extends Component {
  constructor(props) {
    super(props)
    this.state = {
      barWidth: 320
    }
  }

  getBarWidth = (layout) => {
    const { x, y, width, height } = layout
    this.setState({ barWidth: width - 20 })
  }

  render() {
    let thresholds = [
      { color: '#f7d956', name: 'Warning', percentage: '75%', type: 'warning' },
      {
        color: '#e5705a',
        name: 'Margin Call',
        percentage: '83%',
        type: 'marginCall'
      },
      {
        color: '#afafaf',
        name: 'Stabilization',
        percentage: '90%',
        type: 'liquidation'
      }
    ]

    const thresholdsArr = []
    for (const property in this.props.dynamicThresholds) {
      thresholdsArr.push({
        type: property,
        percentage: this.props.dynamicThresholds[property]
      })
    }
    thresholds = thresholds.map((a) => {
      const decimalPercentage = thresholdsArr.filter((b) => b.type === a.type)[0].percentage
      let percentage = (decimalPercentage * 100).toFixed(2)
      percentage = `${percentage}%`
      return {
        ...a,
        percentage,
        decimalPercentage
      }
    })

    const awaitingLiquidation = this.props.loanData?.awaitingLiquidation || false

    let decimalLtv = this.props.loanData?.ltv || 0.0

    const isStabilized = this.props.user?.accounts?.filter((a) => a.ref == this.props.accountRef)[0]?.product?.loan
      ?.isStabilized

    if (decimalLtv > this.props.loanData.thresholds.liquidation && !isStabilized) {
      decimalLtv = this.props.loanData.thresholds.liquidation
    }

    if (awaitingLiquidation) {
      decimalLtv = this.props.loanData.thresholds.liquidation
    }

    const { barWidth } = this.state
    let ltvWidth = decimalLtv * barWidth
    if (ltvWidth > barWidth) {
      ltvWidth = barWidth
    }

    const ltv = (Number(decimalLtv) * 100).toFixed(2)

    let barColor = '#34e89e'
    if (decimalLtv > thresholds[0].decimalPercentage) {
      barColor = thresholds[0].color
    }
    if (decimalLtv > thresholds[1].decimalPercentage) {
      barColor = thresholds[1].color
    }
    if (decimalLtv >= thresholds[2].decimalPercentage || awaitingLiquidation) {
      barColor = thresholds[2].color
    }

    if (isStabilized) {
      barColor = '#afafaf'
    }

    return (
      <Card cardMarginBottom={0}>
        <View
          onLayout={(event) => {
            this.getBarWidth(event.nativeEvent.layout)
          }}
          style={{ alignSelf: 'stretch', marginBottom: 12 }}
        >
          <TextBold style={styles.statusTitle}>Loan Health</TextBold>
          <LoanHealthBar barWidth={barWidth} ltv={ltv} ltvWidth={ltvWidth} barColor={barColor} />
          <ShowThresholds thresholds={thresholds} />
        </View>
      </Card>
    )
  }
}

const mapStateToProps = (state) => ({
  loanData: state.user.loanData || {},
  user: state.user.user,
  accountRef: state.auth.account.ref,
  WebService: state.auth.WebService
})

export default connect(mapStateToProps)(LoanHealth)
