/* eslint-disable */
import React, {useEffect} from 'react'
import {useDispatch} from 'react-redux'
import {View, Text} from 'react-native'

import {
  PriceClient,
  UnaryPriceRequest,
  HistoricalPriceRequest,
  AssetPairId,
} from './proto/api/trading/v1/price_grpc_web_pb'
import * as pb from 'google-protobuf/google/protobuf/empty_pb'
//import {Price} from './proto/api/trading/v1/price_pb'

import {EchoServiceClient} from './echo_grpc_web_pb'
import {EchoRequest} from './echo_pb'

import {updatePrices, updatePrices24h} from '../../../store/user/user.actions'
import config from '../../../config.json'

let GrpcTest = ({getPrices, launchDarkly}) => {
  const dispatch = useDispatch()

  useEffect(() => {
    tryPrice()
  }, [])

  let convertTime = timeIn => {
    const timestampInSeconds = 1715657293
    const timestampInMilliseconds = timestampInSeconds * 1000
    const date = new Date(timeIn)
    const isoString = date.toISOString()
    return isoString
  }

  let tryPrice = async () => {
    let pClient = new PriceClient(config.grpc)

    const request = new pb.Empty()
    pClient.getAssetPairs(request, {}, (err, response) => {
      if (err) {
        console.error('grpc error:', err)
        getPrices()
      } else {
        //usdp pre filter  50211
        let filterList = response.toObject()?.assetPairsList

        if (filterList?.length < 1) {
          filterList = response
            .toObject()
            ?.assetPairsList?.filter(a => a.baseCurrency.assetId != 50211)
        }

        let arrAssetPairId = filterList?.map(a => {
          return new AssetPairId()
            .setBaseCurrencyId(a.baseCurrency.assetId)
            .setQuoteCurrencyId(a.quoteCurrency.assetId)
        })

        const setReq = new UnaryPriceRequest().setAssetPairsList(arrAssetPairId)

        let priceArr = [
          'BCH-USD',
          'BTC-USD',
          'DASH-USD',
          'DOGE-USD',
          'ETH-USD',
          'LTC-USD',
          'PAXG-USD',
          'SALT-USD',
          'USDC-USD',
          'USDP-USD',
          'USDT-USD',
          'XRP-USD',
        ]

        pClient.getPrice(setReq, {}, (err, response) => {
          if (err) {
            console.error('get price req error:', err)
            getPrices()
          } else {
            let formatToday = response.toObject()?.pricesList?.map(a => {
              let asset = a.assetPair?.baseCurrency?.ticker

              return {
                asset: a.assetPair?.baseCurrency?.ticker,
                metric: 'USD',
                price: asset == 'SALT' ? 0.15 : a.price,
                time: convertTime(a.time.seconds),
              }
            })

            const today = priceArr.reduce((acc, pair) => {
              const [asset, metric] = pair.split('-')
              let i = formatToday?.filter(b => b.asset == asset)[0]
              acc[pair] = {
                asset,
                atomic: false,
                isStale: false,
                metric,
                price: i?.price || 0,
                time: i?.time,
              }
              return acc
            }, {})

            dispatch(updatePrices(today))
          }
        })

        /*
        enum TimeDenomination {
          SEC = 0;
          MIN = 1;
          HRS = 2;
          DAY = 3;
          WKS = 4;
        }
        */

        //h2
        //instead we just run getPrices(yesterdayOnly = true), in HomeScreen

        /*
        const historicalPriceRequest = new HistoricalPriceRequest()
          .setAssetPairsList(arrAssetPairId)
          .setTimeDenomination(3)
          .setTimeDuration(2)

        pClient.getHistoricalPrice(historicalPriceRequest, {}, (err, response) => {
          if (err) {
            console.error('historical error:', err)
            getPrices()
          } else {
            let formatYesterday = response.toObject()?.historicalPricesList?.map(a => {
              let price = a.quotesList[0]?.price || 0
              let time = a.quotesList[0]?.time?.seconds || 0
              let asset = a.assetPair?.baseCurrency?.ticker
              return {
                asset,
                metric: 'USD',
                price: asset == 'SALT' ? 0.15 : price,
                time: convertTime(time),
              }
            })

            const yesterday = priceArr.reduce((acc, pair) => {
              const [asset, metric] = pair.split('-')
              let i = formatYesterday?.filter(b => b.asset == asset)[0]
              acc[pair] = {
                asset,
                atomic: false,
                isStale: false,
                metric,
                price: i?.price,
                time: i?.time,
              }
              return acc
            }, {})

            dispatch(updatePrices24h(yesterday))
          }
        })
        */
      }
    })
  }

  return <View />
}

export default GrpcTest
