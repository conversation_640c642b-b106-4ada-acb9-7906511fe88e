// source: api/trading/v1/price.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() { return this || window || global || self || Function('return this')(); }).call(null);

var google_protobuf_timestamp_pb = require('google-protobuf/google/protobuf/timestamp_pb.js');
goog.object.extend(proto, google_protobuf_timestamp_pb);
var google_protobuf_empty_pb = require('google-protobuf/google/protobuf/empty_pb.js');
goog.object.extend(proto, google_protobuf_empty_pb);
goog.exportSymbol('proto.api.trading.v1.AssetPair', null, global);
goog.exportSymbol('proto.api.trading.v1.AssetPairId', null, global);
goog.exportSymbol('proto.api.trading.v1.AssetPairsResponse', null, global);
goog.exportSymbol('proto.api.trading.v1.Currency', null, global);
goog.exportSymbol('proto.api.trading.v1.HistoricalPrice', null, global);
goog.exportSymbol('proto.api.trading.v1.HistoricalPriceEvent', null, global);
goog.exportSymbol('proto.api.trading.v1.HistoricalPriceRequest', null, global);
goog.exportSymbol('proto.api.trading.v1.HistoricalPriceRequest.ParameterCase', null, global);
goog.exportSymbol('proto.api.trading.v1.HistoricalPriceResponse', null, global);
goog.exportSymbol('proto.api.trading.v1.PriceEvent', null, global);
goog.exportSymbol('proto.api.trading.v1.PriceRequest', null, global);
goog.exportSymbol('proto.api.trading.v1.PriceResponse', null, global);
goog.exportSymbol('proto.api.trading.v1.PriceSource', null, global);
goog.exportSymbol('proto.api.trading.v1.TimeDenomination', null, global);
goog.exportSymbol('proto.api.trading.v1.UnaryPriceRequest', null, global);
goog.exportSymbol('proto.api.trading.v1.UnaryPriceRequest.ParameterCase', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.api.trading.v1.AssetPairsResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.api.trading.v1.AssetPairsResponse.repeatedFields_, null);
};
goog.inherits(proto.api.trading.v1.AssetPairsResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.api.trading.v1.AssetPairsResponse.displayName = 'proto.api.trading.v1.AssetPairsResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.api.trading.v1.PriceRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.api.trading.v1.PriceRequest.repeatedFields_, null);
};
goog.inherits(proto.api.trading.v1.PriceRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.api.trading.v1.PriceRequest.displayName = 'proto.api.trading.v1.PriceRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.api.trading.v1.UnaryPriceRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.api.trading.v1.UnaryPriceRequest.repeatedFields_, proto.api.trading.v1.UnaryPriceRequest.oneofGroups_);
};
goog.inherits(proto.api.trading.v1.UnaryPriceRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.api.trading.v1.UnaryPriceRequest.displayName = 'proto.api.trading.v1.UnaryPriceRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.api.trading.v1.PriceResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.api.trading.v1.PriceResponse.repeatedFields_, null);
};
goog.inherits(proto.api.trading.v1.PriceResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.api.trading.v1.PriceResponse.displayName = 'proto.api.trading.v1.PriceResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.api.trading.v1.HistoricalPriceRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.api.trading.v1.HistoricalPriceRequest.repeatedFields_, proto.api.trading.v1.HistoricalPriceRequest.oneofGroups_);
};
goog.inherits(proto.api.trading.v1.HistoricalPriceRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.api.trading.v1.HistoricalPriceRequest.displayName = 'proto.api.trading.v1.HistoricalPriceRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.api.trading.v1.HistoricalPriceResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.api.trading.v1.HistoricalPriceResponse.repeatedFields_, null);
};
goog.inherits(proto.api.trading.v1.HistoricalPriceResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.api.trading.v1.HistoricalPriceResponse.displayName = 'proto.api.trading.v1.HistoricalPriceResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.api.trading.v1.PriceEvent = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.api.trading.v1.PriceEvent, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.api.trading.v1.PriceEvent.displayName = 'proto.api.trading.v1.PriceEvent';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.api.trading.v1.HistoricalPriceEvent = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.api.trading.v1.HistoricalPriceEvent, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.api.trading.v1.HistoricalPriceEvent.displayName = 'proto.api.trading.v1.HistoricalPriceEvent';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.api.trading.v1.HistoricalPrice = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.api.trading.v1.HistoricalPrice.repeatedFields_, null);
};
goog.inherits(proto.api.trading.v1.HistoricalPrice, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.api.trading.v1.HistoricalPrice.displayName = 'proto.api.trading.v1.HistoricalPrice';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.api.trading.v1.AssetPair = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.api.trading.v1.AssetPair, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.api.trading.v1.AssetPair.displayName = 'proto.api.trading.v1.AssetPair';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.api.trading.v1.AssetPairId = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.api.trading.v1.AssetPairId, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.api.trading.v1.AssetPairId.displayName = 'proto.api.trading.v1.AssetPairId';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.api.trading.v1.Currency = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.api.trading.v1.Currency, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.api.trading.v1.Currency.displayName = 'proto.api.trading.v1.Currency';
}

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.api.trading.v1.AssetPairsResponse.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.api.trading.v1.AssetPairsResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.api.trading.v1.AssetPairsResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.api.trading.v1.AssetPairsResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.AssetPairsResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    assetPairsList: jspb.Message.toObjectList(msg.getAssetPairsList(),
    proto.api.trading.v1.AssetPair.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.api.trading.v1.AssetPairsResponse}
 */
proto.api.trading.v1.AssetPairsResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.api.trading.v1.AssetPairsResponse;
  return proto.api.trading.v1.AssetPairsResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.api.trading.v1.AssetPairsResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.api.trading.v1.AssetPairsResponse}
 */
proto.api.trading.v1.AssetPairsResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.api.trading.v1.AssetPair;
      reader.readMessage(value,proto.api.trading.v1.AssetPair.deserializeBinaryFromReader);
      msg.addAssetPairs(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.api.trading.v1.AssetPairsResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.api.trading.v1.AssetPairsResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.api.trading.v1.AssetPairsResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.AssetPairsResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAssetPairsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.api.trading.v1.AssetPair.serializeBinaryToWriter
    );
  }
};


/**
 * repeated AssetPair asset_pairs = 1;
 * @return {!Array<!proto.api.trading.v1.AssetPair>}
 */
proto.api.trading.v1.AssetPairsResponse.prototype.getAssetPairsList = function() {
  return /** @type{!Array<!proto.api.trading.v1.AssetPair>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.api.trading.v1.AssetPair, 1));
};


/**
 * @param {!Array<!proto.api.trading.v1.AssetPair>} value
 * @return {!proto.api.trading.v1.AssetPairsResponse} returns this
*/
proto.api.trading.v1.AssetPairsResponse.prototype.setAssetPairsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.api.trading.v1.AssetPair=} opt_value
 * @param {number=} opt_index
 * @return {!proto.api.trading.v1.AssetPair}
 */
proto.api.trading.v1.AssetPairsResponse.prototype.addAssetPairs = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.api.trading.v1.AssetPair, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.api.trading.v1.AssetPairsResponse} returns this
 */
proto.api.trading.v1.AssetPairsResponse.prototype.clearAssetPairsList = function() {
  return this.setAssetPairsList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.api.trading.v1.PriceRequest.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.api.trading.v1.PriceRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.api.trading.v1.PriceRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.api.trading.v1.PriceRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.PriceRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    assetPairsList: jspb.Message.toObjectList(msg.getAssetPairsList(),
    proto.api.trading.v1.AssetPairId.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.api.trading.v1.PriceRequest}
 */
proto.api.trading.v1.PriceRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.api.trading.v1.PriceRequest;
  return proto.api.trading.v1.PriceRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.api.trading.v1.PriceRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.api.trading.v1.PriceRequest}
 */
proto.api.trading.v1.PriceRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.api.trading.v1.AssetPairId;
      reader.readMessage(value,proto.api.trading.v1.AssetPairId.deserializeBinaryFromReader);
      msg.addAssetPairs(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.api.trading.v1.PriceRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.api.trading.v1.PriceRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.api.trading.v1.PriceRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.PriceRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAssetPairsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.api.trading.v1.AssetPairId.serializeBinaryToWriter
    );
  }
};


/**
 * repeated AssetPairId asset_pairs = 1;
 * @return {!Array<!proto.api.trading.v1.AssetPairId>}
 */
proto.api.trading.v1.PriceRequest.prototype.getAssetPairsList = function() {
  return /** @type{!Array<!proto.api.trading.v1.AssetPairId>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.api.trading.v1.AssetPairId, 1));
};


/**
 * @param {!Array<!proto.api.trading.v1.AssetPairId>} value
 * @return {!proto.api.trading.v1.PriceRequest} returns this
*/
proto.api.trading.v1.PriceRequest.prototype.setAssetPairsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.api.trading.v1.AssetPairId=} opt_value
 * @param {number=} opt_index
 * @return {!proto.api.trading.v1.AssetPairId}
 */
proto.api.trading.v1.PriceRequest.prototype.addAssetPairs = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.api.trading.v1.AssetPairId, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.api.trading.v1.PriceRequest} returns this
 */
proto.api.trading.v1.PriceRequest.prototype.clearAssetPairsList = function() {
  return this.setAssetPairsList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.api.trading.v1.UnaryPriceRequest.repeatedFields_ = [1];

/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.api.trading.v1.UnaryPriceRequest.oneofGroups_ = [[4]];

/**
 * @enum {number}
 */
proto.api.trading.v1.UnaryPriceRequest.ParameterCase = {
  PARAMETER_NOT_SET: 0,
  TIME: 4
};

/**
 * @return {proto.api.trading.v1.UnaryPriceRequest.ParameterCase}
 */
proto.api.trading.v1.UnaryPriceRequest.prototype.getParameterCase = function() {
  return /** @type {proto.api.trading.v1.UnaryPriceRequest.ParameterCase} */(jspb.Message.computeOneofCase(this, proto.api.trading.v1.UnaryPriceRequest.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.api.trading.v1.UnaryPriceRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.api.trading.v1.UnaryPriceRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.api.trading.v1.UnaryPriceRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.UnaryPriceRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    assetPairsList: jspb.Message.toObjectList(msg.getAssetPairsList(),
    proto.api.trading.v1.AssetPairId.toObject, includeInstance),
    time: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.api.trading.v1.UnaryPriceRequest}
 */
proto.api.trading.v1.UnaryPriceRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.api.trading.v1.UnaryPriceRequest;
  return proto.api.trading.v1.UnaryPriceRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.api.trading.v1.UnaryPriceRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.api.trading.v1.UnaryPriceRequest}
 */
proto.api.trading.v1.UnaryPriceRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.api.trading.v1.AssetPairId;
      reader.readMessage(value,proto.api.trading.v1.AssetPairId.deserializeBinaryFromReader);
      msg.addAssetPairs(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setTime(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.api.trading.v1.UnaryPriceRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.api.trading.v1.UnaryPriceRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.api.trading.v1.UnaryPriceRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.UnaryPriceRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAssetPairsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.api.trading.v1.AssetPairId.serializeBinaryToWriter
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeInt64(
      4,
      f
    );
  }
};


/**
 * repeated AssetPairId asset_pairs = 1;
 * @return {!Array<!proto.api.trading.v1.AssetPairId>}
 */
proto.api.trading.v1.UnaryPriceRequest.prototype.getAssetPairsList = function() {
  return /** @type{!Array<!proto.api.trading.v1.AssetPairId>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.api.trading.v1.AssetPairId, 1));
};


/**
 * @param {!Array<!proto.api.trading.v1.AssetPairId>} value
 * @return {!proto.api.trading.v1.UnaryPriceRequest} returns this
*/
proto.api.trading.v1.UnaryPriceRequest.prototype.setAssetPairsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.api.trading.v1.AssetPairId=} opt_value
 * @param {number=} opt_index
 * @return {!proto.api.trading.v1.AssetPairId}
 */
proto.api.trading.v1.UnaryPriceRequest.prototype.addAssetPairs = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.api.trading.v1.AssetPairId, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.api.trading.v1.UnaryPriceRequest} returns this
 */
proto.api.trading.v1.UnaryPriceRequest.prototype.clearAssetPairsList = function() {
  return this.setAssetPairsList([]);
};


/**
 * optional int64 time = 4;
 * @return {number}
 */
proto.api.trading.v1.UnaryPriceRequest.prototype.getTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.api.trading.v1.UnaryPriceRequest} returns this
 */
proto.api.trading.v1.UnaryPriceRequest.prototype.setTime = function(value) {
  return jspb.Message.setOneofField(this, 4, proto.api.trading.v1.UnaryPriceRequest.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.api.trading.v1.UnaryPriceRequest} returns this
 */
proto.api.trading.v1.UnaryPriceRequest.prototype.clearTime = function() {
  return jspb.Message.setOneofField(this, 4, proto.api.trading.v1.UnaryPriceRequest.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.api.trading.v1.UnaryPriceRequest.prototype.hasTime = function() {
  return jspb.Message.getField(this, 4) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.api.trading.v1.PriceResponse.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.api.trading.v1.PriceResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.api.trading.v1.PriceResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.api.trading.v1.PriceResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.PriceResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    pricesList: jspb.Message.toObjectList(msg.getPricesList(),
    proto.api.trading.v1.PriceEvent.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.api.trading.v1.PriceResponse}
 */
proto.api.trading.v1.PriceResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.api.trading.v1.PriceResponse;
  return proto.api.trading.v1.PriceResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.api.trading.v1.PriceResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.api.trading.v1.PriceResponse}
 */
proto.api.trading.v1.PriceResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.api.trading.v1.PriceEvent;
      reader.readMessage(value,proto.api.trading.v1.PriceEvent.deserializeBinaryFromReader);
      msg.addPrices(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.api.trading.v1.PriceResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.api.trading.v1.PriceResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.api.trading.v1.PriceResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.PriceResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPricesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.api.trading.v1.PriceEvent.serializeBinaryToWriter
    );
  }
};


/**
 * repeated PriceEvent prices = 1;
 * @return {!Array<!proto.api.trading.v1.PriceEvent>}
 */
proto.api.trading.v1.PriceResponse.prototype.getPricesList = function() {
  return /** @type{!Array<!proto.api.trading.v1.PriceEvent>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.api.trading.v1.PriceEvent, 1));
};


/**
 * @param {!Array<!proto.api.trading.v1.PriceEvent>} value
 * @return {!proto.api.trading.v1.PriceResponse} returns this
*/
proto.api.trading.v1.PriceResponse.prototype.setPricesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.api.trading.v1.PriceEvent=} opt_value
 * @param {number=} opt_index
 * @return {!proto.api.trading.v1.PriceEvent}
 */
proto.api.trading.v1.PriceResponse.prototype.addPrices = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.api.trading.v1.PriceEvent, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.api.trading.v1.PriceResponse} returns this
 */
proto.api.trading.v1.PriceResponse.prototype.clearPricesList = function() {
  return this.setPricesList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.api.trading.v1.HistoricalPriceRequest.repeatedFields_ = [1];

/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.api.trading.v1.HistoricalPriceRequest.oneofGroups_ = [[4]];

/**
 * @enum {number}
 */
proto.api.trading.v1.HistoricalPriceRequest.ParameterCase = {
  PARAMETER_NOT_SET: 0,
  TIME_END: 4
};

/**
 * @return {proto.api.trading.v1.HistoricalPriceRequest.ParameterCase}
 */
proto.api.trading.v1.HistoricalPriceRequest.prototype.getParameterCase = function() {
  return /** @type {proto.api.trading.v1.HistoricalPriceRequest.ParameterCase} */(jspb.Message.computeOneofCase(this, proto.api.trading.v1.HistoricalPriceRequest.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.api.trading.v1.HistoricalPriceRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.api.trading.v1.HistoricalPriceRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.api.trading.v1.HistoricalPriceRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.HistoricalPriceRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    assetPairsList: jspb.Message.toObjectList(msg.getAssetPairsList(),
    proto.api.trading.v1.AssetPairId.toObject, includeInstance),
    timeDenomination: jspb.Message.getFieldWithDefault(msg, 2, 0),
    timeDuration: jspb.Message.getFieldWithDefault(msg, 3, 0),
    timeEnd: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.api.trading.v1.HistoricalPriceRequest}
 */
proto.api.trading.v1.HistoricalPriceRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.api.trading.v1.HistoricalPriceRequest;
  return proto.api.trading.v1.HistoricalPriceRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.api.trading.v1.HistoricalPriceRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.api.trading.v1.HistoricalPriceRequest}
 */
proto.api.trading.v1.HistoricalPriceRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.api.trading.v1.AssetPairId;
      reader.readMessage(value,proto.api.trading.v1.AssetPairId.deserializeBinaryFromReader);
      msg.addAssetPairs(value);
      break;
    case 2:
      var value = /** @type {!proto.api.trading.v1.TimeDenomination} */ (reader.readEnum());
      msg.setTimeDenomination(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setTimeDuration(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setTimeEnd(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.api.trading.v1.HistoricalPriceRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.api.trading.v1.HistoricalPriceRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.api.trading.v1.HistoricalPriceRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.HistoricalPriceRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAssetPairsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.api.trading.v1.AssetPairId.serializeBinaryToWriter
    );
  }
  f = message.getTimeDenomination();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getTimeDuration();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeInt64(
      4,
      f
    );
  }
};


/**
 * repeated AssetPairId asset_pairs = 1;
 * @return {!Array<!proto.api.trading.v1.AssetPairId>}
 */
proto.api.trading.v1.HistoricalPriceRequest.prototype.getAssetPairsList = function() {
  return /** @type{!Array<!proto.api.trading.v1.AssetPairId>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.api.trading.v1.AssetPairId, 1));
};


/**
 * @param {!Array<!proto.api.trading.v1.AssetPairId>} value
 * @return {!proto.api.trading.v1.HistoricalPriceRequest} returns this
*/
proto.api.trading.v1.HistoricalPriceRequest.prototype.setAssetPairsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.api.trading.v1.AssetPairId=} opt_value
 * @param {number=} opt_index
 * @return {!proto.api.trading.v1.AssetPairId}
 */
proto.api.trading.v1.HistoricalPriceRequest.prototype.addAssetPairs = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.api.trading.v1.AssetPairId, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.api.trading.v1.HistoricalPriceRequest} returns this
 */
proto.api.trading.v1.HistoricalPriceRequest.prototype.clearAssetPairsList = function() {
  return this.setAssetPairsList([]);
};


/**
 * optional TimeDenomination time_denomination = 2;
 * @return {!proto.api.trading.v1.TimeDenomination}
 */
proto.api.trading.v1.HistoricalPriceRequest.prototype.getTimeDenomination = function() {
  return /** @type {!proto.api.trading.v1.TimeDenomination} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.api.trading.v1.TimeDenomination} value
 * @return {!proto.api.trading.v1.HistoricalPriceRequest} returns this
 */
proto.api.trading.v1.HistoricalPriceRequest.prototype.setTimeDenomination = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional uint32 time_duration = 3;
 * @return {number}
 */
proto.api.trading.v1.HistoricalPriceRequest.prototype.getTimeDuration = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.api.trading.v1.HistoricalPriceRequest} returns this
 */
proto.api.trading.v1.HistoricalPriceRequest.prototype.setTimeDuration = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int64 time_end = 4;
 * @return {number}
 */
proto.api.trading.v1.HistoricalPriceRequest.prototype.getTimeEnd = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.api.trading.v1.HistoricalPriceRequest} returns this
 */
proto.api.trading.v1.HistoricalPriceRequest.prototype.setTimeEnd = function(value) {
  return jspb.Message.setOneofField(this, 4, proto.api.trading.v1.HistoricalPriceRequest.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.api.trading.v1.HistoricalPriceRequest} returns this
 */
proto.api.trading.v1.HistoricalPriceRequest.prototype.clearTimeEnd = function() {
  return jspb.Message.setOneofField(this, 4, proto.api.trading.v1.HistoricalPriceRequest.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.api.trading.v1.HistoricalPriceRequest.prototype.hasTimeEnd = function() {
  return jspb.Message.getField(this, 4) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.api.trading.v1.HistoricalPriceResponse.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.api.trading.v1.HistoricalPriceResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.api.trading.v1.HistoricalPriceResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.api.trading.v1.HistoricalPriceResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.HistoricalPriceResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    historicalPricesList: jspb.Message.toObjectList(msg.getHistoricalPricesList(),
    proto.api.trading.v1.HistoricalPrice.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.api.trading.v1.HistoricalPriceResponse}
 */
proto.api.trading.v1.HistoricalPriceResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.api.trading.v1.HistoricalPriceResponse;
  return proto.api.trading.v1.HistoricalPriceResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.api.trading.v1.HistoricalPriceResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.api.trading.v1.HistoricalPriceResponse}
 */
proto.api.trading.v1.HistoricalPriceResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.api.trading.v1.HistoricalPrice;
      reader.readMessage(value,proto.api.trading.v1.HistoricalPrice.deserializeBinaryFromReader);
      msg.addHistoricalPrices(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.api.trading.v1.HistoricalPriceResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.api.trading.v1.HistoricalPriceResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.api.trading.v1.HistoricalPriceResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.HistoricalPriceResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getHistoricalPricesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.api.trading.v1.HistoricalPrice.serializeBinaryToWriter
    );
  }
};


/**
 * repeated HistoricalPrice historical_prices = 1;
 * @return {!Array<!proto.api.trading.v1.HistoricalPrice>}
 */
proto.api.trading.v1.HistoricalPriceResponse.prototype.getHistoricalPricesList = function() {
  return /** @type{!Array<!proto.api.trading.v1.HistoricalPrice>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.api.trading.v1.HistoricalPrice, 1));
};


/**
 * @param {!Array<!proto.api.trading.v1.HistoricalPrice>} value
 * @return {!proto.api.trading.v1.HistoricalPriceResponse} returns this
*/
proto.api.trading.v1.HistoricalPriceResponse.prototype.setHistoricalPricesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.api.trading.v1.HistoricalPrice=} opt_value
 * @param {number=} opt_index
 * @return {!proto.api.trading.v1.HistoricalPrice}
 */
proto.api.trading.v1.HistoricalPriceResponse.prototype.addHistoricalPrices = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.api.trading.v1.HistoricalPrice, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.api.trading.v1.HistoricalPriceResponse} returns this
 */
proto.api.trading.v1.HistoricalPriceResponse.prototype.clearHistoricalPricesList = function() {
  return this.setHistoricalPricesList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.api.trading.v1.PriceEvent.prototype.toObject = function(opt_includeInstance) {
  return proto.api.trading.v1.PriceEvent.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.api.trading.v1.PriceEvent} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.PriceEvent.toObject = function(includeInstance, msg) {
  var f, obj = {
    time: (f = msg.getTime()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
    assetPair: (f = msg.getAssetPair()) && proto.api.trading.v1.AssetPair.toObject(includeInstance, f),
    price: jspb.Message.getFieldWithDefault(msg, 3, ""),
    source: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.api.trading.v1.PriceEvent}
 */
proto.api.trading.v1.PriceEvent.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.api.trading.v1.PriceEvent;
  return proto.api.trading.v1.PriceEvent.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.api.trading.v1.PriceEvent} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.api.trading.v1.PriceEvent}
 */
proto.api.trading.v1.PriceEvent.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setTime(value);
      break;
    case 2:
      var value = new proto.api.trading.v1.AssetPair;
      reader.readMessage(value,proto.api.trading.v1.AssetPair.deserializeBinaryFromReader);
      msg.setAssetPair(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setPrice(value);
      break;
    case 4:
      var value = /** @type {!proto.api.trading.v1.PriceSource} */ (reader.readEnum());
      msg.setSource(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.api.trading.v1.PriceEvent.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.api.trading.v1.PriceEvent.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.api.trading.v1.PriceEvent} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.PriceEvent.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTime();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getAssetPair();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.api.trading.v1.AssetPair.serializeBinaryToWriter
    );
  }
  f = message.getPrice();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getSource();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
};


/**
 * optional google.protobuf.Timestamp time = 1;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.api.trading.v1.PriceEvent.prototype.getTime = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 1));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.api.trading.v1.PriceEvent} returns this
*/
proto.api.trading.v1.PriceEvent.prototype.setTime = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.api.trading.v1.PriceEvent} returns this
 */
proto.api.trading.v1.PriceEvent.prototype.clearTime = function() {
  return this.setTime(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.api.trading.v1.PriceEvent.prototype.hasTime = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional AssetPair asset_pair = 2;
 * @return {?proto.api.trading.v1.AssetPair}
 */
proto.api.trading.v1.PriceEvent.prototype.getAssetPair = function() {
  return /** @type{?proto.api.trading.v1.AssetPair} */ (
    jspb.Message.getWrapperField(this, proto.api.trading.v1.AssetPair, 2));
};


/**
 * @param {?proto.api.trading.v1.AssetPair|undefined} value
 * @return {!proto.api.trading.v1.PriceEvent} returns this
*/
proto.api.trading.v1.PriceEvent.prototype.setAssetPair = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.api.trading.v1.PriceEvent} returns this
 */
proto.api.trading.v1.PriceEvent.prototype.clearAssetPair = function() {
  return this.setAssetPair(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.api.trading.v1.PriceEvent.prototype.hasAssetPair = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string price = 3;
 * @return {string}
 */
proto.api.trading.v1.PriceEvent.prototype.getPrice = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.api.trading.v1.PriceEvent} returns this
 */
proto.api.trading.v1.PriceEvent.prototype.setPrice = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional PriceSource source = 4;
 * @return {!proto.api.trading.v1.PriceSource}
 */
proto.api.trading.v1.PriceEvent.prototype.getSource = function() {
  return /** @type {!proto.api.trading.v1.PriceSource} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {!proto.api.trading.v1.PriceSource} value
 * @return {!proto.api.trading.v1.PriceEvent} returns this
 */
proto.api.trading.v1.PriceEvent.prototype.setSource = function(value) {
  return jspb.Message.setProto3EnumField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.api.trading.v1.HistoricalPriceEvent.prototype.toObject = function(opt_includeInstance) {
  return proto.api.trading.v1.HistoricalPriceEvent.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.api.trading.v1.HistoricalPriceEvent} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.HistoricalPriceEvent.toObject = function(includeInstance, msg) {
  var f, obj = {
    time: (f = msg.getTime()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
    price: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.api.trading.v1.HistoricalPriceEvent}
 */
proto.api.trading.v1.HistoricalPriceEvent.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.api.trading.v1.HistoricalPriceEvent;
  return proto.api.trading.v1.HistoricalPriceEvent.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.api.trading.v1.HistoricalPriceEvent} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.api.trading.v1.HistoricalPriceEvent}
 */
proto.api.trading.v1.HistoricalPriceEvent.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setTime(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setPrice(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.api.trading.v1.HistoricalPriceEvent.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.api.trading.v1.HistoricalPriceEvent.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.api.trading.v1.HistoricalPriceEvent} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.HistoricalPriceEvent.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTime();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getPrice();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional google.protobuf.Timestamp time = 1;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.api.trading.v1.HistoricalPriceEvent.prototype.getTime = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 1));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.api.trading.v1.HistoricalPriceEvent} returns this
*/
proto.api.trading.v1.HistoricalPriceEvent.prototype.setTime = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.api.trading.v1.HistoricalPriceEvent} returns this
 */
proto.api.trading.v1.HistoricalPriceEvent.prototype.clearTime = function() {
  return this.setTime(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.api.trading.v1.HistoricalPriceEvent.prototype.hasTime = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string price = 3;
 * @return {string}
 */
proto.api.trading.v1.HistoricalPriceEvent.prototype.getPrice = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.api.trading.v1.HistoricalPriceEvent} returns this
 */
proto.api.trading.v1.HistoricalPriceEvent.prototype.setPrice = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.api.trading.v1.HistoricalPrice.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.api.trading.v1.HistoricalPrice.prototype.toObject = function(opt_includeInstance) {
  return proto.api.trading.v1.HistoricalPrice.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.api.trading.v1.HistoricalPrice} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.HistoricalPrice.toObject = function(includeInstance, msg) {
  var f, obj = {
    assetPair: (f = msg.getAssetPair()) && proto.api.trading.v1.AssetPair.toObject(includeInstance, f),
    quotesList: jspb.Message.toObjectList(msg.getQuotesList(),
    proto.api.trading.v1.HistoricalPriceEvent.toObject, includeInstance),
    source: jspb.Message.getFieldWithDefault(msg, 3, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.api.trading.v1.HistoricalPrice}
 */
proto.api.trading.v1.HistoricalPrice.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.api.trading.v1.HistoricalPrice;
  return proto.api.trading.v1.HistoricalPrice.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.api.trading.v1.HistoricalPrice} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.api.trading.v1.HistoricalPrice}
 */
proto.api.trading.v1.HistoricalPrice.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.api.trading.v1.AssetPair;
      reader.readMessage(value,proto.api.trading.v1.AssetPair.deserializeBinaryFromReader);
      msg.setAssetPair(value);
      break;
    case 2:
      var value = new proto.api.trading.v1.HistoricalPriceEvent;
      reader.readMessage(value,proto.api.trading.v1.HistoricalPriceEvent.deserializeBinaryFromReader);
      msg.addQuotes(value);
      break;
    case 3:
      var value = /** @type {!proto.api.trading.v1.PriceSource} */ (reader.readEnum());
      msg.setSource(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.api.trading.v1.HistoricalPrice.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.api.trading.v1.HistoricalPrice.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.api.trading.v1.HistoricalPrice} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.HistoricalPrice.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAssetPair();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.api.trading.v1.AssetPair.serializeBinaryToWriter
    );
  }
  f = message.getQuotesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.api.trading.v1.HistoricalPriceEvent.serializeBinaryToWriter
    );
  }
  f = message.getSource();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
};


/**
 * optional AssetPair asset_pair = 1;
 * @return {?proto.api.trading.v1.AssetPair}
 */
proto.api.trading.v1.HistoricalPrice.prototype.getAssetPair = function() {
  return /** @type{?proto.api.trading.v1.AssetPair} */ (
    jspb.Message.getWrapperField(this, proto.api.trading.v1.AssetPair, 1));
};


/**
 * @param {?proto.api.trading.v1.AssetPair|undefined} value
 * @return {!proto.api.trading.v1.HistoricalPrice} returns this
*/
proto.api.trading.v1.HistoricalPrice.prototype.setAssetPair = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.api.trading.v1.HistoricalPrice} returns this
 */
proto.api.trading.v1.HistoricalPrice.prototype.clearAssetPair = function() {
  return this.setAssetPair(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.api.trading.v1.HistoricalPrice.prototype.hasAssetPair = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * repeated HistoricalPriceEvent quotes = 2;
 * @return {!Array<!proto.api.trading.v1.HistoricalPriceEvent>}
 */
proto.api.trading.v1.HistoricalPrice.prototype.getQuotesList = function() {
  return /** @type{!Array<!proto.api.trading.v1.HistoricalPriceEvent>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.api.trading.v1.HistoricalPriceEvent, 2));
};


/**
 * @param {!Array<!proto.api.trading.v1.HistoricalPriceEvent>} value
 * @return {!proto.api.trading.v1.HistoricalPrice} returns this
*/
proto.api.trading.v1.HistoricalPrice.prototype.setQuotesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.api.trading.v1.HistoricalPriceEvent=} opt_value
 * @param {number=} opt_index
 * @return {!proto.api.trading.v1.HistoricalPriceEvent}
 */
proto.api.trading.v1.HistoricalPrice.prototype.addQuotes = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.api.trading.v1.HistoricalPriceEvent, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.api.trading.v1.HistoricalPrice} returns this
 */
proto.api.trading.v1.HistoricalPrice.prototype.clearQuotesList = function() {
  return this.setQuotesList([]);
};


/**
 * optional PriceSource source = 3;
 * @return {!proto.api.trading.v1.PriceSource}
 */
proto.api.trading.v1.HistoricalPrice.prototype.getSource = function() {
  return /** @type {!proto.api.trading.v1.PriceSource} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.api.trading.v1.PriceSource} value
 * @return {!proto.api.trading.v1.HistoricalPrice} returns this
 */
proto.api.trading.v1.HistoricalPrice.prototype.setSource = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.api.trading.v1.AssetPair.prototype.toObject = function(opt_includeInstance) {
  return proto.api.trading.v1.AssetPair.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.api.trading.v1.AssetPair} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.AssetPair.toObject = function(includeInstance, msg) {
  var f, obj = {
    baseCurrency: (f = msg.getBaseCurrency()) && proto.api.trading.v1.Currency.toObject(includeInstance, f),
    quoteCurrency: (f = msg.getQuoteCurrency()) && proto.api.trading.v1.Currency.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.api.trading.v1.AssetPair}
 */
proto.api.trading.v1.AssetPair.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.api.trading.v1.AssetPair;
  return proto.api.trading.v1.AssetPair.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.api.trading.v1.AssetPair} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.api.trading.v1.AssetPair}
 */
proto.api.trading.v1.AssetPair.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.api.trading.v1.Currency;
      reader.readMessage(value,proto.api.trading.v1.Currency.deserializeBinaryFromReader);
      msg.setBaseCurrency(value);
      break;
    case 2:
      var value = new proto.api.trading.v1.Currency;
      reader.readMessage(value,proto.api.trading.v1.Currency.deserializeBinaryFromReader);
      msg.setQuoteCurrency(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.api.trading.v1.AssetPair.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.api.trading.v1.AssetPair.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.api.trading.v1.AssetPair} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.AssetPair.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getBaseCurrency();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.api.trading.v1.Currency.serializeBinaryToWriter
    );
  }
  f = message.getQuoteCurrency();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.api.trading.v1.Currency.serializeBinaryToWriter
    );
  }
};


/**
 * optional Currency base_currency = 1;
 * @return {?proto.api.trading.v1.Currency}
 */
proto.api.trading.v1.AssetPair.prototype.getBaseCurrency = function() {
  return /** @type{?proto.api.trading.v1.Currency} */ (
    jspb.Message.getWrapperField(this, proto.api.trading.v1.Currency, 1));
};


/**
 * @param {?proto.api.trading.v1.Currency|undefined} value
 * @return {!proto.api.trading.v1.AssetPair} returns this
*/
proto.api.trading.v1.AssetPair.prototype.setBaseCurrency = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.api.trading.v1.AssetPair} returns this
 */
proto.api.trading.v1.AssetPair.prototype.clearBaseCurrency = function() {
  return this.setBaseCurrency(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.api.trading.v1.AssetPair.prototype.hasBaseCurrency = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional Currency quote_currency = 2;
 * @return {?proto.api.trading.v1.Currency}
 */
proto.api.trading.v1.AssetPair.prototype.getQuoteCurrency = function() {
  return /** @type{?proto.api.trading.v1.Currency} */ (
    jspb.Message.getWrapperField(this, proto.api.trading.v1.Currency, 2));
};


/**
 * @param {?proto.api.trading.v1.Currency|undefined} value
 * @return {!proto.api.trading.v1.AssetPair} returns this
*/
proto.api.trading.v1.AssetPair.prototype.setQuoteCurrency = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.api.trading.v1.AssetPair} returns this
 */
proto.api.trading.v1.AssetPair.prototype.clearQuoteCurrency = function() {
  return this.setQuoteCurrency(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.api.trading.v1.AssetPair.prototype.hasQuoteCurrency = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.api.trading.v1.AssetPairId.prototype.toObject = function(opt_includeInstance) {
  return proto.api.trading.v1.AssetPairId.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.api.trading.v1.AssetPairId} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.AssetPairId.toObject = function(includeInstance, msg) {
  var f, obj = {
    baseCurrencyId: jspb.Message.getFieldWithDefault(msg, 1, 0),
    quoteCurrencyId: jspb.Message.getFieldWithDefault(msg, 2, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.api.trading.v1.AssetPairId}
 */
proto.api.trading.v1.AssetPairId.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.api.trading.v1.AssetPairId;
  return proto.api.trading.v1.AssetPairId.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.api.trading.v1.AssetPairId} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.api.trading.v1.AssetPairId}
 */
proto.api.trading.v1.AssetPairId.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setBaseCurrencyId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setQuoteCurrencyId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.api.trading.v1.AssetPairId.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.api.trading.v1.AssetPairId.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.api.trading.v1.AssetPairId} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.AssetPairId.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getBaseCurrencyId();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getQuoteCurrencyId();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
};


/**
 * optional uint32 base_currency_id = 1;
 * @return {number}
 */
proto.api.trading.v1.AssetPairId.prototype.getBaseCurrencyId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.api.trading.v1.AssetPairId} returns this
 */
proto.api.trading.v1.AssetPairId.prototype.setBaseCurrencyId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 quote_currency_id = 2;
 * @return {number}
 */
proto.api.trading.v1.AssetPairId.prototype.getQuoteCurrencyId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.api.trading.v1.AssetPairId} returns this
 */
proto.api.trading.v1.AssetPairId.prototype.setQuoteCurrencyId = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.api.trading.v1.Currency.prototype.toObject = function(opt_includeInstance) {
  return proto.api.trading.v1.Currency.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.api.trading.v1.Currency} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.Currency.toObject = function(includeInstance, msg) {
  var f, obj = {
    assetId: jspb.Message.getFieldWithDefault(msg, 1, 0),
    displayName: jspb.Message.getFieldWithDefault(msg, 2, ""),
    description: jspb.Message.getFieldWithDefault(msg, 3, ""),
    ticker: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.api.trading.v1.Currency}
 */
proto.api.trading.v1.Currency.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.api.trading.v1.Currency;
  return proto.api.trading.v1.Currency.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.api.trading.v1.Currency} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.api.trading.v1.Currency}
 */
proto.api.trading.v1.Currency.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAssetId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setDisplayName(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setDescription(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setTicker(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.api.trading.v1.Currency.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.api.trading.v1.Currency.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.api.trading.v1.Currency} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.api.trading.v1.Currency.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAssetId();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getDisplayName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getDescription();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getTicker();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * optional uint32 asset_id = 1;
 * @return {number}
 */
proto.api.trading.v1.Currency.prototype.getAssetId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.api.trading.v1.Currency} returns this
 */
proto.api.trading.v1.Currency.prototype.setAssetId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string display_name = 2;
 * @return {string}
 */
proto.api.trading.v1.Currency.prototype.getDisplayName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.api.trading.v1.Currency} returns this
 */
proto.api.trading.v1.Currency.prototype.setDisplayName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string description = 3;
 * @return {string}
 */
proto.api.trading.v1.Currency.prototype.getDescription = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.api.trading.v1.Currency} returns this
 */
proto.api.trading.v1.Currency.prototype.setDescription = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string ticker = 4;
 * @return {string}
 */
proto.api.trading.v1.Currency.prototype.getTicker = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.api.trading.v1.Currency} returns this
 */
proto.api.trading.v1.Currency.prototype.setTicker = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * @enum {number}
 */
proto.api.trading.v1.TimeDenomination = {
  SEC: 0,
  MIN: 1,
  HRS: 2,
  DAY: 3
};

/**
 * @enum {number}
 */
proto.api.trading.v1.PriceSource = {
  VWAP: 0,
  COIN_API: 1,
  STATIC: 2
};

goog.object.extend(exports, proto.api.trading.v1);
