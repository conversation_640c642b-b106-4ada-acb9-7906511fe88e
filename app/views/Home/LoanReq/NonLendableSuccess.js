import React, {useState, useEffect, useRef} from 'react'
import {View, TextInput, TouchableOpacity, ScrollView, Image, Platform, Linking} from 'react-native'
import {TextReg, TextBold, BackgroundHeader, LocationSelect, Card, Button} from '../../../components'

let NonLendableSuccess = ({closeToHome, isCel = false}) => {
  let goSupport = () => {
    Linking.openURL('mailto:<EMAIL>')
  }
  return (
    <>
      {isCel && (
        <View style={{flex: 1, alignItems: 'center', justifyContent: 'space-between'}}>
          <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
            <Image source={require('../../../imgs/graphics/nonLendCheck.png')} style={{height: 60, width: 60, marginTop: 30}} />
            <TextBold
              style={{fontSize: 26, textAlign: 'center', width: 300, marginTop: 20}}>{`Thank you, <PERSON><PERSON>cius Refinance Applicant.`}</TextBold>
            <TextReg
              style={{
                textAlign: 'center',
                width: 330,
                marginTop: 20,
                fontSize: 17,
              }}>{`We have received your loan request. A SALT Lending Loan Agent will be reaching out to you shortly to learn more details about your specific request.`}</TextReg>
          </View>
          <View style={{flexDirection: 'column', alignSelf: 'stretch'}}>
            <TextReg
              style={{
                textAlign: 'center',
                width: 330,
                marginBottom: 6,
              }}>{`Have a question in the meantime?`}</TextReg>
            <TextReg
              style={{
                textAlign: 'center',
                width: 330,
                marginBottom: 6,
              }}>{`Get in touch with us`}</TextReg>
            <Button style={{alignSelf: 'stretch', marginTop: 10, marginBottom: 0}} onPress={() => goSupport()}>
              <TextBold style={{fontSize: 18, letterSpacing: 0.8, color: '#000', alignSelf: 'stretch'}}>{`CONTACT SUPPORT`}</TextBold>
            </Button>
            <Button
              theme={'secondary'}
              style={{
                alignSelf: 'stretch',
                marginTop: 10,
                marginBottom: 30,
                backgroundColor: '#28283D',
                borderColor: '#00FFBD',
                borderWidth: 2,
              }}
              onPress={() => closeToHome()}>
              <TextBold style={{fontSize: 18, letterSpacing: 0.8, color: '#00FFBD', alignSelf: 'stretch'}}>{`HOME`}</TextBold>
            </Button>
          </View>
        </View>
      )}
      {!isCel && (
        <View style={{flex: 1, alignItems: 'center', justifyContent: 'space-between'}}>
          <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
            <Image source={require('../../../imgs/graphics/nonLendCheck.png')} style={{height: 60, width: 60, marginTop: 30}} />
            <TextBold style={{fontSize: 26, textAlign: 'center', width: 200, marginTop: 20}}>{`Your request was submitted!`}</TextBold>
            <TextReg
              style={{
                textAlign: 'center',
                width: 330,
                marginTop: 20,
                fontSize: 17,
              }}>{`We’ll get back to you as soon as we have available services in your region.`}</TextReg>
          </View>
          <Button style={{alignSelf: 'stretch', marginTop: 20, marginBottom: 40}} onPress={() => closeToHome()}>
            <TextBold style={{fontSize: 18, letterSpacing: 0.8, color: '#000', alignSelf: 'stretch'}}>{`HOME`}</TextBold>
          </Button>
        </View>
      )}
    </>
  )
}

export default NonLendableSuccess
