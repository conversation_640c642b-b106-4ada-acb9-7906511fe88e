import React, {useState, useEffect} from 'react'
import {View, TextInput, TouchableOpacity, Image} from 'react-native'
import {TextReg, TextBold, Button} from '../../../components'

let Discounts = ({rateDiscount, feeDiscount}) => {
  let showBoth = rateDiscount != '' && feeDiscount != ''
  let areSame = rateDiscount != '' && feeDiscount != '' && rateDiscount == feeDiscount

  let msg = ''
  if (showBoth) {
    if (areSame) {
      msg = `Promo code successfully applied for a ${(rateDiscount * 100).toFixed(2)}% Discount`
    } else {
      msg = `Promo code successfully applied for a ${(rateDiscount * 100).toFixed(
        2,
      )}% Interest Rate Discount and ${(feeDiscount * 100).toFixed(2)}% Origination Fee Discount`
    }
  }
  return (
    <View>
      {showBoth && (
        <View
          style={{
            alignItems: 'center',
            width: 300,
            flexDirection: 'row',
          }}>
          <Image
            source={require('../../../imgs/checkmark.png')}
            style={{
              marginTop: -6,
              height: 24,
              width: 24,
              marginRight: 10,
              marginLeft: 4,
            }}
          />
          <TextReg
            style={{
              marginTop: -10,
              width: 220,
            }}>
            {msg}
          </TextReg>
        </View>
      )}
      {!showBoth && (
        <>
          {rateDiscount != '' && (
            <View
              style={{
                alignItems: 'center',
                width: 300,
                flexDirection: 'row',
              }}>
              <Image
                source={require('../../../imgs/checkmark.png')}
                style={{
                  marginTop: -10,
                  height: 24,
                  width: 24,
                  marginRight: 10,
                  marginLeft: 4,
                }}
              />
              <TextReg
                style={{
                  marginTop: -10,
                  width: 220,
                }}>{`Promo code successfully applied for a ${(rateDiscount * 100).toFixed(
                2,
              )}% Interest Rate Discount`}</TextReg>
            </View>
          )}
          {feeDiscount != '' && (
            <View
              style={{
                marginTop: rateDiscount != '' ? 14 : 0,
                alignItems: 'center',
                width: 300,
                flexDirection: 'row',
              }}>
              <Image
                source={require('../../../imgs/checkmark.png')}
                style={{
                  marginTop: -10,
                  height: 24,
                  width: 24,
                  marginRight: 10,
                  marginLeft: 4,
                }}
              />
              <TextReg
                style={{
                  marginTop: -10,
                  width: 220,
                }}>{`Promo code successfully applied for a ${(feeDiscount * 100).toFixed(
                2,
              )}% Origination Fee Discount`}</TextReg>
            </View>
          )}
        </>
      )}
    </View>
  )
}

export default Discounts
