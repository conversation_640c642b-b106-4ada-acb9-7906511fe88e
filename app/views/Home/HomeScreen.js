import React, {Component} from 'react'
import {
  View,
  StatusBar,
  ScrollView,
  RefreshControl,
  Platform,
  ActivityIndicator,
} from 'react-native'
import {connect} from 'react-redux'

import AsyncStorage from '@react-native-async-storage/async-storage'
import Heap from '@heap/react-native-heap'

import {BackgroundHeader} from '../../components'
import {derivedStatusMap, tokenList} from '../../util/enumerables'
import {sendEvent} from '../../store/analytics/analytics.actions'

import {updateActiveTabListener, nextPaymentFormatDate} from '../../util/helpers'
import {Listener} from '../../components'

import config from '../../config.json'
import styles from './styles'

import commonStyles from '../../styles/commonStyles'

import {
  updateUser,
  updateAccount,
  updateAccountPlus,
  getMe,
  showDepositModal,
  dataLoaded,
  increaseRefreshDataCount,
  pauseUnit21,
  updateReferrals,
  updateAllWallets,
  toggleNewAgreement,
  updatePrices24h,
} from '../../store/user/user.actions'
import {isAuthed} from '../../store/auth/auth.actions'
import {showNotifications} from '../../store/notifications/notifications.actions'
import {setFlags} from '../../store/launchDarkly/launchDarkly.actions'
import PinIntro from '../Loans/PinIntro'
import TwoFactorSetup from '../Loans/TwoFactorSetup'
import ShellAccountBoard from './Shell/ShellAccountBoard'
import GrpcTest from './grpc/GrpcTest'
import CsvDown from './CsvDown'
import {setInvestmentRates} from '../../store/investments/investments.actions'
import moment from 'moment'

class HomeScreen extends Component {
  constructor(props) {
    super(props)
    this.state = {
      twoFactorSkip: true,
      latestTransactions: [],
      refreshing: true,
      firstTimeLoading: true,
      pendingLoan: false,
      noLoans: false,
      activeLoan: false,
      loadingCancel: false,
      allAccounts: null,
      firstName: null,
      customRefresh: false,
    }
    this.polling = null
  }

  componentDidMount() {
    this.props.dispatch(sendEvent(`Home Tab`))
    updateActiveTabListener(this.props.navigation)

    this.props.navigation.setParams({
      toggleNotifications: this.toggleNotifications,
    })

    if (!this.props.showPinScreen) {
      this.fetchApiData()
      //this.checkPushPrefs()
    }
    this.noOnboarding()
  }

  componentDidUpdate(prevProps) {
    if (
      (!this.props.showPinScreen && prevProps.showPinScreen) ||
      this.props.refreshCount !== prevProps.refreshCount
    ) {
      this.setState({refreshing: true}, () => {
        //this.fetchApiData('wallets')
        this.fetchApiData()
      })
    }

    if (this.props.goTwoFactor != prevProps.goTwoFactor) {
      this.goToTwoFactor()
    }

    //double check this - for new ID verification
    if (
      this.props.user &&
      prevProps.user &&
      prevProps.user.idVerificationStatus === 'pending' &&
      this.props.user.idVerificationStatus !== 'pending'
    ) {
      clearInterval(this.polling)
    }

    if (
      this.props.user &&
      !this.state.pendingID &&
      this.props.user.idVerificationStatus === 'pending'
    ) {
      this.setState({pendingID: true}, () => {
        this.polling = setInterval(this.dispatchGetMe, 20000)
      })
    }

    //instead we gonna move unit21 to inside of loans i think
    if (prevProps.loanData.id !== this.props.loanData.id) {
      if (this.props.loanData.status === 'active') {
        this.setState({pendingLoan: false, noLoans: false})
      } else if (derivedStatusMap.get(this.props.loanData.status) === 'pending') {
        this.setState({pendingLoan: true, noLoans: false})
      } else {
        this.setState({noLoans: true, pendingLoan: false})
      }
    }
  }

  /*
  checkPushPrefs = async () => {
    const firstTimePref = await AsyncStorage.getItem(`FIRST_TIME_PREFS-${this.props.accountRef}`)
    if (firstTimePref === null) {
      this.props.WebService.getNotificationPreferences()
        .then(res => {
          const prefs = res.data.preferences
          for (const key in prefs) {
            prefs[key].push.value = true
          }
          const updateData = {
            preferenceId: res.data.id,
            preferences: prefs,
          }
          this.props.WebService.updateNotificationPreferences(updateData)
        })
        .catch(err => {
          console.log('firstTimePref', err)
        })

      AsyncStorage.setItem(`FIRST_TIME_PREFS-${this.props.accountRef}`, 'set')
    }
  }
  */

  checkPauseUnit21 = loanStatus => {
    AsyncStorage.getItem('PAUSE_UNIT_21').then(res => {
      if (res) {
        console.log('PAUSE_UNIT_21', res)
        //this.props.dispatch(pauseUnit21(true))
      } else {
        console.log('PAUSE_UNIT_21 else', loanStatus)
        if (loanStatus == 'pending') {
          this.props.dispatch(pauseUnit21(false))
        }
      }
    })
  }

  /*
  getStackwiseTotal = () => {
    //console.log('get stackwise');
    const loanId = this.props.loanData?.id || '0';
    //console.log('loanId', loanId);
    this.props.WebService.getStackwise(loanId)
      .then(res => {
        console.log('getStackwise res', res);
      })
      .catch(err => {
        console.log('getStackwise err', err);
      });
  };
  */

  noOnboarding = () => {
    AsyncStorage.setItem('SKIP_INTRO', 'true')
  }

  dispatchGetMe = () => {
    this.props.dispatch(getMe())
  }

  toggleNotifications = () => {
    this.props.dispatch(showNotifications(true))
  }

  checkExchange = (data = []) => {
    let exchangeAcc = data?.filter(a => a.account?.productType == 'exchange')
    if (exchangeAcc.length < 1) {
      this.props.WebService.createSaltAccount({
        name: 'exchange',
        type: 'exchange',
      })
        .then(res => {
          console.log('exchange acc res', res)
        })
        .catch(err => {
          console.log('exchange acc err', err)
        })
    }
  }

  fetchApiData = async (type = 'all') => {
    this.setState({refreshing: true})
    this.props.dispatch(dataLoaded(false))

    console.log('a1')

    try {
      const [gotUser, investRates, allAccountData, account, lendAccs] = await Promise.all([
        this.getUser(),
        this.props.WebService.getInvestmentRates(),
        this.props.WebService.getAllAccounts(),
        this.props.WebService.getSaltAccount(),
        this.props.WebService.getAccountsLend(),
      ])
      this.props.dispatch(setInvestmentRates(investRates.data))

      console.log('a2')

      if (!allAccountData) {
        allAccountData = {}
      }

      //merge pendingRefinance data into all Accounts
      account.data = await Promise.all(
        account?.data?.map(async (a, index) => {
          if (a?.product?.loan?.pendingLoanId) {
            let refinanceLoan = await this.props.WebService.getPendingRefinanceLoanRef(index + 1)
            const refinancedLoans = (await AsyncStorage.getItem('refinancedLoans'))
              ? JSON.parse(await AsyncStorage.getItem('refinancedLoans'))
              : []

            if (refinanceLoan?.data) {
              const isNew = moment(new Date(refinanceLoan.data.startDate)).isSame(new Date(), 'day')
              if (refinancedLoans.includes(refinanceLoan.data.id) && isNew) {
                return {...a, refinanceLoan: {...refinanceLoan.data, showIndicator: false}}
              } else {
                const filteredLoans = refinancedLoans
                  ? refinancedLoans?.filter(loan => loan !== refinanceLoan.data.id)
                  : []

                await AsyncStorage.setItem('refinancedLoans', JSON.stringify(filteredLoans))
                return {...a, refinanceLoan: refinanceLoan.data}
              }
            } else {
              return a
            }
          } else return a
        }),
      )

      //merge lend account into all Accounts
      allAccountData.data = allAccountData?.data?.map(a => {
        let lendAcc = lendAccs?.data?.filter(b => b.account?.id == a.account?.id)[0]
        return {...a, investments: lendAcc?.investments}
      })

      console.log('a3')

      this.props.dispatch(updateAccount(account?.data))
      const updatedAccountData = await this.props.dispatch(updateAccountPlus(allAccountData?.data))
      console.log('updatedAccountData', updatedAccountData)

      console.log('a4')

      await this.getWallets(updatedAccountData?.user?.accounts)
      this.setState({allAccounts: allAccountData?.data})

      //check if exchange not there
      this.checkExchange(allAccountData?.data)

      await this.getTxAll(updatedAccountData?.user?.accounts)

      console.log('a5')

      this.props.dispatch(dataLoaded(true))
      this.setState({refreshing: false})
    } catch (err) {
      console.log('err2', err)
    }
  }

  getTxAll = allAccounts => {
    return new Promise(async (resolve, reject) => {
      if (!allAccounts) {
        return resolve()
      }
      try {
        let txAll = []

        let promises = allAccounts?.map(async a => {
          let accRef = a?.ref
          let txAllRes = await this.props.WebService.getTxAll(accRef)

          txAllRes?.data?.map(b => {
            b?.transactions?.map(c => {
              txAll.push({...c, productType: a?.productType})
            })
          })
        })

        await Promise.all(promises)

        console.log('txAll', txAll)

        //AsyncStorage.setItem('txAll', JSON.stringify(txAll))
        //for prices, when getting new prices, if there is new currency, get all again , save date and all currencies gotten

        this.setState({txAll}, () => resolve())
      } catch (err) {
        console.log('home- getTxAll err', err)
        reject(err)
      }
    })
  }

  dispatchUpdateAccount = data =>
    new Promise((resolve, reject) => {
      updateAccount(data)
      resolve()
    })

  getWallets = async (accountArr = []) => {
    let walletsRes = []

    await Promise.all(
      accountArr.map(async a => {
        console.log('a', a.ref)
        await this.props.WebService.getWallets(a.ref)
          .then(res => {
            //walletsRes.push(res.data);
            walletsRes[a.ref - 1] = res.data
            return true
          })
          .catch(err => {
            //console.log('getWallets err', err)
          })
      }),
    )

    console.log('walletsRes', walletsRes)

    this.props.dispatch(updateAllWallets(walletsRes))
    return true
  }

  getPrices = async (yesterdayOnly = false) => {
    let usdtObj = {
      asset: 'USDT',
      atomic: false,
      isStale: false,
      metric: 'USD',
      price: '1',
      time: '2024-02-13T10:01:11.007634Z',
    }

    if (yesterdayOnly) {
      let gotYesterday = this.props.WebService.getPrices24h()
      let priceYesterday = await gotYesterday
      priceYesterday.data['USDT-USD'] = usdtObj
      this.props.dispatch(updatePrices24h(priceYesterday.data))
    } else {
      try {
        let gotPrice = this.props.WebService.getPrices()
        let gotYesterday = this.props.WebService.getPrices24h()
        let get30Days = this.props.WebService.getPrices30d()
        let priceToday = await gotPrice

        //usdt?
        let priceYesterday = await gotYesterday
        let price30Days = await get30Days

        let numFields = Object.keys(price30Days?.data || {})
        /*
      numFields.map(a => {
        console.log('a', a)
      })
      */

        console.log('price30Days', numFields.length, price30Days)

        priceToday.data['USDT-USD'] = usdtObj
        priceYesterday.data['USDT-USD'] = usdtObj
        price30Days.data['USDT-USD'] = usdtObj

        console.log('priceYesterday', priceYesterday)

        //console.log('today btc - old pricing - ', priceToday.data['BTC-USD'].price)
        //this.props.dispatch(updatePrices(priceToday.data))
        //  console.log('yes btc - old pricing - ', priceYesterday.data['BTC-USD'].price)
        this.props.dispatch(updatePrices24h(priceYesterday.data))

        return true
      } catch (err) {
        //
        console.log('err', err)
      }
    }
  }

  getUser = async () => {
    return this.props.WebService.getSaltUser()
      .then(async res => {
        this.setState({
          firstName: res.data.firstName,
          idVerificationStatus: res.data.idVerificationStatus,
          personalProfileComplete: res.data.personalProfileComplete,
        })
        let accountsWithRefs = this.props.dispatch(updateUser(res.data))
        Heap.identify(res.data?.id)
        Heap.addUserProperties({email: res.data?.primaryEmail})
        this.updateLDFlags(res.data)
        await this.checkAgreementSigned(res.data)
        this.checkReferralSigned(res.data)
        this.checkMFA(res.data.id)
        this.checkTwoFactorSetup()
        return accountsWithRefs
      })
      .catch(err => {
        console.log('err get User', err)
        this.setState({connectedModalVisable: true})
        this.props.dispatch(dataLoaded(true))
        if (err.response && err.response.status === 401) {
          throw err
        }
      })
  }

  updateLDFlags = async userData => {
    let ldId = userData?.id
    let ldEmail = userData?.emails[0]?.address
    let ldClient = this.props.launchDarkly.ldClient

    //ldClient.flush();
    await ldClient.identify({key: ldId, email: ldEmail, kind: 'user'})
    const allFlagsResult = ldClient.allFlags()

    allFlagsResult
      .then(values => {
        this.props.dispatch(setFlags(values))
        if (values['grpc-active'] === false) {
          this.getPrices()
        } else {
          //get prices for graphs
          this.getPrices(false)
        }
      })
      .catch(err => {
        console.log('ld err', err)
      })
  }

  checkMFA = id => {
    this.props.WebService.checkMFA({id})
      .then(res => {})
      .catch(err => {
        if (err.status === 403) {
          this.props.navigation.navigate('TwoFactor', {
            reset: true,
          })
        }
      })
  }

  checkTwoFactorSetup = async () => {
    const twoFactorSkip = await AsyncStorage.getItem(
      `TWO_FACTOR_SKIP-${this.props.user.primaryEmail}`,
    )
    if (twoFactorSkip || this.props.user.mfaEnabled) {
      this.setState({twoFactorSkip: true})
    } else {
      this.setState({twoFactorSkip: false})
    }
  }

  goToTwoFactor = () => {
    this.props.navigation.navigate('TwoFactor')
    AsyncStorage.setItem(`TWO_FACTOR_SKIP-${this.props.user.primaryEmail}`, 'true')
    this.setState({twoFactorSkip: true})
  }

  skipTwoFactor = () => {
    AsyncStorage.setItem(`TWO_FACTOR_SKIP-${this.props.user.primaryEmail}`, 'true')
    this.setState({twoFactorSkip: true})
  }

  getLatestTransactions = async () => {
    const response = await this.props.WebService.getLatestTransactions()
    this.setState({latestTransactions: response.data})
  }

  checkReferralSigned = data => {
    if (data.referralCode) {
      const {countryCode, province} = data?.address
      this.props.WebService.getUnsignedAgreement(
        'referral-terms',
        countryCode,
        province,
        config.pactSafe.accessId,
      ).then(res => {
        if (res.data.title === 'Referral Terms and Conditions') {
          this.props.dispatch(updateReferrals(false, res.data))
        } else {
          this.props.dispatch(updateReferrals(true))
        }
      })
    }
  }

  checkAgreementSigned = data => {
    let {countryCode, province} = data?.address
    console.log('checkAgreementSigned', countryCode, province, config.pactSafe.accessId)
    return this.props.WebService.getUnsignedAgreement(
      'membership-agreement',
      countryCode,
      province,
      config.pactSafe.accessId,
    )
      .then(res => {
        //this.setState({contract: res.data, loaded: true});
        console.log('getUnsignedAgreement res', res)
        this.props.dispatch(toggleNewAgreement(res.data))
        return true
      })
      .catch(err => {
        console.log('getUnsignedAgreement err', err)
        return true
      })
  }
  /*
  didOpenFromPushNotif = () => {
    firebase
      .notifications()
      .getInitialNotification()
      .then(notificationOpen => {
        if (notificationOpen) {
          //const action = notificationOpen.action
          const notification = notificationOpen.notification
          this.parseMessage(notification)
        }
      })
  }
  */

  parseMessage = notification => {
    const tokenArr = tokenList // will abstract when post launch coin cleanup into store

    let notifToken = null
    tokenArr.map(a => {
      if (notification._data.body.includes(a)) {
        notifToken = a
      }
    })
    if (notifToken) {
      this.props.navigation.navigate('Detail', {title: notifToken})
    }
  }

  depositCollateral = () => {
    this.props.navigation.navigate('Loans')
    this.props.dispatch(showDepositModal(true))
  }

  openLoanRequest = () => {
    this.props.navigation.navigate('LoanRequest')
  }

  goToConvert = () => {
    this.props.navigation.navigate('Convert')
  }

  cancelNonLendable = id => {
    this.setState({loadingCancel: true})
    this.props.WebService.cancelNonLendable(id)
      .then(res => {
        this.setState({loadingCancel: false})
        this.fetchApiData()
      })
      .catch(err => {
        this.setState({loadingCancel: false})
        console.log('cancelNonLendable err', err)
      })
  }

  pickAccount = async accountRef => {
    const refString = accountRef.toString()
    this.props.WebService.updateRef(refString)
    this.props.dispatch(isAuthed({email: this.props.primaryEmail, ref: refString}))
    await this.dispatchUpdateRef(refString)
    this.setState({allAccounts: null})
    const lowerCaseEmail = this.props.primaryEmail.toLowerCase()
    AsyncStorage.setItem(`REF-${lowerCaseEmail}`, refString)
    this.props.dispatch(increaseRefreshDataCount())
  }

  dispatchUpdateRef = accountRef =>
    new Promise((resolve, reject) => {
      this.props.dispatch(isAuthed({email: this.props.primaryEmail, ref: accountRef}))
      resolve()
    })

  render() {
    const nextPaymentAmount = this.props.loanData?.amortizationInfo?.nextPaymentAmount || null

    let nextPaymentDate = this.props.loanData?.amortizationInfo?.nextPaymentDate || null
    nextPaymentDate = nextPaymentFormatDate(nextPaymentDate)

    const personalProfileComplete = this.props.user.personalProfileComplete

    const {latestTransactions} = this.state
    const {loanData} = this.props

    /*
	  const hasLoanBankingInfo =
			dig(loanData, 'depositBankAccount', 'documents', 'length') > 0 ||
			dig(loanData, 'depositBankAccount', 'verifiedAt') ||
			(dig(loanData, 'depositBankAccount', 'upholdId') && !dig(loanData, 'depositBankAccount', 'document', 'rejectedAt'));
      */

    const totalValue = this.props.loanData.amount / this.props.loanData.baseLTV
    const hasCollateralForLoan = this.props.loanData.collateralTotalWithSalt >= totalValue

    const hasIdentityVerification = this.props.user.idVerificationStatus == 'approved'

    const account = this.props?.user?.accounts?.filter(a => a.ref == this.props.accountRef)[0]

    const nonLendableLoanReqId = account?.product?.loan?.nonLendableLoanReqId

    const businessProfileComplete = account?.businessProfile?.isComplete

    const type = loanData?.jurisdiction?.type || 'personal'

    const {thresholds, automatedPaymentType} = this.props.loanData

    const showPinIntro = this.props.storedPin === '0' || this.props.backToSettings

    const showUnit21 = this.state.pendingLoan && !this.props.pauseUnit21
    const contUnit21 = this.state.pendingLoan && this.props.pauseUnit21

    let os = 'android'
    let iosLoad = false
    if (Platform.OS === 'ios') {
      os = 'ios'
      iosLoad = true
    }

    return (
      <View key={'homeScreen'} style={commonStyles.tileContainer}>
        <GrpcTest getPrices={this.getPrices} launchDarkly={this.props.launchDarkly} />
        <CsvDown />
        <Listener />
        {!showPinIntro && (
          <BackgroundHeader
            notification
            title={'Dashboard'}
            toggleNotifications={this.toggleNotifications}
            leftIcon={'homeScreen'}
            navigateSettings={() => this.props.navigation.navigate('Settings')}
          />
        )}
        <StatusBar barStyle="light-content" />
        {showPinIntro && (
          <PinIntro backToSettings={this.props.backToSettings} navigation={this.props.navigation} />
        )}
        {!this.state.twoFactorSkip && (
          <TwoFactorSetup
            showPinScreen={this.props.showPinScreen}
            goToTwoFactor={this.goToTwoFactor}
            skipTwoFactor={this.skipTwoFactor}
          />
        )}
        {!showPinIntro && (
          <ScrollView
            style={styles.swiperContainer}
            contentContainerStyle={{alignItems: 'center', paddingTop: 0}}
            refreshControl={
              <RefreshControl
                refreshing={this.state.refreshing}
                onRefresh={this.fetchApiData}
                colors={['#28283D']}
                tintColor={'#fff'}
                style={{opacity: 0}}
              />
            }>
            {this.state.refreshing && iosLoad && <ActivityIndicator size="large" color="#fff" />}
            {!this.state.refreshing && (
              <ShellAccountBoard
                navigation={this.props.navigation}
                allAccounts={this.state.allAccounts}
                accountCreated={this.props.accountCreated}
                accountRef={this.props.accountRef}
                txAll={this.state.txAll}
              />
            )}
          </ScrollView>
        )}
      </View>
    )
  }
}

HomeScreen.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
  primaryEmail: state.auth.account.email,
  refreshCount: state.user.refreshCount,
  accountCreated: state.user.accountCreated,
  pauseUnit21: state.user.pauseUnit21,
  showPinScreen: state.auth.pinScreen,
  backToSettings: state.user.backToSettings,
  storedPin: state.auth.pin,
  user: state.user.user,
  launchDarkly: state.launchDarkly,
  referrals: state.user.referrals,
  goTwoFactor: state.user.goTwoFactor,
})

export default connect(mapStateToProps)(HomeScreen)
