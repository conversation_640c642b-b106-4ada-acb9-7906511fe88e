import React, {useState, useEffect, useRef} from 'react'
import {View, TextInput, TouchableOpacity, ScrollView, Image, Platform, Linking} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import {TextReg, TextBold, BackgroundHeader, LocationSelect, Card, Button} from '../../components'

let PayoutPreference = ({navigation}) => {
  let loanData = useSelector(state => state.user.loanData || {})

  let goToPayout = () => {
    navigation.navigate('Unit21Flow', {custom: 'LoanPayout'})
  }

  let address = loanData?.depositBankAccount?.address
  let currency = loanData?.depositBankAccount?.currency
  let verifiedAt = loanData?.depositBankAccount?.verifiedAt

  let accountNumber = loanData?.depositBankAccount?.accountNumber
  if (!accountNumber && (!address || !verifiedAt)) {
    return null
  }

  let title = 'Stablecoin Wallet'
  let secondLine = <TextReg />
  if (accountNumber) {
    title = 'Bank Account'
    let accountType = loanData?.depositBankAccount?.accountType || ''
    accountType = accountType.charAt(0).toUpperCase() + accountType.slice(1)
    let bankName = loanData?.depositBankAccount?.name || ''
    secondLine = <TextReg>{`${accountType}: ...${accountNumber}`}</TextReg>
  } else {
    let firstFive = address.slice(0, 5)
    let lastFour = address.slice(-4)
    secondLine = <TextReg>{`${currency} ${firstFive}.....${lastFour}`}</TextReg>
  }

  return (
    <View
      style={{
        margin: 8,
        borderRadius: 10,
        borderWidth: 1,
        borderColor: '#777',
        padding: 8,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}>
      <View style={{flexDirection: 'column'}}>
        <TextReg style={{marginBottom: 10}}>PAYOUT PREFERENCE</TextReg>
        <TextReg>{`${title}`}</TextReg>
        {secondLine}
      </View>
      <TouchableOpacity onPress={() => goToPayout()} style={{flex: 1, flexDirection: 'row', justifyContent: 'flex-end'}}>
        <TextBold style={{color: '#00FFBD', fontSize: 19, marginRight: 20}}>EDIT</TextBold>
      </TouchableOpacity>
    </View>
  )
}

export default PayoutPreference
