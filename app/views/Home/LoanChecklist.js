import React, { Component } from 'react'
import { View, Image, TouchableOpacity } from 'react-native'
import { connect } from 'react-redux'

import { TextBold, Card, TextReg } from '../../components'

import styles from './styles'

class LoanChecklist extends Component {
  constructor(props) {
    super(props)
    this.state = {
      saltRedeemed: false,
      saltTxStatus: null,
    }
  }

  componentDidMount() {
    this.checkForSaltRedeemed()
  }

  checkForSaltRedeemed = () => {
    if (this.props.loanData.saltRedeemed) {
      this.props.WebService.getTransactionHistory('SALT').then(res => {
        const transactions = res.data.transactions
        let hasSaltTx = false
        transactions.map(a => {
          if (
            a.reason == 'salt_redemption' &&
            a.amount == this.props.loanData.saltRedeemed
          ) {
            hasSaltTx = a.status || 'unconfirmed'
          }
        })
        if (hasSaltTx) {
          this.setState({ saltTxStatus: hasSaltTx })
        }
      })
    }
  }

  goToLoanRequest = loanRequest => {
    if (loanRequest) {
      this.props.navigation.navigate('Loans')
    } else {
      this.props.navigation.navigate('LoanRequest')
    }
  }

  goToPersonalInfo = () => {
    this.props.navigation.navigate('PersonalInfo', { flow: 'loanChecklist' })
  }

  goToDepositCollateral = () => {
    this.props.navigation.navigate('DepositCollateral')
  }

  goToLoanPayout = () => {
    this.props.navigation.navigate('LoanPayout')
  }

  goToBusinessEntity = () => {
    this.props.navigation.navigate('BusinessEntity', {
      flow: 'loanChecklist',
    })
  }

  goToIdentityVerification = () => {
    this.props.navigation.navigate('IdentityVerification', {
      flow: 'loanChecklist',
    })
  }

  goToRedeemSalt = () => {
    this.props.navigation.navigate('RedeemSalt', {
      flow: 'loanChecklist',
      status: this.state.saltTxStatus,
    })
  }

  render() {
    let loanRequest = false
    const personalProfileComplete = this.props.personalProfileComplete || false
    const hasLoanBankingInfo = this.props.hasLoanBankingInfo || false
    const businessProfileComplete = this.props.businessProfileComplete || false
    const hasCollateralForLoan = this.props.hasCollateralForLoan || false
    const hasIdentityVerification = this.props.hasIdentityVerification || false
    const loanDataSaltRedeemed = Number(this.props.loanData?.saltRedeemed) || 0
    const hasSaltToRedeem = loanDataSaltRedeemed > 0 || false

    if (this.props.loanData.status != 'none') {
      loanRequest = true
    }

    return (
      <React.Fragment>
        <TextReg
          style={{
            fontSize: 16,
            marginBottom: 16,
            width: 320,
            marginTop: 20,
            color: '#28283d',
          }}
        >
          All items must be completed before your loan will be funded. Complete
          items in any order:
        </TextReg>
        <Card marginTop={0}>
          <TouchableOpacity
            style={{
              alignSelf: 'stretch',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
            onPress={() => {
              this.goToLoanRequest(loanRequest)
            }}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              {loanRequest ? (
                <Image
                  style={styles.loanChecklistCompleteImg}
                  source={require('../../imgs/loanChecklist/checklistComplete.png')}
                />
              ) : (
                <View style={styles.loanChecklistIncompleteImg} />
              )}
              <TextBold
                style={
                  loanRequest
                    ? styles.loanChecklistCompleteText
                    : styles.loanChecklistText
                }
              >
                Loan Request
              </TextBold>
            </View>
            <Image
              source={require('../../imgs/rightArrow.png')}
              style={{
                height: 20,
                width: 20,
              }}
            />
          </TouchableOpacity>
        </Card>
        <Card marginTop={-3}>
          <TouchableOpacity
            style={{
              alignSelf: 'stretch',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
            onPress={() => {
              this.goToPersonalInfo()
            }}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              {personalProfileComplete ? (
                <Image
                  style={styles.loanChecklistCompleteImg}
                  source={require('../../imgs/loanChecklist/checklistComplete.png')}
                />
              ) : (
                <View style={styles.loanChecklistIncompleteImg} />
              )}
              <TextBold
                style={
                  personalProfileComplete
                    ? styles.loanChecklistCompleteText
                    : styles.loanChecklistText
                }
              >
                Complete Personal Profile
              </TextBold>
            </View>
            <Image
              source={require('../../imgs/rightArrow.png')}
              style={{
                height: 20,
                width: 20,
              }}
            />
          </TouchableOpacity>
        </Card>

        {this.props.type == 'business' && (
          <Card marginTop={-3}>
            <TouchableOpacity
              style={{
                alignSelf: 'stretch',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
              onPress={() => {
                this.goToBusinessEntity()
              }}
            >
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                {businessProfileComplete ? (
                  <Image
                    style={styles.loanChecklistCompleteImg}
                    source={require('../../imgs/loanChecklist/checklistComplete.png')}
                  />
                ) : (
                  <View style={styles.loanChecklistIncompleteImg} />
                )}
                <TextBold
                  style={
                    businessProfileComplete
                      ? styles.loanChecklistCompleteText
                      : styles.loanChecklistText
                  }
                >
                  Business Entity Information
                </TextBold>
              </View>
              <Image
                source={require('../../imgs/rightArrow.png')}
                style={{
                  height: 20,
                  width: 20,
                }}
              />
            </TouchableOpacity>
          </Card>
        )}
        <Card marginTop={-3}>
          <TouchableOpacity
            style={{
              alignSelf: 'stretch',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
            onPress={() => {
              this.goToIdentityVerification()
            }}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              {hasIdentityVerification ? (
                <Image
                  style={styles.loanChecklistCompleteImg}
                  source={require('../../imgs/loanChecklist/checklistComplete.png')}
                />
              ) : (
                <View style={styles.loanChecklistIncompleteImg} />
              )}
              <TextBold
                style={
                  hasIdentityVerification
                    ? styles.loanChecklistCompleteText
                    : styles.loanChecklistText
                }
              >
                Identity Verification
              </TextBold>
            </View>
            <Image
              source={require('../../imgs/rightArrow.png')}
              style={{
                height: 20,
                width: 20,
              }}
            />
          </TouchableOpacity>
        </Card>
        <Card marginTop={-3}>
          <TouchableOpacity
            style={{
              alignSelf: 'stretch',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
            onPress={() => {
              this.goToLoanPayout()
            }}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              {hasLoanBankingInfo ? (
                <Image
                  style={styles.loanChecklistCompleteImg}
                  source={require('../../imgs/loanChecklist/checklistComplete.png')}
                />
              ) : (
                <View style={styles.loanChecklistIncompleteImg} />
              )}
              <TextBold
                style={
                  hasLoanBankingInfo
                    ? styles.loanChecklistCompleteText
                    : styles.loanChecklistText
                }
              >
                Loan Payout
              </TextBold>
            </View>
            <Image
              source={require('../../imgs/rightArrow.png')}
              style={{
                height: 20,
                width: 20,
              }}
            />
          </TouchableOpacity>
        </Card>
        <Card marginTop={-3}>
          <TouchableOpacity
            style={{
              alignSelf: 'stretch',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
            onPress={() => {
              this.goToDepositCollateral()
            }}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              {hasCollateralForLoan ? (
                <Image
                  style={styles.loanChecklistCompleteImg}
                  source={require('../../imgs/loanChecklist/checklistComplete.png')}
                />
              ) : (
                <View style={styles.loanChecklistIncompleteImg} />
              )}
              <TextBold
                style={
                  hasCollateralForLoan
                    ? styles.loanChecklistCompleteText
                    : styles.loanChecklistText
                }
              >
                Deposit Collateral
              </TextBold>
            </View>
            <Image
              source={require('../../imgs/rightArrow.png')}
              style={{
                height: 20,
                width: 20,
              }}
            />
          </TouchableOpacity>
        </Card>
        {hasSaltToRedeem && (
          <Card marginTop={-3}>
            <TouchableOpacity
              style={{
                alignSelf: 'stretch',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
              onPress={() => {
                this.goToRedeemSalt()
              }}
            >
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                {this.state.saltTxStatus ? (
                  <Image
                    style={styles.loanChecklistCompleteImg}
                    source={require('../../imgs/loanChecklist/checklistComplete.png')}
                  />
                ) : (
                  <View style={styles.loanChecklistIncompleteImg} />
                )}
                <TextBold
                  style={
                    this.state.saltTxStatus
                      ? styles.loanChecklistCompleteText
                      : styles.loanChecklistText
                  }
                >
                  Redeem Salt
                </TextBold>
              </View>
              <Image
                source={require('../../imgs/rightArrow.png')}
                style={{
                  height: 20,
                  width: 20,
                }}
              />
            </TouchableOpacity>
          </Card>
        )}
      </React.Fragment>
    )
  }
}

LoanChecklist.navigationOptions = ({ navigation }) => ({
  title: null,
  header: <View style={{ height: 40, position: 'absolute' }} />,
})

const mapStateToProps = state => ({
  tokenPrices: state.user.prices,
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(LoanChecklist)
