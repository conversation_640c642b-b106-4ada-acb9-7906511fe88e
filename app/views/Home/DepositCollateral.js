import React, {Component} from 'react'
import {View, TouchableOpacity, ScrollView, Image, Clipboard} from 'react-native'
import {connect} from 'react-redux'

import QRCode from 'react-native-qrcode-svg'

import {Card, TextReg, TextBold, BackgroundHeader, Button} from '../../components'
import commonStyles from '../../styles/commonStyles'
import {numberWithCommas} from '../../util/helpers'

import styles from './styles'
import {getTokenPic} from '../../util/tokens'

import {updateWallets} from '../../store/user/user.actions'
import {showToast} from '../../store/notifications/notifications.actions'

class DepositCollateral extends Component {
  constructor(props) {
    super(props)
    const {collaterals} = this.props.loanData
    const rows = collaterals.map(a => ({
      currency: a.currency,
      height: 46,
      open: false,
      showCopied: false,
      creatingWallet: false,
    }))
    this.state = {
      rows,
      verifiedError: false,
      barWidth: 0,
    }
    this.inputs = {}
  }

  loadingCreateWallet = (a, trueFalse) => {
    let {rows} = this.state
    rows = rows.map(b => {
      if (b.currency == a.currency) {
        return {...b, creatingWallet: trueFalse}
      }
      return {...b}
    })
    this.setState({rows})
  }

  checkForWallet = a =>
    new Promise((resolve, reject) => {
      const hasWallet = this.props.loanData.collaterals.filter(b => b.currency === a.currency)[0]

      //if doesnt have a wallet
      if (!hasWallet.address || this.props.user.idVerificationStatus !== 'approved') {
        // create the wallet

        // if user has not finished signup
        if (this.props.user.idVerificationStatus === 'approved') {
          this.loadingCreateWallet(a, true)
          this.props.WebService.createWallet(a.currency)
            .then(res => {
              this.props.dispatch(updateWallets(res.data))
              this.loadingCreateWallet(a, false)
              resolve()
            })
            .catch(err => {
              this.setState({creatingWallet: false})
              reject()
            })
        } else {
          //show Needs to verify ID
          this.setState({verifiedError: true})
          resolve('idVerify')
        }
      } else {
        resolve()
      }
    })

  expandRow = async (a, open) => {
    if (!this.props.user.mfaEnabled) {
      this.props.dispatch(showToast(true))
      return
    }
    const walletRes = await this.checkForWallet(a)
    let {rows} = this.state
    rows = rows.map(b => {
      if (!open && b.currency == a.currency) {
        return {...b, height: walletRes == 'idVerify' ? 200 : 390, open: true}
      }
      if (open && b.currency == a.currency) {
        return {...b, height: 46, open: false}
      }

      return {...b, height: 46, open: false}
    })
    this.setState({rows})
  }

  copyAddress = a => {
    const {rows} = this.state
    const copiedRows = rows.map(b => {
      if (b.currency == a.currency) {
        return {...b, showCopied: true}
      }
      return b
    })
    Clipboard.setString(a.address)
    this.setState({rows: copiedRows}, () => {
      setTimeout(() => {
        let {rows} = this.state
        rows = rows.map(b => {
          if (b.currency == a.currency) {
            return {...b, showCopied: false}
          }
          return b
        })
        this.setState({rows})
      }, 1400)
    })
  }

  getBarWidth = layout => {
    const {x, y, width, height} = layout
    this.setState({barWidth: width})
  }

  goToIdentityVerification = () => {
    this.props.navigation.navigate('IdentityVerification', {
      flow: 'loanChecklist',
    })
  }

  render() {
    const totalValue = this.props.loanData.amount / this.props.loanData.baseLTV || 0
    const showTotalValue = numberWithCommas(totalValue.toFixed(2))
    const {collaterals} = this.props.loanData
    const collateralTotalWithSalt = this.props.loanData?.collateralTotalWithSalt || 0
    const alreadyDeposited = numberWithCommas(collateralTotalWithSalt.toFixed(2))
    let totalRemaining = totalValue - collateralTotalWithSalt
    if (totalRemaining < 0) {
      totalRemaining = 0
    }
    const showTotalRemaining = numberWithCommas(totalRemaining.toFixed(2))
    const barFillPercent = collateralTotalWithSalt / totalValue

    const barFill = (this.state.barWidth / 100) * (barFillPercent * 100)
    const showCollaterals = collaterals.map((a, k) => {
      if (a.currency == 'XRP' || a.currency == 'DASH' || a.currency == 'DOGE' || a.currency == 'PAXG') {
        return
      }
      const showImg = getTokenPic(a.currency)
      const priceTicker = `${a.currency}-USD`
      const price = this.props.tokenPrices[priceTicker].price
      const remainingAmount = numberWithCommas(parseFloat((totalRemaining / Number(price)).toFixed(2)))

      return (
        <Card key={k} marginTop={-4}>
          <View style={{alignSelf: 'stretch', height: this.state.rows[k].height}}>
            <View
              style={{
                alignSelf: 'stretch',
                flexDirection: 'row',
                paddingLeft: 8,
                paddingRight: 8,
                alignItems: 'center',
                justifyContent: 'space-between',
                height: 46,
              }}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Image style={styles.portfolioImg} source={showImg} />
                <TextReg
                  style={{
                    fontSize: 20,
                    width: 60,
                    marginLeft: 16,
                    marginRight: 4,
                  }}>
                  {a.currency}
                </TextReg>
                <TextBold style={{fontSize: 20}}>{remainingAmount}</TextBold>
              </View>
              <TouchableOpacity disabled={this.state.rows[k].creatingWallet} onPress={() => this.expandRow(a, this.state.rows[k].open)}>
                {this.state.rows[k].open ? (
                  <View style={{height: 30, width: 34}}>
                    <Image
                      style={{
                        height: 30,
                        width: 30,
                        opacity: 0.5,
                      }}
                      source={require('../../imgs/backToSettingsDark.png')}
                    />
                  </View>
                ) : (
                  <View>
                    {this.state.rows[k].creatingWallet ? (
                      <Image
                        source={require('../../imgs/loadingDots.gif')}
                        style={{
                          height: 25,
                          width: 40,
                          opacity: 0.6,
                          alignSelf: 'center',
                        }}
                      />
                    ) : (
                      <Image style={{height: 40, width: 40}} source={require('../../imgs/qrCodeSymbol.png')} />
                    )}
                  </View>
                )}
              </TouchableOpacity>
            </View>
            {this.state.verifiedError ? (
              <View
                style={{
                  marginTop: 14,
                  alignSelf: 'stretch',
                  alignItems: 'center',
                }}>
                <View
                  style={{
                    width: 260,
                    height: 1,
                    borderTopWidth: 1,
                    borderColor: '#CCC',
                    marginBottom: 6,
                  }}
                />
                <View
                  style={{
                    alignSelf: 'stretch',
                    paddingTop: 18,
                    alignItems: 'center',
                  }}>
                  <TextReg
                    style={{
                      fontSize: 16,
                      textAlign: 'center',
                      marginBottom: 14,
                      width: 280,
                    }}>
                    Currently your account doesnt not contain a verified ID
                  </TextReg>
                  <Button onPress={() => this.goToIdentityVerification()}>VERIFY ID</Button>
                </View>
              </View>
            ) : (
              <View style={{marginTop: 14, alignItems: 'center'}}>
                <View
                  style={{
                    width: 260,
                    height: 1,
                    borderTopWidth: 1,
                    borderColor: '#CCC',
                    marginBottom: 6,
                  }}
                />
                <View
                  style={{
                    alignSelf: 'stretch',
                    paddingTop: 18,
                    alignItems: 'center',
                  }}>
                  <View
                    style={{
                      height: 160,
                      width: 160,
                      borderRadius: 14,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 16,
                    }}>
                    <View style={styles.depositQrBox}>
                      <QRCode value={a.address} size={158} backgroundColor={'#000'} color={'#FFF'} />
                    </View>
                  </View>
                  <View
                    style={{
                      width: 300,
                      alignItems: 'center',
                    }}>
                    <TextReg
                      style={{
                        textAlign: 'center',
                        marginBottom: 16,
                        marginTop: 7,
                        width: 260,
                      }}>
                      {`To deposit ${a.currency}, please send it to the following address:`}
                    </TextReg>
                    <View
                      style={{
                        borderRadius: 14,
                        backgroundColor: '#eef0f0',
                        width: 260,
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <TextBold
                        style={{
                          color: '#e6e6e6',
                          width: 201,
                          marginLeft: 16,
                          marginRight: 16,
                          textAlign: 'center',
                          height: 40,
                          marginTop: 2,
                        }}>{`${a.address}`}</TextBold>
                      <View
                        style={{
                          height: 40,
                          width: 40,
                          borderRadius: 14,
                          backgroundColor: this.state.rows[k].showCopied ? '#00ffc1' : '#00FFBD',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}>
                        {this.state.rows[k].showCopied ? (
                          <Image style={{height: 32, width: 32}} source={require('../../imgs/checkmark.png')} />
                        ) : (
                          <TouchableOpacity onPress={() => this.copyAddress(a)}>
                            <Image style={{height: 26, width: 26}} source={require('../../imgs/copyDepositButton.png')} />
                          </TouchableOpacity>
                        )}
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            )}
          </View>
        </Card>
      )
    })
    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Deposit Collateral'} goBack={this.props.navigation.goBack} />
        <ScrollView
          style={{
            backgroundColor: '#28283D',
            alignSelf: 'stretch',
          }}
          contentContainerStyle={{alignItems: 'center'}}>
          <Card marginTop={16}>
            <View
              style={{
                flexDirection: 'row',
                marginRight: 6,
                marginTop: 2,
              }}>
              <Image
                style={{height: 18, width: 18, marginRight: 8, marginTop: 4}}
                source={require('../../imgs/loanChecklist/infoIcon.png')}
              />
              <TextReg style={{fontSize: 16}}>
                Deposit <TextBold style={{fontSize: 16}}>any combination</TextBold> of the assets below. Amounts reflect the minimum that
                would be required to collateralize the loan with only that asset.
              </TextReg>
            </View>
          </Card>
          <Card cardMarginBottom={18}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignSelf: 'stretch',
                marginBottom: 18,
                marginTop: 2,
                marginLeft: 4,
                marginRight: 4,
              }}>
              <View>
                <TextReg style={{marginBottom: 4}}>Already Deposited</TextReg>
                <TextBold style={{fontSize: 17}}>{`$${alreadyDeposited}`}</TextBold>
              </View>
              <View style={{alignItems: 'flex-end'}}>
                <TextReg style={{marginBottom: 4}}>Additional Needed</TextReg>
                <TextBold style={{fontSize: 17}}>{`$${showTotalRemaining}`}</TextBold>
              </View>
            </View>
            <View style={{alignSelf: 'stretch', position: 'relative'}}>
              <View
                onLayout={event => {
                  this.getBarWidth(event.nativeEvent.layout)
                }}
                style={{
                  alignSelf: 'stretch',
                  height: 30,
                  backgroundColor: '#e8e8e8',
                  borderRadius: 14,
                  marginLeft: 4,
                  marginRight: 4,
                  marginBottom: 6,
                  overflow: 'hidden',
                }}>
                <View
                  style={{
                    backgroundColor: '#00FFBD',
                    width: barFill || 0,
                    height: 30,
                    zIndex: 20,
                    position: 'absolute',
                  }}
                />
              </View>
            </View>
            <View
              style={{
                flexDirection: 'row',
                marginLeft: 4,
                marginRight: 4,
                alignSelf: 'stretch',
                justifyContent: 'space-between',
              }}>
              <TextReg style={{fontSize: 17, color: '#9b9b9b'}}>$0</TextReg>
              <TextReg
                style={{
                  fontSize: 17,
                  color: '#9b9b9b',
                }}>{`$${showTotalValue}`}</TextReg>
            </View>
          </Card>
          {totalRemaining != 0 && showCollaterals}
        </ScrollView>
      </View>
    )
  }
}

DepositCollateral.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  user: state.user.user,
  WebService: state.auth.WebService,
  tokenPrices: state.user.prices,
})

export default connect(mapStateToProps)(DepositCollateral)
