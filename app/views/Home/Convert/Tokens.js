import React from 'react';
import {View, Image, TouchableOpacity, Keyboard, TextInput} from 'react-native';
import Swipeable from 'react-native-swipeable';

import {Card, TextBold, TextReg} from '../../../components';

import styles from '../styles';

const Tokens = props => (
  <Card marginTop={0} cardMarginBottom={8}>
    <Swipeable
      key={props.a.currency}
      rightActionActivationDistance={200}
      onRightActionRelease={() => props.removeToken(props.a.currency)}
      rightButtons={props.rightTrash(props.a.currency)}>
      <TouchableOpacity
        activeOpacity={1}
        onPress={() => {
          props.editToken(props.a.currency);
        }}>
        <View style={[styles.convertTokenRow]}>
          <View style={styles.convertTokenBox1}>
            <Image source={props.showImg} style={styles.convertTokenBox1Img} />
            <TextReg style={{fontSize: 20}}>{`${props.a.currency} : $${props.amountOfToken}`}</TextReg>
          </View>
          <View style={styles.convertTokenBox2}>
            <TextInput
              style={{
                fontSize: 20,
                marginRight: 4,
                height: 60,
                color: props.a.percentage > 100 ? '#e5705a' : '#fff',
              }}
              ref={input => (props.inputs[props.a.currency] = input)}
              onChangeText={e => props.editTokenPercentage(e, props.a.currency)}
              onFocus={() => props.editToken(props.a.currency)}
              value={`${props.a.percentage}`}
              underlineColorAndroid="transparent"
              blurOnSubmit
              keyboardType={'numeric'}
              returnKeyType={'done'}
              placeholder={'0'}
              onSubmitEditing={() => Keyboard.dismiss()}
              keyboardAppearance="dark"
            />
            <TextBold
              style={{
                fontSize: 20,
                color: props.a.percentage > 100 ? '#e5705a' : '#fff',
              }}>
              %
            </TextBold>
          </View>
        </View>
      </TouchableOpacity>
    </Swipeable>
  </Card>
);

export default Tokens;
