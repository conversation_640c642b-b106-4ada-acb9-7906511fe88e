import React, {Component} from 'react'
import {View, TouchableOpacity, Image, RefreshControl} from 'react-native'
import {connect} from 'react-redux'
import Lot<PERSON><PERSON>ie<PERSON> from 'lottie-react-native'

import {Card, TextReg, TextBold, BackgroundHeader, Button, AddTokenModal} from '../../../components'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {updateWallets} from '../../../store/user/user.actions'
import commonStyles from '../../../styles/commonStyles'
import {getTokenPic} from '../../../util/tokens'
import {cryptoNameMap} from '../../../util/enumerables'
import {numberWithCommas, listPossibleWallets} from '../../../util/helpers'
import Tokens from './Tokens'

import styles from '../styles'

class Convert extends Component {
  constructor(props) {
    super(props)
    this.state = {
      optionSelect: '',
      loading: true,
      showAddToken: false,
      tokenList: [
        {currency: 'BTC', percentage: '80'},
        {currency: 'ETH', percentage: '20'},
      ],
      amountSpendable: 0,
      stabilizationTxId: '0',
      id: 0,
      defaultMix: true,
      creatingWalletError: false,
      creatingWalletLoading: false,
    }
    this.inputs = {}
  }

  componentDidMount() {
    this.getStabilization()
  }

  getStabilization = () => {
    const id = this.props.loanData.id
    this.props.WebService.getStabilization(id)
      .then(res => {
        let tokenList = res.data.conversionPercentages
        const removeIndex = []

        tokenList = tokenList.map((a, k) => {
          const returnObj = {
            currency: a.currency,
            percentage: a.percentage,
          }
          if (
            a.currency == 'XRP' ||
            a.currency == 'DASH' ||
            a.currency == 'DOGE' ||
            a.currency == 'PAXG'
          ) {
            removeIndex.push(k)
          }
          return returnObj
        })
        if (removeIndex.length > 0) {
          for (let i = removeIndex.length - 1; i >= 0; i--) {
            tokenList.splice(removeIndex[i], 1)
          }
        }

        const usdcWallet = this.props.loanData.collaterals.filter(a => a.currency == 'USDC')[0]
        const usdcBalance = usdcWallet.projectedBalance

        this.setState({
          tokenList,
          amountSpendable: usdcBalance, //res.data.amountSpendable,
          stabilizationTxId: res.data.stabilizationTxId,
          loading: false,
          id,
          defaultMix: true,
        })
      })
      .catch(err => {
        console.log('getStabilization err', err)
      })
  }

  requestConversion = () => {
    const payload = {
      stabilizationTxId: this.state.stabilizationTxId,
      conversionAmount: this.state.amountSpendable,
      conversions: this.state.tokenList,
    }
    console.log('requestConversion payload', payload)

    const id = this.state.id
    this.props.WebService.requestConversion(id, payload)
      .then(res => {
        console.log('requestConversion res', res)
      })
      .catch(err => {
        console.log('requestConversion err', err)
      })
  }

  goToConfirm = () => {
    this.props.navigation.navigate('ConfirmConvert', {
      tokenList: this.state.tokenList,
      amountSpendable: this.state.amountSpendable,
      stabilizationTxId: this.state.stabilizationTxId,
      id: this.state.id,
    })
  }

  goBackFix = () => {
    setTimeout(() => {
      this.props.navigation.popToTop()
    }, 200)
  }

  removeToken = token => {
    const tokenList = this.state.tokenList.filter(a => a.currency !== token)
    this.setState({tokenList, defaultMix: false})
  }

  toggleAddToken = () => {
    this.setState({showAddToken: !this.state.showAddToken})
  }

  addToken = token => {
    let collaterals = listPossibleWallets().map(a => {
      return {
        currency: a,
      }
    })
    collaterals = collaterals.map(a => {
      return this.props.loanData?.collaterals.filter(b => b.currency == a.currency)[0] || a
    })

    const hasWallet = collaterals.filter(a => a.currency === token)[0]
    if (!hasWallet?.address) {
      this.setState({
        creatingWalletLoading: token,
        creatingWalletError: false,
      })
      this.props.WebService.createWallet(token)
        .then(res => {
          this.props.dispatch(updateWallets(res.data))
          const tokenList = this.state.tokenList
          tokenList.push({currency: token, percentage: '0'})
          this.setState({
            tokenList,
            defaultMix: false,
            creatingWalletLoading: false,
          })
          this.toggleAddToken()
        })
        .catch(err => {
          console.log('err', err)
          this.setState({
            creatingWalletError: true,
            creatingWalletLoading: false,
          })
        })
    } else {
      console.log('add', token)
      const tokenList = this.state.tokenList
      tokenList.push({currency: token, percentage: '0'})
      this.setState({tokenList, defaultMix: false})
      this.toggleAddToken()
    }
  }

  editToken = token => {
    const tokenList = this.state.tokenList.map(a => {
      if (a.currency == token) {
        return {currency: a.currency, percentage: ''}
      }
      return a
    })
    this.setState({tokenList, defaultMix: false})
    this.inputs[token].focus()
  }

  editTokenPercentage = (e, token) => {
    //no more than 1 '.'
    if (e.indexOf('.') < e.length - 1 && e.slice(-1) == '.') return

    //no longer than 99.00, 4 digits deep
    if (
      (e.indexOf('.') == e.length - 4 && e.length > 3) ||
      e.includes('-') ||
      (e.length == 1 && e.includes('.'))
    ) {
      return
    }
    //let toNumber = Number(e);
    /*
    if (e.indexOf('.') == e.length - 1 && e.length > 0) {
      toNumber = toNumber.toString() + '.';
    }
    */
    const tokenList = this.state.tokenList.map(a => {
      if (a.currency == token) {
        return {currency: a.currency, percentage: e.toString()}
      }
      return a
    })
    this.setState({tokenList, defaultMix: false})
  }

  rightTrash = token => [
    <TouchableOpacity key={token} onPress={() => this.removeToken(token)}>
      <View style={styles.convertTrashRightBox}>
        <Image
          source={require('../../../imgs/icons/trashWhite.png')}
          style={styles.convertTrashRightImg}
        />
      </View>
    </TouchableOpacity>,
  ]

  render() {
    const usdcTotal = Number(this.state.amountSpendable)
    let totalPercentage = 0
    let errorPercentage = false
    this.state.tokenList.map(a => {
      console.log('renderList', a.percentage)
      const percentage = Number(a.percentage)
      console.log('-', percentage)
      if (percentage <= 0) {
        errorPercentage = 'Cannot include percentage less than 0'
      }
      totalPercentage += percentage
    })
    if (totalPercentage.toFixed(2) > 100) {
      errorPercentage = 'Total percentage is over 100'
    }

    const unusedPercentage = 100 - totalPercentage
    const totalXPercentage = totalPercentage * 0.01 * usdcTotal
    let unusedXPercentage = unusedPercentage * 0.01 * usdcTotal
    if (unusedXPercentage < 0) {
      unusedXPercentage = 0
    }

    console.log('this.state.tokenList', this.state.tokenList)

    const showTokens = this.state.tokenList.map((a, k) => {
      const showImg = getTokenPic(a.currency)
      const amountOfToken = numberWithCommas((usdcTotal * (Number(a.percentage) / 100)).toFixed(2))
      return (
        <Tokens
          key={k}
          a={a}
          removeToken={this.removeToken}
          rightTrash={this.rightTrash}
          editToken={this.editToken}
          inputs={this.inputs}
          editTokenPercentage={this.editTokenPercentage}
          showImg={showImg}
          amountOfToken={amountOfToken}
        />
      )
    })

    let collaterals = listPossibleWallets().map(a => {
      return {
        currency: a,
      }
    })
    collaterals = collaterals.map(a => {
      return this.props.loanData?.collaterals.filter(b => b.currency == a.currency)[0] || a
    })
    const tokenThereArr = this.state.tokenList.map(a => a.currency)

    let launchDarkly = this.props.launchDarkly

    let banned = {
      BTC: launchDarkly['disable-btc-deposit'] || false,
      ETH: launchDarkly['disable-eth-deposit'] || false,
      BCH: launchDarkly['disable-bch-deposit'] || false,
      LTC: launchDarkly['disable-ltc-deposit'] || false,
      USDC: launchDarkly['disable-usdc-deposit'] || false,
      USDT: launchDarkly['disable-usdt-deposit'] || false,
      USDP: launchDarkly['disable-usdp-deposit'] || false,
      SALT: launchDarkly['disable-salt-deposit'] || true, // salt just default true=banned on convert
      TUSD: launchDarkly['disable-tusd-deposit'] || false,
      XRP: launchDarkly['disable-xrp-deposit'] || true,
      DASH: launchDarkly['disable-dash-deposit'] || true,
      DOGE: launchDarkly['disable-doge-deposit'] || true,
      PAXG: launchDarkly['disable-paxg-deposit'] || true,
    }

    console.log('convert -  banned', banned)

    const showCollaterals = collaterals.map((a, k) => {
      if (tokenThereArr.includes(a.currency) || banned[a.currency]) {
        return
      }
      const collateralImg = getTokenPic(a.currency)
      const currencyType = cryptoNameMap.get(a.currency.toLowerCase())
      let {prices, prices24h, loanData} = this.props

      let xPrice = prices[`${a.currency}-USD`]?.price || 0
      let xPrice24 = prices24h[`${a.currency}-USD`]?.price || 0
      xPrice = Number(xPrice)
      xPrice24 = Number(xPrice24)
      let collaterals = loanData?.collaterals || []
      let wallet = collaterals?.filter(b => b.currency == a.currency)[0] || {}
      let balance = Number(wallet?.projectedBalance) || 0
      balance = numberWithCommas(balance?.toFixed(2))
      let value = Number(wallet?.value) || 0
      value = numberWithCommas(value?.toFixed(2))

      let showPrice = Number(xPrice) || 0
      showPrice = numberWithCommas(showPrice?.toFixed(2))

      let change = (((xPrice - xPrice24) / xPrice24) * 100)?.toFixed(2)
      let priceColor = '#00FFBD'
      if (change < 0) {
        priceColor = '#E5705A'
      }

      return (
        <TouchableOpacity
          key={k}
          onPress={() => {
            this.addToken(a.currency)
          }}>
          <View style={styles.convertCollateralsBox}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                flex: 1,
              }}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Image source={collateralImg} style={styles.convertCollateralsImg} />
                <View style={{flexDirection: 'column'}}>
                  <TextReg style={{fontSize: 20, marginRight: 8}}>{a.currency}</TextReg>
                  <TextReg style={{fontSize: 15, color: '#bababa'}}>{currencyType}</TextReg>
                </View>
              </View>
              <View style={{flexDirection: 'column', width: 150, alignItems: 'flex-end'}}>
                <TextBold style={{fontSize: 20, textAlign: 'right'}}>{`$${showPrice}`}</TextBold>
                <TextReg
                  style={{
                    fontSize: 15,
                    color: priceColor,
                    textAlign: 'right',
                  }}>{` (${change}%)`}</TextReg>
              </View>
            </View>
            {this.state.creatingWalletLoading == a.currency && (
              <LottieView
                style={{width: 40, height: 40, marginRight: 14, marginTop: -3}}
                source={require('../../../imgs/lotti/loading-white-dots.json')}
                autoPlay
              />
            )}
          </View>
        </TouchableOpacity>
      )
    })

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Convert'} goBack={this.goBackFix} />
        <AddTokenModal
          showAddToken={this.state.showAddToken}
          showPinScreen={this.props.showPinScreen}
          toggleAddToken={this.toggleAddToken}
          showCollaterals={showCollaterals}
        />
        <KeyboardAwareScrollView
          extraScrollHeight={40}
          style={{
            backgroundColor: '#28283D',
            alignSelf: 'stretch',
          }}
          refreshControl={
            <RefreshControl
              refreshing={this.state.loading}
              colors={['#28283D']}
              tintColor={'#fff'}
              onRefresh={this.getStabilization}
            />
          }
          contentContainerStyle={{alignItems: 'center'}}>
          {!this.state.loading && (
            <View>
              <Card cardMarginBottom={0} marginTop={16}>
                <View style={styles.convertLayout}>
                  <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
                    <View style={styles.convertUSDCBox}>
                      <Image
                        source={require('../../../imgs/logos/main/usdc.png')}
                        style={styles.convertUSDCImg}
                      />
                      <TextReg style={{fontSize: 18, marginBottom: 12}}>USD Coin (USDC)</TextReg>
                      <TextReg style={styles.convertUSDCAmountTitle}>AMOUNT TO CONVERT:</TextReg>
                      <TextBold style={styles.convertUSDCNumber}>{`$${numberWithCommas(
                        usdcTotal.toFixed(2),
                      )}`}</TextBold>
                      <TextReg style={{fontSize: 18, color: '#00FFBD'}}>{`${numberWithCommas(
                        usdcTotal.toFixed(2),
                      )} USDC`}</TextReg>
                    </View>
                  </View>
                  <TextReg style={styles.convertPleaseNoteText}>
                    <TextBold style={{color: '#fff'}}>*Please Note: </TextBold>
                    The USDC balance shown above includes only the volatile assets that were
                    converted during stabilization.
                  </TextReg>
                </View>
              </Card>
              <View style={styles.convertTotalBox}>
                <View style={{alignItems: 'flex-end', marginRight: 20}}>
                  <TextBold style={styles.convertTotalText}>TOTAL:</TextBold>
                  <TextBold style={styles.convertTotalText}>REMAINING USDC:</TextBold>
                </View>
                <View style={{alignItems: 'flex-end'}}>
                  <TextBold style={styles.convertTotalText}>{`$${numberWithCommas(
                    totalXPercentage.toFixed(2),
                  )}`}</TextBold>
                  <TextBold style={styles.convertTotalText}>{`$${numberWithCommas(
                    unusedXPercentage.toFixed(2),
                  )}`}</TextBold>
                </View>
              </View>

              <View
                style={{
                  marginTop: 2,
                  marginBottom: 12,
                  alignSelf: 'stretch',
                  alignItems: 'center',
                }}>
                <TextReg style={{opacity: 0.64, fontSize: 18}}>
                  TAP TO EDIT, SWIPE TO DELETE
                </TextReg>
              </View>
              {showTokens}
              <View style={styles.convertDefaultedBox}>
                {this.state.defaultMix && (
                  <TextBold style={{marginTop: 4, fontSize: 12, color: '#00FFBD'}}>
                    * Defaulted to Original Collateral Mix
                  </TextBold>
                )}
              </View>

              <TouchableOpacity onPress={() => this.toggleAddToken()}>
                <View style={styles.convertAddCurrencyBox}>
                  <TextBold style={styles.convertAddCurrencyText}>+ ADD CURRENCY</TextBold>
                </View>
              </TouchableOpacity>

              <Button
                style={styles.convertNextButton}
                onPress={() => this.goToConfirm()}
                disabled={errorPercentage != false || this.state.tokenList?.length < 1}>
                <TextBold style={{fontSize: 16, color: '#000'}}>NEXT</TextBold>
              </Button>
              {errorPercentage && (
                <View style={styles.convertErrorBox}>
                  <TextReg style={styles.showErrorText}>{errorPercentage}</TextReg>
                </View>
              )}
              {this.state.tokenList?.length < 1 && (
                <View style={styles.convertErrorBox}>
                  <TextReg
                    style={styles.showErrorText}>{`Must have at least 1 convert token`}</TextReg>
                </View>
              )}
              <View style={{alignSelf: 'stretch', height: 30}} />
            </View>
          )}
        </KeyboardAwareScrollView>
      </View>
    )
  }
}

Convert.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  WebService: state.auth.WebService,
  showPinScreen: state.auth.pinScreen,
  prices: state.user.prices,
  prices24h: state.user.prices24h,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(Convert)
