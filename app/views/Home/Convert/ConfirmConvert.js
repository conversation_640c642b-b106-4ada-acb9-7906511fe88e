import React, {Component} from 'react'
import {View, Image, ScrollView} from 'react-native'
import {connect} from 'react-redux'
import BigNumber from 'bignumber.js'

import {Card, TextReg, TextBold, BackgroundHeader, Button} from '../../../components'
import commonStyles from '../../../styles/commonStyles'
import {getTokenPic} from '../../../util/tokens'
import {formatCurrency, numberWithCommas} from '../../../util/helpers'

import styles from '../styles'

class ConfirmConvert extends Component {
  constructor(props) {
    super(props)
    this.state = {
      loading: false,
      error: false,
      totalMix: 0,
    }
    this.polling = null
  }

  componentDidMount() {
    //move over to grpc
    this.polling = setInterval(this.getPrices, 20000)
    let totalMix = 0
    this.props.route.params.tokenList.map(a => {
      totalMix += Number(a.percentage)
    })
    this.setState({totalMix})
  }

  componentWillUnmount() {
    clearInterval(this.polling)
  }

  goBackFix = () => {
    setTimeout(() => {
      this.props.navigation.goBack()
    }, 200)
  }

  goToTop = () => {
    setTimeout(() => {
      this.props.navigation.popToTop()
    }, 200)
  }

  goToSuccess = () => {
    const {tokenList, amountSpendable} = this.props.route.params
    this.props.navigation.navigate('ConvertSuccess', {
      tokenList,
      amountSpendable,
      totalMix: this.state.totalMix,
    })
  }

  getPrices = () => {
    // /tryPrice(this.props.dispatch)
    /*
    this.props.WebService.getPrices()
      .then(res => {
        this.props.dispatch(updatePrices(res.data))
      })
      .catch(err => {
        throw err
      })
      */
  }

  requestConversion = () => {
    const {tokenList, amountSpendable, stabilizationTxId, id} = this.props.route.params

    const payload = {
      stabilizationTxId,
      conversionAmount: amountSpendable,
      conversions: tokenList,
    }
    this.setState({loading: true, error: false})
    this.props.WebService.requestConversion(id, payload)
      .then(res => {
        console.log('requestConversion res', res)
        this.setState({loading: false})
        this.goToSuccess()
      })
      .catch(err => {
        console.log('err', err)
        this.setState({loading: false, error: true})
      })
  }

  render() {
    const {tokenList, amountSpendable} = this.props.route.params

    let totalPercentage = 0
    tokenList.map(a => {
      totalPercentage += Number(a.percentage)
    })
    const payWithTotal = ((totalPercentage / 100) * amountSpendable).toFixed(2)
    const fee = payWithTotal * 0.02

    const totalMinusFee = new BigNumber(payWithTotal).minus(fee) //payWithTotal - fee
    const conversionAmount = formatCurrency(totalMinusFee)
    let usdcRemaining = (amountSpendable - payWithTotal).toFixed(2)
    if (usdcRemaining < 0) {
      usdcRemaining = 0
    }
    usdcRemaining = numberWithCommas(usdcRemaining)

    const showReceivableTokens = tokenList.map((a, k) => {
      const tokenUSD = `${a.currency}-USD`
      let price = Number(this.props.prices[tokenUSD].price)
      price *= 1.015 // 150 basis point price
      const valueOfToken = (
        totalMinusFee.toNumber() *
        (Number(a.percentage) / this.state.totalMix)
      ).toFixed(2)

      let unitsOfToken = (valueOfToken / price) * 1
      if (a.currency == 'DOGE') {
        unitsOfToken = unitsOfToken.toFixed(3)
      } else {
        unitsOfToken = unitsOfToken.toFixed(7)
      }

      let rowStyle = styles.convertConfirmRow
      if (k == tokenList.length - 1) {
        rowStyle = styles.convertConfirmRowNoBorder
      }
      const showImg = getTokenPic(a.currency)

      return (
        <View key={k} style={rowStyle}>
          <View
            style={{
              flex: 1,
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <Image source={showImg} style={styles.confirmTokenImg} />
            <TextReg style={{fontSize: 14, color: '#fff'}}>{`${a.currency}`}</TextReg>
          </View>

          <View
            style={{
              flex: 1,
              alignItems: 'center',
            }}>
            <TextReg style={{fontSize: 14, color: '#fff'}}>{`${numberWithCommas(
              unitsOfToken,
            )}`}</TextReg>
          </View>
          <View
            style={{
              flex: 1,
              alignItems: 'flex-end',
            }}>
            <TextReg
              style={{
                fontSize: 14,
                color: '#fff',
                textAlign: 'right',
              }}>
              {`$${numberWithCommas(valueOfToken)}`}
            </TextReg>
          </View>
        </View>
      )
    })

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Convert'} goBack={this.goBackFix} />
        <ScrollView
          style={{
            backgroundColor: '#28283D',
            alignSelf: 'stretch',
          }}
          contentContainerStyle={{alignItems: 'center'}}>
          <TextBold style={[styles.statusTitle, {marginTop: 20}]}>Confirm Conversion</TextBold>
          <View style={{alignSelf: 'stretch', marginLeft: 30, marginRight: 30}}>
            <TextReg style={{fontSize: 16, marginBottom: 16}}>
              Please review and confirm the following information regarding your conversion details.
            </TextReg>
          </View>
          <Card>
            <View style={{alignSelf: 'stretch'}}>
              <TextBold
                style={{
                  fontSize: 18,
                  marginLeft: 10,
                  marginBottom: 6,
                  marginTop: 8,
                }}>
                Conversion Details:
              </TextBold>
              <View style={styles.convertConfirmRow}>
                <TextReg style={{fontSize: 16, color: '#fff'}}>Pay With</TextReg>
                <TextReg style={{fontSize: 16, color: '#fff'}}>{`$${numberWithCommas(
                  payWithTotal,
                )} USDC`}</TextReg>
              </View>
              <View style={styles.convertConfirmRow}>
                <TextReg style={{fontSize: 16, color: '#fff'}}>Fee (2%)</TextReg>
                <View style={{flexDirection: 'row'}}>
                  <TextReg
                    style={{
                      fontSize: 16,
                      color: '#fff',
                      textDecorationStyle: 'solid',
                    }}>{`$${formatCurrency(fee)}`}</TextReg>
                </View>
              </View>
              <View style={styles.convertConfirmRow}>
                <TextReg style={{fontSize: 16, color: '#fff'}}>Conversion Amount</TextReg>
                <TextReg style={{fontSize: 16, color: '#fff'}}>{`$${conversionAmount}`}</TextReg>
              </View>
              <View style={styles.convertConfirmRowNoBorder}>
                <TextReg style={{fontSize: 16, color: '#fff'}}>USDC Remaining</TextReg>
                <TextReg style={{fontSize: 16, color: '#fff'}}>{`$${usdcRemaining}`}</TextReg>
              </View>
            </View>
          </Card>
          <Card>
            <View style={{alignSelf: 'stretch'}}>
              <TextBold
                style={{
                  fontSize: 18,
                  marginLeft: 10,
                  marginBottom: 6,
                  marginTop: 8,
                }}>
                Receivables:
              </TextBold>
              <View style={styles.convertReceivablesRow}>
                <TextBold style={{fontSize: 16, color: '#fff', flex: 1}}>Currency</TextBold>
                <TextBold
                  style={{
                    fontSize: 16,
                    color: '#fff',
                    flex: 1,
                    textAlign: 'center',
                  }}>
                  Units
                </TextBold>
                <TextBold
                  style={{
                    fontSize: 16,
                    color: '#fff',
                    flex: 1,
                    textAlign: 'right',
                  }}>
                  Value (USD)
                </TextBold>
              </View>
              {showReceivableTokens}
            </View>
          </Card>
          <TextReg
            style={{
              color: '#e5705a',
              marginTop: 2,
              marginLeft: 30,
              marginRight: 30,
              fontSize: 12,
              lineHeight: 13,
            }}>
            By clicking Convert Now, I understand that the amount of cryptocurrency to be received
            upon conversion is subject to adjustment based on market volatility, volume, liquidity,
            network fees and other market factors. I understand that cryptocurrency transactions are
            irreversible and that I will be charged the fee listed above by Salt. High volume orders
            may be subject to additional fees.
          </TextReg>
          <Button
            style={styles.confirmButton}
            onPress={() => this.requestConversion()}
            isLoading={this.state.loading}>
            <TextBold style={{fontSize: 16, color: '#000'}}>CONVERT NOW</TextBold>
          </Button>

          {this.state.error && (
            <View style={styles.convertErrorBox}>
              <TextReg style={styles.showErrorText}>Error submitting conversion request.</TextReg>
            </View>
          )}
          <View style={{alignSelf: 'stretch', height: 22}} />
        </ScrollView>
      </View>
    )
  }
}

ConfirmConvert.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  WebService: state.auth.WebService,
  showPinScreen: state.auth.pinScreen,
  prices: state.user.prices,
})

export default connect(mapStateToProps)(ConfirmConvert)
