import React, {Component} from 'react'
import {View, Image, ScrollView, Platform} from 'react-native'
import {connect} from 'react-redux'

import {Card, TextReg, TextBold, BackgroundHeader, Button} from '../../../components'
import commonStyles from '../../../styles/commonStyles'
import {getTokenPic} from '../../../util/tokens'
import {formatCurrency, numberWithCommas, shortMonth} from '../../../util/helpers'
import {increaseRefreshDataCount} from '../../../store/user/user.actions'

import styles from '../styles'
import BigNumber from 'bignumber.js'

class ConvertSuccess extends Component {
  constructor(props) {
    super(props)
    let showDate = new Date()
    this.state = {showDate}
  }

  goToTop = () => {
    this.props.dispatch(increaseRefreshDataCount())
    setTimeout(() => {
      this.props.navigation.popToTop()
    }, 200)
  }

  render() {
    const {tokenList, amountSpendable, totalMix} = this.props.route.params
    let ltv = this.props.loanData?.ltv
    ltv = (ltv * 100).toFixed(2)

    let showDate = this.state.showDate

    let split1 = ''
    let split2 = ''

    if (Platform.OS === 'ios') {
      showDate = showDate.toLocaleTimeString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      })
      const commaSplit = showDate.split(',')
      split1 = commaSplit[0] + commaSplit[1]
      split2 = commaSplit[2]
    } else {
      split1 = `${showDate.getMonth()}-${showDate.getDate()}-${showDate.getFullYear()}`
      split2 = showDate.toTimeString().split(' ')[0]
      let mornAfternoon = 'AM'
      let timeSplitAndroid = split2.split(':')
      let firstTimeSplit = Number(timeSplitAndroid[0])
      if (firstTimeSplit >= 13) {
        mornAfternoon = 'PM'
        firstTimeSplit = firstTimeSplit - 12
      }
      split2 = `${firstTimeSplit}:${timeSplitAndroid[1]}:${timeSplitAndroid[2]} ${mornAfternoon}`

      let monthSplit = split1.split('-')[0]
      let monthName = shortMonth(Number(monthSplit) + 1)
      split1 = `${monthName} ${split1.split('-')[1]} ${split1.split('-')[2]}`
    }

    let totalPercentage = 0
    tokenList.map(a => {
      totalPercentage += Number(a.percentage)
    })
    const payWithTotal = ((totalPercentage / 100) * amountSpendable).toFixed(2)
    const fee = payWithTotal * 0.02
    const totalMinusFee = new BigNumber(payWithTotal).minus(fee) //payWithTotal - fee
    const conversionAmount = formatCurrency(totalMinusFee)

    const showTokens = tokenList.map((a, k) => {
      const showImg = getTokenPic(a.currency)

      let valueOfToken = (totalMinusFee.toNumber() * (Number(a.percentage) / totalMix)).toFixed(2)

      const tokenUSD = `${a.currency}-USD`
      let price = Number(this.props.prices[tokenUSD].price)
      price *= 1.015 // 150 basis point price

      let unitsOfToken = ((valueOfToken / price) * 1).toFixed(7)
      unitsOfToken = numberWithCommas(unitsOfToken)
      valueOfToken = numberWithCommas(valueOfToken)

      const lastRow = k == tokenList.length - 1

      return (
        <View
          key={k}
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            paddingBottom: 8,
            borderBottomWidth: lastRow ? 0 : 0.5,
            borderColor: '#707070',
            marginBottom: lastRow ? 0 : 8,
            alignSelf: 'stretch',
            marginLeft: 10,
            marginRight: 10,
            alignItems: 'center',
          }}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Image source={showImg} style={styles.convertTokenBox1Img} />
            <TextReg style={{fontSize: 16, color: '#fff'}}>{`${a.currency}`}</TextReg>
          </View>
          <TextReg style={{fontSize: 16}}>{`${unitsOfToken}`}</TextReg>
          <TextReg style={{fontSize: 16, marginRight: 10}}>{`$${valueOfToken}`}</TextReg>
        </View>
      )
    })

    const showUSDCImg = getTokenPic('USDC')

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Convert'} goBack={this.goBackFix} />
        <ScrollView
          style={{
            backgroundColor: '#28283D',
            alignSelf: 'stretch',
          }}
          contentContainerStyle={{alignItems: 'center'}}>
          <Card marginTop={20}>
            <View style={{alignSelf: 'stretch'}}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: 6,
                }}>
                <TextBold
                  style={{
                    fontSize: 18,
                    marginLeft: 10,
                  }}>
                  Conversion Complete!
                </TextBold>
                <Image
                  source={require('../../../imgs/graphics/swap.png')}
                  style={styles.statusImg}
                />
              </View>
              <TextReg style={{marginLeft: 10, marginRight: 10, marginBottom: 8}}>
                {`Your assets have been converted back to your requested collateral mix.  Please review your LTV and loan health on the loan status page. You may also review a record of this transaction on the event history page.`}
              </TextReg>
            </View>
          </Card>

          <Card style={{marginTop: 0}}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                alignSelf: 'stretch',
              }}>
              <View style={{alignItems: 'center', flex: 1}}>
                <TextReg>{split1}</TextReg>
                <TextReg>{split2}</TextReg>
              </View>
              <View
                style={{
                  alignItems: 'center',
                  borderLeftWidth: 0.5,
                  borderRightWidth: 0.5,
                  flex: 1,
                }}>
                <TextReg>Total Converted</TextReg>
                <TextBold>{`$${numberWithCommas(payWithTotal)}`}</TextBold>
              </View>
              <View style={{alignItems: 'center', flex: 1}}>
                <TextReg>Threshold LTV</TextReg>
                <TextBold>{`${ltv}%`}</TextBold>
              </View>
            </View>
          </Card>
          <Card style={{marginTop: 0}}>
            <View style={{alignSelf: 'stretch', marginLeft: 10, marginRight: 10}}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  padding: 10,
                  paddingLeft: 6,
                  borderBottomWidth: 0.5,
                  borderColor: '#707070',
                }}>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <Image source={showUSDCImg} style={{height: 24, width: 24, marginRight: 4}} />
                  <TextReg style={{fontSize: 16}}>USDCoin USDC</TextReg>
                </View>

                <TextReg style={{fontSize: 16}}>{`$${numberWithCommas(payWithTotal)}`}</TextReg>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  borderBottomWidth: 0.5,
                  borderColor: '#707070',
                  padding: 10,
                }}>
                <TextReg style={{fontSize: 16}}>Fee (2%)</TextReg>
                <View style={{flexDirection: 'row'}}>
                  <TextReg
                    style={{
                      fontSize: 16,
                      textDecorationStyle: 'solid',
                    }}>{`$${formatCurrency(fee)}`}</TextReg>
                </View>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  padding: 10,
                }}>
                <TextReg style={{fontSize: 16}}>Conversion Amount</TextReg>
                <TextReg style={{fontSize: 16}}>{`$${numberWithCommas(conversionAmount)}`}</TextReg>
              </View>
            </View>
          </Card>
          <Card style={{marginTop: 0}}>{showTokens}</Card>

          <Button style={styles.confirmButton} onPress={() => this.goToTop()}>
            <TextBold style={{fontSize: 16, color: '#000'}}>CLOSE</TextBold>
          </Button>
          <View style={{alignSelf: 'stretch', height: 22}} />
        </ScrollView>
      </View>
    )
  }
}

ConvertSuccess.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  WebService: state.auth.WebService,
  showPinScreen: state.auth.pinScreen,
  prices: state.user.prices,
})

export default connect(mapStateToProps)(ConvertSuccess)
