import React, { Component } from 'react'
import { View, Text, StyleSheet } from 'react-native'

import { Button, TextReg, Card } from '../../components'
import styles from './styles'

class ZaboBox extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  openZabo = () => {
    console.log('connect zabo')
    this.props.navigation.navigate('ConnectZabo')
  }

  render() {
    return (
      <Card marginTop={10} cardMarginBottom={0}>
        <View
          style={{ alignSelf: 'stretch', paddingLeft: 10, paddingRight: 10 }}
        >
          <TextReg
            style={{
              fontSize: 24,
              marginTop: 16,
              marginBottom: 10,
              color: '#28283D',
            }}
          >
            Zabo
          </TextReg>
          <TextReg style={{ fontSize: 18, marginBottom: 18, color: '#28283D' }}>
            Zabo testing-
          </TextReg>
          <Button
            style={{ alignSelf: 'stretch', marginBottom: 10 }}
            onPress={() => this.openZabo()}
          >
            <TextReg style={{ color: '#FFF' }}>Zabo</TextReg>
          </Button>
        </View>
      </Card>
    )
  }
}

export default ZaboBox
