import React, {useState, useEffect} from 'react'
import {View, TouchableOpacity, Image, SafeAreaView, ScrollView, Dimensions} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import {useNavigation} from '@react-navigation/native'
import DropDownPicker from 'react-native-dropdown-picker'
import LottieView from 'lottie-react-native'

import {Card, TextReg, TextBold} from '../../../../components'
import {increaseRefreshDataCount} from '../../../../store/user/user.actions'
import custodyAccount from '../../../../imgs/graphics/custodyAccount.png'
import dots3 from '../../../../imgs/dots3.png'
import closeX from '../../../../imgs/closeX.png'
import carrotDown from '../../../../imgs/carrotDown.png'
import trashDark from '../../../../imgs/graphics/trashDark.png'
const {height: ScreenHeight} = Dimensions.get('window')

const DeleteAcc = ({route}) => {
  const navigation = useNavigation()
  let dispatch = useDispatch()
  let WebService = useSelector(state => state.auth.WebService)
  let user = useSelector(state => state.user.user)
  let [drop, setDrop] = useState(false)
  let [value, setValue] = useState(null)
  let [loading, setLoading] = useState(false)

  let ref = route?.params.ref
  let thisAcc = user?.accounts?.filter(a => a.ref == ref)[0]

  let wallets = user?.allWallets[ref - 1] || []
  let sumValue = 0
  let assets = []
  wallets?.map(a => {
    console.log('a2', a)
    if (Number(a.value) > 0) {
      assets.push(a.currency)
    }
    sumValue += Number(a.value)
  })

  let hasCollat = false
  if (sumValue > 0) {
    hasCollat = true
  }

  deleteReq = async () => {
    setLoading(true)
    let res = await WebService.deleteAccount(thisAcc?.id, ref)
    setLoading(false)
    dispatch(increaseRefreshDataCount())
    navigation.goBack()
  }

  //make lend != 'active'
  let items = user?.accounts?.map(a => {
    if (a.productType == 'investment' && a?.product?.investment?.status != 'awaiting_collateral') {
      return null
    }

    //only 1 asset of the investment type
    if (a.productType == 'investment') {
      let assetType = a?.product?.investment?.investmentAsset
      console.log('a', a, assets, assetType)

      //only 1 asset, of that assetType
      if (assets?.length > 1) return null
      if (assets[0] != assetType) return null
    }

    return {label: a.name === 'Custody Account' ? 'WALLET Account' : a.name, value: a.ref}
  })
  items = items?.filter(a => a)
  items = items?.filter(a => a.value != ref)

  let showValue = items?.filter(a => a.value == value)[0]?.label

  if (!showValue) showValue = 'Select'

  transferDelete = async () => {
    if (!value) return
    // Transfer assets

    try {
      setLoading(true)
      const transferPromises = wallets?.map(async a => {
        if (a?.projectedBalance != '0') {
          let toId = user?.accounts?.filter(b => b.ref == value)[0]?.id
          const payload = {
            fromWalletId: a.id,
            toAccountId: toId,
            amount: a.projectedBalance,
          }

          let transferRes = await WebService.transferSend(payload, ref)
          console.log('transferRes', transferRes)
          return transferRes
        }
      })

      await Promise.all(transferPromises)
      deleteReq()
    } catch (err) {
      console.log('delete transfer err', err)
    }
  }

  let typeTitle = 'BORROW'
  console.log('route?.params', route?.params)
  if (route?.params?.type == 'investment') {
    typeTitle = 'LEND'
  }

  return (
    <SafeAreaView style={local.box}>
      {drop && (
        <DropDownPicker
          open={drop}
          value={value}
          items={items}
          setOpen={setDrop}
          setValue={setValue}
          itemKey="key"
          theme="DARK"
          listMode={'MODAL'}
          searchable={false}
          closeAfterSelecting={true}
          placeholder={'select'}
          style={local.drop}
          textStyle={{fontSize: 16, color: '#FFF'}}
          placeholderStyle={{fontSize: 16, color: '#FFF'}}
          translation={{
            NOTHING_TO_SHOW: 'Not Found!',
          }}
        />
      )}
      {hasCollat && (
        <ScrollView style={{alignSelf: 'stretch'}}>
          <View
            style={{
              minHeight: ScreenHeight - 150,
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <View style={local.boxHeader}>
              <View style={{height: 24, width: 24}} />
              <TextReg style={{fontSize: 20, marginTop: 3}}>{`Delete Account`}</TextReg>
              <TouchableOpacity onPress={() => navigation.goBack()}>
                <Image
                  source={closeX}
                  style={{
                    height: 26,
                    width: 26,
                  }}
                />
              </TouchableOpacity>
            </View>
            <View style={local.boxCircle}>
              <Image
                source={trashDark}
                style={{
                  height: 50,
                  width: 42,
                }}
              />
            </View>
            <TextBold style={local.titleCenter}>{`Transfer collaterals before delete`}</TextBold>
            <View style={{alignSelf: 'stretch', alignItems: 'center', margin: 20}}>
              <TextReg
                style={{
                  color: '#FFF',
                  fontSize: 15,
                  textAlign: 'center',
                  marginBottom: 20,
                }}>{`This account, ${thisAcc.name}, has assets. Choose a destination account to transfer the assets.`}</TextReg>
              <View style={{alignSelf: 'stretch'}}>
                <TouchableOpacity style={local.boxSelect} onPress={() => setDrop(true)}>
                  <TextReg style={{fontSize: 15}}>{`${showValue}`}</TextReg>
                  <Image
                    source={carrotDown}
                    style={{
                      height: 20,
                      width: 20,
                    }}
                  />
                </TouchableOpacity>
              </View>

              <TouchableOpacity style={local.btnDelete} onPress={() => transferDelete()}>
                {loading ? (
                  <LottieView
                    style={local.dots}
                    source={require('../../../../imgs/lotti/loading-white-dots.json')}
                    autoPlay
                  />
                ) : (
                  <TextBold
                    style={{
                      color: '#000',
                      letterSpacing: 2,
                      fontSize: 16,
                    }}>{`DELETE & TRANSFER`}</TextBold>
                )}
              </TouchableOpacity>
              <TouchableOpacity onPress={() => navigation.goBack()} style={local.btnCancel}>
                <TextBold
                  style={{color: '#00FFBD', letterSpacing: 2, fontSize: 16}}>{`CANCEL`}</TextBold>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      )}
      {!hasCollat && (
        <ScrollView style={{alignSelf: 'stretch'}}>
          <View
            style={{
              minHeight: ScreenHeight - 150,
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <View style={local.boxHeader}>
              <View style={{height: 24, width: 24}} />
              <TextReg style={{fontSize: 20, marginTop: 3}}>{`Delete Account`}</TextReg>
              <TouchableOpacity onPress={() => navigation.goBack()}>
                <Image
                  source={closeX}
                  style={{
                    height: 26,
                    width: 26,
                  }}
                />
              </TouchableOpacity>
            </View>

            <View
              style={{
                alignSelf: 'stretch',
                alignItems: 'center',
                marginLeft: 20,
                marginRight: 20,
                marginTop: -100,
              }}>
              <View style={local.boxCircle}>
                <Image
                  source={trashDark}
                  style={{
                    height: 50,
                    width: 42,
                  }}
                />
              </View>
              <TextBold style={local.titleCenter2}>{`Delete ${typeTitle} Account?`}</TextBold>
              <TextReg
                style={{
                  color: '#FFF',
                  fontSize: 15,
                  textAlign: 'center',
                  marginTop: 20,
                }}>{`Are you sure you want to delete ${thisAcc.name}? This action cannot be undone.`}</TextReg>
            </View>

            <View style={{alignSelf: 'stretch', alignItems: 'center', margin: 20}}>
              <TouchableOpacity style={local.btnDelete} onPress={() => deleteReq()}>
                {loading ? (
                  <LottieView
                    style={local.dots}
                    source={require('../../../../imgs/lotti/loading-white-dots.json')}
                    autoPlay
                  />
                ) : (
                  <TextBold
                    style={{
                      color: '#000',
                      letterSpacing: 2,
                      fontSize: 16,
                    }}>{`DELETE`}</TextBold>
                )}
              </TouchableOpacity>
              <TouchableOpacity onPress={() => navigation.goBack()} style={local.btnCancel}>
                <TextBold
                  style={{color: '#00FFBD', letterSpacing: 2, fontSize: 16}}>{`CANCEL`}</TextBold>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  )
}

let local = {
  box: {
    display: 'flex',
    flex: 1,
    backgroundColor: '#28283B',
    alignSelf: 'stretch',
    padding: 4,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  btnCancel: {
    height: 52,
    alignSelf: 'stretch',
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
    borderWidth: 1,
    borderColor: '#00FFBD',
    marginBottom: 20,
  },
  btnDelete: {
    height: 52,
    alignSelf: 'stretch',
    backgroundColor: '#E5705A',
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
  },
  boxSelect: {
    alignSelf: 'stretch',
    height: 50,
    borderRadius: 10,
    borderColor: '#FFF',
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 14,
    paddingRight: 16,
    marginBottom: 40,
  },
  selectTitle: {
    color: '#FFF',
    fontSize: 15,
    marginTop: 20,
    marginBottom: 4,
    marginLeft: 3,
  },
  boxHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignSelf: 'stretch',
    paddingLeft: 20,
    paddingRight: 20,
  },
  boxCircle: {
    height: 160,
    width: 160,
    borderRadius: 80,
    backgroundColor: '#CB6F5B',
    marginTop: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  titleCenter: {
    color: '#FFF',
    fontSize: 20,
    textAlign: 'center',
    width: 200,
    marginTop: 30,
  },
  titleCenter2: {
    color: '#FFF',
    fontSize: 20,
    textAlign: 'center',
    marginTop: 30,
  },
  drop: {
    height: 54,
    borderWidth: 1,
    borderRadius: 6,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    backgroundColor: '#474756', //'#3D3D50',
    fontSize: 16,
    marginBottom: 18,
    minHeight: 40,
  },
  dots: {
    width: 60,
    height: 60,
    opacity: 0.9,
    alignSelf: 'center',
    zIndex: 5,
  },
  //CB6F5B
}

export default DeleteAcc
