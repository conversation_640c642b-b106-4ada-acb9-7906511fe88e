import React, {useState, useEffect} from 'react'
import {Modal, View, Image, TouchableOpacity, ScrollView, Dimensions} from 'react-native'
import {TextReg, TextBold, BackgroundHeader} from '../../../components'
import {useNavigation} from '@react-navigation/native'
import lendPng from '../../../imgs/graphics/lendIcon2.png'
import {useSelector} from 'react-redux'

let GetAccredited = ({}) => {
  let navigation = useNavigation()
  let WebService = useSelector(state => state.auth.WebService)
  let [sent, setSent] = useState(false)

  let sendEmail = async () => {
    let res = await WebService.sendAccreditedEmail()
    console.log('sendAccreditedEmail res', res)
    setSent(true)
  }

  return (
    <View style={local.page}>
      <BackgroundHeader title={'LEND'} close closeFn={() => navigation.goBack()} />
      <View
        style={{
          flexDirection: 'column',
          justifyContent: 'space-between',
          alignItems: 'center',
          flex: 1,
        }}>
        <View
          style={{
            height: 180,
            width: 180,
            justifyContent: 'center',
            alignItems: 'center',
            borderRadius: 90,
            backgroundColor: '#3D3D50',
            marginTop: 20,
          }}>
          <Image source={lendPng} style={{height: 100, width: 100}} />
        </View>
        <TextBold style={{fontSize: 24, width: 300, textAlign: 'center'}}>
          {'Are you an Accredited Investor?'}
        </TextBold>
        {sent ? (
          <TextReg style={{color: '#FFFFFF', width: 300, textAlign: 'center', marginTop: 10}}>
            {'Thank you, please check your email for more information reguarding accreditation.'}
          </TextReg>
        ) : (
          <TextReg style={{color: '#FFFFFF', width: 300, textAlign: 'center', marginTop: 10}}>
            {
              'LEND is only available for accredited investors. If you’d like more information about accessing this product, please confirm that you meet the requirements to receive more information about the offering below.'
            }
          </TextReg>
        )}
        <View style={{alignSelf: 'stretch', alignItems: 'center', margin: 20}}>
          {!sent && (
            <TouchableOpacity style={local.btnConfirm} onPress={() => sendEmail()}>
              <TextBold
                style={{
                  color: '#000',
                  letterSpacing: 2,
                  fontSize: 16,
                }}>{`CONFIRM`}</TextBold>
            </TouchableOpacity>
          )}
          <TouchableOpacity onPress={() => navigation.goBack()} style={local.btnCancel}>
            <TextBold style={{color: '#00FFBD', letterSpacing: 2, fontSize: 16}}>
              {sent ? `COMPLETE` : `CANCEL`}
            </TextBold>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  )
}

let local = {
  page: {
    flex: 1,
    backgroundColor: '#28283D',
  },
  box: {
    padding: 20,
  },
  textStyle: {
    marginBottom: 4,
  },
  btnCancel: {
    height: 52,
    alignSelf: 'stretch',
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
    borderWidth: 1,
    borderColor: '#00FFBD',
    marginBottom: 20,
  },
  btnConfirm: {
    height: 52,
    alignSelf: 'stretch',
    backgroundColor: '#00FFBD',
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
  },
}

export default GetAccredited
