import React, {useState, useEffect} from 'react'
import {View, TouchableOpacity, Image, Linking} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import {useNavigation} from '@react-navigation/native'
import {Card, TextReg, TextBold, DistBar} from '../../../../components'
import {numberWithCommas, nextPaymentFormatDate, getNextMonthDate} from '../../../../util/helpers'
import custodyAccount from '../../../../imgs/graphics/custodyAccount.png'
import eye from '../../../../imgs/graphics/eye.png'
import trashRed from '../../../../imgs/graphics/trashRed.png'
import dots3 from '../../../../imgs/dots3.png'
import carrotUp from '../../../../imgs/carrotUp.png'
import carrotDown from '../../../../imgs/carrotDown.png'
import {getTokenPic} from '../../../../util/tokens'
import plusWhite from '../../../../imgs/icons/plusWhite.png'
import givePng from '../../../../imgs/graphics/give.png'
import lendPng from '../../../../imgs/graphics/lendIcon.png'

const LendBox = ({arr, goToProduct, goToInvestment}) => {
  let [show, setShow] = useState(false)
  const navigation = useNavigation()
  let [details, setDetails] = useState(false)

  useEffect(() => {
    if (!show) {
      setDetails(false)
    }
  }, [show])

  if (arr?.length < 1) return null

  let showDetails = k => {
    if (details == k) {
      setDetails(false)
    } else {
      setDetails(k)
    }
  }

  let formatDate = dateString => {
    const date = new Date(dateString)

    // Get the month names
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ]
    const month = months[date.getUTCMonth()]

    // Get the day and format with 'st', 'nd', 'rd', 'th'
    const day = date.getUTCDate()
    const daySuffix = day => {
      if (day % 10 === 1 && day !== 11) return 'st'
      if (day % 10 === 2 && day !== 12) return 'nd'
      if (day % 10 === 3 && day !== 13) return 'rd'
      return 'th'
    }

    const year = date.getUTCFullYear()

    // Return formatted string
    return `${month} ${day}${daySuffix(day)}, ${year}`
  }

  let goSupport = () => {
    Linking.openURL('mailto:<EMAIL>')
  }

  let goTo = a => {
    setDetails(false)
    goToInvestment(a?.a?.account)
  }

  let deleteFlow = a => {
    setDetails(false)
    navigation.navigate('DeleteAcc', {ref: a?.a?.account?.ref, type: 'investment'})
  }

  let totalVal = 0

  let innerBoxes = arr.map((a, i) => {
    let wallets = []
    a?.a?.wallets?.map(b => {
      if (!wallets?.includes(b.currency) && b.value != '0') {
        wallets.push(b.currency)
      }
    })

    let showWallets = wallets?.map((b, p) => {
      if (b == 'XRP') {
        b = 'XRPW'
      }
      return (
        <View key={p} style={{...local.tokenBox, zIndex: 20 - p}}>
          <Image source={getTokenPic(b)} style={{height: 22, width: 22}} />
        </View>
      )
    })

    let sumValue = a?.sumValue
    totalVal += Number(sumValue?.replaceAll(/,/g, '')) /////

    let nextPayout = a?.a?.investments[0]?.upcomingProjectedEarnings[0]?.payoutDate || null
    let datePayout = formatDate(nextPayout)

    let canDeposit = false
    if (a?.a?.investments[0]?.status == 'awaiting_collateral') {
      canDeposit = true
    }

    let distWallets = a?.a?.wallets || []

    let isActive = false

    if (a?.a?.account?.product?.investment?.status == 'active') {
      isActive = true
    }

    let status = a?.a?.account?.product?.investment?.status
    if (status == 'pending_signatures' || status == 'awaiting_collateral') {
      canDeposit = false
    }

    let earningNum = a?.a?.investments[0]?.nextMonthlyEarning || 0
    earningNum = Number(earningNum).toFixed(6)

    let assetType = a?.a?.investments[0]?.investmentAsset || ''

    let showApy = a?.a?.investments[0]?.interestRate || 0
    showApy = (Number(showApy) * 100).toFixed(2)

    let showBalanceAsset = 0
    if (a?.a?.investments[0]?.collaterals) {
      let correctCollat = a?.a?.investments[0]?.collaterals?.filter(a => a.currency == assetType)[0]
      showBalanceAsset = correctCollat?.projectedBalance || 0
      showBalanceAsset = +Number(showBalanceAsset).toFixed(7)
    }

    let lendInfo = (
      <>
        <View
          key={i}
          style={{
            flexDirection: 'row',
            marginTop: 6,
            marginBottom: 16,
          }}>
          <View style={{flexDirection: 'column', flex: 1}}>
            <TextReg style={{fontSize: 17, opacity: 0.6, marginTop: 8}}>{`Balance`}</TextReg>
            <TextBold
              style={{
                fontSize: 17,
                opacity: 1,
                marginTop: 2,
              }}>{`${showBalanceAsset} ${assetType}`}</TextBold>
          </View>
          {nextPayout && (
            <View style={{flexDirection: 'column', flex: 1}}>
              <TextReg
                style={{fontSize: 17, opacity: 0.6, marginTop: 8}}>{`Interest Rate`}</TextReg>
              <TextBold style={{fontSize: 17, opacity: 1, marginTop: 2}}>{`${showApy}%`}</TextBold>
            </View>
          )}
        </View>
        <View
          style={{
            flexDirection: 'row',
            marginTop: 6,
            marginBottom: 16,
          }}>
          <View style={{flexDirection: 'column', flex: 1}}>
            <TextReg
              style={{fontSize: 17, opacity: 0.6, marginTop: 8}}>{`Monthly Earnings`}</TextReg>
            <TextBold
              style={{
                fontSize: 17,
                opacity: 1,
                marginTop: 2,
              }}>{`${earningNum} ${assetType}`}</TextBold>
          </View>
          {nextPayout && (
            <View style={{flexDirection: 'column', flex: 1}}>
              <TextReg
                style={{fontSize: 17, opacity: 0.6, marginTop: 8}}>{`Next Earnings Date`}</TextReg>
              <TextBold
                style={{fontSize: 17, opacity: 1, marginTop: 2}}>{`${datePayout}`}</TextBold>
            </View>
          )}
        </View>
      </>
    )

    if (status == 'under_review') {
      lendInfo = (
        <>
          <View style={{flexDirection: 'column', marginTop: 6}}>
            <TextBold style={{fontSize: 17, marginBottom: 6}}>
              {'Your compliance review is processing.'}
            </TextBold>
            <TextReg
              style={{
                marginBottom: 6,
              }}>{`Please allow up to 48 business hours for your compliance review to be completed.`}</TextReg>
            <TouchableOpacity onPress={() => goSupport()} style={{flexDirection: 'column'}}>
              <TextReg>{`Have a question in the meantime?`}</TextReg>
              <TextReg
                style={{
                  color: '#00FFBD',
                  textDecorationLine: 'underline',
                  textDecorationStyle: 'solid',
                  textDecorationColor: '#00FFBD',
                  marginLeft: 4,
                }}>{`Contact Support`}</TextReg>
            </TouchableOpacity>
          </View>
        </>
      )
    }

    if (status == 'pending_signatures') {
      lendInfo = (
        <>
          <View
            style={{
              flexDirection: 'row',
              marginTop: 6,
              backgroundColor: '#28283D',
              borderRadius: 8,
              padding: 10,
            }}>
            <Image source={givePng} style={{height: 40, width: 40, marginRight: 10}} />

            <View style={{flexDirection: 'column', paddingRight: 30}}>
              <TextBold style={{fontSize: 17, marginBottom: 6}}>
                {'Your LEND Documents are pending signature.'}
              </TextBold>
              <TextReg
                style={{
                  marginBottom: 6,
                  paddingRight: 30,
                }}>{`Please check your email inbox for your loan documents to be signed electronically to complete the next step.`}</TextReg>
              <TouchableOpacity onPress={() => goSupport()} style={{flexDirection: 'column'}}>
                <TextReg style={{flexWrap: 'wrap'}}>{`Have a question in the meantime?`}</TextReg>
                <TextReg
                  style={{
                    color: '#00FFBD',
                    textDecorationLine: 'underline',
                    textDecorationStyle: 'solid',
                    textDecorationColor: '#00FFBD',
                    marginTop: 4,
                    flexWrap: 'wrap',
                  }}>{`Contact Support`}</TextReg>
              </TouchableOpacity>
            </View>
          </View>
        </>
      )
    }

    if (status == 'awaiting_collateral') {
      lendInfo = (
        <>
          <View
            style={{
              flexDirection: 'row',
              marginTop: 6,
              backgroundColor: '#28283D',
              borderRadius: 8,
              padding: 10,
            }}>
            <Image source={givePng} style={{height: 40, width: 40, marginRight: 10}} />

            <View style={{flexDirection: 'column', paddingRight: 30}}>
              <TextBold style={{fontSize: 17, marginBottom: 6}}>
                {'Your LEND account is awaiting your collateral deposit.'}
              </TextBold>
              <TextReg
                style={{
                  marginBottom: 6,
                  paddingRight: 30,
                }}>{`Please deposit the collateral required to move onto the next step.`}</TextReg>
              <TextBold style={{color: '#00FFBD', textDecorationLine: 'underline'}}>
                {`Complete Now`}
              </TextBold>
            </View>
          </View>
        </>
      )
    }

    if (status == 'in_progress') {
      lendInfo = (
        <>
          <View
            style={{
              flexDirection: 'row',
              marginTop: 6,
              backgroundColor: '#28283D',
              borderRadius: 8,
              padding: 10,
            }}>
            <Image source={givePng} style={{height: 40, width: 40, marginRight: 10}} />

            <View style={{flexDirection: 'column', paddingRight: 30}}>
              <TextBold style={{fontSize: 17, marginBottom: 6}}>
                {'Your LEND application is in progress.'}
              </TextBold>
              <TextReg
                style={{
                  marginBottom: 6,
                  paddingRight: 30,
                }}>{`Please click here to continue to your application.`}</TextReg>
              <TextBold style={{color: '#00FFBD', textDecorationLine: 'underline'}}>
                {`Complete Now`}
              </TextBold>
            </View>
          </View>
        </>
      )
    }

    if (status == 'pending_approval') {
      lendInfo = (
        <>
          <View style={{flexDirection: 'column', marginTop: 6, paddingRight: 20}}>
            <TextBold style={{fontSize: 17, marginBottom: 6}}>
              {'Your LEND account is Pending Approval.'}
            </TextBold>
            <TextReg
              style={{
                marginBottom: 6,
              }}>{`SALT is performing a review, and we’ll email when all looks good!`}</TextReg>
            <TouchableOpacity onPress={() => goSupport()} style={{flexDirection: 'column'}}>
              <TextReg style={{flexWrap: 'wrap'}}>{`Have a question in the meantime?`}</TextReg>
              <TextReg
                style={{
                  color: '#00FFBD',
                  textDecorationLine: 'underline',
                  textDecorationStyle: 'solid',
                  textDecorationColor: '#00FFBD',
                  marginTop: 4,
                  flexWrap: 'wrap',
                }}>{`Contact Support`}</TextReg>
            </TouchableOpacity>
          </View>
        </>
      )
    }

    let pendingClosure = status == 'pending_closure'

    if (pendingClosure) {
      lendInfo = (
        <>
          <View style={{flexDirection: 'column', marginTop: 6, paddingRight: 20}}>
            <TextBold style={{fontSize: 17, marginBottom: 6}}>
              {'Account closure request under review.'}
            </TextBold>
            <TextReg
              style={{
                marginBottom: 6,
              }}>{`We are in the process of closing your account. This process should take 5-7 business days to complete.`}</TextReg>
            <TouchableOpacity onPress={() => goSupport()} style={{flexDirection: 'column'}}>
              <TextReg style={{flexWrap: 'wrap'}}>{`Have a question in the meantime?`}</TextReg>
              <TextReg
                style={{
                  color: '#00FFBD',
                  textDecorationLine: 'underline',
                  textDecorationStyle: 'solid',
                  textDecorationColor: '#00FFBD',
                  marginTop: 4,
                  flexWrap: 'wrap',
                }}>{`Contact Support`}</TextReg>
            </TouchableOpacity>
          </View>
        </>
      )
    }

    let description = 'Business Lend'
    if (a?.a?.account?.type == 'personal') {
      description = 'Personal Lend'
    }

    return (
      <TouchableOpacity style={local.innerBox} onPress={() => goTo(a)}>
        <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'}}>
          <View style={{flexDirection: 'column'}}>
            <TextReg
              style={{
                fontSize: 17,
                maxWidth: 250,
                overflow: 'hidden',
              }}>{`${a.a?.account.name}`}</TextReg>
            <TextReg style={{fontSize: 15, opacity: 0.8}}>{description}</TextReg>
          </View>
          {!isActive && !pendingClosure && (
            <View style={{zIndex: 40}}>
              <TouchableOpacity
                onPress={() => {
                  deleteFlow(a)
                }}
                style={{
                  flexDirection: 'row',
                  paddingLeft: 2,
                }}>
                <Image source={trashRed} style={{height: 20, width: 16, marginRight: 6}} />
              </TouchableOpacity>
            </View>
          )}
        </View>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginTop: details == i + 1 ? 70 : 6,
          }}>
          {showWallets}
        </View>

        <View
          style={{
            alignSelf: 'stretch',
            height: 2,
            backgroundColor: '#5c5c6f',
            marginLeft: -10,
            marginRight: -10,
            marginTop: 8,
          }}
        />
        {lendInfo}

        {canDeposit && (
          <View style={local.deposit}>
            <TextReg style={{color: '#00FFBD', fontSize: 20}}>{`DEPOSIT`}</TextReg>
          </View>
        )}
      </TouchableOpacity>
    )
  })

  let newLend = () => {
    navigation.navigate('CreateInvestmentAccount')
  }

  //personal max 4
  let newAcc = true

  totalVal = numberWithCommas(totalVal?.toFixed(2))

  let toggleArea = () => {
    if (arr?.length < 1) {
      newLend()
      return
    }
    setShow(!show)
  }

  return (
    <View style={local.box}>
      <TouchableOpacity
        onPress={() => toggleArea()}
        activeOpacity={1}
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignSelf: 'stretch',
          alignItems: 'center',
        }}>
        <View style={{flexDirection: 'row'}}>
          <View style={local.imgBox}>
            <Image source={lendPng} style={{height: 24, width: 23}} />
          </View>
          <View style={{flexDirection: 'column'}}>
            <TextReg>LEND</TextReg>
            <TextBold style={{fontSize: 20}}>{`$${totalVal}`}</TextBold>
          </View>
        </View>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {newAcc && (
            <TouchableOpacity onPress={() => newLend()}>
              <Image
                source={plusWhite}
                style={{height: 18, width: 18, marginRight: arr?.length > 0 ? 30 : 8}}
              />
            </TouchableOpacity>
          )}
          {arr?.length > 0 && (
            <Image
              source={show ? carrotUp : carrotDown}
              style={{height: 22, width: 22, marginRight: 8}}
            />
          )}
        </View>
      </TouchableOpacity>
      {show && <>{innerBoxes}</>}
    </View>
  )
}

let local = {
  box: {
    display: 'flex',
    marginLeft: 12,
    marginRight: 12,
    borderRadius: 14,
    backgroundColor: '#3D3D50',
    alignSelf: 'stretch',
    padding: 14,
    marginTop: 12,
    marginBottom: 12,
  },
  imgBox: {
    backgroundColor: '#28283D',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    marginRight: 10,
  },
  innerBox: {
    backgroundColor: '#505061',
    borderRadius: 14,
    padding: 14,
    alignSelf: 'stretch',
    marginTop: 10,
  },
  tokenBox: {
    backgroundColor: '#000',
    borderRadius: 20,
    height: 40,
    width: 40,
    marginRight: -10,
    borderWidth: 3,
    borderColor: '#505061',
    alignItems: 'center',
    justifyContent: 'center',
  },
  distBar: {
    height: 8,
    borderRadius: 4,
    alignSelf: 'stretch',
    marginRight: 70,
    backgroundColor: '#F90',
    marginTop: 10,
    overflow: 'hidden',
    marginBottom: 20,
  },
  deposit: {
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#00FFBD',
    borderRadius: 4,
    height: 50,
    marginBottom: 10,
  },
  dots3: {
    height: 40,
    width: 40,
    alignItems: 'flex-end',
  },
  dotsImg: {
    height: 30,
    width: 30,
  },
  dotsImgActive: {
    height: 30,
    width: 30,
    backgroundColor: '#3D3D50',
    borderColor: '#888',
    borderWidth: 1,
    borderRadius: 4,
  },
}

export default LendBox
