import React, {useState, useEffect} from 'react'
import {View, TouchableOpacity, Image, Linking} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import {useNavigation} from '@react-navigation/native'
import {Card, TextReg, TextBold} from '../../../../components'
import {numberWithCommas, nextPaymentFormatDate} from '../../../../util/helpers'
import {getTokenPic} from '../../../../util/tokens'

import custodyAccount from '../../../../imgs/graphics/custodyAccount.png'
import eye from '../../../../imgs/graphics/eye.png'
import trashRed from '../../../../imgs/graphics/trashRed.png'
import dots3 from '../../../../imgs/dots3.png'
import carrotUp from '../../../../imgs/carrotUp.png'
import carrotDown from '../../../../imgs/carrotDown.png'
import refreshCoins from '../../../../imgs/graphics/refreshCoins.png'
import refreshBag from '../../../../imgs/graphics/refreshBag.png'
import plusWhite from '../../../../imgs/icons/plusWhite.png'
import givePng from '../../../../imgs/graphics/give.png'
import AsyncStorage from '@react-native-async-storage/async-storage'

const BorrowBox = ({arr, goToProduct, user}) => {
  let [show, setShow] = useState(user?.primaryEmail == '<EMAIL>')
  const [hasRefinanceRequest, setHasRefinanceRequest] = useState(false)
  const navigation = useNavigation()

  let [details, setDetails] = useState(false)

  const hasNewRefinancedLoans = async () =>
    JSON.parse(await AsyncStorage.getItem('refinancedLoans'))

  useEffect(() => {
    if (!show) {
      setDetails(false)
    }
    hasNewRefinancedLoans().then(res => {
      if (res?.length > 0) {
        setHasRefinanceRequest(true)
      }
    })
  }, [show])

  if (arr?.length < 1) return null

  let goSupport = () => {
    Linking.openURL('mailto:<EMAIL>')
  }

  let totalAssets = 0
  let totalLib = 0

  let showDetails = k => {
    if (details == k) {
      setDetails(false)
    } else {
      setDetails(k)
    }
  }

  let goTo = (a, forRefi) => {
    setDetails(false)
    goToProduct(
      {id: a?.a?.account.id, ref: a?.a?.account.ref},
      a?.accountType,
      a?.a?.account,
      forRefi,
    )
  }

  let deleteFlow = a => {
    setDetails(false)
    navigation.navigate('DeleteAcc', {ref: a?.a?.account?.ref, type: 'loan'})
  }

  let innerBoxes = arr.map((a, i) => {
    let collatValue = a?.a?.loans[0]?.collateralValue || '0'
    collatValue = Number(collatValue)?.toFixed(2)
    if (collatValue == '0.00') {
      collatValue = 0
      //still check wallets
      a?.a?.wallets?.map(b => {
        collatValue += Number(b.value)
      })
    }
    totalAssets += Number(collatValue)
    collatValue = numberWithCommas(collatValue)

    let decimalLtv = a?.a?.loans[0]?.ltv || '0'
    let ltvValue = (Number(decimalLtv) * 100).toFixed(2)

    let balanceVal = a?.a?.loans[0]?.amortizationInfo?.currentBalance || '0'
    balanceVal = Number(balanceVal)?.toFixed(2)
    totalLib += Number(balanceVal)
    balanceVal = numberWithCommas(balanceVal)

    let nextPaymentDate = a?.a?.loans[0]?.amortizationInfo?.nextPaymentDate || null
    if (nextPaymentDate) {
      nextPaymentDate = nextPaymentFormatDate(nextPaymentDate)
    }

    //if payment date is already past and not paid-
    let nextPaymentDate1 = a?.a?.loans[0]?.amortizationInfo?.nextPaymentDate || null

    let amSchedule = a?.a?.loans[0]?.amortizationInfo?.amortizationSchedule || []

    let notPaid = amSchedule.filter(a => a.principalPaid == '0')

    if (a?.a?.loans[0]?.interestOnly) {
      notPaid = amSchedule.filter(a => a.interestPaid == '0')
    }

    let earliestDate = notPaid.sort((a, b) => new Date(a.date) - new Date(b.date))[0]
    if (earliestDate && new Date(earliestDate?.date) < new Date(nextPaymentDate1)) {
      nextPaymentDate1 = earliestDate?.date
    }

    if (nextPaymentDate) {
      nextPaymentDate = nextPaymentFormatDate(nextPaymentDate1)
    }

    let isOverDue = false

    let testDate = new Date()
    let paymentDate = nextPaymentDate1 && new Date(nextPaymentDate1)
    if (paymentDate) {
      // Set to beginning of next day (Apr 16 00:00:00)
      let dueDate = new Date(paymentDate)
      dueDate.setDate(dueDate.getDate() + 1)
      dueDate.setHours(0, 0, 0, 0)

      isOverDue = testDate >= dueDate
    }

    let wallets = []
    a?.a?.wallets?.map(b => {
      if (!wallets?.includes(b.currency) && b.value != '0') {
        wallets.push(b.currency)
      }
    })

    let showWallets = wallets?.map((b, p) => {
      if (b == 'XRP') {
        b = 'XRPW'
      }
      return (
        <View key={p} style={{...local.tokenBox, zIndex: 20 - p}}>
          <Image source={getTokenPic(b)} style={{height: 22, width: 22}} />
        </View>
      )
    })

    let isActive = false
    let status = null
    let refinanceLoanStatus = null
    let refinanceLoanInfo = null

    if (a?.a?.account?.refinanceLoan?.status) {
      refinanceLoanStatus = a?.a?.account?.refinanceLoan?.status
    }
    if (a?.a?.account?.loans) {
      status = a?.a?.account?.loans[0]?.status
    } else {
      status = a?.a?.account?.product?.loan?.status
    }

    if (status == 'active') {
      isActive = true
    }

    //if loan not active, but still wallet,
    let walletVal = 0
    a?.a?.wallets?.map(b => {
      walletVal += Number(b.value)
    })

    if (collatValue == '0.00' && walletVal != 0) {
      totalAssets += Number(walletVal)
      collatValue = numberWithCommas(walletVal)
    }
    let thresholds = null
    let statusShow = null
    let borderColor = '#505061'

    const refinanceLoanInformation = () => {
      const account = a?.a?.account
      const refinanceLoan = account?.refinanceLoan
      const isEntityAddressDoc = account?.entityProfile?.address?.documents?.[0]?.rejectedAt
      const isEntitybusinessDoc = account?.entityProfile?.documents?.[0]?.rejectedAt
      const isBusinessDocumentAvailable = account?.entityProfile?.documents.filter(
        doc => doc.type === 'verification_of_business',
      )?.length
      const isBusinessAddressAvailable = account?.entityProfile?.address?.documents.filter(
        doc => doc.type === 'other_proof_of_address',
      )?.length

      if (
        refinanceLoan?.depositBankAccountType !== 'bank_account' &&
        !refinanceLoan?.depositBankAccount?.verifiedAt
      ) {
        return {
          title: 'Refinance in Progress',
          description: 'Payout address verification needed!',
          buttonText: 'Complete Now',
        }
      }

      if (
        a?.a?.account?.type === 'business' &&
        (!!isEntityAddressDoc ||
          !!isEntitybusinessDoc ||
          !isBusinessDocumentAvailable ||
          !isBusinessAddressAvailable)
      ) {
        return {
          title: 'Refinance in Progress',
          description: 'Document upload needed!',
          buttonText: 'Complete Now',
        }
      }

      if (refinanceLoan?.loanDocuments.length <= 0) {
        return {
          title: 'Refinance in Progress',
          description: 'Document upload needed!',
          buttonText: 'Complete Now',
        }
      }

      return {
        title: 'Refinance in Progress',
      }
    }

    if (refinanceLoanStatus) {
      refinanceLoanInfo = (
        <TouchableOpacity onPress={() => goTo(a, true)}>
          <View
            style={{
              backgroundColor: '#28283D',
              paddingVertical: 12,
              paddingHorizontal: 24,
              flexDirection: 'row',
              gap: 20,
              borderRadius: 8,
            }}>
            <View style={{marginTop: 14}}>
              <Image
                source={require('../../../../imgs/referrals/hand.png')}
                style={local.handImage}
              />
              <View style={local.iconContainer}>
                <Image
                  source={require('../../../../imgs/homeActivities/repayment.png')}
                  style={local.iconImage}
                />
              </View>
            </View>
            <View style={{width: '90%', justifyContent: 'center'}}>
              <TextBold>{refinanceLoanInformation()?.title}</TextBold>
              <View>
                {refinanceLoanInformation()?.description && (
                  <TextReg style={{display: 'flex', flexWrap: 'wrap', marginTop: 12}}>
                    {refinanceLoanInformation()?.description}
                  </TextReg>
                )}
                {refinanceLoanInformation()?.buttonText && (
                  <TextBold style={{color: '#00FFBD', textDecorationLine: 'underline'}}>
                    {refinanceLoanInformation()?.buttonText}
                  </TextBold>
                )}
              </View>
            </View>
          </View>
        </TouchableOpacity>
      )
    }
    if (status) {
      //is loan
      thresholds = a?.a?.loans[0]?.thresholds

      let isStabilized = a?.a?.account?.product?.loan?.isStabilized

      statusShow = (
        <View
          style={{
            borderRadius: 10,
            backgroundColor: '#34E89E',
            justifyContent: 'center',
            height: 22,
            paddingLeft: 6,
            paddingRight: 6,
            marginLeft: 6,
            marginTop: 6,
          }}>
          <TextReg style={{color: '#000'}}>{`Healthy`}</TextReg>
        </View>
      )

      if (decimalLtv < Number(thresholds?.warning || 1)) {
        //healthy default
      } else if (decimalLtv < Number(thresholds?.marginCall || 1)) {
        //warning
        borderColor = '#F7D956'
        statusShow = (
          <View
            style={{
              borderRadius: 10,
              backgroundColor: '#F7D956',
              justifyContent: 'center',
              height: 22,
              paddingLeft: 6,
              paddingRight: 6,
              marginLeft: 6,
              marginTop: 6,
            }}>
            <TextReg style={{color: '#000'}}>{`Warning`}</TextReg>
          </View>
        )
      } else if (decimalLtv < Number(thresholds?.liquidation || 1)) {
        //warning
        borderColor = '#E6705B'
        statusShow = (
          <View
            style={{
              borderRadius: 10,
              backgroundColor: '#E6705B',
              justifyContent: 'center',
              height: 22,
              paddingLeft: 6,
              paddingRight: 6,
              marginLeft: 6,
              marginTop: 6,
            }}>
            <TextReg style={{color: '#FFF'}}>{`Margin Call`}</TextReg>
          </View>
        )
      }

      if (isStabilized) {
        borderColor = '#AFAFAF'
        const forStabilization =
          a?.a?.loans[0]?.marginManagementPreference === 'stabilization' ? true : false
        statusShow = (
          <View
            style={{
              borderRadius: 10,
              backgroundColor: '#AFAFAF',
              justifyContent: 'center',
              height: 22,
              paddingLeft: 6,
              paddingRight: 6,
              marginLeft: 6,
              marginTop: 6,
            }}>
            <TextReg style={{color: '#FFF'}}>
              {forStabilization ? 'Stabilization' : 'Liquidation'}
            </TextReg>
          </View>
        )
      }
    }

    let loanInfo = (
      <>
        <View style={{flexDirection: 'row', marginTop: 6}}>
          <View style={{flexDirection: 'column', flex: 1}}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <TextReg style={{fontSize: 17, opacity: 0.6, marginTop: 8}}>{`LTV`}</TextReg>
              {statusShow}
            </View>
            <TextBold style={{fontSize: 17, opacity: 1, marginTop: 2}}>{`${ltvValue}%`}</TextBold>
          </View>
          <View style={{flexDirection: 'column', flex: 1}}>
            <TextReg style={{fontSize: 17, opacity: 0.6, marginTop: 8}}>{`Loan Balance`}</TextReg>
            <TextBold style={{fontSize: 17, opacity: 1, marginTop: 2}}>{`$${balanceVal}`}</TextBold>
          </View>
        </View>

        <View style={{flexDirection: 'row', marginTop: 6, marginBottom: 6}}>
          <View style={{flexDirection: 'column', flex: 1}}>
            <TextReg
              style={{fontSize: 17, opacity: 0.6, marginTop: 8}}>{`Collateral Value`}</TextReg>
            <TextBold
              style={{fontSize: 17, opacity: 1, marginTop: 2}}>{`$${collatValue}`}</TextBold>
          </View>
          {nextPaymentDate && (
            <View style={{flexDirection: 'column', flex: 1}}>
              <TextReg style={{fontSize: 17, opacity: 0.6, marginTop: 8}}>{`Next Payment`}</TextReg>
              <TextBold style={{fontSize: 17, opacity: 1, marginTop: 2}}>
                {nextPaymentDate} {isOverDue && <TextBold style={{fontSize: 14}}>(Due)</TextBold>}
              </TextBold>
            </View>
          )}
        </View>
      </>
    )

    if (status == 'under_review') {
      loanInfo = (
        <>
          <View style={{flexDirection: 'column', marginTop: 6, paddingRight: 20}}>
            <TextBold style={{fontSize: 17, marginBottom: 6}}>
              {'Your compliance review is processing.'}
            </TextBold>
            <TextReg
              style={{
                marginBottom: 6,
              }}>{`Please allow up to 48 business hours for your compliance review to be completed.`}</TextReg>
            <TouchableOpacity onPress={() => goSupport()} style={{flexDirection: 'column'}}>
              <TextReg style={{flexWrap: 'wrap'}}>{`Have a question in the meantime?`}</TextReg>
              <TextReg
                style={{
                  color: '#00FFBD',
                  textDecorationLine: 'underline',
                  textDecorationStyle: 'solid',
                  textDecorationColor: '#00FFBD',
                  marginTop: 4,
                  flexWrap: 'wrap',
                }}>{`Contact Support`}</TextReg>
            </TouchableOpacity>
          </View>
        </>
      )
    }

    if (status == 'pending_signatures') {
      loanInfo = (
        <>
          <View
            style={{
              flexDirection: 'row',
              marginTop: 6,
              backgroundColor: '#28283D',
              borderRadius: 8,
              padding: 10,
            }}>
            <Image source={givePng} style={{height: 40, width: 40, marginRight: 10}} />

            <View style={{flexDirection: 'column', paddingRight: 30}}>
              <TextBold style={{fontSize: 17, marginBottom: 6}}>
                {'Your LOAN Documents are pending signature.'}
              </TextBold>
              <TextReg
                style={{
                  marginBottom: 6,
                  paddingRight: 30,
                }}>{`Please check your email inbox for your loan documents to be signed electronically to complete the next step.`}</TextReg>
              <TouchableOpacity onPress={() => goSupport()} style={{flexDirection: 'column'}}>
                <TextReg style={{flexWrap: 'wrap'}}>{`Have a question in the meantime?`}</TextReg>
                <TextReg
                  style={{
                    color: '#00FFBD',
                    textDecorationLine: 'underline',
                    textDecorationStyle: 'solid',
                    textDecorationColor: '#00FFBD',
                    marginTop: 4,
                    flexWrap: 'wrap',
                  }}>{`Contact Support`}</TextReg>
              </TouchableOpacity>
            </View>
          </View>
        </>
      )
    }

    if (status == 'awaiting_collateral') {
      loanInfo = (
        <>
          <View
            style={{
              flexDirection: 'row',
              marginTop: 6,
              backgroundColor: '#28283D',
              borderRadius: 8,
              padding: 10,
            }}>
            <Image source={givePng} style={{height: 40, width: 40, marginRight: 10}} />

            <View style={{flexDirection: 'column', paddingRight: 40}}>
              <TextBold style={{fontSize: 17, marginBottom: 6}}>
                {'Your LOAN account is awaiting your collateral deposit.'}
              </TextBold>
              <TextReg
                style={{
                  marginBottom: 6,
                  paddingRight: 30,
                }}>{`Please deposit the collateral required to move onto the next step.`}</TextReg>
            </View>
          </View>
        </>
      )
    }

    if (status == 'in_progress') {
      loanInfo = (
        <>
          <View
            style={{
              flexDirection: 'row',
              marginTop: 6,
              backgroundColor: '#28283D',
              borderRadius: 8,
              padding: 10,
            }}>
            <Image source={givePng} style={{height: 40, width: 40, marginRight: 10}} />

            <View style={{flexDirection: 'column', paddingRight: 30}}>
              <TextBold style={{fontSize: 17, marginBottom: 6}}>
                {'Your LOAN application is in progress.'}
              </TextBold>
              <TextReg
                style={{
                  marginBottom: 6,
                  paddingRight: 30,
                }}>{`Please click here to continue to your application.`}</TextReg>
              <TextBold style={{color: '#00FFBD', textDecorationLine: 'underline'}}>
                {`Complete Now`}
              </TextBold>
            </View>
          </View>
        </>
      )
    }

    if (status == 'awaiting_funding') {
      loanInfo = (
        <>
          <View style={{flexDirection: 'column', marginTop: 6, paddingRight: 20}}>
            <TextBold style={{fontSize: 17, marginBottom: 6}}>
              {'Your LOAN account is awaiting funding.'}
            </TextBold>
            <TextReg
              style={{
                marginBottom: 6,
              }}>{`Please allow up to 48 business hours for your loan to be funded.`}</TextReg>
            <TouchableOpacity onPress={() => goSupport()} style={{flexDirection: 'column'}}>
              <TextReg style={{flexWrap: 'wrap'}}>{`Have a question in the meantime?`}</TextReg>
              <TextReg
                style={{
                  color: '#00FFBD',
                  textDecorationLine: 'underline',
                  textDecorationStyle: 'solid',
                  textDecorationColor: '#00FFBD',
                  marginTop: 4,
                  flexWrap: 'wrap',
                }}>{`Contact Support`}</TextReg>
            </TouchableOpacity>
          </View>
        </>
      )
    }

    if (!status || status == 'cancelled') {
      loanInfo = (
        <>
          <View
            style={{
              flexDirection: 'row',
              marginTop: 6,
              backgroundColor: '#28283D',
              borderRadius: 8,
              padding: 10,
            }}>
            <Image source={givePng} style={{height: 40, width: 40, marginRight: 10}} />

            <View style={{flexDirection: 'column', paddingRight: 30}}>
              <TextBold style={{fontSize: 17, marginBottom: 6}}>{'Submit a Loan Request'}</TextBold>
              <TouchableOpacity onPress={() => goSupport()} style={{flexDirection: 'column'}}>
                <TextReg style={{flexWrap: 'wrap'}}>{`Have a question in the meantime?`}</TextReg>
                <TextReg
                  style={{
                    color: '#00FFBD',
                    textDecorationLine: 'underline',
                    textDecorationStyle: 'solid',
                    textDecorationColor: '#00FFBD',
                    marginTop: 4,
                    flexWrap: 'wrap',
                  }}>{`Contact Support`}</TextReg>
              </TouchableOpacity>
            </View>
          </View>
        </>
      )
    }

    let description = 'Business Loan'
    if (a?.a?.account?.type == 'personal') {
      description = 'Personal Loan'
    }

    const showIndicator = a?.a?.account?.refinanceLoan?.showIndicator

    return (
      <TouchableOpacity
        style={{...local.innerBox, borderColor: showIndicator ? '#00FFBD' : borderColor}}
        key={i}
        onPress={() => goTo(a)}>
        <View
          style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start'}}>
          <View style={{flexDirection: 'column'}}>
            <TextReg
              style={{
                fontSize: 17,
                maxWidth: 250,
                overflow: 'hidden',
              }}>{`${a.a?.account.name}`}</TextReg>
            <TextReg style={{fontSize: 15, opacity: 0.8}}>{description}</TextReg>
          </View>
          {!isActive && (
            <View style={{zIndex: 40}}>
              <TouchableOpacity
                onPress={() => {
                  deleteFlow(a)
                }}
                style={{
                  flexDirection: 'row',
                  paddingLeft: 2,
                }}>
                <Image source={trashRed} style={{height: 20, width: 16, marginRight: 6}} />
              </TouchableOpacity>
            </View>
          )}
        </View>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginTop: 6,
            zIndex: 5,
          }}>
          {showWallets}
        </View>
        <View
          style={{
            alignSelf: 'stretch',
            height: 2,
            backgroundColor: '#5c5c6f',
            marginLeft: -10,
            marginRight: -10,
            marginTop: 8,
          }}
        />

        {loanInfo}
        {refinanceLoanInfo}
      </TouchableOpacity>
    )
  })

  let totalDif = totalAssets - totalLib
  totalDif = numberWithCommas(totalDif?.toFixed(2))

  totalAssets = numberWithCommas(totalAssets?.toFixed(2))
  totalLib = numberWithCommas(totalLib?.toFixed(2))

  let newAcc = true
  //if accounts already >8 (1 personal, 7 business,)
  //then dont show newAcc option

  let newLoan = () => {
    const allActiveAccountsWithLoans =
      user?.accounts?.filter(
        a =>
          !a?.refinanceLoan &&
          a.loans?.length > 0 &&
          a.loans[0]?.status === 'active' &&
          !a?.product?.loan?.pendingLoanId &&
          Number(a.loans[0]?.amortizationInfo?.currentBalance) !== 0,
      ) || null
    if (allActiveAccountsWithLoans?.length > 0) {
      navigation.navigate('RefinanceOptions', {leveraged: true})
    } else {
      navigation.navigate('CreateAccount', {leveraged: true})
    }
  }

  let toggleArea = () => {
    if (arr?.length < 1) {
      newLoan()
      return
    }
    setShow(!show)
  }

  return (
    <View style={local.box}>
      {hasRefinanceRequest && (
        <View
          style={{
            width: 12,
            height: 12,
            backgroundColor: '#00FFBD',
            borderRadius: 6,
            position: 'absolute',
            right: -1,
            top: -1,
          }}
        />
      )}
      <TouchableOpacity
        onPress={() => toggleArea()}
        activeOpacity={1}
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignSelf: 'stretch',
          alignItems: 'center',
        }}>
        <View style={{flexDirection: 'row'}}>
          <View style={local.imgBox}>
            <Image source={givePng} style={{height: 24, width: 24}} />
          </View>
          <View style={{flexDirection: 'column'}}>
            <TextReg>{`BORROW`}</TextReg>
            <TextBold style={{fontSize: 20}}>{`$${totalDif}`}</TextBold>
          </View>
        </View>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {newAcc && (
            <TouchableOpacity onPress={() => newLoan()}>
              <Image
                source={plusWhite}
                style={{height: 18, width: 18, marginRight: arr?.length > 0 ? 30 : 8}}
              />
            </TouchableOpacity>
          )}
          {arr?.length > 0 && (
            <Image
              source={show ? carrotUp : carrotDown}
              style={{height: 22, width: 22, marginRight: 8}}
            />
          )}
        </View>
      </TouchableOpacity>

      {show && arr?.length > 0 && (
        <View>
          <View style={{alignSelf: 'stretch', flexDirection: 'row', margin: 10, marginTop: 20}}>
            <View style={{flexDirection: 'row', flex: 1}}>
              <Image source={refreshCoins} style={{height: 20, width: 20, marginRight: 3}} />
              <TextBold style={{fontSize: 18, color: '#00A59C'}}>{`+${totalAssets}`}</TextBold>
            </View>
            <View style={{flexDirection: 'row', flex: 1}}>
              <Image source={refreshBag} style={{height: 20, width: 20, marginRight: 2}} />
              <TextBold style={{fontSize: 18, color: '#E5705A'}}>{`-$${totalLib}`}</TextBold>
            </View>
          </View>
          {innerBoxes}
        </View>
      )}
    </View>
  )
}

let local = {
  box: {
    display: 'flex',
    marginLeft: 12,
    marginRight: 12,
    borderRadius: 14,
    backgroundColor: '#3D3D50',
    alignSelf: 'stretch',
    padding: 14,
    marginTop: 12,
  },
  imgBox: {
    backgroundColor: '#28283D',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    marginRight: 10,
  },
  innerBox: {
    backgroundColor: '#505061',
    borderRadius: 14,
    padding: 12,
    alignSelf: 'stretch',
    marginTop: 10,
    zIndex: 20,
    borderWidth: 1,
  },
  tokenBox: {
    backgroundColor: '#000',
    borderRadius: 20,
    height: 40,
    width: 40,
    marginRight: -10,
    borderWidth: 3,
    borderColor: '#505061',
    alignItems: 'center',
    justifyContent: 'center',
  },
  distBar: {
    height: 8,
    borderRadius: 4,
    alignSelf: 'stretch',
    marginRight: 70,
    backgroundColor: '#F90',
    marginTop: 10,
    overflow: 'hidden',
    marginBottom: 20,
  },
  deposit: {
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#00FFBD',
    borderRadius: 4,
    height: 50,
    marginBottom: 10,
  },
  dots3: {
    height: 40,
    width: 40,
    alignItems: 'flex-end',
  },
  dotsImg: {
    height: 30,
    width: 30,
  },
  dotsImgActive: {
    height: 30,
    width: 30,
    backgroundColor: '#3D3D50',
    borderColor: '#888',
    borderWidth: 1,
    borderRadius: 4,
  },
  handImage: {tintColor: '#FFF', height: 33, width: 33},
  iconContainer: {
    position: 'absolute',
    left: 20,
    top: -10,
    backgroundColor: '#5A33E3',
    borderRadius: 14.5,
    width: 19,
    height: 19,
    borderWidth: 1.3,
    borderColor: '#FFF',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  iconImage: {tintColor: '#FFF', height: 11, width: 11},
}

export default BorrowBox
