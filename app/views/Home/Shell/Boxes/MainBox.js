import React, {useState, useEffect} from 'react'
import {View, TouchableOpacity, Image} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import {useNavigation} from '@react-navigation/native'

import {Card, TextReg, TextBold, DistBar} from '../../../../components'
import custodyAccount from '../../../../imgs/graphics/custodyAccount.png'
import dots3 from '../../../../imgs/dots3.png'
import walletCoins from '../../../../imgs/graphics/walletCoins.png'

import {numberWithCommas} from '../../../../util/helpers'
import {showSlide} from '../../../../store/user/user.actions'
import {getTokenPic} from '../../../../util/tokens'

const MainBox = ({arr, goToProduct, showMainOpen, user, launchDarkly}) => {
  let [show, setShow] = useState(
    user?.primaryEmail == '<EMAIL>' ||
      (arr?.a?.wallets?.length < 1 && showMainOpen),
  )

  let dispatch = useDispatch()
  const navigation = useNavigation()

  let goToDelete = () => {
    navigation.navigate('DeleteAcc', {id: 123})
  }

  let wallets = arr?.a?.wallets
  let sumValue = numberWithCommas(Number(arr?.sumValue?.replace(/,/g, '')).toFixed(2))

  if (user?.primaryEmail == '<EMAIL>') {
    sumValue = '34,552.02'
  }

  let disableDeposit = launchDarkly?.['disable-deposit'] || false

  let showWallets = wallets?.map((a, k) => {
    let tokenName = a.currency
    if (tokenName == 'XRP') {
      tokenName = 'XRPW'
    }
    let pic = getTokenPic(tokenName)
    return (
      <View style={{...local.tokenBox, zIndex: 30 - k}}>
        <Image source={pic} style={{height: 23, width: 23}} />
      </View>
    )
  })

  let deposit = () => {
    dispatch(
      showSlide({
        action: 'deposit',
        chosenRef: arr?.a?.account?.ref,
      }),
    )
  }

  let walletsEnabled = user?.walletsEnabled || false

  return (
    <View style={local.box}>
      <TouchableOpacity onPress={() => setShow(!show)} style={{flexDirection: 'row', flex: 1}}>
        <View style={local.imgBox}>
          <Image source={custodyAccount} style={{height: 22, width: 22}} />
        </View>
        <View style={{flexDirection: 'column'}}>
          <TextReg>{`WALLETS`}</TextReg>
          <TextBold style={{fontSize: 20}}>{`$${sumValue}`}</TextBold>
        </View>
      </TouchableOpacity>
      {show && (
        <View style={local.innerBox}>
          <TouchableOpacity
            onPress={() =>
              goToProduct(
                {id: arr?.a?.account?.id, ref: arr?.a?.account?.ref},
                arr?.accountType,
                arr?.a?.account,
              )
            }>
            <View
              style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'}}>
              <View style={{flexDirection: 'column'}}>
                <TextReg style={{fontSize: 17}}>{`Main Wallet`}</TextReg>
                <TextReg style={{fontSize: 15, opacity: 0.8}}>{`Personal Wallet`}</TextReg>
              </View>
            </View>
            {wallets?.length > 0 && (
              <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 6}}>
                {showWallets}
              </View>
            )}
          </TouchableOpacity>

          <View
            style={{
              alignSelf: 'stretch',
              height: 2,
              backgroundColor: '#5c5c6f',
              marginLeft: -10,
              marginRight: -10,
              marginTop: 8,
            }}
          />
          {arr?.sumValue == 0 ? (
            <View
              style={{
                alignSelf: 'stretch',
                alignItems: 'center',
                flexDirection: 'column',
                marginTop: 10,
                marginBottom: 10,
              }}>
              <Image source={walletCoins} style={{height: 60, width: 200}} />
              <TextBold style={{marginTop: 12, marginBottom: 6, fontSize: 18}}>
                {'Your wallet is empty.'}
              </TextBold>
              {walletsEnabled ? (
                <TextReg>{'Deposit some crypto to your wallet account!'}</TextReg>
              ) : (
                <View style={{flexDirection: 'column', alignItems: 'center', alignSelf: 'stretch'}}>
                  <TextReg style={{textAlign: 'center', marginTop: 4, color: '#e35d45'}}>
                    {'Wallets are not enabled yet please finish user verification flow.'}
                  </TextReg>
                  <TouchableOpacity
                    onPress={() =>
                      goToProduct(
                        {id: arr?.a?.account?.id, ref: arr?.a?.account?.ref},
                        arr?.accountType,
                        arr?.a?.account,
                      )
                    }
                    style={local.startV}>
                    <TextReg style={{color: '#00FFBD', fontSize: 20}}>{`START`}</TextReg>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          ) : (
            <>
              <TextReg style={{fontSize: 17, opacity: 0.6, marginTop: 8}}>{`Balance`}</TextReg>
              <TextBold style={{fontSize: 17, opacity: 1, marginTop: 2}}>{`$${sumValue}`}</TextBold>
            </>
          )}
          {arr?.sumValue != 0 && <DistBar wallets={wallets} />}
          {walletsEnabled && !disableDeposit && (
            <TouchableOpacity onPress={() => deposit()} style={local.deposit}>
              <TextReg style={{color: '#00FFBD', fontSize: 20}}>{`DEPOSIT`}</TextReg>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  )
}

let local = {
  box: {
    display: 'flex',
    marginLeft: 12,
    marginRight: 12,
    borderRadius: 14,
    backgroundColor: '#3D3D50',
    alignSelf: 'stretch',
    padding: 14,
  },
  imgBox: {
    backgroundColor: '#28283D',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    marginRight: 10,
  },
  innerBox: {
    backgroundColor: '#505061',
    borderRadius: 14,
    padding: 14,
    alignSelf: 'stretch',
    marginTop: 10,
  },
  tokenBox: {
    backgroundColor: '#000',
    borderRadius: 20,
    height: 40,
    width: 40,
    marginRight: -10,
    borderWidth: 3,
    borderColor: '#505061',
    justifyContent: 'center',
    alignItems: 'center',
  },
  distBar: {
    height: 8,
    borderRadius: 4,
    alignSelf: 'stretch',
    marginRight: 70,
    backgroundColor: '#F90',
    marginTop: 10,
    overflow: 'hidden',
    marginBottom: 10,
  },
  deposit: {
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#00FFBD',
    borderRadius: 4,
    height: 50,
    marginBottom: 10,
    marginTop: 10,
  },
  startV: {
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#00FFBD',
    borderRadius: 4,
    height: 50,
    marginBottom: 10,
    marginTop: 10,
    width: 300,
  },
}

export default MainBox
