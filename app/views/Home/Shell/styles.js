import {StyleSheet, Dimensions, PixelRatio} from 'react-native';

const {width, height} = Dimensions.get('window');

const styles = StyleSheet.create({
  accountListImg: {
    height: 32,
    width: 32,
    marginLeft: -1,
  },
  accountListImgExchange: {
    width: 24,
    height: 24,
    marginLeft: 1,
  },
  accountListImgBox: {
    height: 48,
    width: 48,
    borderRadius: 14,
    backgroundColor: '#28283D',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 2,
  },
  accountListBox: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignSelf: 'stretch',
  },
});

export default styles;
