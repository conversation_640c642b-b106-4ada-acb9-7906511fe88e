import {log} from '@grpc/grpc-js/build/src/logging'
import React from 'react'
import {View, StyleSheet} from 'react-native'
import Animated, {useAnimatedStyle, useDerivedValue, withSpring} from 'react-native-reanimated'
import {getYForX} from 'react-native-redash'

const CURSOR = 30

const Dot = ({xAxis, graphWidth, dataPath, color = '#00FFBD', withVerticalLine, graphHeight}) => {
  const style = useAnimatedStyle(() => {
    const translateX = xAxis.x.value - CURSOR / 2
    let yPosition = getYForX(dataPath, xAxis.x.value) || 0
    const translateY = yPosition - CURSOR / 2
    return {
      transform: [{translateX}, {translateY}, {scale: withSpring(true ? 1 : 0)}],
    }
  })

  const lineStyle = useAnimatedStyle(() => {
    const translateX = xAxis.x.value - 1
    return {
      height: graphHeight ? graphHeight : 0,
      transform: [{translateX}, {scale: withSpring(true ? 1 : 0)}],
    }
  })

  return (
    <View style={StyleSheet.absoluteFill}>
      {withVerticalLine && (
        <Animated.View style={[styles.verticalLine, lineStyle]}>
          {/* This empty view spans the height of the graph */}
        </Animated.View>
      )}
      <Animated.View style={[styles.cursor, style]}>
        <View style={[styles.cursorBody, {backgroundColor: color}]} />
      </Animated.View>
    </View>
  )
}

const styles = StyleSheet.create({
  cursor: {
    width: CURSOR,
    height: CURSOR,
    borderRadius: CURSOR / 2,
    backgroundColor: 'rgba(0, 255, 189, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cursorBody: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  verticalLine: {
    position: 'absolute',
    width: 1, // Adjust thickness of the line
    backgroundColor: '#6991E6', // Color of the vertical line
  },
})

export default Dot
