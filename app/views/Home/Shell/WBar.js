import React, {useState, useEffect} from 'react'
import {View, TouchableOpacity} from 'react-native'
import Svg, {Path, Line, Text} from 'react-native-svg'
import Animated, {useSharedValue, useAnimatedProps, withTiming} from 'react-native-reanimated'
import {Gesture, GestureDetector} from 'react-native-gesture-handler'
import {useVector} from 'react-native-redash'
import {graphs} from './Model'
import Dot from './Dot'
import moment from 'moment'

import Header from './Header'
import {TextReg} from '../../../components'
import {log} from '@grpc/grpc-js/build/src/logging'

const AnimatedPath = Animated.createAnimatedComponent(Path)

const WBar = ({
  graphWidth,
  prices30: initialPrices30,
  prices30Variant1: initialPrices30Variant1,
  prices30Variant2: initialPrices30Variant2,
  datesArr,
  graphLoaded,
}) => {
  const [prices30, setPrices30] = useState({
    ...initialPrices30,
    prices: initialPrices30.prices.slice(-365),
  })

  const [prices30Variant1, setPrices30Variant1] = useState({
    ...initialPrices30Variant1,
    prices: initialPrices30Variant1.prices.slice(-365),
  })
  const [prices30Variant2, setPrices30Variant2] = useState({
    ...initialPrices30Variant2,
    prices: initialPrices30Variant2.prices.slice(-365),
  })

  //console.log('prices30', prices30) // exchange
  //console.log('prices30Variant1', prices30Variant1) // loan
  //console.log('prices30Variant2', prices30Variant2) // lend

  let pricesTotal = prices30.prices.map((price, index) => {
    let total = price[0] + prices30Variant1.prices[index][0] + prices30Variant2.prices[index][0]
    return [total, price[1]]
  })
  let prices30Total = {
    percent_change: 0,
    prices: pricesTotal,
  }

  let maxTotal = Math.max(...pricesTotal.map(price => price[0])) || 0

  let lengthDays = initialPrices30.prices.length || 0

  let initialSpan = 'y'
  if (lengthDays <= 31) {
    initialSpan = 'm'
  }
  if (lengthDays <= 7) {
    initialSpan = 'w'
  }
  if (lengthDays <= 3) {
    initialSpan = 'd'
  }

  let [span, setSpan] = useState(initialSpan)
  const [graphNum, setGraphNum] = useState(1)
  const shareX = useVector(graphWidth)

  const area1 = graphs(graphWidth, prices30, 1, maxTotal)[0]?.data.area
  const dataPath1 = graphs(graphWidth, prices30, false, maxTotal)[0].data.path
  let dataObj1 = graphs(graphWidth, prices30, false, maxTotal)[0].data

  const area2 = graphs(graphWidth, prices30Variant1, 1, maxTotal)[0]?.data.area
  const dataPath2 = graphs(graphWidth, prices30Variant1, false, maxTotal)[0].data.path
  let dataObj2 = graphs(graphWidth, prices30Variant1, false, maxTotal)[0].data

  const area3 = graphs(graphWidth, prices30Variant2, 1, maxTotal)[0]?.data.area
  const dataPath3 = graphs(graphWidth, prices30Variant2, false, maxTotal)[0].data.path
  let dataObj3 = graphs(graphWidth, prices30Variant2, false, maxTotal)[0].data

  const areaTotal = graphs(graphWidth, prices30Total, 1, maxTotal)[0]?.data.area
  const dataPathTotal = graphs(graphWidth, prices30Total, false, maxTotal)[0].data.path
  let dataObjTotal = graphs(graphWidth, prices30Total, false, maxTotal)[0].data

  const gesturePan = Gesture.Pan().onUpdate(event => {
    shareX.x.value = event.x
    if (shareX.x.value < 0) {
      shareX.x.value = 0
    }
    if (shareX.x.value > graphWidth) {
      shareX.x.value = graphWidth
    }
  })
  let maxVerticalLines = 12
  if (span == 'm') {
    maxVerticalLines = 9
  }
  if (span == 'w') {
    maxVerticalLines = 6
  }

  const totalPrices = prices30?.prices?.length || 0
  const step = Math.max(1, Math.floor(totalPrices / (maxVerticalLines - 1)))
  const verticalLines = Array.from({length: totalPrices}).map((_, index) => {
    let showDate = ''
    if (prices30?.prices[index]) {
      let date = new Date(prices30.prices[index][1])

      if (span == 'y') {
        date.setDate(date.getDate() - 0)
      }
      if (span == 'w' || span == 'm') {
        date.setDate(date.getDate() + 0)
      }
      showDate = `${date.getUTCMonth() + 1}/${date.getUTCDate()}`
    }

    if (index % step === 0 || index === 0 || index === totalPrices - 1) {
      const xPosition =
        index === 0
          ? 0
          : index === totalPrices - 1
          ? graphWidth
          : (graphWidth / (totalPrices - 1)) * index // Distribute lines evenly
      return (
        <React.Fragment key={`v-line-${index}`}>
          <Line
            x1={xPosition}
            y1={0}
            x2={xPosition}
            y2={150}
            stroke="rgba(255, 255, 255, 0.1)"
            strokeWidth="1"
          />
          <Text
            x={xPosition}
            y={160} // Position slightly below the graph
            fill="#FFF"
            opacity="0.5"
            fontSize="8"
            //textAnchor="center" // Center the text // crashses android
            alignmentBaseline="hanging" // Adjust text baseline
          >
            {span == 'y' && index === step * 12 ? '' : showDate}
          </Text>
        </React.Fragment>
      )
    }
    return null
  })

  //update max/min for d,w,m,y
  let yNums = []
  for (let i = 1; i <= 4; i++) {
    yNums.push((maxTotal * (i / 4)).toFixed(2))
  }

  let yValues = [150] //[150, 100, 50, 6]

  const horizontalLines = yValues.map((yValue, index) => {
    let showY = yNums[index]
    if (index === 0) {
      showY = ''
    }
    return (
      <React.Fragment key={`h-line-${index}`}>
        <Line
          x1={0}
          y1={yValue}
          x2={graphWidth}
          y2={yValue}
          stroke="rgba(255, 255, 255, 0.1)"
          strokeWidth="1"
          //strokeDasharray="4" // This makes the line dashed
        />
        <Text
          x={0} // Align text to the left of the graph
          y={yValue}
          fill="#FFF"
          opacity="0"
          fontSize="10"
          textAnchor="start" // Align text to the start
          alignmentBaseline="middle" // Center the text vertically
        >
          {index == 0 ? '' : `$${showY}`}
        </Text>
      </React.Fragment>
    )
  })

  const horizontalLines2 = yValues.map((yValue, index) => {
    let showY = yNums[index]
    if (index === 0) {
      showY = ''
    }

    let marginTop = 37
    if (index == 2) {
      marginTop = 29
    }
    if (index == 3) {
      marginTop = 0
    }
    return (
      <View
        style={{
          marginTop: -3,
          backgroundColor: index == 0 ? 'transparent' : '#222',
          border: `1px solid #fff`,
          borderRadius: 4,
          marginTop: marginTop,
          borderWidth: index == 0 ? 0 : 0.5,
          borderRadius: 4,
          borderColor: '#888',
        }}>
        <TextReg style={{color: '#FFF', opacity: 1, fontSize: 10}}>
          {index == 0 ? '' : `$${showY}`}
        </TextReg>
      </View>
    )
  })

  const gestureTap = Gesture.Tap().onTouchesDown(event => {
    'worklet'
    const xCo = event.allTouches[0]?.x
    shareX.x.value = xCo
    if (shareX.x.value < 0) {
      shareX.x.value = 0
    }
    if (shareX.x.value > graphWidth) {
      shareX.x.value = graphWidth
    }
  })

  let chop = span => {
    if (span === 'm') {
      let prices30Last30 = initialPrices30.prices.slice(-31)
      setPrices30({...initialPrices30, prices: prices30Last30})
      let prices30Variant1Last30 = initialPrices30Variant1.prices.slice(-31)
      setPrices30Variant1({...initialPrices30Variant1, prices: prices30Variant1Last30})
      let prices30Variant2Last30 = initialPrices30Variant2.prices.slice(-31)
      setPrices30Variant2({...initialPrices30Variant2, prices: prices30Variant2Last30})
    }
    if (span === 'w') {
      let prices30Last7 = initialPrices30.prices.slice(-7)
      setPrices30({...initialPrices30, prices: prices30Last7})
      let prices30Variant1Last7 = initialPrices30Variant1.prices.slice(-7)
      setPrices30Variant1({...initialPrices30Variant1, prices: prices30Variant1Last7})
      let prices30Variant2Last7 = initialPrices30Variant2.prices.slice(-7)
      setPrices30Variant2({...initialPrices30Variant2, prices: prices30Variant2Last7})
    }
    if (span === 'y') {
      setPrices30({...prices30, prices: initialPrices30.prices})
      setPrices30Variant1({...prices30Variant1, prices: initialPrices30Variant1.prices})
      setPrices30Variant2({...prices30Variant2, prices: initialPrices30Variant2.prices})
    }

    setSpan(span)
  }

  if (span === 'y' || (span === 'm' && lengthDays <= 31)) {
    datesArr = datesArr.map(date => {
      const dateObj = moment(date, 'MMM D, YYYY')
      dateObj.subtract(0, 'days')
      return dateObj.format('MMM D, YYYY')
    })
  } else {
    datesArr = datesArr.map(date => {
      const dateObj = moment(date, 'MMM D, YYYY')
      return dateObj.format('MMM D, YYYY')
    })
  }

  let nextDay = moment().format('MMM D, YYYY') //today

  const today = new Date().toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  })

  if (datesArr[datesArr.length - 1] === today) {
    datesArr.pop()
  }

  return (
    <>
      <View
        style={{flex: 1, marginTop: 20}}
        onLayout={event => {
          const {width} = event.nativeEvent.layout
          graphLoaded(width)
        }}>
        <GestureDetector gesture={gestureTap}>
          <GestureDetector gesture={gesturePan}>
            <View style={{height: 150, width: graphWidth}}>
              {true && (
                <View style={{flexDirection: 'column'}}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginTop: -10,
                    }}>
                    {/* moving dates */}
                    <Header
                      translation={shareX}
                      graphWidth={graphWidth}
                      prices={prices30Total}
                      datesArr={datesArr}
                      dataObj={dataObjTotal}
                      showDates={true}
                      span={span}
                      nextDay={nextDay}
                    />
                    <View style={{flexDirection: 'row', alignItems: 'center', marginTop: -10}}>
                      <View
                        style={{
                          height: 10,
                          width: 10,
                          borderRadius: 5,
                          backgroundColor: '#fff',
                          marginRight: 5,
                        }}
                      />
                      <TextReg style={{fontSize: 16}}>Net Amount</TextReg>
                    </View>
                    <Header
                      translation={shareX}
                      graphWidth={graphWidth}
                      prices={prices30Total}
                      datesArr={datesArr}
                      dataObj={dataObjTotal}
                    />
                  </View>
                  <View
                    style={{
                      alignSelf: 'stretch',
                      backgroundColor: '#888',
                      height: 1,
                      marginLeft: -16,
                      marginRight: -16,
                      marginTop: 0,
                      marginBottom: 6,
                    }}
                  />
                  <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                    <View style={{flexDirection: 'row', alignItems: 'center', marginTop: -10}}>
                      <View
                        style={{
                          height: 10,
                          width: 10,
                          borderRadius: 5,
                          backgroundColor: '#FF9900',
                          marginRight: 5,
                        }}
                      />
                      <TextReg style={{fontSize: 14}}>BORROW Accounts</TextReg>
                    </View>
                    <Header
                      translation={shareX}
                      graphWidth={graphWidth}
                      prices={prices30Variant1}
                      datesArr={datesArr}
                      dataObj={dataObj2}
                    />
                  </View>
                  <View
                    style={{
                      alignSelf: 'stretch',
                      backgroundColor: '#888',
                      height: 1,
                      marginLeft: -16,
                      marginRight: -16,
                      marginTop: 0,
                      marginBottom: 6,
                    }}
                  />
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: 8,
                    }}>
                    <View style={{flexDirection: 'row', alignItems: 'center', marginTop: -10}}>
                      <View
                        style={{
                          height: 10,
                          width: 10,
                          borderRadius: 5,
                          backgroundColor: '#00FFBD',
                          marginRight: 5,
                        }}
                      />
                      <TextReg style={{fontSize: 14}}>LEND Accounts</TextReg>
                    </View>
                    <Header
                      translation={shareX}
                      graphWidth={graphWidth}
                      prices={prices30Variant2}
                      datesArr={datesArr}
                      dataObj={dataObj3}
                    />
                  </View>
                </View>
              )}
              <View
                style={{
                  position: 'absolute',
                  flexDirection: 'column-reverse',
                  left: 0,
                  top: 102,
                  opacity: 1,
                  zIndex: 100,
                  pointerEvents: 'none',
                }}>
                {false && horizontalLines2}
              </View>
              <View>
                <Svg width={graphWidth} height={180}>
                  {false && verticalLines}
                  {true && horizontalLines}
                  <Path
                    d={areaTotal}
                    fill="transparent"
                    stroke="#FFFFFF"
                    strokeWidth={2}
                    opacity={0.7}
                  />
                  <Path
                    d={area2}
                    fill="transparent"
                    stroke="#FF9900"
                    strokeWidth={2}
                    opacity={0.6}
                  />
                  <Path
                    d={area3}
                    fill="transparent"
                    stroke="#00FFBD"
                    strokeWidth={2}
                    opacity={0.9}
                  />
                </Svg>
                <Dot
                  xAxis={shareX}
                  graphWidth={graphWidth}
                  dataPath={dataPathTotal}
                  color={'#FFF'}
                  withVerticalLine={true}
                  graphHeight={150}
                />
                <Dot xAxis={shareX} graphWidth={graphWidth} dataPath={dataPath2} color={'#F90'} />
                <Dot
                  xAxis={shareX}
                  graphWidth={graphWidth}
                  dataPath={dataPath3}
                  color={'#00FFBD'}
                />
              </View>
            </View>
          </GestureDetector>
        </GestureDetector>
      </View>

      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginTop: 290,
          justifyContent: 'flex-end',
        }}>
        {false && (
          <TouchableOpacity
            onPress={() => chop('d')}
            style={span == 'd' ? local.toggleChartActive : local.toggleChart}>
            <TextReg style={{color: span == 'd' ? '#000' : '#fff'}}>{`1D`}</TextReg>
          </TouchableOpacity>
        )}
        <TouchableOpacity
          onPress={() => chop('w')}
          style={span == 'w' ? local.toggleChartActive : local.toggleChart}>
          <TextReg style={{color: span == 'w' ? '#000' : '#fff'}}>{`1W`}</TextReg>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => chop('m')}
          style={span == 'm' ? local.toggleChartActive : local.toggleChart}>
          <TextReg style={{color: span == 'm' ? '#000' : '#fff'}}>{`1M`}</TextReg>
        </TouchableOpacity>
        {lengthDays > 30 && (
          <TouchableOpacity
            onPress={() => chop('y')}
            style={span == 'y' ? local.toggleChartActive : local.toggleChart}>
            <TextReg style={{color: span == 'y' ? '#000' : '#fff'}}>{`1Y`}</TextReg>
          </TouchableOpacity>
        )}
      </View>
    </>
  )
}

let local = {
  toggleChart: {
    height: 33,
    width: 33,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: '#888',
    borderWidth: 1,
    borderRadius: 4,
    marginRight: 5,
  },
  toggleChartActive: {
    height: 33,
    width: 33,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: '#888',
    borderWidth: 1,
    borderRadius: 4,
    marginRight: 5,
    backgroundColor: '#9075ec',
  },
}

export default WBar
