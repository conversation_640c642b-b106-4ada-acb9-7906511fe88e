import React, {useState, useEffect} from 'react'
import {View, TouchableOpacity, Image, Dimensions} from 'react-native'
import LinearGradient from 'react-native-linear-gradient'
import {useNavigation} from '@react-navigation/native'
import {useSelector} from 'react-redux'
import {Card, TextReg, TextBold, Button} from '../../../components'

import img1 from '../../../imgs/graphics/marketDiscover1.png'
import img2 from '../../../imgs/graphics/marketDiscover2.png'
import img3 from '../../../imgs/graphics/marketDiscover3.png'
import {log} from '@grpc/grpc-js/build/src/logging'

const MarketplaceDiscover = ({goToMarketplace, adminApr, adminApy, user}) => {
  const navigation = useNavigation()
  let WebService = useSelector(state => state.auth.WebService)
  let [entities, setEntities] = useState([])
  let [isAccredited, setIsAccredited] = useState(false)

  useEffect(() => {
    getEntities()
  }, [])

  let getEntities = async () => {
    WebService.getEntitys()
      .then(res => {
        let isAccredited = false
        let arr = res.data?.filter(a => a.isEntityAccredited)
        if (arr?.length >= 1) {
          isAccredited = true
        }
        setEntities(res.data)
        setIsAccredited(isAccredited)
      })
      .catch(err => {
        console.log('getEntities err', err)
      })
  }

  let showApr = (adminApr * 100).toFixed(2)

  goReferrals = () => {
    navigation.navigate('Referrals')
  }

  goLoans = () => {
    const allActiveAccountsWithLoans =
      user?.accounts?.filter(
        a =>
          !a?.refinanceLoan &&
          a?.loans?.length > 0 &&
          a?.loans[0]?.status === 'active' &&
          !a?.product?.loan?.pendingLoanId &&
          Number(a?.loans[0]?.amortizationInfo?.currentBalance) !== 0,
      ) || null
    if (allActiveAccountsWithLoans?.length > 0) {
      navigation.navigate('RefinanceOptions', {leveraged: true})
    } else {
      navigation.navigate('CreateAccount', {leveraged: true})
    }
  }

  goLend = () => {
    if (!isAccredited && !user?.isUserAccredited) {
      navigation.navigate('GetAccredited')
    } else {
      navigation.navigate('CreateInvestmentAccount')
    }
  }

  let testAcc = user?.primaryEmail == '<EMAIL>'
  const {width, height} = Dimensions.get('window')

  let size = 110
  if (testAcc && width > 1000) {
    size = 140
  }

  return (
    <View style={{alignItems: 'center', marginTop: testAcc ? 30 : 16}}>
      <TextReg style={{fontSize: 17}}>{`Don't miss these opportunities!`}</TextReg>
      <View
        style={{
          alignSelf: 'stretch',
          marginTop: 10,
          justifyContent: 'center',
          flexDirection: 'row',
        }}>
        <TouchableOpacity
          onPress={() => goReferrals()}
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            height: size,
            width: size,
          }}>
          <TextBold style={{color: '#fff', zIndex: 20}}>{`REFER`}</TextBold>
          <TextReg style={{color: '#fff', zIndex: 20}}>{`Earn $50 BTC`}</TextReg>
          <Image
            source={img1}
            style={{
              height: size + 10,
              width: size + 10,
              position: 'absolute',
            }}></Image>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => goLoans()}
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            height: size,
            width: size,
          }}>
          <TextBold style={{color: '#fff', zIndex: 20}}>{`BORROW`}</TextBold>
          <TextReg style={{color: '#fff', zIndex: 20}}>{`${showApr}% APR`}</TextReg>
          <Image
            source={img2}
            style={{
              height: size + 10,
              width: size + 10,
              position: 'absolute',
            }}></Image>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => goLend()}
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            height: size,
            width: size,
          }}>
          <TextBold style={{color: '#fff', zIndex: 20}}>{`LEND`}</TextBold>
          <TextReg style={{color: '#fff', zIndex: 20}}>{`${adminApy}% APY`}</TextReg>
          <Image
            source={img3}
            style={{
              height: size + 10,
              width: size + 10,
              position: 'absolute',
            }}></Image>
        </TouchableOpacity>
      </View>
    </View>
  )
}
export default MarketplaceDiscover
