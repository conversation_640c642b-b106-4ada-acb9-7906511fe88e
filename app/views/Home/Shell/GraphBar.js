import React from 'react';
import {View} from 'react-native';
import {Gesture, GestureDetector} from 'react-native-gesture-handler';
import Animated, {useSharedValue, useAnimatedStyle, useAnimatedGestureHandler} from 'react-native-reanimated';

let GraphBar = () => {
  console.log('graphBar');

  const xBar = useSharedValue(0);
  const yBar = useSharedValue(0);

  let gesturePan = Gesture.Pan().onUpdate(event => {
    console.log('pan', event.x, event.translationX);
    xBar.value = event.x;
  });
  let gestureTap = Gesture.Tap().onTouchesDown(event => {
    let xCo = event.allTouches[0]?.x;
    let yCo = event.allTouches[0]?.y;
    console.log('tap', xCo, yCo);
    xBar.value = xCo;
    //yBar.value = yCo;
  });

  let barStyle = useAnimatedStyle(() => ({
    transform: [{translateX: xBar.value}, {translateY: yBar.value}],
  }));

  return (
    <GestureDetector gesture={gesturePan}>
      <GestureDetector gesture={gestureTap}>
        <View style={{height: 156, width: 446, zIndex: 20}}>
          <Animated.View style={[{height: 156, width: 2, backgroundColor: 'blue'}, barStyle]} />
        </View>
      </GestureDetector>
    </GestureDetector>
  );
};

export default GraphBar;
