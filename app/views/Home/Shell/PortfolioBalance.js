import React, {useState, useEffect, useRef} from 'react'
import {View, TouchableOpacity, Image, Animated} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import AsyncStorage from '@react-native-async-storage/async-storage'
import {Card, TextReg, TextBold} from '../../../components'
import {numberWithCommas} from '../../../util/helpers'
import LottieView from 'lottie-react-native'

import homeCal from '../../../imgs/graphics/homeCal.png'
import homeGraph from '../../../imgs/graphics/homeGraph.png'
import upGraph from '../../../imgs/graphics/upGraph.png'

import WBar from './WBar'

import {tourCount} from '../../../store/user/user.actions'
import {log} from '@grpc/grpc-js/build/src/logging'

const PortfolioBalance = props => {
  const dispatch = useDispatch()
  let launchDarkly = useSelector(state => state.launchDarkly)
  let [graphWidth, setGraphWidth] = useState(0)
  let [topShow, setTopShow] = useState('graph')
  let [borrowGraph, setBorrowGraph] = useState([])
  let [exchangeGraph, setExchangeGraph] = useState([])
  let [lendGraph, setLendGraph] = useState([])
  const fadeAnim = useRef(new Animated.Value(0)).current

  let calculateTotals = () => {
    let totalBorrow = props.graphArr?.map((a, index, arr) => {
      let borrow = Object.keys(a.borrow).reduce((acc, currency) => {
        const borrowAmount = a.borrow[currency] || 0
        const price = a.prices[currency] || 0
        let result = acc + borrowAmount * price
        if (index === arr.length - 1) {
        }
        return result
      }, 0)
      borrow -= a?.loanValue || 0

      /*
      if (borrow < 0) {
        borrow = 0
      }
        */

      if (index === arr.length - 1) {
        borrow = props.borrowNet
      }

      return [borrow, a.epoch]
    })

    console.log('totalBorrow', totalBorrow)
    console.log('props.totalLend', props.totalLend)

    let totalExhange = props.graphArr?.map((a, index, arr) => {
      let exchange = Object.keys(a.exchange).reduce((acc, currency) => {
        const exchangeAmount = a.exchange[currency] || 0
        const price = a.prices[currency] || 0
        const result = acc + exchangeAmount * price
        return result
      }, 0)

      if (index === arr.length - 1) {
        let lastExchange = Number(props.lastExchange || 0)
        console.log('exchange- last', lastExchange)
        exchange = lastExchange
      }

      return [exchange, a.epoch]
    })

    let totalLend = props.graphArr?.map((a, index, arr) => {
      let lend = Object.keys(a.lend).reduce((acc, currency) => {
        const lendAmount = a.lend[currency] || 0
        const price = a.prices[currency] || 0
        return acc + lendAmount * price
      }, 0)

      if (index === arr.length - 1) {
        console.log('lend- last', props.totalLend)
        lend = props.totalLend
      }

      return [lend, a.epoch]
    })

    setBorrowGraph({prices: totalBorrow.slice(-365), percent_change: 0})
    setExchangeGraph({prices: totalExhange.slice(-365), percent_change: 0})
    setLendGraph({prices: totalLend.slice(-365), percent_change: 0})
  }

  useEffect(() => {
    if (props.graphArr?.length > 0) {
      calculateTotals()
    }
  }, [props.graphArr])

  let shortMonths = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'June',
    'July',
    'Aug',
    'Sept',
    'Oct',
    'Nov',
    'Dec',
  ]

  let datesArr = borrowGraph['prices']?.map(a => {
    let dateObj = new Date(a[1])
    let month = dateObj.getUTCMonth()
    let showMonth = shortMonths[month]
    let day = dateObj.getUTCDate()
    let year = dateObj.getUTCFullYear()
    return `${showMonth} ${day}, ${year}`
  })
  if (datesArr?.length > 365) {
    datesArr = datesArr.slice(-365)
  }

  let balanceNum = Number(props.portfolioBalanceNum?.replace(/,/g, ''))

  let openTour = async () => {
    await AsyncStorage.setItem('HOME_TOUR', 'false')
    dispatch(tourCount())
  }

  let homeQ = launchDarkly['show-home-tour'] || false

  let totalBalance = numberWithCommas(balanceNum.toFixed(2))
  console.log('totalBalance', totalBalance)

  let borrowNet = numberWithCommas(Number(props.borrowNet || 0)?.toFixed(2))
  let netAmt = 0.0 + props.borrowNet //props.borrowNet + props.lendNet
  netAmt = numberWithCommas(netAmt.toFixed(2))

  let earnApy = Number(props.earnApy || 0)
  let borrowApy = Number(props.borrowApy || 0)

  let netApy = (earnApy - borrowApy)?.toFixed(2)
  let netSymb = 'APY'
  let netColor = '#7BEAD7'

  let monthlyNet = props.monthlyIncome - props.monthlyPayments
  let netSign = monthlyNet >= 0 ? '+' : '-'
  monthlyNet = Number(Math.abs(monthlyNet))?.toFixed(2)
  monthlyNet = numberWithCommas(monthlyNet)

  if (netSign == '-') {
    netSymb = 'APR'
    netColor = '#FF9900'
  }

  netApy = Math.abs(netApy)?.toFixed(2)
  netApy = `${netApy}%`

  let lendAmt = numberWithCommas(Number(props.monthlyIncome || 0)?.toFixed(2))

  let title = 'Account Summary'
  if (topShow == 'cal') {
    title = 'Monthly Summary'
  }
  let showMonthlyDate = new Date().toLocaleDateString('en-US', {
    month: 'short',
    year: 'numeric',
  })

  if (props.graphArr?.length < 1) {
    return null
  }
  if (props.under7 && props.hasRun?.current) {
    return (
      <>
        <Card
          style={{alignItems: 'flex-start', padding: 16, height: 84, backgroundColor: '#5A33E3'}}
          cardMarginBottom={12}>
          <TextReg style={{fontSize: 17, marginBottom: 4}}>Portfolio Balance</TextReg>
          <TextBold style={{fontSize: 21, marginBottom: 4}}>{`$${totalBalance} USD`}</TextBold>
        </Card>
        <Card style={{alignItems: 'flex-start', padding: 16, height: 190}} cardMarginBottom={12}>
          <TextBold>{`Account Summary`}</TextBold>
          <View style={{alignSelf: 'stretch', height: 180, alignItems: 'center'}}>
            <Image
              source={upGraph}
              style={{height: 72, width: 140, marginTop: 20, marginBottom: 20}}
            />
            <TextBold style={{fontSize: 17}}>{`Your data will show after 7 days.`}</TextBold>
          </View>
        </Card>
      </>
    )
  }

  if (props.graphArr?.length < 1 && props.hasRun?.current) {
    return (
      <>
        <Card
          style={{alignItems: 'flex-start', padding: 16, height: 84, backgroundColor: '#5A33E3'}}
          cardMarginBottom={12}>
          <TextReg style={{fontSize: 17, marginBottom: 4}}>Portfolio Balance</TextReg>
          <TextBold style={{fontSize: 21, marginBottom: 4}}>{`$${totalBalance} USD`}</TextBold>
        </Card>
        <Card style={{alignItems: 'flex-start', padding: 16, height: 190}} cardMarginBottom={12}>
          <TextBold>{`Account Summary`}</TextBold>
          <View style={{alignSelf: 'stretch', height: 180, alignItems: 'center'}}>
            <Image
              source={upGraph}
              style={{height: 72, width: 140, marginTop: 20, marginBottom: 20}}
            />
            <TextBold style={{fontSize: 17}}>{`Your data will show after 7 days.`}</TextBold>
          </View>
        </Card>
      </>
    )
  }

  let showMonthlyPayments = (props.monthlyPayments || 0)?.toFixed(2)
  showMonthlyPayments = numberWithCommas(showMonthlyPayments)
  let showBorrowApy = Math.abs(props.borrowApy || 0)?.toFixed(2)

  let showLendApy = Math.abs(props.earnApy || 0)?.toFixed(2)

  let graphLoaded = width => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start()
  }

  return (
    <>
      <Card
        style={{alignItems: 'flex-start', padding: 16, height: 84, backgroundColor: '#5A33E3'}}
        cardMarginBottom={12}>
        <TextReg style={{fontSize: 17, marginBottom: 4}}>Portfolio Balance</TextReg>
        <TextBold style={{fontSize: 21, marginBottom: 4}}>{`$${totalBalance} USD`}</TextBold>
      </Card>

      {!props.under7 && (
        <Card
          style={{alignItems: 'flex-start', padding: 16, height: topShow == 'graph' ? 405 : 220}}
          cardMarginBottom={12}>
          <View
            style={{
              flexDirection: 'row',
              alignSelf: 'stretch',
              justifyContent: 'space-between',
            }}></View>

          {balanceNum > 0 && (
            <>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignSelf: 'stretch',
                  alignItems: 'center',
                  marginTop: -4,
                }}>
                <View
                  style={{flexDirection: 'column', marginTop: title == 'Monthly Summary' ? 16 : 0}}>
                  <TextBold style={{fontSize: 16, marginTop: -16}}>{title}</TextBold>
                  {title == 'Monthly Summary' && (
                    <TextReg style={{fontSize: 13, color: '#00FFBD'}}>{showMonthlyDate}</TextReg>
                  )}
                </View>
                <View style={{flexDirection: 'row'}}>
                  <TouchableOpacity
                    onPress={() => setTopShow('cal')}
                    style={{
                      height: 38,
                      width: 38,
                      borderWidth: 1,
                      borderColor: '#555',
                      backgroundColor: topShow == 'cal' ? '#04053E' : '#3D3D50',
                      borderRadius: 6,
                      marginRight: 10,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Image source={homeCal} style={{height: 24, width: 24}} />
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => setTopShow('graph')}
                    style={{
                      height: 38,
                      width: 38,
                      borderWidth: 1,
                      borderColor: '#555',
                      backgroundColor: topShow == 'graph' ? '#04053E' : '#3D3D50',
                      borderRadius: 6,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Image source={homeGraph} style={{height: 24, width: 24}} />
                  </TouchableOpacity>
                </View>
              </View>

              {topShow == 'cal' && (
                <>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignSelf: 'stretch',
                      alignItems: 'center',
                      marginTop: 10,
                    }}>
                    <TextReg style={{fontSize: 16}}>{`Monthly Net`}</TextReg>
                    <View style={{flexDirection: 'column', alignItems: 'flex-end'}}>
                      <TextBold
                        style={{
                          color: '#fff',
                          fontSize: 16,
                        }}>{`${netSign}$${monthlyNet}`}</TextBold>
                      <TextBold
                        style={{color: netColor, fontSize: 14}}>{`${netApy} ${netSymb}`}</TextBold>
                    </View>
                  </View>
                  <View
                    style={{
                      alignSelf: 'stretch',
                      backgroundColor: '#888',
                      height: 1,
                      marginLeft: -16,
                      marginRight: -16,
                      marginTop: 10,
                    }}
                  />
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignSelf: 'stretch',
                      alignItems: 'center',
                      marginTop: 6,
                    }}>
                    <TextReg style={{fontSize: 16}}>{`Monthly Payment`}</TextReg>
                    <View style={{flexDirection: 'column', alignItems: 'flex-end'}}>
                      <TextBold
                        style={{
                          color: '#FFF',
                          fontSize: 16,
                        }}>{`-$${showMonthlyPayments}`}</TextBold>
                      <TextBold
                        style={{
                          color: '#FF9900',
                          fontSize: 14,
                        }}>{`${showBorrowApy}% APR`}</TextBold>
                    </View>
                  </View>

                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignSelf: 'stretch',
                      alignItems: 'center',
                      marginTop: 6,
                    }}>
                    <TextReg style={{fontSize: 16}}>{`Monthly Income`}</TextReg>
                    <View style={{flexDirection: 'column', alignItems: 'flex-end'}}>
                      <TextBold style={{color: '#fff', fontSize: 16}}>{`+$${lendAmt}`}</TextBold>
                      <TextBold
                        style={{color: '#7BEAD7', fontSize: 14}}>{`${showLendApy}% APY`}</TextBold>
                    </View>
                  </View>
                </>
              )}

              {topShow == 'graph' && (
                <>
                  {/*loading dots under the graph*/}
                  <View
                    style={{
                      top: 90,
                      left: 16,
                      right: 16,
                      position: 'absolute',
                      height: 0.5,
                      backgroundColor: '#aaa',
                    }}
                  />
                  <View
                    style={{
                      top: 127,
                      left: 16,
                      right: 16,
                      position: 'absolute',
                      height: 0.5,
                      backgroundColor: '#aaa',
                    }}
                  />
                  <View
                    style={{
                      position: 'absolute',
                      top: 164,
                      bottom: 80,
                      left: 16,
                      right: 16,
                      borderWidth: 0,
                      borderColor: '#777',
                      borderRadius: 2,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderBottomWidth: 1,
                      borderRightWidth: 1,
                      opacity: 0.5,
                    }}>
                    {false &&
                      Array.from({length: 12}).map((_, index) => (
                        <View
                          key={index}
                          style={{
                            position: 'absolute',
                            top: 0,
                            bottom: 0,
                            left: `${index * 8.33}%`,
                            width: 1,
                            backgroundColor: '#777',
                          }}
                        />
                      ))}
                    {false &&
                      Array.from({length: 4}).map((_, index) => (
                        <View
                          key={index}
                          style={{
                            position: 'absolute',
                            top: `${index * 25}%`,
                            left: 0,
                            right: 0,
                            height: 1,
                            borderWidth: 0.5,
                            borderColor: '#777',
                            borderStyle: 'dashed',
                          }}
                        />
                      ))}
                    <LottieView
                      style={{height: 50, width: 60, opacity: 0.8}}
                      source={require('../../../imgs/lotti/loading-white-dots.json')}
                      autoPlay
                      loop
                    />
                  </View>
                  {true && (
                    <Animated.View
                      style={{
                        alignSelf: 'stretch',
                        backgroundColor: '#3D3D50',
                        opacity: fadeAnim,
                      }}
                      onLayout={event => {
                        var {x, y, width, height} = event.nativeEvent.layout
                        setGraphWidth(width)
                      }}>
                      {graphWidth != 0 && borrowGraph['prices']?.length > 0 && (
                        <WBar
                          graphWidth={graphWidth}
                          prices30={exchangeGraph}
                          prices30Variant1={borrowGraph}
                          prices30Variant2={lendGraph}
                          datesArr={datesArr}
                          graphLoaded={graphLoaded}
                        />
                      )}
                    </Animated.View>
                  )}
                </>
              )}
            </>
          )}
        </Card>
      )}
    </>
  )
}

export default PortfolioBalance
