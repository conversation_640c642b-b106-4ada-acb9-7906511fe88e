import * as shape from 'd3-shape'
import {scaleLinear} from 'd3-scale'
import {Dimensions} from 'react-native'
import {parse} from 'react-native-redash'

const POINTS = 32

const buildGraph = (datapoints, label, graphWidth, gradiant = false, maxTotal=0) => {

  const formattedValues = datapoints?.prices?.map(price => [parseFloat(price[0]), price[1]])
  
  const dates = datapoints?.prices?.map(value => value[1])

  const scaleX = scaleLinear()
    .domain([Math.min(...dates), Math.max(...dates)])
    .range([0, graphWidth])



  const scaleY = scaleLinear().domain([0, maxTotal]).range([150, 0]) //150 - maxRange

  if (gradiant) {
    let yRange = maxTotal - 0
    let timeRange = yRange / 200
    return {
      area: shape
        .area()
        .x(([, x]) => scaleX(x))
        .y(([y]) => scaleY(y - gradiant * timeRange))
        .curve(shape.curveBasis)(formattedValues),
    }
  }

  return {
    label,
    percentChange: datapoints.percent_change,
    path: parse(
      shape
        .line()
        .x(([, x]) => scaleX(x))
        .y(([y]) => scaleY(y))
        .curve(shape.curveBasis)(formattedValues),
    ),
  }
}

export const graphs = (graphWidth, prices30, gradiant = false, maxTotal) => {
  let data = buildGraph(prices30, 'Last Hour', graphWidth, gradiant, maxTotal)
  return [
    {
      label: '1H',
      value: 0,
      data,
    },
  ]
}
