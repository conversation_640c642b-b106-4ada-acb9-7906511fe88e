import React, {useState} from 'react'
import {View, Text} from 'react-native'
import Animated, {
  interpolate,
  useSharedValue,
  useDerivedValue,
  runOnJS,
} from 'react-native-reanimated'
import {ReText, round} from 'react-native-redash'
import {graphs} from './Model'

import {TextBold} from '../../../components'
import {log} from '@grpc/grpc-js/build/src/logging'

let Header = ({translation, graphWidth, prices, datesArr, nextDay, showDates, span}) => {
  let [moneyString, setMoneyString] = useState('$0')
  let [dateString, setDateString] = useState('')
  let [perString, setPerString] = useState('')

  let eachWidth = graphWidth / (prices?.prices?.length - 1)
  let firstValue = prices['prices'][0][0] || 0

  //rate limit for slow devices
  let lastUpdate = useSharedValue(0)
  let updateInterval = 100

  useDerivedValue(() => {
    const now = Date.now()

    if (now - lastUpdate.value < updateInterval) {
      return // Skip this update
    }
    lastUpdate.value = now

    // Map translation.x.value from 0 to prices?.prices?.length - 1
    let modDate = Math.floor(
      interpolate(translation.x.value, [0, graphWidth], [0, prices?.prices?.length - 1]),
    )

    if (span === 'm') {
      if (datesArr?.length < 31) {
        modDate = Math.floor(
          interpolate(translation.x.value, [0, graphWidth], [0, datesArr?.length]),
        )
      } else {
        modDate = Math.floor(interpolate(translation.x.value, [0, graphWidth], [0, 30]))
      }
    }

    if (span === 'w') {
      if (datesArr?.length < 7) {
        modDate = Math.floor(
          interpolate(translation.x.value, [0, graphWidth], [0, datesArr?.length - 1]),
        )
      } else {
        modDate = Math.floor(interpolate(translation.x.value, [0, graphWidth], [0, 6]))
      }
    }
    // Ensure modDate does not exceed the length of datesArr
    let dateString
    if (modDate >= datesArr.length) {
      dateString = nextDay
    } else {
      // Use datesArr but only the last 31 days if span is 'm'
      dateString = datesArr[modDate]
      if (span === 'm') {
        dateString = datesArr.slice(-30)[modDate]
      }
      if (span === 'w') {
        dateString = datesArr.slice(-6)[modDate]
      }
    }

    if (!dateString) {
      dateString = nextDay
    }

    // Interpolate the price based on translation.x.value
    const priceIndex = interpolate(
      translation.x.value,
      [0, graphWidth], // Input range (start to end of graph)
      [0, prices.prices.length - 1], // Output range (index in prices array)
    )

    // Interpolate to get the price between the closest two prices
    const price = interpolate(
      priceIndex,
      [Math.floor(priceIndex), Math.ceil(priceIndex)],
      [
        parseFloat(prices.prices[Math.floor(priceIndex)][0]),
        parseFloat(prices.prices[Math.ceil(priceIndex)][0]),
      ],
    )

    let firstValue = parseFloat(prices.prices[0][0])
    let numUp = price - firstValue
    let perUp = ((numUp / firstValue) * 100).toFixed(2)
    let moneyString = `$${round(price, 2).toLocaleString('en-US', {
      currency: 'USD',
      minimumFractionDigits: 2,
    })}`

    runOnJS(setDateString)(dateString)
    runOnJS(setPerString)(perUp)
    runOnJS(setMoneyString)(moneyString)
  })

  if (showDates) {
    return (
      <Text
        style={{
          marginLeft: 0,
          marginBottom: 1.5,
          color: '#00FFBD',
          fontSize: 12,
          position: 'absolute',
          top: -25,
        }}>
        {dateString}
      </Text>
    )
  }

  return (
    <View style={{height: 30}}>
      <TextBold style={{color: '#fff', fontSize: 20, marginBottom: 4, fontSize: 15}}>
        +{moneyString} USD
      </TextBold>
    </View>
  )
}
export default Header

/*
  <View style={{flexDirection: 'row', alignItems: 'center'}}>
        <Text
          style={{
            fontSize: 10,
            marginLeft: 2,
            opacity: 0.9,
            color: '#aaa',
          }}>{`${perString}%`}</Text>
        <Text style={{marginLeft: 4, marginBottom: 1.5, opacity: 0.8, color: '#fff', fontSize: 10}}>
          {dateString}
        </Text>
      </View>
      */
