import React, {useState, useEffect, useRef} from 'react'
import {View, StatusBar, ScrollView, Image, TouchableOpacity, Platform} from 'react-native'
import {useSelector, useDispatch} from 'react-redux'
import AsyncStorage from '@react-native-async-storage/async-storage'

import {Card, TextReg, TextBold, BackgroundHeader, Button, AddTokenModal} from '../../../components'
import {
  numberWithCommas,
  dig,
  getApr,
  getBaseRate,
  getPayment,
  newRate,
} from '../../../util/helpers'
import {updateLoansDrill, clearAccountCreated} from '../../../store/user/user.actions'
import {isAuthed} from '../../../store/auth/auth.actions'

import noLoanImg from '../../../imgs/graphics/noLoanImg.png'
import custodyAccount from '../../../imgs/graphics/custodyAccount.png'
import PortfolioBalance from './PortfolioBalance'
import MarketplaceDiscover from './MarketplaceDiscover'
import {
  getInvestments,
  getInvestmentsSuccess,
  saveInvestmentRequest,
} from '../../../store/investments/investments.actions'

import MainBox from './Boxes/MainBox'
import BorrowBox from './Boxes/BorrowBox'
import LendBox from './Boxes/LendBox'
import {navigateInvestments} from '../../Investments/helpers'

import styles from './styles'
import {log} from '@grpc/grpc-js/build/src/logging'
import {navigateRefinanceLoan} from '../../Loans/Refinance/helpers'

let ShellAccountBoard = ({navigation, allAccounts, accountCreated, accountRef, txAll}) => {
  const dispatch = useDispatch()
  const user = useSelector(state => state.user.user)
  const WebService = useSelector(state => state.auth.WebService)
  let tokenPrices24h = useSelector(state => state.user.prices24h)
  let tokenPrices = useSelector(state => state.user.prices)

  let launchDarkly = useSelector(state => state.launchDarkly)

  let [lastExchange, setLastExchange] = useState(0)
  let [borrowNet, setBorrowNet] = useState(0)
  let [totalLend, setTotalLend] = useState(0)
  let [portfolioBalanceNum, setPortfolioBalanceNum] = useState('0')
  let [mainArr, setMainArr] = useState([])
  let [loanArr, setLoanArr] = useState([])
  let [lendArr, setLendArr] = useState([])
  let hasRun = useRef(false)
  let [graphArr, setGraphArr] = useState([])
  let [adminApr, setAdminApr] = useState(null)
  let [adminApy, setAdminApy] = useState(null)
  let [monthlyPayments, setMonthlyPayments] = useState(0)
  let [monthlyIncome, setMonthlyIncome] = useState(0)
  let [borrowApy, setBorrowApy] = useState(0)
  let [earnApy, setEarnApy] = useState(0)

  //console.log('shell user', user)

  let dashboardFlag = launchDarkly['dashboardGraphs'] || false

  useEffect(() => {
    getAprs()
    getApys()
    openAcc()
  }, [])

  let getAprs = async () => {
    let rates = await WebService.getLoanRatesMaps()

    let adminFee = await WebService.getAdminFee()
    adminFee = Number(adminFee.data?.originationFee || '0.0')

    let baseRates = rates.data?.baseRates

    const termLength = Math.max(...Object.keys(baseRates).map(Number))
    const highestFloorObject = baseRates[termLength]
    const selectedLTV = Math.min(...Object.keys(highestFloorObject).map(Number))
    const lowestValue = highestFloorObject[selectedLTV]

    //
    let baseRate = getBaseRate(selectedLTV, baseRates, termLength)
    baseRate = Math.round(baseRate * 1000000) / 1000000

    let loanAmount = 10000

    let decimalApr = getApr(loanAmount, selectedLTV, termLength, baseRate)

    const monthsToMilli = 2592000000 * termLength

    let loanPreFee = loanAmount
    let loanFee = loanAmount / (1 - adminFee) - loanAmount
    loanFee = Number((Math.ceil(loanFee * 100) / 100).toFixed(2))
    loanAmount = loanAmount + loanFee

    let payment = getPayment(baseRate, loanAmount, monthsToMilli, false)

    let adminAPR = newRate(termLength, payment * -1, loanPreFee)
    adminAPR = Math.round(adminAPR * 1000000 * 12) / 1000000

    //floor?
    const factor = Math.pow(10, 4)
    adminAPR = Math.floor(adminAPR * factor) / factor

    setAdminApr(adminAPR)
  }

  let getApys = async () => {
    let rates
    try {
      rates = await WebService.getInvestmentRates()
    } catch (err) {
      return
    }
    let bestRate = rates.data?.filter(a => a.isActive)
    bestRate = bestRate.reduce(
      (max, current) => (Number(current.earnRate) > Number(max.earnRate) ? current : max),
      bestRate[0],
    )
    bestRate = (Number(bestRate?.earnRate || 0.08) * 100).toFixed(2)

    setAdminApy(bestRate)
  }

  let goToMarketplace = () => {
    navigation.navigate('Marketplace')
  }

  let goToProduct = (product, accountType, thisUserAcc, forRefi) => {
    if (!thisUserAcc?.loans) return
    let loanData = thisUserAcc.loans[0]
    let loanWallets = user?.allWallets[product.ref - 1]
    if (accountType == 'Exchange') {
      loanData = thisUserAcc.product?.exchange
    } else if (accountType == 'Loan') {
      loanData = {
        ...loanData,
        collaterals: loanWallets,
      }
    }

    if (forRefi) {
      loanData = thisUserAcc?.refinanceLoan
        ? {
            ...loanData,
            pendingLoanId: thisUserAcc?.product?.loan?.pendingLoanId,
            refinanceLoan: thisUserAcc?.refinanceLoan,
          }
        : {
            ...loanData,
            pendingLoanId: thisUserAcc?.product?.loan?.pendingLoanId,
          }
      dispatch(updateLoansDrill(loanData))

      //maybe we update refs?
      dispatch(isAuthed({ref: product.ref, email: user.primaryEmail}))
      WebService.updateRef(product.ref)
      navigateRefinanceLoan(thisUserAcc, user, navigation)
      //
    } else {
      console.log('goToProduct', loanData)
      dispatch(updateLoansDrill(loanData))

      //maybe we update refs?
      dispatch(isAuthed({ref: product.ref, email: user.primaryEmail}))
      WebService.updateRef(product.ref)
      //

      if (accountType == 'Loan') {
        navigation.push('Loans', {...product, nextPage: ''})
      }
      if (accountType == 'Exchange') {
        let verified = user?.walletsEnabled
        if (verified) {
          navigation.navigate('Collateral', {ref: product.ref})
        } else {
          navigation.push('CustodyPage', {...product, nextPage: ''})
        }
      }
    }
  }

  let goToInvestment = async account => {
    const product = account?.product

    //maybe we update refs?
    dispatch(isAuthed({ref: account.ref, email: user.primaryEmail}))

    await WebService.updateRef(account?.ref)

    const isInvestmentEmpty = product?.investment?.errorMessage

    if (isInvestmentEmpty) {
      navigation.push('CreateInvestment')
    } else {
      await WebService.getInvestments().then(async res => {
        const investment = res.data[0]
        await dispatch(getInvestmentsSuccess(res.data))
        navigateInvestments(investment, account, user, navigation)
      })
    }
  }

  let openAcc = () => {
    if (accountCreated == 'auto') {
      let thisAcc = user?.accounts.filter(a => a.ref == accountRef)[0] || []
      console.log('auto user', thisAcc)

      //first set redux accountCreated = null
      dispatch(clearAccountCreated())

      if (thisAcc?.productType == 'loan') {
        let productType = 'Loan'
        try {
          goToProduct({id: thisAcc?.id, ref: thisAcc?.ref}, productType, thisAcc)
        } catch (err) {
          console.log('err', err)
        }
      }
      //maybe bugs lets remove lend auto
      /*
      if (thisAcc?.productType == 'investment') {
        goToInvestment(thisAcc)
      }
      */
    }
  }

  let getStepDescription = a => {
    if (!a?.loans) return
    let loan = a?.loans[0] || []
    let applicationStep = <TextReg style={{color: '#CCC'}}>-</TextReg>
    if (a.account?.productType == 'loan') {
      applicationStep = <TextReg style={{color: '#CCC'}}>Request a loan</TextReg>
    }
    if (a.account?.productType == 'exchange') {
      let anyBalance = false
      a?.wallets.map(b => {
        if (b.value > Number(b.value)) {
          anyBalance = true
        }
      })
      if (anyBalance) {
        applicationStep = <TextReg style={{color: '#CCC'}}>-</TextReg>
      } else {
        applicationStep = <TextReg style={{color: '#CCC'}}>Deposit Funds</TextReg>
      }
    }
    if (loan?.status && loan?.status != 'active') {
      applicationStep = <TextReg style={{color: '#CCC'}}>Continue loan request</TextReg>
    }
    if (loan?.status == 'active') {
      let ltv = (Number(loan.ltv) * 100).toFixed(2)

      if (loan?.thresholds) {
        const warning = Number(loan?.thresholds.warning) * 100
        const marginCall = Number(loan?.thresholds.marginCall) * 100
        const liquidation = Number(loan?.thresholds.liquidation) * 100
        if (ltv > warning) {
          //
        }
        if (ltv > marginCall) {
          //
        }
        if (ltv > liquidation) {
          //
        }
        if (a.account.isStabilized || loan?.awaitingLiquidation) {
          //
        }
      }

      applicationStep = (
        <View style={styles.allAccountsActive}>
          <View style={{flexDirection: 'row', alignItems: 'flex-end'}}>
            <TextReg
              style={{
                fontSize: 16,
                paddingTop: 2,
              }}>{`LTV ${ltv}%`}</TextReg>
          </View>
        </View>
      )
    }

    return applicationStep
  }

  let fetchTransactionHistories = async () => {
    if (hasRun.current) return

    // which tokens to get
    const currencies = txAll.map(tx => tx.currency)
    const uniqueCurrencies = [...new Set(currencies)]

    //filter out bad tx
    txAll = txAll.filter(a => {
      return a.reason != 'commingle' && a.reason != 'sweep'
    })

    console.log('txAll', txAll)

    // how long ago
    const txDate = txAll.sort((a, b) => new Date(a.date) - new Date(b.date))
    let oldest = txDate[0]?.date
    console.log('oldest', oldest)
    let now = new Date()
    let daysAgo = now - new Date(oldest)
    daysAgo = Math.floor(daysAgo / (1000 * 60 * 60 * 24))

    let pairs = uniqueCurrencies.map(a => `${a}-USD`).join(',')

    if (isNaN(daysAgo)) {
      daysAgo = -1
    }

    let originalDaysAgo = daysAgo

    //first check localstorage
    let startTime = Date.now()
    let jsonLoad = await AsyncStorage.getItem(`GETPRICESX-${user?.primaryEmail}`)
    let jsonPriceHistory = false
    if (jsonLoad && daysAgo > 50) {
      jsonPriceHistory = JSON.parse(jsonLoad)
      console.log('jsonPriceHistory', jsonPriceHistory?.timestamp, jsonPriceHistory?.pairs)

      //if my current pairs and priceHistory pairs are same, just get new day data
      if (jsonPriceHistory?.pairs == pairs) {
        console.log('same pairs')
        let jsonTimestamp = new Date(jsonPriceHistory.timestamp)
        let currentTime = new Date(startTime)
        let timeDifference = currentTime - jsonTimestamp
        let daysDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24))
        console.log('Number of days between now and jsonPriceHistory.timestamp:', daysDifference)
        if (daysDifference > 0) {
          daysAgo = daysDifference
          console.log('update daysAgo', daysAgo)
        } else {
          console.log('0')
        }
      } else {
        console.log('different pairs, get prices all new')
        daysAgo = originalDaysAgo
      }
    } else {
      console.log('no localStorage or days less than 50', daysAgo)
    }

    console.log('WebService.getPricesX', pairs, daysAgo)

    let priceHistory = {}
    if (pairs && daysAgo > -1) {
      try {
        priceHistory = await WebService.getPricesX(pairs, daysAgo + 1).catch(err =>
          console.log(err),
        )
      } catch (err) {
        console.log('err', err)
      }
    }

    //stitch together jsonPriceHistory and priceHistory
    if (jsonPriceHistory) {
      priceHistory.data = {...jsonPriceHistory.data, ...priceHistory.data}
    }

    //re save new stitch + pairs + timestamp
    let jsonData = {data: priceHistory.data, timestamp: startTime, pairs}
    AsyncStorage.setItem(`GETPRICESX-${user?.primaryEmail}`, JSON.stringify(jsonData))

    console.log('3')

    // console.log('priceHistory', priceHistory)
    let objTokens = uniqueCurrencies.reduce((acc, currency) => {
      acc[currency] = 0 // Set each currency as key with 0 as value
      return acc
    }, {})

    let newPriceHistoryObj = {...priceHistory?.data}
    const logNewPriceHistoryObj = {...newPriceHistoryObj}
    console.log('3.1')

    if (Object.keys(newPriceHistoryObj).length > 0) {
      let lastDate = Object.keys(newPriceHistoryObj)
        .sort((a, b) => new Date(a) - new Date(b))
        .pop()

      let lastPrices = newPriceHistoryObj[lastDate]
      console.log('lastPrices', lastPrices)
      newPriceHistoryObj[new Date().toISOString()] = lastPrices
    }

    try {
      let historyArr = Object.entries(newPriceHistoryObj)
        .map(([date, prices]) => ({
          date,
          prices,
          borrow: {...objTokens},
          lend: {...objTokens},
          exchange: {...objTokens},
          loanValue: 0,
          epoch: new Date(date).getTime(),
        }))
        .sort((a, b) => new Date(a.date) - new Date(b.date))

      let remainingTx = [...txAll]

      let borrowObj = {...objTokens}
      let exchangeObj = {...objTokens}
      let lendObj = {...objTokens}

      let loanAccs = user?.accounts.filter(a => a?.productType == 'loan') || []
      let loanValues = loanAccs?.map(a => {
        let startDate = a.loans[0]?.startDate
        let initAmt = 0
        if (startDate) {
          initAmt = a.loans[0]?.amount
        }
        let valueArr = [{amt: Number(initAmt).toFixed(2), date: startDate}]
        if (!a.loans[0]?.interestOnly) {
          a.loans[0]?.payments?.reverse()?.map(b => {
            initAmt -= b.principal
            valueArr.push({amt: initAmt?.toFixed(2), date: b.paidAt})
          })
        }
        return valueArr
      })

      try {
        historyArr = historyArr.map(a => {
          let historyDate = new Date(a.date)

          const [matchingTx, newRemainingTx] = remainingTx.reduce(
            (acc, tx) => {
              if (new Date(tx.date) <= historyDate) {
                acc[0].push(tx) // Matching transactions
              } else {
                acc[1].push(tx) // Remaining transactions
              }
              return acc
            },
            [[], []],
          )

          let dailyBorrow = {...borrowObj}
          let dailyExchange = {...exchangeObj}
          let dailyLend = {...lendObj}

          matchingTx.forEach(tx => {
            switch (tx.productType) {
              case 'loan':
                if (tx.type === 'sent') {
                  dailyBorrow[tx.currency] -= Number(tx.amount)
                } else if (tx.type === 'received') {
                  dailyBorrow[tx.currency] += Number(tx.amount)
                }
                break

              case 'exchange':
                if (tx.type === 'sent') {
                  dailyExchange[tx.currency] -= Number(tx.amount)
                } else if (tx.type === 'received') {
                  dailyExchange[tx.currency] += Number(tx.amount)
                }
                break

              case 'investment':
                if (tx.type === 'sent') {
                  dailyLend[tx.currency] -= Number(tx.amount)
                } else if (tx.type === 'received') {
                  dailyLend[tx.currency] += Number(tx.amount)
                }
                break

              default:
                console.warn(`Unknown product type: ${tx.productType}`)
            }
          })

          // Cumulative totals for each productType and currency for that day
          borrowObj = {...dailyBorrow}
          exchangeObj = {...dailyExchange}
          lendObj = {...dailyLend}

          // Assign the cumulative totals to this date
          a.borrow = {...borrowObj}
          a.lend = {...lendObj}
          a.exchange = {...exchangeObj}

          // Calculate loanValue for the historyDate
          a.loanValue = loanValues.reduce((total, valueArr) => {
            let applicableAmt = valueArr.reduce((amt, value) => {
              return new Date(value.date) <= historyDate ? Number(value.amt) : amt
            }, 0)
            return total + applicableAmt
          }, 0)

          // Update remainingTx for the next iteration
          remainingTx = newRemainingTx

          return {...a}
        })
      } catch (err) {
        console.log('err', err)
      }

      // Ensure USDC is present in the prices object for the last two indices
      if (historyArr.length > 1) {
        const lastIndex = historyArr.length - 1
        const secondLastIndex = historyArr.length - 2

        if (!historyArr[lastIndex].prices.USDC) {
          historyArr[lastIndex].prices.USDC = 0.9999
        }

        if (!historyArr[secondLastIndex].prices.USDC) {
          historyArr[secondLastIndex].prices.USDC = 0.9999
        }
      }
      let historyArrStable = historyArr.map(a => {
        return {...a, prices: {...a.prices, USDC: 1, USDT: 1, TUSD: 1, USDP: 1}}
      })

      setGraphArr(historyArrStable)

      hasRun.current = true
    } catch (err) {
      hasRun.current = true
    }
  }

  useEffect(() => {
    calcAccounts()
  }, [tokenPrices24h, user, allAccounts])

  useEffect(() => {
    fetchTransactionHistories()
  }, [mainArr, loanArr, lendArr])

  let calcAccounts = () => {
    allAccounts = allAccounts?.map(a => {
      let thisUserAcc = user?.accounts.filter(b => b?.createdAt == a.account?.createdAt)[0]
      let walletRef = thisUserAcc?.ref - 1
      let thisAccWallet = (user?.allWallets && user?.allWallets[walletRef]) || []
      return {
        ...a,
        account: {
          ...a.account,
          ...thisUserAcc,
        },
        wallets: thisAccWallet,
      }
    })
    let portfolioBalanceNum = 0
    let portfolioBalanceYesterday = 0

    allAccounts = allAccounts?.sort((a, b) => {
      let aValue = a.loans.length > 0 ? a.loans[0].collateralValue : 0
      let bValue = b.loans.length > 0 ? b.loans[0].collateralValue : 0
      aValue = aValue || 0
      bValue = bValue || 0
      return bValue - aValue
    })
    let exchangeAcc = allAccounts?.filter(b => b?.account?.productType == 'exchange')
    let nonExchangeAcc = allAccounts?.filter(b => b?.account?.productType != 'exchange')

    allAccounts = exchangeAcc?.concat(nonExchangeAcc)
    let tokensBalance = {
      BCH: 0,
      BTC: 0,
      DASH: 0,
      DOGE: 0,
      ETH: 0,
      LTC: 0,
      PAXG: 0,
      SALT: 0,
      TUSD: 0,
      USDC: 0,
      USDP: 0,
      XRP: 0,
    }
    let accData = []

    let accountList = allAccounts?.map((a, k) => {
      let accountType = a.account?.productType || ''
      accountType = accountType?.charAt(0).toUpperCase() + accountType?.slice(1)

      let sum24Value = 0
      let sumValue = 0
      a.wallets.map(b => {
        let {currency} = b
        let titleUSD = `${currency}-USD`
        let price24h = tokenPrices24h[titleUSD]?.price
        let value24h = price24h * Number(b.projectedBalance)
        sum24Value += value24h
        sumValue += Number(b.value)
      })
      let difference = ((sumValue - sum24Value) / sum24Value) * 100 || 0
      let difColor = '#00FFBD'
      let difSymbol = '+'
      if (difference < 0) {
        difColor = '#E5705A'
        difSymbol = ''
      }
      if (difference == 0) {
        difSymbol = ''
        difColor = '#999'
      }
      sumValue = numberWithCommas(Number(sumValue).toFixed(2))

      let showDifference = difference.toFixed(2)

      accData?.push({
        a,
        accountType,
        sumValue,
        showDifference,
      })
      return null
    })

    let mainArr = accData?.filter(a => a?.a?.account?.productType == 'exchange')[0]
    let loanArr = accData?.filter(a => a?.a?.account?.productType == 'loan')
    let lendArr = accData?.filter(a => a?.a?.account?.productType == 'investment')

    //portfolio balance now shows totalDif = assets - libalilties
    let totalAssets = 0
    let totalLib = 0
    let totalLend = 0

    let loanTotals = 0
    let aprTotals = 0

    let monthlyPayments = 0
    let monthlyIncome = 0

    let borrowAssets = loanArr.map((a, i) => {
      if (a?.a?.loans?.length > 0 && a?.a?.loans[0]?.status == 'active') {
        let loanNum = Number(a?.a?.loans[0]?.amount)
        loanTotals += loanNum
        aprTotals += (Number(a?.a?.loans[0]?.apr) * loanNum) / 100

        let amInfo = a?.a?.loans[0]?.amortizationInfo

        monthlyPayments += Number(amInfo?.nextPaymentAmount)

        let nextDate = amInfo?.nextPaymentDate
        let nextAmtIndex = amInfo?.amortizationSchedule?.findIndex(a => a.date == nextDate)
        let nextAmt = amInfo?.amortizationSchedule?.[nextAmtIndex]
        let nextAmtAfter = amInfo?.amortizationSchedule?.[nextAmtIndex + 1]

        let nextAmtNum = Number(nextAmt?.principalDue || 0) + Number(nextAmt?.interestDue || 0)

        //if next is paid already, nitesh says use one after that
        if (Number(nextAmt?.interestPaid || 0) >= Number(nextAmt?.interestDue || 0)) {
          nextAmtNum =
            Number(nextAmtAfter?.principalDue || 0) + Number(nextAmtAfter?.interestDue || 0)
        }

        if (monthlyPayments == 0 && nextAmtNum > 0) {
          monthlyPayments = nextAmtNum
        }

        //last payment made
        if (amInfo?.currentBalance == 0) {
          monthlyPayments = 0
        }
      }

      let collatValue = a?.a?.loans[0]?.collateralValue || '0'
      collatValue = Number(collatValue)?.toFixed(2)
      //do wallets instead
      if (collatValue == '0.00') {
        collatValue = 0
        a?.a?.wallets?.map(b => {
          collatValue += Number(b.value)
        })
      }
      totalAssets += Number(collatValue)
      borrowNet += Number(collatValue)

      let balanceVal = a?.a?.loans[0]?.amortizationInfo?.currentBalance || '0'
      balanceVal = Number(balanceVal)?.toFixed(2)
      totalLib += Number(balanceVal)
      borrowNet += Number(collatValue)
    })

    setMonthlyPayments(monthlyPayments)

    let avgWeighted = ((aprTotals / loanTotals) * 100).toFixed(4)

    setBorrowApy(avgWeighted * 100)

    let lendTotals = 0
    let apyTotals = 0
    let amountStaked = 0
    let amountAtEnd = 0

    let totalLendBalance = 0

    lendArr?.map((a, k) => {
      totalLend += Number(a?.sumValue?.replace(/,/g, ''))
      if (a?.a?.investments[0]?.status == 'active') {
        let asset = a?.a?.investments[0]?.investmentAsset
        console.log('asset', asset)
        let price = tokenPrices[`${asset}-USD`]?.price
        console.log('price', price)
        let payoutAmount = Number(a?.a?.investments[0]?.upcomingProjectedEarnings[0]?.payoutAmount)
        console.log('payoutAmount', payoutAmount)

        let accBalance = 0
        if (a?.a?.investments[0]?.collaterals) {
          let correctCollat = a?.a?.investments[0]?.collaterals?.filter(a => a.currency == asset)[0]
          accBalance = correctCollat?.projectedBalance || 0
        }
        console.log('accBalance', accBalance, price)

        totalLendBalance += Number(accBalance * price)
        console.log('totalLendBalance', totalLendBalance)

        monthlyIncome += payoutAmount * price
      }
    })

    console.log('totalLend, totalLendBalance', totalLend, totalLendBalance)

    let earnApy = (((monthlyIncome * 12) / totalLendBalance) * 100).toFixed(2)
    if (isNaN(earnApy)) {
      earnApy = 0
    }
    setEarnApy(earnApy)

    monthlyIncome = monthlyIncome?.toFixed(2)

    setMonthlyIncome(monthlyIncome)

    let borrowNet = totalAssets - totalLib

    //then add custody wallet
    let exchangeSum = Number(mainArr?.sumValue?.replace(/,/g, ''))
    totalAssets += exchangeSum

    let totalDif = totalAssets - totalLib

    //use dif ?
    portfolioBalanceNum = numberWithCommas((totalLend + totalDif).toFixed(2))
    let lastNet = (totalLend + totalDif).toFixed(2)

    setBorrowNet(borrowNet)
    setPortfolioBalanceNum(portfolioBalanceNum)
    setMainArr(mainArr)
    setLoanArr(loanArr)
    setLendArr(lendArr)
    setTotalLend(totalLend)
    setLastExchange(exchangeSum)
  }

  let showMainOpen = loanArr?.length < 1 && lendArr?.length < 1

  let under7 = false
  if (txAll?.length > 0) {
    //over 7 days show graph
    let firstDate = new Date(txAll[0].date)
    let now = new Date()
    let daysDifference = Math.floor((now - firstDate) / (1000 * 60 * 60 * 24))
    if (daysDifference > 7) {
      //console.log('over 7 show graph')
    } else {
      //console.log('under 7 show wait')
      under7 = true
    }
  }

  return (
    <>
      {dashboardFlag && (
        <PortfolioBalance
          portfolioBalanceNum={portfolioBalanceNum}
          borrowNet={borrowNet}
          totalLend={totalLend}
          graphArr={graphArr}
          monthlyPayments={monthlyPayments}
          monthlyIncome={monthlyIncome}
          borrowApy={borrowApy}
          hasRun={hasRun}
          earnApy={earnApy}
          user={user}
          under7={under7}
          lastExchange={lastExchange}
        />
      )}

      <>
        {/* getStepDescription() somewhere in borrow/lend */}
        <MainBox
          arr={mainArr}
          goToProduct={goToProduct}
          showMainOpen={showMainOpen}
          user={user}
          launchDarkly={launchDarkly}
        />
        <BorrowBox arr={loanArr} goToProduct={goToProduct} user={user} />
        <LendBox arr={lendArr} goToProduct={goToProduct} goToInvestment={goToInvestment} />
      </>

      <MarketplaceDiscover
        goToMarketplace={this.goToMarketplace}
        adminApr={adminApr}
        adminApy={adminApy}
        user={user}
      />
    </>
  )
}

export default ShellAccountBoard
