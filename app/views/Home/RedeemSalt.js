import React, {Component} from 'react';
import {
  View,
  TouchableOpacity,
  ScrollView,
  Image,
  Clipboard,
} from 'react-native';
import {connect} from 'react-redux';

import QRCode from 'react-native-qrcode-svg';

import {updateWallets} from '../../store/user/user.actions';
import {showToast} from '../../store/notifications/notifications.actions';

import {
  Card,
  TextReg,
  TextBold,
  BackgroundHeader,
  Button,
  ConfirmRedeemSalt,
} from '../../components';
import commonStyles from '../../styles/commonStyles';

import styles from './styles';

class RedeemSalt extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showCopied: false,
      showConfirm: false,
      loading: false,
    };
    this.inputs = {};
  }

  componentDidMount() {
    console.log('mount salt redeem');
    this.checkForAddress();
  }

  checkForAddress = async () => {
    console.log('check');
    const collaterals = this.props.loanData?.collaterals || [];
    const saltWallet = collaterals.filter(a => a.currency == 'SALT')[0];
    if (!saltWallet?.address) {
      console.log('undefined');
      await this.props.WebService.createWallet('SALT')
        .then(res => {
          this.props.dispatch(updateWallets(res.data));
        })
        .catch(err => {
          if (!this.props.user.mfaEnabled) {
            this.props.dispatch(showToast(true));
            return;
          }
        });
    }
  };

  copyAddress = address => {
    Clipboard.setString(address);
    this.setState({showCopied: true}, () => {
      setTimeout(() => {
        this.setState({showCopied: false});
      }, 1400);
    });
  };

  submit = () => {
    this.setState({loading: true, error: false});
    const loanId = this.props.loanData?.id || 0;
    this.props.WebService.redeemSalt(loanId)
      .then(res => {
        this.props.navigation.popToTop();
        //close modal , and or just exit to
      })
      .catch(err => {
        this.setState({loading: false, error: true});
        console.log('redeemSalt err', err);
      });
  };

  toggleConfirm = () => {
    this.setState({showConfirm: !this.state.showConfirm});
  };

  render() {
    const status = this.props.route.params?.status || null;
    const collaterals = this.props.loanData?.collaterals || [];
    const saltWallet = collaterals.filter(a => a.currency == 'SALT')[0];
    let saltDeposited = saltWallet.projectedBalance;
    const totalSaltNeeded = this.props.loanData?.saltRedeemed || '0';
    let additionalNeeded = Number(totalSaltNeeded - saltDeposited).toFixed(2);
    if (additionalNeeded <= 0) {
      additionalNeeded = 0;
    }
    let percentageOfTotal =
      (Number(saltDeposited) / totalSaltNeeded) * (100).toFixed(0);
    if (percentageOfTotal >= 100) {
      percentageOfTotal = 100;
    }

    const saltAddress = saltWallet?.address || '';
    const canRedeem = Number(saltDeposited) >= Number(totalSaltNeeded);
    console.log('saltAddress', saltAddress);

    const newApr = (Number(this.props.loanData.apr) * 100).toFixed(2);

    if (status == 'unconfirmed') {
      percentageOfTotal = 100;
      additionalNeeded = 0;
      saltDeposited = totalSaltNeeded;
    }

    if (saltDeposited > totalSaltNeeded) {
      saltDeposited = totalSaltNeeded;
    }
    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader
          title={'Redeem Salt'}
          goBack={this.props.navigation.goBack}
        />
        <ConfirmRedeemSalt
          showConfirm={this.state.showConfirm}
          showPinScreen={this.props.showPinScreen}
          toggleConfirm={this.toggleConfirm}
          submit={this.submit}
          loading={this.state.loading}
          totalSaltNeeded={totalSaltNeeded}
          newApr={newApr}
        />
        <ScrollView
          style={{
            backgroundColor: '#f3f3f3',
            alignSelf: 'stretch',
          }}
          contentContainerStyle={{alignItems: 'center'}}>
          <Card marginTop={16}>
            <View
              style={{
                flexDirection: 'row',
                marginRight: 6,
                marginTop: 2,
              }}
            />
            <TextBold
              style={{
                fontSize: 20,
                marginTop: 10,
                marginBottom: 8,
              }}>
              Redeem Salt
            </TextBold>
            <TextReg
              style={{
                fontSize: 16,
                marginBottom: 20,
                width: 340,
                marginTop: 10,
                color: '#28283d',
              }}>
              By redeeming SALT, you are{' '}
              <TextReg
                style={{
                  color: '#28283d',
                  textDecorationLine: 'underline',
                  textDecorationStyle: 'solid',
                  textDecorationColor: '#00FFBD',
                }}>
                spending it
              </TextReg>{' '}
              in order to buy down your interest rate. We only accept SALT
              coming from our on-platform wallet.
            </TextReg>
            <View
              style={{
                width: 340,
                height: 46,
                backgroundColor: '#EEEFF3',
                borderRadius: 14,
                marginBottom: 8,
                overflow: 'hidden',
              }}>
              <View
                style={{
                  height: 46,
                  width: `${percentageOfTotal}%`,
                  backgroundColor: '#00FFBD',
                  borderTopLeftRadius: 4,
                  borderBottomLeftRadius: 4,
                }}
              />
            </View>
            <View
              style={{
                flexDirection: 'row',
                width: 340,
                justifyContent: 'space-between',
                marginBottom: 20,
              }}>
              <View>
                <TextReg>Already Deposited</TextReg>
                <TextBold>{saltDeposited} SALT</TextBold>
              </View>
              <View style={{alignItems: 'flex-end'}}>
                <TextReg>Additional Needed</TextReg>
                <TextBold>{additionalNeeded} SALT</TextBold>
              </View>
            </View>
            {canRedeem ? (
              <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
                <Image
                  style={{
                    height: 120,
                    width: 120,
                    marginBottom: 30,
                  }}
                  source={require('../../imgs/loanChecklist/checkMarkBlack.png')}
                />
              </View>
            ) : (
              <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
                <View
                  style={{
                    height: status == 'unconfirmed' ? 130 : 180,
                    width: status == 'unconfirmed' ? 130 : 180,
                    borderRadius: 14,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: 40,
                  }}>
                  {status == 'unconfirmed' ? (
                    <Image
                      style={{
                        height: 120,
                        width: 120,
                      }}
                      source={require('../../imgs/loanChecklist/checkMarkBlack.png')}
                    />
                  ) : (
                    <View style={styles.depositQrBox}>
                      <QRCode
                        value={saltAddress}
                        size={178}
                        backgroundColor={'#000'}
                        color={'#FFF'}
                      />
                    </View>
                  )}
                </View>
                <View
                  style={{
                    borderRadius: 14,
                    backgroundColor: '#eef0f0',
                    width: 320,
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginBottom: 30,
                  }}>
                  <TextBold
                    style={{
                      color: '#e6e6e6',
                      width: 241,
                      marginLeft: 16,
                      marginRight: 16,
                      textAlign: 'center',
                      height: 40,
                      marginTop: 2,
                    }}>{`${saltAddress}`}</TextBold>
                  <View
                    style={{
                      height: 46,
                      width: 46,
                      borderRadius: 14,
                      backgroundColor: this.state.showCopied
                        ? '#00ffc1'
                        : '#00FFBD',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    {this.state.showCopied ? (
                      <Image
                        style={{height: 38, width: 38}}
                        source={require('../../imgs/checkmark.png')}
                      />
                    ) : (
                      <TouchableOpacity
                        onPress={() => this.copyAddress(saltAddress)}>
                        <Image
                          style={{height: 32, width: 32}}
                          source={require('../../imgs/copyDepositButton.png')}
                        />
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
              </View>
            )}

            {status != 'unconfirmed' && (
              <View
                style={{
                  alignSelf: 'stretch',
                  alignItems: 'center',
                }}>
                <Button
                  disabled={!canRedeem}
                  style={{marginBottom: 8, width: 320}}
                  isLoading={this.state.loading}
                  onPress={() => this.toggleConfirm()}>
                  REDEEM
                </Button>
              </View>
            )}
          </Card>
        </ScrollView>
      </View>
    );
  }
}

RedeemSalt.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
});

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  user: state.user.user,
  WebService: state.auth.WebService,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
});

export default connect(mapStateToProps)(RedeemSalt);
