import React, {Component} from 'react'
import {View, Image, Dimensions, Linking} from 'react-native'
import {connect} from 'react-redux'
import AsyncStorage from '@react-native-async-storage/async-storage'

const {height: ScreenHeight} = Dimensions.get('window')

import {pauseUnit21} from '../../../store/user/user.actions'
import {logout} from '../../../store/auth/auth.actions'

import {BackgroundHeader, TextReg, Button} from '../../../components'
import docMagnify from '../../../imgs/unit21/docMagnify.png'

import styles from '../styles'

class ManualCompliance extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  contactUs = () => {
    Linking.openURL('mailto:<EMAIL>')
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    this.props.navigation.pop(stackDepth)
  }

  render() {
    let forEntity = this.props.route?.params?.forEntity ? true : false

    return (
      <View
        style={{
          //flex: 1,
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
          paddingBottom: 80,
        }}>
        <BackgroundHeader title={' '} close closeFn={this.closeUnit21} />
        <View
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
            justifyContent: 'space-between',
          }}>
          <View style={{alignItems: 'center'}}>
            <Image
              source={docMagnify}
              style={{
                height: 54,
                width: 50,
                marginBottom: 14,
              }}
            />
            <TextReg
              style={{
                color: '#FFF',
                fontSize: 20,
                marginBottom: 14,
                marginTop: 10,
                width: 220,
                textAlign: 'center',
              }}>
              Manual Compliance Review Required
            </TextReg>

            <TextReg
              style={{
                color: '#FFF',
                fontSize: 16,
                marginBottom: 10,
                textAlign: 'center',
              }}>
              Your {forEntity ? 'entity creation request' : 'loan request'} requires a manual compliance review from our operations team,
              which may take up to 48 hours. Once we have reviewed your request, we will email you with additional details and next steps.
            </TextReg>
          </View>
          <View style={{alignItems: 'center'}}>
            <TextReg style={{color: '#FFF', marginBottom: 8}}>Have a question in the meantime?</TextReg>
            <TextReg style={{color: '#FFF', marginBottom: 30}}>Get it touch with us.</TextReg>

            <Button style={{alignSelf: 'stretch', marginBottom: 30}} onPress={() => this.contactUs()}>
              <TextReg style={{color: '#OOO', fontSize: 18}}>CONTACT US</TextReg>
            </Button>
          </View>
        </View>
      </View>
    )
  }
}

ManualCompliance.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
  gesturesEnabled: false,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  tokenPrices: state.user.prices,
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(ManualCompliance)
