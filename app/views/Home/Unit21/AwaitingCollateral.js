import React, {useEffect, useState} from 'react'
import {StyleSheet, View, Platform, TouchableOpacity, Image, Linking, ScrollView} from 'react-native'
import AsyncStorage from '@react-native-async-storage/async-storage'
import {TextReg, TextBold, BackgroundHeader, Button} from '../../../components'
import {useDispatch, useSelector} from 'react-redux'
import {updateFlowB1, updateAccountPlus, increaseUnit21Refresh} from '../../../store/user/user.actions'
import styles from '../styles'

let AwaitingCollateral = ({navigation, route}) => {
  let loanData = useSelector(state => state.user.loanData || {})
  let stackDepth = route?.params?.stackDepth || 1

  let next = async () => {
    navigation.navigate('Unit21DepositCollateral', {stackDepth: stackDepth + 1})
  }

  let closeUnit21 = () => {
    //bug with flowB1, override pop1000
    navigation.pop(1000)
  }

  let goSupport = () => {
    Linking.openURL('mailto:<EMAIL>')
  }

  return (
    <View style={{...localStyles.box, alignItems: 'center'}}>
      <View style={{alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24}}>
        <BackgroundHeader title={'Investment'} close closeFn={closeUnit21} />
      </View>
      <ScrollView style={{alignSelf: 'stretch', flexDirection: 'column', paddingLeft: 24, paddingRight: 24}}>
        <View style={{alignSelf: 'stretch', flexDirection: 'column', alignItems: 'center'}}>
          <Image
            source={require('../../../imgs/graphics/celCoins.png')}
            style={{
              height: 54,
              width: 54,
              marginTop: 40,
            }}
          />
          <TextBold style={{fontSize: 20, marginTop: 20}}>{`Awaiting Collateral`}</TextBold>
          <TextReg
            style={{
              marginTop: 20,
              marginLeft: 10,
              marginRight: 10,
            }}>
            SALT is in the process of securing your loan and is currently awaiting collateral from Celsius. To move forward, please complete
            the following steps:
          </TextReg>
          <View style={{flexDirection: 'row', marginLeft: 10, marginRight: 10, alignSelf: 'flex-start', marginTop: 10}}>
            <View style={{height: 4, width: 4, borderRadius: 2, backgroundColor: '#fff', marginRight: 5, marginLeft: 3, marginTop: 6}} />
            <TextReg>{`Click the "Continue" button to proceed to the next page.`}</TextReg>
          </View>
          <View style={{flexDirection: 'row', marginLeft: 10, marginTop: 10, marginRight: 10, alignSelf: 'flex-start'}}>
            <View style={{height: 4, width: 4, borderRadius: 2, backgroundColor: '#fff', marginRight: 5, marginLeft: 3, marginTop: 6}} />
            <TextReg>{`On that page, you'll have the option to generate either a Bitcoin (BTC) or Ethereum (ETH) address where your loan funds will be transferred.`}</TextReg>
          </View>
          <View style={{flexDirection: 'row', marginLeft: 10, marginTop: 10, marginRight: 10, alignSelf: 'flex-start'}}>
            <View style={{height: 4, width: 4, borderRadius: 2, backgroundColor: '#fff', marginRight: 5, marginLeft: 3, marginTop: 6}} />
            <TextReg>{`If your loan refinance requires additional collateral, you can make those deposits on the same page.`}</TextReg>
          </View>
          <TextReg style={{marginTop: 10}}>{`By completing these steps, you'll expedite the loan activation process.`}</TextReg>
        </View>
        <View style={{alignSelf: 'stretch', flexDirection: 'column', alignItems: 'center'}}>
          <Button
            style={{
              alignSelf: 'stretch',
              marginTop: 30,
              backgroundColor: '#00FFBD',
              marginBottom: 10,
            }}
            onPress={() => next()}
            theme={'secondary'}>
            <TextReg style={{color: '#000', fontSize: 18}}>CONTINUE</TextReg>
          </Button>
          <TouchableOpacity style={{margin: 10}} onPress={() => goSupport()}>
            <TextReg style={{color: '#00FFBD', fontSize: 18}}>CONTACT SUPPORT</TextReg>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  )
}

export default AwaitingCollateral

const localStyles = StyleSheet.create({
  box: {
    flex: 1,
    paddingTop: 24,
    backgroundColor: '#28283D',
  },
})
