import React, {Component} from 'react'
import {
  View,
  Image,
  TouchableOpacity,
  Dimensions,
  Clipboard,
  ScrollView,
  RefreshControl,
} from 'react-native'
import {connect} from 'react-redux'
import LottieView from 'lottie-react-native'
import AsyncStorage from '@react-native-async-storage/async-storage'

const {height: ScreenHeight, width: ScreenWidth} = Dimensions.get('window')

import QRCode from 'react-native-qrcode-svg'

import {Card, TextReg, TextBold, BackgroundHeader, Button, Toast} from '../../../components'
import {numberWithCommas, listPossibleWallets} from '../../../util/helpers'

import styles from '../styles'
import {getTokenPic} from '../../../util/tokens'

import {
  pauseUnit21,
  increaseUnit21Refresh,
  updateLoansDrill,
  updateAllWallets,
} from '../../../store/user/user.actions'

import {showToast} from '../../../store/notifications/notifications.actions'

class Unit21DepositCollateral extends Component {
  constructor(props) {
    super(props)
    let possibleWallets = listPossibleWallets()
    let {collaterals} = this.props.loanData
    collaterals = possibleWallets.map((a, k) => {
      let collateralRow = collaterals.filter(b => b.currency == a)[0] || []
      if (collateralRow.length > 0) {
        return collateralRow
      } else {
        return {
          currency: a,
          creatingWallet: false,
          height: 46,
          open: false,
          showCopied: false,
          refreshing: false,
        }
      }
    })

    const rows = collaterals.map(a => ({
      currency: a.currency,
      height: 46,
      open: false,
      showCopied: false,
      creatingWallet: false,
    }))

    this.state = {
      rows,
      verifiedError: false,
      barWidth: 0,
    }
    this.inputs = {}
    this.polling = null
  }

  componentDidMount() {
    this.startWalletsPolling()
  }

  componentWillUnmount() {
    clearInterval(this.polling)
  }

  startWalletsPolling = () => {
    this.polling = setInterval(this.getWalletData, 30000)
  }

  loadingCreateWallet = (a, trueFalse) => {
    let {rows} = this.state
    rows = rows.map(b => {
      if (b.currency == a.currency) {
        return {...b, creatingWallet: trueFalse}
      }
      return {...b}
    })
    this.setState({rows})
  }

  checkForWallet = a =>
    new Promise((resolve, reject) => {
      const hasWallet = this.props.loanData.collaterals.filter(b => b.currency === a.currency)[0]
      const {amount, verification} = this.props.loanData
      let verified = false
      if (
        verification?.photoIdVerificationStatus == 'passed' ||
        verification?.photoIdVerificationStatus == 'completed_prior'
      ) {
        verified = true
      }
      if (Number(amount) <= 25000 && verification?.identityVerificationStatus == 'passed') {
        verified = true
      }
      //if doesnt have a wallet
      if (!hasWallet?.address || !verified) {
        // create the wallet

        // if user has not finished signup
        if (verified) {
          this.loadingCreateWallet(a, true)
          this.props.WebService.createWallet(a.currency)
            .then(res => {
              let {loanData, collaterals} = this.props.loanData
              collaterals.push(res.data)
              loanData = {
                ...loanData,
                collaterals: collaterals,
              }
              this.props.dispatch(updateLoansDrill(loanData))
              //this.props.dispatch(updateWallets(res.data));

              this.loadingCreateWallet(a, false)
              this.getWalletData()
              resolve()
            })
            .catch(err => {
              this.setState({creatingWallet: false})
              reject()
            })
        } else {
          //show Needs to verify ID
          this.setState({verifiedError: true})
          resolve('idVerify')
        }
      } else {
        resolve()
      }
    })

  expandRow = async (a, open) => {
    if (!this.props.user.mfaEnabled) {
      this.props.dispatch(showToast(true))
      return
    }
    const walletRes = await this.checkForWallet(a)
    let {rows} = this.state
    rows = rows.map(b => {
      if (!open && b.currency == a.currency) {
        return {...b, height: walletRes == 'idVerify' ? 200 : 390, open: true}
      }
      if (open && b.currency == a.currency) {
        return {...b, height: 46, open: false}
      }

      return {...b, height: 46, open: false}
    })
    this.setState({rows})
  }

  copyAddress = a => {
    const {rows} = this.state
    const copiedRows = rows.map(b => {
      if (b.currency == a.currency) {
        return {...b, showCopied: true}
      }
      return b
    })
    Clipboard.setString(a.address)
    this.setState({rows: copiedRows}, () => {
      setTimeout(() => {
        let {rows} = this.state
        rows = rows.map(b => {
          if (b.currency == a.currency) {
            return {...b, showCopied: false}
          }
          return b
        })
        this.setState({rows})
      }, 1400)
    })
  }

  getBarWidth = layout => {
    const {x, y, width, height} = layout
    this.setState({barWidth: width})
  }

  continue = () => {
    this.props.dispatch(increaseUnit21Refresh())
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    this.props.navigation.pop(stackDepth)
  }

  getWalletData = async () => {
    this.setState({refreshing: true})
    let accountArr = this.props.user?.accounts || []
    let walletsRes = []

    await Promise.all(
      accountArr.map(async a => {
        await this.props.WebService.getWallets(a.ref).then(res => {
          //walletsRes.push(res.data);
          walletsRes[a.ref - 1] = res.data
          return true
        })
      }),
    )
    this.props.dispatch(updateAllWallets(walletsRes))
    this.setState({refreshing: false})
  }

  render() {
    let possibleWallets = listPossibleWallets()
    const totalValue = this.props.loanData.amount / this.props.loanData.baseLTV || 0
    const showTotalValue = numberWithCommas(totalValue.toFixed(2))
    //let {collaterals} = this.props.loanData;

    let {user} = this.props
    let productRef = this.props?.accountRef
    let collaterals = user?.allWallets[productRef - 1]

    const collateralTotalWithSalt = collaterals.reduce(
      (sum, collateral) => parseFloat(sum) + parseFloat(collateral.value),
      0,
    )
    const alreadyDeposited = numberWithCommas(collateralTotalWithSalt.toFixed(2))
    let totalRemaining = totalValue - collateralTotalWithSalt
    if (totalRemaining < 0) {
      totalRemaining = 0
    }
    const showTotalRemaining = numberWithCommas(totalRemaining.toFixed(2))
    const barFillPercent = collateralTotalWithSalt / totalValue

    const barFill = (this.state.barWidth / 100) * (barFillPercent * 100)

    let launchDarkly = this.props.launchDarkly

    let banned = {
      BTC: launchDarkly['disable-btc-deposit'] || false,
      ETH: launchDarkly['disable-eth-deposit'] || false,
      BCH: launchDarkly['disable-bch-deposit'] || false,
      LTC: launchDarkly['disable-ltc-deposit'] || false,
      USDC: launchDarkly['disable-usdc-deposit'] || false,
      USDP: launchDarkly['disable-usdp-deposit'] || false,
      SALT: launchDarkly['disable-salt-deposit'] || false,
      TUSD: launchDarkly['disable-tusd-deposit'] || false,
      XRP: launchDarkly['disable-xrp-deposit'] || true,
      DASH: launchDarkly['disable-dash-deposit'] || true,
      DOGE: launchDarkly['disable-doge-deposit'] || true,
      PAXG: launchDarkly['disable-paxg-deposit'] || true,
    }

    collaterals = possibleWallets.map((a, k) => {
      let collateralRow = collaterals.filter(b => b.currency == a) || []
      if (collateralRow.length > 0) {
        return collateralRow[0]
      } else {
        return {
          currency: a,
          creatingWallet: false,
          height: 46,
          open: false,
          showCopied: false,
        }
      }
    })

    const showCollaterals = collaterals.map((a, k) => {
      if (banned[a.currency]) {
        return
      }
      const showImg = getTokenPic(a.currency)
      const priceTicker = `${a.currency}-USD`
      const price = this.props.tokenPrices[priceTicker].price
      console.log(
        'totalRemaining-price',
        a.currency,
        totalRemaining,
        price,
        totalRemaining / Number(price),
      )
      let remainingAmount = Math.ceil((totalRemaining / Number(price)) * 100) / 100
      console.log('remainingAmount 1', a.currency, remainingAmount)
      remainingAmount = numberWithCommas(remainingAmount.toFixed(2))
      console.log('remainingAmount 2', a.currency, remainingAmount)

      return (
        <Card key={k} marginTop={-4} style={{backgroundColor: '#48485A'}}>
          <View style={{alignSelf: 'stretch', height: this.state.rows[k].height}}>
            <View
              style={{
                alignSelf: 'stretch',
                flexDirection: 'row',
                paddingLeft: 8,
                paddingRight: 8,
                alignItems: 'center',
                justifyContent: 'space-between',
                height: 46,
              }}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Image style={styles.portfolioImg} source={showImg} />
                <TextReg
                  style={{
                    fontSize: 20,
                    width: 60,
                    marginLeft: 16,
                    marginRight: 4,
                    color: '#FFF',
                  }}>
                  {a.currency}
                </TextReg>
                <TextBold style={{fontSize: 20, color: '#FFF'}}>{remainingAmount}</TextBold>
              </View>
              <TouchableOpacity
                disabled={this.state.rows[k].creatingWallet}
                onPress={() => this.expandRow(a, this.state.rows[k].open)}>
                {this.state.rows[k].open ? (
                  <View style={{height: 30, width: 34}}>
                    <Image
                      style={{
                        height: 30,
                        width: 30,
                        opacity: 0.5,
                      }}
                      source={require('../../../imgs/backToSettingsDark.png')}
                    />
                  </View>
                ) : (
                  <View>
                    {this.state.rows[k].creatingWallet ? (
                      <LottieView
                        ref={animation => {
                          this.animation = animation
                        }}
                        style={styles.whiteDotsAnimationWallet}
                        source={require('../../../imgs/lotti/loading-white-dots.json')}
                        autoPlay
                      />
                    ) : (
                      <Image
                        style={{height: 40, width: 40}}
                        source={require('../../../imgs/unit21/qrCodeSymbol.png')}
                      />
                    )}
                  </View>
                )}
              </TouchableOpacity>
            </View>
            {this.state.verifiedError ? (
              <View
                style={{
                  marginTop: 14,
                  alignSelf: 'stretch',
                  alignItems: 'center',
                }}>
                <View
                  style={{
                    width: 260,
                    height: 1,
                    borderTopWidth: 1,
                    borderColor: '#CCC',
                    marginBottom: 6,
                  }}
                />
                <View
                  style={{
                    alignSelf: 'stretch',
                    paddingTop: 18,
                    alignItems: 'center',
                  }}>
                  <TextReg
                    style={{
                      fontSize: 16,
                      textAlign: 'center',
                      marginBottom: 14,
                      width: 280,
                      color: '#FFF',
                    }}>
                    Currently your account doesnt not contain a verified ID
                  </TextReg>
                  <Button onPress={() => this.goToIdentityVerification()}>VERIFY ID</Button>
                </View>
              </View>
            ) : (
              <View style={{marginTop: 14, alignItems: 'center'}}>
                <View
                  style={{
                    width: 260,
                    height: 1,
                    borderTopWidth: 1,
                    borderColor: '#CCC',
                    marginBottom: 6,
                  }}
                />
                <View
                  style={{
                    alignSelf: 'stretch',
                    paddingTop: 18,
                    alignItems: 'center',
                  }}>
                  <View
                    style={{
                      height: 160,
                      width: 160,
                      borderRadius: 14,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 16,
                    }}>
                    <View style={styles.depositQrBox}>
                      {a.address ? (
                        <QRCode
                          value={a.address}
                          size={158}
                          backgroundColor={'#FFF'}
                          color={'#48485A'}
                        />
                      ) : (
                        <View
                          style={{
                            height: 158,
                            width: 158,
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}>
                          <LottieView
                            ref={animation => {
                              this.animation = animation
                            }}
                            style={{...styles.whiteDotsAnimationWallet, marginLeft: 0}}
                            source={require('../../../imgs/lotti/loading-white-dots.json')}
                            autoPlay
                          />
                        </View>
                      )}
                    </View>
                  </View>
                  <View
                    style={{
                      width: 300,
                      alignItems: 'center',
                    }}>
                    <TextReg
                      style={{
                        textAlign: 'center',
                        marginBottom: 16,
                        marginTop: 7,
                        width: 260,
                        color: '#FFF',
                      }}>
                      {`To deposit ${a.currency}, please send it to the following address:`}
                    </TextReg>
                    <View
                      style={{
                        borderRadius: 14,
                        backgroundColor: '#eef0f0',
                        width: 274,
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <TextBold
                        style={{
                          color: '#000',
                          width: 201,
                          marginLeft: 16,
                          marginRight: 16,
                          textAlign: 'center',
                          height: 40,
                          marginTop: 2,
                        }}>{`${a.address}`}</TextBold>
                      <View
                        style={{
                          height: 40,
                          width: 40,
                          borderRadius: 14,
                          backgroundColor: this.state.rows[k].showCopied ? '#00ffc1' : '#00FFBD',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}>
                        {this.state.rows[k].showCopied ? (
                          <Image
                            style={{height: 32, width: 32}}
                            source={require('../../../imgs/checkmark.png')}
                          />
                        ) : (
                          <TouchableOpacity onPress={() => this.copyAddress(a)}>
                            <Image
                              style={{height: 26, width: 26}}
                              source={require('../../../imgs/copyDepositButton.png')}
                            />
                          </TouchableOpacity>
                        )}
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            )}
          </View>
        </Card>
      )
    })

    let currentlyDisabled = this.props.launchDarkly['disable-deposit'] || false

    return (
      <View
        style={{
          //flex: 1,
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader title={'Deposit Collateral'} close closeFn={this.closeUnit21} />
        <Toast goTo={() => this.props.navigation.navigate('TwoFactor', {flow: 'home'})} />
        <ScrollView
          style={{
            alignSelf: 'stretch',
          }}
          contentContainerStyle={{
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
          refreshControl={
            <RefreshControl
              refreshing={this.state.refreshing}
              onRefresh={this.getWalletData}
              colors={['#28283D']}
              tintColor={'#fff'}
            />
          }>
          <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
            <View
              style={{
                alignSelf: 'stretch',
                alignItems: 'center',
                marginLeft: 20,
                marginRight: 20,
              }}>
              <TextReg
                style={{
                  color: '#FFF',
                  fontSize: 18,
                  marginBottom: 14,
                  marginTop: 10,
                }}>
                Deposit any combination of the assets below. Amounts reflect the minimum that would
                be required to collateralize the loan with only that asset.
              </TextReg>
            </View>

            <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
              <Card cardMarginBottom={18} style={{backgroundColor: '#48485A'}}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignSelf: 'stretch',
                    marginBottom: 18,
                    marginTop: 2,
                    marginLeft: 4,
                    marginRight: 4,
                  }}>
                  <View>
                    <TextReg style={{marginBottom: 4, color: '#FFF'}}>Already Deposited</TextReg>
                    <TextBold
                      style={{
                        fontSize: 17,
                        color: '#FFF',
                      }}>{`$${alreadyDeposited}`}</TextBold>
                  </View>
                  <View style={{alignItems: 'flex-end'}}>
                    <TextReg style={{marginBottom: 4, color: '#FFF'}}>Additional Needed</TextReg>
                    <TextBold
                      style={{
                        fontSize: 17,
                        color: '#FFF',
                      }}>{`$${showTotalRemaining}`}</TextBold>
                  </View>
                </View>
                <View style={{alignSelf: 'stretch', position: 'relative'}}>
                  <View
                    onLayout={event => {
                      this.getBarWidth(event.nativeEvent.layout)
                    }}
                    style={{
                      alignSelf: 'stretch',
                      height: 30,
                      backgroundColor: '#e8e8e8',
                      borderRadius: 14,
                      marginLeft: 4,
                      marginRight: 4,
                      marginBottom: 6,
                      overflow: 'hidden',
                    }}>
                    <View
                      style={{
                        backgroundColor: '#00FFBD',
                        width: barFill || 0,
                        height: 30,
                        zIndex: 20,
                        position: 'absolute',
                      }}
                    />
                  </View>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    marginLeft: 4,
                    marginRight: 4,
                    alignSelf: 'stretch',
                    justifyContent: 'space-between',
                  }}>
                  <TextReg style={{fontSize: 17, color: '#9b9b9b'}}>$0</TextReg>
                  <TextReg
                    style={{
                      fontSize: 17,
                      color: '#9b9b9b',
                    }}>{`$${showTotalValue}`}</TextReg>
                </View>
              </Card>
              {!currentlyDisabled && totalRemaining != 0 && showCollaterals}
              {currentlyDisabled && (
                <View style={{width: 300, marginTop: 10, marginBottom: 10}}>
                  <TextReg style={{marginTop: 0}}>
                    {`Recent industry events have impacted our business and some of our key partners. Until we are able to determine the extent of this impact, we have paused deposits and withdrawals on the SALT platform effective immediately.`}
                  </TextReg>
                  <TextReg style={{marginTop: 10}}>
                    {`During this time, your loan will remain active and all our loan monitoring systems will be fully operational. We are working diligently with our partners to secure a clear path forward.`}
                  </TextReg>
                  <TextReg style={{marginTop: 10}}>
                    {`We understand that this is difficult news during these turbulent times. We will provide updates as frequently as possible.`}
                  </TextReg>
                </View>
              )}
            </View>
          </View>
          <Button
            disabled={totalRemaining > 0}
            style={{
              alignSelf: 'stretch',
              marginBottom: 80,
              marginTop: 20,
              marginLeft: 20,
              marginRight: 20,
            }}
            onPress={() => this.continue()}>
            <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
          </Button>
        </ScrollView>
      </View>
    )
  }
}

Unit21DepositCollateral.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
  gesturesEnabled: false,
})

const mapStateToProps = state => ({
  accountRef: state.auth.account.ref,
  loanData: state.user.loanData || {},
  tokenPrices: state.user.prices,
  WebService: state.auth.WebService,
  user: state.user.user,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(Unit21DepositCollateral)
