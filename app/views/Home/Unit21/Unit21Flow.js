import React, {Component} from 'react'
import {View, Dimensions, Platform} from 'react-native'
import {connect} from 'react-redux'
import AsyncStorage from '@react-native-async-storage/async-storage'

const {height: ScreenHeight} = Dimensions.get('window')
import {
  pauseUnit21,
  updateLoans,
  unit21Showing,
  updateLoansDrill,
  increaseUnit21Refresh,
  updateUser,
  updateFlowB1,
  updateFlowB1Whole,
  updateAccountPlus,
} from '../../../store/user/user.actions'

import {BackgroundHeader} from '../../../components'
import {numberWithCommas} from '../../../util/helpers'

import BusinessVerification1 from './BusinessVerification1'
import AllocationSelection from './AllocationSelection'

class Unit21Flow extends Component {
  constructor(props) {
    super(props)
    this.state = {
      showL1: false,
      getControlInfo: false,
      businessDocShow: false,
      loadingAllocation: false,
      stackDepth: 1,
    }
    this.inputs = []
  }

  componentDidMount() {
    this.checkStatus()
    this.props.dispatch(unit21Showing(true))
  }
  componentWillUnmount() {
    this.props.dispatch(unit21Showing(false))
  }

  componentDidUpdate(prevProps) {
    if (this.props.unit21RefreshCount != prevProps.unit21RefreshCount) {
      this.checkStatus()
    }
  }

  getLoanDataNew = () =>
    this.props.WebService.getLoans().then(res => {
      if (res.data?.length > 0) {
        const loanData = res.data[0]
        return this.props.dispatch(updateLoans(loanData))
      }
      console.log('else')
    })

  getExchangeAccountNew = () =>
    this.props.WebService.getSaltAccount().then(res => {
      let exchangeAcc = res.data?.filter(a => a.productType == 'exchange')[0]
      return this.props.dispatch(updateLoansDrill(exchangeAcc?.product?.exchange))
    })

  whichL2 = (user, stackDepth) => {
    this.props.navigation.navigate('JumioVerification', {stackDepth})
    stackDepth += 1
    this.setState({stackDepth})
  }

  whichL1 = async stackDepth => {
    let thisAccount =
      this.props.user?.accounts?.filter(a => a.ref == this.props.accountRef)[0] || []
    let isBusiness = thisAccount?.type == 'business' || false
    let isLoan = thisAccount?.productType == 'loan'
    let isCustody = thisAccount?.productType == 'exchange'

    let {street1, province, city, postalCode, countryCode} = this.props.user?.address || ''
    let {govtId, dateOfBirth, countryOfCitizenship} = this.props.user
    let callingCode = this.props.user?.phone?.callingCode || '1'
    let {number} = this.props.user?.phone || ''
    let {useOfFunds, isInMilitary} = this.props.loanData?.loanQuestionnaire || {}
    //loan jurisdiction country & province
    if (isLoan) {
      countryCode = this.props?.loanData?.jurisdiction?.countryCode
      province = this.props?.loanData?.jurisdiction?.province
    }

    let hasAddress = street1 && province && city && postalCode && countryCode
    let hasBirthdate = dateOfBirth
    let hasPhone = callingCode && number && number !== ''
    let hasCitizenship = countryOfCitizenship
    let hasSocial = govtId
    let hasFundsUse = useOfFunds
    let isMilitaryAnswered = isInMilitary !== null

    let questionnaireDone = this.props?.loanData?.verification?.questionnaireStatus == 'passed'

    //console.log('hasStuff- personal', hasAddress, hasBirthdate, hasPhone, hasCitizenship, hasSocial, hasFundsUse, isMilitaryAnswered);

    if (isLoan && !hasFundsUse) {
      this.props.navigation.navigate('FlowL1Funds', {stackDepth})
      return
    }
    if (isLoan && !isMilitaryAnswered) {
      this.props.navigation.navigate('FlowL1Military', {stackDepth})
      return
    }

    /*
    if (isBusiness) {
      let businessProfile = thisAccount ? thisAccount?.businessProfile : {}
      let businessAddress = businessProfile?.address
      let busStreet1 = businessAddress?.street1 || ''
      let busCity = businessAddress?.city || ''
      let busProvince = businessAddress?.province || ''
      let busPostal = businessAddress?.postalCode || ''
      let busCountryCode = businessAddress?.countryCode || ''
      let {listDBA, legalName, taxIdNumber, industry, entityType} = businessProfile
      let {formationDate, formationJurisdiction} = businessProfile

      let hasBusAddress = busStreet1 && busCity && busProvince && busPostal && busCountryCode

      let b1Obj = {}

      //entity1
      let entity1 = b1Obj.entity1 || null
      if (businessProfile.isEntityCurrencyExchange) entity1 = 1
      if (businessProfile.isEntityMoneyLending) entity1 = 2
      if (businessProfile.isEntityPreciousMetals) entity1 = 3
      if (businessProfile.isEntityArmsAmmunition) entity1 = 4
      if (businessProfile.isEntityNarcotics) entity1 = 5
      if (businessProfile.isEntityMarijuana) entity1 = 6
      if (businessProfile.isEntityAdultEntertainment) entity1 = 7
      if (businessProfile.isEntityCasinos) entity1 = 8
      if (businessProfile.isEntityOnlineGambling) entity1 = 9
      if (businessProfile.isEntityForeignGovernments) entity1 = 10
      if (businessProfile.isEntityNotForProfit) entity1 = 11
      if (businessProfile.isEntityMoneyService) entity1 = 12
      if (businessProfile.isEntityMoneyLending === false && !entity1) entity1 = 13
      if (businessProfile.isEntityMoneyLending === null && !entity1) entity1 = null
      b1Obj.entity1 = entity1

      //entity2
      if (businessProfile.isEntityAssociation) b1Obj.entity2 = true

      //name
      if (businessProfile.legalName) b1Obj.name = businessProfile.legalName
      if (businessProfile.listDBA) b1Obj.dba = businessProfile.listDBA
      if (businessProfile.entityType) b1Obj.entity = businessProfile.entityType

      //formation
      let showDate = ''
      if (businessProfile.formationDate && businessProfile?.formationDate?.includes('-')) {
        showDate = businessProfile?.formationDate?.split('T')[0]
        const year = showDate.split('-')[0]
        const month = showDate.split('-')[1]
        const day = showDate.split('-')[2]
        showDate = `${month}/${day}/${year}`
      }
      if (businessProfile.formationDate) b1Obj.formation = showDate
      if (businessProfile.industry) b1Obj.sector = businessProfile.industry
      if (businessProfile.taxIdNumber) b1Obj.ein = businessProfile.taxIdNumber

      //address
      if (businessAddress.street1) b1Obj.address1 = businessAddress.street1
      if (businessAddress.street2) b1Obj.address2 = businessAddress.street2
      if (businessAddress.city) b1Obj.city = businessAddress.city
      if (businessAddress.postalCode) b1Obj.postal = businessAddress.postalCode
      if (businessAddress.countryCode) b1Obj.countryCode = businessAddress.countryCode
      if (businessAddress.province) b1Obj.regionCode = businessAddress.province
      if (businessProfile?.address?.id) b1Obj.businessAddressID = businessProfile?.address?.id

      //docs
      let docs = businessProfile?.address?.documents || []
      let otherDocs = businessProfile?.documents || []
      docs = docs.concat(otherDocs)
      let addressDocs = docs.filter(a => a.type == 'other_proof_of_address')
      let businessDocs = docs.filter(a => a.type == 'verification_of_business')
      b1Obj.docsA = addressDocs
      b1Obj.docsB = businessDocs

      this.props.dispatch(updateFlowB1Whole(b1Obj))

      if (
        !businessProfile.isComplete ||
        this.props.loanData?.verification?.businessVerificationStatus == 'required' ||
        this.props.loanData?.verification?.businessVerificationStatus == 'retry'
      ) {
        //remove entity
        //this.props.navigation.navigate('FlowB1Entity', {stackDepth})
        //return
      }
    }
    */

    // business beneficiaries
    if (isBusiness && this.props.loanData?.verification.beneficiariesStatus == 'required') {
      this.props.navigation.navigate('Beneficiaries', {stackDepth})
      return
    }

    this.props.navigation.navigate('FlowL1PhoneNumber', {stackDepth})
    if (countryOfCitizenship == 'US') {
      stackDepth += 5
    } else {
      stackDepth += 4
    }
    this.setState({stackDepth})
    return
  }

  getAcc = async () => {
    const getAllAccounts = this.props.WebService.getAllAccounts()
    const allAccountData = await getAllAccounts
    this.props.dispatch(updateAccountPlus(allAccountData.data))
    return
  }

  checkStatus = async () => {
    await this.getAcc()
    let thisAccount =
      this.props.user?.accounts?.filter(a => a.ref == this.props.accountRef)[0] || []

    if (thisAccount?.productType == 'exchange') {
      await this.getExchangeAccountNew()
    } else {
      await this.getLoanDataNew()
    }

    let {loanData, user, accountRef} = this.props
    let {amount, baseLTV, depositBankAccount} = loanData
    let verification = loanData?.verification || []
    let totalValue = amount / baseLTV || 0
    let collaterals = user?.allWallets[accountRef - 1]
    let collateralValueCalc = 0
    collaterals.map(a => {
      collateralValueCalc += Number(a.value)
    })

    let totalRemaining = totalValue - collateralValueCalc
    let account = user.accounts.filter(a => a.ref == accountRef)[0]
    let isLoan = account?.productType == 'loan'
    let isBusiness = account?.type == 'business' || false
    let countryCode = user?.address?.countryCode || ''
    let showCompliance = false
    let useRewardAsPayment = this.props?.loanData?.reward?.useRewardAsPayment
    let {useOfFunds, isInMilitary} = this.props.loanData?.loanQuestionnaire || {}
    let hasFundsUse = useOfFunds
    let isMilitaryAnswered = isInMilitary !== null

    //celcius refferal code
    let referralCode = this.props.user?.referredByCode
    let isCel = referralCode == 'CELREFI23'

    let {stackDepth} = this.state
    stackDepth += 1
    this.setState({stackDepth})

    //custom route
    let custom = false
    if (stackDepth <= 2) {
      custom = this.props?.route?.params?.custom
    }

    if (custom == 'LoanPayout') {
      this.props.navigation.navigate('LoanPayout', {
        goBackDisabled: true,
        stackDepth,
        edit: true,
      })
      stackDepth += 1
      this.setState({stackDepth})
      return
    }

    //loan questions even before L1
    if (isLoan && !hasFundsUse) {
      this.props.navigation.navigate('FlowL1Funds', {stackDepth})
      return
    }
    if (isLoan && !isMilitaryAnswered) {
      this.props.navigation.navigate('FlowL1Military', {stackDepth})
      return
    }

    //personal && L1
    if (!isBusiness && verification.identityVerificationStatus == 'required') {
      this.whichL1(stackDepth)
      return
    }

    //business && L1
    /*
    if (
      isBusiness &&
      (verification.businessVerificationStatus == 'required' ||
        verification.businessVerificationStatus == 'retry' ||
        verification.businessVerificationStatus == 'failed')
    ) {
      this.props.navigation.navigate('ManualCompliance', {stackDepth})
      return
    }
    */

    //business && controller info
    if (isBusiness && verification.identityVerificationStatus == 'required') {
      this.whichL1(stackDepth)
      return
    }

    // business beneficiaries
    if (isBusiness && verification.beneficiariesStatus == 'required') {
      this.props.navigation.navigate('Beneficiaries', {stackDepth})
      return
    }

    //once beneficiaries done
    if (isBusiness && verification.businessVerificationStatus == 'required') {
      //remove entity
      //this.props.navigation.navigate('FlowB1Entity', {stackDepth})
      //  return
    }

    //L1 done by here
    if (verification.photoIdVerificationStatus == 'failed') {
      this.props.navigation.navigate('ManualCompliance', {stackDepth})
      return
    }

    //check L2

    if (
      verification.photoIdVerificationStatus == 'required' ||
      verification.photoIdVerificationStatus == 'retry' ||
      verification.photoIdVerificationStatus == 'pending'
    ) {
      this.whichL2(user, stackDepth)
      return
    }

    //cion custom payout tos
    //& then skips payout type after deposit collateral
    if (loanData.depositBankAccountType == 'merchant_account' && !this.props.merchantTos) {
      this.props.navigation.navigate('MerchantTos', {stackDepth})
      return
    }

    if (
      verification.residencyVerificationStatus == 'required' ||
      verification.residencyVerificationStatus == 'retry'
    ) {
      this.props.navigation.navigate('JurisdictionDocs', {stackDepth})
      return
    }

    //if this is an exchange account- show
    if (thisAccount?.productType == 'exchange') {
      this.props.navigation.navigate('ExchangePending', {stackDepth})
      return
    }

    if (Number(amount) > 75000 && verification.bankruptcyStatus !== 'skip') {
      if (verification.bankruptcyStatus == 'manual_verification') {
        //now we show later
        showCompliance = true
      } else if (verification.bankruptcyStatus == 'passed') {
        //passed
      } else {
        //failed or retry
        this.props.navigation.navigate('Unit21Bankruptcy', {stackDepth})
        return
      }
    }

    if (Number(amount) > 150000 && verification.amountVerificationStatus != 'passed') {
      showCompliance = true
    }

    if (
      custom == 'LoanPayout' ||
      (!depositBankAccount && loanData.depositBankAccountType != 'merchant_account')
    ) {
      this.props.navigation.navigate('LoanPayout', {
        goBackDisabled: true,
        stackDepth,
      })
      stackDepth += 1
      this.setState({stackDepth})
      return
    }

    //celcius

    //if depositBankAccountType = marchantTos
    if (totalRemaining > 0) {
      if (isCel) {
        this.props.navigation.navigate('AwaitingCollateral', {stackDepth})
        return
      }
      this.props.navigation.navigate('Unit21DepositCollateral', {stackDepth})
      return
    }

    if (loanData?.marginManagementPreference === null) {
      this.props.navigation.navigate('ConfirmRiskManagement', {stackDepth})
      return
    }

    if (loanData.saltRedeemed && loanData.saltRedeemed != '0') {
      let hasRedeemed = false
      hasRedeemed = await this.checkForSaltRedeemed().then(res => res)

      if (!hasRedeemed) {
        this.props.navigation.navigate('Unit21SaltRedeem', {
          goBackDisabled: true,
          stackDepth,
        })
        return
      }
    }

    if (
      verification.identityVerificationStatus == 'manual_verification' ||
      verification.bankruptcyStatus == 'manual_verification' ||
      verification.businessVerificationStatus == 'manual_verification' ||
      verification.residencyVerificationStatus == 'manual_verification' ||
      showCompliance
    ) {
      this.props.navigation.navigate('ManualCompliance', {stackDepth})
      return
    }

    if (loanData?.status == 'pending_signatures') {
      this.props.navigation.navigate('Unit21LoanDocs', {stackDepth})
    } else {
      this.props.navigation.navigate('Unit21LoanPending', {stackDepth})
    }
  }

  checkForSaltRedeemed = () =>
    this.props.WebService.getTransactionHistory('SALT').then(res => {
      const transactions = res.data.transactions

      if (!transactions) {
        return false
      }
      let hasSaltTx = false
      transactions.map(a => {
        if (a.reason == 'salt_redemption' && a.amount == this.props.loanData.saltRedeemed) {
          hasSaltTx = a.status || 'unconfirmed'
        }
      })
      if (hasSaltTx) {
        return true
      }
      return false
    })

  closeUnit21 = () => {
    this.props.navigation.pop()
  }

  render() {
    const isBusiness = this.props.loanData.jurisdiction?.type == 'business' || false

    let title = 'Information'
    if (this.state.getControlInfo) title = 'Control User Information'
    if (isBusiness && !this.state.getControlInfo) title = 'Business Information'
    let useRewardAsPayment = this.state.useRewardAsPayment
    if (useRewardAsPayment === null) {
      title = 'Stackwise'
    }

    return (
      <View
        style={{
          flex: 1,
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
          zIndex: 60,
        }}
      />
    )
  }
}

Unit21Flow.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  tokenPrices: state.user.prices,
  user: state.user.user,
  accountRef: state.auth.account.ref,
  unit21RefreshCount: state.user.unit21RefreshCount,
  WebService: state.auth.WebService,
  pauseUnit21: state.user.pauseUnit21,
  launchDarkly: state.launchDarkly,
  merchantTos: state.user.merchantTos,
})

export default connect(mapStateToProps)(Unit21Flow)
