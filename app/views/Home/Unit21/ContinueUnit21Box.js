import React, {Component} from 'react'
import {View, Text, StyleSheet, Image, TouchableOpacity} from 'react-native'
import {connect} from 'react-redux'

import {Button, TextReg, Card} from '../../../components'
import styles from '../styles'
import commonStyles from '../../../styles/commonStyles'

class ContinueUnit21Box extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  render() {
    let verifyPayout = false
    let {loanData} = this.props
    if (loanData?.depositBankAccount?.address && !loanData?.depositBankAccount?.verifiedAt) {
      verifyPayout = true
    }

    let text = 'Almost there!'
    let subText = 'You just have a couple more steps to complete in order to recieve your loan funds. Let’s keep going!'
    let buttonText = 'NEXT'
    if (verifyPayout) {
      text = 'Verify your payout wallet address!'
      subText = `Click 'verify' to review and verify your payout stablecoin address.`
      buttonText = 'VERIFY'
    }

    return (
      <Card marginTop={10} cardMarginBottom={10}>
        <View style={{alignSelf: 'stretch', paddingLeft: 10, paddingRight: 10}}>
          <View style={{flexDirection: 'row'}}>
            {verifyPayout && (
              <Image source={require('../../../imgs/statusWarn.png')} style={{height: 30, width: 35, marginRight: 10, marginTop: 26}} />
            )}
            <TextReg
              style={{
                fontSize: 24,
                marginTop: 12,
                marginBottom: 10,
                color: '#fff',
              }}>
              {text}
            </TextReg>
          </View>
          <TextReg style={{fontSize: 18, marginBottom: 18, color: '#fff'}}>{subText}</TextReg>
          <TouchableOpacity
            style={{...commonStyles.button, alignSelf: 'stretch', marginBottom: 10}}
            onPress={() => this.props.continueUnit21(verifyPayout)}>
            <TextReg style={{color: '#000', letterSpacing: 1.2, fontSize: 20}}>{buttonText}</TextReg>
          </TouchableOpacity>
        </View>
      </Card>
    )
  }
}

ContinueUnit21Box.heapOptions = {
  eventProps: {include: ['title'], exclude: ['children']},
}

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  user: state.user.user,
})

export default connect(mapStateToProps)(ContinueUnit21Box)
