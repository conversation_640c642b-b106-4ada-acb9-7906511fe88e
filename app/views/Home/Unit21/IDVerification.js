import React, {Component} from 'react'
import {View, Image, TouchableOpacity, Dimensions, Platform, NativeModules} from 'react-native'
import {connect} from 'react-redux'
import * as ImagePicker from 'react-native-image-picker'
//import {launchSocureDocV} from '@socure-inc/docv-react-native'

import Unit21Progress from './Unit21Progress'
const {height: ScreenHeight} = Dimensions.get('window')
import {BackgroundHeader, TextReg, Button} from '../../../components'
import accountPersonal from '../../../imgs/accountPersonal.png'
import removeFileImg from '../../../imgs/closeX.png'
import {askingForPermissions} from '../../../store/auth/auth.actions'

import styles from '../styles'

class IDVerification extends Component {
  constructor(props) {
    super(props)
    this.state = {
      idRetry: false,
    }
  }

  componentDidMount() {
    //this.launchSocure()
  }

  /*
  launchSocure = () => {
    let flow = null
    launchSocureDocV(
      '********-13c5-4862-8aa4-fde9d61dd412',
      flow,
      res => {
        console.log('id1 res', res)
        this.props.navigation.navigate('Unit21Bankruptcy')
      },
      err => {
        console.log('id1 err', err)
      },
    )
  }
  */

  next = () => {
    //this.launchSocure()
  }

  retry = () => {
    this.setState({idRetry: true, files: [], selfieData: {}})
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    this.props.navigation.pop(stackDepth)
  }

  render() {
    let stackDepth = this.props.route?.params?.stackDepth || 1

    return (
      <View
        style={{
          //flex: 1,
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
          paddingBottom: 80,
        }}>
        <BackgroundHeader
          title={'Verification'}
          close
          closeFn={this.closeUnit21}
          goBack={() => (stackDepth <= 2 ? this.closeUnit21() : this.props.navigation.goBack())}
        />
        <View style={{marginLeft: 30, marginRight: 30, alignSelf: 'stretch'}}>
          <Unit21Progress complete={9} />
        </View>
        <View style={styles.unit21IDVerificationHeaderBox}>
          <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
            {this.state.idRetry && (
              <View style={styles.convertErrorBox}>
                <TextReg style={styles.showErrorText}>Error with validation, Please retry.</TextReg>
              </View>
            )}

            <Button style={{alignSelf: 'stretch', marginBottom: 30}} onPress={() => this.next()}>
              <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
            </Button>
          </View>
        </View>
      </View>
    )
  }
}

IDVerification.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  tokenPrices: state.user.prices,
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(IDVerification)
