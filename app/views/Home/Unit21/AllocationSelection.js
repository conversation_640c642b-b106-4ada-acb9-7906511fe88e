import React, {Component} from 'react'
import {View, ScrollView, Image, TouchableOpacity} from 'react-native'
import {connect} from 'react-redux'

import {
  numberWithCommas,
  getBaseRate,
  getPayment,
  getRewardRateMap,
  createMiniAmSchedule,
  getTotalInterest,
  getTotalRewards,
  getAprForRewards,
} from '../../../util/helpers'
import {BackgroundHeader, TextReg, TextBold, Button} from '../../../components'
import {increaseUnit21Refresh, increaseRefreshDataCount} from '../../../store/user/user.actions'

import styles from '../styles'

class AllocationSelection extends Component {
  constructor(props) {
    super(props)
    this.state = {
      aprFloors: null,
      baseRates: null,
      rewardRates: null,
      rewardCeilings: null,
      allocationPick: 'apply',
      loadingAllocation: false,
    }
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    this.props.navigation.pop(stackDepth)
  }

  componentDidMount() {
    this.getLoanRatesMaps()
  }

  getLoanRatesMaps = () => {
    this.props.WebService.getLoanRatesMaps()
      .then(res => {
        this.setState({
          baseRates: res.data?.baseRates || null,
          rewardRates: res.data?.rewardRates || null,
        })
      })
      .catch(err => {
        console.log('getLoanRatesMaps err', err)
      })
  }

  pickAllocation = allocationPick => {
    this.setState({allocationPick})
  }

  submitAllocation = () => {
    let {allocationPick} = this.state
    let useRewardAsPayment = allocationPick == 'apply'
    let id = this.props?.loanData?.id
    this.setState({loadingAllocation: true})
    this.props.WebService.updateRewardAllocation(id, {useRewardAsPayment})
      .then(res => {
        this.props.dispatch(increaseRefreshDataCount())
        this.setState({loadingAllocation: false})
        this.props.dispatch(increaseUnit21Refresh())
      })
      .catch(err => {
        this.setState({loadingAllocation: false})
        console.log('updateRewardAllocation err', err)
      })
  }

  render() {
    let {baseRates, rewardRates} = this.state
    let termLength = this.props.loanData?.term
    let selectedLTV = this.props.loanData?.baseLTV
    let isInterestOnly = this.props.loanData?.interestOnly

    const monthsToMilli = ********** * termLength
    let loanAmount = Number(this.props.loanData?.amount)
    let baseRate = getBaseRate(selectedLTV, baseRates, termLength)
    baseRate = Math.round(baseRate * 1000000) / 1000000

    //payment
    let payment = getPayment(baseRate, loanAmount, monthsToMilli, isInterestOnly)
    console.log('payment', payment)
    payment = payment.toFixed(2)
    let showMonthly = numberWithCommas(payment)

    let isLeveraged = this.props?.user?.accounts?.filter(a => a.ref == this.props.accountRef)[0]
      ?.isLeveraged
    let ldNon = this.props.launchDarkly['non-leveraged-loans'] || false
    if (!ldNon) {
      isLeveraged = true
    }

    // apr
    let {decimalApr, showRewardRate, showTotalRewards, totalRewards} = getAprForRewards(
      loanAmount,
      selectedLTV,
      isInterestOnly,
      termLength,
      baseRate,
      rewardRates,
      isLeveraged,
    )

    console.log(
      'allocations',
      loanAmount,
      selectedLTV,
      isInterestOnly,
      termLength,
      baseRate,
      rewardRates,
    )

    showTotalRewards = numberWithCommas(showTotalRewards)
    console.log('showTotalRewards', showTotalRewards)
    const apr = decimalApr.toFixed(6)
    const showApr = Number(apr * 100)
      .toFixed(3)
      .slice(0, -1)

    let showPaymentMinusReward = getPayment(apr, loanAmount, monthsToMilli, isInterestOnly)
    showPaymentMinusReward = numberWithCommas(showPaymentMinusReward.toFixed(2))

    return (
      <View
        style={{
          flex: 1,
          backgroundColor: '#28283D',
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader title={'Verification'} close closeFn={this.closeUnit21} />

        <ScrollView
          style={{
            alignSelf: 'stretch',
            paddingLeft: 20,
            paddingRight: 20,
            flex: 1,
          }}>
          <View style={{alignSelf: 'stretch'}}>
            <TextReg
              style={{
                alignSelf: 'stretch',
                fontSize: 20,
                marginTop: 10,
                marginBottom: 10,
              }}>
              {'Allocation Selection'}
            </TextReg>
            <TextReg style={{marginBottom: 16}}>
              StackWise is a rewards program that allocates a portion of your interest payments and
              applies them back to your loan. Choose between direct crypto deposits to your
              collateral wallet or apply your rewards directly to your monthly payments.
            </TextReg>
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => this.pickAllocation('apply')}
              style={{
                height: 124,
                alignSelf: 'stretch',
                borderColor: this.state.allocationPick == 'apply' ? '#00FFBD' : '#777',
                borderWidth: this.state.allocationPick == 'apply' ? 3 : 2,
                borderRadius: 14,
                justifyContent: 'center',
                marginBottom: 10,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-around',
                }}>
                <Image
                  source={require('../../../imgs/graphics/noLoanImg.png')}
                  style={{
                    height: 54,
                    width: 54,
                    opacity: this.state.allocationPick == 'apply' ? 1 : 0.5,
                  }}
                />
                <View
                  style={{
                    width: 240,
                    opacity: this.state.allocationPick == 'apply' ? 1 : 0.7,
                    marginLeft: -10,
                  }}>
                  <TextBold style={{marginBottom: 4}}>Apply Rewards to Monthly Payments</TextBold>
                  <View
                    style={{
                      alignSelf: 'stretch',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginRight: 10,
                      marginBottom: 2,
                    }}>
                    <TextReg>APR:</TextReg>
                    <TextBold>{`${showApr}%`}</TextBold>
                  </View>
                  <View
                    style={{
                      alignSelf: 'stretch',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginRight: 10,
                      marginBottom: 2,
                    }}>
                    <TextReg>Lower Payment:</TextReg>
                    <TextBold>{`$${showPaymentMinusReward}`}</TextBold>
                  </View>
                  <View
                    style={{
                      alignSelf: 'stretch',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginRight: 10,
                    }}>
                    <TextReg>Total Crypto Rewards:</TextReg>
                    <TextBold>$0.00</TextBold>
                  </View>
                </View>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              activeOpacity={1}
              onPress={() => this.pickAllocation('deposit')}
              style={{
                height: 124,
                alignSelf: 'stretch',
                borderColor: this.state.allocationPick == 'deposit' ? '#00FFBD' : '#777',
                borderWidth: this.state.allocationPick == 'deposit' ? 3 : 2,
                borderRadius: 14,
                justifyContent: 'center',
                marginBottom: 10,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-around',
                }}>
                <Image
                  source={require('../../../imgs/icons/dark/loans.png')}
                  style={{
                    height: 50,
                    width: 50,
                    marginLeft: 4,
                    opacity: this.state.allocationPick == 'deposit' ? 1 : 0.5,
                  }}
                />
                <View
                  style={{
                    width: 240,
                    opacity: this.state.allocationPick == 'deposit' ? 1 : 0.7,
                    marginLeft: -10,
                  }}>
                  <TextBold style={{marginBottom: 8}}>Deposit Rewards Into My Wallet</TextBold>
                  <View
                    style={{
                      alignSelf: 'stretch',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginRight: 10,
                      marginBottom: 2,
                    }}>
                    <TextReg>APR:</TextReg>
                    <TextBold>{`${showApr}%`}</TextBold>
                  </View>
                  <View
                    style={{
                      alignSelf: 'stretch',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginRight: 10,
                      marginBottom: 2,
                    }}>
                    <TextReg>Higher Payment:</TextReg>
                    <TextBold>{`$${showMonthly}`}</TextBold>
                  </View>
                  <View
                    style={{
                      alignSelf: 'stretch',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginRight: 10,
                    }}>
                    <TextReg>Total Crypto Rewards:</TextReg>
                    <TextBold>{`$${showTotalRewards}`}</TextBold>
                  </View>
                </View>
              </View>
            </TouchableOpacity>
            <TextReg style={{fontSize: 12, opacity: 0.9}}>
              *We must estimate the finance charge, total of payments, the payment schedule and the
              annual percentage rate because we do not know exactly when the applicable loan
              proceeds will be disbursed. Our estimate is based on a loan origination on the 15th
              calendar day of the month.
            </TextReg>
          </View>

          <Button
            isLoading={this.state.loadingAllocation}
            style={{
              alignSelf: 'stretch',
              marginTop: 40,
              marginBottom: 80,
            }}
            onPress={() => this.submitAllocation()}>
            <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
          </Button>
        </ScrollView>
      </View>
    )
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  loanData: state.user.loanData || {},
  user: state.user.user,
  launchDarkly: state.launchDarkly,
  accountRef: state.auth.account.ref,
  account: state.auth.account,
})

export default connect(mapStateToProps)(AllocationSelection)
