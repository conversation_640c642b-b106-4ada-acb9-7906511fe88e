import React, {Component} from 'react'
import {View, Image, Dimensions, Linking, ScrollView} from 'react-native'
import {connect} from 'react-redux'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'

const {height: ScreenHeight} = Dimensions.get('window')
import {BackgroundHeader, TextReg, Button, TextBold} from '../../../components'
import Unit21Progress from './Unit21Progress'
import FlowL1Address from './flowL1/FlowL1Address'

import styles from '../styles'

class L1Flow extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    this.props.navigation.pop(stackDepth)
  }

  next = () => {
    console.log('next')
  }

  render() {
    let l1Step = this.props.route?.params?.l1Step
    return (
      <View
        style={{
          flex: 1,
          backgroundColor: '#28283D',
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader title={'Verification'} close closeFn={this.closeUnit21} goBack={() => this.props.navigation.goBack()} />
        <KeyboardAwareScrollView
          nestedScrollEnabled
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
            alignSelf: 'stretch',
            flexDirection: 'column',
          }}
          contentContainerStyle={{
            justifyContent: 'space-between',
            minHeight: ScreenHeight - 210,
          }}
          enableResetScrollToCoords={false}
          extraScrollHeight={24}>
          <View style={{alignItems: 'center'}}>
            <Unit21Progress complete={1} />
            <FlowL1Address />
          </View>
          <View style={{alignItems: 'center'}}>
            <Button style={{alignSelf: 'stretch', marginBottom: 30}} onPress={() => this.next()}>
              <TextReg style={{color: '#000', fontSize: 20, letterSpacing: 1.8}}>NEXT</TextReg>
            </Button>
          </View>
        </KeyboardAwareScrollView>
      </View>
    )
  }
}

L1Flow.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
  gesturesEnabled: false,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  tokenPrices: state.user.prices,
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(L1Flow)
