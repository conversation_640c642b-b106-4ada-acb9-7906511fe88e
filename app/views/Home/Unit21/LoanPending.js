import React, {Component} from 'react';
import {View, Image, Dimensions, Linking} from 'react-native';
import {connect} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';

const {height: ScreenHeight} = Dimensions.get('window');

import {BackgroundHeader, TextReg, Button} from '../../../components';
import {pauseUnit21} from '../../../store/user/user.actions';
import docMagnify from '../../../imgs/unit21/docMagnify.png';

import styles from '../styles';

class LoanPending extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1;
    this.props.navigation.pop(stackDepth);
  };

  contactUs = () => {
    //Linking.openURL('mailto:<EMAIL>');
    Linking.openURL('https://saltlending.com/contact-us/');
  };

  render() {
    return (
      <View
        style={{
          //flex: 1,
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
          paddingBottom: 80,
        }}>
        <BackgroundHeader title={' '} close closeFn={this.closeUnit21} />
        <View style={styles.unit21IDVerificationHeaderBox}>
          <View style={{alignItems: 'center'}}>
            <Image
              source={docMagnify}
              style={{
                height: 54,
                width: 50,
                marginBottom: 14,
              }}
            />
            <TextReg
              style={{
                color: '#FFF',
                fontSize: 20,
                marginBottom: 14,
                marginTop: 10,
                width: 220,
                textAlign: 'center',
              }}>
              Loan Payout Pending
            </TextReg>

            <TextReg
              style={{
                color: '#FFF',
                fontSize: 16,
                marginBottom: 10,
                textAlign: 'center',
              }}>
              Your compliance review is still processing. Please allow up to 48 hours for your compliance review to be completed and for
              your funds to be paid out.
            </TextReg>
          </View>
          <View style={{alignItems: 'center'}}>
            <TextReg style={{color: '#FFF', marginBottom: 8}}>Have a question in the meantime?</TextReg>
            <TextReg style={{color: '#FFF', marginBottom: 10}}>Get it touch with us.</TextReg>
            <Button style={{alignSelf: 'stretch', marginBottom: 30}} onPress={() => this.contactUs()}>
              <TextReg style={{color: '#000', fontSize: 18}}>CONTACT US</TextReg>
            </Button>
          </View>
        </View>
      </View>
    );
  }
}

LoanPending.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
});

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  tokenPrices: state.user.prices,
  WebService: state.auth.WebService,
});

export default connect(mapStateToProps)(LoanPending);
