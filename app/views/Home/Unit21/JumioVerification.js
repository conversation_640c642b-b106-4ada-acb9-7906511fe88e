import React, {Component} from 'react'
import {
  View,
  Image,
  TouchableOpacity,
  Dimensions,
  Platform,
  NativeModules,
  NativeEventEmitter,
} from 'react-native'
import {connect} from 'react-redux'

const {JumioMobileSDK} = NativeModules
import {PERMISSIONS, request, check, RESULTS} from 'react-native-permissions'

const {height: ScreenHeight} = Dimensions.get('window')
import {BackgroundHeader, TextReg, TextBold, Button} from '../../../components'
import accountPersonal from '../../../imgs/accountPersonal.png'
import removeFileImg from '../../../imgs/closeX.png'
import {askingForPermissions} from '../../../store/auth/auth.actions'
import {increaseRefreshDataCount, increaseUnit21Refresh} from '../../../store/user/user.actions'
import {navigateInvestments} from '../../Investments/helpers'

import styles from '../styles'
import {navigateRefinanceLoan} from '../../Loans/Refinance/helpers'

class JumioVerification extends Component {
  constructor(props) {
    super(props)

    let forInvestment = this.props.route?.params?.forInvestment || false

    let photoIdVerificationStatus = forInvestment
      ? this.props?.lendData?.verification?.photoIdVerificationStatus
      : this.props?.loanData?.verification?.photoIdVerificationStatus

    console.log('this.props?.loanData?.verification', this.props?.loanData?.verification)

    let thisAccount =
      this.props.user?.accounts?.filter(a => a.ref == this.props.accountRef)[0] || []

    this.state = {
      photoIdVerificationStatus,
      loading: photoIdVerificationStatus == 'pending' || false,
      error: false,
      productID:
        thisAccount?.productType == 'exchange'
          ? this.props.loanData?.exchangeId
          : forInvestment
          ? this.props.lendData?.id
          : this.props.loanData?.id,
    }
    this.polling = null
  }

  componentDidMount() {
    this.startJumioListener()
    if (this.state.photoIdVerificationStatus) {
      this.polling = setInterval(this.checkStatus, 20000)
    }
  }
  componentWillUnmount() {
    clearInterval(this.polling)
  }

  checkStatus = () => {
    let thisAccount =
      this.props.user?.accounts?.filter(a => a.ref == this.props.accountRef)[0] || []

    if (thisAccount?.productType == 'exchange') {
      //exchange endpoint
      this.props.WebService.getSaltAccount().then(res => {
        let exchangeAcc = res.data?.filter(a => a.productType == 'exchange')[0]
        let photoIdVerificationStatus =
          exchangeAcc?.product?.exchange?.verification?.photoIdVerificationStatus
        if (photoIdVerificationStatus == 'required' || photoIdVerificationStatus == 'pending') {
          //keep trying
        }
        if (
          photoIdVerificationStatus == 'passed' ||
          photoIdVerificationStatus == 'failed' ||
          photoIdVerificationStatus == 'skip'
        ) {
          //go next
          this.setState({loading: false})
          clearInterval(this.polling)
          this.props.dispatch(increaseUnit21Refresh())
        }
        if (photoIdVerificationStatus == 'retry') {
          this.setState({loading: false, error: true})
        }
      })
    } else if (thisAccount?.productType == 'investment') {
      //investment endpoint
      //loan endpoint
      this.props.WebService.getInvestments().then(res => {
        if (res.data?.length > 0) {
          const lendData = res.data[0]
          let photoIdVerificationStatus = lendData?.verification?.photoIdVerificationStatus
          if (photoIdVerificationStatus == 'required' || photoIdVerificationStatus == 'pending') {
            //keep trying
          }
          if (photoIdVerificationStatus == 'passed' || photoIdVerificationStatus == 'failed') {
            //go next
            this.setState({loading: false})
            clearInterval(this.polling)
            navigateInvestments(lendData, thisAccount, this.props.user, this.props.navigation)
          }
          if (photoIdVerificationStatus == 'retry') {
            this.setState({loading: false, error: true})
          }
        }
      })
    } else {
      //loan endpoint
      this.props.WebService.getLoans().then(async res => {
        if (res.data?.length > 0) {
          let forRefi = this.props?.route?.params?.forRefi || false
          const loanData = forRefi ? res.data[0]?.refinanceLoan : res.data[0]

          let photoIdVerificationStatus = loanData?.verification?.photoIdVerificationStatus
          if (photoIdVerificationStatus == 'required' || photoIdVerificationStatus == 'pending') {
            //keep trying
          }
          if (photoIdVerificationStatus == 'passed' || photoIdVerificationStatus == 'failed') {
            //go next
            clearInterval(this.polling)
            if (forRefi) {
              await this.props.dispatch(increaseRefreshDataCount())
              await this.props.WebService.getPendingRefinanceLoanRef(this.props?.accountRef)
                .then(async res => {
                  this.setState({loading: false})
                  navigateRefinanceLoan(
                    this.props.currentAccount,
                    this.props.user,
                    this.props.navigation,
                    res?.data,
                  )
                })
                .catch(err => {
                  this.setState({loading: false})
                  console.log('err', err)
                })
            } else {
              this.setState({loading: false})
              this.props.dispatch(increaseUnit21Refresh())
            }
          }
          if (photoIdVerificationStatus == 'retry') {
            this.setState({loading: false, error: true})
          }
        }
      })
    }
  }

  startJumioListener = () => {
    let forInvestment = this.props.route?.params?.forInvestment || false

    const emitterJumio = new NativeEventEmitter(JumioMobileSDK)
    emitterJumio.addListener('EventResult', EventResult => {
      let productID = forInvestment ? this.props.lendData?.id : this.props.loanData?.id
      this.props.WebService.jumioFormComplete(productID)
        .then(res => {
          console.log('jumioFormComplete res', res)
        })
        .catch(err => {
          console.log('jumioFormComplete err', err)
        })
      //on success - start polling for results
      this.polling = setInterval(this.checkStatus, 20000)
    })
    emitterJumio.addListener(
      'EventError',
      EventError => {
        console.log('')
        console.log('EventError: ' + JSON.stringify(EventError), EventError)
        //this.setState({error: true, loading: false})
      },
      //on error show error and make redo jumio
    )
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    this.props.navigation.pop(stackDepth)
  }

  getJumioCreds = async () => {
    this.props.dispatch(askingForPermissions(true))
    this.setState({error: false, loading: true})

    //android get permissions
    if (Platform.OS != 'ios') {
      const result = await check(PERMISSIONS.ANDROID.CAMERA)
      if (result == 'denied') {
        await request(PERMISSIONS.ANDROID.CAMERA)
      }
    }

    let productID = this.state.productID
    this.props.WebService.getJumioCreds(productID)
      .then(res => {
        this.startJumio(res.data.sdkToken)
      })
      .catch(err => {
        console.log('err', err)
        this.setState({error: true})
      })
  }

  startJumio = jumioToken => {
    JumioMobileSDK.initialize(jumioToken, 'US')
    JumioMobileSDK.start()
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    this.props.navigation.pop(stackDepth)
  }

  render() {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    let forInvestment = this.props.route?.params?.forInvestment || false

    return (
      <View
        style={{
          //flex: 1,
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
          paddingBottom: 80,
        }}>
        <BackgroundHeader
          title={'Verification'}
          close={forInvestment ? false : true}
          closeFn={this.closeUnit21}
          goBack={() => (stackDepth <= 2 ? this.closeUnit21() : this.props.navigation.goBack())}
        />
        <View style={styles.unit21IDVerificationHeaderBox}>
          <View style={{marginTop: 30, alignSelf: 'stretch', alignItems: 'center'}}>
            <Image
              style={{
                width: 84,
                height: 90,
                marginTop: 20,
                marginBottom: 24,
              }}
              source={require('../../../imgs/graphics/loanPending.png')}
            />
            <TextReg
              style={{
                color: '#FFF',
                fontSize: 16,
                textAlign: 'center',
                marginBottom: 20,
              }}>
              Your {forInvestment ? 'lend' : 'loan'} request requires an ID Verification review.
              Please continue with our verification flow.
            </TextReg>
            <TextReg
              style={{
                color: '#FFF',
                fontSize: 16,
                textAlign: 'center',
              }}>
              Once submitted, processing will take approximately 30 seconds - 3 minutes.
            </TextReg>
          </View>

          <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
            {this.state.error && (
              <View style={styles.convertErrorBox}>
                <TextReg style={styles.showErrorText}>
                  Error with validation, please retry verification flow.
                </TextReg>
              </View>
            )}
            <Button
              isLoading={this.state.loading}
              style={{alignSelf: 'stretch', marginBottom: 30}}
              onPress={() => this.getJumioCreds()}>
              <TextReg style={{color: '#000', fontSize: 18}}>START</TextReg>
            </Button>
          </View>
        </View>
      </View>
    )
  }
}

JumioVerification.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = (state, props) => {
  const lendData = state.investments.byId[props.route?.params?.id] || {}
  const user = state?.user?.user
  const loanData = props?.route?.params?.forRefi
    ? state.user?.loanData?.refinanceLoan || props?.route?.params?.refinanceLoan || {}
    : state.user.loanData || {}
  return {
    user,
    accountRef: state.auth.account.ref,
    lendData,
    loanData,
    tokenPrices: state.user.prices,
    WebService: state.auth.WebService,
    launchDarkly: state.launchDarkly,
  }
}

export default connect(mapStateToProps)(JumioVerification)
