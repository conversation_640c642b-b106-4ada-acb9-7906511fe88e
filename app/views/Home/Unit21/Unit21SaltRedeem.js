import React, {Component} from 'react';
import {View, TouchableOpacity, Image, Clipboard, Dimensions} from 'react-native';
import {connect} from 'react-redux';

import AsyncStorage from '@react-native-async-storage/async-storage';
import QRCode from 'react-native-qrcode-svg';

import {updateWallets, pauseUnit21, increaseUnit21Refresh} from '../../../store/user/user.actions';
import {showToast} from '../../../store/notifications/notifications.actions';
import {TextReg, TextBold, BackgroundHeader, Button, ConfirmRedeemSalt} from '../../../components';

const {height: ScreenHeight} = Dimensions.get('window');
import styles from '../styles';

class Unit21SaltRedeem extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showCopied: false,
      showConfirm: false,
      loading: false,
    };
    this.inputs = {};
    console.log('construct');
  }

  componentDidMount() {
    this.checkForAddress();
  }

  checkForAddress = async () => {
    const collaterals = this.props.loanData?.collaterals || [];
    console.log('checkForAddress', collaterals);

    const saltWallet = collaterals.filter(a => a.currency == 'SALT')[0];
    if (!saltWallet?.address) {
      await this.props.WebService.createWallet('ETH').then(res => {
        console.log('res eth', res);
        this.props.dispatch(updateWallets(res.data));
      });
      this.props.WebService.createWallet('SALT')
        .then(res => {
          this.props.dispatch(updateWallets(res.data));
        })
        .catch(err => {
          if (!this.props.user.mfaEnabled) {
            this.props.dispatch(showToast(true));
            return;
          }
        });
    }
  };

  copyAddress = address => {
    Clipboard.setString(address);
    this.setState({showCopied: true}, () => {
      setTimeout(() => {
        this.setState({showCopied: false});
      }, 1400);
    });
  };

  submit = () => {
    this.setState({loading: true, error: false});
    const loanId = this.props.loanData?.id || 0;
    this.props.WebService.redeemSalt(loanId)
      .then(res => {
        //this.props.navigation.popToTop()
        this.toggleConfirm();
        //close modal , and or just exit to
        /*
	      this.props.navigation.navigate('LoanPayout', {
	        goBackDisabled: true
	      });
        */
        this.props.dispatch(increaseUnit21Refresh());
      })
      .catch(err => {
        this.setState({loading: false, error: true});
        console.log('redeemSalt err', err);
      });
  };

  toggleConfirm = () => {
    this.setState({showConfirm: !this.state.showConfirm});
  };

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1;
    this.props.navigation.pop(stackDepth);
  };

  render() {
    const status = this.props.route.params?.status || null;
    const collaterals = this.props.loanData?.collaterals || [];
    const saltWallet = collaterals.filter(a => a.currency == 'SALT')[0];
    let saltDeposited = saltWallet.projectedBalance;
    const totalSaltNeeded = this.props.loanData?.saltRedeemed || '0';
    let additionalNeeded = Number(totalSaltNeeded - saltDeposited).toFixed(2);
    if (additionalNeeded <= 0) {
      additionalNeeded = 0;
    }
    let percentageOfTotal = (Number(saltDeposited) / totalSaltNeeded) * (100).toFixed(0);
    if (percentageOfTotal >= 100) {
      percentageOfTotal = 100;
    }

    const saltAddress = saltWallet?.address || ' ';
    const canRedeem = Number(saltDeposited) >= Number(totalSaltNeeded);

    const newApr = (Number(this.props.loanData.apr) * 100).toFixed(2);

    if (status == 'unconfirmed') {
      percentageOfTotal = 100;
      additionalNeeded = 0;
      saltDeposited = totalSaltNeeded;
    }

    if (saltDeposited > totalSaltNeeded) {
      saltDeposited = totalSaltNeeded;
    }
    return (
      <View
        style={{
          //flex: 1,
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
          alignItems: 'center',
        }}>
        <BackgroundHeader title={'Redeem Salt'} close closeFn={this.closeUnit21} />
        <ConfirmRedeemSalt
          showConfirm={this.state.showConfirm}
          showPinScreen={this.props.showPinScreen}
          toggleConfirm={this.toggleConfirm}
          submit={this.submit}
          loading={this.state.loading}
          totalSaltNeeded={totalSaltNeeded}
          newApr={newApr}
        />

        <View
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          <View>
            <TextReg
              style={{
                fontSize: 16,
                marginBottom: 20,
                width: 340,
                marginTop: 10,
                color: '#FFF',
              }}>
              By redeeming SALT, you are{' '}
              <TextReg
                style={{
                  color: '#FFF',
                  textDecorationLine: 'underline',
                  textDecorationStyle: 'solid',
                  textDecorationColor: '#00FFBD',
                }}>
                spending it
              </TextReg>{' '}
              in order to buy down your interest rate. We only accept SALT coming from our on-platform wallet.
            </TextReg>
            <View
              style={{
                width: 340,
                height: 46,
                backgroundColor: '#EEEFF3',
                borderRadius: 14,
                marginBottom: 8,
                overflow: 'hidden',
              }}>
              <View
                style={{
                  height: 46,
                  width: `${percentageOfTotal}%`,
                  backgroundColor: '#00FFBD',
                  borderTopLeftRadius: 4,
                  borderBottomLeftRadius: 4,
                }}
              />
            </View>
            <View
              style={{
                flexDirection: 'row',
                width: 340,
                justifyContent: 'space-between',
                marginBottom: 20,
              }}>
              <View>
                <TextReg style={{color: '#FFF'}}>Already Deposited</TextReg>
                <TextBold style={{color: '#FFF'}}>{saltDeposited} SALT</TextBold>
              </View>
              <View style={{alignItems: 'flex-end'}}>
                <TextReg style={{color: '#FFF'}}>Additional Needed</TextReg>
                <TextBold style={{color: '#FFF'}}>{additionalNeeded} SALT</TextBold>
              </View>
            </View>
            {canRedeem ? (
              <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
                <Image
                  style={{
                    height: 60,
                    width: 60,
                    marginBottom: 30,
                  }}
                  source={require('../../../imgs/loanChecklist/checkMarkBlack.png')}
                />
              </View>
            ) : (
              <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
                <View
                  style={{
                    height: status == 'unconfirmed' ? 130 : 180,
                    width: status == 'unconfirmed' ? 130 : 180,
                    borderRadius: 14,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: 40,
                  }}>
                  {status == 'unconfirmed' ? (
                    <Image
                      style={{
                        height: 120,
                        width: 120,
                      }}
                      source={require('../../../imgs/loanChecklist/checkMarkBlack.png')}
                    />
                  ) : (
                    <View style={styles.depositQrBox}>
                      <QRCode value={saltAddress} size={178} backgroundColor={'#28283D'} color={'#fff'} />
                    </View>
                  )}
                </View>
                <View
                  style={{
                    borderRadius: 14,
                    backgroundColor: '#eef0f0',
                    width: 318,
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginBottom: 30,
                  }}>
                  <TextBold
                    style={{
                      color: '#000',
                      width: 241,
                      marginLeft: 16,
                      marginRight: 16,
                      textAlign: 'center',
                      height: 40,
                      marginTop: 2,
                    }}>{`${saltAddress}`}</TextBold>
                  <View
                    style={{
                      height: 46,
                      width: 46,
                      borderRadius: 14,
                      backgroundColor: this.state.showCopied ? '#00ffc1' : '#00FFBD',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    {this.state.showCopied ? (
                      <Image style={{height: 38, width: 38}} source={require('../../../imgs/checkmark.png')} />
                    ) : (
                      <TouchableOpacity onPress={() => this.copyAddress(saltAddress)}>
                        <Image style={{height: 32, width: 32}} source={require('../../../imgs/copyDepositButton.png')} />
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
              </View>
            )}
          </View>

          {status != 'unconfirmed' && (
            <View
              style={{
                alignSelf: 'stretch',
                alignItems: 'center',
              }}>
              <Button
                disabled={!canRedeem}
                style={{alignSelf: 'stretch', marginBottom: 30}}
                isLoading={this.state.loading}
                onPress={() => this.toggleConfirm()}>
                REDEEM
              </Button>
            </View>
          )}
        </View>
      </View>
    );
  }
}

Unit21SaltRedeem.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
});

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  user: state.user.user,
  WebService: state.auth.WebService,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
});

export default connect(mapStateToProps)(Unit21SaltRedeem);
