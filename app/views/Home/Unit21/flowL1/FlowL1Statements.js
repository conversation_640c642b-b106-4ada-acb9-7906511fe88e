import React, {Component} from 'react'
import {View, TouchableOpacity, Image, TextInput, Keyboard, Animated, Dimensions} from 'react-native'
import {connect} from 'react-redux'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import SwiperFlatList from 'react-native-swiper-flatlist'

let {height: ScreenHeight, width} = Dimensions.get('window')
width -= 60
import {BackgroundHeader, Button, TextReg, TextBold, LocationSelect} from '../../../../components'
import {increaseUnit21Refresh, updateUser} from '../../../../store/user/user.actions'
import Unit21Progress from '../Unit21Progress'
import agreedYes from '../../../../imgs/agreedYes.png'

import styles from '../../styles'

class FlowL1Statements extends Component {
  constructor(props) {
    super(props)

    this.state = {
      acknowledge1: false,
      acknowledge2: false,
      loading: false,
    }

    this.inputs = []
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    this.props.navigation.pop(stackDepth)
  }

  toggleAcknowledge = num => {
    if (num == 1) {
      this.setState({acknowledge1: !this.state.acknowledge1})
    }
    if (num == 2) {
      this.setState({acknowledge2: !this.state.acknowledge2})
    }
  }

  next = async () => {
    /*
    let referenceId = this.props.loanData.verification?.referenceId || '1'
    this.props.WebService.unit21UpdateEntity(referenceId).catch(err => {
      this.retryEntity(1)
    })
    */
    this.props.dispatch(increaseUnit21Refresh())
  }

  render() {
    let stackDepth = this.props.route?.params?.stackDepth || 0
    let {acknowledge1, acknowledge2, loading} = this.state

    return (
      <View
        style={{
          flex: 1,
          backgroundColor: '#28283D',
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader
          title={'Verification'}
          close
          closeFn={this.closeUnit21}
          goBack={() => (stackDepth <= 2 ? this.closeUnit21() : this.props.navigation.goBack())}
        />
        <KeyboardAwareScrollView
          nestedScrollEnabled
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
            alignSelf: 'stretch',
            flexDirection: 'column',
          }}
          contentContainerStyle={{
            justifyContent: 'space-between',
            flexGrow: 1,
          }}
          enableResetScrollToCoords={false}
          extraScrollHeight={24}>
          <View style={{alignItems: 'center'}}>
            <Unit21Progress complete={8} />
            <View style={{flexDirection: 'column', alignSelf: 'stretch'}}>
              <TextBold
                style={{
                  color: '#FFF',
                  fontSize: 22,
                  marginBottom: 16,
                  marginTop: 3,
                  alignSelf: 'stretch',
                  textAlign: 'left',
                }}>
                Please acknowledge the following statements
              </TextBold>

              <TouchableOpacity onPress={() => this.toggleAcknowledge(1)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
                <View style={styles.toggleAcknowledgeView}>
                  <Image
                    source={agreedYes}
                    style={[
                      styles.unit21ToggleCheckImg,
                      {
                        opacity: this.state.acknowledge1 ? 1 : 0,
                      },
                    ]}
                  />
                </View>
                <TextReg style={styles.unit21AcknowledgeTitle}>
                  *I acknowledge that I will not use this loan for any illegal purposes.
                </TextReg>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => this.toggleAcknowledge(2)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
                <View style={styles.toggleAcknowledgeView}>
                  <Image
                    source={agreedYes}
                    style={[
                      styles.unit21ToggleCheckImg,
                      {
                        opacity: this.state.acknowledge2 ? 1 : 0,
                      },
                    ]}
                  />
                </View>
                <TextReg style={styles.unit21AcknowledgeTitle}>*I acknowledge that the source of collateral is my own.</TextReg>
              </TouchableOpacity>
            </View>
          </View>
          <View style={{alignItems: 'center'}}>
            <Button
              style={{alignSelf: 'stretch', marginBottom: 30}}
              disabled={!acknowledge1 || !acknowledge2}
              isLoading={loading}
              onPress={() => this.next()}>
              <TextReg style={{color: '#000', fontSize: 20, letterSpacing: 1.8}}>NEXT</TextReg>
            </Button>
          </View>
        </KeyboardAwareScrollView>
      </View>
    )
  }
}

FlowL1Statements.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  user: state.user.user,
  WebService: state.auth.WebService,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(FlowL1Statements)
