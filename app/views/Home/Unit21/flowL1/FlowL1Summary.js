import React, {Component} from 'react'
import {
  View,
  TouchableOpacity,
  Image,
  TextInput,
  Keyboard,
  Animated,
  Dimensions,
} from 'react-native'
import {connect} from 'react-redux'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'

const {height: ScreenHeight} = Dimensions.get('window')
import {BackgroundHeader, Button, TextReg, TextBold, LocationSelect} from '../../../../components'
import {
  increaseRefreshDataCount,
  increaseUnit21Refresh,
  updateUser,
} from '../../../../store/user/user.actions'
import Unit21Progress from '../Unit21Progress'
import {navigateInvestments} from '../../../Investments/helpers'

import styles from '../../styles'
import {navigateRefinanceLoan} from '../../../Loans/Refinance/helpers'

class FlowL1Summary extends Component {
  constructor(props) {
    super(props)
    let {street1, street2, province, city, postalCode, countryCode} = props.user?.address || ''
    let {govtId, dateOfBirth, countryOfCitizenship} = props.user
    let callingCode = props.user?.phone?.callingCode || '1'
    const {number} = props.user?.phone || ''
    callingCode = `+${callingCode}`
    this.state = {
      callingCode,
      phoneNumber: number,
      countryCode: countryCode || null,
      loading: false,
      street1,
      street2,
      province,
      city,
      postalCode,
      countryCode,
      govtId,
      dateOfBirth,
      countryOfCitizenship,
      errMessage: false,
    }

    this.inputs = []
  }

  componentDidMount() {
    //removeU21
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    stackDepth += 4
    this.props.navigation.pop(stackDepth)
  }

  next = () => {
    this.setState({loading: true, errMessage: false})
    let forInvestment = this.props.route?.params?.forInvestment || false
    let forRefi = this.props.route?.params?.forRefi || false

    let referenceId = forInvestment
      ? this.props.lendData?.verification?.referenceId || '1'
      : this.props.loanData?.verification?.referenceId || '1'

    this.props.WebService.unit21Check1(referenceId)
      .then(res => this.props.WebService.getSaltUser())
      .then(async res => {
        this.props.dispatch(updateUser(res.data))
        this.setState({loading: false})
        if (forRefi) {
          await this.props.dispatch(increaseRefreshDataCount())
          await this.props.WebService.getPendingRefinanceLoanRef(this.props?.currentAccount?.ref)
            .then(async res => {
              navigateRefinanceLoan(
                this.props?.currentAccount,
                this.props?.user,
                this.props?.navigation,
                res?.data,
              )
            })
            .catch(err => {
              console.log('err', err)
            })
        } else if (this.props.lendData?.id) {
          await this.props.WebService.getInvestments().then(async res => {
            const investment = res.data[0]
            navigateInvestments(
              investment,
              this.props.account,
              this.props.user,
              this.props.navigation,
            )
          })
        } else {
          this.props.dispatch(increaseUnit21Refresh())
        }
      })
      .catch(err => {
        this.setState({loading: false})
        console.log('next err', err)
        if (err?.data?.body) {
          this.setState({errMessage: err?.data?.body})
        }
      })
  }

  //<TextReg style={styles.unit21InfoInputTitle}>{'Personal Residential Address'}</TextReg>

  render() {
    let stackDepth = this.props.route?.params?.stackDepth || 0
    let forInvestment = this.props.route?.params?.forInvestment || false
    let forRefi = this.props.route?.params?.forRefi || false
    stackDepth += 5
    let {
      loading,
      street1,
      street2,
      province,
      city,
      postalCode,
      countryCode,
      govtId,
      dateOfBirth,
      countryOfCitizenship,
      callingCode,
      phoneNumber,
    } = this.state

    return (
      <View
        style={{
          flex: 1,
          backgroundColor: '#28283D',
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader
          title={'Verification'}
          close={forInvestment || forRefi ? false : true}
          closeFn={this.closeUnit21}
          goBack={() => (stackDepth <= 2 ? this.closeUnit21() : this.props.navigation.goBack())}
        />
        <KeyboardAwareScrollView
          nestedScrollEnabled
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
            alignSelf: 'stretch',
            flexDirection: 'column',
          }}
          contentContainerStyle={{
            justifyContent: 'space-between',
            flexGrow: 1,
          }}
          enableResetScrollToCoords={false}
          extraScrollHeight={24}>
          <View style={{alignItems: 'center'}}>
            <Unit21Progress complete={6} />
            <View style={{flexDirection: 'column', alignSelf: 'stretch'}}>
              <TextBold
                style={{
                  color: '#FFF',
                  fontSize: 22,
                  marginBottom: 16,
                  marginTop: 3,
                  alignSelf: 'stretch',
                  textAlign: 'left',
                }}>
                Summary
              </TextBold>

              <TextReg
                style={{
                  color: '#FFF',
                  fontSize: 16,
                  marginBottom: 10,
                  textAlign: 'left',
                  alignSelf: 'stretch',
                }}>
                Review your information
              </TextReg>
              <View style={styles.l1ReviewBox}>
                <TextBold style={{fontSize: 20}}>{`Phone #`}</TextBold>
                <TextReg
                  style={{
                    fontSize: 19,
                  }}>{`${callingCode} ${phoneNumber}`}</TextReg>
              </View>
              <View style={styles.l1ReviewBox}>
                <TextBold style={{fontSize: 20}}>{'Address'}</TextBold>
                <View style={{flexDirection: 'column'}}>
                  <TextReg
                    style={{
                      fontSize: 19,
                      textAlign: 'right',
                    }}>{`${street1}`}</TextReg>
                  {street2 != '' && (
                    <TextReg
                      style={{
                        fontSize: 19,
                        textAlign: 'right',
                      }}>{`${street2}`}</TextReg>
                  )}
                  <TextReg
                    style={{
                      fontSize: 19,
                      textAlign: 'right',
                    }}>{`${city}, ${province} ${postalCode}`}</TextReg>
                </View>
              </View>
              <View style={styles.l1ReviewBox}>
                <TextBold style={{fontSize: 20}}>{`Date of Birth`}</TextBold>
                <TextReg style={{fontSize: 19}}>{dateOfBirth}</TextReg>
              </View>
              <View style={styles.l1ReviewBox}>
                <TextBold style={{fontSize: 20}}>{`Country of Citizenship`}</TextBold>
                <TextReg style={{fontSize: 19}}>{countryOfCitizenship}</TextReg>
              </View>
              {govtId != '' && (
                <View style={styles.l1ReviewBox}>
                  <TextBold style={{fontSize: 20}}>{`SSN`}</TextBold>
                  <TextReg style={{fontSize: 19}}>{govtId}</TextReg>
                </View>
              )}
            </View>
          </View>
          <View style={{alignItems: 'center'}}>
            {this.state.errMessage && (
              <View
                style={{
                  alignSelf: 'stretch',
                  alignItems: 'center',
                  marginBottom: 10,
                }}>
                <TextReg style={{color: '#e5705a', fontSize: 18}}>{this.state.errMessage}</TextReg>
              </View>
            )}
            <Button
              style={{alignSelf: 'stretch', marginBottom: 30}}
              isLoading={loading}
              onPress={() => this.next()}>
              <TextReg style={{color: '#000', fontSize: 20, letterSpacing: 1.8}}>NEXT</TextReg>
            </Button>
          </View>
        </KeyboardAwareScrollView>
      </View>
    )
  }
}

FlowL1Summary.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = (state, props) => {
  const lendData = state.investments.byId[props.route?.params?.id] || {}
  const user = state?.user?.user
  const currentAccount = user?.accounts?.find(a => a.ref == state.auth.account.ref) || {}
  const loanData = props?.route?.params?.forRefi
    ? state.user?.loanData?.refinanceLoan || props?.route?.params?.refinanceLoan || {}
    : state.user.loanData || {}

  return {
    lendData,
    loanData,
    user,
    currentAccount,
    account: state.auth.account,
    WebService: state.auth.WebService,
  }
}

export default connect(mapStateToProps)(FlowL1Summary)
