import React, {Component} from 'react'
import {
  View,
  TouchableOpacity,
  Image,
  TextInput,
  Keyboard,
  Animated,
  Dimensions,
} from 'react-native'
import {connect} from 'react-redux'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'

const {height: ScreenHeight} = Dimensions.get('window')
import {BackgroundHeader, Button, TextReg, TextBold, LocationSelect} from '../../../../components'
import {
  increaseRefreshDataCount,
  increaseUnit21Refresh,
  updateUser,
} from '../../../../store/user/user.actions'
import Unit21Progress from '../Unit21Progress'
import agreedYes from '../../../../imgs/agreedYes.png'
import {navigateRefinanceLoan} from '../../../Loans/Refinance/helpers'

import styles from '../../styles'

class FlowL1Funds extends Component {
  constructor(props) {
    super(props)

    let {useOfFunds, isInMilitary} = this.props?.loanData?.loanQuestionnaire
    this.state = {
      loanPurpose: useOfFunds || '',
      loading: false,
    }

    this.inputs = []
  }

  closeUnit21 = () => {
    if (this.props?.route?.params?.forRefi) {
      this.props.navigation.navigate('Home')
    } else {
      let stackDepth = this.props.route?.params?.stackDepth || 1
      this.props.navigation.pop(stackDepth)
    }
  }

  toggleLoanPurpose = num => {
    if (this.state.loanPurpose == num) {
      num = null
    }
    this.setState({loanPurpose: num})
  }

  next = async () => {
    const purposeArr = [
      'debt_consolidation',
      'home_improvement',
      'major_purchase',
      'moving_expenses',
      'digital_asset_reinvestment',
      'real_estate_investment',
    ]
    const params = this.props?.route?.params

    const chosenPurpose = purposeArr[this.state.loanPurpose - 1]
    let loanMilitaryQuestionsPayload = {
      militarySpecification: '',
      useOfFunds: chosenPurpose,
    }
    let loanID = this.props.loanData?.id

    this.setState({loading: true})
    console.log('loanMilitaryQuestionsPayload', loanMilitaryQuestionsPayload, loanID)
    await this.props.WebService.loanMilitaryQuestions(loanMilitaryQuestionsPayload, loanID)
      .then(res => this.props.WebService.getSaltUser())
      .then(async res => {
        this.props.dispatch(updateUser(res.data))
        this.setState({loading: false})
        if (params?.forRefi) {
          await this.props.dispatch(increaseRefreshDataCount())
          await this.props.WebService.getPendingRefinanceLoanRef(this.props?.currentAccount?.ref)
            .then(async res => {
              navigateRefinanceLoan(
                this.props.currentAccount,
                this.props.user,
                this.props.navigation,
                res?.data,
              )
            })
            .catch(err => {
              console.log('err', err)
            })
        } else {
          this.props.dispatch(increaseUnit21Refresh())
        }
      })
      .catch(err => {
        console.log('patchUser err', err)
        this.setState({loading: false})
      })
  }

  //<TextReg style={styles.unit21InfoInputTitle}>{'Personal Residential Address'}</TextReg>

  render() {
    let stackDepth = this.props.route?.params?.stackDepth || 0
    let {loading} = this.state
    const purposeArr = [
      'debt_consolidation',
      'home_improvement',
      'major_purchase',
      'moving_expenses',
      'digital_asset_reinvestment',
      'real_estate_investment',
    ]

    const isActive = num => {
      if (num === 1)
        return this.state?.loanPurpose === 'debt_consolidation' || this.state.loanPurpose === 1
      if (num === 2)
        return this.state?.loanPurpose === 'home_improvement' || this.state.loanPurpose === 2
      if (num === 3)
        return this.state?.loanPurpose === 'major_purchase' || this.state.loanPurpose === 3
      if (num === 4)
        return this.state?.loanPurpose === 'moving_expenses' || this.state.loanPurpose === 4
      if (num === 5)
        return (
          this.state?.loanPurpose === 'digital_asset_reinvestment' || this.state.loanPurpose === 5
        )
      if (num === 6)
        return this.state?.loanPurpose === 'real_estate_investment' || this.state.loanPurpose === 6
      return false
    }

    console.log('this.state.loanPurpose', this.state.loanPurpose)

    return (
      <View
        style={{
          flex: 1,
          backgroundColor: '#28283D',
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader title={'Verification'} close closeFn={this.closeUnit21} />
        <KeyboardAwareScrollView
          nestedScrollEnabled
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
            alignSelf: 'stretch',
            flexDirection: 'column',
          }}
          contentContainerStyle={{
            justifyContent: 'space-between',
            flexGrow: 1,
          }}
          enableResetScrollToCoords={false}
          extraScrollHeight={24}>
          <View style={{alignItems: 'center', marginBottom: 30}}>
            <Unit21Progress complete={1} />
            <View style={{flexDirection: 'column', alignSelf: 'stretch'}}>
              <TextBold
                style={{
                  color: '#FFF',
                  fontSize: 22,
                  marginBottom: 16,
                  marginTop: 3,
                  alignSelf: 'stretch',
                  textAlign: 'left',
                }}>
                What will the funds be used for?
              </TextBold>

              <>
                <TouchableOpacity
                  onPress={() => this.toggleLoanPurpose(1)}
                  activeOpacity={1}
                  style={styles.toggleAcknowledgeButton}>
                  <View style={styles.toggleAcknowledgeView}>
                    <Image
                      source={agreedYes}
                      style={[
                        styles.unit21ToggleCheckImg,
                        {
                          opacity: isActive(1) ? 1 : 0,
                        },
                      ]}
                    />
                  </View>
                  <TextReg style={styles.unit21AcknowledgeTitle}>Debt Consolidation</TextReg>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => this.toggleLoanPurpose(2)}
                  activeOpacity={1}
                  style={styles.toggleAcknowledgeButton}>
                  <View style={styles.toggleAcknowledgeView}>
                    <Image
                      source={agreedYes}
                      style={[
                        styles.unit21ToggleCheckImg,
                        {
                          opacity: isActive(2) ? 1 : 0,
                        },
                      ]}
                    />
                  </View>
                  <TextReg style={styles.unit21AcknowledgeTitle}>Home Improvement</TextReg>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => this.toggleLoanPurpose(3)}
                  activeOpacity={1}
                  style={styles.toggleAcknowledgeButton}>
                  <View style={styles.toggleAcknowledgeView}>
                    <Image
                      source={agreedYes}
                      style={[
                        styles.unit21ToggleCheckImg,
                        {
                          opacity: isActive(3) ? 1 : 0,
                        },
                      ]}
                    />
                  </View>
                  <TextReg style={styles.unit21AcknowledgeTitle}>Major Purchase</TextReg>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => this.toggleLoanPurpose(4)}
                  activeOpacity={1}
                  style={styles.toggleAcknowledgeButton}>
                  <View style={styles.toggleAcknowledgeView}>
                    <Image
                      source={agreedYes}
                      style={[
                        styles.unit21ToggleCheckImg,
                        {
                          opacity: isActive(4) ? 1 : 0,
                        },
                      ]}
                    />
                  </View>
                  <TextReg style={styles.unit21AcknowledgeTitle}>Moving Expenses</TextReg>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => this.toggleLoanPurpose(5)}
                  activeOpacity={1}
                  style={styles.toggleAcknowledgeButton}>
                  <View style={styles.toggleAcknowledgeView}>
                    <Image
                      source={agreedYes}
                      style={[
                        styles.unit21ToggleCheckImg,
                        {
                          opacity: isActive(5) ? 1 : 0,
                        },
                      ]}
                    />
                  </View>
                  <TextReg style={styles.unit21AcknowledgeTitle}>
                    Digital Asset Reinvestment
                  </TextReg>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => this.toggleLoanPurpose(6)}
                  activeOpacity={1}
                  style={styles.toggleAcknowledgeButton}>
                  <View style={styles.toggleAcknowledgeView}>
                    <Image
                      source={agreedYes}
                      style={[
                        styles.unit21ToggleCheckImg,
                        {
                          opacity: isActive(6) ? 1 : 0,
                        },
                      ]}
                    />
                  </View>
                  <TextReg style={styles.unit21AcknowledgeTitle}>Real Estate Investment</TextReg>
                </TouchableOpacity>
              </>
            </View>
          </View>
          <View style={{alignItems: 'center'}}>
            <Button
              style={{alignSelf: 'stretch', marginBottom: 20}}
              disabled={!this.state.loanPurpose}
              isLoading={loading}
              onPress={() => this.next()}>
              <TextReg style={{color: '#000', fontSize: 20, letterSpacing: 1.8}}>NEXT</TextReg>
            </Button>
          </View>
        </KeyboardAwareScrollView>
      </View>
    )
  }
}

FlowL1Funds.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = (state, props) => {
  const user = state?.user?.user
  const currentAccount = user?.accounts?.find(a => a.ref == state.auth.account.ref) || {}
  const forRefi = props?.route?.params?.forRefi
  const loanData = forRefi
    ? state.user?.loanData?.refinanceLoan || props?.route?.params?.refinanceLoan
    : state.user.loanData || {}

  return {
    loanData,
    user,
    currentAccount,
    WebService: state.auth.WebService,
  }
}

export default connect(mapStateToProps)(FlowL1Funds)
