import React, {Component} from 'react'
import {
  View,
  TouchableOpacity,
  Image,
  TextInput,
  Keyboard,
  Animated,
  Dimensions,
} from 'react-native'
import {connect} from 'react-redux'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'

const {height: ScreenHeight} = Dimensions.get('window')
import {BackgroundHeader, Button, TextReg, TextBold, LocationSelect} from '../../../../components'
import {increaseUnit21Refresh, updateUser} from '../../../../store/user/user.actions'
import countryCodes from '../../../../util/countryCodes'

import Unit21Progress from '../Unit21Progress'

import styles from '../../styles'

class FlowL1PhoneNumber extends Component {
  constructor(props) {
    super(props)
    let {street1, street2, province, city, postalCode, countryCode} = props.user?.address || ''
    let {govtId, dateOfBirth, countryOfCitizenship} = props.user
    let callingCode = props.user?.phone?.callingCode || '1'
    const {number} = props.user?.phone || ''
    callingCode = `+${callingCode}`

    this.countryCodesArr = countryCodes()
    this.state = {
      countryCodes: this.countryCodesArr,
      countryCode: '',
      callingCode,
      phoneNumber: number,
      countryCode: countryCode || null,
      loading: false,
    }

    this.inputs = []
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    this.props.navigation.pop(stackDepth)
  }

  updateField = (text, type) => {
    const state = this.state
    if (type == 'dateOfBirth') {
      const textBefore = text
      text = formatDateText(text, state[type])
      if (text?.length != 0 && !text) {
        state.dateNumbersError = true
        text = textBefore
      } else {
        state.dateNumbersError = false
      }
    }
    state[type] = text
    this.setState(state)
  }

  next = () => {
    /*
    const phonePayload = {
      countryCode: this.state.countryCode,
      callingCode: this.state.callingCode,
      number: this.state.phoneNumber,
    };\*/
    let stackDepth = this.props.route?.params?.stackDepth || 0
    let forInvestment = this.props.route?.params?.forInvestment || false
    let forRefi = this.props.route?.params?.forRefi || false
    let investmentId = this.props.route?.params?.id || false

    const phoneCallingCode = this.state.callingCode?.replace('+', '')
    const phoneCleaned = this.state.phoneNumber?.replace('-', '')
    this.setState({loading: true})
    //    this.props.WebService.unit21NewPhone(this.state.countryCode, phoneCallingCode, phoneCleaned)
    const params = forRefi
      ? this.props?.route?.params
      : {
          stackDepth,
          forInvestment,
          id: investmentId,
        }
    let {countryCode} = this.state
    this.props.WebService.unit21NewPhone(countryCode, phoneCallingCode, phoneCleaned)
      .then(res => this.props.WebService.getSaltUser())
      .then(res => {
        this.props.dispatch(updateUser(res.data))
        this.setState({loading: false})

        this.props.navigation.navigate('FlowL1Address', params)
      })
      .catch(err => {
        console.log(' newPhone err', err)
        if (err.data?.body?.error == 'Phone limit reached') {
          const phoneID = this.props.user.phone?.id
          this.props.WebService.deletePhone(phoneID).then(res => {
            console.log('delete phone add new')
            this.props.WebService.unit21NewPhone(countryCode, phoneCallingCode, phoneCleaned)
              .then(res => this.props.WebService.getSaltUser())
              .then(res => {
                this.props.dispatch(updateUser(res.data))
                this.setState({loading: false})
                this.props.navigation.navigate('FlowL1Address', params)
              })
          })
        } else {
          //show error
          this.setState({loading: false})
        }
      })
  }

  onSelectCode = code => {
    let selectedCountry = code.split('  ')[1]
    const countryCode = countryCodes().filter(a => a.name == selectedCountry)[0].code
    let selectedCountryCode = code.split(')')[0]
    selectedCountryCode = selectedCountryCode.substring(1)
    this.setState({callingCode: selectedCountryCode, countryCode})
  }

  //<TextReg style={styles.unit21InfoInputTitle}>{'Personal Residential Address'}</TextReg>

  render() {
    let stackDepth = this.props.route?.params?.stackDepth || 0
    let forInvestment = this.props.route?.params?.forInvestment || false
    let forRefi = this.props.route?.params?.forRefi || false
    let {loading, phoneNumber} = this.state

    let formattedCodes = this.state.countryCodes.map(a => `(${a.dial_code})  ${a.name}`)
    formattedCodes = [`(+1)  United States`].concat(formattedCodes)
    console.log('params: ', this.props.route?.params)
    return (
      <View
        style={{
          flex: 1,
          backgroundColor: '#28283D',
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader
          title={'Verification'}
          close={forInvestment || forRefi ? false : true}
          closeFn={this.closeUnit21}
          goBack={() => (stackDepth <= 2 ? this.closeUnit21() : this.props.navigation.goBack())}
        />
        <KeyboardAwareScrollView
          nestedScrollEnabled
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
            alignSelf: 'stretch',
            flexDirection: 'column',
          }}
          contentContainerStyle={{
            justifyContent: 'space-between',
            flexGrow: 1,
          }}
          enableResetScrollToCoords={false}
          extraScrollHeight={24}>
          <View style={{alignItems: 'center'}}>
            <Unit21Progress complete={3} />
            <View style={{flexDirection: 'column', alignSelf: 'stretch'}}>
              <TextBold
                style={{
                  color: '#FFF',
                  fontSize: 22,
                  marginBottom: 16,
                  marginTop: 3,
                  alignSelf: 'stretch',
                  textAlign: 'left',
                }}>
                Confirm your phone number
              </TextBold>

              <TextReg
                style={{
                  color: '#FFF',
                  fontSize: 16,
                  marginBottom: 20,
                  textAlign: 'left',
                  alignSelf: 'stretch',
                }}>
                Recieve importal collateral updates & warnings
              </TextReg>
              <View
                style={{
                  flexDirection: 'row',
                  alignSelf: 'stretch',
                  justifyContent: 'center',
                  marginBottom: 10,
                  border: '0px solid #fff',
                  borderBottomWidth: 1,
                }}>
                <View style={{width: 100, marginRight: 8, height: 54}}>
                  <LocationSelect
                    style={{
                      width: 100,
                      borderColor: '#ddd',
                      height: 54,
                      backgroundColor: '#474756',
                      borderRadius: 6,
                    }}
                    options={formattedCodes}
                    onSelect={this.onSelectCode}
                    placeholder={this.state.callingCode ? this.state.callingCode : '+1'}
                    newValue={this.state.callingCode ? this.state.callingCode : '+1'}
                  />
                </View>
                <TextInput
                  style={[styles.unit21InfoInput, {flexGrow: 1}]}
                  onChangeText={text => this.updateField(text, 'phoneNumber')}
                  ref={input => (this.inputs.phoneNumber = input)}
                  value={this.state.phoneNumber}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={'(*************'}
                  placeholderTextColor={'#999'}
                  keyboardType={'numeric'}
                  onSubmitEditing={() => Keyboard.dismiss()}
                  keyboardAppearance="dark"
                />
              </View>

              <TextReg
                style={{
                  color: '#999',
                  fontSize: 14,
                  marginBottom: 20,
                  textAlign: 'left',
                  alignSelf: 'stretch',
                  marginTop: 4,
                }}>
                {`Your phone number may be used for customer communications, fraud prevention and two-factor authentication. Standard carrier rates may be charged.`}
              </TextReg>
              <TextReg
                style={{
                  color: '#999',
                  fontSize: 14,
                  marginBottom: 20,
                  textAlign: 'left',
                  alignSelf: 'stretch',
                  marginTop: 4,
                }}>
                {`IMPORTANT INFORMATION ABOUT PROCEDURES FOR OPENING A NEW ACCOUNT: ﻿To help the government fight the funding of terrorism and money laundering activities, Federal law requires all financial institutions to obtain, verify and record information that identifies each person who opens an account. What this means for you: ﻿When you open an account, we will ask for your name, address, date of birth and other information that will allow us to identify you. We may also ask to see your driver's license or other identifying documents.`}
              </TextReg>
            </View>
          </View>
          <View style={{alignItems: 'center'}}>
            <Button
              style={{alignSelf: 'stretch', marginBottom: 30}}
              disabled={!phoneNumber || phoneNumber == ''}
              isLoading={loading}
              onPress={() => this.next()}>
              <TextReg style={{color: '#000', fontSize: 20, letterSpacing: 1.8}}>NEXT</TextReg>
            </Button>
          </View>
        </KeyboardAwareScrollView>
      </View>
    )
  }
}

FlowL1PhoneNumber.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  user: state.user.user,
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(FlowL1PhoneNumber)
