import React, {Component} from 'react'
import {
  View,
  TouchableOpacity,
  Image,
  TextInput,
  Keyboard,
  Animated,
  Dimensions,
} from 'react-native'
import {connect} from 'react-redux'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'

const {height: ScreenHeight} = Dimensions.get('window')
import {BackgroundHeader, Button, TextReg, TextBold, LocationSelect} from '../../../../components'
import {increaseUnit21Refresh, updateUser} from '../../../../store/user/user.actions'
import iso3166 from 'iso-3166-2'
import countryCodes from '../../../../util/countryCodes'
import Unit21Progress from '../Unit21Progress'

import styles from '../../styles'

class FlowL1Social extends Component {
  constructor(props) {
    super(props)
    let {govtId, dateOfBirth, countryOfCitizenship} = props.user

    this.state = {
      social: govtId || '',
      loading: false,
    }

    this.inputs = []
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    stackDepth += 3
    this.props.navigation.pop(stackDepth)
  }

  updateField = (text, type) => {
    const state = this.state
    if (type == 'dateOfBirth') {
      const textBefore = text
      text = formatDateText(text, state[type])
      if (text?.length != 0 && !text) {
        state.dateNumbersError = true
        text = textBefore
      } else {
        state.dateNumbersError = false
      }
    }
    state[type] = text
    this.setState(state)
  }

  next = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    let forInvestment = this.props.route?.params?.forInvestment || false
    let forRefi = this.props.route?.params?.forRefi || false
    let investmentId = this.props.route?.params?.id || false

    let userPatchPayload = {
      govtId: this.state.social,
    }

    if (this.state.social.includes('*')) {
      //do nothing just go next-
      //this page probably doesnt show actually
      this.props.navigation.navigate(
        'FlowL1Summary',
        this.props?.route?.params?.forRefi
          ? this.props?.route?.params
          : {stackDepth, forInvestment, id: investmentId},
      )
      return
    }

    console.log('userPatchPayload', userPatchPayload)

    let params = forRefi
      ? this.props?.route?.params
      : {
          stackDepth,
          forInvestment,
          id: investmentId,
        }
    this.setState({loading: true})
    this.props.WebService.patchUser(userPatchPayload)
      .then(res => this.props.WebService.getSaltUser())
      .then(res => {
        this.props.dispatch(updateUser(res.data))
        this.setState({loading: false})
        this.props.navigation.navigate('FlowL1Summary', params)
      })
      .catch(err => {
        console.log('patchUser err', err)
        this.setState({loading: false})
      })
  }

  //<TextReg style={styles.unit21InfoInputTitle}>{'Personal Residential Address'}</TextReg>

  render() {
    let stackDepth = this.props.route?.params?.stackDepth || 0
    let forInvestment = this.props.route?.params?.forInvestment || false
    let forRefi = this.props.route?.params?.forRefi || false
    stackDepth += 4

    let {loading, social} = this.state

    return (
      <View
        style={{
          flex: 1,
          backgroundColor: '#28283D',
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader
          title={'Verification'}
          close={forInvestment || forRefi ? false : true}
          closeFn={this.closeUnit21}
          goBack={() => (stackDepth <= 2 ? this.closeUnit21() : this.props.navigation.goBack())}
        />
        <KeyboardAwareScrollView
          nestedScrollEnabled
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
            alignSelf: 'stretch',
            flexDirection: 'column',
          }}
          contentContainerStyle={{
            justifyContent: 'space-between',
            flexGrow: 1,
          }}
          enableResetScrollToCoords={false}
          extraScrollHeight={24}>
          <View style={{alignItems: 'center'}}>
            <Unit21Progress complete={6} />
            <View style={{flexDirection: 'column', alignSelf: 'stretch'}}>
              <TextBold
                style={{
                  color: '#FFF',
                  fontSize: 22,
                  marginBottom: 16,
                  marginTop: 3,
                  alignSelf: 'stretch',
                  textAlign: 'left',
                }}>
                Enter your Social Security Number/ITIN
              </TextBold>

              <TextReg
                style={{
                  color: '#FFF',
                  fontSize: 16,
                  marginBottom: 20,
                  textAlign: 'left',
                  alignSelf: 'stretch',
                }}>
                {`Your SSN/ITIN will be submitted to an identity verification service and will not be permanently stored.`}
              </TextReg>
              <TextReg style={styles.unit21InfoInputTitle}>SSN/ITIN</TextReg>
              <TextInput
                style={styles.unit21InfoInput}
                onChangeText={text => this.updateField(text, 'social')}
                ref={input => (this.inputs.social = input)}
                value={this.state.social}
                underlineColorAndroid="transparent"
                blurOnSubmit
                returnKeyType={'next'}
                placeholder={'000000000'}
                placeholderTextColor={'#999'}
                keyboardType={'numeric'}
                onSubmitEditing={() => Keyboard.dismiss()}
                keyboardAppearance="dark"
              />
            </View>
          </View>
          <View style={{alignItems: 'center'}}>
            <Button
              style={{alignSelf: 'stretch', marginBottom: 30}}
              disabled={!(social.length == 9 || social.length == 4)}
              isLoading={loading}
              onPress={() => this.next()}>
              <TextReg style={{color: '#000', fontSize: 20, letterSpacing: 1.8}}>NEXT</TextReg>
            </Button>
          </View>
        </KeyboardAwareScrollView>
      </View>
    )
  }
}

FlowL1Social.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  user: state.user.user,
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(FlowL1Social)
