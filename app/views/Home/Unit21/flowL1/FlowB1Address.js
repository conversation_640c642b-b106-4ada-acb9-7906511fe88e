import React, {Component} from 'react'
import {
  View,
  TouchableOpacity,
  Image,
  TextInput,
  Keyboard,
  Animated,
  Dimensions,
} from 'react-native'
import {connect} from 'react-redux'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'

const {height: ScreenHeight} = Dimensions.get('window')
import {
  BackgroundHeader,
  Button,
  TextReg,
  TextBold,
  LocationSelect,
} from '../../../../components'
import {
  increaseUnit21Refresh,
  updateUser,
} from '../../../../store/user/user.actions'
import iso3166 from 'iso-3166-2'
import countryCodes from '../../../../util/countryCodes'
import Unit21Progress from '../Unit21Progress'

import styles from '../../styles'

class FlowB1Address extends Component {
  constructor(props) {
    super(props)
    let thisAccount =
      this.props.user?.accounts?.filter(
        a => a.ref == this.props.accountRef,
      )[0] || null
    let businessProfile = thisAccount?.businessProfile || null
    let businessAddress = businessProfile?.address || null
    const {street1, street2, city, postalCode} = businessAddress || ''

    let pickableCountryCodes = countryCodes().map(a => a.name)
    pickableCountryCodes = ['United States'].concat(pickableCountryCodes)

    //loan jurisdiction country & province
    let countryCode =
      businessAddress?.countryCode ||
      this.props?.loanData?.jurisdiction?.countryCode
    let province =
      businessAddress?.province || this.props?.loanData?.jurisdiction?.province

    this.state = {
      address1: street1 || '',
      address2: street2 || '',
      city: city || '',
      postal: postalCode || '',
      countryCodes: pickableCountryCodes,
      countrySubs: [],
      selectedCountry: null,
      countryCode: countryCode || null,
      pickableCountrySubs: [],
      selectedProvince: province || null,
      regionCode: null,
      jurisdictionError: false,
      loading: false,
    }

    let countryName
    let pickableCountrySubs = []

    if (countryCode) {
      countryName = countryCodes().filter(a => a.code == countryCode)[0].name
      //this.state.countryCode = countryCode
      this.state.selectedCountry = countryName
      const isoCountry = iso3166.country(countryCode)
      const subArr = Object.values(isoCountry.sub)
      pickableCountrySubs = subArr.map(a => a.name)
      pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
        if (a < b) return -1
        if (b < a) return 1
        return 0
      })
      this.state.pickableCountrySubs = pickableCountrySubs
    }

    if (countryCode && this.state.selectedProvince) {
      const regionCode = this.state.selectedProvince
      const subData = iso3166.subdivision(countryCode, regionCode)
      const selectedProvince = subData?.name || null
      this.state.selectedProvince = selectedProvince
      this.state.regionCode = regionCode
    }

    this.inputs = []
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    stackDepth += 1
    this.props.navigation.pop(stackDepth)
  }

  onSelect = selectedCountry => {
    const countryCode = countryCodes().filter(a => a.name == selectedCountry)[0]
      .code
    const isoCountry = iso3166.country(countryCode)
    let pickableCountrySubs = []
    const subArr = Object.values(isoCountry.sub)
    pickableCountrySubs = subArr.map(a => a.name)
    pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
      if (a < b) return -1
      if (b < a) return 1
      return 0
    })
    let isBusiness = this.props.loanData?.jurisdiction?.type == 'business'
    let jurisdictionError = null
    if (this.props.loanData?.jurisdiction?.countryCode != countryCode) {
      jurisdictionError = 'Must match jurisdiction of request'
    }
    if (!isBusiness) {
      jurisdictionError = null
    }
    this.setState({
      selectedCountry,
      countryCode,
      pickableCountrySubs,
      selectedProvince: null,
      regionCode: null,
      jurisdictionError,
    })
  }

  onProvinceSelect = selectedProvince => {
    const subData = iso3166.subdivision(
      this.state.countryCode,
      selectedProvince,
    )
    const regionCode = subData.regionCode
    let isBusiness = this.props.loanData?.jurisdiction?.type == 'business'
    let jurisdictionError = null
    if (
      this.props.loanData?.jurisdiction?.countryCode == 'US' &&
      this.props.loanData?.jurisdiction?.province != regionCode
    ) {
      jurisdictionError = 'Must match jurisdiction of request'
    }
    if (isBusiness) {
      jurisdictionError = null
    }
    this.setState({
      selectedProvince,
      regionCode,
      jurisdictionError,
    })
  }

  updateField = (text, type) => {
    const state = this.state
    if (type == 'dateOfBirth') {
      const textBefore = text
      text = formatDateText(text, state[type])
      if (text?.length != 0 && !text) {
        state.dateNumbersError = true
        text = textBefore
      } else {
        state.dateNumbersError = false
      }
    }
    state[type] = text
    this.setState(state)
  }

  next = () => {
    let thisAccount =
      this.props.user?.accounts?.filter(
        a => a.ref == this.props.accountRef,
      )[0] || null
    let businessProfile = thisAccount?.businessProfile || null
    const businessAddressID = businessProfile?.address?.id
    const businessAddressPayload = {
      city: this.state.city,
      countryCode: this.state.countryCode,
      document: [],
      postalCode: this.state.postal,
      province: this.state.regionCode,
      street1: this.state.address1,
      street2: this.state.address2,
    }
    /*
    const addressID = this.props.user?.address?.id;
    const addressPayload = {
      city: this.state.city,
      countryCode: this.state.countryCode,
      postalCode: this.state.postal,
      province: this.state.regionCode,
      street1: this.state.address1,
      street2: this.state.address2,
    };
    */

    this.setState({loading: true})

    //this.props.WebService.patchAddress(addressID, addressPayload)
    this.props.WebService.patchAddress(
      businessAddressID,
      businessAddressPayload,
    )
      .then(res => this.props.WebService.getSaltUser())
      .then(res => {
        this.props.dispatch(updateUser(res.data))
        this.setState({loading: false})
        this.props.dispatch(increaseUnit21Refresh())
      })
      .catch(err => {
        console.log('patchAddress err', err)
        this.setState({loading: false})
      })
  }
  //<TextReg style={styles.unit21InfoInputTitle}>{'Personal Residential Address'}</TextReg>

  render() {
    let stackDepth = this.props.route?.params?.stackDepth || 0
    stackDepth += 1
    let {loading} = this.state

    return (
      <View
        style={{
          flex: 1,
          backgroundColor: '#28283D',
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader
          title={'Verification'}
          close
          closeFn={this.closeUnit21}
          goBack={() =>
            stackDepth <= 2
              ? this.closeUnit21()
              : this.props.navigation.goBack()
          }
        />
        <KeyboardAwareScrollView
          nestedScrollEnabled
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
            alignSelf: 'stretch',
            flexDirection: 'column',
          }}
          contentContainerStyle={{
            justifyContent: 'space-between',
            flexGrow: 1,
          }}
          enableResetScrollToCoords={false}
          extraScrollHeight={24}>
          <View style={{alignItems: 'center'}}>
            <Unit21Progress complete={1} />
            <View style={{flexDirection: 'column', alignSelf: 'stretch'}}>
              <TextBold
                style={{
                  color: '#FFF',
                  fontSize: 22,
                  marginBottom: 16,
                  marginTop: 3,
                  alignSelf: 'stretch',
                  textAlign: 'left',
                }}>
                Business Address
              </TextBold>

              <TextReg
                style={{
                  color: '#FFF',
                  fontSize: 16,
                  marginBottom: 20,
                  textAlign: 'left',
                  alignSelf: 'stretch',
                }}>
                This is where your business is located
              </TextReg>
              <TextInput
                style={styles.unit21InfoInput}
                onChangeText={text => this.updateField(text, 'address1')}
                ref={input => (this.inputs.address1 = input)}
                value={this.state.address1}
                underlineColorAndroid="transparent"
                blurOnSubmit
                returnKeyType={'next'}
                placeholder={'Address Line 1'}
                placeholderTextColor={'#999'}
                onSubmitEditing={() => this.inputs.address2.focus()}
                keyboardAppearance="dark"
              />
              <TextInput
                style={styles.unit21InfoInput}
                onChangeText={text => this.updateField(text, 'address2')}
                ref={input => (this.inputs.address2 = input)}
                value={this.state.address2}
                underlineColorAndroid="transparent"
                blurOnSubmit
                returnKeyType={'next'}
                placeholder={'Address Line 2'}
                placeholderTextColor={'#999'}
                onSubmitEditing={() => Keyboard.dismiss()}
                keyboardAppearance="dark"
              />
              <LocationSelect
                options={this.state.countryCodes}
                onSelect={this.onSelect}
                placeholder={
                  this.state.selectedCountry
                    ? this.state.selectedCountry
                    : 'Country'
                }
              />
              <TextInput
                style={styles.unit21InfoInput}
                onChangeText={text => this.updateField(text, 'city')}
                ref={input => (this.inputs.city = input)}
                value={this.state.city}
                underlineColorAndroid="transparent"
                blurOnSubmit
                returnKeyType={'next'}
                placeholder={'City'}
                placeholderTextColor={'#999'}
                onSubmitEditing={() => Keyboard.dismiss()}
                keyboardAppearance="dark"
              />
              <View
                style={{
                  flexDirection: 'row',
                  alignSelf: 'stretch',
                  marginBottom: 10,
                }}>
                <View style={{flex: 1}}>
                  <LocationSelect
                    onSelect={this.onProvinceSelect}
                    options={this.state.pickableCountrySubs}
                    placeholder={
                      this.state.selectedProvince
                        ? this.state.selectedProvince
                        : 'State / Province'
                    }
                    placeholderTextColor={'#999'}
                  />
                </View>
                <View style={{height: 10, width: 10}} />
                <View style={{flex: 1}}>
                  <TextInput
                    style={styles.unit21InfoInputHalf}
                    onChangeText={text => this.updateField(text, 'postal')}
                    ref={input => (this.inputs.postal = input)}
                    value={this.state.postal}
                    underlineColorAndroid="transparent"
                    blurOnSubmit
                    returnKeyType={'next'}
                    placeholder={'Postal Code'}
                    placeholderTextColor={'#999'}
                    onSubmitEditing={() => Keyboard.dismiss()}
                    keyboardAppearance="dark"
                  />
                </View>
              </View>
              {this.state.jurisdictionError && (
                <View
                  style={{
                    alignSelf: 'stretch',
                    alignItems: 'center',
                    marginTop: -20,
                    marginBottom: 10,
                  }}>
                  <TextReg style={{color: '#e5705a', fontSize: 18}}>
                    {this.state.jurisdictionError}
                  </TextReg>
                </View>
              )}
            </View>
          </View>
          <View style={{alignItems: 'center'}}>
            <Button
              style={{alignSelf: 'stretch', marginBottom: 30}}
              isLoading={loading}
              onPress={() => this.next()}>
              <TextReg
                style={{color: '#000', fontSize: 20, letterSpacing: 1.8}}>
                NEXT
              </TextReg>
            </Button>
          </View>
        </KeyboardAwareScrollView>
      </View>
    )
  }
}

FlowB1Address.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  user: state.user.user,
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
})

export default connect(mapStateToProps)(FlowB1Address)
