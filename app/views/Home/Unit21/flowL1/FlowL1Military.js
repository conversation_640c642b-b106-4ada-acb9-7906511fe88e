import React, {Component} from 'react'
import {
  View,
  TouchableOpacity,
  Image,
  TextInput,
  Keyboard,
  Animated,
  Dimensions,
} from 'react-native'
import {connect} from 'react-redux'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import SwiperFlatList from 'react-native-swiper-flatlist'

let {height: ScreenHeight, width} = Dimensions.get('window')
width -= 60
import {BackgroundHeader, Button, TextReg, TextBold, LocationSelect} from '../../../../components'
import {
  increaseRefreshDataCount,
  increaseUnit21Refresh,
  updateLoans,
  updateUser,
} from '../../../../store/user/user.actions'
import Unit21Progress from '../Unit21Progress'
import agreedYes from '../../../../imgs/agreedYes.png'

import styles from '../../styles'
import {navigateRefinanceLoan} from '../../../Loans/Refinance/helpers'
import {derivedStatusMap} from '../../../../util/enumerables'
import {dig} from '../../../../util/helpers'

class FlowL1Military extends Component {
  constructor(props) {
    super(props)

    let {useOfFunds, isInMilitary} = this.props.loanData?.loanQuestionnaire
    this.state = {
      activeMilitary: isInMilitary || false,
      loading: false,
      acknowledge1: false,
      acknowledge2: false,
    }

    this.inputs = []
    this.militaryScrollValue = new Animated.Value(0)
    this.xOffset = 0
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    this.props.navigation.pop(stackDepth)
  }

  toggleAcknowledge = num => {
    if (num == 1) {
      this.setState({acknowledge1: !this.state.acknowledge1})
    }
    if (num == 2) {
      this.setState({acknowledge2: !this.state.acknowledge2})
    }
  }

  onScroll = e => {
    this.xOffset = e.nativeEvent.contentOffset.x

    Animated.timing(this.militaryScrollValue, {
      toValue: this.xOffset,
      duration: 0,
    }).start()
  }

  onScrollEnd = index => {
    let activeMilitary = false
    if (index == 1) {
      activeMilitary = true
    }
    this.setState({activeMilitary})
    //this.showLessPayments(index)
  }

  toggleToInactiveMilitary = () => {
    this.refs.swiper._scrollToIndex(0)
    this.setState({activeMilitary: false})
  }

  toggleToActiveMilitary = () => {
    this.refs.swiper._scrollToIndex(1)
    this.setState({activeMilitary: true})
  }

  next = async () => {
    const params = this.props?.route?.params

    let loanID = this.props.loanData?.id
    let loanMilitaryQuestionsPayload = {
      militarySpecification: '',
      isInMilitary: this.state.activeMilitary,
    }

    this.setState({loading: true})
    await this.props.WebService.loanMilitaryQuestions(loanMilitaryQuestionsPayload, loanID)
      .then(res => this.props.WebService.getSaltUser())
      .then(async res => {
        this.props.dispatch(updateUser(res.data))
        if (params?.forRefi) {
          await this.props.dispatch(increaseRefreshDataCount())
          await this.props.WebService.getPendingRefinanceLoanRef(this.props?.currentAccount?.ref)
            .then(async res => {
              navigateRefinanceLoan(
                this.props.currentAccount,
                this.props.user,
                this.props.navigation,
                res?.data,
              )
            })
            .catch(err => {
              console.log('err', err)
            })
          this.setState({loading: false})
        } else {
          this.setState({loading: false})
          this.props.dispatch(increaseUnit21Refresh())
        }
      })
      .catch(err => {
        console.log('patchUser err', err)
        this.setState({loading: false})
      })
  }

  //<TextReg style={styles.unit21InfoInputTitle}>{'Personal Residential Address'}</TextReg>

  render() {
    const halfWidth = width / 2
    const xInterpolated = this.militaryScrollValue.interpolate({
      inputRange: [0, width],
      outputRange: [0, halfWidth - 4],
    })

    let stackDepth = this.props.route?.params?.stackDepth || 0
    let {loading, acknowledge1, acknowledge2} = this.state

    console.log('route params', this.props.loanData, this.props.user)

    return (
      <View
        style={{
          flex: 1,
          backgroundColor: '#28283D',
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader
          title={'Verification'}
          close
          closeFn={this.closeUnit21}
          goBack={() => (stackDepth <= 2 ? this.closeUnit21() : this.props.navigation.goBack())}
        />
        <KeyboardAwareScrollView
          nestedScrollEnabled
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
            alignSelf: 'stretch',
            flexDirection: 'column',
          }}
          contentContainerStyle={{
            justifyContent: 'space-between',
            flexGrow: 1,
          }}
          enableResetScrollToCoords={false}
          extraScrollHeight={24}>
          <View style={{alignItems: 'center', marginBottom: 20}}>
            <Unit21Progress complete={2} />
            <View style={{flexDirection: 'column', alignSelf: 'stretch'}}>
              <TextBold
                style={{
                  color: '#FFF',
                  fontSize: 22,
                  marginBottom: 20,
                  marginTop: 3,
                  alignSelf: 'stretch',
                  textAlign: 'left',
                }}>
                Are you an active duty member of the United States Military?
              </TextBold>

              <View
                style={{
                  width,
                  height: 44,
                  alignSelf: 'stretch',
                  borderRadius: 25,
                  backgroundColor: '#FFF',
                  marginTop: 8,
                  marginBottom: 14,
                  overflow: 'hidden',
                  //backgroundColor: '#3D3D50',
                }}>
                <Animated.View
                  style={{
                    width: halfWidth - 4,
                    position: 'absolute',
                    backgroundColor: '#00FFBD',
                    height: 38,
                    top: 3,
                    left: 4,
                    borderRadius: 20,
                    alignSelf: 'flex-start',
                    transform: [{translateX: xInterpolated}],
                    zIndex: 10,
                  }}
                />

                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    zIndex: 30,
                  }}>
                  <TouchableOpacity
                    onPress={this.toggleToInactiveMilitary}
                    style={styles.infoToggleOption}>
                    <Animated.Text
                      style={{
                        marginTop: 12,
                        textAlign: 'center',
                        fontSize: 16,
                        color: '#3f4347',
                        fontFamily: 'Europa-Regular',
                      }}>
                      {`NO`}
                    </Animated.Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={this.toggleToActiveMilitary}
                    style={styles.infoToggleOption}>
                    <Animated.Text
                      style={{
                        marginTop: 12,
                        textAlign: 'center',
                        fontSize: 16,
                        color: '#3f4347',
                        fontFamily: 'Europa-Regular',
                      }}>
                      {`YES`}
                    </Animated.Text>
                  </TouchableOpacity>
                </View>
              </View>
              <SwiperFlatList
                ref="swiper"
                onMomentumScrollEnd={this.onScrollEnd}
                index={0}
                scrollEventThrottle={16}
                onScroll={this.onScroll}
                style={{height: 0, opacity: 0}}>
                <View
                  style={{
                    flex: 1,
                    width,
                  }}
                />
                <View
                  style={{
                    flex: 1,
                    width,
                  }}
                />
              </SwiperFlatList>
            </View>
          </View>
          <View style={{alignSelf: 'stretch'}}>
            <TextReg
              style={{
                color: '#FFF',
                fontSize: 18,
                marginBottom: 10,
                marginTop: 3,
                alignSelf: 'stretch',
                textAlign: 'left',
                marginTop: 20,
              }}>
              Please acknowledge the following statements
            </TextReg>
            <TouchableOpacity
              onPress={() => this.toggleAcknowledge(1)}
              activeOpacity={1}
              style={styles.toggleAcknowledgeButton}>
              <View style={styles.toggleAcknowledgeView}>
                <Image
                  source={agreedYes}
                  style={[
                    styles.unit21ToggleCheckImg,
                    {
                      opacity: this.state.acknowledge1 ? 1 : 0,
                    },
                  ]}
                />
              </View>
              <TextReg style={styles.unit21AcknowledgeTitleMilitary}>
                *I acknowledge that I will not use this loan for any illegal purposes.
              </TextReg>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => this.toggleAcknowledge(2)}
              activeOpacity={1}
              style={styles.toggleAcknowledgeButton}>
              <View style={styles.toggleAcknowledgeView}>
                <Image
                  source={agreedYes}
                  style={[
                    styles.unit21ToggleCheckImg,
                    {
                      opacity: this.state.acknowledge2 ? 1 : 0,
                    },
                  ]}
                />
              </View>
              <TextReg style={styles.unit21AcknowledgeTitleMilitary}>
                *I acknowledge that the source of collateral is my own.
              </TextReg>
            </TouchableOpacity>
            <Button
              style={{alignSelf: 'stretch', marginBottom: 30, marginTop: 30}}
              isLoading={loading}
              disabled={!acknowledge1 || !acknowledge2}
              onPress={() => this.next()}>
              <TextReg style={{color: '#000', fontSize: 20, letterSpacing: 1.8}}>NEXT</TextReg>
            </Button>
          </View>
        </KeyboardAwareScrollView>
      </View>
    )
  }
}

FlowL1Military.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = (state, props) => {
  const user = state?.user?.user
  const currentAccount = user?.accounts?.find(a => a.ref == state.auth.account.ref) || {}
  const forRefi = props?.route?.params?.forRefi
  const loanData = forRefi
    ? state.user?.loanData?.refinanceLoan || props?.route?.params?.refinanceLoan || {}
    : state.user.loanData || {}
  return {
    loanData,
    user,
    currentAccount,
    WebService: state.auth.WebService,
  }
}

export default connect(mapStateToProps)(FlowL1Military)
