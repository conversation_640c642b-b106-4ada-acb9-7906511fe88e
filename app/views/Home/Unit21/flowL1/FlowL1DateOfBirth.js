import React, {Component} from 'react'
import {
  View,
  TouchableOpacity,
  Image,
  TextInput,
  Keyboard,
  Animated,
  Dimensions,
} from 'react-native'
import {connect} from 'react-redux'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'

const {height: ScreenHeight} = Dimensions.get('window')
import {BackgroundHeader, Button, TextReg, TextBold, LocationSelect} from '../../../../components'
import {increaseUnit21Refresh, updateUser} from '../../../../store/user/user.actions'
import Unit21Progress from '../Unit21Progress'

import styles from '../../styles'

class FlowL1DateOfBirth extends Component {
  constructor(props) {
    super(props)
    let {govtId, dateOfBirth, countryOfCitizenship} = props.user
    let year = ''
    let month = ''
    let day = ''
    if (dateOfBirth && dateOfBirth.includes('-')) {
      year = dateOfBirth.split('-')[0]
      month = dateOfBirth.split('-')[1]
      day = dateOfBirth.split('-')[2]
      dateOfBirth = `${month}/${day}/${year}`
    }

    this.state = {
      day: day || '',
      month: month || '',
      year: year || '',
      dateOfBirth: dateOfBirth || '',
      loading: false,
    }

    this.inputs = []
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    stackDepth += 1
    this.props.navigation.pop(stackDepth)
  }

  updateField = (text, type) => {
    const state = this.state

    //goin backwards
    /*
    if (type == 'year' && text.length < 1 && state['year']?.length > 0) {
      this.inputs.day.focus();
    }
    */

    state[type] = text
    this.setState(state)

    //going forward
    if (type == 'day' && text.length >= 2) {
      this.inputs.year.focus()
    }
    if (type == 'month' && text.length >= 2) {
      this.inputs.day.focus()
    }
  }

  submitBirthdate = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 0
    let forInvestment = this.props.route?.params?.forInvestment || false
    let forRefi = this.props.route?.params?.forRefi || false
    let investmentId = this.props.route?.params?.id || false

    let {day, month, year} = this.state
    let dateWithDashes = `${year}-${month}-${day}`
    let userPatchPayload = {
      dateOfBirth: dateWithDashes,
    }

    const params = forRefi
      ? this.props?.route?.params
      : {
          stackDepth,
          forInvestment,
          id: investmentId,
        }
    this.setState({loading: true})
    this.props.WebService.patchUser(userPatchPayload)
      .then(res => this.props.WebService.getSaltUser())
      .then(res => {
        this.props.dispatch(updateUser(res.data))
        this.setState({loading: false})
        this.props.navigation.navigate('FlowL1Citizen', params)
      })
      .catch(err => {
        this.setState({loading: false})
        console.log('patchUser err', err)
      })
  }

  //<TextReg style={styles.unit21InfoInputTitle}>{'Personal Residential Address'}</TextReg>

  render() {
    let stackDepth = this.props.route?.params?.stackDepth || 0
    let forInvestment = this.props.route?.params?.forInvestment || false
    let forRefi = this.props.route?.params?.forRefi || false

    stackDepth += 2

    let {loading, day, month, year} = this.state
    let canContinue =
      (day.length == 1 || day.length == 2) &&
      (month.length == 1 || month.length == 2) &&
      year.length == 4

    let dateError = false
    let dateWithDashes = `${year}-${month}-${day}`
    let dateString = new Date(dateWithDashes)
    let dateNow = new Date()
    if (
      (canContinue &&
        (Number(day) < 1 || Number(month) < 1 || Number(day) > 31 || Number(month) > 12)) ||
      (canContinue &&
        (dateString == 'Invalid Date' ||
          dateString?.valueOf() < -2208963600000 ||
          dateString?.valueOf() > dateNow?.valueOf()))
    ) {
      dateError = true
      canContinue = false
    }
    return (
      <View
        style={{
          flex: 1,
          backgroundColor: '#28283D',
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader
          title={'Verification'}
          close={forInvestment || forRefi ? false : true}
          closeFn={this.closeUnit21}
          goBack={() => (stackDepth <= 2 ? this.closeUnit21() : this.props.navigation.goBack())}
        />
        <KeyboardAwareScrollView
          nestedScrollEnabled
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
            alignSelf: 'stretch',
            flexDirection: 'column',
          }}
          contentContainerStyle={{
            justifyContent: 'space-between',
            flexGrow: 1,
          }}
          enableResetScrollToCoords={false}
          extraScrollHeight={24}>
          <View style={{alignItems: 'center'}}>
            <Unit21Progress complete={5} />
            <View style={{flexDirection: 'column', alignSelf: 'stretch'}}>
              <TextBold
                style={{
                  color: dateError ? '#E6705B' : '#FFF',
                  fontSize: 22,
                  marginBottom: 16,
                  marginTop: 3,
                  alignSelf: 'stretch',
                  textAlign: 'left',
                }}>
                Date of Birth
              </TextBold>

              <TextReg
                style={{
                  color: dateError ? '#E6705B' : '#FFF',
                  fontSize: 16,
                  marginBottom: 20,
                  textAlign: 'left',
                  alignSelf: 'stretch',
                }}>
                Your date of birth is checked along with your other personal details to confirm your
                identity.
              </TextReg>
              <View
                style={{
                  flexDirection: 'row',
                  alignSelf: 'stretch',
                  justifyContent: 'center',
                  marginBottom: 10,
                }}>
                <TextInput
                  style={dateError ? styles.unit21InfoInputDateError2 : styles.unit21InfoInputDate}
                  onChangeText={text => this.updateField(text, 'month')}
                  ref={input => (this.inputs.month = input)}
                  value={this.state.month}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={'MM'}
                  placeholderTextColor={'#999'}
                  onSubmitEditing={() => this.inputs.day.focus()}
                  keyboardAppearance="dark"
                  keyboardType={'numeric'}
                />
                <TextInput
                  style={dateError ? styles.unit21InfoInputDateError2 : styles.unit21InfoInputDate}
                  onChangeText={text => this.updateField(text, 'day')}
                  ref={input => (this.inputs.day = input)}
                  value={this.state.day}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={'DD'}
                  placeholderTextColor={'#999'}
                  onSubmitEditing={() => this.inputs.year.focus()}
                  keyboardAppearance="dark"
                  keyboardType={'numeric'}
                />
                <TextInput
                  style={dateError ? styles.unit21InfoInputDateError2 : styles.unit21InfoInputDate}
                  onChangeText={text => this.updateField(text, 'year')}
                  ref={input => (this.inputs.year = input)}
                  value={this.state.year}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={'YYYY'}
                  placeholderTextColor={'#999'}
                  onSubmitEditing={() => this.submitBirthdate()}
                  keyboardAppearance="dark"
                  keyboardType={'numeric'}
                />
              </View>
              {this.state.jurisdictionError && (
                <View
                  style={{
                    alignSelf: 'stretch',
                    alignItems: 'center',
                    marginTop: -20,
                    marginBottom: 10,
                  }}>
                  <TextReg style={{color: '#e5705a', fontSize: 18}}>
                    {this.state.jurisdictionError}
                  </TextReg>
                </View>
              )}
            </View>
          </View>
          <View style={{alignItems: 'center'}}>
            <Button
              style={{alignSelf: 'stretch', marginBottom: 30}}
              disabled={!canContinue}
              isLoading={loading}
              onPress={() => this.submitBirthdate()}>
              <TextReg style={{color: '#000', fontSize: 20, letterSpacing: 1.8}}>NEXT</TextReg>
            </Button>
          </View>
        </KeyboardAwareScrollView>
      </View>
    )
  }
}

FlowL1DateOfBirth.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  user: state.user.user,
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(FlowL1DateOfBirth)
