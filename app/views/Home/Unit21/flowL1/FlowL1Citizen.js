import React, {Component} from 'react'
import {
  View,
  TouchableOpacity,
  Image,
  TextInput,
  Keyboard,
  Animated,
  Dimensions,
} from 'react-native'
import {connect} from 'react-redux'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'

const {height: ScreenHeight} = Dimensions.get('window')
import {BackgroundHeader, Button, TextReg, TextBold, LocationSelect} from '../../../../components'
import {increaseUnit21Refresh, updateUser} from '../../../../store/user/user.actions'
import iso3166 from 'iso-3166-2'
import countryCodes from '../../../../util/countryCodes'
import Unit21Progress from '../Unit21Progress'

import styles from '../../styles'

class FlowL1Citizen extends Component {
  constructor(props) {
    super(props)
    let {govtId, dateOfBirth, countryOfCitizenship} = props.user
    let pickableCountryCodes = countryCodes().map(a => a.name)
    pickableCountryCodes = ['United States'].concat(pickableCountryCodes)

    this.state = {
      countryOfCitizenship: countryOfCitizenship || null,
      selectedCitizenship: null,
      countryCodes: pickableCountryCodes,
      countrySubs: [],
      loading: false,
    }

    if (countryOfCitizenship) {
      const selectedCitizenship = countryCodes().filter(a => a.code == countryOfCitizenship)[0]
        ?.name
      this.state.selectedCitizenship = selectedCitizenship
    } else if (this.state.selectedCountry) {
      this.state.selectedCitizenship = this.state.selectedCountry
      this.state.countryOfCitizenship = countryCode
    }

    this.inputs = []
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    stackDepth += 2
    this.props.navigation.pop(stackDepth)
  }

  onCitizenSelect = selectedCitizenship => {
    const countryOfCitizenship = countryCodes().filter(a => a.name == selectedCitizenship)[0].code
    this.setState({selectedCitizenship, countryOfCitizenship})
  }

  next = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 0
    let forInvestment = this.props.route?.params?.forInvestment || false
    let forRefi = this.props.route?.params?.forRefi || false
    let investmentId = this.props.route?.params?.id || false

    console.log('next', this.state)
    let userPatchPayload = {
      countryOfCitizenship: this.state.countryOfCitizenship,
    }

    let params = forRefi
      ? this.props?.route?.params
      : {
          stackDepth,
          forInvestment,
          id: investmentId,
        }
    this.setState({loading: true})
    this.props.WebService.patchUser(userPatchPayload)
      .then(res => this.props.WebService.getSaltUser())
      .then(res => {
        this.props.dispatch(updateUser(res.data))
        if (this.state.countryOfCitizenship == 'US') {
          this.setState({loading: false})

          this.props.navigation.navigate('FlowL1Social', params)
        } else {
          //this.props.dispatch(increaseUnit21Refresh());
          this.props.navigation.navigate('FlowL1Summary', params)
        }
        //
      })
      .catch(err => {
        console.log('patchUser err', err)
        this.setState({loading: false})
      })
  }

  render() {
    let stackDepth = this.props.route?.params?.stackDepth || 0
    let forInvestment = this.props.route?.params?.forInvestment || false
    let forRefi = this.props.route?.params?.forRefi || false
    stackDepth += 3

    let {loading, countryOfCitizenship} = this.state

    return (
      <View
        style={{
          flex: 1,
          backgroundColor: '#28283D',
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader
          title={'Verification'}
          close={forInvestment || forRefi ? false : true}
          closeFn={this.closeUnit21}
          goBack={() => (stackDepth <= 2 ? this.closeUnit21() : this.props.navigation.goBack())}
        />
        <KeyboardAwareScrollView
          nestedScrollEnabled
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
            alignSelf: 'stretch',
            flexDirection: 'column',
          }}
          contentContainerStyle={{
            justifyContent: 'space-between',
            flexGrow: 1,
          }}
          enableResetScrollToCoords={false}
          extraScrollHeight={24}>
          <View style={{alignItems: 'center'}}>
            <Unit21Progress complete={6} />
            <View style={{flexDirection: 'column', alignSelf: 'stretch'}}>
              <TextBold
                style={{
                  color: '#FFF',
                  fontSize: 22,
                  marginBottom: 16,
                  marginTop: 3,
                  alignSelf: 'stretch',
                  textAlign: 'left',
                }}>
                Country of Citizenship
              </TextBold>

              <TextReg
                style={{
                  color: '#FFF',
                  fontSize: 16,
                  marginBottom: 20,
                  textAlign: 'left',
                  alignSelf: 'stretch',
                }}>
                Country of Citizenship
              </TextReg>
              <LocationSelect
                options={this.state.countryCodes}
                onSelect={this.onCitizenSelect}
                placeholder={
                  this.state.selectedCitizenship
                    ? this.state.selectedCitizenship
                    : 'Country of Citizenship'
                }
              />
            </View>
          </View>
          <View style={{alignItems: 'center'}}>
            <Button
              style={{alignSelf: 'stretch', marginBottom: 30}}
              disabled={!countryOfCitizenship}
              isLoading={loading}
              onPress={() => this.next()}>
              <TextReg style={{color: '#000', fontSize: 20, letterSpacing: 1.8}}>NEXT</TextReg>
            </Button>
          </View>
        </KeyboardAwareScrollView>
      </View>
    )
  }
}

FlowL1Citizen.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  user: state.user.user,
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(FlowL1Citizen)
