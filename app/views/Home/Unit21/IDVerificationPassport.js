import React, {Component} from 'react'
import {View, Image, TouchableOpacity, Dimensions, Platform, ScrollView, NativeModules} from 'react-native'
import {connect} from 'react-redux'
//const { RnSocureSdk } = NativeModules

import * as ImagePicker from 'react-native-image-picker'

const {height: ScreenHeight} = Dimensions.get('window')

import {BackgroundHeader, TextReg, Button, LocationSelect} from '../../../components'
import Unit21Progress from './Unit21Progress'
import passport from '../../../imgs/unit21/passport.png'
import idFront from '../../../imgs/unit21/idFront.png'
import idBack from '../../../imgs/unit21/idBack.png'
import removeFileImg from '../../../imgs/closeX.png'
import {askingForPermissions} from '../../../store/auth/auth.actions'
import {updateLoans} from '../../../store/user/user.actions'

import downArrowBlack from '../../../imgs/downArrowBlack.png'

import styles from '../styles'

class IDVerification extends Component {
  constructor(props) {
    super(props)
    this.state = {
      entityType: '',
      entityTypeList: ['Passport', `Driver's License`],
      selfieData: props?.route?.params?.selfieData || {},
      idV2Data: [],
      isLoading: false,
      idRetry: false,
      scannedLicenseRes: '',
      referenceId: '',
      uuid: '',
      uploadScannedInfo: {},
    }
    this.polling = null
  }

  componentWillUnmount() {
    clearInterval(this.polling)
  }

  next = () => {
    this.props.navigation.navigate('Unit21Bankruptcy')
  }

  onEntitySelect = entityType => {
    console.log('onEntitySelect', entityType)
    this.setState({entityType, idV2Data: []})
  }

  launchSocure = imageType => {
    if (imageType == 'idFront' || imageType == 'idback') {
      //start polling - looking for retry, passed, ect, just not required or what it was before
      this.setState({isLoading: true})

      /*
			RnSocureSdk.scanLicense().then((res) => {
				console.log('RES: ', res)
				this.setState({ scannedLicenseRes: res })
				//uploadRes
				RnSocureSdk.uploadScannedInfo()
					.then((res) => {
						console.log('uploadScannedInfo res', res)
						const { referenceId, uuid } = res
						this.setState({ referenceId, uuid, uploadScannedInfo: res })
						//setUploadReferenceId(res.referenceId);
						//setUploadUuid(res.uuid);
					})
					.catch((error) => {
						//setError(error.message);
					})

				//this.polling = setInterval(this.socurePolling, 10000);
				//navigation.navigate('ScannedInformation', res);
			})
      */
    }
    if (imageType == 'passport') {
      /*
			RnSocureSdk.scanPassport().then((res) => {
				console.log('RES: ', res)
				//this.polling = setInterval(this.socurePolling, 10000);

        //navigation.navigate('ScannedInformation', {
        //  barcode: res.mrz,
        //  type: res.type,
        //});

			})
      */
    }
  }

  openImageSelect = (imageType, verification = false) => {
    this.props.dispatch(askingForPermissions(true))
    const options = {
      title: 'Select Document',
      maxWidth: imageType == 'passport' ? 2200 : 1800, //1600
      maxHeight: imageType == 'passport' ? 2200 : 1800, //1600
      quality: 0.9,
      saveToPhotos: false,
      //takePhotoButtonTitle: null,
      includeBase64: true,
      mediaType: 'photo',
      //chooseFromLibraryButtonTitle: null,
    }

    this.setState({
      refreshingUpload: true,
      imageError: false,
    })

    //ImagePicker.launchImageLibrary(options, async (response) => {
    ImagePicker.launchCamera(options, async response => {
      if (response.didCancel) {
        this.setState({refreshingUpload: false})
        return
      } else if (response.error) {
        this.setState({refreshingUpload: false})
        return
      } else if (response.customButton) {
        console.log('User tapped custom button: ', response.customButton)
      } else {
        response = response.assets[0]
        const photo = {uri: response.uri}

        const validImage = this.validateImage(response.type, response.fileSize)
        if (validImage === 'type') {
          this.setState({refreshingUpload: false, imageError: 'type'})
          return
        } else if (validImage === 'size') {
          this.setState({refreshingUpload: false, imageError: 'size'})
          return
        }

        let path = response.uri
        if (Platform.OS === 'ios') {
          path = '~' + path.substring(path.indexOf('/Documents'))
        }
        if (!response.fileName) response.fileName = path.split('/').pop()

        const idV2Data = this.state.idV2Data
        if (imageType == 'passport') {
          idV2Data.push({
            media: response.base64,
            media_type: 'IMAGE_PASSPORT_FRONT',
            name: 'Passport',
          })
        }
        if (imageType == 'idFront') {
          idV2Data.push({
            media: response.base64,
            media_type: 'IMAGE_DRIVERS_LICENSE_FRONT',
            name: 'ID Front',
          })
        }
        if (imageType == 'idBack') {
          idV2Data.push({
            media: response.base64,
            media_type: 'IMAGE_DRIVERS_LICENSE_BACK',
            name: 'ID Back',
          })
        }

        this.setState({idV2Data})
      }
    })
  }

  uploadIdData = () => {
    this.setState({idRetry: false})
    const id = this.props.loanData.verification?.referenceId || ''
    const {selfieData, idV2Data} = this.state
    const payload = [selfieData].concat(idV2Data)
    this.setState({isLoading: true})

    this.props.WebService.unit21UploadID(id, payload)
      .then(res => {
        this.setState({isLoading: false})
        console.log('unit21UploadID res ', res)
        if (res.data.photoIdVerificationStatus == 'passed') {
          if (Number(this.props.loanData.amount) >= 75000 && this.props.loanData.verification.bankruptcyStatus !== 'skip') {
            this.props.navigation.navigate('Unit21Bankruptcy')
          } else {
            let thisAccount = this.props.user?.accounts?.filter(a => a.ref == this.props.accountRef)[0] || []

            if (thisAccount?.productType == 'exchange') {
              this.props.navigation.navigate('ExchangePending')
            } else {
              this.props.navigation.navigate('LoanPayout', {
                goBackDisabled: true,
              })
            }
          }
          //if 75k > go bankrupties
          //else go deposit collateral
        } else {
          // look for retry- update screen
          if (res.data.photoIdVerificationStatus == 'retry') {
            //delete current images -
            //send back to facial step and redo
            this.setState({idV2Data: []})
            this.props.navigation.route?.onGoBack()
            this.props.navigation.goBack()

            //this.setState({ idRetry: true })
          }
        }
      })
      .catch(err => {
        this.setState({isLoading: false})
        console.log('unit21UploadID err ', err)
      })
  }

  validateImage = (type, size) => {
    const fileTypes = ['application/pdf', 'image/jpg', 'image/jpeg', 'image/png', 'image/gif', 'image/tif', 'image/tiff']
    if (!fileTypes.includes(type)) {
      return 'type'
    }
    if (size / 1000000 > 50) {
      return 'size'
    }
    return true
  }

  removeFile = name => {
    let {idV2Data} = this.state
    idV2Data = idV2Data.filter(a => a.name != name)
    this.setState({idV2Data})
  }

  getLoanDataNew = () =>
    this.props.WebService.getLoans().then(res => {
      if (res.data?.length > 0) {
        const loanData = res.data[0]
        this.props.dispatch(updateLoans(loanData))
        return loanData
      }
    })

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    stackDepth += 1
    this.props.navigation.pop(stackDepth)
  }

  socurePolling = async () => {
    console.log('socurePolling')
    const loanData = await this.getLoanDataNew()
    if (loanData?.verification?.photoIdVerificationStatus == 'passed') {
      clearInterval(this.polling)
      this.setState({isLoading: false})
      if (Number(this.props.loanData.amount) >= 75000 && this.props.loanData.verification.bankruptcyStatus !== 'skip') {
        this.props.navigation.navigate('Unit21Bankruptcy')
      } else {
        this.props.navigation.navigate('LoanPayout', {
          goBackDisabled: true,
        })
      }
      //if 75k > go bankrupties
      //else go deposit collateral
    } else {
      // look for retry- update screen
      if (loanData?.verification?.photoIdVerificationStatus == 'retry') {
        clearInterval(this.polling)
        this.setState({isLoading: false})

        //delete current images -
        //send back to facial step and redo
        this.setState({idV2Data: []})
        this.props.route?.params?.onGoBack()
        this.props.navigation.goBack()

        //this.setState({ idRetry: true })
      }
    }
  }

  render() {
    const idFrontRow = this.state.idV2Data.filter(a => a.name == 'ID Front')[0] || null
    const idBackRow = this.state.idV2Data.filter(a => a.name == 'ID Back')[0] || null
    const passportRow = this.state.idV2Data.filter(a => a.name == 'Passport')[0] || null

    let canContinue = false
    if (this.state.entityType == 'Passport' && passportRow) {
      canContinue = true
    }
    if (this.state.entityType == `Driver's License` && idFrontRow && idBackRow) {
      canContinue = true
    }

    const showFiles = this.state.idV2Data.map((a, k) => (
      <View style={styles.unit21ShowFilesBox} key={k}>
        <View
          style={{
            width: 200,
            height: 40,
            backgroundColor: '#FFF',
            borderRadius: 14,
            alignItems: 'center',
            paddingLeft: 4,
            justifyContent: 'center',
            paddingRight: 4,
          }}>
          <TextReg style={{color: '#000'}}>{a.name}</TextReg>
        </View>
        <TouchableOpacity onPress={() => this.removeFile(a.name)}>
          <Image
            source={removeFileImg}
            style={{
              height: 30,
              width: 30,
            }}
          />
        </TouchableOpacity>
      </View>
    ))

    let stackDepth = this.props.route?.params?.stackDepth || 1

    return (
      <View
        style={{
          //flex: 1,
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
          paddingBottom: 60,
        }}>
        <BackgroundHeader title={'Verification'} goBack={() => this.props.navigation.goBack()} close closeFn={this.closeUnit21} />
        <View style={{marginLeft: 30, marginRight: 30, alignSelf: 'stretch'}}>
          <Unit21Progress complete={10} />
        </View>
        <ScrollView
          style={{
            paddingLeft: 30,
            paddingRight: 30,
          }}
          contentContainerStyle={{
            alignSelf: 'stretch',
            flex: 1,
            justifyContent: 'space-between',
          }}>
          <View>
            <TextReg style={styles.unit21SelfieText}>Upload a valid Drivers License or a Passport</TextReg>
            <TextReg style={styles.personalInfoInputTitle}>I am uploading a</TextReg>
            <View
              style={{
                backgroundColor: '#FFF',
                borderRadius: 14,
                height: 40,
                marginBottom: 20,
                position: 'relative',
              }}>
              <LocationSelect
                options={this.state.entityTypeList}
                onSelect={this.onEntitySelect}
                placeholder={this.state.entityType ? this.state.entityType : ''}
              />
              <Image source={downArrowBlack} style={styles.unit21IDArrowImg} />
            </View>
            {this.state.entityType == 'Passport' && !passportRow && (
              <TouchableOpacity onPress={() => this.openImageSelect('passport')} style={styles.unit21IDClickBox}>
                <Image
                  source={passport}
                  style={{
                    height: 68,
                    width: 50,
                    marginBottom: 10,
                  }}
                />
                <TextReg style={{color: '#00FFBD', fontSize: 18}}>Tap to Upload</TextReg>
                <TextReg style={styles.unit21ClickBoxDetails}>Accepted files: JPG, PNG</TextReg>
              </TouchableOpacity>
            )}
            {this.state.entityType == `Driver's License` && (
              <>
                {!idFrontRow && (
                  <TouchableOpacity
                    onPress={() => this.openImageSelect('idFront')}
                    style={[
                      styles.unit21IDClickBox,
                      {
                        marginBottom: 16,
                      },
                    ]}>
                    <Image
                      source={idFront}
                      style={{
                        height: 50,
                        width: 100,
                        marginBottom: 14,
                      }}
                    />
                    <TextReg style={{color: '#00FFBD', fontSize: 18}}>Tap to Upload</TextReg>
                    <TextReg style={styles.unit21ClickBoxDetails}>Accepted files: JPG, PNG</TextReg>
                  </TouchableOpacity>
                )}
                {!idBackRow && (
                  <TouchableOpacity onPress={() => this.openImageSelect('idBack')} style={styles.unit21IDClickBox}>
                    <Image
                      source={idBack}
                      style={{
                        height: 50,
                        width: 100,
                        marginBottom: 14,
                      }}
                    />
                    <TextReg style={{color: '#00FFBD', fontSize: 18}}>Tap to Upload</TextReg>
                    <TextReg style={styles.unit21ClickBoxDetails}>Accepted files: JPG, PNG</TextReg>
                  </TouchableOpacity>
                )}
              </>
            )}
            {showFiles}
          </View>
          <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
            {this.state.idRetry && (
              <View style={styles.convertErrorBox}>
                <TextReg style={styles.showErrorText}>Error with validation, Please update images and retry.</TextReg>
              </View>
            )}

            <Button
              isLoading={this.state.isLoading}
              disabled={!canContinue}
              style={{
                alignSelf: 'stretch',
                marginBottom: 30,
                backgroundColor: '#00FFBD',
              }}
              onPress={() => this.uploadIdData()}
              theme={'secondary'}>
              <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
            </Button>
          </View>
        </ScrollView>
      </View>
    )
  }
}

/*
<View>
  <TextReg>{this.state.uploadScannedInfo.toString()}</TextReg>
  <TextReg>{this.state.scannedLicenseRes.toString()}</TextReg>
  <TextReg>{this.state.selfieData.toString()}</TextReg>
  <TextReg>{this.state.referenceId}</TextReg>
  <TextReg>{this.state.uuid}</TextReg>
</View>
*/

IDVerification.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  user: state.user.user,
  accountRef: state.auth.account.ref,
  loanData: state.user.loanData || {},
  tokenPrices: state.user.prices,
  WebService: state.auth.WebService,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(IDVerification)
