import React, {Component} from 'react'
import {
  View,
  TouchableOpacity,
  Dimensions,
  Platform,
  Image,
  TextInput,
  Keyboard,
  ScrollView,
} from 'react-native'
import {connect} from 'react-redux'
import AsyncStorage from '@react-native-async-storage/async-storage'

import * as ImagePicker from 'react-native-image-picker'

const {height: ScreenHeight} = Dimensions.get('window')

import removeFileImg from '../../../imgs/closeX.png'
import {BackgroundHeader, TextReg, Button, LocationSelect} from '../../../components'
import {
  pauseUnit21,
  increaseUnit21Refresh,
  increaseRefreshDataCount,
} from '../../../store/user/user.actions'
import {askingForPermissions} from '../../../store/auth/auth.actions'

import styles from '../styles'
import {navigateRefinanceLoan} from '../../Loans/Refinance/helpers'

class Unit21Bankruptcy extends Component {
  constructor(props) {
    super(props)
    this.state = {
      bankruptcy: false,
      documents:
        this.props?.loanData?.loanDocuments?.filter(a => a.type == 'bankruptcy_specification') ||
        [],
      employerName: '',
      occupationTitle: '',
      occupationIndustry: '',
      salary: '',
      collateralSource: '',
      collateralList: [
        'Mining',
        'US Exchange Purchases',
        'International Exchange Purchases',
        'Other',
      ],
    }
    if (this.state.documents.length > 0) {
      this.state.bankruptcy = true
    }
    this.inputs = []
  }

  updateField = (text, type) => {
    const state = this.state
    state[type] = text
    this.setState(state)
  }

  onSelectCollateral = collateralSource => {
    console.log('collateralSource', collateralSource)
    this.setState({collateralSource})
  }

  openImageSelect = (verification = false) => {
    this.props.dispatch(askingForPermissions(true))
    const options = {
      title: 'Select Document',
      maxWidth: 2200,
      maxHeight: 2200,
      quality: 1.0,
      storageOptions: {
        skipBackup: true,
        path: 'images',
        cameraRoll: true,
        waitUntilSaved: true,
      },
      //takePhotoButtonTitle: null,
      noData: false,
      mediaType: 'photo',
    }

    this.setState({
      refreshingUpload: true,
      imageError: false,
    })

    ImagePicker.launchImageLibrary(options, async response => {
      if (response.didCancel) {
        this.setState({refreshingUpload: false})
        return
      } else if (response.error) {
        this.setState({refreshingUpload: false})
        return
      } else if (response.customButton) {
        //console.log('User tapped custom button: ', response.customButton)
      } else {
        response = response.assets[0]
        const photo = {uri: response.uri}
        const formdata = new FormData()

        const validImage = this.validateImage(response.type, response.fileSize)
        if (validImage === 'type') {
          this.setState({refreshingUpload: false, imageError: 'type'})
          return
        } else if (validImage === 'size') {
          this.setState({refreshingUpload: false, imageError: 'size'})
          return
        }

        let path = response.uri
        if (Platform.OS === 'ios') {
          path = '~' + path.substring(path.indexOf('/Documents'))
        }
        if (!response.fileName) response.fileName = path.split('/').pop()

        formdata.append('file', [
          {
            uri: Platform.OS === 'android' ? photo.uri : photo.uri.replace('file://', ''),
            name: response.fileName || 'testName.jpg',
            type: response.type,
            size: response.fileSize,
          },
        ])
        formdata.append('name', response.fileName)
        formdata.append('key', 'true')

        const loanId = this.props?.loanData?.id
        this.props?.WebService.uploadDocument(
          loanId,
          'loan',
          'bankruptcy_specification',
          photo.uri.replace('file://', ''),
          response.fileName,
        )
          .then(res => {
            const documents = this.state.documents
            const parsedData = JSON.parse(res.data)

            documents.push({
              name: response.fileName,
              id: parsedData.id,
            })

            this.setState({
              refreshingUpload: false,
              documents,
            })

            //refresh user data - so docs show up on back and forth
            //this.getAccounts()
          })
          .catch(err => {
            console.log('upload doc err', err)
            this.setState({refreshingUpload: false})
          })
      }
    })
  }

  validateImage = (type, size) => {
    const fileTypes = [
      'application/pdf',
      'image/jpg',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/tif',
      'image/tiff',
    ]
    if (!fileTypes.includes(type)) {
      return 'type'
    }
    if (size / 1000000 > 50) {
      return 'size'
    }
    return true
  }

  toggleBankruptcy = trueFalse => {
    this.setState({bankruptcy: trueFalse})
  }

  submit = async () => {
    let {employerName, occupationTitle, occupationIndustry, salary, collateralSource} = this.state

    const userPatchPayload = {
      annualSalary: salary,
      employer: employerName,
      industry: occupationIndustry,
      occupation: occupationTitle,
      sourceOfCollateral: collateralSource,
    }

    await this.props.WebService.patchUser(userPatchPayload)

    const payload = {has_bankruptcy: this.state.bankruptcy}
    this.props.WebService.unit21Bankruptcy(payload)
      .then(async res => {
        if (this.props?.route?.params?.forRefi) {
          await this.props.dispatch(increaseRefreshDataCount())
          await this.props.WebService.getPendingRefinanceLoanRef(this.props?.currentAccount?.ref)
            .then(async res => {
              navigateRefinanceLoan(
                this.props.currentAccount,
                this.props.user,
                this.props.navigation,
                res?.data,
              )
            })
            .catch(err => {
              console.log('err', err)
            })
        } else {
          this.props.dispatch(increaseUnit21Refresh())
        }
      })
      .catch(err => {
        console.log('unit21Bankruptcy err', err)
      })
  }

  removeDocument = id => {
    this.props.WebService.removeDocument(id)
      .then(res => {
        const documents = this.state.documents.filter(a => a.id != id)
        this.setState({documents})
      })
      .catch(err => {
        console.log('documents err', err)
      })
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    this.props.navigation.pop(stackDepth)
  }

  render() {
    const showDocuments = this.state.documents.map((a, k) => (
      <View style={styles.unit21ShowFilesBox} key={k}>
        <View style={styles.unit21ShowFilesName}>
          <TextReg style={{color: '#000'}}>{a.name}</TextReg>
        </View>
        <TouchableOpacity onPress={() => this.removeDocument(a.id)}>
          <Image
            source={removeFileImg}
            style={{
              height: 30,
              width: 30,
            }}
          />
        </TouchableOpacity>
      </View>
    ))
    let {employerName, occupationTitle, occupationIndustry, salary, collateralSource} = this.state
    let continueAllowed = true
    if (
      employerName == '' ||
      occupationTitle == '' ||
      occupationIndustry == '' ||
      salary == '' ||
      collateralSource == ''
    ) {
      continueAllowed = false
    }

    return (
      <View
        style={{
          //flex: 1,
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader title={'Verification'} close closeFn={this.closeUnit21} />
        <ScrollView
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
          }}>
          <TextReg style={styles.unit21InfoInputTitle}>{'Employer Name'}</TextReg>
          <TextInput
            style={styles.unit21InfoInput}
            onChangeText={text => this.updateField(text, 'employerName')}
            ref={input => (this.inputs.employerName = input)}
            value={this.state.employerName}
            underlineColorAndroid="transparent"
            blurOnSubmit
            returnKeyType={'next'}
            placeholder={''}
            placeholderTextColor={'#AAA'}
            onSubmitEditing={() => this.inputs.occupationTitle.focus()}
            keyboardAppearance="dark"
          />
          <TextReg style={styles.unit21InfoInputTitle}>{'Occupation/Job Title'}</TextReg>
          <TextInput
            style={styles.unit21InfoInput}
            onChangeText={text => this.updateField(text, 'occupationTitle')}
            ref={input => (this.inputs.occupationTitle = input)}
            value={this.state.occupationTitle}
            underlineColorAndroid="transparent"
            blurOnSubmit
            returnKeyType={'next'}
            placeholder={''}
            placeholderTextColor={'#AAA'}
            onSubmitEditing={() => this.inputs.occupationIndustry.focus()}
            keyboardAppearance="dark"
          />
          <TextReg style={styles.unit21InfoInputTitle}>{'Occupation/Industry'}</TextReg>
          <TextInput
            style={styles.unit21InfoInput}
            onChangeText={text => this.updateField(text, 'occupationIndustry')}
            ref={input => (this.inputs.occupationIndustry = input)}
            value={this.state.occupationIndustry}
            underlineColorAndroid="transparent"
            blurOnSubmit
            returnKeyType={'next'}
            placeholder={''}
            placeholderTextColor={'#AAA'}
            onSubmitEditing={() => this.inputs.salary.focus()}
            keyboardAppearance="dark"
          />
          <TextReg style={styles.unit21InfoInputTitle}>{'Annual Salary ($)'}</TextReg>
          <TextInput
            style={styles.unit21InfoInput}
            onChangeText={text => this.updateField(text, 'salary')}
            ref={input => (this.inputs.salary = input)}
            value={this.state.salary}
            underlineColorAndroid="transparent"
            blurOnSubmit
            returnKeyType={'next'}
            placeholder={''}
            placeholderTextColor={'#AAA'}
            keyboardType={'numeric'}
            onSubmitEditing={() => Keyboard.dismiss()}
            keyboardAppearance="dark"
          />
          <TextReg style={styles.unit21InfoInputTitle}>{'Collateral Source'}</TextReg>
          <LocationSelect
            options={this.state.collateralList}
            onSelect={this.onSelectCollateral}
            placeholder={this.state.collateralSource ? this.state.collateralSource : ''}
            listmode={'SCROLLVIEW'}
            searchable={'no'}
          />
          <TextReg
            style={{
              color: '#FFF',
              fontSize: 20,
              marginBottom: 14,
              marginTop: 10,
            }}>
            Have you filed for bankruptcy in the past 10 years?
          </TextReg>
          <View
            style={{
              height: 50,
              alignSelf: 'stretch',
              borderRadius: 25,
              backgroundColor: '#FFF',
              marginTop: 8,
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: 14,
            }}>
            <TouchableOpacity
              onPress={() => this.toggleBankruptcy(false)}
              style={{
                height: 44,
                borderRadius: 23,
                width: 60,
                backgroundColor: !this.state.bankruptcy ? '#00FFBD' : '#FFF',
                alignItems: 'center',
                justifyContent: 'center',
                marginLeft: 4,
              }}>
              <TextReg
                style={{
                  color: '#000',
                }}>
                NO
              </TextReg>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => this.toggleBankruptcy(true)}
              style={{
                height: 44,
                borderRadius: 23,
                width: 60,
                backgroundColor: this.state.bankruptcy ? '#00FFBD' : '#FFF',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 4,
              }}>
              <TextReg style={{color: '#000'}}>YES</TextReg>
            </TouchableOpacity>
          </View>
          {this.state.bankruptcy && (
            <>
              <TextReg
                style={{
                  color: '#FFF',
                  fontSize: 18,
                  marginBottom: 14,
                  marginTop: 20,
                }}>
                Please upload one of the following documents.
              </TextReg>
              <TextReg style={{color: '#FFF', marginBottom: 12, marginLeft: 4}}>
                1. The Discharge of Debtor
              </TextReg>
              <TextReg style={{color: '#FFF', marginBottom: 40, marginLeft: 4}}>
                2. Order Closing Case Discharged Documents
              </TextReg>
              <Button
                isLoading={this.state.refreshingUpload}
                style={{
                  alignSelf: 'stretch',
                  backgroundColor: '#28283D',
                  borderColor: '#FFF',
                  borderWidth: 2,
                }}
                onPress={() => this.openImageSelect()}>
                <TextReg style={{color: '#FFF', fontSize: 18}}>UPLOAD DOCUMENT</TextReg>
              </Button>
              {showDocuments}
            </>
          )}

          <Button
            disabled={
              (this.state.bankruptcy && this.state.documents.length == 0) || !continueAllowed
            }
            style={{alignSelf: 'stretch', marginBottom: 80, marginTop: 20}}
            onPress={() => this.submit()}>
            <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
          </Button>
        </ScrollView>
      </View>
    )
  }
}

Unit21Bankruptcy.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = (state, props) => {
  const user = state?.user?.user
  const currentAccount = user?.accounts?.find(a => a.ref == state.auth.account.ref) || {}
  const forRefi = props?.route?.params?.forRefi
  const loanData = forRefi
    ? state.user?.loanData?.refinanceLoan || props?.route?.params?.refinanceLoan || {}
    : state.user.loanData || {}

  return {
    loanData,
    user,
    tokenPrices: state.user.prices,
    WebService: state.auth.WebService,
    launchDarkly: state.launchDarkly,
    currentAccount,
  }
}

export default connect(mapStateToProps)(Unit21Bankruptcy)
