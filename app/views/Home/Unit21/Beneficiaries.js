import React, {Component} from 'react'
import {
  View,
  TextInput,
  Keyboard,
  Dimensions,
  TouchableOpacity,
  Image,
  PixelRatio,
} from 'react-native'
import {connect} from 'react-redux'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import AsyncStorage from '@react-native-async-storage/async-storage'
const {height: ScreenHeight} = Dimensions.get('window')
import {
  pauseUnit21,
  increaseUnit21Refresh,
  increaseRefreshDataCount,
} from '../../../store/user/user.actions'
import {BackgroundHeader, TextReg, Button} from '../../../components'
import agreedYes from '../../../imgs/agreedYes.png'

import styles from '../styles'
import {navigateRefinanceLoan} from '../../Loans/Refinance/helpers'

class Beneficiaries extends Component {
  constructor(props) {
    super(props)
    let businessBeneficiaries = this.props?.loanData?.verification?.businessBeneficiaries || []
    this.state = {
      firstPercentage: null,
      showAddNew: false,
      firstName: '',
      lastName: '',
      email: '',
      addOwnership: '',
      businessBeneficiaries,
      addMore: false,
      full: businessBeneficiaries?.length > 0 ? false : null,
    }
    this.inputs = []

    let stackDepth = this.props.route?.params?.stackDepth || 1
  }

  closeUnit21 = () => {
    //bug with flowB1, override pop1000
    this.props.navigation.pop(1000)
  }

  toggleAddBeneficiary = () => {
    this.setState({showAddNew: !this.state.showAddNew})
  }

  updateField = (text, type) => {
    const state = this.state
    const textRemovePercentage = text.replace('%', '').replace(' ', '')
    state[type] = textRemovePercentage
    this.setState(state)
  }

  submitAddNew = () => {
    let {email, firstName, lastName} = this.state
    const payload = {
      email,
      firstName,
      lastName,
      ownership: this.state.addOwnership,
    }
    console.log('payload', payload)
    this.props.WebService.addNewBeneficiary(payload)
      .then(res => {
        const {businessBeneficiaries} = this.state
        businessBeneficiaries.push(res.data)
        this.setState({
          showAddNew: false,
          businessBeneficiaries,
          email: '',
          firstName: '',
          lastName: '',
          addOwnership: '',
        })
      })
      .catch(err => {
        console.log('add new err', err)
      })
  }

  submitBeneficiaries = () => {
    if (this.state.full === false && this.state.businessBeneficiaries?.length < 1) {
      let email = this.props.user?.primaryEmail
      let firstName = this.props.user?.firstName
      let lastName = this.props.user?.lastName
      this.setState({email, firstName, lastName, addOwnership: this.state.firstPercentage}, () => {
        this.submitAddNew()
      })
      return
    }
    this.props.WebService.submitBeneficiaries()
      .then(async res => {
        if (this.props?.route?.params?.forRefi) {
          await this.props.dispatch(increaseRefreshDataCount())
          await this.props.WebService.getPendingRefinanceLoanRef(this.props?.currentAccount?.ref)
            .then(async res => {
              navigateRefinanceLoan(
                this.props.currentAccount,
                this.props.user,
                this.props.navigation,
                res?.data,
              )
            })
            .catch(err => {
              console.log('err', err)
            })
        } else {
          this.props.dispatch(increaseUnit21Refresh())
        }
        return
      })
      .catch(err => {
        console.log('submitBeneficiaries err', err)
      })
  }

  removeBeneficiary = id => {
    console.log('remove id', id)
    this.props.WebService.removeBeneficiary(id)
      .then(async res => {
        const businessBeneficiaries = this.state.businessBeneficiaries.filter(a => a.id != id)
        this.setState({businessBeneficiaries})
        if (this.props?.route?.params?.forRefi) {
          await this.props.dispatch(increaseRefreshDataCount())
          await this.props.WebService.getPendingRefinanceLoanRef(this.props?.currentAccount?.ref)
            .then(async res => {
              navigateRefinanceLoan(
                this.props.currentAccount,
                this.props.user,
                this.props.navigation,
                res?.data,
              )
            })
            .catch(err => {
              console.log('err', err)
            })
        } else {
          this.props.dispatch(increaseUnit21Refresh())
        }
      })
      .catch(err => {
        console.log('removeBeneficiary err', err)
      })
  }

  goBack = () => {
    if (this.state.showAddNew) {
      this.toggleAddBeneficiary()
    } else {
      //delete all beneficiaries
      this.state.businessBeneficiaries?.map(a => {
        this.removeBeneficiary(a.id)
      })
    }
  }

  render() {
    console.log('bene', this.state)
    const showBeneficiaries = this.state.businessBeneficiaries.map((a, k) => {
      if (a.ownership || true) {
        return (
          <View
            style={{
              alignSelf: 'stretch',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <View
              style={{
                borderRadius: 14,
                alignSelf: 'stretch',
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                backgroundColor: '#FFF',
                marginTop: 8,
                padding: 4,
                flex: 1,
              }}>
              <View style={{flex: 1}}>
                <TextReg
                  style={{
                    fontSize: 17,
                    alignSelf: 'stretch',
                    textAlign: 'left',
                    marginLeft: 4,
                    color: '#000',
                  }}>
                  {a.firstName} {a.lastName}
                </TextReg>
                <TextReg
                  style={{
                    fontSize: 15,
                    marginBottom: 2,
                    textAlign: 'left',
                    marginLeft: 4,
                    color: '#000',
                  }}>
                  {a.email}
                </TextReg>
              </View>
              <TextReg
                style={{
                  fontSize: 22,
                  textAlign: 'left',
                  color: '#000',
                  marginRight: 4,
                }}>
                {a.ownership}%
              </TextReg>
            </View>
            <TouchableOpacity onPress={() => this.removeBeneficiary(a.id)}>
              <Image
                source={require('../../../imgs/closeX.png')}
                style={{height: 30, width: 30, marginLeft: 10, marginTop: 6}}
              />
            </TouchableOpacity>
          </View>
        )
      }
    })

    const percentageNum = this.state.firstPercentage || ''
    let stackDepth = this.props.route?.params?.stackDepth || 0
    let {addMore, full, businessBeneficiaries, showAddNew} = this.state
    let enabled =
      full ||
      (full === false && percentageNum?.length > 0) ||
      (full === false && businessBeneficiaries?.length > 0)
    let yourPercent = full === false && !false && businessBeneficiaries?.length < 1
    let {firstName, lastName, addOwnership, email} = this.state
    return (
      <View
        style={{
          //flex: 1,
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
          paddingBottom: 60,
          padding: 24,
        }}>
        <BackgroundHeader
          title={'Beneficiaries'}
          goBack={businessBeneficiaries?.length > 0 ? this.goBack : null}
          close={!this.state.showAddNew}
          closeFn={this.closeUnit21}
        />
        <View
          style={{
            alignSelf: 'stretch',
            flex: 1,
            justifyContent: 'space-between',
          }}>
          <KeyboardAwareScrollView
            contentContainerStyle={{alignItems: 'center'}}
            enableResetScrollToCoords={false}
            extraScrollHeight={24}>
            {businessBeneficiaries?.length < 1 && (
              <View style={{alignSelf: 'stretch'}}>
                <TextReg
                  style={{
                    color: '#FFF',
                    fontSize: 16,
                    marginBottom: 10,
                    alignSelf: 'stretch',
                    textAlign: 'left',
                    marginTop: 20,
                  }}>
                  Do you have full ownership of this business?
                </TextReg>
                <TouchableOpacity
                  onPress={() => this.setState({full: true})}
                  activeOpacity={1}
                  style={styles.toggleAcknowledgeButton}>
                  <View style={styles.toggleAcknowledgeView}>
                    <Image
                      source={agreedYes}
                      style={[
                        styles.unit21ToggleCheckImg,
                        {
                          opacity: full === true ? 1 : 0,
                        },
                      ]}
                    />
                  </View>
                  <TextReg style={styles.unit21AcknowledgeTitle}>{'Yes'}</TextReg>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => this.setState({full: false})}
                  activeOpacity={1}
                  style={styles.toggleAcknowledgeButton}>
                  <View style={styles.toggleAcknowledgeView}>
                    <Image
                      source={agreedYes}
                      style={[
                        styles.unit21ToggleCheckImg,
                        {
                          opacity: full === false ? 1 : 0,
                        },
                      ]}
                    />
                  </View>
                  <TextReg style={styles.unit21AcknowledgeTitle}>{'No'}</TextReg>
                </TouchableOpacity>
              </View>
            )}
            {yourPercent && (
              <View style={{alignSelf: 'stretch', position: 'relative'}}>
                <TextReg
                  style={{
                    color: '#FFF',
                    fontSize: 16,
                    marginBottom: 10,
                    alignSelf: 'stretch',
                    textAlign: 'left',
                    marginTop: 20,
                  }}>
                  What is your ownership percentage?
                </TextReg>
                <TextInput
                  style={styles.unit21InfoInput}
                  onChangeText={text => this.updateField(text, 'firstPercentage')}
                  ref={input => (this.inputs.firstPercentage = input)}
                  value={percentageNum}
                  underlineColorAndroid="transparent"
                  keyboardType={'numeric'}
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  placeholderTextColor={'#AAA'}
                  onSubmitEditing={() => Keyboard.dismiss()}
                  keyboardAppearance="dark"
                />
                <View
                  pointerEvents="none"
                  style={{position: 'absolute', zIndex: 30, flexDirection: 'row'}}>
                  <TextReg style={{opacity: 0, fontSize: 17}}>{percentageNum}</TextReg>
                  <TextReg style={{marginTop: 66, marginLeft: 15, fontSize: 18}}>{`%`}</TextReg>
                </View>
              </View>
            )}
            {showAddNew && (
              <View style={{alignSelf: 'stretch'}}>
                <TextReg style={styles.unit21InfoInputTitle}>First Name</TextReg>
                <TextInput
                  style={styles.unit21InfoInput}
                  onChangeText={text => this.updateField(text, 'firstName')}
                  ref={input => (this.inputs.firstName = input)}
                  value={this.state.firstName}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={'First Name'}
                  placeholderTextColor={'#AAA'}
                  onSubmitEditing={() => this.inputs.lastName.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.unit21InfoInputTitle}>Last Name</TextReg>
                <TextInput
                  style={styles.unit21InfoInput}
                  onChangeText={text => this.updateField(text, 'lastName')}
                  ref={input => (this.inputs.lastName = input)}
                  value={this.state.lastName}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={'Last Name'}
                  placeholderTextColor={'#AAA'}
                  onSubmitEditing={() => this.inputs.email.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.unit21InfoInputTitle}>Email</TextReg>
                <TextInput
                  style={styles.unit21InfoInput}
                  onChangeText={text => this.updateField(text, 'email')}
                  ref={input => (this.inputs.email = input)}
                  value={this.state.email}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={'Email'}
                  placeholderTextColor={'#AAA'}
                  onSubmitEditing={() => this.inputs.addOwnership.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.unit21InfoInputTitle}>Ownership (%)</TextReg>
                <TextInput
                  style={styles.unit21InfoInput}
                  onChangeText={text => this.updateField(text, 'addOwnership')}
                  ref={input => (this.inputs.addOwnership = input)}
                  value={this.state.addOwnership}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  keyboardType={'numeric'}
                  returnKeyType={'next'}
                  placeholder={'%'}
                  placeholderTextColor={'#AAA'}
                  onSubmitEditing={() => Keyboard.dismiss()}
                  keyboardAppearance="dark"
                />
                <Button
                  disabled={!addOwnership || !email || !lastName || !firstName}
                  style={{alignSelf: 'stretch', marginTop: 14}}
                  onPress={() => this.submitAddNew()}>
                  <TextReg style={{color: '#000', fontSize: 18}}>ADD</TextReg>
                </Button>
              </View>
            )}
            {businessBeneficiaries?.length > 0 && !showAddNew && (
              <>
                <TextReg
                  style={{
                    color: '#FFF',
                    fontSize: 16,
                    marginBottom: 10,
                    alignSelf: 'stretch',
                    textAlign: 'left',
                    marginTop: 20,
                  }}>
                  Please add any other beneficiaries.
                </TextReg>
                <TextReg
                  style={{
                    color: '#FFF',
                    fontSize: 16,
                    marginBottom: 20,
                    alignSelf: 'stretch',
                    textAlign: 'left',
                  }}>
                  A beneficiary is anyone who owns 25% or more in your entity. They will undergo
                  KYC.
                </TextReg>

                <Button
                  style={{alignSelf: 'stretch', marginTop: 2}}
                  onPress={() => this.toggleAddBeneficiary()}>
                  <TextReg style={{color: '#000', fontSize: 18}}>ADD BENEFICIARY</TextReg>
                </Button>
                <View style={{alignSelf: 'stretch', marginTop: 20}}>{showBeneficiaries}</View>
              </>
            )}
          </KeyboardAwareScrollView>
          {!this.state.showAddNew && (
            <Button
              disabled={!enabled}
              style={{alignSelf: 'stretch', marginBottom: 50}}
              onPress={() => this.submitBeneficiaries()}>
              <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
            </Button>
          )}
        </View>
      </View>
    )
  }
}

Beneficiaries.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
  gesturesEnabled: false,
})

const mapStateToProps = (state, props) => {
  const user = state?.user?.user
  const currentAccount = user?.accounts?.find(a => a.ref == state.auth.account.ref) || {}
  const forRefi = props?.route?.params?.forRefi
  return {
    loanData: forRefi
      ? state.user?.loanData?.refinanceLoan || props?.route?.params?.refinanceLoan
      : state.user.loanData || {},
    tokenPrices: state.user.prices,
    WebService: state.auth.WebService,
    launchDarkly: state.launchDarkly,
    user,
    currentAccount,
  }
}

export default connect(mapStateToProps)(Beneficiaries)
