import React, {Component} from 'react';
import {View, Image, Dimensions, Linking} from 'react-native';
import {connect} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';

const {height: ScreenHeight} = Dimensions.get('window');

import {BackgroundHeader, TextReg, Button} from '../../../components';
import {pauseUnit21} from '../../../store/user/user.actions';
import docMagnify from '../../../imgs/unit21/docMagnify.png';
import {increaseRefreshDataCount} from '../../../store/user/user.actions';

import styles from '../styles';

class ExchangePending extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1;
    this.props.navigation.pop(stackDepth);
  };

  continue = () => {
    //refresh data and
    this.props.dispatch(increaseRefreshDataCount());
    setTimeout(() => {
      this.props.navigation.popToTop();
    }, 200);
  };

  render() {
    return (
      <View
        style={{
          //flex: 1,
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
          paddingBottom: 80,
        }}>
        <BackgroundHeader title={' '} close closeFn={this.closeUnit21} />
        <View style={styles.unit21IDVerificationHeaderBox}>
          <View style={{alignItems: 'center'}}>
            <Image
              source={docMagnify}
              style={{
                height: 54,
                width: 50,
                marginBottom: 14,
              }}
            />
            <TextReg
              style={{
                color: '#FFF',
                fontSize: 20,
                marginBottom: 14,
                marginTop: 10,
                width: 220,
                textAlign: 'center',
              }}>
              Verification Submitted
            </TextReg>

            <TextReg
              style={{
                color: '#FFF',
                fontSize: 16,
                marginBottom: 10,
                textAlign: 'center',
              }}>
              Verification completed, you can now deposit funds into any of your crypto wallets or begin with a new loan request in the
              marketplace.
            </TextReg>
          </View>
          <View style={{alignItems: 'center'}}>
            <Button style={{alignSelf: 'stretch', marginBottom: 30}} onPress={() => this.continue()}>
              <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
            </Button>
          </View>
        </View>
      </View>
    );
  }
}

ExchangePending.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
});

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  tokenPrices: state.user.prices,
  WebService: state.auth.WebService,
});

export default connect(mapStateToProps)(ExchangePending);
