import React, {Component} from 'react'
import {
  View,
  Image,
  Dimensions,
  Linking,
  DocumentReferenceType,
  TouchableOpacity,
  ScrollView,
} from 'react-native'
import {connect} from 'react-redux'
import AsyncStorage from '@react-native-async-storage/async-storage'
import * as ImagePicker from 'react-native-image-picker'

const {height: ScreenHeight} = Dimensions.get('window')

import {
  pauseUnit21,
  increaseUnit21Refresh,
  increaseRefreshDataCount,
} from '../../../store/user/user.actions'
import {logout, askingForPermissions} from '../../../store/auth/auth.actions'

import removeFileImg from '../../../imgs/closeX.png'
import {BackgroundHeader, TextReg, Button} from '../../../components'
import docMagnify from '../../../imgs/unit21/docMagnify.png'

import styles from '../styles'
import {navigateRefinanceLoan} from '../../Loans/Refinance/helpers'

class JurisdictionDocs extends Component {
  constructor(props) {
    super(props)

    this.state = {
      documents:
        this.props?.loanData?.loanDocuments?.filter(a => a.type == 'verification_of_residency') ||
        [],
    }
  }

  contactUs = () => {
    Linking.openURL('mailto:<EMAIL>')
  }

  logout = () => {
    this.props.dispatch(logout())
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    this.props.navigation.pop(stackDepth)
  }

  continue = async () => {
    if (this.props?.route?.params?.forRefi) {
      await this.props.dispatch(increaseRefreshDataCount())
      await this.props.WebService.getPendingRefinanceLoanRef(this.props?.currentAccount?.ref)
        .then(async res => {
          navigateRefinanceLoan(
            this.props.currentAccount,
            this.props.user,
            this.props.navigation,
            res?.data,
          )
        })
        .catch(err => {
          console.log('err', err)
        })
    } else {
      await this.props.dispatch(increaseUnit21Refresh())
    }
  }

  validateImage = (type, size) => {
    const fileTypes = [
      'application/pdf',
      'image/jpg',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/tif',
      'image/tiff',
    ]
    if (!fileTypes.includes(type)) {
      return 'type'
    }
    if (size / 1000000 > 50) {
      return 'size'
    }
    return true
  }

  openImageSelect = documentType => {
    this.props.dispatch(askingForPermissions(true))
    const options = {
      title: 'Select Document',
      maxWidth: 2200,
      maxHeight: 2200,
      quality: 1.0,
      storageOptions: {
        skipBackup: true,
        path: 'images',
        cameraRoll: true,
        waitUntilSaved: true,
      },
      //takePhotoButtonTitle: null,
      noData: false,
      mediaType: 'photo',
    }

    this.setState({
      refreshingUpload: false,
      imageError: false,
    })

    ImagePicker.launchImageLibrary(options, async response => {
      if (response.didCancel) {
        this.setState({
          refreshingUpload: false,
        })
        return
      } else if (response.error) {
        this.setState({
          refreshingUpload: false,
        })
        return
      } else if (response.customButton) {
        //console.log('User tapped custom button: ', response.customButton)
      } else {
        response = response.assets[0]
        const photo = {uri: response.uri}
        const formdata = new FormData()

        const validImage = this.validateImage(response.type, response.fileSize)
        if (validImage === 'type') {
          this.setState({
            refreshingUpload: false,
            imageError: 'type',
          })
          return
        } else if (validImage === 'size') {
          this.setState({
            refreshingUpload: false,
            imageError: 'size',
          })
          return
        }

        let path = response.uri
        if (Platform.OS === 'ios') {
          path = '~' + path.substring(path.indexOf('/Documents'))
        }
        if (!response.fileName) response.fileName = path.split('/').pop()

        formdata.append('file', [
          {
            uri: Platform.OS === 'android' ? photo.uri : photo.uri.replace('file://', ''),
            name: response.fileName || 'testName.jpg',
            type: response.type,
            size: response.fileSize,
          },
        ])
        formdata.append('name', response.fileName)
        formdata.append('key', 'true')

        const {accountRef} = this.props
        const accounts = this.props?.user?.accounts
        let businessProfile
        if (accounts) {
          businessProfile = this.props?.user?.accounts?.filter(a => a.ref == accountRef)[0]
            ?.businessProfile
        }

        //https://borrower-portal.stage.saltlending.tech/api/v0/documents/0be4cfb6-fa28-44b1-92a6-d065e796233f/address/other_proof_of_address?ref=2

        const docType = 'verification_of_residency'
        const referenceType = 'loan'
        const loanId = this.props?.loanData?.id
        this.setState({refreshingUpload: true})

        this.props.WebService.uploadDocument(
          loanId,
          referenceType,
          docType,
          photo.uri.replace('file://', ''),
          response.fileName,
        )
          .then(res => {
            const documents = this.state.documents
            const parsedData = JSON.parse(res.data)

            documents.push({
              name: response.fileName,
              id: parsedData.id,
              type: 'verification_of_residency',
            })

            this.setState({
              refreshingUpload: false,
              documents,
            })

            //refresh user data - so docs show up on back and forth
            //this.getAccounts()
          })
          .catch(err => {
            console.log('upload doc err', err)
            this.setState({
              refreshingUpload: false,
            })
          })
      }
    })
  }

  removeDocument = id => {
    this.props.WebService.removeDocument(id)
      .then(async res => {
        const documents = this.state.documents.filter(a => a.id != id)
        this.setState({documents})
        await this.props.dispatch(increaseRefreshDataCount())
      })
      .catch(err => {
        console.log('documents err', err)
      })
  }

  render() {
    console.log('this.props juris docs', this.state)

    let docsRejected = false
    let filteredDocs = this.state.documents?.filter(b => !b?.rejectedAt) || []
    if (this.state.documents?.length > 0 && filteredDocs?.length < 1) {
      docsRejected = true
    }

    const showJurisDocs = this.state.documents?.map((a, k) => {
      return (
        <View style={styles.unit21ShowFilesBox} key={k}>
          <View style={{...styles.unit21ShowFilesName, width: !a?.rejectedAt ? '90%' : '100%'}}>
            <TextReg style={{color: '#000', maxWidth: 230}}>{a.name}</TextReg>
          </View>
          {!a?.rejectedAt && (
            <TouchableOpacity onPress={() => this.removeDocument(a.id)}>
              <Image
                source={removeFileImg}
                style={{
                  height: 30,
                  width: 30,
                }}
              />
            </TouchableOpacity>
          )}
        </View>
      )
    })
    return (
      <View
        style={{
          //flex: 1,
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader title={' '} close closeFn={this.closeUnit21} />
        <ScrollView
          contentContainerStyle={{justifyContent: 'space-between'}}
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            marginBottom: 30,
            flex: 1,
          }}>
          <View style={{alignItems: 'center'}}>
            <Image
              source={docMagnify}
              style={{
                height: 54,
                width: 50,
                marginBottom: 14,
              }}
            />
            <TextReg
              style={{
                color: '#FFF',
                fontSize: 20,
                marginBottom: 8,
                marginTop: 10,
                width: 220,
                textAlign: 'center',
              }}>
              Jurisdiction Document Upload
            </TextReg>

            <View style={{opacity: 1, alignSelf: 'stretch', marginBottom: 10}}>
              <TextReg
                style={{
                  color: '#FFF',
                  fontSize: 18,
                  marginBottom: 14,
                  marginTop: 20,
                }}>
                Please upload one of the following documents.
              </TextReg>
              <TextReg
                style={{
                  color: '#FFF',
                  marginBottom: 4,
                  marginLeft: 4,
                }}>
                1. Verification of Jurisdiction
              </TextReg>
              <TextReg
                style={{
                  color: '#FFF',
                  marginBottom: 2,
                  marginLeft: 10,
                  fontSize: 12,
                  fontStyle: 'italic',
                  opacity: 0.7,
                }}>
                Provide ONE of the following:
              </TextReg>
              <TextReg style={{color: '#FFF', marginBottom: 2, marginLeft: 10}}>
                - Postal Statement
              </TextReg>
              <TextReg style={{color: '#FFF', marginBottom: 2, marginLeft: 10}}>
                - Lease Agreement
              </TextReg>
              <TextReg style={{color: '#FFF', marginBottom: 2, marginLeft: 10}}>
                - Sale Agreement
              </TextReg>
              <TextReg style={{color: '#FFF', marginBottom: 2, marginLeft: 10}}>
                - Government-issued ID
              </TextReg>
              <TextReg style={{color: '#FFF', marginBottom: 2, marginLeft: 10}}>
                - Bank Statement
              </TextReg>
              <TextReg style={{color: '#FFF', marginBottom: 2, marginLeft: 10}}>
                - Voided Check
              </TextReg>
              <TextReg style={{color: '#FFF', marginBottom: 2, marginLeft: 10}}>
                - Utility Bill
              </TextReg>
              <Button
                disabled={showJurisDocs.length > 8}
                isLoading={this.state.refreshingUpload}
                style={{
                  alignSelf: 'stretch',
                  backgroundColor: '#28283D',
                  borderColor: '#FFF',
                  borderWidth: 2,
                  marginTop: 24,
                  marginBottom: 8,
                }}
                theme={'secondary'}
                onPress={() => this.openImageSelect('jurisdiction')}>
                <TextReg style={{color: '#FFF', fontSize: 18}}>UPLOAD DOCUMENT</TextReg>
              </Button>
              {showJurisDocs}
            </View>
          </View>
          {docsRejected && (
            <View>
              <TextReg style={{color: '#e5705a', fontSize: 18, padding: 10}}>
                {'The previous document was rejected by our team. Please upload new document.'}
              </TextReg>
            </View>
          )}
          <Button
            disabled={this.state.documents?.length < 1 || docsRejected}
            style={{
              alignSelf: 'stretch',
              marginBottom: 80,
              marginTop: 10,
              opacity: this.state.documents?.length < 1 || docsRejected ? 0.4 : 1,
            }}
            onPress={() => this.continue()}>
            <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
          </Button>
        </ScrollView>
      </View>
    )
  }
}

JurisdictionDocs.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
  gesturesEnabled: false,
})

const mapStateToProps = (state, props) => {
  const user = state?.user?.user
  const currentAccount = user?.accounts?.find(a => a.ref == state.auth.account.ref) || {}
  const forRefi = props?.route?.params?.forRefi
  const loanData = forRefi
    ? state.user?.loanData?.refinanceLoan || props?.route?.params?.refinanceLoan || {}
    : state.user.loanData || {}
  return {
    loanData,
    tokenPrices: state.user.prices,
    WebService: state.auth.WebService,
    currentAccount,
  }
}

export default connect(mapStateToProps)(JurisdictionDocs)
