import React, {Component} from 'react';
import {View, Image, Dimensions, Linking, DocumentReferenceType, TouchableOpacity, ScrollView} from 'react-native';
import {connect} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as ImagePicker from 'react-native-image-picker';

const {height: ScreenHeight} = Dimensions.get('window');

import {pauseUnit21, increaseUnit21Refresh, merchantTosRead} from '../../../store/user/user.actions';
import {logout} from '../../../store/auth/auth.actions';

import removeFileImg from '../../../imgs/closeX.png';
import {BackgroundHeader, TextReg, TextBold, Button} from '../../../components';

import styles from '../styles';

class MerchantTos extends Component {
  constructor(props) {
    super(props);
    this.state = {
      agreed: false,
    };
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1;
    this.props.navigation.pop(stackDepth);
  };

  agreeMerchant = () => {
    this.setState({agreed: !this.state.agreed});
  };

  submitMerchantTos = () => {
    this.props.dispatch(merchantTosRead());
    this.props.dispatch(increaseUnit21Refresh());
  };

  render() {
    let merchantName = this.props.loanData?.depositBankAccount?.name;
    return (
      <ScrollView
        style={{
          //flex: 1,
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader title={'Merchant Tos'} close closeFn={this.closeUnit21} />
        <View
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
            justifyContent: 'space-between',
          }}>
          <View>
            <TextReg
              style={{
                color: '#FFF',
                fontSize: 17,
                marginBottom: 20,
                alignSelf: 'stretch',
                textAlign: 'left',
                marginTop: 20,
              }}>
              Confirm that you agree to send loan payout to a third party merchant
            </TextReg>
            <View style={{flexDirection: 'row', marginBottom: 24}}>
              <TextBold style={{fontSize: 17}}>Merchant Name: </TextBold>
              <TextReg style={{fontSize: 17}}>{merchantName}</TextReg>
            </View>
            <TextReg
              style={{
                fontSize: 16,
              }}>{`By clicking submit, you are directing SALT Lending to disburse your loan proceeds directly to the merchant named above. If you are using the loan proceeds to purchase a product or service from the named merchant, such purchase may be subject to the merchant's cancellation, refund or similar policy. If you have questions or concerns about such purchase, please contact the merchant directly. If you have any questions or concerns about your loan, please contact SALT customer support.`}</TextReg>
          </View>
          <TouchableOpacity
            onPress={() => this.agreeMerchant()}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              width: 310,
              height: 120,
              borderRadius: 14,
            }}>
            {this.state.agreed ? (
              <Image source={require('../../../imgs/check-box.png')} style={{height: 40, width: 40}} />
            ) : (
              <Image source={require('../../../imgs/blank-check-box.png')} style={{height: 40, width: 40}} />
            )}
            <TextReg
              style={{
                color: '#FFF',
                fontSize: 15,
                width: 260,
                textAlign: 'center',
              }}>
              {`I have read, understand, and agree.`}
            </TextReg>
          </TouchableOpacity>
          <Button disabled={!this.state.agreed} style={{alignSelf: 'stretch', marginBottom: 60}} onPress={() => this.submitMerchantTos()}>
            <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
          </Button>
        </View>
      </ScrollView>
    );
  }
}

MerchantTos.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
  gesturesEnabled: false,
});

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  tokenPrices: state.user.prices,
  WebService: state.auth.WebService,
});

export default connect(mapStateToProps)(MerchantTos);
