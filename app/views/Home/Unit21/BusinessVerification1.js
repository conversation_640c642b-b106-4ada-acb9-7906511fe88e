import React, {Component} from 'react'
import {View, TouchableOpacity, Image, TextInput, Keyboard, Dimensions, Platform} from 'react-native'
import {connect} from 'react-redux'
import * as ImagePicker from 'react-native-image-picker'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import iso3166 from 'iso-3166-2'
import countryCodes from '../../../util/countryCodes'

import removeFileImg from '../../../imgs/closeX.png'
import {Button, TextReg, LocationSelect, BackgroundHeader} from '../../../components'
import {increaseUnit21Refresh, updateUser, updateAccountPlus} from '../../../store/user/user.actions'
import {askingForPermissions} from '../../../store/auth/auth.actions'

import {formatDateText} from '../../../util/helpers'

const {width: ScreenWidth, height: ScreenHeight} = Dimensions.get('window')

import agreedYes from '../../../imgs/agreedYes.png'

import styles from '../styles'

class BusinessVerification1 extends Component {
  constructor(props) {
    super(props)

    console.log('showdocs', props)
    let showDocs = props.route?.params?.showDocs || false

    let pickableCountryCodes = countryCodes().map(a => a.name)
    pickableCountryCodes = ['United States'].concat(pickableCountryCodes)

    //loan jurisdiction country & province
    let countryCode = this.props?.loanData?.jurisdiction?.countryCode
    let province = this.props?.loanData?.jurisdiction?.province

    this.state = {
      address1: '',
      address2: '',
      city: '',
      postal: '',
      acknowledge1: false,
      acknowledge2: false,
      loading: false,
      showL1: false,
      businessGroup: false,
      businessInvolved: null,
      formationJurisdiction: '',
      entityType: '',
      dateOfFormation: '',
      industrySector: '',
      businessName: '',
      dba: '',
      taxID: '',
      entityTypeList: ['LLC', 'Corporation', 'Trust', 'Sole Proprietor', 'Non-Profit'],
      countryCodes: pickableCountryCodes || [],
      countrySubs: [],
      selectedCountry: null,
      countryCode: countryCode || null,
      pickableCountrySubs: [],
      selectedProvince: province || null,
      regionCode: null,
      documents: [],
      refreshingUploadAddress: false,
      refreshingUploadBusiness: false,
      jurisdictionError: false,
      selectedProvinceFormation: null,
      regionCodeFormation: null,
      showDocs,
    }

    let countryName
    let pickableCountrySubs = []

    //if loan req country, fill out providence options
    if (countryCode) {
      countryName = countryCodes().filter(a => a.code == countryCode)[0].name
      //this.state.countryCode = countryCode
      this.state.selectedCountry = countryName
      this.state.selectedJurisdiction = countryName
      const isoCountry = iso3166.country(countryCode)
      const subArr = Object.values(isoCountry.sub)
      pickableCountrySubs = subArr.map(a => a.name)
      pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
        if (a < b) return -1
        if (b < a) return 1
        return 0
      })
      this.state.pickableCountrySubs = pickableCountrySubs
      this.state.pickableCountrySubsFormation = pickableCountrySubs
    }

    //if already providence, fill in &
    if (countryCode && this.state.selectedProvince) {
      const regionCode = this.state.selectedProvince
      const subData = iso3166.subdivision(countryCode, regionCode)
      const selectedProvince = subData?.name || null
      this.state.selectedProvince = selectedProvince
      this.state.regionCode = regionCode
    }

    this.inputs = []
    this.scroll = null
  }

  componentDidMount() {
    this.getBusinessInfo()
  }

  getAccounts = () => this.props.WebService.getSaltAccount().then(res => res.data)

  getBusinessInfo = async (refresh = false) => {
    const {accountRef} = this.props
    let accounts = this.props?.user?.accounts
    if (refresh) {
      accounts = await this.getAccounts()
    }

    let businessProfile
    if (accounts) {
      businessProfile = accounts?.filter(a => a.ref == accountRef)[0]?.businessProfile
    }

    const businessAddress = businessProfile?.address
    const {street1, street2, city, province, postalCode} = businessAddress || ''
    let countryCode = businessAddress?.countryCode || this.state.countryCode
    const {listDBA, legalName, taxIdNumber, industry, entityType} = businessProfile
    let {formationDate, formationJurisdiction} = businessProfile
    if (!formationDate) formationDate = ''
    formationDate = formationDate.split('T')[0]
    if (formationDate && formationDate.includes('-')) {
      const year = formationDate.split('-')[0]
      const month = formationDate.split('-')[1]
      const day = formationDate.split('-')[2]
      formationDate = `${month}/${day}/${year}`
    }

    const {
      isEntityArmsAmmunition,
      isEntityCurrencyExchange,
      isEntityMarijuana,
      isEntityMoneyLending,
      isEntityNarcotics,
      isEntityPreciousMetals,
      isEntityAssociation,
      isEntityOnlineGambling,
      isEntityNotForProfit,
      isEntityMoneyService,
      isEntityForeignGovernments,
      isEntityCasinos,
      isEntityAdultEntertainment,
    } = businessProfile
    let businessInvolved = null
    if (isEntityCurrencyExchange) {
      businessInvolved = 1
    }
    if (isEntityMoneyLending) {
      businessInvolved = 2
    }
    if (isEntityPreciousMetals) {
      businessInvolved = 3
    }
    if (isEntityArmsAmmunition) {
      businessInvolved = 4
    }
    if (isEntityNarcotics) {
      businessInvolved = 5
    }
    if (isEntityMarijuana) {
      businessInvolved = 6
    }
    if (isEntityMoneyService) {
      businessInvolved = 13
    }
    if (isEntityNotForProfit) {
      businessInvolved = 12
    }
    if (isEntityForeignGovernments) {
      businessInvolved = 11
    }
    if (isEntityCasinos) {
      businessInvolved = 9
    }
    if (isEntityOnlineGambling) {
      businessInvolved = 10
    }
    if (isEntityAdultEntertainment) {
      businessInvolved = 8
    }
    if (
      isEntityArmsAmmunition === false &&
      isEntityCurrencyExchange === false &&
      isEntityMarijuana === false &&
      isEntityMoneyLending === false &&
      isEntityNarcotics === false &&
      isEntityPreciousMetals === false &&
      isEntityOnlineGambling === false &&
      isEntityNotForProfit === false &&
      isEntityMoneyService === false &&
      isEntityForeignGovernments === false &&
      isEntityCasinos === false &&
      isEntityAdultEntertainment === false
    ) {
      businessInvolved = 7
    }

    this.setState({
      address1: street1 || '',
      address2: street2 || '',
      city: city || '',
      postal: postalCode || '',
      businessGroup: isEntityAssociation || false,
      businessInvolved: businessInvolved || null,
      formationJurisdiction: formationJurisdiction || '',
      entityType: entityType || '',
      dateOfFormation: formationDate || '',
      industrySector: industry || '',
      businessName: legalName || '',
      dba: listDBA || '',
      taxID: taxIdNumber || '',
      countryCode: countryCode || null,
      selectedProvince: province || this.state.selectedProvince,
      documents: businessAddress?.documents || [],
    })

    let countryName
    let pickableCountrySubs = []
    if (countryCode) {
      countryName = countryCodes().filter(a => a.code == countryCode)[0].name
      const isoCountry = iso3166.country(countryCode)
      const subArr = Object.values(isoCountry.sub)
      pickableCountrySubs = subArr.map(a => a.name)
      pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
        if (a < b) return -1
        if (b < a) return 1
        return 0
      })
      this.setState({selectedCountry: countryName, pickableCountrySubs})
    }

    if (countryCode && province) {
      const regionCode = province
      const subData = iso3166.subdivision(countryCode, regionCode)
      const selectedProvince = subData.name

      let jurisdictionFormationError = null
      if (countryCode == 'US' && this.props.loanData?.jurisdiction?.province != regionCode) {
        jurisdictionFormationError = 'Must match jurisdiction of request'
      }

      this.setState({selectedProvince, regionCode, jurisdictionFormationError})
    }

    if (refresh) {
      this.scroll.scrollToEnd({animated: true})
    }
  }

  onSelect = selectedCountry => {
    const countryCode = countryCodes().filter(a => a.name == selectedCountry)[0].code
    const isoCountry = iso3166.country(countryCode)
    let pickableCountrySubs = []
    const subArr = Object.values(isoCountry.sub)
    pickableCountrySubs = subArr.map(a => a.name)
    pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
      if (a < b) return -1
      if (b < a) return 1
      return 0
    })

    this.setState({
      selectedCountry,
      countryCode,
      pickableCountrySubs,
      //subArr,
      selectedProvince: null,
      regionCode: null,
    })
  }

  /*
  onProvinceSelect = selectedProvince => {
    console.log('onProvinceSelect', this.state.countryCode);
    const subData = iso3166.subdivision(this.state.countryCode, selectedProvince);
    const regionCode = subData.regionCode;
    this.setState({
      selectedProvince,
      regionCode,
    });
  };
  */

  onProvinceSelect = selectedProvince => {
    console.log('proinve selsct', this.state.selectedJurisdiction, selectedProvince)
    const countryCode = countryCodes().filter(a => a.name == this.state.selectedJurisdiction)[0].code
    const subData = iso3166.subdivision(countryCode, selectedProvince)
    console.log('subData', subData)
    const regionCode = subData?.regionCode

    let jurisdictionFormationError = null
    if (this.props.loanData?.jurisdiction?.countryCode == 'US' && this.props.loanData?.jurisdiction?.province != regionCode) {
      jurisdictionFormationError = 'Must match jurisdiction of request'
    }
    this.setState({
      selectedProvince,
      regionCode,
      jurisdictionFormationError,
    })
  }

  updateField = (text, type) => {
    const state = this.state
    if (type == 'dateOfFormation') {
      const textBefore = text
      text = formatDateText(text, state[type])
      if (text?.length != 0 && !text) {
        state.dateNumbersError = true
        text = textBefore
      } else {
        state.dateNumbersError = false
      }
    }
    state[type] = text
    state.redoGovtId = false
    this.setState(state)
  }

  toggleAcknowledge = num => {
    if (num == 1) {
      this.setState({acknowledge1: !this.state.acknowledge1})
    }
    if (num == 2) {
      this.setState({acknowledge2: !this.state.acknowledge2})
    }
  }

  toggleBusinessInvolved = num => {
    if (this.state.businessInvolved == num) {
      num = null
    }
    this.setState({businessInvolved: num})
  }

  toggleActiveMilitary = trueFalse => {
    this.setState({activeMilitary: trueFalse})
  }

  toggleBusinessGroup = trueFalse => {
    this.setState({businessGroup: trueFalse})
  }

  toIdVerify = () => {
    this.setState({loading: true})
    const {accountRef} = this.props
    const accounts = this.props?.user?.accounts
    let businessProfile
    if (accounts) {
      businessProfile = this.props?.user?.accounts?.filter(a => a.ref == accountRef)[0]?.businessProfile
    }

    const businessAddressID = businessProfile?.address?.id
    const businessAddressPayload = {
      city: this.state.city,
      countryCode: this.state.countryCode,
      document: [],
      postalCode: this.state.postal,
      province: this.state.regionCode,
      street1: this.state.address1,
      street2: this.state.address2,
    }

    const businessArr = [
      'isEntityCurrencyExchange',
      'isEntityMoneyLending',
      'isEntityPreciousMetals',
      'isEntityArmsAmmunition',
      'isEntityNarcotics',
      'isEntityMarijuana',
      'none',
      'isEntityAdultEntertainment',
      'isEntityCasinos',
      'isEntityOnlineGambling',
      'isEntityForeignGovernments',
      'isEntityNotForProfit',
      'isEntityMoneyService',
    ]

    const chosenBusiness = businessArr[this.state.businessInvolved - 1]

    const dateMonth = this.state.dateOfFormation.split('/')[0]
    const dateDay = this.state.dateOfFormation.split('/')[1]
    const dateYear = this.state.dateOfFormation.split('/')[2]
    const dateWithDashes = `${dateYear}-${dateMonth}-${dateDay}T00:00:00.000Z`
    const entityPayload = {
      entityType: this.state.entityType,
      formationDate: dateWithDashes,
      industry: this.state.industrySector,
      formationJurisdiction: this.state.formationJurisdiction,
      isEntityAssociation: this.state.businessGroup,
      isEntityInvolved: chosenBusiness,
      legalName: this.state.businessName,
      listDBA: this.state.dba,
      taxIdNumber: this.state.taxID,
    }

    this.props.WebService.patchAddress(businessAddressID, businessAddressPayload)
      .then(res => this.props.WebService.patchBusiness(entityPayload))
      .then(res => this.props.WebService.getSaltAccount())
      .then(res => this.props.WebService.businessVerification())
      .then(res => this.props.WebService.getSaltUser())
      .then(res => {
        this.props.dispatch(updateUser(res.data))
        return this.props.WebService.getAllAccounts()
      })
      .then(res => {
        this.props.dispatch(updateAccountPlus(res.data))
        this.setState({loading: false})
        this.props.dispatch(increaseUnit21Refresh())
      })
      .catch(err => {
        this.setState({loading: false})
        console.log('updateBusinessEntity err', err)
      })
  }

  onSelectEntity = entityType => {
    this.setState({entityType})
  }

  openImageSelect = documentType => {
    this.props.dispatch(askingForPermissions(true))
    const options = {
      title: 'Select Document',
      maxWidth: 2200,
      maxHeight: 2200,
      quality: 1.0,
      storageOptions: {
        skipBackup: true,
        path: 'images',
        cameraRoll: true,
        waitUntilSaved: true,
      },
      //takePhotoButtonTitle: null,
      noData: false,
      mediaType: 'photo',
    }

    this.setState({
      refreshingUploadAddress: documentType == 'address',
      refreshingUploadBusiness: documentType == 'business',

      imageError: false,
    })

    ImagePicker.launchImageLibrary(options, async response => {
      if (response.didCancel) {
        this.setState({
          refreshingUploadAddress: false,
          refreshingUploadBusiness: false,
        })
        return
      } else if (response.error) {
        this.setState({
          refreshingUploadAddress: false,
          refreshingUploadBusiness: false,
        })
        return
      } else if (response.customButton) {
        //console.log('User tapped custom button: ', response.customButton)
      } else {
        response = response.assets[0]
        const photo = {uri: response.uri}
        const formdata = new FormData()

        const validImage = this.validateImage(response.type, response.fileSize)
        if (validImage === 'type') {
          this.setState({
            refreshingUploadAddress: false,
            refreshingUploadBusiness: false,
            imageError: 'type',
          })
          return
        } else if (validImage === 'size') {
          this.setState({
            refreshingUploadAddress: false,
            refreshingUploadBusiness: false,
            imageError: 'size',
          })
          return
        }

        let path = response.uri
        if (Platform.OS === 'ios') {
          path = '~' + path.substring(path.indexOf('/Documents'))
        }
        if (!response.fileName) response.fileName = path.split('/').pop()

        formdata.append('file', [
          {
            uri: Platform.OS === 'android' ? photo.uri : photo.uri.replace('file://', ''),
            name: response.fileName || 'testName.jpg',
            type: response.type,
            size: response.fileSize,
          },
        ])
        formdata.append('name', response.fileName)
        formdata.append('key', 'true')

        const {accountRef} = this.props
        const accounts = this.props?.user?.accounts
        let businessProfile
        if (accounts) {
          businessProfile = this.props?.user?.accounts?.filter(a => a.ref == accountRef)[0]?.businessProfile
        }

        const businessAddressID = businessProfile?.address?.id
        const businessProfileID = businessProfile?.id

        //https://borrower-portal.stage.saltlending.tech/api/v0/documents/0be4cfb6-fa28-44b1-92a6-d065e796233f/address/other_proof_of_address?ref=2

        const docType = documentType == 'address' ? 'other_proof_of_address' : 'verification_of_business'

        const referenceType = documentType == 'address' ? 'address' : 'business_profile'

        const referenceId = documentType == 'address' ? businessAddressID : businessProfileID

        this.props.WebService.uploadDocument(referenceId, referenceType, docType, photo.uri.replace('file://', ''), response.fileName)
          .then(res => {
            const documents = this.state.documents
            const parsedData = JSON.parse(res.data)

            documents.push({
              name: response.fileName,
              id: parsedData.id,
              type: documentType == 'address' ? 'other_proof_of_address' : 'verification_of_business',
            })

            this.setState({
              refreshingUploadAddress: false,
              refreshingUploadBusiness: false,
              documents,
            })

            //refresh user data - so docs show up on back and forth
            //this.getAccounts()
          })
          .catch(err => {
            console.log('upload doc err', err)
            this.setState({
              refreshingUploadAddress: false,
              refreshingUploadBusiness: false,
            })
          })
      }
    })
  }

  validateImage = (type, size) => {
    const fileTypes = ['application/pdf', 'image/jpg', 'image/jpeg', 'image/png', 'image/gif', 'image/tif', 'image/tiff']
    if (!fileTypes.includes(type)) {
      return 'type'
    }
    if (size / 1000000 > 50) {
      return 'size'
    }
    return true
  }

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1
    this.props.navigation.pop(stackDepth)
  }

  removeDocument = id => {
    this.props.WebService.removeDocument(id)
      .then(res => {
        const documents = this.state.documents.filter(a => a.id != id)
        this.setState({documents})
      })
      .catch(err => {
        console.log('documents err', err)
      })
  }

  render() {
    let continueAllowed = true
    if (
      this.state.businessName == '' ||
      this.state.entityType == '' ||
      this.state.industrySector == '' ||
      this.state.dateOfFormation == '' ||
      this.state.taxID == '' ||
      this.state.address1 == '' ||
      this.state.city == '' ||
      !this.state.selectedProvince ||
      this.state.postal == '' ||
      !this.state.businessInvolved ||
      this.state.jurisdictionFormationError
    ) {
      continueAllowed = false
    }

    let bothBusinessDocs = false

    const addressProofDocs = this.state.documents.filter(a => a.type == 'other_proof_of_address')
    const businessDocs = this.state.documents.filter(a => a.type == 'verification_of_business')

    if (continueAllowed && this.state.showDocs) {
      if (addressProofDocs.length > 0 && businessDocs.length > 0) {
        bothBusinessDocs = true
      }
      continueAllowed = bothBusinessDocs
    }

    const showAddressDocs = addressProofDocs.map((a, k) => (
      <View style={styles.unit21ShowFilesBox} key={k}>
        <View style={styles.unit21ShowFilesName}>
          <TextReg style={{color: '#000'}}>{a.name}</TextReg>
        </View>
        <TouchableOpacity onPress={() => this.removeDocument(a.id)}>
          <Image
            source={removeFileImg}
            style={{
              height: 30,
              width: 30,
            }}
          />
        </TouchableOpacity>
      </View>
    ))

    const showBusinessDocs = businessDocs.map((a, k) => (
      <View style={styles.unit21ShowFilesBox} key={k}>
        <View style={styles.unit21ShowFilesName}>
          <TextReg style={{color: '#000'}}>{a.name}</TextReg>
        </View>
        <TouchableOpacity onPress={() => this.removeDocument(a.id)}>
          <Image
            source={removeFileImg}
            style={{
              height: 30,
              width: 30,
            }}
          />
        </TouchableOpacity>
      </View>
    ))

    let stackDepth = this.props.route?.params?.stackDepth || 1

    let dateError = false

    /*
    let dateString = new Date(this.state.dateOfFormation)
    let dateNow = new Date()
    */

    const [d, m, y] = this.state.dateOfFormation.split('/')
    const dateString = new Date(y, m - 1, d)
    let dateNow = new Date()

    if (
      this.state.dateOfFormation?.length == 10 &&
      (dateString == 'Invalid Date' ||
        dateString?.valueOf() < -2208963600000 || //before 1900
        dateString?.valueOf() > dateNow?.valueOf()) // before 1900
    ) {
      dateError = true
    }
    if (this.state.dateOfFormation?.length > 10) {
      dateError = true
    }

    return (
      <View
        style={{
          flex: 1,
          backgroundColor: '#28283D',
          alignSelf: 'stretch',
        }}>
        <BackgroundHeader
          title={'Verification'}
          close
          closeFn={this.closeUnit21}
          goBack={() => (stackDepth <= 2 ? this.closeUnit21() : this.props.navigation.goBack())}
        />
        <KeyboardAwareScrollView
          ref={scroll => {
            this.scroll = scroll
          }}
          style={{alignSelf: 'stretch', paddingLeft: 30, paddingRight: 30}}
          enableResetScrollToCoords={false}
          extraScrollHeight={24}>
          <>
            <TextReg style={styles.unit21InfoInputTitle}>Business Information</TextReg>
            <TextInput
              style={styles.unit21InfoInput}
              onChangeText={text => this.updateField(text, 'businessName')}
              ref={input => (this.inputs.businessName = input)}
              value={this.state.businessName}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              placeholder={'Business Name'}
              placeholderTextColor={'#999'}
              onSubmitEditing={() => this.inputs.dba.focus()}
              keyboardAppearance="dark"
            />
            <TextInput
              style={styles.unit21InfoInput}
              onChangeText={text => this.updateField(text, 'dba')}
              ref={input => (this.inputs.dba = input)}
              value={this.state.dba}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              placeholder={'DBA (if any)'}
              placeholderTextColor={'#999'}
              onSubmitEditing={() => this.inputs.address2.focus()}
              keyboardAppearance="dark"
            />
            <LocationSelect
              options={this.state.entityTypeList}
              onSelect={this.onSelectEntity}
              placeholder={this.state.entityType ? this.state.entityType : 'Entity Type'}
              searchable={'no'}
              placeholderStyle={{
                color: this.state.entityType ? '#fff' : '#999',
              }}
            />
            <TextInput
              style={styles.unit21InfoInput}
              onChangeText={text => this.updateField(text, 'industrySector')}
              ref={input => (this.inputs.industrySector = input)}
              value={this.state.industrySector}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              placeholder={'Industry Sector'}
              placeholderTextColor={'#999'}
              onSubmitEditing={() => this.inputs.taxID.focus()}
              keyboardAppearance="dark"
            />
            <TextInput
              style={styles.unit21InfoInput}
              onChangeText={text => this.updateField(text, 'formationJurisdiction')}
              ref={input => (this.inputs.formationJurisdiction = input)}
              value={this.state.formationJurisdiction}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              placeholder={'Jurisdiction of Formation'}
              placeholderTextColor={'#999'}
              onSubmitEditing={() => this.inputs.dateOfFormation.focus()}
              keyboardAppearance="dark"
            />

            <TextReg style={dateError ? styles.unit21InfoInputTitleError : styles.unit21InfoInputTitle}>Date of Formation</TextReg>
            <TextInput
              style={dateError ? styles.unit21InfoInputDateError : styles.unit21InfoInput}
              onChangeText={text => this.updateField(text, 'dateOfFormation')}
              ref={input => (this.inputs.dateOfFormation = input)}
              value={this.state.dateOfFormation}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              placeholder={'MM/DD/YYYY'}
              placeholderTextColor={'#999'}
              keyboardType={'numeric'}
              onSubmitEditing={() => this.inputs.industrySector.focus()}
              keyboardAppearance="dark"
            />

            <TextReg style={this.state.redoGovtId ? styles.unit21InfoInputTitleError : styles.unit21InfoInputTitle}>EIN/Tax ID</TextReg>
            <TextInput
              style={this.state.redoGovtId ? styles.unit21InfoInputError : styles.unit21InfoInput}
              onChangeText={text => this.updateField(text, 'taxID')}
              ref={input => (this.inputs.taxID = input)}
              value={this.state.taxID}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              placeholder={''}
              placeholderTextColor={'#999'}
              keyboardType={'numeric'}
              onSubmitEditing={() => Keyboard.dismiss()}
              keyboardAppearance="dark"
            />
          </>

          <TextReg style={styles.unit21InfoInputTitle}>{'Address'}</TextReg>
          <TextInput
            style={styles.unit21InfoInput}
            onChangeText={text => this.updateField(text, 'address1')}
            ref={input => (this.inputs.address1 = input)}
            value={this.state.address1}
            underlineColorAndroid="transparent"
            blurOnSubmit
            returnKeyType={'next'}
            placeholder={'Address Line 1'}
            placeholderTextColor={'#999'}
            onSubmitEditing={() => this.inputs.address2.focus()}
            keyboardAppearance="dark"
          />
          <TextInput
            style={styles.unit21InfoInput}
            onChangeText={text => this.updateField(text, 'address2')}
            ref={input => (this.inputs.address2 = input)}
            value={this.state.address2}
            underlineColorAndroid="transparent"
            blurOnSubmit
            returnKeyType={'next'}
            placeholder={'Address Line 2'}
            placeholderTextColor={'#999'}
            onSubmitEditing={() => Keyboard.dismiss()}
            keyboardAppearance="dark"
          />
          <LocationSelect
            options={this.state.countryCodes}
            onSelect={this.onSelect}
            placeholder={this.state.selectedCountry ? this.state.selectedCountry : 'Country'}
          />
          <TextInput
            style={styles.unit21InfoInput}
            onChangeText={text => this.updateField(text, 'city')}
            ref={input => (this.inputs.city = input)}
            value={this.state.city}
            underlineColorAndroid="transparent"
            blurOnSubmit
            returnKeyType={'next'}
            placeholder={'City'}
            placeholderTextColor={'#999'}
            onSubmitEditing={() => Keyboard.dismiss()}
            keyboardAppearance="dark"
          />
          <View
            style={{
              flexDirection: 'row',
              alignSelf: 'stretch',
              marginBottom: 10,
            }}>
            <View style={{flex: 1}}>
              <LocationSelect
                onSelect={this.onProvinceSelect}
                options={this.state.pickableCountrySubs}
                placeholder={this.state.selectedProvince ? this.state.selectedProvince : 'State / Province'}
              />
            </View>
            <View style={{height: 10, width: 10}} />
            <View style={{flex: 1}}>
              <TextInput
                style={styles.unit21InfoInputHalf}
                onChangeText={text => this.updateField(text, 'postal')}
                ref={input => (this.inputs.postal = input)}
                value={this.state.postal}
                underlineColorAndroid="transparent"
                blurOnSubmit
                returnKeyType={'next'}
                placeholder={'Postal Code'}
                placeholderTextColor={'#999'}
                onSubmitEditing={() => Keyboard.dismiss()}
                keyboardAppearance="dark"
              />
            </View>
          </View>
          {this.state.jurisdictionFormationError && (
            <View
              style={{
                alignSelf: 'stretch',
                alignItems: 'center',
                marginTop: -12,
                marginBottom: 14,
              }}>
              <TextReg style={{color: '#e5705a', fontSize: 18}}>{this.state.jurisdictionFormationError}</TextReg>
            </View>
          )}

          <>
            <TextReg style={styles.unit21InfoInputTitleBusiness}>Is the entity involved in any of the following services?</TextReg>
            <TouchableOpacity onPress={() => this.toggleBusinessInvolved(1)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
              <View style={styles.toggleAcknowledgeView}>
                <Image
                  source={agreedYes}
                  style={[
                    styles.unit21ToggleCheckImg,
                    {
                      opacity: this.state.businessInvolved == 1 ? 1 : 0,
                    },
                  ]}
                />
              </View>
              <TextReg style={styles.unit21AcknowledgeTitle}>Currency Exchange/Money Changer Services</TextReg>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => this.toggleBusinessInvolved(2)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
              <View style={styles.toggleAcknowledgeView}>
                <Image
                  source={agreedYes}
                  style={[
                    styles.unit21ToggleCheckImg,
                    {
                      opacity: this.state.businessInvolved == 2 ? 1 : 0,
                    },
                  ]}
                />
              </View>
              <TextReg style={styles.unit21AcknowledgeTitle}>Money Lending/Pawning</TextReg>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => this.toggleBusinessInvolved(3)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
              <View style={styles.toggleAcknowledgeView}>
                <Image
                  source={agreedYes}
                  style={[
                    styles.unit21ToggleCheckImg,
                    {
                      opacity: this.state.businessInvolved == 3 ? 1 : 0,
                    },
                  ]}
                />
              </View>
              <TextReg style={styles.unit21AcknowledgeTitle}>Precious Metals, Stones, or Jewelry</TextReg>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => this.toggleBusinessInvolved(4)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
              <View style={styles.toggleAcknowledgeView}>
                <Image
                  source={agreedYes}
                  style={[
                    styles.unit21ToggleCheckImg,
                    {
                      opacity: this.state.businessInvolved == 4 ? 1 : 0,
                    },
                  ]}
                />
              </View>
              <TextReg style={styles.unit21AcknowledgeTitle}>Arms & Ammunition</TextReg>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => this.toggleBusinessInvolved(5)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
              <View style={styles.toggleAcknowledgeView}>
                <Image
                  source={agreedYes}
                  style={[
                    styles.unit21ToggleCheckImg,
                    {
                      opacity: this.state.businessInvolved == 5 ? 1 : 0,
                    },
                  ]}
                />
              </View>
              <TextReg style={styles.unit21AcknowledgeTitle}>Narcotics/Pharmaceuticals</TextReg>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => this.toggleBusinessInvolved(6)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
              <View style={styles.toggleAcknowledgeView}>
                <Image
                  source={agreedYes}
                  style={[
                    styles.unit21ToggleCheckImg,
                    {
                      opacity: this.state.businessInvolved == 6 ? 1 : 0,
                    },
                  ]}
                />
              </View>
              <TextReg style={styles.unit21AcknowledgeTitle}>Marijuana-Related Business</TextReg>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => this.toggleBusinessInvolved(8)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
              <View style={styles.toggleAcknowledgeView}>
                <Image
                  source={agreedYes}
                  style={[
                    styles.unit21ToggleCheckImg,
                    {
                      opacity: this.state.businessInvolved == 8 ? 1 : 0,
                    },
                  ]}
                />
              </View>
              <TextReg style={styles.unit21AcknowledgeTitle}>Adult Entertainment</TextReg>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => this.toggleBusinessInvolved(9)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
              <View style={styles.toggleAcknowledgeView}>
                <Image
                  source={agreedYes}
                  style={[
                    styles.unit21ToggleCheckImg,
                    {
                      opacity: this.state.businessInvolved == 9 ? 1 : 0,
                    },
                  ]}
                />
              </View>
              <TextReg style={styles.unit21AcknowledgeTitle}>Casinos</TextReg>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => this.toggleBusinessInvolved(10)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
              <View style={styles.toggleAcknowledgeView}>
                <Image
                  source={agreedYes}
                  style={[
                    styles.unit21ToggleCheckImg,
                    {
                      opacity: this.state.businessInvolved == 10 ? 1 : 0,
                    },
                  ]}
                />
              </View>
              <TextReg style={styles.unit21AcknowledgeTitle}>Online Gambling/Gaming</TextReg>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => this.toggleBusinessInvolved(11)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
              <View style={styles.toggleAcknowledgeView}>
                <Image
                  source={agreedYes}
                  style={[
                    styles.unit21ToggleCheckImg,
                    {
                      opacity: this.state.businessInvolved == 11 ? 1 : 0,
                    },
                  ]}
                />
              </View>
              <TextReg style={styles.unit21AcknowledgeTitle}>Foreign Governments & Officials</TextReg>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => this.toggleBusinessInvolved(12)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
              <View style={styles.toggleAcknowledgeView}>
                <Image
                  source={agreedYes}
                  style={[
                    styles.unit21ToggleCheckImg,
                    {
                      opacity: this.state.businessInvolved == 12 ? 1 : 0,
                    },
                  ]}
                />
              </View>
              <TextReg style={styles.unit21AcknowledgeTitle}>Not-for-Profits/Charitable Organizations</TextReg>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => this.toggleBusinessInvolved(13)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
              <View style={styles.toggleAcknowledgeView}>
                <Image
                  source={agreedYes}
                  style={[
                    styles.unit21ToggleCheckImg,
                    {
                      opacity: this.state.businessInvolved == 13 ? 1 : 0,
                    },
                  ]}
                />
              </View>
              <TextReg style={styles.unit21AcknowledgeTitle}>Money Service Businesses</TextReg>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => this.toggleBusinessInvolved(7)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
              <View style={styles.toggleAcknowledgeView}>
                <Image
                  source={agreedYes}
                  style={[
                    styles.unit21ToggleCheckImg,
                    {
                      opacity: this.state.businessInvolved == 7 ? 1 : 0,
                    },
                  ]}
                />
              </View>
              <TextReg style={styles.unit21AcknowledgeTitle}>None of the above</TextReg>
            </TouchableOpacity>
            <View style={{height: 20, alignSelf: 'stretch'}} />
            <TextReg style={styles.unit21InfoInputTitleBusiness}>
              Is the entity associated with a government, political entity, military or religious group?
            </TextReg>
            <View style={styles.unit21MilitaryBox}>
              <TouchableOpacity
                onPress={() => this.toggleBusinessGroup(false)}
                style={{
                  height: 44,
                  borderRadius: 23,
                  width: 60,
                  backgroundColor: !this.state.businessGroup ? '#00FFBD' : '#FFF',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginLeft: 4,
                }}>
                <TextReg
                  style={{
                    color: '#000',
                  }}>
                  NO
                </TextReg>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => this.toggleBusinessGroup(true)}
                style={{
                  height: 44,
                  borderRadius: 23,
                  width: 60,
                  backgroundColor: this.state.businessGroup ? '#00FFBD' : '#FFF',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 4,
                }}>
                <TextReg style={{color: '#000'}}>YES</TextReg>
              </TouchableOpacity>
            </View>
          </>
          {this.state.showDocs && (
            <>
              <View
                style={{
                  marginTop: 10,
                  width: 300,
                  alignSelf: 'center',
                  borderTopWidth: 1.5,
                  borderColor: '#E6705B',
                  marginBottom: 6,
                }}
              />
              <TextReg style={styles.unit21InfoInputTitleBusinessError}>
                Your business could not be found with the information provided above. Please provide additional documents so that we can
                verify your business information.
              </TextReg>
              <>
                <TextReg
                  style={{
                    color: '#FFF',
                    fontSize: 17,
                    marginBottom: 14,
                    marginTop: 12,
                  }}>
                  Please the following documents.
                </TextReg>
                <TextReg style={{color: '#FFF', marginBottom: 4, marginLeft: 4}}>1. Proof of Address</TextReg>
                <TextReg
                  style={{
                    color: '#FFF',
                    marginBottom: 2,
                    marginLeft: 10,
                    fontSize: 12,
                    fontStyle: 'italic',
                    opacity: 0.7,
                  }}>
                  Provide ONE of the following:
                </TextReg>
                <TextReg style={{color: '#FFF', marginBottom: 2, marginLeft: 10}}>- Latest Bank Statement</TextReg>
                <TextReg style={{color: '#FFF', marginBottom: 2, marginLeft: 10}}>- Latest Utility Bill</TextReg>
                <TextReg style={{color: '#FFF', marginBottom: 2, marginLeft: 10}}>- Tax Filing</TextReg>
                <TextReg style={{color: '#FFF', marginBottom: 2, marginLeft: 10}}>- Government Issued Document</TextReg>
                <Button
                  isLoading={this.state.refreshingUploadAddress}
                  style={{
                    alignSelf: 'stretch',
                    backgroundColor: '#28283D',
                    borderColor: '#FFF',
                    borderWidth: 2,
                    marginTop: 8,
                    marginBottom: 8,
                  }}
                  theme={'secondary'}
                  onPress={() => this.openImageSelect('address')}>
                  <TextReg style={{color: '#FFF', fontSize: 18}}>UPLOAD DOCUMENT</TextReg>
                </Button>

                {showAddressDocs}
                <View style={{opacity: showAddressDocs.length > 0 ? 1 : 0.7}}>
                  <TextReg
                    style={{
                      color: '#FFF',
                      marginBottom: 4,
                      marginLeft: 4,
                      marginTop: 20,
                    }}>
                    2. Verification of Business
                  </TextReg>
                  <TextReg
                    style={{
                      color: '#FFF',
                      marginBottom: 2,
                      marginLeft: 10,
                      fontSize: 12,
                      fontStyle: 'italic',
                      opacity: 0.7,
                    }}>
                    Provide ONE of the following:
                  </TextReg>
                  <TextReg style={{color: '#FFF', marginBottom: 2, marginLeft: 10}}>- Corporation Certification</TextReg>
                  <TextReg style={{color: '#FFF', marginBottom: 2, marginLeft: 10}}>- Article of Incorporation</TextReg>
                  <TextReg style={{color: '#FFF', marginBottom: 2, marginLeft: 10}}>- Registration</TextReg>
                  <Button
                    disabled={showAddressDocs.length <= 0}
                    isLoading={this.state.refreshingUploadBusiness}
                    style={{
                      alignSelf: 'stretch',
                      backgroundColor: '#28283D',
                      borderColor: '#FFF',
                      borderWidth: 2,
                      marginTop: 8,
                      marginBottom: 8,
                    }}
                    theme={'secondary'}
                    onPress={() => this.openImageSelect('business')}>
                    <TextReg style={{color: '#FFF', fontSize: 18}}>UPLOAD DOCUMENT</TextReg>
                  </Button>
                  {showBusinessDocs}
                </View>
              </>
            </>
          )}

          <Button
            isLoading={this.state.loading}
            disabled={!continueAllowed}
            style={{
              alignSelf: 'stretch',
              marginTop: 20,
              backgroundColor: '#00FFBD',
              marginBottom: 30,
            }}
            onPress={() => this.toIdVerify()}
            theme={'secondary'}>
            <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
          </Button>
        </KeyboardAwareScrollView>
      </View>
    )
  }
}

BusinessVerification1.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  tokenPrices: state.user.prices,
  user: state.user.user,
  accountRef: state.auth.account.ref,
  WebService: state.auth.WebService,
  pauseUnit21: state.user.pauseUnit21,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(BusinessVerification1)
