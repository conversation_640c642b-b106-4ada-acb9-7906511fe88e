import React, {Component} from 'react';
import {View, Image, TouchableOpacity, Dimensions, SafeAreaView, Linking, AsyncStorage} from 'react-native';
import {connect} from 'react-redux';

const {height: ScreenHeight} = Dimensions.get('window');

import {BackgroundHeader, TextReg, Button} from '../../../components';
import signLoanDocs from '../../../imgs/graphics/signLoanDocs.png';

import styles from '../styles';

class LoanDocs extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  componentDidMount() {
    console.log('LoanDocs');
  }

  contactUs = () => {
    Linking.openURL('mailto:<EMAIL>');
  };

  closeUnit21 = () => {
    let stackDepth = this.props.route?.params?.stackDepth || 1;
    this.props.navigation.pop(stackDepth);
  };

  render() {
    console.log('loanDocs loanData', this.props.loanData);
    return (
      <View
        style={{
          //flex: 1,
          backgroundColor: '#28283D',
          height: ScreenHeight,
          alignSelf: 'stretch',
          paddingBottom: 80,
        }}>
        <BackgroundHeader title={' '} goBack={() => this.props.navigation.goBack()} />
        <View
          style={{
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
            flex: 1,
            justifyContent: 'space-between',
          }}>
          <View style={{alignItems: 'center'}}>
            <Image
              source={signLoanDocs}
              style={{
                marginTop: 10,
                height: 80,
                width: 80,
              }}
            />
            <TextReg
              style={{
                color: '#FFF',
                fontSize: 20,
                marginBottom: 22,
                marginTop: 10,
                width: 220,
                textAlign: 'center',
              }}>
              Sign Your Loan Docs to Get Funded
            </TextReg>

            <TextReg
              style={{
                color: '#FFF',
                fontSize: 16,
                marginBottom: 10,
                textAlign: 'center',
              }}>
              We’ve sent your loan docs to the email you’ve provided. Once your loan docs are signed and submitted, we will prepare your
              loan for funding.
            </TextReg>
          </View>
          <View style={{alignItems: 'center'}}>
            <TextReg style={{marginBottom: 20, width: 250, textAlign: 'center', fontSize: 16}}>
              Have a question in the meantime? Get in touch with us.
            </TextReg>
            <Button style={{alignSelf: 'stretch', marginBottom: 10, marginBottom: 30}} onPress={() => this.contactUs()}>
              <TextReg style={{color: '#000', fontSize: 18}}>CONTACT US</TextReg>
            </Button>
          </View>
        </View>
      </View>
    );
  }
}

LoanDocs.navigationOptions = ({navigation}) => ({
  title: 'Info',
  header: <View style={{height: 40, position: 'absolute'}} />,
});

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  tokenPrices: state.user.prices,
  WebService: state.auth.WebService,
});

export default connect(mapStateToProps)(LoanDocs);
