import React, {useEffect, useState} from 'react'
import {StyleSheet, View, Platform, TouchableOpacity, Image, ScrollView} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {TextReg, TextBold, BackgroundHeader, But<PERSON>} from '../../../../components'
import {useDispatch, useSelector} from 'react-redux'
import {updateFlowB1} from '../../../../store/user/user.actions'
import Unit21Progress from '../Unit21Progress'
import styles from '../../styles'
import agreedYes from '../../../../imgs/agreedYes.png'

let FlowB1Entity = ({navigation, route}) => {
  let dispatch = useDispatch()
  let b1Obj = useSelector(state => state.user.flowB1 || {})
  let WebService = useSelector(state => state.auth.WebService || {})
  let [pick, setPick] = useState(b1Obj?.entity1 || null)

  //load slow from async for 1st one
  useEffect(() => {
    setPick(b1Obj?.entity1 || false)
  }, [b1Obj])

  let stackDepth = route?.params?.stackDepth || 1

  let closeUnit21 = () => {
    navigation.pop(stackDepth)
  }

  let next = async () => {
    let nameArr = [
      'isEntityCurrencyExchange',
      'isEntityMoneyLending',
      'isEntityPreciousMetals',
      'isEntityArmsAmmunition',
      'isEntityNarcotics',
      'isEntityMarijuana',
      'isEntityAdultEntertainment',
      'isEntityCasinos',
      'isEntityOnlineGambling',
      'isEntityForeignGovernments',
      'isEntityNotForProfit',
      'isEntityMoneyService',
      'none',
    ]
    let chosenBusiness = nameArr[pick - 1]
    let payload = {
      isEntityInvolved: chosenBusiness,
    }
    let res = await WebService.patchBusiness(payload)
    dispatch(updateFlowB1({entity1: pick}))
    navigation.navigate('FlowB1Entity2', {stackDepth: stackDepth + 1})
  }

  let toggle = num => {
    console.log('toggle', num)
    if (pick != num) {
      setPick(num)
    } else {
      setPick(null)
    }
  }
  let arr = [
    {num: 1, name: 'Currency Exchange/Money Changer Services'},
    {num: 2, name: 'Money Lending/Pawning'},
    {num: 3, name: 'Precious Metals, Stones, or Jewelry'},
    {num: 4, name: 'Arms & Ammunition'},
    {num: 5, name: 'Narcotics/Pharmaceuticals'},
    {num: 6, name: 'Marijuana-Related Business'},
    {num: 7, name: 'Adult Entertainment'},
    {num: 8, name: 'Casinos'},
    {num: 9, name: 'Online Gambling/Gaming'},
    {num: 10, name: 'Foreign Governments & Officials'},
    {num: 11, name: 'Not-for-Profits/Charitable Organizations'},
    {num: 12, name: 'Money Service Businesses'},
    {num: 13, name: 'None of the above'},
  ]

  let showOptions = arr?.map((a, k) => {
    return (
      <TouchableOpacity key={k} onPress={() => toggle(a.num)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
        <View style={styles.toggleAcknowledgeView}>
          <Image
            source={agreedYes}
            style={[
              styles.unit21ToggleCheckImg,
              {
                opacity: pick == a.num ? 1 : 0,
              },
            ]}
          />
        </View>
        <TextReg style={styles.unit21AcknowledgeTitle}>{a.name}</TextReg>
      </TouchableOpacity>
    )
  })

  return (
    <ScrollView style={localStyles.box}>
      <View>
        <BackgroundHeader
          title={'Entity Question'}
          close
          closeFn={closeUnit21}
          goBack={() => (stackDepth <= 2 ? closeUnit21() : navigation.goBack())}
        />
        <Unit21Progress complete={1} />
        <TextReg
          style={{
            fontSize: 20,
            marginBottom: 10,
          }}>{`Is the entity involved in or providing any of the following services?`}</TextReg>
        {showOptions}
      </View>
      <Button
        isLoading={false}
        disabled={!pick}
        style={{
          alignSelf: 'stretch',
          marginTop: 20,
          backgroundColor: '#00FFBD',
          marginBottom: 50,
        }}
        onPress={() => next()}
        theme={'secondary'}>
        <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
      </Button>
    </ScrollView>
  )
}

export default FlowB1Entity

const localStyles = StyleSheet.create({
  box: {
    flex: 1,
    padding: 24,
    backgroundColor: '#28283D',
  },
})
