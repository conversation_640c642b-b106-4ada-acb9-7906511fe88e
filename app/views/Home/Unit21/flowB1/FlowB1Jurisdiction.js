import React, {useEffect, useState} from 'react'
import {StyleSheet, View, Platform, TouchableOpacity, Image, TextInput, Keyboard} from 'react-native'
import {useSelector} from 'react-redux'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {TextReg, TextBold, BackgroundHeader, Button, LocationSelect} from '../../../../components'
import Unit21Progress from '../Unit21Progress'
import iso3166 from 'iso-3166-2'
import countryCodes from '../../../../util/countryCodes'

import styles from '../../styles'

let FlowB1Jurisdiction = ({navigation, route}) => {
  let WebService = useSelector(state => state.auth.WebService || {})

  let stackDepth = route?.params?.stackDepth || 1
  let [country, setCountry] = useState(null)
  let [countryCode, setCountryCode] = useState(null)
  let [region, setRegion] = useState(null)
  let [regionCode, setRegionCode] = useState(null)
  let [error, setError] = useState(false)

  let [pickCodes, setPickCodes] = useState([])
  let [pickRegion, setPickRegion] = useState([])
  let loanData = useSelector(state => state.user.loanData || {})

  useEffect(() => {
    locationSetup()
  }, [])

  let locationSetup = () => {
    let pickableCountryCodes = countryCodes().map(a => a.name)
    pickableCountryCodes = ['United States'].concat(pickableCountryCodes)
    setPickCodes(pickableCountryCodes)

    //loan jurisdiction country & province
    let countryCode = loanData?.jurisdiction?.countryCode || ''
    setCountryCode(countryCode)
    let regionCode = loanData?.jurisdiction?.province || ''
    setRegionCode(regionCode)
    setRegion(regionCode)

    let country = countryCodes().filter(a => a.code == countryCode)[0].name || ''
    setCountry(country)

    if (countryCode != '') {
      const isoCountry = iso3166.country(countryCode)
      let pickableCountrySubs = []
      const subArr = Object.values(isoCountry.sub)
      const subData = iso3166.subdivision(countryCode, regionCode)
      let region = subData?.name
      setRegion(region)
      pickableCountrySubs = subArr.map(a => a.name)
      pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
        if (a < b) return -1
        if (b < a) return 1
        return 0
      })
      setPickRegion(pickableCountrySubs)
    }
  }

  let closeUnit21 = () => {
    navigation.pop(stackDepth)
  }

  let next = async () => {
    let formationJurisdiction = `${country}, ${region}`
    let payload = {
      formationJurisdiction,
    }
    await WebService.patchBusiness(payload)
    navigation.navigate('FlowB1Address', {stackDepth: stackDepth + 1})
  }

  let onSelect = selectedCountry => {
    const countryCode = countryCodes().filter(a => a.name == selectedCountry)[0].code
    const isoCountry = iso3166.country(countryCode)
    let pickableCountrySubs = []
    const subArr = Object.values(isoCountry.sub)
    pickableCountrySubs = subArr.map(a => a.name)
    pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
      if (a < b) return -1
      if (b < a) return 1
      return 0
    })
    setCountryCode(countryCode)
    setCountry(selectedCountry)
    setPickRegion(pickableCountrySubs)
    setRegion(null)
    setRegionCode(null)
    setError(false)
  }

  let onRegionSelect = selectedProvince => {
    const countryCode = countryCodes().filter(a => a.name == country)[0]?.code
    const subData = iso3166.subdivision(countryCode, selectedProvince)
    const regionCode = subData?.regionCode
    let region = subData?.name

    let jurisdictionFormationError = false
    if (loanData?.jurisdiction?.countryCode == 'US' && loanData?.jurisdiction?.province != regionCode) {
      jurisdictionFormationError = true
    }

    setRegion(region)
    setRegionCode(regionCode)
    setError(jurisdictionFormationError)
  }

  let goBack = () => {
    Keyboard.dismiss()
    stackDepth <= 2 ? closeUnit21() : navigation.goBack()
  }

  return (
    <View style={stylesLocal.box}>
      <KeyboardAwareScrollView
        ref={scroll => {
          scroll = scroll
        }}
        style={{flex: 1, alignSelf: 'stretch'}}
        enableResetScrollToCoords={false}>
        <BackgroundHeader title={'Entity Info'} close closeFn={closeUnit21} goBack={() => goBack()} />
        <Unit21Progress complete={5} />
        <TextReg style={{marginBottom: 5, marginTop: 10}}>Jurisdiction of Formation - Country</TextReg>
        <LocationSelect options={pickCodes} onSelect={onSelect} placeholder={country ? country : 'Country'} />
        <TextReg style={{marginBottom: 5, marginTop: 10}}>Jurisdiction of Formation - Province</TextReg>
        <LocationSelect options={pickRegion} onSelect={onRegionSelect} placeholder={region ? region : 'State / Province'} />
        {error && (
          <View
            style={{
              alignSelf: 'stretch',
              alignItems: 'center',
            }}>
            <TextReg style={{color: '#e5705a', fontSize: 18}}>{`Must match jurisdiction of request`}</TextReg>
          </View>
        )}
      </KeyboardAwareScrollView>
      <Button
        isLoading={false}
        disabled={!country || !region || error}
        style={{
          alignSelf: 'stretch',
          marginTop: 20,
          backgroundColor: '#00FFBD',
          marginBottom: 10,
        }}
        onPress={() => next()}
        theme={'secondary'}>
        <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
      </Button>
    </View>
  )
}

export default FlowB1Jurisdiction

const stylesLocal = StyleSheet.create({
  box: {
    flex: 1,
    padding: 24,
    backgroundColor: '#28283D',
    justifyContent: 'space-between',
  },
})
