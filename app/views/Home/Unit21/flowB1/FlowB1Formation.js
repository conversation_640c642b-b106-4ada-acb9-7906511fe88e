import React, {useEffect, useState} from 'react'
import {StyleSheet, View, Platform, TouchableOpacity, Image, TextInput, Keyboard} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {TextReg, TextBold, BackgroundHeader, Button, LocationSelect} from '../../../../components'
import {useDispatch, useSelector} from 'react-redux'
import {updateFlowB1} from '../../../../store/user/user.actions'
import {formatDateText} from '../../../../util/helpers'

import Unit21Progress from '../Unit21Progress'

import styles from '../../styles'

let FlowB1Formation = ({navigation, route}) => {
  let WebService = useSelector(state => state.auth.WebService || {})
  let dispatch = useDispatch()
  let b1Obj = useSelector(state => state.user.flowB1 || {})
  let stackDepth = route?.params?.stackDepth || 1
  let [formation, setFormation] = useState(b1Obj?.formation || '')

  let [sector, setSector] = useState(b1Obj?.sector || '')
  let [ein, setEin] = useState(b1Obj?.ein || '')
  let inputs = []

  useEffect(() => {
    //console.log('FlowB1Formation', navigation, route)
  }, [])

  let closeUnit21 = () => {
    navigation.pop(stackDepth)
  }

  let next = async () => {
    dispatch(updateFlowB1({formation, sector, ein}))
    const dateMonth = formation?.split('/')[0]
    const dateDay = formation?.split('/')[1]
    const dateYear = formation?.split('/')[2]
    const dateWithDashes = `${dateYear}-${dateMonth}-${dateDay}T00:00:00.000Z`

    let payload = {
      formationDate: dateWithDashes,
      industry: sector,
      taxIdNumber: ein,
    }
    await WebService.patchBusiness(payload)
    navigation.navigate('FlowB1Jurisdiction', {stackDepth: stackDepth + 1})
  }

  let formatDate = text => {
    let textBefore = text
    let oldText = formation
    text = formatDateText(text, oldText)
    if (text?.length != 0 && !text) {
      text = oldText
      if (text && text?.length == 9) {
        text = textBefore
      }
    } else {
      //
    }
    setFormation(text)
  }

  let dateError = false
  //let dateString = new Date(formation)

  const [d, m, y] = formation.split('/')
  const dateString = new Date(y, m - 1, d)

  let dateNow = new Date()

  if (
    formation?.length == 10 &&
    (dateString == 'Invalid Date' ||
      dateString?.valueOf() < -2208963600000 || //before 1900
      dateString?.valueOf() > dateNow?.valueOf()) // before 1900
  ) {
    dateError = true
  }
  if (formation?.length > 10) {
    dateError = true
  }

  let goBack = () => {
    Keyboard.dismiss()
    stackDepth <= 2 ? closeUnit21() : navigation.goBack()
  }

  return (
    <View style={stylesLocal.box}>
      <KeyboardAwareScrollView style={{flex: 1, alignSelf: 'stretch'}} enableResetScrollToCoords={true}>
        <BackgroundHeader title={'Entity Info'} close closeFn={closeUnit21} goBack={() => goBack()} />
        <Unit21Progress complete={4} />
        <TextReg style={{marginBottom: 5, marginTop: 10, color: dateError ? '#E6705B' : '#FFF'}}>Date of Formation</TextReg>
        <TextInput
          style={dateError ? styles.unit21InfoInputDateError : styles.unit21InfoInput}
          onChangeText={text => formatDate(text)}
          value={formation}
          underlineColorAndroid="transparent"
          blurOnSubmit
          returnKeyType={'next'}
          placeholder={'MM/DD/YYYY'}
          placeholderTextColor={'#999'}
          onSubmitEditing={() => inputs.sector.focus()}
          keyboardAppearance="dark"
          keyboardType={'numeric'}
        />
        <TextReg style={{marginBottom: 5}}>Industry Sector</TextReg>
        <TextInput
          style={styles.unit21InfoInput}
          onChangeText={text => setSector(text)}
          ref={input => (inputs.sector = input)}
          value={sector}
          underlineColorAndroid="transparent"
          blurOnSubmit
          returnKeyType={'next'}
          placeholder={'Industry Sector'}
          placeholderTextColor={'#999'}
          keyboardAppearance="dark"
          onSubmitEditing={() => inputs.sector.focus()}
        />
        <TextReg style={{marginBottom: 5}}>EIN/Tax ID</TextReg>
        <TextInput
          style={styles.unit21InfoInput}
          onChangeText={text => setEin(text)}
          ref={input => (inputs.ein = input)}
          value={ein}
          underlineColorAndroid="transparent"
          blurOnSubmit
          returnKeyType={'next'}
          placeholder={'EIN / Tax ID'}
          placeholderTextColor={'#999'}
          keyboardAppearance="dark"
          onSubmitEditing={() => Keyboard.dismiss()}
        />
      </KeyboardAwareScrollView>
      <Button
        isLoading={false}
        disabled={!formation || !sector || !ein || dateError}
        style={{
          alignSelf: 'stretch',
          marginTop: 20,
          backgroundColor: '#00FFBD',
          marginBottom: 10,
        }}
        onPress={() => next()}
        theme={'secondary'}>
        <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
      </Button>
    </View>
  )
}

export default FlowB1Formation

const stylesLocal = StyleSheet.create({
  box: {
    flex: 1,
    padding: 24,
    backgroundColor: '#28283D',
    justifyContent: 'space-between',
  },
})
