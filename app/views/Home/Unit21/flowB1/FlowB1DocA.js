import React, {useEffect, useState} from 'react'
import {StyleSheet, View, Platform, TouchableOpacity, Image} from 'react-native'
import * as ImagePicker from 'react-native-image-picker'
import {TextReg, TextBold, BackgroundHeader, Button} from '../../../../components'
import {useDispatch, useSelector} from 'react-redux'
import {updateFlowB1, updateAccountPlus} from '../../../../store/user/user.actions'
import Unit21Progress from '../Unit21Progress'
import removeFileImg from '../../../../imgs/closeX.png'
import styles from '../../styles'

let FlowB1DocA = ({navigation, route}) => {
  let dispatch = useDispatch()

  let stackDepth = route?.params?.stackDepth || 1
  let [answer, setAnswer] = useState(false)
  let [loading, setLoading] = useState(false)
  let [error, setError] = useState(false)
  let [docs, setDocs] = useState([])
  let [rejectedErr, setRejectedErr] = useState(false)

  let user = useSelector(state => state.user.user || {})
  let accountRef = useSelector(state => state.auth.account.ref || {})
  let WebService = useSelector(state => state.auth.WebService || {})

  useEffect(() => {
    getDocs()
  }, [])

  let getDocs = async () => {
    let accounts = user?.accounts
    let businessProfile
    if (accounts) {
      businessProfile = accounts?.filter(a => a.ref == accountRef)[0]?.businessProfile
    }
    let docs = businessProfile?.address?.documents || []
    let otherDocs = businessProfile?.documents || []
    docs = docs.concat(otherDocs)
    docs = docs?.filter(a => a.type == 'other_proof_of_address') || []
    let filteredDocs = docs?.filter(a => !a?.rejectedAt) || []
    if (docs?.length > 0 && filteredDocs?.length < 1) {
      setRejectedErr(true)
    }
    setDocs(filteredDocs)
  }

  let removeDocument = async id => {
    try {
      const res = await WebService.removeDocument(id)
      let newDocs = docs?.filter(a => a.id != id)
      if (!newDocs) {
        newDocs = []
      }
      setDocs(newDocs)
      getAcc()
    } catch (error) {
      console.error('Failed to remove document:', error)
    }
  }

  let closeUnit21 = () => {
    navigation.pop(stackDepth)
  }

  let next = () => {
    navigation.navigate('FlowB1DocB', {stackDepth: stackDepth + 1})
  }

  let getAcc = async () => {
    const getAllAccounts = WebService.getAllAccounts()
    const allAccountData = await getAllAccounts
    dispatch(updateAccountPlus(allAccountData.data))
  }

  let openImageSelect = documentType => {
    const options = {
      title: 'Select Document',
      maxWidth: 2200,
      maxHeight: 2200,
      quality: 1.0,
      storageOptions: {
        skipBackup: true,
        path: 'images',
        cameraRoll: true,
        waitUntilSaved: true,
      },
      //takePhotoButtonTitle: null,
      noData: false,
      mediaType: 'photo',
    }

    setLoading(true)
    setError(false)

    ImagePicker.launchImageLibrary(options, async response => {
      if (response.didCancel) {
        setLoading(false)
        return
      } else if (response.error) {
        setLoading(false)
        return
      } else if (response.customButton) {
        //console.log('User tapped custom button: ', response.customButton)
      } else {
        response = response.assets[0]
        const photo = {uri: response.uri}
        const formdata = new FormData()

        const validImage = validateImage(response.type, response.fileSize)
        if (validImage === 'type') {
          setLoading(false)
          setError('type')
          return
        } else if (validImage === 'size') {
          setLoading(false)
          setError('size')
          return
        }

        let path = response.uri
        if (Platform.OS === 'ios') {
          path = '~' + path.substring(path.indexOf('/Documents'))
        }
        if (!response.fileName) response.fileName = path.split('/').pop()

        formdata.append('file', [
          {
            uri: Platform.OS === 'android' ? photo.uri : photo.uri.replace('file://', ''),
            name: response.fileName || 'testName.jpg',
            type: response.type,
            size: response.fileSize,
          },
        ])
        formdata.append('name', response.fileName)
        formdata.append('key', 'true')

        const accounts = user?.accounts
        let businessProfile
        if (accounts) {
          businessProfile = user?.accounts?.filter(a => a.ref == accountRef)[0]?.businessProfile
        }

        const businessAddressID = businessProfile?.address?.id
        const businessProfileID = businessProfile?.id

        //https://borrower-portal.stage.saltlending.tech/api/v0/documents/0be4cfb6-fa28-44b1-92a6-d065e796233f/address/other_proof_of_address?ref=2

        const docType = documentType == 'address' ? 'other_proof_of_address' : 'verification_of_business'

        const referenceType = documentType == 'address' ? 'address' : 'business_profile'

        const referenceId = documentType == 'address' ? businessAddressID : businessProfileID

        WebService.uploadDocument(referenceId, referenceType, docType, photo.uri.replace('file://', ''), response.fileName)
          .then(res => {
            const parsedData = JSON.parse(res.data)

            docs.push({
              name: response.fileName,
              id: parsedData.id,
              type: documentType == 'address' ? 'other_proof_of_address' : 'verification_of_business',
            })

            setLoading(false)
            //setDocs(docs)
            //dispatch(updateFlowB1({docsA: docs}))
            setRejectedErr(false)
            getAcc()

            //refresh user data - so docs show up on back and forth
            //this.getAccounts()
          })
          .catch(err => {
            console.log('upload doc err', err)
            setLoading(false)
          })
      }
    })
  }

  let validateImage = (type, size) => {
    const fileTypes = ['application/pdf', 'image/jpg', 'image/jpeg', 'image/png', 'image/gif', 'image/tif', 'image/tiff']
    if (!fileTypes.includes(type)) {
      return 'type'
    }
    if (size / 1000000 > 50) {
      return 'size'
    }
    return true
  }

  const addressProofDocs = docs?.filter(a => a.type == 'other_proof_of_address') || []
  const businessDocs = docs?.filter(a => a.type == 'verification_of_business') || []

  const showDocs = addressProofDocs?.map((a, k) => (
    <View style={styles.unit21ShowFilesBox} key={k}>
      <View style={styles.unit21ShowFilesName}>
        <TextReg style={{color: '#000'}}>{a.name}</TextReg>
      </View>
      {!a.verifiedAt && (
        <TouchableOpacity onPress={() => removeDocument(a.id)}>
          <Image
            source={removeFileImg}
            style={{
              height: 30,
              width: 30,
            }}
          />
        </TouchableOpacity>
      )}
    </View>
  ))

  return (
    <View style={localStyles.box}>
      <View>
        <BackgroundHeader
          title={'Documents'}
          close
          closeFn={closeUnit21}
          goBack={() => (stackDepth <= 2 ? closeUnit21() : navigation.goBack())}
        />
        <Unit21Progress complete={7} />
        <View style={{flexDirection: 'row', marginTop: 20}}>
          <TextReg
            style={{
              fontSize: 20,
              marginRight: 6,
            }}>{`Proof of Address -`}</TextReg>
          <TextReg
            style={{
              fontSize: 20,
              color: '#e5705a',
            }}>
            Required
          </TextReg>
        </View>
        <TextReg style={{fontSize: 16, marginTop: 14}}>Provide ONE of the following:</TextReg>
        <TextReg style={{fontSize: 16, marginTop: 6, marginLeft: 10}}>- Latest Bank Statement</TextReg>
        <TextReg style={{fontSize: 16, marginTop: 4, marginLeft: 10}}>- Latest Utility Bill</TextReg>
        <TextReg style={{fontSize: 16, marginTop: 4, marginLeft: 10}}>- Tax Filing</TextReg>
        <TextReg style={{fontSize: 16, marginTop: 4, marginLeft: 10, marginBottom: 5}}>{`- Government Issued Document`}</TextReg>
        {showDocs}
        <Button
          isLoading={loading}
          style={{
            alignSelf: 'stretch',
            backgroundColor: '#28283D',
            borderColor: '#FFF',
            borderWidth: 2,
            marginTop: 20,
            marginBottom: 8,
          }}
          theme={'secondary'}
          onPress={() => openImageSelect('address')}>
          <TextReg style={{color: '#FFF', fontSize: 18}}>UPLOAD DOCUMENT</TextReg>
        </Button>
        {rejectedErr && (
          <View>
            <TextReg style={{color: '#e5705a', fontSize: 18, padding: 10}}>
              {'The previous document was rejected by our team. Please upload new document.'}
            </TextReg>
          </View>
        )}
      </View>
      <Button
        isLoading={false}
        disabled={showDocs?.length < 1}
        style={{
          alignSelf: 'stretch',
          marginTop: 20,
          backgroundColor: '#00FFBD',
          marginBottom: 10,
        }}
        onPress={() => next()}
        theme={'secondary'}>
        <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
      </Button>
    </View>
  )
}

export default FlowB1DocA

const localStyles = StyleSheet.create({
  box: {
    flex: 1,
    padding: 24,
    backgroundColor: '#28283D',
    justifyContent: 'space-between',
  },
})
