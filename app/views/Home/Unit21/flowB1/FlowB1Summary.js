import React, {useEffect, useState} from 'react'
import {StyleSheet, View, Platform, TouchableOpacity, Image, ScrollView} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {TextReg, TextBold, BackgroundHeader, Button} from '../../../../components'
import {increaseUnit21Refresh, updateFlowB1} from '../../../../store/user/user.actions'

import {useDispatch, useSelector} from 'react-redux'
import Unit21Progress from '../Unit21Progress'

let FlowB1Summary = ({navigation, route}) => {
  let dispatch = useDispatch()
  let WebService = useSelector(state => state.auth.WebService || {})
  let stackDepth = route?.params?.stackDepth || 1
  let b1Obj = useSelector(state => state.user.flowB1 || {})
  console.log('b1Obj- summary', b1Obj)

  let docAName = ''
  if (b1Obj?.docsA?.length > 0) {
    docAName = b1Obj?.docsA[0]?.name
  }
  let docBName = ''
  if (b1Obj?.docsB?.length > 0) {
    docBName = b1Obj?.docsB[0]?.name
  }
  let reviewArr = [
    {name: 'Business Name', value: b1Obj?.name || '', nav: 'FlowB1Name'},
    {name: 'DBA (if any)', value: b1Obj?.dba || '', nav: 'FlowB1Name'},
    {name: 'Entity Type', value: b1Obj?.entity || '', nav: 'FlowB1Name'},
    {name: 'Date of Formation', value: b1Obj?.formation || '', nav: 'FlowB1Formation'},
    {name: 'Industry Sector', value: b1Obj?.sector || '', nav: 'FlowB1Formation'},
    {name: 'EIN/Tax ID', value: b1Obj?.ein || '', nav: 'FlowB1Formation'},
    {
      name: 'Business Address',
      value:
        (
          <View>
            <TextBold>{b1Obj?.address1}</TextBold>
            <TextBold>{b1Obj?.address2}</TextBold>
            <View style={{flexDirection: 'row'}}>
              <TextBold>{`${b1Obj.city},${b1Obj.regionCode} `}</TextBold>
              <TextBold>{b1Obj?.postal}</TextBold>
            </View>
            <TextBold>{b1Obj?.countryCode}</TextBold>
          </View>
        ) || '',
      nav: 'FlowB1Address',
    },
  ]

  useEffect(() => {
    console.log('FlowB1Summary', navigation, route)
  }, [])

  let closeUnit21 = () => {
    navigation.pop(stackDepth)
  }

  let next = async () => {
    //navigation.navigate('FlowB1Name', {stackDepth: stackDepth + 1})
    await WebService.businessVerification()
    dispatch(increaseUnit21Refresh())
  }

  let showArr = reviewArr?.map(a => {
    return (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottomWidth: 1,
          borderColor: '#888',
          paddingBottom: 10,
          marginBottom: 10,
        }}>
        <View>
          <TextReg>{a.name}</TextReg>
          <TextBold style={{fontSize: 18, marginTop: 5}}>{a.value}</TextBold>
        </View>
        <TextBold onPress={() => navigation.navigate(a.nav, {stackDepth: stackDepth + 1})} style={{fontSize: 19, color: '#00FFBD'}}>
          EDIT
        </TextBold>
      </View>
    )
  })

  return (
    <ScrollView style={styles.box}>
      <View>
        <BackgroundHeader
          title={'Summary'}
          close
          closeFn={closeUnit21}
          goBack={() => (stackDepth <= 2 ? closeUnit21() : navigation.goBack())}
        />
        <Unit21Progress complete={9} />

        <TextReg
          style={{
            fontSize: 20,
            marginRight: 6,
          }}>{`Review your Business Information`}</TextReg>
        <TextReg
          style={{
            fontSize: 16,
            marginTop: 14,
          }}>{`If you need to make any corrections to the information below, click “EDIT” and you will be taken to that page to edit your information.`}</TextReg>
        <View style={{marginTop: 20, alignSelf: 'stretch'}}>{showArr}</View>
      </View>
      <Button
        isLoading={false}
        disabled={false}
        style={{
          alignSelf: 'stretch',
          marginTop: 20,
          backgroundColor: '#00FFBD',
          marginBottom: 60,
        }}
        onPress={() => next()}
        theme={'secondary'}>
        <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
      </Button>
    </ScrollView>
  )
}

export default FlowB1Summary

const styles = StyleSheet.create({
  box: {
    flex: 1,
    padding: 24,
    backgroundColor: '#28283D',
  },
  qText: {
    fontSize: 20,
  },
})
