import React, {useEffect, useState, useRef} from 'react'
import {
  StyleSheet,
  View,
  Platform,
  TouchableOpacity,
  Image,
  TextInput,
  Keyboard,
} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {TextR<PERSON>, TextBold, BackgroundHeader, Button, LocationSelect} from '../../../../components'
import {useDispatch, useSelector} from 'react-redux'
import {updateFlowB1} from '../../../../store/user/user.actions'
import Unit21Progress from '../Unit21Progress'
import iso3166 from 'iso-3166-2'
import countryCodes from '../../../../util/countryCodes'

import styles from '../../styles'

let FlowB1Address = ({navigation, route}) => {
  let WebService = useSelector(state => state.auth.WebService || {})

  let dispatch = useDispatch()
  let b1Obj = useSelector(state => state.user.flowB1 || {})

  let stackDepth = route?.params?.stackDepth || 1
  let [country, setCountry] = useState(null)
  let [countryCode, setCountryCode] = useState(b1Obj?.countryCode || null)
  let [region, setRegion] = useState(null)
  let [regionCode, setRegionCode] = useState(b1Obj?.regionCode || null)

  let [pickCodes, setPickCodes] = useState([])
  let [pickRegion, setPickRegion] = useState([])
  let loanData = useSelector(state => state.user.loanData || {})

  let [address1, setAddress1] = useState(b1Obj?.address1 || '')
  let [address2, setAddress2] = useState(b1Obj?.address2 || '')
  let [postal, setPostal] = useState(b1Obj?.postal || '')
  let [city, setCity] = useState(b1Obj?.city || '')
  let [space, setSpace] = useState(false)
  const scrollViewRef = useRef(null)

  let inputs = []

  useEffect(() => {
    locationSetup()
  }, [])

  let locationSetup = () => {
    let pickableCountryCodes = countryCodes().map(a => a.name)
    pickableCountryCodes = ['United States'].concat(pickableCountryCodes)
    setPickCodes(pickableCountryCodes)

    //loan jurisdiction country & province
    let countryCode = b1Obj?.countryCode || loanData?.jurisdiction?.countryCode || ''
    setCountryCode(countryCode)
    let regionCode = b1Obj?.regionCode || loanData?.jurisdiction?.province || ''
    setRegionCode(regionCode)
    setRegion(regionCode)

    let country = countryCodes().filter(a => a.code == countryCode)[0].name || ''
    setCountry(country)

    if (countryCode != '') {
      const isoCountry = iso3166.country(countryCode)
      let pickableCountrySubs = []
      const subArr = Object.values(isoCountry.sub)
      pickableCountrySubs = subArr.map(a => a.name)
      pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
        if (a < b) return -1
        if (b < a) return 1
        return 0
      })
      setPickRegion(pickableCountrySubs)
    }
  }

  let closeUnit21 = () => {
    navigation.pop(stackDepth)
  }

  let next = async () => {
    dispatch(updateFlowB1({address1, address2, postal, city, countryCode, regionCode}))

    const businessAddressID = b1Obj?.businessAddressID
    console.log('businessAddressID', businessAddressID)
    const payload = {
      city: city,
      countryCode: countryCode,
      document: [],
      postalCode: postal,
      province: regionCode,
      street1: address1,
      street2: address2,
    }
    console.log('payload', payload)
    WebService.patchAddress(businessAddressID, payload)

    navigation.navigate('FlowB1DocA', {stackDepth: stackDepth + 1})
  }

  let onSelect = selectedCountry => {
    const countryCode = countryCodes().filter(a => a.name == selectedCountry)[0].code
    const isoCountry = iso3166.country(countryCode)
    let pickableCountrySubs = []
    const subArr = Object.values(isoCountry.sub)
    pickableCountrySubs = subArr.map(a => a.name)
    pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
      if (a < b) return -1
      if (b < a) return 1
      return 0
    })
    setCountryCode(countryCode)
    setCountry(selectedCountry)
    setPickRegion(pickableCountrySubs)
    setRegion(null)
    setRegionCode(null)
  }

  let onRegionSelect = selectedProvince => {
    const countryCode = countryCodes().filter(a => a.name == country)[0].code
    const subData = iso3166.subdivision(countryCode, selectedProvince)
    const regionCode = subData?.regionCode

    setRegion(selectedProvince)
    setRegionCode(regionCode)
  }

  let goBack = () => {
    Keyboard.dismiss()
    stackDepth <= 2 ? closeUnit21() : navigation.goBack()
  }

  // Function to handle keyboard appearance
  const handleFocus = () => {
    // Only set space to true on Android devices
    if (Platform.OS === 'android') {
      setSpace(true)

      // Scroll to the bottom of the page
      setTimeout(() => {
        if (scrollViewRef.current) {
          scrollViewRef.current.scrollToEnd({animated: true})
        }
      }, 100)
    }
  }

  const handleBlur = () => {
    if (Platform.OS === 'android') {
      setSpace(false)
    }
  }

  return (
    <View style={stylesLocal.box}>
      <KeyboardAwareScrollView
        ref={scrollViewRef}
        style={{flex: 1, alignSelf: 'stretch'}}
        enableResetScrollToCoords={false}>
        <BackgroundHeader
          title={'Entity Address'}
          close
          closeFn={closeUnit21}
          goBack={() => goBack()}
        />
        <Unit21Progress complete={6} />
        <TextReg style={{marginBottom: 4, marginTop: 5}}>Address Line 1</TextReg>
        <TextInput
          style={{...styles.unit21InfoInput, marginBottom: 10}}
          onChangeText={text => setAddress1(text)}
          value={address1}
          underlineColorAndroid="transparent"
          blurOnSubmit
          returnKeyType={'next'}
          placeholder={''}
          placeholderTextColor={'#999'}
          onSubmitEditing={() => inputs.address2.focus()}
          keyboardAppearance="dark"
        />
        <TextReg style={{marginBottom: 4}}>Address Line 2</TextReg>
        <TextInput
          style={{...styles.unit21InfoInput, marginBottom: 12}}
          onChangeText={text => setAddress2(text)}
          ref={input => (inputs.address2 = input)}
          value={address2}
          underlineColorAndroid="transparent"
          blurOnSubmit
          returnKeyType={'next'}
          placeholder={''}
          placeholderTextColor={'#999'}
          onSubmitEditing={() => Keyboard.dismiss()}
          keyboardAppearance="dark"
        />
        <TextReg style={{marginBottom: 5}}>Country</TextReg>
        <LocationSelect
          options={pickCodes}
          onSelect={onSelect}
          placeholder={country ? country : 'Country'}
        />
        <TextReg style={{marginBottom: 5, marginTop: -4}}>State / Province</TextReg>
        <LocationSelect
          options={pickRegion}
          onSelect={onRegionSelect}
          placeholder={region ? region : 'State / Province'}
        />
        <View style={{flexDirection: 'row', marginTop: -4}}>
          <View style={{flex: 1, flexDirection: 'column', marginRight: 10}}>
            <TextReg style={{marginBottom: 4}}>City</TextReg>
            <TextInput
              style={styles.unit21InfoInputHalf}
              onChangeText={text => setCity(text)}
              ref={input => (inputs.city = input)}
              value={city}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              placeholder={'City'}
              placeholderTextColor={'#999'}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onSubmitEditing={() => inputs.postal.focus()}
              keyboardAppearance="dark"
            />
          </View>
          <View style={{flex: 1, flexDirection: 'column'}}>
            <TextReg style={{marginBottom: 4}}>Zip Code</TextReg>
            <TextInput
              style={styles.unit21InfoInputHalf}
              onChangeText={text => setPostal(text)}
              ref={input => (inputs.postal = input)}
              value={postal}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              placeholder={'Zip Code'}
              placeholderTextColor={'#999'}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onSubmitEditing={() => Keyboard.dismiss()}
              keyboardAppearance="dark"
            />
          </View>
        </View>
        {space && <View style={{height: 300}} />}
      </KeyboardAwareScrollView>
      <Button
        isLoading={false}
        disabled={!country || !region || !address1 || !city || !postal}
        style={{
          alignSelf: 'stretch',
          backgroundColor: '#00FFBD',
          marginBottom: 10,
        }}
        onPress={() => next()}
        theme={'secondary'}>
        <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
      </Button>
    </View>
  )
}

export default FlowB1Address

const stylesLocal = StyleSheet.create({
  box: {
    flex: 1,
    padding: 24,
    backgroundColor: '#28283D',
    justifyContent: 'space-between',
  },
})
