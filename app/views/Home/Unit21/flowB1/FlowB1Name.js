import React, {useEffect, useState} from 'react'
import {StyleSheet, View, Platform, TouchableOpacity, Image, TextInput, Keyboard} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {TextReg, TextBold, BackgroundHeader, Button, LocationSelect} from '../../../../components'
import {useDispatch, useSelector} from 'react-redux'
import {updateFlowB1} from '../../../../store/user/user.actions'
import Unit21Progress from '../Unit21Progress'

import styles from '../../styles'

let FlowB1Name = ({navigation, route}) => {
  let WebService = useSelector(state => state.auth.WebService || {})

  let dispatch = useDispatch()
  let b1Obj = useSelector(state => state.user.flowB1 || {})
  let stackDepth = route?.params?.stackDepth || 1
  let [name, setName] = useState(b1Obj?.name || '')
  let [dba, setDba] = useState(b1Obj?.dba || '')
  let [entity, setEntity] = useState(b1Obj?.entity || '')
  let inputs = []
  let entityTypeList = ['LLC', 'Corporation', 'Trust', 'Sole Proprietor', 'Non-Profit']

  let closeUnit21 = () => {
    navigation.pop(stackDepth)
  }

  let next = async () => {
    dispatch(updateFlowB1({name, dba, entity}))
    let payload = {
      entityType: entity,
      legalName: name,
      listDBA: dba,
    }
    await WebService.patchBusiness(payload)
    navigation.navigate('FlowB1Formation', {stackDepth: stackDepth + 1})
  }

  let goBack = () => {
    Keyboard.dismiss()
    stackDepth <= 2 ? closeUnit21() : navigation.goBack()
  }

  return (
    <View style={stylesLocal.box}>
      <KeyboardAwareScrollView style={{flex: 1, alignSelf: 'stretch'}} enableResetScrollToCoords={true}>
        <BackgroundHeader title={'Entity Info'} close closeFn={closeUnit21} goBack={() => goBack()} />
        <Unit21Progress complete={3} />
        <TextReg style={{marginBottom: 5, marginTop: 10}}>Business Name</TextReg>
        <TextInput
          style={styles.unit21InfoInput}
          onChangeText={text => setName(text)}
          value={name}
          underlineColorAndroid="transparent"
          blurOnSubmit
          returnKeyType={'next'}
          placeholder={'Business Name'}
          placeholderTextColor={'#999'}
          onSubmitEditing={() => inputs.dba.focus()}
          keyboardAppearance="dark"
        />
        <TextReg style={{marginBottom: 5}}>DBA</TextReg>
        <TextInput
          style={styles.unit21InfoInput}
          onChangeText={text => setDba(text)}
          ref={input => (inputs.dba = input)}
          value={dba}
          underlineColorAndroid="transparent"
          blurOnSubmit
          returnKeyType={'next'}
          placeholder={'DBA (if any)'}
          placeholderTextColor={'#999'}
          keyboardAppearance="dark"
          onSubmitEditing={() => Keyboard.dismiss()}
        />
        <TextReg style={{marginBottom: 5}}>Entity Type</TextReg>
        <LocationSelect
          options={entityTypeList}
          onSelect={pick => setEntity(pick)}
          placeholder={entity ? entity : 'Entity Type'}
          searchable={'no'}
          placeholderStyle={{
            color: entity ? '#fff' : '#999',
          }}
        />
      </KeyboardAwareScrollView>
      <Button
        isLoading={false}
        disabled={!name || !entity}
        style={{
          alignSelf: 'stretch',
          marginTop: 20,
          backgroundColor: '#00FFBD',
          marginBottom: 10,
        }}
        onPress={() => next()}
        theme={'secondary'}>
        <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
      </Button>
    </View>
  )
}

export default FlowB1Name

const stylesLocal = StyleSheet.create({
  box: {
    flex: 1,
    padding: 24,
    backgroundColor: '#28283D',
    justifyContent: 'space-between',
  },
})
