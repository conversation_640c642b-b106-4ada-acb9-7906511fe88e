import React, {useEffect, useState} from 'react'
import {StyleSheet, View, Platform, TouchableOpacity, Image} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {TextReg, TextBold, BackgroundHeader, Button} from '../../../../components'
import {useDispatch, useSelector} from 'react-redux'
import {updateFlowB1} from '../../../../store/user/user.actions'
import Unit21Progress from '../Unit21Progress'

let FlowB1Entity2 = ({navigation, route}) => {
  let dispatch = useDispatch()
  let b1Obj = useSelector(state => state.user.flowB1 || {})
  let WebService = useSelector(state => state.auth.WebService || {})

  let stackDepth = route?.params?.stackDepth || 1
  let [answer, setAnswer] = useState(b1Obj?.entity2 || false)

  useEffect(() => {
    //console.log('FlowB1Entity', navigation, route)
  }, [])

  let closeUnit21 = () => {
    navigation.pop(stackDepth)
  }

  let next = async () => {
    dispatch(updateFlowB1({entity2: answer}))
    let payload = {
      isEntityAssociation: answer,
    }
    await WebService.patchBusiness(payload)
    navigation.navigate('FlowB1Name', {stackDepth: stackDepth + 1})
  }

  return (
    <View style={styles.box}>
      <View>
        <BackgroundHeader
          title={'Entity Question'}
          close
          closeFn={closeUnit21}
          goBack={() => (stackDepth <= 2 ? closeUnit21() : navigation.goBack())}
        />
        <Unit21Progress complete={2} />
        <TextReg
          style={{
            fontSize: 20,
            marginTop: 20,
          }}>{`Is the entity associated with a government, political entity, military, or religious group?`}</TextReg>
        <View style={styles.slideBox}>
          <TouchableOpacity
            onPress={() => setAnswer(false)}
            style={{
              height: 44,
              borderRadius: 23,
              width: 130,
              backgroundColor: answer ? '#3D3D50' : '#00FFBD',
              alignItems: 'center',
              justifyContent: 'center',
              marginLeft: 4,
            }}>
            <TextReg style={{color: answer ? '#fff' : '#000'}}>NO</TextReg>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => setAnswer(true)}
            style={{
              height: 44,
              borderRadius: 23,
              width: 130,
              backgroundColor: answer ? '#00FFBD' : '#3D3D50',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 4,
            }}>
            <TextReg style={{color: answer ? '#000' : '#fff'}}>YES</TextReg>
          </TouchableOpacity>
        </View>
      </View>
      <Button
        isLoading={false}
        disabled={false}
        style={{
          alignSelf: 'stretch',
          marginTop: 20,
          backgroundColor: '#00FFBD',
          marginBottom: 10,
        }}
        onPress={() => next()}
        theme={'secondary'}>
        <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
      </Button>
    </View>
  )
}

export default FlowB1Entity2

const styles = StyleSheet.create({
  box: {
    flex: 1,
    padding: 24,
    backgroundColor: '#28283D',
    justifyContent: 'space-between',
  },
  qText: {
    fontSize: 20,
  },
  slideBox: {
    marginTop: 30,
    height: 50,
    alignSelf: 'stretch',
    borderRadius: 25,
    backgroundColor: '#3D3D50',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 14,
    overflow: 'hidden',
  },
})
