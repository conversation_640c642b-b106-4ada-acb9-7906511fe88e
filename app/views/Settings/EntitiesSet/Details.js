import React from 'react'
import {Modal, View, Image, TouchableOpacity, ScrollView, SafeAreaView} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import {useNavigation} from '@react-navigation/native'

import {TextBold, TextReg} from '../../../components'
import paperImg from '../../../imgs/graphics/paper.png'
import warnImg from '../../../imgs/graphics/warn.png'

import styles from '../styles'

let Details = props => {
  let WebService = useSelector(state => state.auth.WebService || {})
  let navigation = useNavigation()
  let a = props?.route?.params

  let showDateF = a.formationDate?.split('T')[0]
  const year = showDateF.split('-')[0]
  const month = showDateF.split('-')[1]
  const day = showDateF.split('-')[2]
  showDateF = `${month}/${day}/${year}`

  let country = a.formationJurisdiction?.split(', ')[0]
  let province = a.formationJurisdiction?.split(', ')[1]
  let address1 = a.address?.street1
  let address2 = a.address?.street2 || ''

  let bothDocs = a.documents?.concat(a.address?.documents)
  let showDocs = bothDocs?.map((b, k) => {
    if (b.rejectedAt) return null
    let pending = true
    if (b.verifiedAt) pending = false
    let showType = 'Address Verification Document'
    if (b.type == 'verification_of_business') {
      showType = 'Verification of Business'
    }

    let barColor = '#00FFBD'
    if (pending) barColor = '#F7D956'
    let img = paperImg
    if (pending) img = warnImg

    return (
      <View style={local.docBox}>
        <View style={local.docCard}>
          <View style={{height: 44, width: 4, backgroundColor: barColor, marginRight: 10}} />
          <Image source={img} style={{height: pending ? 24 : 26, width: pending ? 26 : 19, marginRight: 10}} />
          <TextReg>{`${showType}`}</TextReg>
        </View>
        {pending && (
          <TextReg
            style={{color: '#F7D956', marginTop: 6, fontSize: 12, marginBottom: 10}}>{`Pending verification per SALT's Review`}</TextReg>
        )}
      </View>
    )
  })

  let showSector = a.industry
  if (showSector?.length > 16) {
    showSector = showSector?.substring(0, 16)
    showSector += '...'
  }

  let doc1 = a?.documents?.filter(b => b.verifiedAt)?.length > 0
  let doc2 = a?.address?.documents?.filter(b => b.verifiedAt)?.length > 0

  return (
    <SafeAreaView style={local.box}>
      <View style={{flexDirection: 'row', alignSelf: 'stretch', justifyContent: 'space-between', marginTop: 5, marginBottom: 5}}>
        <TouchableOpacity onPress={() => navigation?.goBack()} style={{height: 40, width: 40, marginLeft: 20}}>
          <Image source={require('../../../imgs/backToSettings.png')} style={{height: 25, width: 20}} />
        </TouchableOpacity>
        <TextReg style={{color: '#FFF', fontSize: 18}}>Entity Details</TextReg>
        <View style={{height: 40, width: 40, marginRight: 20}} />
      </View>
      <ScrollView style={{alignSelf: 'stretch'}}>
        <TextReg style={{marginLeft: 20, fontSize: 22, marginBottom: 10}}>{`${a.legalName}`}</TextReg>
        <View style={{flexDirection: 'row', marginLeft: 20, marginBottom: a.isVerified ? 20 : 4}}>
          {doc1 && doc2 && <Image source={require('../../../imgs/graphics/shield.png')} style={{height: 25, width: 20, marginLeft: 10, marginTop:2}} />}
          {a.isEntityAccredited && <Image source={require('../../../imgs/graphics/ribbon.png')} style={{height: 28, width: 20, marginLeft: 10, marginBottom:6}} />}

        </View>
        <View style={local.card}>
          <View style={{flexDirection: 'column', paddingLeft: 12, paddingRight: 12, marginBottom: 10, alignSelf: 'stretch'}}>
            <TextReg>{`Entity Name`}</TextReg>
            <TextBold style={{fontSize: 20}}>{`${a.legalName}`}</TextBold>

            <View style={local.row}>
              <TextReg style={{fontSize: 16}}>{`DBA (in any)`}</TextReg>
              <TextBold style={{fontSize: 16}}>{`${a.listDBA}`}</TextBold>
            </View>
            <View style={local.row}>
              <TextReg style={{fontSize: 16}}>{`Entity Type`}</TextReg>
              <TextBold style={{fontSize: 16}}>{`${a.entityType}`}</TextBold>
            </View>
            <View style={local.row}>
              <TextReg style={{fontSize: 16}}>{`Date of Formation`}</TextReg>
              <TextBold style={{fontSize: 16}}>{`${showDateF}`}</TextBold>
            </View>
            <View style={local.row}>
              <TextReg style={{fontSize: 16}}>{`Industry Sector`}</TextReg>
              <TextBold style={{fontSize: 16}}>{`${showSector}`}</TextBold>
            </View>
            <View style={local.row}>
              <TextReg style={{fontSize: 16}}>{`EIN/Tax ID`}</TextReg>
              <TextBold style={{fontSize: 16}}>{`${a.taxIdNumber}`}</TextBold>
            </View>
            <View style={local.row}>
              <TextReg style={{fontSize: 16}}>{`Formation Jurisdiction`}</TextReg>
              <TextBold style={{fontSize: 16}}>{`${country}`}</TextBold>
            </View>
            <View style={local.row}>
              <TextReg style={{fontSize: 16}}>{`Formation State/Province`}</TextReg>
              <TextBold style={{fontSize: 16}}>{`${province}`}</TextBold>
            </View>
            <View style={local.rowLast}>
              <TouchableOpacity
                onPress={() => navigation?.navigate('EntityAddressSet', {...a, stackDepth: 1, editAddressFlow: true})}
                style={{flexDirection: 'row'}}>
                <TextReg style={{fontSize: 16}}>{`Address`}</TextReg>
                <Image source={require('../../../imgs/graphics/edit.png')} style={{height: 21, width: 20, marginLeft: 6}} />
              </TouchableOpacity>
              <View style={{flexDirection: 'column'}}>
                <TextBold style={{fontSize: 16, textAlign: 'right'}}>{`${address1}`}</TextBold>
                {address2 && <TextBold style={{fontSize: 16, textAlign: 'right'}}>{`${address2}`}</TextBold>}
                <TextBold
                  style={{
                    fontSize: 16,
                    textAlign: 'right',
                  }}>{`${a.address?.city}, ${a.address?.province}/${a.address?.countryCode} ${a.address?.postalCode}`}</TextBold>
              </View>
            </View>
          </View>
        </View>
        <TouchableOpacity onPress={() => navigation.navigate('EntityDoc1Set', {...a, stackDepth: 1})}>
          <View
            style={{
              alignSelf: 'stretch',
              justifyContent: 'space-between',
              flexDirection: 'row',
              marginLeft: 24,
              marginRight: 24,
              marginTop: 16,
              marginBottom: 10,
            }}>
            <TextReg style={{fontSize: 22}}>{`Document Uploads`}</TextReg>
            <Image source={require('../../../imgs/graphics/greenPlus.png')} style={{height: 20, width: 20, marginTop: 4}} />
          </View>
        </TouchableOpacity>
        {showDocs}

        <TouchableOpacity
          onPress={() => navigation?.navigate('EntityDeleteSet', {id: a.id, name: a.legalName})}
          style={{
            alignSelf: 'stretch',
            flexDirection: 'row',
            marginLeft: 22,
            marginRight: 24,
            marginTop: 20,
            marginBottom: 100,
          }}>
          <Image source={require('../../../imgs/graphics/trash.png')} style={{height: 26, width: 22}} />
          <TextReg style={{fontSize: 18, marginLeft: 8, color: '#E5705A', letterSpacing: 1.8}}>{`DELETE ENTITY`}</TextReg>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  )
}

export default Details

let local = {
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
  card: {
    backgroundColor: '#3D3D50',
    alignSelf: 'stretch',
    borderRadius: 10,
    marginLeft: 20,
    marginRight: 20,
    marginBottom: 10,
    paddingTop: 10,
  },
  docCard: {
    backgroundColor: '#3D3D50',
    alignSelf: 'stretch',
    borderRadius: 4,
    marginRight: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  docBox: {
    alignSelf: 'stretch',
    borderRadius: 4,
    marginLeft: 20,
    marginBottom: 14,
  },
  row: {
    alignSelf: 'stretch',
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingTop: 10,
    paddingBottom: 10,
    borderColor: '#999',
    borderWidth: 0,
    borderBottomWidth: 1,
    fontSize: 16,
  },
  rowLast: {
    alignSelf: 'stretch',
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingTop: 10,
    paddingBottom: 10,
    borderWidth: 0,
    fontSize: 16,
  },
}
