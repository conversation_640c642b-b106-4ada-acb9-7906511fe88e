import React, {useEffect, useState} from 'react'
import {
  StyleSheet,
  View,
  Platform,
  TouchableOpacity,
  Image,
  TextInput,
  Keyboard,
} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {TextReg, TextBold, BackgroundHeader, Button, LocationSelect} from '../../../../components'
import {useDispatch, useSelector} from 'react-redux'
import {useFocusEffect} from '@react-navigation/native'

import {updateFlowB1} from '../../../../store/user/user.actions'
import {formatDateText} from '../../../../util/helpers'
import iso3166 from 'iso-3166-2'
import countryCodes from '../../../../util/countryCodes'

import Progress from './Progress'

import styles from '../styles'

let EntityInfo1 = ({navigation, route}) => {
  let WebService = useSelector(state => state.auth.WebService || {})
  let dispatch = useDispatch()
  let b1Obj = useSelector(state => {})
  let [a, setA] = useState({...route?.params})

  useFocusEffect(
    React.useCallback(() => {
      getA()
    }, []),
  )

  let getA = async () => {
    let res = await WebService.getEntitys()
    let arr = res.data
    let entity = arr?.filter(b => b.id == a.id)[0]
    setA({stackDepth: a.stackDepth, marketFlow: a.marketFlow, ...entity})
    setName(entity?.legalName || '')
    setDba(entity?.listDBA || '')
    setEntity(entity?.entityType || '')
    setSector(entity?.industry || '')
    setEin(entity?.taxIdNumber || '')
    locationSetup(entity)
    dateSetup(entity)
  }

  let [name, setName] = useState(route?.params?.legalName || '')
  let [dba, setDba] = useState(route?.params?.listDBA || '')
  let [entity, setEntity] = useState(route?.params?.entityType || '')
  let [sector, setSector] = useState(route?.params?.industry || '')
  let [ein, setEin] = useState(route?.params?.taxIdNumber || '')
  let [formation, setFormation] = useState('')
  let [dateError, setDateError] = useState(false)

  //jurisdiction
  let [country, setCountry] = useState(null)
  let [countryCode, setCountryCode] = useState(null)
  let [region, setRegion] = useState(null)
  let [regionCode, setRegionCode] = useState(null)
  let [pickCodes, setPickCodes] = useState([])
  let [pickRegion, setPickRegion] = useState([])

  let inputs = []
  let entityTypeList = ['LLC', 'Corporation', 'Trust', 'Sole Proprietor', 'Non-Profit']

  let id = route?.params?.id
  let forInvestment = route?.params?.forInvestment ? true : false
  let stackDepth = route?.params?.stackDepth || 3

  let dateSetup = (entity = a) => {
    if (entity?.formationDate) {
      let showDate = entity?.formationDate?.split('T')[0]
      const year = showDate.split('-')[0]
      const month = showDate.split('-')[1]
      const day = showDate.split('-')[2]
      showDate = `${month}/${day}/${year}`
      if (showDate == '01/01/1970') {
        showDate = ''
      }
      setFormation(showDate)
    }
  }

  let locationSetup = async (entity = a) => {
    let pickableCountryCodes = countryCodes().map(a => a.name)
    pickableCountryCodes = ['United States'].concat(pickableCountryCodes)
    setPickCodes(pickableCountryCodes)

    let country = entity?.formationJurisdiction?.split(', ')[0] || ''
    let region = entity?.formationJurisdiction?.split(', ')[1] || ''

    setCountry(country)
    setRegion(region)
    if (country) {
      await onSelect(country)
    }
    if (region) {
      onRegionSelect(region, entity)
    }
  }

  let onSelect = selectedCountry => {
    return new Promise((resolve, reject) => {
      const countryCode = countryCodes().filter(a => a.name == selectedCountry)[0].code
      const isoCountry = iso3166.country(countryCode)
      let pickableCountrySubs = []
      const subArr = Object.values(isoCountry.sub)
      pickableCountrySubs = subArr.map(a => a.name)
      pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
        if (a < b) return -1
        if (b < a) return 1
        return 0
      })
      setCountryCode(countryCode)
      setCountry(selectedCountry)
      setPickRegion(pickableCountrySubs)
      setRegion(null)
      setRegionCode(null)
      resolve('ok')
    })
  }

  let onRegionSelect = (selectedProvince, entity) => {
    let country1 = country || entity?.formationJurisdiction?.split(', ')[0] || ''

    const countryCode = countryCodes().filter(a => a.name == country1)[0]?.code
    const subData = iso3166.subdivision(countryCode, selectedProvince)
    const regionCode = subData?.regionCode
    let region = subData?.name
    setRegion(region)
    setRegionCode(regionCode)
  }

  let close = () => {
    navigation.pop(stackDepth)
  }

  let next = async () => {
    //dispatch(updateFlowB1({formation, sector, ein}))
    const dateMonth = formation?.split('/')[0]
    const dateDay = formation?.split('/')[1]
    const dateYear = formation?.split('/')[2]
    const dateWithDashes = `${dateYear}-${dateMonth}-${dateDay}T00:00:00.000Z`

    let formationJurisdiction = `${country}, ${region}`

    let payload = {
      formationDate: dateWithDashes,
      industry: sector,
      taxIdNumber: ein,
      legalName: name,
      listDBA: dba,
      entityType: entity,
      formationJurisdiction,
    }

    try {
      let res = await WebService.updateEntity(id, payload)
      navigation.navigate('EntityAddressSet', {
        ...route?.params,
        stackDepth: stackDepth + 1,
        forInvestment,
      })
    } catch (err) {
      console.log('err', err)
    }
    //await WebService.patchBusiness(payload)
  }

  let formatDate = (text = '') => {
    let textBefore = text
    let oldText = formation
    text = formatDateText(text, oldText)
    if (text?.length != 0 && !text) {
      text = oldText
      if (text && text?.length == 9) {
        text = textBefore
      }
    } else {
      //
    }
    setFormation(text)
  }

  let checkDate = () => {
    let newErr = false

    const [m, d, y] = formation.split('/')
    const dateString = new Date(y, m - 1, d)
    let dateNow = new Date()

    let year = formation?.split('/')[2] || false
    if (year && year < 1900) {
      newErr = true
    }

    console.log('d,m,y -set', d, m, y, dateString)

    if (
      formation?.length == 10 &&
      (Number(d) < 1 ||
        Number(m) < 1 ||
        Number(d) > 31 ||
        Number(m) > 12 || //incorrect day / month
        dateString == 'Invalid Date' ||
        dateString?.valueOf() < -2208963600000 || //before 1900
        dateString?.valueOf() > dateNow?.valueOf()) // after now
    ) {
      newErr = true
    }
    console.log('newErr', newErr, Number(d) < 1, Number(m) < 1, Number(d) > 31, Number(m) > 12)

    if (formation?.length > 10) {
      newErr = true
    }
    setDateError(newErr)

    if (formation?.length > 0 && formation?.length < 10) {
      setDateError(true)
    }
  }

  let goBack = () => {
    Keyboard.dismiss()
    navigation.goBack()
  }

  return (
    <View style={stylesLocal.box}>
      <KeyboardAwareScrollView
        style={{flex: 1, alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24}}
        enableResetScrollToCoords={true}>
        <BackgroundHeader
          title={'Create New Entity'}
          close
          closeFn={() => close()}
          goBack={() => goBack()}
        />
        <Progress complete={4} />
        <TextReg style={{fontSize: 24, marginBottom: 10}}>{`Entity Information`}</TextReg>
        <TextReg style={{marginBottom: 5, marginTop: 10}}>Business Name</TextReg>
        <TextInput
          style={styles.unit21InfoInput}
          onChangeText={text => setName(text)}
          value={name}
          underlineColorAndroid="transparent"
          blurOnSubmit
          returnKeyType={'next'}
          placeholder={'Business Name'}
          placeholderTextColor={'#999'}
          onSubmitEditing={() => inputs.dba.focus()}
          keyboardAppearance="dark"
        />
        <TextReg style={{marginBottom: 5}}>DBA</TextReg>
        <TextInput
          style={styles.unit21InfoInput}
          onChangeText={text => setDba(text)}
          ref={input => (inputs.dba = input)}
          value={dba}
          underlineColorAndroid="transparent"
          blurOnSubmit
          returnKeyType={'next'}
          placeholder={'DBA (if any)'}
          placeholderTextColor={'#999'}
          keyboardAppearance="dark"
          onSubmitEditing={() => Keyboard.dismiss()}
        />
        <TextReg style={{marginBottom: 5}}>Entity Type</TextReg>
        <LocationSelect
          options={entityTypeList}
          onSelect={pick => setEntity(pick)}
          placeholder={entity ? entity : 'Entity Type'}
          searchable={'no'}
          placeholderStyle={{
            color: entity ? '#fff' : '#999',
          }}
        />
        <TextReg style={{marginBottom: 5, marginTop: 10, color: dateError ? '#E6705B' : '#FFF'}}>
          Date of Formation
        </TextReg>
        <TextInput
          style={dateError ? styles.unit21InfoInputDateError : styles.unit21InfoInput}
          onChangeText={text => formatDate(text)}
          value={formation}
          underlineColorAndroid="transparent"
          blurOnSubmit
          returnKeyType={'next'}
          placeholder={'MM/DD/YYYY'}
          placeholderTextColor={'#999'}
          onSubmitEditing={() => inputs.sector.focus()}
          keyboardAppearance="dark"
          keyboardType={'numeric'}
          onBlur={() => checkDate()}
        />
        <TextReg style={{marginBottom: 5}}>Industry Sector</TextReg>
        <TextInput
          style={styles.unit21InfoInput}
          onChangeText={text => setSector(text)}
          ref={input => (inputs.sector = input)}
          value={sector}
          underlineColorAndroid="transparent"
          blurOnSubmit
          returnKeyType={'next'}
          placeholder={'Industry Sector'}
          placeholderTextColor={'#999'}
          keyboardAppearance="dark"
          onSubmitEditing={() => inputs.sector.focus()}
        />
        <TextReg style={{marginBottom: 5}}>EIN/Tax ID</TextReg>
        <TextInput
          style={styles.unit21InfoInput}
          onChangeText={text => setEin(text)}
          ref={input => (inputs.ein = input)}
          value={ein}
          underlineColorAndroid="transparent"
          blurOnSubmit
          returnKeyType={'next'}
          placeholder={'EIN / Tax ID'}
          placeholderTextColor={'#999'}
          keyboardAppearance="dark"
          onSubmitEditing={() => Keyboard.dismiss()}
        />

        <TextReg style={{marginBottom: 5, marginTop: 10}}>
          Jurisdiction of Formation - Country
        </TextReg>
        <LocationSelect
          options={pickCodes}
          onSelect={onSelect}
          placeholder={country ? country : 'Country'}
        />
        <TextReg style={{marginBottom: 5, marginTop: 10}}>
          Jurisdiction of Formation - Province
        </TextReg>
        <LocationSelect
          options={pickRegion}
          onSelect={onRegionSelect}
          placeholder={region ? region : 'State / Province'}
        />

        <Button
          isLoading={false}
          disabled={
            !name || !sector || !ein || !country || !region || dateError || formation?.length < 10
          }
          style={{
            alignSelf: 'stretch',
            marginTop: 10,
            backgroundColor: '#00FFBD',
            marginBottom: 10,
          }}
          onPress={() => next()}
          theme={'secondary'}>
          <TextReg style={{color: '#000', fontSize: 18}}>CONTINUE</TextReg>
        </Button>
        <Button
          isLoading={false}
          style={{
            alignSelf: 'stretch',
            marginTop: 4,
            borderColor: '#00FFBD',
            borderWidth: 1,
            marginBottom: 30,
            backgroundColor: '#28283D',
          }}
          onPress={() => goBack()}>
          <TextReg style={{color: '#00FFBD', fontSize: 18}}>BACK</TextReg>
        </Button>
      </KeyboardAwareScrollView>
    </View>
  )
}

export default EntityInfo1

const stylesLocal = StyleSheet.create({
  box: {
    flex: 1,
    paddingTop: 24,
    backgroundColor: '#28283D',
    justifyContent: 'space-between',
  },
})
