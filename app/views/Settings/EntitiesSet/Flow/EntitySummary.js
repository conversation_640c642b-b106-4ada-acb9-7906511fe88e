import React, {useState} from 'react'
import {StyleSheet, View, TouchableOpacity, Image} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {TextReg, TextBold, BackgroundHeader, Button} from '../../../../components'
import {useDispatch, useSelector} from 'react-redux'
import {useFocusEffect} from '@react-navigation/native'
import Progress from './Progress'

let EntitySummary = ({navigation, route}) => {
  let dispatch = useDispatch()
  let WebService = useSelector(state => state.auth.WebService || {})
  let [a, setA] = useState({...route?.params})

  let id = route?.params?.id
  let forInvestment = route?.params?.forInvestment ? true : false
  let forEntity = route?.params?.forEntity ? true : false
  let stackDepth = route?.params?.stackDepth || 7

  let close = () => {
    navigation.pop(stackDepth)
  }

  useFocusEffect(
    React.useCallback(() => {
      getA()
    }, []),
  )

  let getA = async () => {
    let res = await WebService.getEntitys()
    let arr = res.data
    let entity = arr?.filter(b => b.id == a.id)[0]
    setA({...a, ...entity})
  }

  let next = async () => {
    if (a.marketFlow) {
      navigation.popToTop()
      if (forInvestment) {
        navigation.navigate('ManualCompliance', {forEntity})
      } else navigation.navigate('EntitySelectSet', {marketFlow: true, forInvestment})
      return
    }
    navigation.popToTop()
    navigation.navigate('EntitiesSet')
  }
  let edit = num => {
    if (num == 1 || num == 2 || num == 3 || num == 4 || num == 5 || num == 6) {
      navigation.navigate('EntityInfo1Set', {...a, stackDepth: stackDepth + 1})
    }
    if (num == 7) {
      navigation.navigate('EntityAddressSet', {...a, stackDepth: stackDepth + 1})
    }
  }

  let showDateF = a.formationDate?.split('T')[0]
  const year = showDateF.split('-')[0]
  const month = showDateF.split('-')[1]
  const day = showDateF.split('-')[2]
  showDateF = `${month}/${day}/${year}`

  let country = a.formationJurisdiction?.split(', ')[0]
  let province = a.formationJurisdiction?.split(', ')[1]
  let address1 = a.address?.street1
  let address2 = a.address?.street2 || ''

  let showSector = a.industry
  if (showSector?.length > 20) {
    showSector = showSector?.substring(0, 20)
  }

  return (
    <View style={styles.box}>
      <KeyboardAwareScrollView
        style={{flex: 1, alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24}}
        enableResetScrollToCoords={true}>
        <View>
          <BackgroundHeader
            title={'Summary'}
            close
            closeFn={() => close()}
            goBack={() => navigation.goBack()}
          />
          <Progress complete={8} />
          <TextReg style={{fontSize: 24}}>{`Review your Business Information`}</TextReg>
          <TextReg
            style={{
              fontSize: 16,
              marginTop: 10,
              marginBottom: 10,
            }}>{`If you need to make any corrections to the information below, click "EDIT" and you will be taken to that page to edit your information`}</TextReg>
          <View style={styles.row}>
            <View style={{flexDirection: 'column'}}>
              <TextReg style={{fontSize: 16, marginBottom: 4}}>{`Business Name`}</TextReg>
              <TextBold style={{fontSize: 16}}>{`${a.legalName}`}</TextBold>
            </View>
            <TouchableOpacity
              onPress={() => {
                edit(1)
              }}>
              <TextBold style={{color: '#00FFBD', fontSize: 18}}>{`EDIT`}</TextBold>
            </TouchableOpacity>
          </View>
          <View style={styles.row}>
            <View style={{flexDirection: 'column'}}>
              <TextReg style={{fontSize: 16, marginBottom: 4}}>{`DBA (if any)`}</TextReg>
              <TextBold style={{fontSize: 16}}>{`${a.listDBA}`}</TextBold>
            </View>
            <TouchableOpacity
              onPress={() => {
                edit(2)
              }}>
              <TextBold style={{color: '#00FFBD', fontSize: 18}}>{`EDIT`}</TextBold>
            </TouchableOpacity>
          </View>
          <View style={styles.row}>
            <View style={{flexDirection: 'column'}}>
              <TextReg style={{fontSize: 16, marginBottom: 4}}>{`Entity Type`}</TextReg>
              <TextBold style={{fontSize: 16}}>{`${a.entityType}`}</TextBold>
            </View>
            <TouchableOpacity
              onPress={() => {
                edit(3)
              }}>
              <TextBold style={{color: '#00FFBD', fontSize: 18}}>{`EDIT`}</TextBold>
            </TouchableOpacity>
          </View>
          <View style={styles.row}>
            <View style={{flexDirection: 'column'}}>
              <TextReg style={{fontSize: 16, marginBottom: 4}}>{`Date of Formation`}</TextReg>
              <TextBold style={{fontSize: 16}}>{`${showDateF}`}</TextBold>
            </View>
            <TouchableOpacity
              onPress={() => {
                edit(4)
              }}>
              <TextBold style={{color: '#00FFBD', fontSize: 18}}>{`EDIT`}</TextBold>
            </TouchableOpacity>
          </View>
          <View style={styles.row}>
            <View style={{flexDirection: 'column'}}>
              <TextReg style={{fontSize: 16, marginBottom: 4}}>{`Industry Sector`}</TextReg>
              <TextBold style={{fontSize: 16}}>{`${a.industry}`}</TextBold>
            </View>
            <TouchableOpacity
              onPress={() => {
                edit(5)
              }}>
              <TextBold style={{color: '#00FFBD', fontSize: 18}}>{`EDIT`}</TextBold>
            </TouchableOpacity>
          </View>
          <View style={styles.row}>
            <View style={{flexDirection: 'column'}}>
              <TextReg style={{fontSize: 16, marginBottom: 4}}>{`EIN/TaxID`}</TextReg>
              <TextBold style={{fontSize: 16}}>{`${a.taxIdNumber}`}</TextBold>
            </View>
            <TouchableOpacity
              onPress={() => {
                edit(6)
              }}>
              <TextBold style={{color: '#00FFBD', fontSize: 18}}>{`EDIT`}</TextBold>
            </TouchableOpacity>
          </View>
          <View style={styles.row}>
            <View style={{flexDirection: 'column'}}>
              <TextReg style={{fontSize: 16, marginBottom: 4}}>{`Business Address`}</TextReg>
              <TextBold style={{fontSize: 16}}>{`${address1}`}</TextBold>
              {address2 && <TextBold style={{fontSize: 16}}>{`${address2}`}</TextBold>}
              <TextBold style={{fontSize: 16}}>{`${a.address?.city}, ${province}`}</TextBold>
              <TextBold style={{fontSize: 16}}>{`${country} ${a.address?.postalCode}`}</TextBold>
            </View>
            <TouchableOpacity
              onPress={() => {
                edit(7)
              }}>
              <TextBold style={{color: '#00FFBD', fontSize: 18}}>{`EDIT`}</TextBold>
            </TouchableOpacity>
          </View>
        </View>
        <Button
          isLoading={false}
          disabled={false}
          style={{
            alignSelf: 'stretch',
            marginTop: 20,
            backgroundColor: '#00FFBD',
            marginBottom: 50,
          }}
          onPress={() => next()}
          theme={'secondary'}>
          <TextReg style={{color: '#000', fontSize: 18}}>NEXT</TextReg>
        </Button>
      </KeyboardAwareScrollView>
    </View>
  )
}

export default EntitySummary

const styles = StyleSheet.create({
  box: {
    flex: 1,
    paddingTop: 24,
    backgroundColor: '#28283D',
    justifyContent: 'space-between',
  },
  qText: {
    fontSize: 20,
  },
  slideBox: {
    marginTop: 30,
    height: 50,
    alignSelf: 'stretch',
    borderRadius: 25,
    backgroundColor: '#3D3D50',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 14,
    overflow: 'hidden',
  },
  row: {
    alignSelf: 'stretch',
    alignItems: 'center',
    borderWidth: 0,
    borderColor: '#888',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 10,
    paddingBottom: 10,
  },
})
