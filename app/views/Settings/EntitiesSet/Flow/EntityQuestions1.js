import React, {useEffect, useState} from 'react'
import {StyleSheet, View, Platform, TouchableOpacity, Image, ScrollView} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {TextReg, TextBold, BackgroundHeader, Button} from '../../../../components'
import {useDispatch, useSelector} from 'react-redux'
import Progress from './Progress'
import styles from '../styles'
import agreedYes from '../../../../imgs/agreedYes.png'

let EntityQuestions1 = ({navigation, route}) => {
  let dispatch = useDispatch()
  let b1Obj = useSelector(state => {})
  let WebService = useSelector(state => state.auth.WebService || {})
  let [pick, setPick] = useState(b1Obj?.entity1 || null)

  let id = route?.params?.id
  let forInvestment = route?.params?.forInvestment ? true : false
  let stackDepth = route?.params?.stackDepth || 1

  useEffect(() => {
    loadPick()
  }, [route?.params])

  let loadPick = () => {
    if (route?.params?.isEntityCurrencyExchange) setPick(1)
    if (route?.params?.isEntityMoneyLending) setPick(2)
    if (route?.params?.isEntityPreciousMetals) setPick(3)
    if (route?.params?.isEntityArmsAmmunition) setPick(4)
    if (route?.params?.isEntityNarcotics) setPick(5)
    if (route?.params?.isEntityMarijuana) setPick(6)
    if (route?.params?.isEntityAdultEntertainment) setPick(7)
    if (route?.params?.isEntityCasinos) setPick(8)
    if (route?.params?.isEntityOnlineGambling) setPick(9)
    if (route?.params?.isEntityForeignGovernments) setPick(10)
    if (route?.params?.isEntityNotForProfit) setPick(11)
    if (route?.params?.isEntityMoneyService) setPick(12)

    //if none
    if (
      route?.params?.isEntityCurrencyExchange == false &&
      route?.params?.isEntityMoneyLending == false &&
      route?.params?.isEntityPreciousMetals == false &&
      route?.params?.isEntityArmsAmmunition == false &&
      route?.params?.isEntityNarcotics == false &&
      route?.params?.isEntityMarijuana == false &&
      route?.params?.isEntityAdultEntertainment == false &&
      route?.params?.isEntityCasinos == false &&
      route?.params?.isEntityOnlineGambling == false &&
      route?.params?.isEntityForeignGovernments == false &&
      route?.params?.isEntityNotForProfit == false &&
      route?.params?.isEntityMoneyService == false
    ) {
      setPick(13)
    }
  }

  let close = () => {
    navigation.pop(stackDepth)
  }

  let next = async () => {
    let nameArr = [
      'isEntityCurrencyExchange',
      'isEntityMoneyLending',
      'isEntityPreciousMetals',
      'isEntityArmsAmmunition',
      'isEntityNarcotics',
      'isEntityMarijuana',
      'isEntityAdultEntertainment',
      'isEntityCasinos',
      'isEntityOnlineGambling',
      'isEntityForeignGovernments',
      'isEntityNotForProfit',
      'isEntityMoneyService',
    ]
    let chosenBusiness = nameArr[pick - 1]
    let payload = {
      isEntityCurrencyExchange: false,
      isEntityMoneyLending: false,
      isEntityPreciousMetals: false,
      isEntityArmsAmmunition: false,
      isEntityNarcotics: false,
      isEntityMarijuana: false,
      isEntityAdultEntertainment: false,
      isEntityCasinos: false,
      isEntityOnlineGambling: false,
      isEntityForeignGovernments: false,
      isEntityNotForProfit: false,
      isEntityMoneyService: false,
    }
    /*
    let payload = {
      isEntityInvolved: chosenBusiness,
    }
    */
    if (pick != 13) {
      payload[chosenBusiness] = true
    }

    try {
      let res = await WebService.updateEntity(id, payload)
      navigation.navigate('EntityQuestions2Set', {...route?.params, stackDepth: stackDepth + 1, forInvestment})
    } catch (err) {
      console.log('err', err)
    }
  }

  let toggle = num => {
    if (pick != num) {
      setPick(num)
    } else {
      setPick(null)
    }
  }
  let arr = [
    {num: 1, name: 'Currency Exchange/Money Changer Services'},
    {num: 2, name: 'Money Lending/Pawning'},
    {num: 3, name: 'Precious Metals, Stones, or Jewelry'},
    {num: 4, name: 'Arms & Ammunition'},
    {num: 5, name: 'Narcotics/Pharmaceuticals'},
    {num: 6, name: 'Marijuana-Related Business'},
    {num: 7, name: 'Adult Entertainment'},
    {num: 8, name: 'Casinos'},
    {num: 9, name: 'Online Gambling/Gaming'},
    {num: 10, name: 'Foreign Governments & Officials'},
    {num: 11, name: 'Not-for-Profits/Charitable Organizations'},
    {num: 12, name: 'Money Service Businesses'},
    {num: 13, name: 'None of the above'},
  ]

  let showOptions = arr?.map((a, k) => {
    return (
      <TouchableOpacity key={k} onPress={() => toggle(a.num)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
        <View style={styles.toggleAcknowledgeView}>
          <Image
            source={agreedYes}
            style={[
              styles.unit21ToggleCheckImg,
              {
                opacity: pick == a.num ? 1 : 0,
              },
            ]}
          />
        </View>
        <TextReg style={styles.unit21AcknowledgeTitle}>{a.name}</TextReg>
      </TouchableOpacity>
    )
  })

  let goBack = () => {
    console.log('ehh')
    navigation.goBack()
    //navigation.navigate('EntitySelectSet')
  }
  console.log('2')

  return (
    <ScrollView style={localStyles.box}>
      <View>
        <BackgroundHeader title={'Create New Entity'} close closeFn={() => close()} goBack={() => goBack()} />
        <Progress complete={1} />
        <TextReg style={{fontSize: 24, marginBottom: 16}}>{`Entity Questions`}</TextReg>
        <TextReg
          style={{
            fontSize: 20,
            marginBottom: 10,
          }}>{`Is the entity involved in or providing any of the following services?`}</TextReg>
        {showOptions}
      </View>
      <Button
        isLoading={false}
        disabled={!pick}
        style={{
          alignSelf: 'stretch',
          marginTop: 20,
          backgroundColor: '#00FFBD',
          marginBottom: 50,
        }}
        onPress={() => next()}
        theme={'secondary'}>
        <TextReg style={{color: '#000', fontSize: 18}}>CONTINUE</TextReg>
      </Button>
    </ScrollView>
  )
}

export default EntityQuestions1

const localStyles = StyleSheet.create({
  box: {
    flex: 1,
    padding: 24,
    backgroundColor: '#28283D',
  },
})
