import React, {useEffect, useState} from 'react'
import {StyleSheet, View, Platform, TouchableOpacity, Image} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {TextReg, TextBold, BackgroundHeader, But<PERSON>} from '../../../../components'
import {useDispatch, useSelector} from 'react-redux'
//import {updateFlowB1} from '../../../../store/user/user.actions'
import Progress from './Progress'

let EntityQuestions2 = ({navigation, route}) => {
  let dispatch = useDispatch()
  let b1Obj = useSelector(state => {})
  let WebService = useSelector(state => state.auth.WebService || {})

  let [answer, setAnswer] = useState(route?.params?.isEntityAssociation || false)
  let id = route?.params?.id
  let forInvestment = route?.params?.forInvestment ? true : false
  let stackDepth = route?.params?.stackDepth || 2

  useEffect(() => {
    //console.log('FlowB1Entity', navigation, route)
  }, [])

  let close = () => {
    navigation.pop(stackDepth)
  }

  let next = async () => {
    let payload = {
      isEntityAssociation: answer,
    }
    try {
      let res = await WebService.updateEntity(id, payload)
      navigation.navigate('EntityInfo1Set', {...route?.params, stackDepth: stackDepth + 1, forInvestment})
    } catch (err) {
      console.log('err', err)
    }
  }

  return (
    <View style={styles.box}>
      <View>
        <BackgroundHeader title={'Create New Entity'} close closeFn={() => close()} goBack={() => navigation.goBack()} />
        <Progress complete={2} />
        <TextReg style={{fontSize: 24}}>{`Entity Questions`}</TextReg>
        <TextReg
          style={{
            fontSize: 20,
            marginTop: 20,
          }}>{`Is the entity associated with a government, political entity, military, or religious group?`}</TextReg>
        <View style={styles.slideBox}>
          <TouchableOpacity
            onPress={() => setAnswer(false)}
            style={{
              height: 44,
              borderRadius: 23,
              width: 130,
              backgroundColor: answer ? '#3D3D50' : '#00FFBD',
              alignItems: 'center',
              justifyContent: 'center',
              marginLeft: 4,
            }}>
            <TextReg style={{color: answer ? '#fff' : '#000'}}>NO</TextReg>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => setAnswer(true)}
            style={{
              height: 44,
              borderRadius: 23,
              width: 130,
              backgroundColor: answer ? '#00FFBD' : '#3D3D50',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 4,
            }}>
            <TextReg style={{color: answer ? '#000' : '#fff'}}>YES</TextReg>
          </TouchableOpacity>
        </View>
      </View>
      <Button
        isLoading={false}
        disabled={false}
        style={{
          alignSelf: 'stretch',
          marginTop: 20,
          backgroundColor: '#00FFBD',
          marginBottom: 10,
        }}
        onPress={() => next()}
        theme={'secondary'}>
        <TextReg style={{color: '#000', fontSize: 18}}>CONTINUE</TextReg>
      </Button>
    </View>
  )
}

export default EntityQuestions2

const styles = StyleSheet.create({
  box: {
    flex: 1,
    padding: 24,
    backgroundColor: '#28283D',
    justifyContent: 'space-between',
  },
  qText: {
    fontSize: 20,
  },
  slideBox: {
    marginTop: 30,
    height: 50,
    alignSelf: 'stretch',
    borderRadius: 25,
    backgroundColor: '#3D3D50',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 14,
    overflow: 'hidden',
  },
})
