import React, {useEffect, useState} from 'react'
import {StyleSheet, View, Platform, TouchableOpacity, Image, TextInput, Keyboard} from 'react-native'
import * as ImagePicker from 'react-native-image-picker'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {useFocusEffect} from '@react-navigation/native'
import {TextReg, TextBold, BackgroundHeader, Button, LocationSelect} from '../../../../components'
import {useDispatch, useSelector} from 'react-redux'
import removeFileImg from '../../../../imgs/closeX.png'
import {updateFlowB1} from '../../../../store/user/user.actions'
import {askingForPermissions} from '../../../../store/auth/auth.actions'

import Progress from './Progress'

import styles from '../styles'

let EntityDoc1 = ({navigation, route}) => {
  let dispatch = useDispatch()

  let [loading, setLoading] = useState(false)
  let [error, setError] = useState(false)
  let [docs, setDocs] = useState([])
  let [rejectedErr, setRejectedErr] = useState(false)

  let user = useSelector(state => state.user.user || {})
  let accountRef = useSelector(state => state.auth.account.ref || {})
  let WebService = useSelector(state => state.auth.WebService || {})

  let id = route?.params?.id
  let a = route?.params
  let forInvestment = route?.params?.forInvestment ? true : false
  let stackDepth = route?.params?.stackDepth || 5

  useEffect(() => {
    getDocs()
  }, [])

  useFocusEffect(
    React.useCallback(() => {
      getDocs()
    }, []),
  )

  let getDocs = async () => {
    let res = await WebService.getEntitys()
    let arr = res.data
    let entity = arr?.filter(b => b.id == a.id)[0]

    let docs = entity?.address?.documents || []
    let otherDocs = entity?.documents || []
    docs = docs.concat(otherDocs)
    docs = docs?.filter(a => a.type == 'other_proof_of_address') || []
    let filteredDocs = docs?.filter(b => !b?.rejectedAt) || []
    if (docs?.length > 0 && filteredDocs?.length < 1) {
      setRejectedErr(true)
    }
    setDocs(filteredDocs)
  }

  let removeDocument = async id => {
    try {
      const res = await WebService.removeDocument(id)
      let newDocs = docs?.filter(a => a.id != id)
      if (!newDocs) {
        newDocs = []
      }
      setDocs(newDocs)
    } catch (error) {
      console.error('Failed to remove document:', error)
    }
  }

  let close = () => {
    navigation.pop(stackDepth)
  }

  let next = () => {
    navigation.navigate('EntityDoc2Set', {...a, stackDepth: stackDepth + 1, forInvestment})
  }

  let goBack = () => {
    navigation.goBack()
  }

  let openImageSelect = documentType => {
    dispatch(askingForPermissions(true))

    const options = {
      title: 'Select Document',
      maxWidth: 2200,
      maxHeight: 2200,
      quality: 1.0,
      storageOptions: {
        skipBackup: true,
        path: 'images',
        cameraRoll: true,
        waitUntilSaved: true,
      },
      //takePhotoButtonTitle: null,
      noData: false,
      mediaType: 'photo',
    }

    setLoading(true)
    setError(false)
    setRejectedErr(false)

    ImagePicker.launchImageLibrary(options, async response => {
      if (response.didCancel) {
        setLoading(false)
        return
      } else if (response.error) {
        setLoading(false)
        return
      } else if (response.customButton) {
        //console.log('User tapped custom button: ', response.customButton)
      } else {
        response = response.assets[0]
        const photo = {uri: response.uri}
        const formdata = new FormData()

        const validImage = validateImage(response.type, response.fileSize)
        if (validImage === 'type') {
          setLoading(false)
          setError('type')
          return
        } else if (validImage === 'size') {
          setLoading(false)
          setError('size')
          return
        }

        let path = response.uri
        if (Platform.OS === 'ios') {
          path = '~' + path.substring(path.indexOf('/Documents'))
        }
        if (!response.fileName) response.fileName = path.split('/').pop()

        formdata.append('file', [
          {
            uri: Platform.OS === 'android' ? photo.uri : photo.uri.replace('file://', ''),
            name: response.fileName || 'testName.jpg',
            type: response.type,
            size: response.fileSize,
          },
        ])
        formdata.append('name', response.fileName)
        formdata.append('key', 'true')

        const accounts = user?.accounts
        let businessProfile
        if (accounts) {
          businessProfile = user?.accounts?.filter(a => a.ref == accountRef)[0]?.businessProfile
        }

        const businessAddressID = a?.address?.id
        const businessProfileID = a?.id

        //https://borrower-portal.stage.saltlending.tech/api/v0/documents/0be4cfb6-fa28-44b1-92a6-d065e796233f/address/other_proof_of_address?ref=2

        const docType = documentType == 'address' ? 'other_proof_of_address' : 'verification_of_business'

        const referenceType = documentType == 'address' ? 'address' : 'business_profile'

        const referenceId = documentType == 'address' ? businessAddressID : businessProfileID

        WebService.uploadDocument(referenceId, referenceType, docType, photo.uri.replace('file://', ''), response.fileName)
          .then(res => {
            console.log('res', res)
            const parsedData = JSON.parse(res.data)

            docs.push({
              name: response.fileName,
              id: parsedData.id,
              type: documentType == 'address' ? 'other_proof_of_address' : 'verification_of_business',
            })

            setLoading(false)
            //setDocs(docs)
            //dispatch(updateFlowB1({docsA: docs}))
            setRejectedErr(false)
          })
          .catch(err => {
            console.log('upload doc err', err)
            setLoading(false)
          })
      }
    })
  }

  let validateImage = (type, size) => {
    const fileTypes = ['application/pdf', 'image/jpg', 'image/jpeg', 'image/png', 'image/gif', 'image/tif', 'image/tiff']
    if (!fileTypes.includes(type)) {
      return 'type'
    }
    if (size / 1000000 > 50) {
      return 'size'
    }
    return true
  }

  const addressProofDocs = docs?.filter(a => a.type == 'other_proof_of_address') || []
  const businessDocs = docs?.filter(a => a.type == 'verification_of_business') || []

  const showDocs = addressProofDocs?.map((a, k) => (
    <View style={styles.unit21ShowFilesBox} key={k}>
      <View style={styles.unit21ShowFilesName}>
        <TextReg style={{color: '#FFF', maxWidth: 240}}>{a.name}</TextReg>
      </View>
      {!a.verifiedAt && (
        <TouchableOpacity onPress={() => removeDocument(a.id)}>
          <Image
            source={removeFileImg}
            style={{
              height: 30,
              width: 30,
              marginRight: 4,
            }}
          />
        </TouchableOpacity>
      )}
    </View>
  ))

  return (
    <View style={stylesLocal.box}>
      <KeyboardAwareScrollView style={{flex: 1, alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24}} enableResetScrollToCoords={true}>
        <BackgroundHeader title={'Create New Entity'} close closeFn={() => close()} goBack={() => goBack()} />
        <Progress complete={7} />
        <TextReg style={{fontSize: 24}}>{`Address Verification`}</TextReg>

        <View style={{flexDirection: 'row', marginTop: 20}}>
          <TextReg
            style={{
              fontSize: 20,
              marginRight: 6,
            }}>{`Proof of Address -`}</TextReg>
          <TextReg
            style={{
              fontSize: 20,
              color: '#e5705a',
            }}>
            Required
          </TextReg>
        </View>
        <TextReg style={{fontSize: 16, marginTop: 14}}>Provide ONE of the following:</TextReg>
        <TextReg style={{fontSize: 16, marginTop: 6, marginLeft: 10}}>- Latest Bank Statement</TextReg>
        <TextReg style={{fontSize: 16, marginTop: 4, marginLeft: 10}}>- Latest Utility Bill</TextReg>
        <TextReg style={{fontSize: 16, marginTop: 4, marginLeft: 10}}>- Tax Filing</TextReg>
        <TextReg style={{fontSize: 16, marginTop: 4, marginLeft: 10, marginBottom: 5}}>{`- Government Issued Document`}</TextReg>

        <Button
          isLoading={loading}
          style={{
            alignSelf: 'stretch',
            backgroundColor: '#28283D',
            borderColor: '#00FFBD',
            borderWidth: 1,
            marginTop: 20,
            marginBottom: 8,
          }}
          theme={'secondary'}
          onPress={() => openImageSelect('address')}>
          <TextReg style={{color: '#00FFBD', fontSize: 18}}>UPLOAD DOCUMENT</TextReg>
        </Button>
        {showDocs}
        {docs?.length > 0 && <View style={{height: 1, marginTop: 20, marginBottom: 30, alignSelf: 'stretch', backgroundColor: '#888'}} />}
        {rejectedErr && (
          <View>
            <TextReg style={{color: '#e5705a', fontSize: 18, padding: 10}}>
              {'The previous document was rejected by our team. Please upload new document.'}
            </TextReg>
          </View>
        )}

        <Button
          isLoading={false}
          disabled={docs?.length < 1}
          style={{
            alignSelf: 'stretch',
            marginTop: 10,
            backgroundColor: '#00FFBD',
            marginBottom: 10,
          }}
          onPress={() => next()}
          theme={'secondary'}>
          <TextReg style={{color: '#000', fontSize: 18}}>CONTINUE</TextReg>
        </Button>
        <Button
          isLoading={false}
          style={{
            alignSelf: 'stretch',
            marginTop: 4,
            borderColor: '#00FFBD',
            borderWidth: 1,
            marginBottom: 30,
            backgroundColor: '#28283D',
          }}
          onPress={() => goBack()}>
          <TextReg style={{color: '#00FFBD', fontSize: 18}}>BACK</TextReg>
        </Button>
      </KeyboardAwareScrollView>
    </View>
  )
}

export default EntityDoc1

const stylesLocal = StyleSheet.create({
  box: {
    flex: 1,
    paddingTop: 24,
    backgroundColor: '#28283D',
    justifyContent: 'space-between',
  },
})
