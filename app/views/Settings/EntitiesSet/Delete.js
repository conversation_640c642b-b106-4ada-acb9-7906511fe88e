import React from 'react'
import {Modal, View, Image, TouchableOpacity, ScrollView, SafeAreaView} from 'react-native'
import {useNavigation} from '@react-navigation/native'
import {useDispatch, useSelector} from 'react-redux'
import {TextBold, TextReg} from '../../../components'
import trash2 from '../../../imgs/graphics/trash2.png'
import styles from '../styles'

let Delete = ({route}) => {
  let navigation = useNavigation()
  let WebService = useSelector(state => state.auth.WebService || {})

  let name = route?.params?.name
  let id = route?.params?.id

  let deleteEntity = async () => {
    WebService.deleteEntity(id)
      .then(res => {
        navigation?.pop(2)
      })
      .catch(err => {
        navigation.navigate('NoDeleteSet', {id, name})
      })
  }

  return (
    <SafeAreaView style={local.box}>
      <View />
      <View style={{alignItems: 'center'}}>
        <Image source={trash2} style={{height: 70, width: 60, marginBottom: 14}} />
        <TextBold style={{fontSize: 30}}>Delete This Entity?</TextBold>
        <TextReg
          style={{
            width: 280,
            textAlign: 'center',
            marginTop: 10,
          }}>{`Are you sure you want to delete ${name}? This action cannot be undone.`}</TextReg>
      </View>
      <View style={{alignSelf: 'stretch'}}>
        <TouchableOpacity
          onPress={() => {
            deleteEntity()
          }}
          style={{
            backgroundColor: '#E5705A',
            borderRadius: 5,
            alignSelf: 'stretch',
            height: 46,
            alignItems: 'center',
            marginLeft: 20,
            marginRight: 20,
            justifyContent: 'center',
            marginBottom: 10,
          }}>
          <TextReg style={{fontSize: 20, color: '#28283D', letterSpacing: 1.6}}>{`DELETE`}</TextReg>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            navigation?.goBack()
          }}
          style={{
            borderColor: '#00FFBD',
            borderWidth: 1,
            borderRadius: 5,
            alignSelf: 'stretch',
            height: 46,
            alignItems: 'center',
            marginLeft: 20,
            marginRight: 20,
            justifyContent: 'center',
            marginBottom: 30,
          }}>
          <TextReg style={{fontSize: 20, color: '#00FFBD', letterSpacing: 1.6}}>{`CANCEL`}</TextReg>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  )
}

export default Delete

let local = {
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
}
