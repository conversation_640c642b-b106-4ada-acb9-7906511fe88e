import React, {useState} from 'react'
import {useDispatch, useSelector} from 'react-redux'
import {useNavigation, useFocusEffect} from '@react-navigation/native'

import {
  View,
  Image,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  ActivityIndicator,
  RefreshControl,
  Platform,
} from 'react-native'
import {increaseRefreshDataCount} from '../../../store/user/user.actions'
import {TextReg} from '../../../components'
import {isAuthed} from '../../../store/auth/auth.actions'

let Entities = ({route}) => {
  let dispatch = useDispatch()
  let WebService = useSelector(state => state.auth.WebService || {})
  let user = useSelector(state => state.user.user || {})
  let navigation = useNavigation()
  let [pick, setPick] = useState(-1)
  let [arr, setArr] = useState([])
  let [loading, setLoading] = useState(true)

  let leveraged = route?.params?.leveraged
  let forInvestment = route?.params?.forInvestment ? true : false

  useFocusEffect(
    React.useCallback(() => {
      getArr()
    }, []),
  )

  let getArr = async () => {
    setLoading(true)
    WebService.getEntitys()
      .then(res => {
        if (route?.params?.marketFlow) {
          const index = findKeyOfLatestObject(res.data)
          setPick(index)
        }
        setArr(res.data)
        setLoading(false)
      })
      .catch(err => {
        console.log('getEntitys err', err)
        setLoading(false)
      })
  }

  const findKeyOfLatestObject = arr => {
    return arr.reduce((acc, curr, index) => {
      if (new Date(curr.createdAt) > new Date(arr[acc].createdAt)) {
        return index
      }
      return acc
    }, 0) // Start comparing from the first element
  }

  let showEntities = arr?.map((a, k) => {
    let date = new Date(a.createdAt)
    let options = {}
    console.log('arr: ', a, k)
    let showDate = ''
    if (Platform.OS === 'ios') {
      options = {month: 'short', day: 'numeric', year: 'numeric'}
      showDate = date.toLocaleDateString('en-US', options)
    } else {
      var dayOfWeek = ['Mon', 'Tue', 'Wed', 'Thur', 'Fri', 'Sat', 'Sun'],
        monthName = [
          'January',
          'February',
          'March',
          'April',
          'May',
          'June',
          'July',
          'August',
          'September',
          'October',
          'November',
          'December',
        ],
        utc = date.getTime() + date.getTimezoneOffset() * 60000,
        US_date = new Date(a.createdAt)

      showDate =
        monthName[US_date.getMonth()] + ' ' + US_date.getDate() + ', ' + US_date.getFullYear()
    }

    let showName = a.legalName
    if (a.legalName == null) {
      showName = 'Unnamed'
    }

    let doc1 = a?.documents?.filter(b => b.verifiedAt)?.length > 0
    let doc2 = a?.address?.documents?.filter(b => b.verifiedAt)?.length > 0
    return (
      <TouchableOpacity
        key={k}
        disabled={forInvestment ? a?.isDisabled : false}
        style={
          forInvestment
            ? a.isDisabled
              ? {...local.card, opacity: 0.6}
              : local.card
            : k == pick
            ? local.cardActive
            : local.card
        }
        onPress={() => setPick(pick == k ? -1 : k)}>
        <View
          style={{
            flexDirection: 'row',
            paddingLeft: 12,
            paddingRight: 12,
            marginBottom: 9,
            alignSelf: 'stretch',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <View
              style={{
                height: 50,
                width: 50,
                borderRadius: 10,
                backgroundColor: '#28283D',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Image
                source={require('../../../imgs/graphics/building.png')}
                style={{height: 30, width: 18}}
              />
            </View>
            <View style={{marginLeft: 10}}>
              <TextReg style={{fontSize: 16, marginTop: 0}}>{`${showName}`}</TextReg>
              <TextReg style={{fontSize: 16, marginTop: 0}}>{`${showDate}`}</TextReg>
            </View>
          </View>
          <View style={{flexDirection: 'row'}}>
            {doc1 && doc2 && (
              <Image
                source={require('../../../imgs/graphics/shield.png')}
                style={{height: 25, width: 20, marginRight: 8, marginTop: 3}}
              />
            )}
            {a.isEntityAccredited && (
              <Image
                source={require('../../../imgs/graphics/ribbon.png')}
                style={{height: 28, width: 20, marginRight: 6}}
              />
            )}
          </View>
        </View>
      </TouchableOpacity>
    )
  })

  let showEntitiesLend = arr?.map((a, k) => {
    if (!a.isEntityAccredited) return
    let date = new Date(a.createdAt)
    let options = {}
    let showDate = ''
    if (Platform.OS === 'ios') {
      options = {month: 'short', day: 'numeric', year: 'numeric'}
      showDate = date.toLocaleDateString('en-US', options)
    } else {
      var dayOfWeek = ['Mon', 'Tue', 'Wed', 'Thur', 'Fri', 'Sat', 'Sun'],
        monthName = [
          'January',
          'February',
          'March',
          'April',
          'May',
          'June',
          'July',
          'August',
          'September',
          'October',
          'November',
          'December',
        ],
        utc = date.getTime() + date.getTimezoneOffset() * 60000,
        US_date = new Date(a.createdAt)

      showDate =
        monthName[US_date.getMonth()] + ' ' + US_date.getDate() + ', ' + US_date.getFullYear()
    }

    let showName = a.legalName
    if (a.legalName == null) {
      showName = 'Unnamed'
    }

    let doc1 = a?.documents?.filter(b => b.verifiedAt)?.length > 0
    let doc2 = a?.address?.documents?.filter(b => b.verifiedAt)?.length > 0
    return (
      <TouchableOpacity
        key={k}
        disabled={forInvestment ? a?.isDisabled : false}
        style={
          forInvestment
            ? a.isDisabled
              ? {...local.card, opacity: 0.6}
              : local.card
            : k == pick
            ? local.cardActive
            : local.card
        }
        onPress={() => setPick(pick == k ? -1 : k)}>
        <View
          style={{
            flexDirection: 'row',
            paddingLeft: 12,
            paddingRight: 12,
            marginBottom: 9,
            alignSelf: 'stretch',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <View
              style={{
                height: 50,
                width: 50,
                borderRadius: 10,
                backgroundColor: '#28283D',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Image
                source={require('../../../imgs/graphics/building.png')}
                style={{height: 30, width: 18}}
              />
            </View>
            <View style={{marginLeft: 10}}>
              <TextReg style={{fontSize: 16, marginTop: 0}}>{`${showName}`}</TextReg>
              <TextReg style={{fontSize: 16, marginTop: 0}}>{`${showDate}`}</TextReg>
            </View>
          </View>
          <View style={{flexDirection: 'row'}}>
            {doc1 && doc2 && (
              <Image
                source={require('../../../imgs/graphics/shield.png')}
                style={{height: 25, width: 20, marginRight: 8, marginTop: 3}}
              />
            )}
            {a.isEntityAccredited && (
              <Image
                source={require('../../../imgs/graphics/ribbon.png')}
                style={{height: 28, width: 20, marginRight: 6}}
              />
            )}
          </View>
        </View>
      </TouchableOpacity>
    )
  })

  let next = async notNew => {
    if (notNew) {
      let entity = arr[pick]

      const addressDocs = entity?.address?.documents
      const businessDocs = entity?.documents
      let id = arr[pick]?.id

      const docsVerified =
        businessDocs?.some(doc => doc.verifiedAt) && addressDocs?.some(doc => doc.verifiedAt)

      const docsRejected =
        addressDocs?.length > 0 && businessDocs?.length > 0
          ? businessDocs?.every(doc => doc.rejectedAt) || addressDocs?.every(doc => doc.rejectedAt)
          : false

      const isVerified = entity.isComplete && docsVerified
      const isRejected = entity.isComplete && docsRejected
      const isComplete =
        entity.isComplete && addressDocs?.length && businessDocs?.length && !isRejected
          ? true
          : false

      if (forInvestment && isComplete && !isVerified) {
        navigation?.navigate('ManualCompliance', {forEntity: true})
      } else if (!isComplete || isRejected) {
        navigation?.navigate('EntityQuestions1Set', {...entity, marketFlow: true, forInvestment})
      } else {
        WebService.createSaltAccount({
          type: 'business',
          leveraged: leveraged,
          productType: forInvestment ? 'investment' : 'loan',
        }).then(async res => {
          try {
            let id = res.data?.accountId
            let ref = res.data?.ref
            dispatch(isAuthed({ref, email: user.primaryEmail}))
            await WebService.updateRef(ref)
            res = await WebService.assignEntity(entity.id, ref)
            res = await WebService.setAccountName(id, ref)
            if (forInvestment) {
              dispatch(increaseRefreshDataCount())
              navigation?.popToTop()
              navigation.navigate('CreateInvestment')
            } else {
              dispatch(increaseRefreshDataCount('auto'))
              navigation?.popToTop()
              navigation.navigate('Home')
            }
          } catch (err) {
            console.log('err', err)
          }
        })
      }
    } else {
      let res = await WebService.newEntity()
      let obj = res.data
      navigation?.navigate('EntityQuestions1Set', {...obj, marketFlow: true, forInvestment})
    }
  }
  return (
    <SafeAreaView style={local.box}>
      <View
        style={{
          flexDirection: 'row',
          alignSelf: 'stretch',
          justifyContent: 'space-between',
          marginTop: 5,
          marginBottom: 5,
        }}>
        {forInvestment ? (
          <TouchableOpacity
            onPress={() => navigation?.goBack()}
            style={{height: 40, width: 40, marginLeft: 20}}>
            <Image
              source={require('../../../imgs/backToSettings.png')}
              style={{height: 25, width: 20}}
            />
          </TouchableOpacity>
        ) : (
          <View style={{height: 40, width: 40, marginRight: 20}} />
        )}
        <TextReg style={{color: '#FFF', fontSize: 18}}>
          {forInvestment ? 'Open LEND Account' : 'Entity Select'}
        </TextReg>
        <View style={{height: 40, width: 40, marginRight: 20}} />
      </View>
      <ScrollView
        style={{alignSelf: 'stretch'}}
        refreshControl={
          <RefreshControl
            refreshing={false}
            onRefresh={() => getArr()}
            colors={['#28283D']}
            progressBackgroundColor={['#28283D00']}
            tintColor={'#28283D'}
            style={{opacity: 0}}
          />
        }>
        {loading && <ActivityIndicator size="large" color="#fff" style={{marginBottom: 10}} />}

        <TextReg
          style={{
            fontSize: 18,
            marginLeft: 20,
            marginRight: 20,
            marginTop: 10,
            marginBottom: 14,
          }}>{`Which Entity would you like to open this ${
          forInvestment ? 'LEND' : 'BORROW'
        } account under?`}</TextReg>

        {arr?.length >= 7 && (
          <TouchableOpacity
            onPress={() => {
              next(pick > -1)
            }}
            style={{...local.new, backgroundColor: pick > -1 ? '#00FFBD' : '#28283D'}}>
            <TextReg style={{fontSize: 20, color: pick > -1 ? '#28283D' : '#00FFBD'}}>
              {pick > -1 ? `CONTINUE` : `+ CREATE NEW`}
            </TextReg>
          </TouchableOpacity>
        )}
        <>{forInvestment ? showEntitiesLend : showEntities}</>
        {arr?.length <= 6 && (
          <TouchableOpacity
            onPress={() => {
              next(pick > -1)
            }}
            style={{
              ...local.new,
              marginBottom: 30,
              backgroundColor: pick > -1 ? '#00FFBD' : '#28283D',
            }}>
            <TextReg style={{fontSize: 20, color: pick > -1 ? '#28283D' : '#00FFBD'}}>
              {pick > -1 ? `CONTINUE` : `+ CREATE NEW`}
            </TextReg>
          </TouchableOpacity>
        )}
      </ScrollView>
    </SafeAreaView>
  )
}

export default Entities

let local = {
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
  card: {
    backgroundColor: '#3D3D50',
    alignSelf: 'stretch',
    borderRadius: 10,
    marginLeft: 18,
    marginRight: 18,
    marginBottom: 8,
    paddingTop: 10,
    borderWidth: 2,
    borderColor: '#28283D',
  },
  cardActive: {
    backgroundColor: '#3D3D50',
    alignSelf: 'stretch',
    borderRadius: 10,
    marginLeft: 18,
    marginRight: 18,
    marginBottom: 8,
    paddingTop: 10,
    borderWidth: 2,
    borderColor: '#00FFBD',
  },
  new: {
    borderColor: '#00FFBD',
    borderWidth: 1,
    borderRadius: 10,
    alignSelf: 'stretch',
    height: 50,
    alignItems: 'center',
    marginLeft: 20,
    marginRight: 20,
    justifyContent: 'center',
    marginBottom: 8,
    marginTop: 2,
    backgroundColor: '#28283D',
  },
}
