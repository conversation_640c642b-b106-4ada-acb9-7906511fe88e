import React, {Component} from 'react';
import {View, Text, TextInput, Keyboard, ScrollView} from 'react-native';

import {connect} from 'react-redux';
import {updateActiveTabListener} from '../../../util/helpers';
import {screenView, sendEvent} from '../../../store/analytics/analytics.actions';
import {Button, BackgroundHeader, ErrorModal, LocationSelect, TextReg} from '../../../components';
import commonStyles from '../../../styles/commonStyles';

import styles from '../styles';

class ContactSupport extends Component {
  constructor(props) {
    super(props);
    this.state = {
      text: '',
      phone: '',
      dropdownData: [
        {
          value: 'Catagory',
        },
      ],
      isLoading: false,
      showErrorModal: false,
      validatePhoneNum: true,
      chosenCatagory: null,
    };
  }

  componentDidMount() {
    updateActiveTabListener(this.props.navigation);
    this.props.dispatch(screenView(`Contact Support`));
  }

  hideKeyboard = () => {
    Keyboard.dismiss();
  };

  toggleLoadingModal = () => {
    this.setState({showLoadingModal: !this.state.showLoadingModal});
  };

  showErrorModal = () => {
    this.setState({showLoadingModal: false, showErrorModal: true});
  };

  closeErrorModal = () => {
    this.setState({showErrorModal: false});
  };

  closeContactSupport = () => {
    this.setState({showLoadingModal: false}, () => this.props.navigation.goBack());
  };

  onSelect = chosenCatagory => {
    this.setState({chosenCatagory});
  };

  handleSubmit = async () => {
    // first using the user email, get their ID
    this.setState({isLoading: true});

    let data1 = {
      name: `${this.props.user.firstName} ${this.props.user.lastName}`,
      emails: [{email: this.props.user.primaryEmail}],
      phones: [],
    };

    let customerID;
    let k1res = await this.props.WebService.kustomerSupport1(data1).catch(async err => {
      console.log('err', err);
      if (err.data?.body?.errors[0]?.detail == 'duplicate email') {
        console.log('get the customerID from already email');
        let emailEncoded = encodeURIComponent(this.props.user.primaryEmail);
        let customerGetRes = await this.props.WebService.kustomerSupportGet({emailEncoded});
        console.log('customerGetRes', customerGetRes);
        //customerID = customerGetRes?.data?.data?.id;
        return customerGetRes;
      }
    });
    console.log('k1res', k1res);
    customerID = k1res.data?.data?.id;
    let data2 = {
      customerID,
    };
    let k2res = await this.props.WebService.kustomerSupport2(data2);
    console.log('k2res', k2res);
    let convoID = k2res?.data?.data?.id;
    console.log('convoID', convoID);

    let preview = this.state.text;
    let k3res = await this.props.WebService.kustomerSupport3({convoID, preview}).catch(err => {
      console.log('k3 err', err);
    });
    console.log('k3res', k3res);
    if (k3res.data?.data?.id) {
      this.setState({text: '', showSuccess: true, isLoading: false});
      setTimeout(() => {
        this.setState({showSuccess: false});
      }, 3000);
    }
    /*
    this.props.WebService.getZendeskUser(userData)
      .then(res => {
        const userID = res.data.user.id;
        const feedbackData = {
          ticket: {
            subject: 'Mobile Ticket',
            comment: {
              body: `${this.state.text} \n - \n Email : ${this.props.user.primaryEmail} \n Phone : ${this.state.phone} `,
            },
            requester_id: userID,
            submitter_id: userID,
            priority: 'normal',
          },
        };
        return this.props.WebService.postZendeskFeedback(feedbackData);
      })
      .then(res => {
        // get ticket id
        const ticketID = res.data.ticket.id;
        const fieldID = res.data.ticket.fields[0].id;
        //const chosenCatagory = this.dropdown.selectedItem().field;
        const chosenCatagory = this.state.chosenCatagory;
        const updateData = {
          ticket: {
            custom_fields: [{id: fieldID, value: chosenCatagory}],
          },
        };
        return this.props.WebService.updateZendeskField(ticketID, updateData);
      })
      .then(res => {
        this.props.dispatch(sendEvent(`Submit Support Ticket`));
        this.closeContactSupport();
        this.setState({isLoading: false});
      })
      .catch(err => {
        this.showErrorModal();
        this.setState({isLoading: false});
      });
      */
  };

  handlePhoneInput = text => {
    text = text.replace('-', '').replace(' ', '');
    const validatePhoneNum = /^[0-9]{0,15}$/.test(text);
    this.setState({phone: text, validatePhoneNum});
  };

  render() {
    let {showSuccess} = this.state;
    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Contact Support'} goBack={this.props.navigation.goBack} />

        <ScrollView
          style={{
            flex: 1,
            flexDirection: 'column',
            alignSelf: 'stretch',
          }}
          contentContainerStyle={{
            justifyContent: 'flex-start',
            alignItems: 'center',
          }}>
          <ErrorModal
            modalVisable={this.state.showErrorModal}
            closeErrorModal={this.closeErrorModal}
            showPinScreen={this.props.showPinScreen}
          />
          <TextReg style={styles.description}>
            Describe your issue below and a Customer Support representative will be in contact via Email
          </TextReg>

          <View style={styles.inputContainer}>
            <TextInput
              style={styles.inputField}
              onChangeText={text => this.setState({text})}
              value={this.state.text}
              multiline
              underlineColorAndroid="transparent"
              keyboardAppearance="dark"
            />
          </View>

          <Button
            style={styles.submitButton}
            onPress={this.handleSubmit}
            disabled={!this.state.validatePhoneNum}
            isLoading={this.state.isLoading}>
            Submit
          </Button>
          {showSuccess && <TextReg style={{fontSize: 17}}>Submitted</TextReg>}
        </ScrollView>
      </View>
    );
  }
}

ContactSupport.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  //headerTintColor: '#FFF',
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    //shadowColor: 'transparent',
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
});

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  account: state.auth.account,
  user: state.user.user,
  showPinScreen: state.auth.pinScreen,
});

export default connect(mapStateToProps)(ContactSupport);
