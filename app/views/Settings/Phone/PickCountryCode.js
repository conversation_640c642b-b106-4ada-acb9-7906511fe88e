import React, {Component} from 'react';
import {View, Text, Image, FlatList, TouchableOpacity, TextInput} from 'react-native';
import {connect} from 'react-redux';
import {updateActiveTabListener} from '../../../util/helpers';
import {screenView} from '../../../store/analytics/analytics.actions';
import {BackgroundHeader} from '../../../components';

import countryCodes from '../../../util/countryCodes';

import searchIcon from '../../../imgs/searchIcon.png';

import commonStyles from '../../../styles/commonStyles';
import styles from '../styles';

class PickCountryCode extends Component {
  constructor(props) {
    super(props);
    this.countryCodesArr = countryCodes();
    this.state = {
      countryCodes: this.countryCodesArr,
      searchCountry: '',
    };
  }

  componentDidMount() {
    updateActiveTabListener(this.props.navigation);
    this.props.dispatch(screenView(`Change Password`));
  }

  updateSearch = text => {
    const updatedSearchArr = this.countryCodesArr.filter(a => a.name.includes(text));
    this.setState({searchCountry: text});
    this.setState({countryCodes: updatedSearchArr});
  };

  handleSelectCountry = item => {
    this.props.route.params.selectCountry(item);
    this.props.navigation.goBack();
  };

  render() {
    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Country Code'} goBack={this.props.navigation.goBack} />
        <View style={styles.pickCountryCodeInputBox}>
          <Image
            source={searchIcon}
            style={{
              height: 20,
              width: 20,
              opacity: 0.7,
            }}
          />
          <TextInput
            style={styles.pickCountryCodeInput}
            onChangeText={text => this.updateSearch(text)}
            value={this.state.searchCountry}
            underlineColorAndroid="transparent"
            blurOnSubmit
            onSubmitEditing={() => this.hideKeyboard()}
            placeholder={'Search'}
            keyboardAppearance="dark"
          />
        </View>
        <FlatList
          style={styles.list}
          data={this.state.countryCodes}
          renderItem={({item}) => (
            <TouchableOpacity
              style={styles.pickCountryCodeListItem}
              onPress={() => {
                this.handleSelectCountry(item);
              }}
              key={item.dial_code}>
              <View style={styles.listItemLeft}>
                <Text style={styles.listItemText}>{item.name}</Text>
              </View>
              <View style={styles.listItemRight}>
                <Text style={styles.listItemText}>{item.dial_code}</Text>
              </View>
            </TouchableOpacity>
          )}
        />
      </View>
    );
  }
}

PickCountryCode.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
});

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
});

export default connect(mapStateToProps)(PickCountryCode);
