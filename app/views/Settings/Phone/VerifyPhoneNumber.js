import React, {Component} from 'react'
import {View, Keyboard} from 'react-native'
import {connect} from 'react-redux'
import {updateActiveTabListener} from '../../../util/helpers'
import {screenView} from '../../../store/analytics/analytics.actions'

import {updateUser, deletePhone, addPhone} from '../../../store/user/user.actions'
import NewPhone from './NewPhone'
import VerifyPhonePin from './VerifyPhonePin'
import PhonePinSuccess from './PhonePinSuccess'
import countryCodes from '../../../util/countryCodes'
import {BackgroundHeader} from '../../../components'

import styles from '../styles'

class VerifyPhoneNumber extends Component {
  constructor(props) {
    super(props)
    const defaultCountry = countryCodes().filter(a => a.name === 'United States')[0]
    this.state = {
      phoneNumber: '',
      countryCode: defaultCountry.dial_code,
      countryName: defaultCountry.name,
      countryLetters: defaultCountry.code,
      countryImageUri: defaultCountry.uri,
      showPinVerify: false,
      pin: '',
      pinError: false,
      phonePinSuccess: false,
      isSubmitting: false,
      phoneError: false,
    }
  }
  componentDidMount() {
    updateActiveTabListener(this.props.navigation)
    this.props.screenView(`Verify Phone`)
    if (this.props.user.phone) {
      const countryCode = `+${this.props.user.phone.callingCode}`
      const countryLetters = this.props.user.phone.countryCode
      const phoneNumber = this.props.user.phone.number
      const validatePhoneNum = phoneNumber.length >= 1 && phoneNumber.length <= 15
      this.setState({
        countryCode,
        countryLetters,
        phoneNumber,
        validatePhoneNum,
      })
    }
  }

  openSelectCountry = () => {
    const selectCountry = this.selectCountry
    this.props.navigation.navigate('PickCountryCode', {selectCountry})
  }

  hideKeyboard = () => {
    Keyboard.dismiss()
  }

  handleSubmit = async () => {
    let useSms = this.props.launchDarkly['enable-text-message-notifications'] || false

    this.setState({isSubmitting: true, phoneError: false})
    if (this.props.user.phone && !this.props.user.phone.isVerified) {
      await this.props.deletePhone(this.props.user.phone.id)
    }
    const parsedCountryCode = this.state.countryCode.replace('+', '')
    this.props
      .addPhone(this.state.countryLetters, parsedCountryCode, this.state.phoneNumber)
      .then(res => {
        this.setState({
          isSubmitting: false,
          showPinVerify: useSms ? true : false,
          phoneID: res.data.id,
        })
        if (!useSms) {
          this.props.navigation.goBack()
        }
      })
      .catch(err => {
        this.setState({isSubmitting: false})
        let error = err?.data?.body?.error || ''
        if (error == 'cannot add phone that has already been verified') {
          this.setState({phoneError: 'Cannot add phone that has already been verified'})
        }
      })
  }

  handleBack = () => {
    this.props.navigation.goBack()
  }

  selectCountry = item => {
    this.setState({
      countryName: item.name,
      countryCode: item.dial_code,
      countryLetters: item.code,
      countryImageUri: item.uri || '',
    })
  }

  handlePhoneInput = text => {
    text = text.replace('-', '').replace(' ', '')
    const validatePhoneNum = text.length >= 1 && text.length <= 15
    this.setState({phoneNumber: text, validatePhoneNum})
  }

  handlePinInput = text => {
    this.setState({pin: text})
  }

  handleSubmitPin = () => {
    this.setState({pinError: false, isSubmitting: true})
    this.props.WebService.verifyPhonePin(this.state.phoneID, this.state.pin)
      .then(res => {
        this.setState({phonePinSuccess: true, isSubmitting: false})
      })
      .catch(err => {
        this.setState({pinError: true, isSubmitting: false})
      })
  }

  handleResend = () => {
    this.props.WebService.resendPhonePin(this.state.phoneID)
      .then(res => {
        // console.log('resendPhonePin res', res)
      })
      .catch(err => {
        // console.log(err)
      })
  }

  backToSettingsScreen = () => {
    this.props.WebService.getSaltUser().then(res => {
      this.props.updateUser(res.data)
    })
    this.setState({phonePinSuccess: false})
    this.props.navigation.pop(2)
  }

  render() {
    let useSms = this.props.launchDarkly['enable-text-message-notifications'] || false
    return (
      <View style={styles.verifyPhoneContainer}>
        <BackgroundHeader title={'Add Phone'} toggleNotifications={this.toggleNotifications} goBack={this.props.navigation.goBack} />
        {this.state.phonePinSuccess && <PhonePinSuccess backToSettingsScreen={this.backToSettingsScreen} />}
        {!this.state.phonePinSuccess && (
          <View style={styles.verifyPhoneContainer}>
            {this.state.showPinVerify ? (
              <VerifyPhonePin
                phoneNumber={this.state.phoneNumber}
                countryCode={this.state.countryCode}
                handlePinInput={this.handlePinInput}
                handleSubmitPin={this.handleSubmitPin}
                pin={this.state.pin}
                hideKeyboard={this.hideKeyboard}
                handleResend={this.handleResend}
                pinError={this.state.pinError}
                isSubmitting={this.state.isSubmitting}
              />
            ) : (
              <NewPhone
                validatePhoneNum={this.state.validatePhoneNum}
                openSelectCountry={this.openSelectCountry}
                handlePhoneInput={this.handlePhoneInput}
                hideKeyboard={this.hideKeyboard}
                handleSubmit={this.handleSubmit}
                handleBack={this.handleBack}
                countryName={this.state.countryName}
                countryCode={this.state.countryCode}
                phoneNumber={this.state.phoneNumber}
                isSubmitting={this.state.isSubmitting}
                countryLetters={this.state.countryLetters}
                countryImageUri={this.state.countryImageUri}
                phoneError={this.state.phoneError}
                useSms={useSms}
              />
            )}
          </View>
        )}
      </View>
    )
  }
}

VerifyPhoneNumber.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  accountRef: state.auth.account.ref,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps, {deletePhone, addPhone, screenView, updateUser})(VerifyPhoneNumber)
