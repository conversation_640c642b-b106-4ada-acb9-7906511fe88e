import React from 'react'
import { View, Text, Image, TouchableOpacity } from 'react-native'
import { KeyboardAwareScrollView } from '@codler/react-native-keyboard-aware-scroll-view'
import { Button } from '../../../components'

import verifyPhoneImg from '../../../imgs/verifyPhoneImgSuccess.png'

import styles from '../styles'

const PhonePinSuccess = props => (
  <KeyboardAwareScrollView
    style={styles.verifyPhoneNumberContainerAvoidKeyboard}
  >
    <View style={styles.verifyPhoneNumberContainer}>
      <View style={styles.verifyPhoneNumberGroup}>
        <Image
          source={verifyPhoneImg}
          style={{
            height: 125,
            width: 96,
            marginBottom: 20,
            marginTop: 50,
          }}
        />
        <Text style={[styles.verifyPhoneNumberTitle, { marginBottom: 30 }]}>
          Phone Number Verified
        </Text>
        <Button onPress={props.backToSettingsScreen}>Ok</Button>
      </View>
    </View>
  </KeyboardAwareScrollView>
)

export default PhonePinSuccess
