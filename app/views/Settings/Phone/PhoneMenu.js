import React, {Component} from 'react'
import {View, Text, Image, FlatList, TouchableOpacity} from 'react-native'
import {connect} from 'react-redux'

import DeletePhoneModal from './DeletePhoneModal'
import {updateUser, deletePhone} from '../../../store/user/user.actions'
import {showNotifications} from '../../../store/notifications/notifications.actions'
import {BackgroundHeader, Card} from '../../../components'

import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'

class PhoneMenu extends Component {
  constructor(props) {
    super(props)
    this.state = {
      showDeleteModal: false,
      showAddPhone: false,
    }
  }

  clickButton = (showArrow, item) => {
    if (showArrow) {
      this.props.navigation.navigate('PhoneNumber', {type: item})
    } else {
      this.setState({showDeleteModal: true})
    }
  }

  closeDeleteModal = () => {
    this.setState({showDeleteModal: false})
  }

  toggleNotifications = () => {
    this.props.dispatch(showNotifications(true))
  }

  confirmDelete = () => {
    this.setState({showDeleteModal: false})
    this.props
      .deletePhone(this.props.user.phone.id, this.props.accountRef)
      .then(() => this.props.WebService.getSaltUser(this.props.accountRef))
      .then(res => {
        this.props.updateUser(res.data)
      })
      .catch(err => {
        //console.log('delete phone err', err)
      })
  }

  closeAddPhone = () => {}

  goBack = () => {
    if (this.props.navigation.route?.flow === 'loanChecklist') {
      this.props.navigation.popToTop()
      this.props.navigation.navigate('Home')
    } else {
      this.props.navigation.goBack()
    }
  }

  render() {
    let useSms = this.props.launchDarkly['enable-text-message-notifications'] || false
    const phoneList = []
    let showArrow = true
    let userPhone = this.props.user?.phone || null
    if ((userPhone && userPhone?.isVerified) || (!useSms && userPhone)) {
      phoneList.push(this.props.user.phone)
      showArrow = false
    } else {
      phoneList.push({title: 'Add Phone Number'})
    }

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Phone Number'} goBack={this.goBack} />
        <DeletePhoneModal
          closeDeleteModal={this.closeDeleteModal}
          showDeleteModal={this.state.showDeleteModal}
          confirmDelete={this.confirmDelete}
          showPinScreen={this.props.showPinScreen}
          useSms={useSms}
        />
        <FlatList
          style={styles.list}
          contentContainerStyle={{alignItems: 'center'}}
          data={phoneList}
          renderItem={({item}) => (
            <Card marginTop={14}>
              <TouchableOpacity style={{alignSelf: 'stretch'}} onPress={() => this.clickButton(showArrow, item)}>
                <View style={styles.listItemPhone}>
                  <View style={styles.listItemLeft}>
                    <Text style={styles.listItemText}>{showArrow ? item.title : item.number}</Text>
                  </View>
                  <View style={styles.listItemRight}>
                    {showArrow ? (
                      <Image source={require('../../../imgs/rightArrow.png')} style={styles.rightArrowIcon} />
                    ) : (
                      <Text style={styles.removePhoneText}>Remove</Text>
                    )}
                  </View>
                </View>
              </TouchableOpacity>
            </Card>
          )}
        />
      </View>
    )
  }
}

PhoneMenu.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  accountRef: state.auth.account.ref,
  showPinScreen: state.auth.pinScreen,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps, {
  deletePhone,
  updateUser,
})(PhoneMenu)
