import React from 'react'
import { View, Text, Image, TouchableOpacity, TextInput } from 'react-native'
import { KeyboardAwareScrollView } from '@codler/react-native-keyboard-aware-scroll-view'
import { Button } from '../../../components'

import verifyPhoneImg from '../../../imgs/verifyPhoneImg.png'

import styles from '../styles'

const VerifyPhonePin = (props) => (
  <KeyboardAwareScrollView style={styles.verifyPhoneNumberContainerAvoidKeyboard}>
    <View style={styles.verifyPhoneNumberContainer}>
      <View style={styles.verifyPhoneNumberGroup}>
        <Image
          source={verifyPhoneImg}
          style={{
            height: 125,
            width: 96,
            marginBottom: 20
          }}
        />
        <Text style={styles.verifyPhoneNumberTitle}>Verify Phone Number</Text>
        <Text style={styles.verifyPhoneNumberDescription}>Please type verification code sent to</Text>
        <Text style={styles.verifyPhoneNumberDescription}>{`${props.countryCode} ${props.phoneNumber}`}</Text>

        <View style={styles.verifyPhonePinBox}>
          <TextInput
            style={styles.verifyPhonePinInput}
            onChangeText={(text) => props.handlePinInput(text)}
            value={props.pin}
            underlineColorAndroid="transparent"
            blurOnSubmit
            returnKeyType={'done'}
            onSubmitEditing={() => props.hideKeyboard()}
            placeholder={'123456'}
            autoCapitalize={'none'}
            keyboardAppearance="dark"
          />
        </View>
        {props.pinError && (
          <View>
            <Text style={styles.pinErrorText}>Incorrect Pin</Text>
          </View>
        )}
        <Button onPress={props.handleSubmitPin} isLoading={props.isSubmitting}>
          Verify
        </Button>
        <TouchableOpacity onPress={props.handleResend}>
          <Text style={styles.verifyPhoneNumberNotNow}>Resend Code</Text>
        </TouchableOpacity>
      </View>
    </View>
  </KeyboardAwareScrollView>
)

export default VerifyPhonePin
