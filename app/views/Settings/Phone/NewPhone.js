import React from 'react'
import {View, Text, Image, TouchableOpacity, TextInput} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {Button, TextReg} from '../../../components'

import verifyPhoneImg from '../../../imgs/verifyPhoneImg.png'

import styles from '../styles'

const NewPhone = props => {
  let info = 'Please enter your phone number so you can get notifications via phone calls'
  if (props.useSms) {
    info = 'Please enter your phone number so you can get notifications via calls and SMS'
  }
  return (
    <KeyboardAwareScrollView style={styles.verifyPhoneNumberContainerAvoidKeyboard}>
      <View style={styles.verifyPhoneNumberContainer}>
        <View style={styles.verifyPhoneNumberGroup}>
          <Image
            source={verifyPhoneImg}
            style={{
              height: 125,
              width: 96,
              marginBottom: 20,
            }}
          />
          <Text style={styles.verifyPhoneNumberTitle}>Add Phone Number</Text>
          <Text style={styles.verifyPhoneNumberDescription}>{info}</Text>
          <TouchableOpacity style={styles.verifyPhoneNumberCountrySelect} onPress={() => props.openSelectCountry()}>
            <View style={styles.verifyPhoneNumberCountrySide}>
              <Image source={props.countryImageUri} style={styles.countryFlagImg} />
              <Text style={styles.verifyPhoneNumberCountryText}>{props.countryName}</Text>
            </View>
            <Image source={require('../../../imgs/rightArrow.png')} style={styles.rightArrowIcon} />
          </TouchableOpacity>
          <View style={styles.verifyPhoneNumberPhoneBox}>
            <View style={styles.verifyPhoneNumberCountryCodeBox}>
              <Text style={styles.verifyPhoneNumberCountryCodeText}>{props.countryCode}</Text>
            </View>
            <TextInput
              style={styles.verifyPhoneNumberInput}
              onChangeText={text => props.handlePhoneInput(text)}
              value={props.phoneNumber}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'done'}
              onSubmitEditing={() => props.hideKeyboard()}
              placeholder={'************'}
              keyboardType={'numeric'}
              textContentType="telephoneNumber"
              keyboardAppearance="dark"
            />
          </View>
          <Button
            style={{alignSelf: 'stretch', marginLeft: 40, marginRight: 40}}
            disabled={!props.validatePhoneNum}
            onPress={props.handleSubmit}
            isLoading={props.isSubmitting}>
            Next
          </Button>
          {props.phoneError && (
            <View style={{width: 280, marginTop: 14}}>
              <TextReg style={[styles.pinErrorText, {textAlign: 'center'}]}>{props.phoneError}</TextReg>
            </View>
          )}
        </View>
      </View>
    </KeyboardAwareScrollView>
  )
}

export default NewPhone
