import React from 'react'
import {Modal, View, Text, Image, TouchableOpacity} from 'react-native'
import {Button} from '../../../components'

import styles from '../styles'

const DeletePhoneModal = props => {
  let info = `If you remove your phone number, you will no longer be able to receive calls. Do you still want to remove
  your phone number?`
  if (props.useSms) {
    info = `If you remove your phone number, you will no longer be able to receive call or SMS notifications. Do you still want to remove
    your phone number?`
  }
  return (
    <Modal animationType="fade" transparent visible={props.showDeleteModal && !props.showPinScreen} onRequestClose={() => ({})}>
      <View style={styles.helpModalBox}>
        <View style={styles.helpModalSquare}>
          <TouchableOpacity
            style={styles.helpModalX}
            onPress={() => {
              props.closeDeleteModal()
            }}>
            <Image source={require('../../../imgs/closeX.png')} style={styles.closeModalImg} />
          </TouchableOpacity>
          <View style={styles.helpModalTitleBox}>
            <Text style={styles.helpModalTitle}>{props.title}</Text>
          </View>
          <View style={styles.deleteModalDescriptionBox}>
            <Text style={styles.deleteModalTitle}>Remove Phone Number?</Text>
            <Text style={styles.deleteModalDescription}>{info}</Text>
            <Button onPress={props.confirmDelete}>Remove</Button>
          </View>
        </View>
      </View>
    </Modal>
  )
}

export default DeletePhoneModal
