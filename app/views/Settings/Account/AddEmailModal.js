import React from 'react'
import {
  Mo<PERSON>,
  ScrollView,
  View,
  Text,
  Image,
  TouchableOpacity,
  TextInput,
} from 'react-native'

import {TextReg, Background, HeaderButtons, Button} from '../../../components'

import styles from '../styles'

const AddEmailModal = props => (
  <Modal
    animationType="slide"
    transparent
    visible={props.showAddEmailModal && !props.showPinScreen}
    onRequestClose={() => ({})}>
    <Background />
    <ScrollView
      style={{
        alignSelf: 'stretch',
        flexDirection: 'column',
        paddingTop: 20,
      }}
      contentContainerStyle={{
        justifyContent: 'flex-start',
        alignItems: 'center',
      }}>
      <HeaderButtons title={'Add Email Address'} close={props.closeModal} />

      <TextReg style={styles.changeEmailInputTitle}>New Email Address</TextReg>

      <TextInput
        style={styles.changeEmailInput}
        onChangeText={text => props.handleEmailInput(text)}
        value={props.newEmail}
        underlineColorAndroid="transparent"
        blurOnSubmit
        returnKeyType={'next'}
        onSubmitEditing={() => {
          props.sendOutNewEmail()
        }}
        placeholder={''}
        textContentType="password"
        keyboardAppearance="dark"
      />

      <Button
        style={{
          marginBottom: 20,
          opacity: !props.validEmail ? 0.5 : 1,
          alignSelf: 'stretch',
          marginLeft: 30,
          marginRight: 30,
        }}
        disabled={!props.validEmail}
        onPress={props.sendOutNewEmail}
        theme={'secondary'}
        isLoading={props.verifyLoading}>
        NEXT
      </Button>
      {props.error && (
        <View>
          <TextReg style={{color: '#fff'}}>{props.error}</TextReg>
        </View>
      )}
    </ScrollView>
  </Modal>
)

export default AddEmailModal
