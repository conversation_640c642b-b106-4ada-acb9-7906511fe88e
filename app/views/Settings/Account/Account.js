import React, {Component} from 'react'
import {View, TouchableOpacity, Image, ScrollView} from 'react-native'
import {connect} from 'react-redux'
import {screenView} from '../../../store/analytics/analytics.actions'

import {showPinScreen} from '../../../store/auth/auth.actions'
import {allowBackToSettings} from '../../../store/user/user.actions'

import commonStyles from '../../../styles/commonStyles'
import {
  Card,
  TextReg,
  TextBold,
  BackgroundHeader,
  SwitchAccountsModal,
  CreateAccountModal,
} from '../../../components'

import styles from '../styles'

class Account extends Component {
  constructor(props) {
    super(props)
    this.state = {
      switchAccountsModal: false,
      showCreateAccountModal: false,
    }
  }

  componentDidMount() {
    this.props.dispatch(screenView(`Settings - Account`))
  }

  openSettingsItem = item => {
    let navigationLink = null
    switch (item.title) {
      case 'Email Address':
        navigationLink = 'ChangeEmail'
        break
      case 'Phone Number':
        navigationLink = 'PhoneMenu'
        break
      case 'Change Password':
        navigationLink = 'ChangePassword'
        break
      case 'Banking':
        navigationLink = 'Banking'
        break
      case 'Identity Verification':
        navigationLink = 'IdentityVerification'
        break
      case 'Two-Factor Authentication':
        navigationLink = 'TwoFactor'
        break
      case 'Personal Info':
        navigationLink = 'PersonalInfo'
        break
      case 'Business Entity':
        navigationLink = 'BusinessEntity'
        break
      case 'Change PIN':
        this.props.dispatch(showPinScreen(false))
        this.props.dispatch(allowBackToSettings(true))
        navigationLink = false
        break
      case 'Account Deletion':
        navigationLink = 'AccountDeletion'
        break
    }

    if (navigationLink) {
      this.props.navigation.navigate(navigationLink, {type: item.title})
    }
  }

  createAccountPage = () => {
    //this.props.navigation.navigate('CreateAccount')
    this.setState({switchAccountsModal: false, showCreateAccountModal: true})
  }

  render() {
    const AccountListData = [
      {title: 'Email Address', key: '1'},
      {title: 'Phone Number', key: '2'},
      //{ title: 'Identity Verification', key: '3' },
      //{title: 'Banking', key: '4'},
      {title: 'Change Password', key: '5'},
      //{ title: 'Change PIN', key: '6' },
      {title: 'Personal Info', key: '7'},
      {title: 'Two-Factor Authentication', key: '8'},
      {title: 'Account Deletion', key: '9'},
    ]

    let thisAccount = this.props?.user?.accounts?.filter(a => a.ref == this.props.accountRef)[0]

    let isBusiness = thisAccount?.type == 'business'

    if (thisAccount?.productType && thisAccount?.productType != 'exchange') {
      console.log('pushing banking')
      AccountListData.push({title: 'Banking', key: '4'})
    }

    if (isBusiness) {
      AccountListData.push({title: 'Business Entity', key: '10'})
    }

    AccountListData.sort((a, b) => parseInt(a.key) - parseInt(b.key))

    const {idVerifiationStatus} = this.props.user

    let idNotifDot = null
    if (
      idVerifiationStatus === 'retry' ||
      idVerifiationStatus === 'pending' ||
      idVerifiationStatus === 'rejected' ||
      idVerifiationStatus === 'uninitiated'
    ) {
      idNotifDot = (
        <View
          style={{
            backgroundColor: idVerifiationStatus === 'pending' ? '#268bff' : '#f7004e',
            height: 8,
            width: 8,
            borderRadius: 14,
            marginLeft: 10,
            marginTop: 2,
          }}
        />
      )
    }

    let lastItem = AccountListData.length - 1

    const showAccountMenu = AccountListData.map((a, k) => (
      <TouchableOpacity
        key={k}
        style={{alignSelf: 'stretch'}}
        onPress={() => this.openSettingsItem(a)}>
        <View style={k == lastItem ? styles.listItemLast : styles.listItem}>
          <View style={[styles.listItemLeft, {flexDirection: 'row', alignItems: 'center'}]}>
            <TextReg style={styles.listItemText}>{a.title}</TextReg>
            {a.title === 'Identity Verification' && idNotifDot}
          </View>
          <View style={styles.listItemRight}>
            <Image source={require('../../../imgs/rightArrow.png')} style={styles.rightArrowIcon} />
          </View>
        </View>
      </TouchableOpacity>
    ))

    let accountName = this.props?.user?.accounts?.filter(a => a.ref == this.props.accountRef)[0]
      ?.name
    if (!accountName) {
      accountName = `${this.props.user.firstName} ${this.props.user.lastName}`
    }

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader
          title={'Account'}
          style={{height: 290}}
          goBack={() => this.props.navigation.goBack()}
        />

        <ScrollView style={{paddingTop: 18}} contentContainerStyle={{alignItems: 'center'}}>
          <Card
            style={{
              paddingTop: 0,
              paddingBottom: -4,
              paddingLeft: 14,
              paddingRight: 14,
            }}>
            {showAccountMenu}
          </Card>
        </ScrollView>
      </View>
    )
  }
}

const mapStateToProps = state => ({
  user: state.user.user,
  accountRef: state.auth.account.ref,
})

Account.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
})

export default connect(mapStateToProps)(Account)
