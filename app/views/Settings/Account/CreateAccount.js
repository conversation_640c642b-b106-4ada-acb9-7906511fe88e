import React, { Component } from 'react';
import { View, TextInput, Keyboard } from 'react-native';
import { connect } from 'react-redux';

import { updateActiveTabListener, dig } from '../../../util/helpers';
import { screenView } from '../../../store/analytics/analytics.actions';
import { <PERSON><PERSON>, TextReg, BackgroundHeader, Card } from '../../../components';
import commonStyles from '../../../styles/commonStyles';
import styles from '../styles';

class CreateAccount extends Component {
  constructor (props) {
    super(props);
    this.state = {};
  }

  /*
  focusNextField = field => {
    this.inputs[field].focus()
  }
  */

  render () {
    return (
      <View style={commonStyles.tileContainerDark}>
        <BackgroundHeader title={'Create Account'} goBack={this.props.navigation.goBack} />

        <Card style={{ paddingBottom: 20 }} marginTop={14}>
          <TextReg>CREATE ACCOUNT</TextReg>
        </Card>
      </View>
    );
  }
}

CreateAccount.navigationOptions = ({ navigation }) => ({
  title: null,
  header: <View style={{ height: 40, position: 'absolute' }} />,
  headerBackTitle: null
});

const mapStateToProps = (state) => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  account: state.auth.account,
  showPinScreen: state.auth.pinScreen
});

export default connect(mapStateToProps)(CreateAccount);
