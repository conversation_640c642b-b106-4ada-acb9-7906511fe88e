import React, {Component} from 'react';
import {View, TextInput, Keyboard} from 'react-native';
import {connect} from 'react-redux';
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view';

import {updateActiveTabListener, dig} from '../../../util/helpers';
import {screenView} from '../../../store/analytics/analytics.actions';
import {Button, TextReg, BackgroundHeader, Card, SuccessModal} from '../../../components';
import commonStyles from '../../../styles/commonStyles';
import styles from '../styles';

class ChangePassword extends Component {
  constructor(props) {
    super(props);
    this.state = {
      oldPassword: '',
      newPassword: '',
      confirmNewPassword: '',
      twoFactor: '',
      success: false,
      error: false,
      errorMessage: '',
      loading: false,
    };
    this.inputs = {};
  }

  componentDidMount() {
    updateActiveTabListener(this.props.navigation);
    this.props.dispatch(screenView(`Change Password`));
  }

  hideKeyboard = () => {
    Keyboard.dismiss();
  };

  focusNextField = field => {
    this.inputs[field].focus();
  };

  handleSubmit = () => {
    this.setState({error: false, loading: true});

    const lowercase = {
      test: text => /[a-z]+/.test(text),
      message: 'Password requires at least one lowercase letter.',
    };
    const uppercase = {
      test: text => /[A-Z]+/.test(text),
      message: 'Password requires at least one uppercase letter.',
    };
    const number = {
      test: text => /[0-9]+/.test(text),
      message: 'Password requires at least one number.',
    };
    const special = {
      test: text => /[-!$%^&*@#()_+|~`{}\[\]:";'<>?,.\/]/.test(text),
      message: 'Password requires at least one special character.',
    };
    const same = {
      test: text => text === this.state.confirmNewPassword,
      message: 'Passwords must match.',
    };
    const length = {
      test: text => text.length >= 8,
      message: 'Password must be eight characters.',
    };
    const validations = [lowercase, uppercase, number, special, length, same];
    const results = validations.filter(validation => !validation.test(this.state.newPassword));

    if (results.length > 0) {
      this.setState({
        error: true,
        errorMessage: results[0].message,
        loading: false,
      });
      return;
    }

    this.props.AuthService.login(this.props.account.email, this.state.oldPassword)
      .then(user => {
        if (this.props.user.mfaEnabled) {
          return this.props.WebService.verify2FACode({
            code: this.state.twoFactor,
          })
            .then(res => user)
            .catch(err => {
              this.setState({
                error: true,
                errorMessage: 'Incorrect 2FA code.',
                loading: false,
              });
            });
        }
        return user;
      })
      //.then(user => this.props.AuthService.login2FA(user, this.state.twoFactor))
      .then(user => this.props.AuthService.changePassword(user, this.state.oldPassword, this.state.newPassword))
      .then(res => {
        this.setState({success: true, loading: false});
      })
      .catch(err => {
        if (dig(err, 'code') === 'NotAuthorizedException') {
          this.setState({
            error: true,
            errorMessage: 'Old password is incorrect.',
            loading: false,
          });
        } else if (dig(err, 'code') === 'CodeMismatchException') {
          this.setState({
            error: true,
            errorMessage: 'Incorrect 2FA code.',
            loading: false,
          });
        } else {
          this.setState({
            error: true,
            errorMessage: 'An error occured. Please try again.',
            loading: false,
          });
        }
      });
  };

  handleUpdateOldPassword = text => {
    this.setState({
      oldPassword: text,
      error: false,
      errorMessage: '',
    });
  };

  handleUpdatePassword = text => {
    this.setState({newPassword: text, error: false, errorMessage: ''});
  };

  handleUpdateConfirmPassword = text => {
    this.setState({confirmNewPassword: text, error: false, errorMessage: ''});
  };

  handleUpdateTwoFactor = text => {
    this.setState({twoFactor: text, error: false, errorMessage: ''});
  };

  closeModal = () => {
    this.setState({
      success: false,
      oldPassword: '',
      newPassword: '',
      confirmNewPassword: '',
      twoFactor: '',
    });
    this.hideKeyboard();
    this.props.navigation.goBack();
  };

  focus2fa = () => {
    if (this.props.user.mfaEnabled) {
      this.focusNextField('twoFactor');
    } else {
      this.handleSubmit();
    }
  };

  render() {
    let canSubmit = this.state.oldPassword && this.state.newPassword && this.state.confirmNewPassword === this.state.newPassword;

    if (this.props.user.mfaEnabled) {
      canSubmit = canSubmit && this.state.twoFactor.length === 6;
    }

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Change Password'} goBack={this.props.navigation.goBack} />
        <KeyboardAwareScrollView style={styles.scrollViewContainer} contentContainerStyle={{alignSelf: 'stretch', alignItems: 'center'}}>
          <Card style={{paddingBottom: 20}} marginTop={14}>
            <View style={styles.changePasswordBox}>
              <TextReg style={styles.changePasswordInputTitle}>Old Password</TextReg>
              <TextInput
                style={styles.changePasswordInput}
                onChangeText={text => this.handleUpdateOldPassword(text)}
                ref={input => (this.inputs.oldPassword = input)}
                value={this.state.oldPassword}
                underlineColorAndroid="transparent"
                blurOnSubmit
                secureTextEntry
                returnKeyType={'next'}
                onSubmitEditing={() => this.focusNextField('newPassword')}
                placeholder={''}
                textContentType="password"
                keyboardAppearance="dark"
              />
              <TextReg style={styles.changePasswordInputTitle}>New Password</TextReg>
              <TextInput
                style={styles.changePasswordInput}
                onChangeText={text => this.handleUpdatePassword(text)}
                ref={input => (this.inputs.newPassword = input)}
                value={this.state.newPassword}
                underlineColorAndroid="transparent"
                blurOnSubmit
                secureTextEntry
                returnKeyType={'next'}
                onSubmitEditing={() => this.focusNextField('confirmNewPassword')}
                placeholder={''}
                textContentType="password"
                keyboardAppearance="dark"
              />
              <TextReg style={styles.changePasswordInputTitle}>Confirm New Password</TextReg>
              <TextInput
                style={styles.changePasswordInput}
                onChangeText={text => this.handleUpdateConfirmPassword(text)}
                ref={input => (this.inputs.confirmNewPassword = input)}
                value={this.state.confirmNewPassword}
                underlineColorAndroid="transparent"
                blurOnSubmit
                secureTextEntry
                returnKeyType={'next'}
                onSubmitEditing={() => this.focus2fa()}
                placeholder={''}
                textContentType="password"
                keyboardAppearance="dark"
              />
              {this.props.user.mfaEnabled && (
                <View>
                  <TextReg style={styles.changePasswordInputTitle}>Two-Factor</TextReg>
                  <TextInput
                    style={styles.changePasswordInput}
                    onChangeText={text => this.handleUpdateTwoFactor(text)}
                    ref={input => (this.inputs.twoFactor = input)}
                    value={this.state.twoFactor}
                    underlineColorAndroid="transparent"
                    blurOnSubmit
                    returnKeyType={'done'}
                    onSubmitEditing={() => this.hideKeyboard()}
                    placeholder={''}
                    keyboardType={'numeric'}
                    keyboardAppearance="dark"
                  />
                </View>
              )}
              <Button
                style={{alignSelf: 'stretch', marginTop: 10, marginBottom: 6}}
                disabled={!canSubmit}
                onPress={this.handleSubmit}
                isLoading={this.state.loading}>
                SUBMIT
              </Button>
              {this.state.error && (
                <View style={styles.changePasswordErrorBox}>
                  <TextReg style={styles.changePasswordErrorText}>{this.state.errorMessage}</TextReg>
                </View>
              )}
              <SuccessModal
                showModal={this.state.success}
                showPinScreen={this.props.showPinScreen}
                title={'Password Changed'}
                closeModal={() => this.closeModal}
              />
            </View>
          </Card>
        </KeyboardAwareScrollView>
      </View>
    );
  }
}

ChangePassword.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
});

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  AuthService: state.auth.AuthService,
  user: state.user.user,
  account: state.auth.account,
  showPinScreen: state.auth.pinScreen,
});

export default connect(mapStateToProps)(ChangePassword);
