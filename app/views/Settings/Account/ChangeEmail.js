import React, {Component} from 'react'
import {View, ScrollView, RefreshControl, Keyboard, TouchableOpacity} from 'react-native'
import {connect} from 'react-redux'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'

import {updateUser, updateAccount} from '../../../store/user/user.actions'
import {updateActiveTabListener} from '../../../util/helpers'
import {screenView} from '../../../store/analytics/analytics.actions'
import {TextReg, BackgroundHeader, Card, TextBold} from '../../../components'
import AddEmailModal from './AddEmailModal'
import VerifyEmailModal from './VerifyEmailModal'
import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'

class ChangeEmail extends Component {
  constructor(props) {
    super(props)
    this.state = {
      newEmail: '',
      twoFactor: '',
      success: false,
      error: false,
      errorMessage: '',
      loading: false,
      showCodeScreen: false,
      showAddEmailModal: false,
      validEmail: false,
      refreshing: false,
      code: '',
      textInputActive: false,
      emailIdChosen: '',
      verifyLoading: false,
      showPrimary: false,
    }
    this.inputs = {}
  }

  componentDidMount() {
    updateActiveTabListener(this.props.navigation)
    this.props.dispatch(screenView(`Change Email`))
  }

  hideKeyboard = () => {
    Keyboard.dismiss()
  }

  handleEmailInput = text => {
    this.setState({newEmail: text, error: false, errorMessage: ''})
    console.log('is valid handle email', this.isValidEmail(text))
    if (this.isValidEmail(text)) {
      this.setState({validEmail: true})
    } else {
      this.setState({validEmail: false})
    }
  }

  handleUpdateTwoFactor = text => {
    this.setState({twoFactor: text, error: false, errorMessage: ''})
  }

  sendOutNewEmail = () => {
    let lowerEmail = this.state.newEmail?.toLowerCase()
    console.log('lowerEmail', lowerEmail)
    this.setState({verifyLoading: true})
    this.props.WebService.addEmail({email: lowerEmail})
      .then(res => {
        console.log('WebService.addEmail res', res)
        //refresh user info
        this.getUser()
        this.toggleAddEmail()
      })
      .catch(err => {
        console.log('WebService.addEmail err', err)
        this.setState({verifyLoading: false, error: err.data.body.error})
      })
    //then on success - show 2fa input
    //this.setState({ show2FA: true })
  }

  getUser = () => {
    this.setState({refreshing: true})
    this.props.WebService.getSaltUser()
      .then(res => {
        console.log('getSaltUser - post email res', res)
        this.props.dispatch(updateUser(res.data))
        this.getAccounts()
      })
      .catch(err => {
        this.setState({connectedModalVisable: true, refreshing: false})
        if (err.response && err.response.status === 401) {
          throw err
        }
      })
  }

  getAccounts = () => {
    this.props.WebService.getSaltAccount().then(res => {
      console.log('get accounts res- loan screen', res)
      this.props.dispatch(updateAccount(res.data))
      this.setState({refreshing: false})
    })
  }

  isValidEmail = email => {
    const re = /^(([^<>()\[\]\.,;:\s@\"]+(\.[^<>()\[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i
    return re.test(email)
  }

  closeModal = () => {
    this.setState({
      success: false,
      oldPassword: '',
      newPassword: '',
      confirmNewPassword: '',
      twoFactor: '',
    })
    this.hideKeyboard()
  }

  toggleAddEmail = () => {
    const newToggleEmail = !this.state.showAddEmailModal
    this.setState({showAddEmailModal: newToggleEmail})
    if (!newToggleEmail) {
      this.setState({
        emailIdChosen: '',
        code: '',
        verifyLoading: false,
        showPrimary: false,
        newEmail: '',
        error: false,
      })
      this.hideKeyboard()
    }
  }

  toggleVerifyEmail = () => {
    const newShowCodeScreen = !this.state.showCodeScreen
    this.setState({showCodeScreen: newShowCodeScreen})
    if (!newShowCodeScreen) {
      this.setState({
        emailIdChosen: '',
        code: '',
        verifyLoading: false,
        showPrimary: false,
        newEmail: '',
        error: false,
      })
      this.hideKeyboard()
    }
  }

  verifyEmail = a => {
    this.toggleVerifyEmail()
    this.setState({emailIdChosen: a.id})
  }

  sendVerifyEmail = () => {
    if (this.state.showPrimary) {
      this.setState({verifyLoading: true})
      this.props.WebService.makeEmailPrimary(this.state.emailIdChosen, {
        twoFactorCode: this.state.code,
      })
        .then(res => {
          console.log('makeEmailPrimary res', res)
          this.getUser()
          this.toggleVerifyEmail()
        })
        .catch(err => {
          console.log('makeEmailPrimary err', err)
          const error = err.data.body.error
          this.setState({verifyLoading: false, error})
        })
    } else {
      const data = {emailId: this.state.emailIdChosen, pin: this.state.code}
      this.setState({verifyLoading: true})
      this.props.WebService.verifyEmail(data)
        .then(res => {
          console.log('WebService.verifyEmail res', res)
          this.getUser()
          this.toggleVerifyEmail()
        })
        .catch(err => {
          console.log('WebService.verifyEmail err', err)
          let error = err.data.body.error
          if (error === 'Invalid pin') {
            error = 'Invalid Code'
          }
          this.setState({verifyLoading: false, error})
        })
    }
  }

  deleteEmail = a => {
    this.props.WebService.deleteEmail(a.id)
      .then(res => {
        console.log('deleteEmail res', res)
        this.getUser()
      })
      .catch(err => {
        console.log('deleteEmail err', err)
      })
  }

  handleVerificationCode = (text = '') => {
    if (text.length > 6) {
      //this.setState({ readyToCont: false })
      return
    }
    this.setState({code: text}, () => {
      if (text.length === 6) {
        this.sendVerifyEmail()
      }
    })
  }

  focusTextInput = () => {
    this.setState({textInputActive: true})
  }

  blurTextInput = () => {
    this.setState({textInputActive: false})
  }

  showBlock = num => {
    if (this.state.code.length > num) {
      return this.state.code[num]
    }
    return ''
  }

  makePrimary = a => {
    this.setState({emailIdChosen: a.id, showPrimary: true, error: false}, () => {
      this.toggleVerifyEmail()
    })
  }

  render() {
    let emailList = this.props.user?.emails?.sort((a, b) => (a.isPrimary ? -1 : 1))

    emailList = emailList?.map((a, k) => (
      <Card key={k} marginTop={6}>
        <View
          style={{
            alignSelf: 'stretch',
            height: a.isPrimary ? 50 : 80,
            backgrounColor: 'white',
            borderRadius: 14,
            justifyContent: 'center',
            padding: 8,
          }}>
          <View
            style={{
              alignSelf: 'stretch',
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            <TextReg
              style={{
                color: '#e6e6e6',
                fontSize: 15,
              }}>{`${a.address}`}</TextReg>
            <View>
              {a.isPrimary && <TextBold style={{color: '#34e89e', fontSize: 12, marginTop: 4}}>PRIMARY</TextBold>}
              {!a.isVerified && <TextBold style={{color: '#fb1f63', fontSize: 12, marginTop: 4}}>UNVERIFIED</TextBold>}
            </View>
          </View>
          <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
            <View>
              {!a.isPrimary && a.isVerified && (
                <View style={{marginTop: 17}}>
                  <TouchableOpacity
                    onPress={() => {
                      this.makePrimary(a)
                    }}>
                    <TextBold style={{color: '#00FFBD', fontSize: 17}}>MAKE PRIMARY</TextBold>
                  </TouchableOpacity>
                </View>
              )}
              {!a.isPrimary && !a.isVerified && (
                <View style={{marginTop: 18}}>
                  <TouchableOpacity
                    onPress={() => {
                      this.verifyEmail(a)
                    }}>
                    <TextBold style={{color: '#00FFBD', fontSize: 17}}>VERIFY</TextBold>
                  </TouchableOpacity>
                </View>
              )}
            </View>
            <View>
              {!a.isPrimary && (
                <View style={{marginTop: 18}}>
                  <TouchableOpacity
                    onPress={() => {
                      this.deleteEmail(a)
                    }}>
                    <TextBold style={{color: '#00FFBD', fontSize: 17}}>REMOVE</TextBold>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>
        </View>
      </Card>
    ))

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Change Email'} goBack={this.props.navigation.goBack} />
        <KeyboardAwareScrollView
          style={styles.scrollViewContainer}
          contentContainerStyle={{
            alignSelf: 'stretch',
            alignItems: 'center',
            paddingTop: 20,
          }}>
          <ScrollView
            style={styles.swiperContainer}
            contentContainerStyle={{
              alignItems: 'center',
              paddingBottom: 200,
            }}
            refreshControl={
              <RefreshControl refreshing={this.state.refreshing} onRefresh={this.getUser} colors={['#28283D']} tintColor={'#fff'} />
            }>
            {emailList}
            {emailList?.length < 2 ? (
              <View style={{alignSelf: 'stretch', marginLeft: 32, marginTop: 10}}>
                <TouchableOpacity onPress={() => this.toggleAddEmail()}>
                  <TextBold style={{color: '#00FFBD', fontSize: 18}}>ADD EMAIL ADDRESS</TextBold>
                </TouchableOpacity>
              </View>
            ) : (
              <TextReg
                style={{
                  alignSelf: 'flex-start',
                  fontSize: 16,
                  marginTop: 4,
                  marginLeft: 22,
                }}>
                2 Address Maximum
              </TextReg>
            )}
          </ScrollView>
        </KeyboardAwareScrollView>
        <AddEmailModal
          validEmail={this.state.validEmail}
          sendOutNewEmail={this.sendOutNewEmail}
          newEmail={this.state.newEmail}
          handleEmailInput={this.handleEmailInput}
          closeModal={this.toggleAddEmail}
          showAddEmailModal={this.state.showAddEmailModal}
          showPinScreen={this.props.showPinScreen}
          error={this.state.error}
          verifyLoading={this.state.verifyLoading}
        />
        <VerifyEmailModal
          closeModal={this.toggleVerifyEmail}
          showCodeScreen={this.state.showCodeScreen}
          showPinScreen={this.props.showPinScreen}
          code={this.state.code}
          handleVerificationCode={this.handleVerificationCode}
          inputs={this.inputs}
          textInputActive={this.state.textInputActive}
          focusTextInput={this.focusTextInput}
          blurTextInput={this.blurTextInput}
          showBlock={this.showBlock}
          sendVerifyEmail={this.sendVerifyEmail}
          verifyLoading={this.state.verifyLoading}
          showPrimary={this.state.showPrimary}
          error={this.state.error}
        />
      </View>
    )
  }
}

ChangeEmail.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  account: state.auth.account,
  showPinScreen: state.auth.pinScreen,
})

export default connect(mapStateToProps)(ChangeEmail)
