import React, {Component} from 'react'
import {
  View,
  TextInput,
  Keyboard,
  ScrollView,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native'
import {connect} from 'react-redux'

import {
  Button,
  TextReg,
  TextBold,
  BackgroundHeader,
  Card,
  LocationSelect,
} from '../../../components'

import iso3166 from 'iso-3166-2'
import moment from 'moment'
import countryCodes from '../../../util/countryCodes'
import * as ImagePicker from 'react-native-image-picker'
import {increaseRefreshDataCount, updateAccount} from '../../../store/user/user.actions'

import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'

class BusinessEntity extends Component {
  constructor(props) {
    super(props)
    let pickableCountryCodes = countryCodes().map(a => a.name)
    pickableCountryCodes = ['United States'].concat(pickableCountryCodes)
    const businessData =
      this.props.user?.accounts?.filter(a => a.ref == this.props.accountRef)[0]?.businessProfile ||
      null

    const businessAddressId = businessData?.address?.id

    let involvedServices = false
    if (businessData?.isEntityArmsAmmunition) {
      involvedServices = 'isEntityArmsAmmunition'
    }
    if (businessData?.isEntityCurrencyExchange) {
      involvedServices = 'isEntityCurrencyExchange'
    }
    if (businessData?.isEntityMarijuana) {
      involvedServices = 'isEntityMarijuana'
    }
    if (businessData?.isEntityMoneyLending) {
      involvedServices = 'isEntityMoneyLending'
    }
    if (businessData?.isEntityNarcotics) {
      involvedServices = 'isEntityNarcotics'
    }
    if (businessData?.isEntityPreciousMetals) {
      involvedServices = 'isEntityPreciousMetals'
    }

    const documents =
      businessData?.address?.documents?.filter(a => a.type == 'other_proof_of_address') || []

    const verificationDocs =
      businessData?.documents.filter(a => a.type == 'verification_of_business') || []

    let dateFormation = ''

    if (businessData?.formationDate) {
      dateFormation = moment.utc(businessData?.formationDate).format('MM/DD/YYYY') || ''
    }

    this.state = {
      legalName: businessData?.legalName || '',
      address1: businessData?.address.street1 || '',
      address2: businessData?.address.street2 || '',
      city: businessData?.address.city || '',
      zipCode: businessData?.address.postalCode || '',
      taxID: businessData?.taxIdNumber || '',
      entityType: businessData?.entityType || '',
      listDBA: businessData?.listDBA || '',
      entityTypeList: ['LLC', 'Corporation', 'Trust', 'Sole Proprietor', 'Non-Profit'],
      countryCodes: pickableCountryCodes,
      countrySubs: [],
      selectedCountry: businessData?.address.countryCode || null,
      countryCode: businessData?.address.countryCode || null,
      pickableCountrySubs: [],
      selectedProvince: businessData?.address.province || null,
      regionCode: businessData?.address.province || null,
      involvedServices,
      associatedGroup: false,
      felonyCrime: businessData?.hasShareholderCommittedFelony || false,
      felonyCrimeInput: businessData?.felonySpecification || '',
      bankruptcy: businessData?.hasBankruptcy || false,
      bankruptcyInput: businessData?.bankruptcySpecification || '',
      documents,
      verificationDocs,
      dateFormation,
      businessAddressId,
      businessID: businessData?.id || '',

      loading: false,
      dateNumbersError: false,

      jurisdictionFormation: businessData?.formationJurisdiction || '',
      beneficialOwners: businessData?.beneficialOwners?.[0] || '',
      industrySector: businessData?.industry || '',
      grossAnnual: businessData?.isRevenueExceed || false,
      useOfFunds: businessData?.useOfFunds || null,

      countryCodesFormation: pickableCountryCodes,
      countrySubsFormation: [],
      selectedCountryFormation: businessData?.operationsJurisdiction?.countryCode || null,
      countryCodeFormation: businessData?.operationsJurisdiction?.countryCode || null,
      pickableCountrySubsFormation: [],
      selectedProvinceFormation: businessData?.operationsJurisdiction?.province || null,
      regionCodeFormation: businessData?.operationsJurisdiction?.province || null,
    }
    this.inputs = {}
  }

  updateField = (text, type) => {
    const state = this.state
    if (type == 'dateFormation') {
      const textBefore = text
      text = this.formatDateText(text, state[type])
      if (text.length != 0 && !text) {
        state.dateNumbersError = true
        text = textBefore
      } else {
        state.dateNumbersError = false
      }
    }
    state[type] = text
    this.setState(state)
  }

  formatDateText = (text, oldText) => {
    if (/[a-zA-Z]/.test(text)) {
      return false
    } else if (oldText && text.length < oldText.length) {
      return text
    } else if (
      oldText &&
      oldText.length == 2 &&
      text.length > oldText.length &&
      text.substring(2) != '/'
    ) {
      return text.substring(0, 2) + '/' + text.substring(2)
    } else if (
      oldText &&
      oldText.length == 5 &&
      text.length > oldText.length &&
      text.substring(5) != '/'
    ) {
      return text.substring(0, 5) + '/' + text.substring(5)
    } else if (text.length == 2 || text.length == 5) {
      return text + '/'
    } else if (text.length == 10) {
      const one = text.substring(0, 2)
      const two = text.substring(3, 5)
      const three = text.substring(6, 10)
      const testOne = /^\d+$/.test(one)
      const testTwo = /^\d+$/.test(two)
      const testThree = /^\d+$/.test(three)
      const firstSlash = text.substring(2, 3)
      const secondSlash = text.substring(5, 6)
      const year = new Date().getFullYear()
      if (
        !testOne ||
        !testTwo ||
        !testThree ||
        firstSlash != '/' ||
        secondSlash != '/' ||
        Number(one) > 12 ||
        Number(two) > 31 ||
        Number(three) > year
      ) {
        return false
      }
    } else if (text.length > 10) {
      return false
    }
    return text
  }

  goBack = () => {
    if (this.props.navigation.route?.flow === 'loanChecklist') {
      this.props.navigation.popToTop()
      this.props.navigation.navigate('Home')
    } else {
      this.props.navigation.goBack()
    }
  }

  onSelect = selection => {
    const selectedCountry = this.state.countryCodes.filter((a, k) => k == selection)[0]
    const countryCode = countryCodes().filter(a => a.name == selectedCountry)[0].code
    const isoCountry = iso3166.country(countryCode)
    let pickableCountrySubs = []
    const subArr = Object.values(isoCountry.sub)
    pickableCountrySubs = subArr.map(a => a.name)
    pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
      if (a < b) return -1
      if (b < a) return 1
      return 0
    })
    this.setState({
      selectedCountry,
      countryCode,
      pickableCountrySubs,
      subArr,
    })
  }

  onProvinceSelect = province => {
    const selectedProvince = this.state.pickableCountrySubs.filter((a, k) => k == province)[0]

    const subData = iso3166.subdivision(this.state.countryCode, selectedProvince)
    const regionCode = subData.regionCode
    this.setState({selectedProvince, regionCode, highlightProvince: false})
  }

  onEntitySelect = selection => {
    const entityType = this.state.entityTypeList[selection]
    this.setState({entityType})
  }

  questionRes = (type, res) => {
    const state = this.state
    state[type] = res
    this.setState(state)
  }

  onSelectFormation = selection => {
    const selectedCountryFormation = this.state.countryCodes.filter((a, k) => k == selection)[0]
    const countryCodeFormation = countryCodes().filter(a => a.name == selectedCountryFormation)[0]
      .code
    const isoCountry = iso3166.country(countryCodeFormation)
    let pickableCountrySubsFormation = []
    const subArrFormation = Object.values(isoCountry.sub)
    pickableCountrySubsFormation = subArrFormation.map(a => a.name)
    pickableCountrySubsFormation = pickableCountrySubsFormation.sort((a, b) => {
      if (a < b) return -1
      if (b < a) return 1
      return 0
    })
    this.setState({
      selectedCountryFormation,
      countryCodeFormation,
      pickableCountrySubsFormation,
      subArrFormation,
    })
  }

  onProvinceSelectFormation = province => {
    const selectedProvinceFormation = this.state.pickableCountrySubsFormation.filter(
      (a, k) => k == province,
    )[0]

    const subData = iso3166.subdivision(this.state.countryCodeFormation, selectedProvinceFormation)
    const regionCodeFormation = subData.regionCode
    this.setState({selectedProvinceFormation, regionCodeFormation})
  }

  openImageSelect = (verification = false) => {
    const options = {
      title: 'Select Document',
      maxWidth: 2200,
      maxHeight: 2200,
      quality: 1.0,
      storageOptions: {
        skipBackup: true,
        path: 'images',
        cameraRoll: true,
        waitUntilSaved: true,
      },
      //takePhotoButtonTitle: null,
      noData: true,
      mediaType: 'photo',
    }

    this.setState({
      refreshingUpload: true,
      imageError: false,
    })

    ImagePicker.launchImageLibrary(options, async response => {
      if (response.didCancel) {
        this.setState({refreshingUpload: false})
        return
      } else if (response.error) {
        this.setState({refreshingUpload: false})
        return
      } else if (response.customButton) {
        console.log('User tapped custom button: ', response.customButton)
      } else {
        response = response.assets[0]
        const photo = {uri: response.uri}
        const formdata = new FormData()

        console.log('image upload response', response)

        const validImage = this.validateImage(response.type, response.fileSize)
        if (validImage === 'type') {
          this.setState({refreshingUpload: false, imageError: 'type'})
          return
        } else if (validImage === 'size') {
          this.setState({refreshingUpload: false, imageError: 'size'})
          return
        }

        let path = response.uri
        if (Platform.OS === 'ios') {
          path = '~' + path.substring(path.indexOf('/Documents'))
        }
        if (!response.fileName) response.fileName = path.split('/').pop()

        formdata.append('file', [
          {
            uri: Platform.OS === 'android' ? photo.uri : photo.uri.replace('file://', ''),
            name: response.fileName || 'testName.jpg',
            type: response.type,
            size: response.fileSize,
          },
        ])
        formdata.append('name', response.fileName)
        formdata.append('key', 'true')

        this.props.WebService.uploadDocument(
          verification ? this.state.businessID : this.state.businessAddressId,
          verification ? 'business_profile' : 'address',
          verification ? 'verification_of_business' : 'other_proof_of_address',
          photo.uri.replace('file://', ''),
          response.fileName,
        )
          .then(res => {
            console.log('upload doc res', res)
            const documents = verification ? this.state.verificationDocs : this.state.documents
            documents.push({
              name: response.fileName,
            })

            verification
              ? this.setState({
                  refreshingUpload: false,
                  verificationDocs: documents,
                })
              : this.setState({
                  refreshingUpload: false,
                  documents,
                })

            //refresh user data - so docs show up on back and forth
            this.getAccounts()
          })
          .catch(err => {
            console.log('upload doc err', err)
            this.setState({refreshingUpload: false})
          })
      }
    })
  }

  validateImage = (type, size) => {
    const fileTypes = [
      'application/pdf',
      'image/jpg',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/tif',
      'image/tiff',
    ]
    if (!fileTypes.includes(type)) {
      return 'type'
    }
    if (size / 1000000 > 50) {
      return 'size'
    }
    return true
  }

  getAccounts = () => {
    this.props.WebService.getSaltAccount().then(res => {
      this.props.dispatch(updateAccount(res.data))
    })
  }

  submit = () => {
    const businessPayload = {
      bankruptcySpecification: this.state.bankruptcyInput,
      hasBankruptcy: this.state.bankruptcy,
      beneficialOwners: this.state.beneficialOwners,
      entityType: this.state.entityType,
      felonySpecification: this.state.felonyCrimeInput,
      formationDate: this.state.dateFormation || null,
      formationJurisdiction: this.state.jurisdictionFormation,
      hasShareholderCommittedFelony: this.state.felonyCrime,
      industry: this.state.industrySector,
      isEntityAssociation: this.state.associatedGroup,
      isEntityInvolved: this.state.involvedServices,
      isRevenueExceed: this.state.grossAnnual,
      legalName: this.state.legalName,
      listDBA: this.state.listDBA,
      operationsJurisdictionCountry: this.state.countryCodeFormation,
      operationsJurisdictionState: this.state.regionCodeFormation,
      taxIdNumber: this.state.taxID,
      useOfFunds: this.state.useOfFunds,
    }

    console.log('businessPayload', businessPayload)

    this.props.WebService.patchBusiness(businessPayload)
      .then(res => {
        console.log('patchBusiness res', res)
        this.setState({loading: false})
        this.props.dispatch(increaseRefreshDataCount())
        this.backToLoanChecklist()
      })
      .catch(err => {
        console.log('patchBusiness err', err)
        this.setState({loading: false})
      })

    const businessAddress = {
      city: this.state.city,
      countryCode: this.state.countryCode,
      document: null,
      postalCode: this.state.zipCode,
      province: this.state.regionCode,
      street1: this.state.address1,
      street2: this.state.address2,
    }

    this.props.WebService.patchAddress(this.state.businessAddressId, businessAddress)
      .then(res => {
        console.log('patchAddress res', res)
      })
      .catch(err => {
        console.log('patch address err', err)
      })

    /*
    //patchAddress
    const addressId = this.props.user.address.id
    const addressPayload = {
      city: this.state.city,
      countryCode: this.state.countryCode,
      postalCode: this.state.zipCode,
      province: this.state.regionCode,
      street1: this.state.address1,
      street2: this.state.address2,
    }
    this.props.WebService.patchAddress(addressId, addressPayload)
      .then(res => {
        console.log('patchAddress res', res)
      })
      .catch(err => {
        console.log('patchAddress err', err)
      })

      */
  }

  backToLoanChecklist = () => {
    this.props.navigation.popToTop()
    this.props.navigation.navigate('Home')
  }

  render() {
    console.log(' business entity', this.props, this.state)
    const showDocuments = this.state.documents.map((a, k) => (
      <View key={k}>
        <View style={styles.banksUploadDocumentBox}>
          <TextReg>{a.name}</TextReg>
        </View>
      </View>
    ))

    const showVerificationDocuments = this.state.verificationDocs.map((a, k) => (
      <View key={k}>
        <View style={styles.banksUploadDocumentBox}>
          <TextReg>{a.name}</TextReg>
        </View>
      </View>
    ))

    let ifYesError = false
    if (this.state.bankruptcy && this.state.bankruptcyInput == '') {
      ifYesError = true
    }
    if (this.state.felonyCrime && this.state.felonyCrimeInput == '') {
      ifYesError = true
    }

    let submitDate = true
    if (this.state.dateNumbersError) {
      submitDate = false
    }
    if (this.state.dateFormation.length > 0 && this.state.dateFormation.length != 10) {
      submitDate = false
    }

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Business Entity Info'} goBack={this.goBack} />
        <ScrollView
          style={styles.scrollViewContainer}
          contentContainerStyle={{alignSelf: 'stretch', alignItems: 'center'}}>
          <Card style={{paddingBottom: 20}} marginTop={14}>
            <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
              <TextBold style={{fontSize: 18, marginBottom: 20}}>Business Entity Info</TextBold>
              <View style={{width: 300}}>
                <TextReg style={styles.personalInfoInputTitle}>Legal Name</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'legalName')}
                  ref={input => (this.inputs.legalName = input)}
                  value={this.state.legalName}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  autoCorrect={false}
                  onSubmitEditing={() => this.inputs.entityType.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.personalInfoInputTitle}>Entity Type</TextReg>
                <LocationSelect
                  options={['LLC', 'Corporation', 'Trust', 'Sole Proprietor', 'Non-Profit']}
                  onSelect={this.onEntitySelect}
                  placeholder={this.state.entityType ? this.state.entityType : ''}
                />
                <TextReg style={styles.personalInfoInputTitle}>Industry Sector</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'industrySector')}
                  ref={input => (this.inputs.industrySector = input)}
                  value={this.state.industrySector}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.inputs.listDBA.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.personalInfoInputTitle}>List DBA</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'listDBA')}
                  ref={input => (this.inputs.listDBA = input)}
                  value={this.state.listDBA}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.inputs.address1.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.personalInfoInputTitle}>Residential Address Line 1</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'address1')}
                  ref={input => (this.inputs.address1 = input)}
                  value={this.state.address1}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.inputs.address2.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.personalInfoInputTitle}>Residential Address Line 2</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'address2')}
                  ref={input => (this.inputs.address2 = input)}
                  value={this.state.address2}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.inputs.country.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.personalInfoInputTitle}>Country</TextReg>
                <LocationSelect
                  options={this.state.countryCodes}
                  onSelect={this.onSelect}
                  placeholder={this.state.selectedCountry ? this.state.selectedCountry : ''}
                />
                <TextReg style={styles.personalInfoInputTitle}>Province</TextReg>
                <LocationSelect
                  onSelect={this.onProvinceSelect}
                  highlight={this.state.highlightProvince}
                  options={this.state.pickableCountrySubs}
                  placeholder={this.state.selectedProvince ? this.state.selectedProvince : ''}
                />
                <TextReg style={[styles.personalInfoInputTitle, {marginTop: 4}]}>City</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'city')}
                  ref={input => (this.inputs.city = input)}
                  value={this.state.city}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.inputs.zipCode.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.personalInfoInputTitle}>Zip Code</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'zipCode')}
                  ref={input => (this.inputs.zipCode = input)}
                  value={this.state.zipCode}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.inputs.jurisdictionFormation.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.personalInfoInputTitle}>Jurisdiction of Formation</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'jurisdictionFormation')}
                  ref={input => (this.inputs.jurisdictionFormation = input)}
                  value={this.state.jurisdictionFormation}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.inputs.dateFormation.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.personalInfoInputTitle}>
                  Date of Formation (mm/dd/yyyy)
                </TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'dateFormation')}
                  ref={input => (this.inputs.dateFormation = input)}
                  value={this.state.dateFormation}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.inputs.taxID.focus()}
                  keyboardAppearance="dark"
                />

                {this.state.dateNumbersError && (
                  <TextReg
                    style={[
                      styles.personalInfoInputTitle,
                      {
                        marginTop: -8,
                        marginBottom: 10,
                        fontSize: 16,
                        color: '#E5705A',
                      },
                    ]}>
                    Incorrect Date Format
                  </TextReg>
                )}

                <TextReg style={styles.personalInfoInputTitle}>Govt. Tax ID Number or EIN</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'taxID')}
                  ref={input => (this.inputs.taxID = input)}
                  value={this.state.taxID}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.inputs.beneficialOwners.focus()}
                  keyboardAppearance="dark"
                />

                <View
                  style={{
                    borderBottomWidth: 1,
                    borderColor: '#CCC',
                    marginLeft: 10,
                    marginRight: 10,
                    marginBottom: 16,
                    marginTop: 10,
                  }}
                />

                <View style={{width: 300}}>
                  <TextBold style={{fontSize: 17, marginBottom: 20, marginTop: 10}}>
                    Is the entity involved in any of the following services?
                  </TextBold>
                  <TouchableOpacity
                    onPress={() =>
                      this.questionRes('involvedServices', 'isEntityCurrencyExchange')
                    }>
                    <View
                      style={[
                        styles.personalMultipleQuestionButton,
                        {
                          backgroundColor:
                            this.state.involvedServices == 'isEntityCurrencyExchange'
                              ? '#00FFBD'
                              : '#3D3D50',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color:
                            this.state.involvedServices == 'isEntityCurrencyExchange'
                              ? '#000'
                              : '#FFF',
                        }}>
                        Currency Exchange/Money Changer Services
                      </TextReg>
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => this.questionRes('involvedServices', 'isEntityMoneyLending')}>
                    <View
                      style={[
                        styles.personalMultipleQuestionButton,
                        {
                          backgroundColor:
                            this.state.involvedServices == 'isEntityMoneyLending'
                              ? '#00FFBD'
                              : '#3D3D50',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color:
                            this.state.involvedServices == 'isEntityMoneyLending' ? '#000' : '#FFF',
                        }}>
                        Money Lending/Pawning
                      </TextReg>
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => this.questionRes('involvedServices', 'isEntityPreciousMetals')}>
                    <View
                      style={[
                        styles.personalMultipleQuestionButton,
                        {
                          backgroundColor:
                            this.state.involvedServices == 'isEntityPreciousMetals'
                              ? '#00FFBD'
                              : '#3D3D50',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color:
                            this.state.involvedServices == 'isEntityPreciousMetals'
                              ? '#000'
                              : '#FFF',
                        }}>
                        Precious Metals, Stones, or Jewelry
                      </TextReg>
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => this.questionRes('involvedServices', 'isEntityArmsAmmunition')}>
                    <View
                      style={[
                        styles.personalMultipleQuestionButton,
                        {
                          backgroundColor:
                            this.state.involvedServices == 'isEntityArmsAmmunition'
                              ? '#00FFBD'
                              : '#3D3D50',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color:
                            this.state.involvedServices == 'isEntityArmsAmmunition'
                              ? '#000'
                              : '#FFF',
                        }}>
                        Arms & Ammunition
                      </TextReg>
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => this.questionRes('involvedServices', 'isEntityNarcotics')}>
                    <View
                      style={[
                        styles.personalMultipleQuestionButton,
                        {
                          backgroundColor:
                            this.state.involvedServices == 'isEntityNarcotics'
                              ? '#00FFBD'
                              : '#3D3D50',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color:
                            this.state.involvedServices == 'isEntityNarcotics' ? '#000' : '#FFF',
                        }}>
                        Narcotics/Pharmaceuticals
                      </TextReg>
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => this.questionRes('involvedServices', 'isEntityMarijuana')}>
                    <View
                      style={[
                        styles.personalMultipleQuestionButton,
                        {
                          backgroundColor:
                            this.state.involvedServices == 'isEntityMarijuana'
                              ? '#00FFBD'
                              : '#3D3D50',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color:
                            this.state.involvedServices == 'isEntityMarijuana' ? '#000' : '#FFF',
                        }}>
                        Marijuana-Related Business
                      </TextReg>
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity onPress={() => this.questionRes('involvedServices', 'none')}>
                    <View
                      style={[
                        styles.personalMultipleQuestionButton,
                        {
                          backgroundColor:
                            this.state.involvedServices == 'none' ? '#00FFBD' : '#3D3D50',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color: this.state.involvedServices == 'none' ? '#000' : '#FFF',
                        }}>
                        None of the above
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                </View>

                <View
                  style={{
                    height: 2,
                    width: 300,
                    borderBottomWidth: 1,
                    borderColor: '#cbcbcb',
                    marginTop: 30,
                    marginBottom: 30,
                  }}
                />

                <TextBold style={{fontSize: 17, marginBottom: 10}}>
                  Is the entity associated with a government, political entity, military or
                  religious group?
                </TextBold>
                <View style={styles.personalQuestionRow}>
                  <TouchableOpacity onPress={() => this.questionRes('associatedGroup', true)}>
                    <View
                      style={[
                        styles.personalQuestionButton,
                        {
                          backgroundColor: this.state.associatedGroup ? '#00FFBD' : '#FFf',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color: '#000',
                        }}>
                        YES
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => this.questionRes('associatedGroup', false)}>
                    <View
                      style={[
                        styles.personalQuestionButton,
                        {
                          backgroundColor: !this.state.associatedGroup ? '#00FFBD' : '#FFf',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color: '#000',
                        }}>
                        NO
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                </View>

                <TextBold style={{fontSize: 17, marginBottom: 10, marginTop: 30}}>
                  Does the entity's gross annual revenue exceed $1,000,000 US Dollars?
                </TextBold>
                <View style={styles.personalQuestionRow}>
                  <TouchableOpacity onPress={() => this.questionRes('grossAnnual', true)}>
                    <View
                      style={[
                        styles.personalQuestionButton,
                        {
                          backgroundColor: this.state.grossAnnual ? '#00FFBD' : '#FFf',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color: '#000',
                        }}>
                        YES
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => this.questionRes('grossAnnual', false)}>
                    <View
                      style={[
                        styles.personalQuestionButton,
                        {
                          backgroundColor: !this.state.grossAnnual ? '#00FFBD' : '#FFf',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color: '#000',
                        }}>
                        NO
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                </View>

                <TextBold style={{fontSize: 17, marginBottom: 10, marginTop: 30}}>
                  Has any executive, director, primary owner, or over 10% equity shareholder ever
                  pled guilty or been convicted of a felony or financial crime?
                </TextBold>
                <View style={styles.personalQuestionRow}>
                  <TouchableOpacity onPress={() => this.questionRes('felonyCrime', true)}>
                    <View
                      style={[
                        styles.personalQuestionButton,
                        {
                          backgroundColor: this.state.felonyCrime ? '#00FFBD' : '#FFf',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color: '#000',
                        }}>
                        YES
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => this.questionRes('felonyCrime', false)}>
                    <View
                      style={[
                        styles.personalQuestionButton,
                        {
                          backgroundColor: !this.state.felonyCrime ? '#00FFBD' : '#FFf',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color: '#000',
                        }}>
                        NO
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                </View>
                {this.state.felonyCrime && (
                  <View style={{marginTop: 12}}>
                    <TextReg style={{color: '#E5705A', marginBottom: 10}}>
                      If YES, please specify
                    </TextReg>
                    <TextInput
                      style={styles.personalInfoInput}
                      onChangeText={text => this.updateField(text, 'felonyCrimeInput')}
                      ref={input => (this.inputs.felonyCrimeInput = input)}
                      value={this.state.felonyCrimeInput}
                      underlineColorAndroid="transparent"
                      blurOnSubmit
                      returnKeyType={'next'}
                      placeholder={''}
                      onSubmitEditing={() => Keyboard.dismiss()}
                      keyboardAppearance="dark"
                    />
                  </View>
                )}

                <TextBold style={{fontSize: 17, marginBottom: 10, marginTop: 30}}>
                  Have you ever filed for bankruptcy?
                </TextBold>
                <View style={styles.personalQuestionRow}>
                  <TouchableOpacity onPress={() => this.questionRes('bankruptcy', true)}>
                    <View
                      style={[
                        styles.personalQuestionButton,
                        {
                          backgroundColor: this.state.bankruptcy ? '#00FFBD' : '#FFf',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color: '#000',
                        }}>
                        YES
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => this.questionRes('bankruptcy', false)}>
                    <View
                      style={[
                        styles.personalQuestionButton,
                        {
                          backgroundColor: !this.state.bankruptcy ? '#00FFBD' : '#FFf',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color: '#000',
                        }}>
                        NO
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                </View>
                {this.state.bankruptcy && (
                  <View style={{marginTop: 12}}>
                    <TextReg style={{color: '#E5705A', marginBottom: 10}}>
                      If YES, please specify
                    </TextReg>
                    <TextInput
                      style={styles.personalInfoInput}
                      onChangeText={text => this.updateField(text, 'bankruptcyInput')}
                      ref={input => (this.inputs.felonyCrime = input)}
                      value={this.state.bankruptcyInput}
                      underlineColorAndroid="transparent"
                      blurOnSubmit
                      returnKeyType={'next'}
                      placeholder={''}
                      onSubmitEditing={() => Keyboard.dismiss()}
                      keyboardAppearance="dark"
                    />
                  </View>
                )}

                <View
                  style={{
                    borderBottomWidth: 1,
                    borderColor: '#CCC',
                    marginLeft: 10,
                    marginRight: 10,
                    marginBottom: 16,
                    marginTop: 20,
                  }}
                />

                <TextBold style={{fontSize: 18, marginBottom: 20}}>Proof of Address</TextBold>
                <TextReg style={styles.personalInfoInputTitle}>
                  Provide ONE of the following:
                </TextReg>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <View
                    style={{
                      height: 4,
                      width: 4,
                      backgroundColor: '#555',
                      borderRadius: 2,
                      marginRight: 4,
                    }}
                  />
                  <TextReg>Latest Bank Statement</TextReg>
                </View>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <View
                    style={{
                      height: 4,
                      width: 4,
                      backgroundColor: '#555',
                      borderRadius: 2,
                      marginRight: 4,
                    }}
                  />
                  <TextReg>Latest Utility Bill</TextReg>
                </View>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <View
                    style={{
                      height: 4,
                      width: 4,
                      backgroundColor: '#555',
                      borderRadius: 2,
                      marginRight: 4,
                    }}
                  />
                  <TextReg>Tax Filing</TextReg>
                </View>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <View
                    style={{
                      height: 4,
                      width: 4,
                      backgroundColor: '#555',
                      borderRadius: 2,
                      marginRight: 4,
                    }}
                  />
                  <TextReg>Government Issued Document</TextReg>
                </View>

                <View>
                  <Button
                    style={styles.banksEditUploadButton}
                    onPress={() => this.openImageSelect()}>
                    {this.state.refreshingUpload ? (
                      <Image
                        source={require('../../../imgs/loadingDots.gif')}
                        style={styles.banksEditLoadingDots}
                      />
                    ) : (
                      <TextReg style={{color: '#000'}}>
                        Upload {this.state.documents.length === 0 ? 'Document' : 'Another'}
                      </TextReg>
                    )}
                  </Button>
                  {showDocuments}
                  <TextReg>
                    Address information on documents must match the information provided above.
                  </TextReg>
                  {this.props.imageError && (
                    <View style={styles.banksUploadErrorBox}>
                      <TextReg style={styles.banksUploadErrorText}>
                        {this.props.imageError === 'type' &&
                          'Invalid Image Type - JPEG, JPG, PNG, TIF, PDF only'}
                        {this.props.imageError === 'size' && 'Image too large, 50MB max'}
                      </TextReg>
                    </View>
                  )}
                </View>

                <View
                  style={{
                    borderBottomWidth: 1,
                    borderColor: '#CCC',
                    marginLeft: 10,
                    marginRight: 10,
                    marginBottom: 16,
                    marginTop: 20,
                  }}
                />

                <TextBold style={{fontSize: 18, marginBottom: 20}}>
                  Verification of Business
                </TextBold>
                <TextReg style={styles.personalInfoInputTitle}>
                  Provide ONE of the following:
                </TextReg>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <View
                    style={{
                      height: 4,
                      width: 4,
                      backgroundColor: '#555',
                      borderRadius: 2,
                      marginRight: 4,
                    }}
                  />
                  <TextReg>Corporation Certification</TextReg>
                </View>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <View
                    style={{
                      height: 4,
                      width: 4,
                      backgroundColor: '#555',
                      borderRadius: 2,
                      marginRight: 4,
                    }}
                  />
                  <TextReg>Article of Incorporation</TextReg>
                </View>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <View
                    style={{
                      height: 4,
                      width: 4,
                      backgroundColor: '#555',
                      borderRadius: 2,
                      marginRight: 4,
                    }}
                  />
                  <TextReg>Registration</TextReg>
                </View>

                <View>
                  <Button
                    style={styles.banksEditUploadButton}
                    onPress={() => this.openImageSelect(true)}>
                    {this.state.refreshingUpload ? (
                      <Image
                        source={require('../../../imgs/loadingDots.gif')}
                        style={styles.banksEditLoadingDots}
                      />
                    ) : (
                      <TextReg style={{color: '#000'}}>
                        Upload {this.state.verificationDocs.length === 0 ? 'Document' : 'Another'}
                      </TextReg>
                    )}
                  </Button>
                  {showVerificationDocuments}
                  <TextReg>
                    Address information on documents must match the information provided above.
                  </TextReg>
                  {this.props.imageError && (
                    <View style={styles.banksUploadErrorBox}>
                      <TextReg style={styles.banksUploadErrorText}>
                        {this.props.imageError === 'type' &&
                          'Invalid Image Type - JPEG, JPG, PNG, TIF, PDF only'}
                        {this.props.imageError === 'size' && 'Image too large, 50MB max'}
                      </TextReg>
                    </View>
                  )}
                </View>
              </View>
            </View>

            <View
              style={{
                borderBottomWidth: 1,
                borderColor: '#CCC',
                marginLeft: 10,
                marginRight: 10,
                marginBottom: 16,
                marginTop: 10,
              }}
            />

            <View style={{width: 300}}>
              <TextBold style={{fontSize: 17, marginBottom: 20}}>
                What will the loan funds be used for?
              </TextBold>
              <TouchableOpacity
                onPress={() => this.questionRes('useOfFunds', 'capital_expenditure')}>
                <View
                  style={[
                    styles.personalMultipleQuestionButton,
                    {
                      backgroundColor:
                        this.state.useOfFunds == 'capital_expenditure' ? '#00FFBD' : '#3D3D50',
                    },
                  ]}>
                  <TextReg
                    style={{
                      fontSize: 18,
                      color: this.state.useOfFunds == 'capital_expenditure' ? '#000' : '#FFF',
                    }}>
                    Capital Expenditure/Large Purchase
                  </TextReg>
                </View>
              </TouchableOpacity>

              <TouchableOpacity onPress={() => this.questionRes('useOfFunds', 'working_capital')}>
                <View
                  style={[
                    styles.personalMultipleQuestionButton,
                    {
                      backgroundColor:
                        this.state.useOfFunds == 'working_capital' ? '#00FFBD' : '#3D3D50',
                    },
                  ]}>
                  <TextReg
                    style={{
                      fontSize: 18,
                      color: this.state.useOfFunds == 'working_capital' ? '#000' : '#FFF',
                    }}>
                    Working Capital
                  </TextReg>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => this.questionRes('useOfFunds', 'business_expansion')}>
                <View
                  style={[
                    styles.personalMultipleQuestionButton,
                    {
                      backgroundColor:
                        this.state.useOfFunds == 'business_expansion' ? '#00FFBD' : '#3D3D50',
                    },
                  ]}>
                  <TextReg
                    style={{
                      fontSize: 18,
                      color: this.state.useOfFunds == 'business_expansion' ? '#000' : '#FFF',
                    }}>
                    Business Expansion
                  </TextReg>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => this.questionRes('useOfFunds', 'new_business_creation')}>
                <View
                  style={[
                    styles.personalMultipleQuestionButton,
                    {
                      backgroundColor:
                        this.state.useOfFunds == 'new_business_creation' ? '#00FFBD' : '#3D3D50',
                    },
                  ]}>
                  <TextReg
                    style={{
                      fontSize: 18,
                      color: this.state.useOfFunds == 'new_business_creation' ? '#000' : '#FFF',
                    }}>
                    New Business Creation
                  </TextReg>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => this.questionRes('useOfFunds', 'debt_consolidation')}>
                <View
                  style={[
                    styles.personalMultipleQuestionButton,
                    {
                      backgroundColor:
                        this.state.useOfFunds == 'debt_consolidation' ? '#00FFBD' : '#3D3D50',
                    },
                  ]}>
                  <TextReg
                    style={{
                      fontSize: 18,
                      color: this.state.useOfFunds == 'debt_consolidation' ? '#000' : '#FFF',
                    }}>
                    Debt Consolidation
                  </TextReg>
                </View>
              </TouchableOpacity>

              <TouchableOpacity onPress={() => this.questionRes('useOfFunds', 'reinvestment')}>
                <View
                  style={[
                    styles.personalMultipleQuestionButton,
                    {
                      backgroundColor:
                        this.state.useOfFunds == 'reinvestment' ? '#00FFBD' : '#3D3D50',
                    },
                  ]}>
                  <TextReg
                    style={{
                      fontSize: 18,
                      color: this.state.useOfFunds == 'reinvestment' ? '#000' : '#FFF',
                    }}>
                    Reinvestment
                  </TextReg>
                </View>
              </TouchableOpacity>

              <Button
                disabled={ifYesError || !submitDate}
                style={{marginTop: 30, marginBottom: 20, alignSelf: 'stretch'}}
                isLoading={this.state.loading}
                onPress={() => this.submit()}>
                SUBMIT
              </Button>
              {ifYesError && (
                <View style={styles.showErrorBox}>
                  <TextReg style={styles.showErrorText}>
                    If YES for reponse, please specify reason.
                  </TextReg>
                </View>
              )}
              {!submitDate && (
                <View style={styles.showErrorBox}>
                  <TextReg style={styles.showErrorText}>Incorrect Date Format</TextReg>
                </View>
              )}
            </View>
          </Card>
        </ScrollView>
      </View>
    )
  }
}

BusinessEntity.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  account: state.auth.account,
  showPinScreen: state.auth.pinScreen,
  accountRef: state.auth.account.ref,
})

export default connect(mapStateToProps)(BusinessEntity)
