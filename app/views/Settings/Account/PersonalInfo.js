import React, {Component} from 'react'
import {
  View,
  TextInput,
  Keyboard,
  TouchableOpacity,
  Image,
  Platform,
  ScrollView,
} from 'react-native'
import {connect} from 'react-redux'
import {increaseRefreshDataCount} from '../../../store/user/user.actions'

import * as ImagePicker from 'react-native-image-picker'

import {
  Button,
  TextReg,
  TextBold,
  BackgroundHeader,
  Card,
  LocationSelect,
} from '../../../components'

import iso3166 from 'iso-3166-2'
import countryCodes from '../../../util/countryCodes'

import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'

class PersonalInfo extends Component {
  constructor(props) {
    super(props)
    let verified = props?.user?.walletsEnabled
    console.log('verified already', verified)

    let pickableCountryCodes = countryCodes().map(a => a.name)
    pickableCountryCodes = ['United States'].concat(pickableCountryCodes)
    this.state = {
      firstName: this.props.user?.firstName || '',
      middleInitial: this.props.user?.middleInitial || '',
      lastName: this.props.user?.lastName || '',
      address1: this.props.user?.address?.street1 || '',
      address2: this.props.user?.address?.street2 || '',
      country: '',
      state: '',
      city: this.props.user?.address?.city || '',
      zipCode: this.props.user?.address?.postalCode || '',
      birthday: this.props.user?.dateOfBirth || null,
      social: this.props.user?.govtId || '',
      occupation: this.props.user?.occupation || '',
      employer: this.props.user?.employer || '',

      hasSecuritiesAffiliation: this.props.user?.hasSecuritiesAffiliation || false,
      hasSecuritiesAffiliationInput: this.props.user?.affiliationSpecification || '',
      isCompanyShareholder: this.props.user?.isCompanyShareholder || false,
      isCompanyShareholderInput: this.props.user?.shareholderSpecification || '',
      hasBankruptcy: this.props.user?.hasBankruptcy || false,
      hasBankruptcyInput: this.props.user?.bankruptcySpecification || '',
      isInMilitary: this.props.user?.isInMilitary || false,
      isInMilitaryInput: this.props.user?.militarySpecification || '',

      countryCodes: pickableCountryCodes,
      countrySubs: [],
      selectedCountry: null,
      selectedCountryTwo: this.props.user?.countryOfCitizenship || null,
      selectedCountryTwoName: '',
      countryCode: null,
      pickableCountrySubs: [],
      selectedProvince: null,
      regionCode: null,

      highlightCountry: false,
      highlightProvince: false,
      highlightCountryTwo: false,

      documents: this.props.user?.address?.documents || [],
      refreshingUpload: false,
      imageError: false,

      useOfFunds: this.props.user?.useOfFunds || '',
      dateNumbersError: false,
    }
    this.inputs = {}

    let countryName
    let pickableCountrySubs = []
    if (this.props.user.address?.countryCode) {
      countryName = countryCodes().filter(a => a.code == this.props.user?.address?.countryCode)[0]
        .name
      this.state.countryCode = this.props.user?.address?.countryCode
      this.state.selectedCountry = countryName
      const isoCountry = iso3166.country(this.props.user?.address?.countryCode)
      const subArr = Object.values(isoCountry.sub)
      pickableCountrySubs = subArr.map(a => a.name)
      pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
        if (a < b) return -1
        if (b < a) return 1
        return 0
      })
      this.state.pickableCountrySubs = pickableCountrySubs
    }

    if (this.props.user?.address?.province) {
      const regionCode = this.props.user?.address?.province

      const subData = iso3166.subdivision(this.state.countryCode, regionCode)
      const selectedProvince = subData?.name

      this.state.selectedProvince = selectedProvince
      this.state.regionCode = regionCode
    }
  }

  onSelect = selection => {
    const selectedCountry = this.state.countryCodes.filter((a, k) => a == selection)[0]
    const countryCode = countryCodes().filter(a => a.name == selectedCountry)[0].code
    const isoCountry = iso3166.country(countryCode)
    let pickableCountrySubs = []
    const subArr = Object.values(isoCountry.sub)
    pickableCountrySubs = subArr.map(a => a.name)
    pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
      if (a < b) return -1
      if (b < a) return 1
      return 0
    })
    this.setState({
      selectedCountry,
      countryCode,
      pickableCountrySubs,
      subArr,
      highlightCountry: false,
      highlightProvince: true,
    })
  }

  onSelectTwo = selection => {
    const selectedCountry = this.state.countryCodes.filter((a, k) => a == selection)[0]
    console.log('selection', selection, selectedCountry, this.state.countryCodes, countryCodes())

    const countryCode = countryCodes().filter(a => a.name == selectedCountry)[0].code
    const isoCountry = iso3166.country(countryCode)
    let pickableCountrySubs = []
    const subArr = Object.values(isoCountry.sub)
    pickableCountrySubs = subArr.map(a => a.name)
    pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
      if (a < b) return -1
      if (b < a) return 1
      return 0
    })
    console.log('countryCode', countryCode)
    this.setState({
      selectedCountryTwoName: selectedCountry,
      selectedCountryTwo: countryCode,
      highlightCountryTwo: false,
    })
    this.inputs.occupation.focus()
  }

  onProvinceSelect = province => {
    const selectedProvince = this.state.pickableCountrySubs.filter((a, k) => k == province)[0]

    const subData = iso3166.subdivision(this.state.countryCode, selectedProvince)
    const regionCode = subData.regionCode
    this.setState({selectedProvince, regionCode, highlightProvince: false})
    this.inputs.city.focus()
  }

  highlightCountry = () => {
    Keyboard.dismiss()
    this.setState({highlightCountry: true})
  }

  highlightCountryTwo = () => {
    Keyboard.dismiss()
    this.setState({highlightCountryTwo: true})
  }

  updateField = (text, type) => {
    const state = this.state
    if (type == 'birthday') {
      const textBefore = text
      text = this.formatDateText(text, state[type])
      if (text.length != 0 && !text) {
        state.dateNumbersError = true
        text = textBefore
      } else {
        state.dateNumbersError = false
      }
    }
    state[type] = text
    this.setState(state)
  }

  formatDateText = (text, oldText) => {
    if (/[a-zA-Z]/.test(text)) {
      return false
    } else if (oldText && text.length < oldText.length) {
      return text
    } else if (
      oldText &&
      oldText.length == 2 &&
      text.length > oldText.length &&
      text.substring(2) != '/'
    ) {
      return text.substring(0, 2) + '/' + text.substring(2)
    } else if (
      oldText &&
      oldText.length == 5 &&
      text.length > oldText.length &&
      text.substring(5) != '/'
    ) {
      return text.substring(0, 5) + '/' + text.substring(5)
    } else if (text.length == 2 || text.length == 5) {
      return text + '/'
    } else if (text.length == 10) {
      const one = text.substring(0, 2)
      const two = text.substring(3, 5)
      const three = text.substring(6, 10)
      const testOne = /^\d+$/.test(one)
      const testTwo = /^\d+$/.test(two)
      const testThree = /^\d+$/.test(three)
      const firstSlash = text.substring(2, 3)
      const secondSlash = text.substring(5, 6)
      const year = new Date().getFullYear()
      if (
        !testOne ||
        !testTwo ||
        !testThree ||
        firstSlash != '/' ||
        secondSlash != '/' ||
        Number(one) > 12 ||
        Number(two) > 31 ||
        Number(three) > year
      ) {
        return false
      }
    } else if (text.length > 10) {
      return false
    }
    return text
  }

  backToLoanChecklist = () => {
    this.props.navigation.popToTop()
    this.props.navigation.navigate('Home')
  }

  questionRes = (type, res) => {
    const state = this.state
    state[type] = res
    this.setState(state)
  }

  openImageSelect = () => {
    const options = {
      title: 'Select Document',
      maxWidth: 2200,
      maxHeight: 2200,
      quality: 1.0,
      storageOptions: {
        skipBackup: true,
        path: 'images',
        cameraRoll: true,
        waitUntilSaved: true,
      },
      //takePhotoButtonTitle: null,
      noData: true,
      mediaType: 'photo',
    }

    this.setState({
      refreshingUpload: true,
      imageError: false,
    })

    ImagePicker.launchImageLibrary(options, async response => {
      if (response.didCancel) {
        this.setState({refreshingUpload: false})
        return
      } else if (response.error) {
        this.setState({refreshingUpload: false})
        return
      } else if (response.customButton) {
        console.log('User tapped custom button: ', response.customButton)
      } else {
        response = response.assets[0]
        const photo = {uri: response.uri}
        const formdata = new FormData()

        const validImage = this.validateImage(response.type, response.fileSize)
        if (validImage === 'type') {
          this.setState({refreshingUpload: false, imageError: 'type'})
          return
        } else if (validImage === 'size') {
          this.setState({refreshingUpload: false, imageError: 'size'})
          return
        }

        let path = response.uri
        if (Platform.OS === 'ios') {
          path = '~' + path.substring(path.indexOf('/Documents'))
        }
        if (!response.fileName) response.fileName = path.split('/').pop()

        formdata.append('file', [
          {
            uri: Platform.OS === 'android' ? photo.uri : photo.uri.replace('file://', ''),
            name: response.fileName || 'testName.jpg',
            type: response.type,
            size: response.fileSize,
          },
        ])
        formdata.append('name', response.fileName)
        formdata.append('key', 'true')

        console.log('gonna send to web')
        this.props.WebService.uploadDocument(
          this.props.user.address.id,
          'address',
          'other_proof_of_address',
          photo.uri.replace('file://', ''),
          response.fileName,
        )
          .then(res => {
            const documents = this.state.documents
            documents.push({
              name: response.fileName,
            })
            this.setState({
              refreshingUpload: false,
              documents,
            })
          })
          .catch(err => {
            console.log('upload doc err', err)
            this.setState({refreshingUpload: false})
          })
      }
    })
  }

  validateImage = (type, size) => {
    const fileTypes = [
      'application/pdf',
      'image/jpg',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/tif',
      'image/tiff',
    ]
    if (!fileTypes.includes(type)) {
      return 'type'
    }
    if (size / 1000000 > 50) {
      return 'size'
    }
    return true
  }

  submit = () => {
    //patchAddress
    const addressId = this.props.user.address.id
    const addressPayload = {
      city: this.state.city,
      countryCode: this.state.countryCode,
      postalCode: this.state.zipCode,
      province: this.state.regionCode,
      street1: this.state.address1,
      street2: this.state.address2,
    }
    this.props.WebService.patchAddress(addressId, addressPayload)
      .then(res => {
        console.log('patchAddress res', res)
      })
      .catch(err => {
        console.log('patchAddress err', err)
      })

    //patchUser

    this.setState({loading: true})

    const userPayload = {
      affiliationSpecification: this.state.hasSecuritiesAffiliationInput,
      bankruptcySpecification: this.state.hasBankruptcyInput,
      countryOfCitizenship: this.state.selectedCountryTwo,
      dateOfBirth: this.state.birthday,
      employer: this.state.employer,
      firstName: this.state.firstName,
      govtId: this.state.social,
      hasBankruptcy: this.state.hasBankruptcy,
      hasSecuritiesAffiliation: this.state.hasSecuritiesAffiliation,
      isCompanyShareholder: this.state.isCompanyShareholder,
      isInMilitary: this.state.isInMilitary,
      lastName: this.state.lastName,
      middleInitial: this.state.middleInitial,
      militarySpecification: this.state.isInMilitaryInput,
      occupation: this.state.occupation,
      shareholderSpecification: this.state.isCompanyShareholderInput,
      useOfFunds: this.state.useOfFunds,
    }

    this.props.WebService.patchUser(userPayload)
      .then(res => {
        console.log('patchUser res', res)
        this.setState({loading: false})
        this.props.dispatch(increaseRefreshDataCount())
        this.backToLoanChecklist()
      })
      .catch(err => {
        console.log('patchUser err', err)
        this.setState({loading: false})
      })
  }

  goToPhoneNumber = () => {
    this.props.navigation.navigate('PhoneMenu', {flow: 'loanChecklist'})
  }

  render() {
    const fromLoanChecklist = this.props.navigation.route?.flow === 'loanChecklist'

    const showDocuments = this.state.documents.map((a, k) => (
      <View key={k}>
        <View style={styles.banksUploadDocumentBox}>
          <TextReg style={{color: '#fff'}}>{a.name}</TextReg>
        </View>
      </View>
    ))

    let formatedPhone = this.props.user?.phone?.number
    if (formatedPhone) {
      formatedPhone =
        formatedPhone.substring(0, 3) +
        '-' +
        formatedPhone.substring(3, 6) +
        '-' +
        formatedPhone.substring(6, formatedPhone.length)
    }

    let ifYesError = false
    if (this.state.hasSecuritiesAffiliation && this.state.hasSecuritiesAffiliationInput == '') {
      ifYesError = true
    }
    if (this.state.isCompanyShareholder && this.state.isCompanyShareholderInput == '') {
      ifYesError = true
    }
    if (this.state.hasBankruptcy && this.state.hasBankruptcyInput == '') {
      ifYesError = true
    }
    if (this.state.isInMilitary && this.state.isInMilitaryInput == '') {
      ifYesError = true
    }

    let submitDate = true
    if (this.state.dateNumbersError) {
      submitDate = false
    }
    if (this.state.birthday?.length > 0 && this.state.birthday?.length != 10) {
      submitDate = false
    }

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader
          title={'Personal Info'}
          goBack={fromLoanChecklist ? this.backToLoanChecklist : this.props.navigation.goBack}
        />
        <ScrollView contentContainerStyle={{alignSelf: 'stretch', alignItems: 'center'}}>
          <Card style={{paddingBottom: 20}} marginTop={14}>
            <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
              <View style={{width: 300}}>
                <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
                  <TextBold style={{fontSize: 18, marginBottom: 30, marginTop: 20}}>
                    Personal Info
                  </TextBold>
                </View>

                <TextReg style={styles.personalInfoInputTitle}>Phone Number</TextReg>
                {this.props.user.phone?.isVerified ? (
                  <TouchableOpacity onPress={this.goToPhoneNumber}>
                    <View style={{marginBottom: 18}}>
                      <TextReg
                        style={{
                          fontSize: 17,
                        }}>{`+${this.props.user.phone.callingCode} ${formatedPhone}`}</TextReg>
                    </View>
                  </TouchableOpacity>
                ) : (
                  <TouchableOpacity onPress={this.goToPhoneNumber}>
                    <View
                      style={[
                        styles.personalQuestionButton,
                        {
                          backgroundColor: '#00FFBD',
                          marginBottom: 18,
                        },
                      ]}>
                      <TextReg style={{fontSize: 18, color: '#000'}}>ADD PHONE</TextReg>
                    </View>
                  </TouchableOpacity>
                )}

                <TextReg style={styles.personalInfoInputTitle}>First Name</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'firstName')}
                  ref={input => (this.inputs.firstName = input)}
                  value={this.state.firstName}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.inputs.middleInitial.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.personalInfoInputTitle}>Middle Initial</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'middleInitial')}
                  ref={input => (this.inputs.middleInitial = input)}
                  value={this.state.middleInitial}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.inputs.lastName.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.personalInfoInputTitle}>Last Name</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'lastName')}
                  ref={input => (this.inputs.lastName = input)}
                  value={this.state.lastName}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.inputs.address1.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.personalInfoInputTitle}>Residential Address Line 1</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'address1')}
                  ref={input => (this.inputs.address1 = input)}
                  value={this.state.address1}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.inputs.address2.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.personalInfoInputTitle}>Residential Address Line 2</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'address2')}
                  ref={input => (this.inputs.address2 = input)}
                  value={this.state.address2}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.highlightCountry()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.personalInfoInputTitle}>Country</TextReg>

                <LocationSelect
                  options={this.state.countryCodes}
                  onSelect={this.onSelect}
                  highlight={this.state.highlightCountry}
                  placeholder={this.state.selectedCountry ? this.state.selectedCountry : ''}
                />

                <TextReg style={[styles.personalInfoInputTitle, {marginTop: 4}]}>
                  State/Province
                </TextReg>
                <LocationSelect
                  onSelect={this.onProvinceSelect}
                  highlight={this.state.highlightProvince}
                  options={this.state.pickableCountrySubs}
                  placeholder={this.state.selectedProvince ? this.state.selectedProvince : ''}
                />
                <TextReg style={[styles.personalInfoInputTitle, {marginTop: 4}]}>City</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'city')}
                  ref={input => (this.inputs.city = input)}
                  value={this.state.city}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.inputs.zipCode.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.personalInfoInputTitle}>Zip Code</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'zipCode')}
                  ref={input => (this.inputs.zipCode = input)}
                  value={this.state.zipCode}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.inputs.birthday.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.personalInfoInputTitle}>Date of Birth (mm/dd/yyyy)</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'birthday')}
                  ref={input => (this.inputs.birthday = input)}
                  value={this.state.birthday}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.inputs.social.focus()}
                  keyboardAppearance="dark"
                />
                {this.state.dateNumbersError && (
                  <TextReg
                    style={[
                      styles.personalInfoInputTitle,
                      {
                        marginTop: -8,
                        marginBottom: 10,
                        fontSize: 16,
                        color: '#E5705A',
                      },
                    ]}>
                    Incorrect Date Format
                  </TextReg>
                )}
                <TextReg style={styles.personalInfoInputTitle}>
                  Social Security Number or Tax ID Number
                </TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'social')}
                  ref={input => (this.inputs.social = input)}
                  value={this.state.social}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.highlightCountryTwo()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.personalInfoInputTitle}>Country of Citizenship</TextReg>
                <LocationSelect
                  options={this.state.countryCodes}
                  onSelect={this.onSelectTwo}
                  highlight={this.state.highlightCountryTwo}
                  placeholder={this.state.selectedCountryTwo ? this.state.selectedCountryTwo : ''}
                />
                <TextBold style={{fontSize: 18, marginBottom: 20, marginTop: 20}}>
                  Employment Information
                </TextBold>
                <TextReg style={styles.personalInfoInputTitle}>Occupation/Role</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'occupation')}
                  ref={input => (this.inputs.occupation = input)}
                  value={this.state.occupation}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => this.inputs.employer.focus()}
                  keyboardAppearance="dark"
                />
                <TextReg style={styles.personalInfoInputTitle}>Employer Name</TextReg>
                <TextInput
                  style={styles.personalInfoInput}
                  onChangeText={text => this.updateField(text, 'employer')}
                  ref={input => (this.inputs.employer = input)}
                  value={this.state.employer}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  placeholder={''}
                  onSubmitEditing={() => Keyboard.dismiss()}
                  keyboardAppearance="dark"
                />
                <View
                  style={{
                    height: 2,
                    width: 300,
                    borderBottomWidth: 1,
                    borderColor: '#cbcbcb',
                    marginTop: 20,
                    marginBottom: 20,
                  }}
                />
                <TextBold style={{fontSize: 18, marginBottom: 20, marginTop: 20}}>
                  Proof of Residential Address
                </TextBold>
                <TextReg style={styles.personalInfoInputTitle}>
                  Provide ONE of the following:
                </TextReg>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <View
                    style={{
                      height: 4,
                      width: 4,
                      backgroundColor: '#555',
                      borderRadius: 2,
                      marginRight: 4,
                    }}
                  />
                  <TextReg>Government Issued ID</TextReg>
                </View>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <View
                    style={{
                      height: 4,
                      width: 4,
                      backgroundColor: '#555',
                      borderRadius: 2,
                      marginRight: 4,
                    }}
                  />
                  <TextReg>Latest Bank or Postal Statement</TextReg>
                </View>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <View
                    style={{
                      height: 4,
                      width: 4,
                      backgroundColor: '#555',
                      borderRadius: 2,
                      marginRight: 4,
                    }}
                  />
                  <TextReg>Registered Lease / Sale Agreement</TextReg>
                </View>

                <View>
                  <Button
                    style={styles.banksEditUploadButton}
                    onPress={() => this.openImageSelect()}>
                    {this.state.refreshingUpload ? (
                      <Image
                        source={require('../../../imgs/loadingDots.gif')}
                        style={styles.banksEditLoadingDots}
                      />
                    ) : (
                      <TextReg style={{color: '#000'}}>
                        Upload {this.state.documents.length === 0 ? 'Document' : 'Another'}
                      </TextReg>
                    )}
                  </Button>
                  {showDocuments}
                  <TextReg>
                    Address information on documents must match the information provided above.
                  </TextReg>
                  {this.props.imageError && (
                    <View style={styles.banksUploadErrorBox}>
                      <TextReg style={styles.banksUploadErrorText}>
                        {this.props.imageError === 'type' &&
                          'Invalid Image Type - JPEG, JPG, PNG, TIF, PDF only'}
                        {this.props.imageError === 'size' && 'Image too large, 50MB max'}
                      </TextReg>
                    </View>
                  )}
                </View>

                <View
                  style={{
                    height: 2,
                    width: 300,
                    borderBottomWidth: 1,
                    borderColor: '#cbcbcb',
                    marginTop: 30,
                    marginBottom: 30,
                  }}
                />

                <TextBold style={{fontSize: 17, marginBottom: 10}}>
                  Are you, your partner, or any other immediate family / household members, in-laws,
                  siblings, or dependents employed by or associated with the securities industry?
                </TextBold>
                <TextReg style={styles.personalInfoInputTitle}>
                  (A sole proprietor, partner, officer, director, branch manager, registered
                  representative, or other associated person of a Broker-Dealer firm)
                </TextReg>
                <View style={styles.personalQuestionRow}>
                  <TouchableOpacity
                    onPress={() => this.questionRes('hasSecuritiesAffiliation', true)}>
                    <View
                      style={[
                        styles.personalQuestionButton,
                        {
                          backgroundColor: this.state.hasSecuritiesAffiliation ? '#00FFBD' : '#FFf',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color: '#000',
                        }}>
                        YES
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => this.questionRes('hasSecuritiesAffiliation', false)}>
                    <View
                      style={[
                        styles.personalQuestionButton,
                        {
                          backgroundColor: !this.state.hasSecuritiesAffiliation
                            ? '#00FFBD'
                            : '#FFf',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color: '#000',
                        }}>
                        NO
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                </View>
                {this.state.hasSecuritiesAffiliation && (
                  <View style={{marginTop: 12}}>
                    <TextReg style={{color: '#E5705A', marginBottom: 10}}>
                      If YES, please specify
                    </TextReg>
                    <TextInput
                      style={styles.personalInfoInput}
                      onChangeText={text => this.updateField(text, 'hasSecuritiesAffiliationInput')}
                      ref={input => (this.inputs.hasSecuritiesAffiliation = input)}
                      value={this.state.hasSecuritiesAffiliationInput}
                      underlineColorAndroid="transparent"
                      blurOnSubmit
                      returnKeyType={'next'}
                      placeholder={''}
                      onSubmitEditing={() => Keyboard.dismiss()}
                      keyboardAppearance="dark"
                    />
                  </View>
                )}

                <TextBold style={{fontSize: 17, marginBottom: 10, marginTop: 30}}>
                  Are you, your partner, or any other immediate family / household members, in-laws,
                  siblings, or dependents an officer, director, or 10% or more shareholder in a
                  publicly-owned company?
                </TextBold>
                <View style={styles.personalQuestionRow}>
                  <TouchableOpacity onPress={() => this.questionRes('isCompanyShareholder', true)}>
                    <View
                      style={[
                        styles.personalQuestionButton,
                        {
                          backgroundColor: this.state.isCompanyShareholder ? '#00FFBD' : '#FFf',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color: '#000',
                        }}>
                        YES
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => this.questionRes('isCompanyShareholder', false)}>
                    <View
                      style={[
                        styles.personalQuestionButton,
                        {
                          backgroundColor: !this.state.isCompanyShareholder ? '#00FFBD' : '#FFf',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color: '#000',
                        }}>
                        NO
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                </View>
                {this.state.isCompanyShareholder && (
                  <View style={{marginTop: 12}}>
                    <TextReg style={{color: '#E5705A', marginBottom: 10}}>
                      If YES, please specify
                    </TextReg>
                    <TextInput
                      style={styles.personalInfoInput}
                      onChangeText={text => this.updateField(text, 'isCompanyShareholderInput')}
                      ref={input => (this.inputs.isCompanyShareholder = input)}
                      value={this.state.isCompanyShareholderInput}
                      underlineColorAndroid="transparent"
                      blurOnSubmit
                      returnKeyType={'next'}
                      placeholder={''}
                      onSubmitEditing={() => Keyboard.dismiss()}
                      keyboardAppearance="dark"
                    />
                  </View>
                )}

                <TextBold style={{fontSize: 17, marginBottom: 10, marginTop: 30}}>
                  Have you ever filed for bankruptcy?
                </TextBold>
                <View style={styles.personalQuestionRow}>
                  <TouchableOpacity onPress={() => this.questionRes('hasBankruptcy', true)}>
                    <View
                      style={[
                        styles.personalQuestionButton,
                        {
                          backgroundColor: this.state.hasBankruptcy ? '#00FFBD' : '#FFf',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color: '#000',
                        }}>
                        YES
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => this.questionRes('hasBankruptcy', false)}>
                    <View
                      style={[
                        styles.personalQuestionButton,
                        {
                          backgroundColor: !this.state.hasBankruptcy ? '#00FFBD' : '#FFf',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color: '#000',
                        }}>
                        NO
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                </View>
                {this.state.hasBankruptcy && (
                  <View style={{marginTop: 12}}>
                    <TextReg style={{color: '#E5705A', marginBottom: 10}}>
                      If YES, please specify
                    </TextReg>
                    <TextInput
                      style={styles.personalInfoInput}
                      onChangeText={text => this.updateField(text, 'hasBankruptcyInput')}
                      ref={input => (this.inputs.hasBankruptcy = input)}
                      value={this.state.hasBankruptcyInput}
                      underlineColorAndroid="transparent"
                      blurOnSubmit
                      returnKeyType={'next'}
                      placeholder={''}
                      onSubmitEditing={() => Keyboard.dismiss()}
                      keyboardAppearance="dark"
                    />
                  </View>
                )}

                <TextBold style={{fontSize: 17, marginBottom: 10, marginTop: 30}}>
                  Are you an Active Duty member of the United States military?
                </TextBold>

                <View style={styles.personalQuestionRow}>
                  <TouchableOpacity onPress={() => this.questionRes('isInMilitary', true)}>
                    <View
                      style={[
                        styles.personalQuestionButton,
                        {
                          backgroundColor: this.state.isInMilitary ? '#00FFBD' : '#FFf',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color: '#000',
                        }}>
                        YES
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => this.questionRes('isInMilitary', false)}>
                    <View
                      style={[
                        styles.personalQuestionButton,
                        {
                          backgroundColor: !this.state.isInMilitary ? '#00FFBD' : '#FFf',
                        },
                      ]}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          color: '#000',
                        }}>
                        NO
                      </TextReg>
                    </View>
                  </TouchableOpacity>
                </View>
                {this.state.isInMilitary && (
                  <View style={{marginTop: 12}}>
                    <TextReg style={{color: '#E5705A', marginBottom: 10}}>
                      If YES, please specify
                    </TextReg>
                    <TextInput
                      style={styles.personalInfoInput}
                      onChangeText={text => this.updateField(text, 'isInMilitaryInput')}
                      ref={input => (this.inputs.isInMilitary = input)}
                      value={this.state.isInMilitaryInput}
                      underlineColorAndroid="transparent"
                      blurOnSubmit
                      returnKeyType={'next'}
                      placeholder={''}
                      onSubmitEditing={() => Keyboard.dismiss()}
                      keyboardAppearance="dark"
                    />
                  </View>
                )}

                <TextBold style={{fontSize: 17, marginBottom: 20, marginTop: 30}}>
                  What will the loan funds be used for?
                </TextBold>
                <TouchableOpacity
                  onPress={() => this.questionRes('useOfFunds', 'debt_consolidation')}>
                  <View
                    style={[
                      styles.personalMultipleQuestionButton,
                      {
                        backgroundColor:
                          this.state.useOfFunds == 'debt_consolidation' ? '#00FFBD' : '#FFf',
                      },
                    ]}>
                    <TextReg
                      style={{
                        fontSize: 18,
                        color: '#000',
                      }}>
                      Debt Consolidation
                    </TextReg>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => this.questionRes('useOfFunds', 'home_improvement')}>
                  <View
                    style={[
                      styles.personalMultipleQuestionButton,
                      {
                        backgroundColor:
                          this.state.useOfFunds == 'home_improvement' ? '#00FFBD' : '#FFf',
                      },
                    ]}>
                    <TextReg
                      style={{
                        fontSize: 18,
                        color: '#000',
                      }}>
                      Home Improvement
                    </TextReg>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity onPress={() => this.questionRes('useOfFunds', 'major_purchase')}>
                  <View
                    style={[
                      styles.personalMultipleQuestionButton,
                      {
                        backgroundColor:
                          this.state.useOfFunds == 'major_purchase' ? '#00FFBD' : '#FFf',
                      },
                    ]}>
                    <TextReg
                      style={{
                        fontSize: 18,
                        color: '#000',
                      }}>
                      Major Purchase
                    </TextReg>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity onPress={() => this.questionRes('useOfFunds', 'moving_expenses')}>
                  <View
                    style={[
                      styles.personalMultipleQuestionButton,
                      {
                        backgroundColor:
                          this.state.useOfFunds == 'moving_expenses' ? '#00FFBD' : '#FFf',
                      },
                    ]}>
                    <TextReg
                      style={{
                        fontSize: 18,
                        color: '#000',
                      }}>
                      Moving Expenses
                    </TextReg>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => this.questionRes('useOfFunds', 'digital_asset_reinvestment')}>
                  <View
                    style={[
                      styles.personalMultipleQuestionButton,
                      {
                        backgroundColor:
                          this.state.useOfFunds == 'digital_asset_reinvestment'
                            ? '#00FFBD'
                            : '#FFf',
                      },
                    ]}>
                    <TextReg
                      style={{
                        fontSize: 18,
                        color: '#000',
                      }}>
                      Digital Asset Reinvestment
                    </TextReg>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => this.questionRes('useOfFunds', 'real_estate_investment')}>
                  <View
                    style={[
                      styles.personalMultipleQuestionButton,
                      {
                        backgroundColor:
                          this.state.useOfFunds == 'real_estate_investment' ? '#00FFBD' : '#FFf',
                      },
                    ]}>
                    <TextReg
                      style={{
                        fontSize: 18,
                        color: '#000',
                      }}>
                      Real Estate Investment
                    </TextReg>
                  </View>
                </TouchableOpacity>

                <Button
                  disabled={ifYesError || !submitDate}
                  style={{
                    marginTop: 30,
                    marginBottom: 20,
                    alignSelf: 'stretch',
                  }}
                  isLoading={this.state.loading}
                  onPress={() => this.submit()}>
                  SUBMIT
                </Button>
                {ifYesError && (
                  <View style={styles.showErrorBox}>
                    <TextReg style={styles.showErrorText}>
                      If YES for reponse, please specify reason.
                    </TextReg>
                  </View>
                )}
                {!submitDate && (
                  <View style={styles.showErrorBox}>
                    <TextReg style={styles.showErrorText}>Incorrect Date Format</TextReg>
                  </View>
                )}
              </View>
            </View>
          </Card>
        </ScrollView>
      </View>
    )
  }
}

PersonalInfo.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  account: state.auth.account,
  showPinScreen: state.auth.pinScreen,
})

export default connect(mapStateToProps)(PersonalInfo)
