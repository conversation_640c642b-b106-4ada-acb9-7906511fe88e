import React, { Component } from 'react';
import { View, Image, NativeModules, NativeEventEmitter } from 'react-native';
import { connect } from 'react-redux';
//const { JumioMobileSDKNetverify } = NativeModules
import iso from 'iso-3166-1';

import { getMe } from '../../../store/user/user.actions';

import { TextReg, BackgroundHeader, Button, Card, TextBold } from '../../../components';
import commonStyles from '../../../styles/commonStyles';
import statusCheck from '../../../imgs/checkmark.png';
import statusPending from '../../../imgs/statusPending.png';
import statusFailed from '../../../imgs/statusFailed.png';

import styles from '../styles';

class IdentityVerification extends Component {
  constructor (props) {
    super(props);
    this.state = {
      jumioSecret: '',
      jumioToken: '',
      loading: false
    };
    /*
    this.polling = null
    const emitterNetverify = new NativeEventEmitter(JumioMobileSDKNetverify)
    emitterNetverify.addListener('EventDocumentData', EventDocumentData => {
      const scanIdRef = EventDocumentData.scanReference

      this.props.WebService.jumioInit({ providerId: scanIdRef })
        .then(res => {
          this.postJumioSDK(scanIdRef)
        })
        .catch(err => {
          this.setState({
            error: true,
            errorMessage: `Jumio Verification Error`,
          })
        })
    })
    emitterNetverify.addListener('EventError', EventError => {
      // user quit jumio
      this.setState({ loading: false })
    })
    */
  }

  componentDidMount () {
    /*
    this.props.WebService.getJumioCreds().then(res => {
      this.setState({
        jumioSecret: res.data.secret,
        jumioToken: res.data.token,
      })
    })
    */
  }

  componentDidUpdate (prevProps) {
    if (
      this.props.user &&
			prevProps.user &&
			(prevProps.user.idVerificationStatus === 'pending' || prevProps.user.idVerificationStatus === 'uninitiated')
    ) {
      if (this.props.user.idVerificationStatus === 'approved') {
        this.setState({ loading: false });
        clearInterval(this.polling);
        this.props.completePage(6);
      } else if (this.props.user.idVerificationStatus === 'retry') {
        // show error in UI
        // continue with signup instead
        /*
        this.setState({
          error: true,
          errorMessage: 'ID Verification failed, please try again.',
          loading: false,
        })
        */
        this.setState({ loading: false, error: true });
        clearInterval(this.polling);
      } else if (this.props.user.idVerificationStatus === 'rejected') {
        // show error in UI
        /*
        this.setState({
          error: true,
          errorMessage: `Your account requires additional checks to verify your identity. Our team is on it, we appreciate your patience.`,
          loading: false,
          rejected: true,
        })
        */
        this.setState({ loading: false, error: true });
      }
    }
  }

  componentWillUnmount () {
    clearInterval(this.polling);
  }

	startJumio = () => {
	  if (this.props.user.idVerificationStatus === 'approved' || this.props.user.idVerificationStatus === 'pending') {
	    return;
	  }
	  this.setState({ error: false, errorMessage: '' });
	  //this.initJumioSDK()
	  //JumioMobileSDKNetverify.startNetverify()
	}

	postJumioSDK = (scanIdRef) => {
	  this.props.WebService.jumioFormComplete({
	    jumioId: scanIdRef
	  })
	    .then((res) => {
	      this.setState({ loading: true });
	      this.polling = setInterval(this.props.getMe, 20000);
	    })
	    .catch((err) => {
	      this.setState({
	        error: true,
	        loading: false,
	        errorMessage: 'Network Error'
	      });
	    });
	}

	/*
  initJumioSDK = () => {
    const alpha3 = iso.whereAlpha2(this.props.user.address.countryCode).alpha3

    JumioMobileSDKNetverify.initNetverify(
      this.state.jumioToken,
      this.state.jumioSecret,
      'US',
      {
        requireVerification: true,
        customerId: this.props.user.id,
        preselectedCountry: alpha3,
        cameraPosition: 'BACK',
        documentTypes: ['DRIVER_LICENSE', 'PASSPORT', 'IDENTITY_CARD', 'VISA'],
      }
    )
  }
  */

	goBack = () => {
	  if (this.props.navigation.route?.flow === 'loanChecklist') {
	    this.props.navigation.popToTop();
	    this.props.navigation.navigate('Home');
	  } else {
	    this.props.navigation.goBack();
	  }
	}

	render () {
	  let statusImg = statusCheck;
	  let status = 'ID verified';
	  let subStatus = null;
	  let showRetryButton = false;
	  if (this.props.user.idVerificationStatus === 'pending') {
	    status = 'ID Verification Pending';
	    statusImg = statusPending;
	  }
	  if (this.props.user.idVerificationStatus === 'rejected') {
	    status = 'ID Verification Failed';
	    subStatus = 'Please contact customer support to resolve';
	    statusImg = statusFailed;
	  }
	  if (this.props.user.idVerificationStatus === 'retry') {
	    status = 'ID Verification Failed';
	    statusImg = statusFailed;
	    showRetryButton = true;
	  }
	  if (this.props.user.idVerificationStatus === 'uninitiated') {
	    showRetryButton = true;
	    statusImg = statusFailed;
	    status = 'ID Verification Uninitiated';
	  }
	  return (
	    <View style={commonStyles.tileContainer}>
	      <BackgroundHeader title={'Identity Verification'} goBack={this.goBack} />
	      <Card marginTop={20}>
	        <View style={{ alignSelf: 'stretch' }}>
	          <View style={styles.listItemID}>
	            <View style={styles.listItemLeft}>
	              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
	                <Image source={statusImg} style={{ width: 12, height: 12, marginRight: 8 }} />
	                <TextReg style={styles.listItemText}>{status}</TextReg>
	              </View>
	              {subStatus && <TextReg style={{ marginTop: 4 }}>{subStatus}</TextReg>}
	            </View>
	            <View style={styles.listItemRight} />
	          </View>
	        </View>
	      </Card>
	      {showRetryButton && (
	        <Button isLoading={this.state.loading} onPress={() => this.startJumio()} style={{ marginTop: 14 }}>
	          <TextBold style={{ fontSize: 17, letterSpacing: 0.7, color: '#fff' }}>
	            {this.props.user.idVerificationStatus == 'uninitiated' ? `VERIFY ID` : `RETRY ID VERIFICATION`}
	          </TextBold>
	        </Button>
	      )}
	    </View>
	  );
	}
}

IdentityVerification.navigationOptions = ({ navigation }) => ({
  title: null,
  header: <View style={{ height: 40, position: 'absolute' }} />,
  headerBackTitle: null
});

const mapStateToProps = (state) => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  account: state.auth.account,
  showPinScreen: state.auth.pinScreen
});

export default connect(mapStateToProps, { getMe })(IdentityVerification);
