import React, {Component} from 'react';
import {View, Image} from 'react-native';
import {connect} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {Button, TextReg, BackgroundHeader} from '../../../components';
import commonStyles from '../../../styles/commonStyles';
import {updateUpholdHide} from '../../../store/user/user.actions';

class UpholdPrefs extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  resetUphold = () => {
    this.setState({loading: true});

    AsyncStorage.removeItem(`UpholdToken-${this.props.user.primaryEmail}`, () => {});

    AsyncStorage.removeItem(`UpholdHide-${this.props.user.primaryEmail}`, () => {
      this.setState({loading: false});
      this.props.dispatch(updateUpholdHide([]));
      this.props.navigation.goBack();
    });
  };

  render() {
    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Uphold Preferences'} goBack={this.props.navigation.goBack} />
        <View
          style={{
            alignSelf: 'stretch',
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: 40,
          }}>
          <Image
            source={require('../../../imgs/upholdWide.png')}
            style={{
              height: 35,
              width: 110,
              marginBottom: 30,
            }}
          />
          <TextReg
            style={{
              textAlign: 'center',
              width: 220,
              marginBottom: 40,
              color: '#FFF',
            }}>
            Reset Uphold settings and refresh all saved wallet preferences
          </TextReg>

          <Button style={{alignSelf: 'stretch', marginLeft: 30, marginRight: 30}} isLoading={this.state.loading} onPress={this.resetUphold}>
            <TextReg style={{letterSpacing: 1.2, color: '#000'}}>RESET</TextReg>
          </Button>
        </View>
      </View>
    );
  }
}

UpholdPrefs.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
});

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  account: state.auth.account,
  showPinScreen: state.auth.pinScreen,
});

export default connect(mapStateToProps)(UpholdPrefs);
