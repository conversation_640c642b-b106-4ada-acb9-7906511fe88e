import React, {Component} from 'react'
import {View, Keyboard, Clipboard, BackHandler} from 'react-native'
import {connect} from 'react-redux'
import {updateActiveTabListener} from '../../../util/helpers'
import {screenView} from '../../../store/analytics/analytics.actions'
import {getMe, increaseRefreshDataCount} from '../../../store/user/user.actions'
import {derivedStatusMap} from '../../../util/enumerables'

import {Button, TextReg, BackgroundHeader, Card, SetupModal, TextBold, ChangeDeviceModal, SuccessModal} from '../../../components'
import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'

class TwoFactor extends Component {
  constructor(props) {
    super(props)
    this.state = {
      showSetup: false,
      showChangeDevice: false,
      step: 0,
      changeStep: 0,
      currentTwoFactor: '',
      newTwoFactor: '',
      setupTwoFactor: '',
      changeError: false,
      secret: '',
      changeLoading: false,
      setupLoading: false,
      showCopied: false,
      activeReset: false,
      setupError: false,
      loginPassword: '',
      setupErrorPassword: false,
      setupSuccess: false,
      changeSuccess: false,
      rateLimitError: false,
      timer: 0,
    }
    this.inputs = {}
  }

  componentDidMount() {
    updateActiveTabListener(this.props.navigation)
    this.props.dispatch(screenView(`Two-Factor`))
    BackHandler.addEventListener('hardwareBackPress', this.backAction)
    console.log('did mount')
  }
  /*
    backAction = () => {
      console.log('did back');
      this.closeSetup();
      return true;
    };
  */

  componentWillUnmount() {
    //BackHandler.removeEventListener('hardwareBackPress', this.backAction);
  }

  hideKeyboard = () => {
    Keyboard.dismiss()
  }

  focusNextField = field => {
    this.inputs[field].focus()
  }

  showChangeDevice = () => {
    this.setState({showChangeDevice: true})
  }

  closeChangeDevice = () => {
    this.setState({
      changeStep: 0,
      showChangeDevice: false,
      currentTwoFactor: '',
      newTwoFactor: '',
      setupTwoFactor: '',
      rateLimitError: false,
      changeError: false,
    })
  }

  showSetup = () => {
    this.setState({showSetup: true})
  }

  closeSetup = () => {
    this.setState({
      step: 0,
      showSetup: false,
      currentTwoFactor: '',
      newTwoFactor: '',
      setupTwoFactor: '',
      setupSuccess: false,
      setupError: false,
      setupErrorPassword: false,
      rateLimitError: false,
      loginPassword: '',
    })

    if (!this.props.pauseUnit21) {
      this.props.dispatch(increaseRefreshDataCount())
      return
    }
    if (this.props.route.params && this.props.route.params.reset) {
      this.props.navigation.popToTop()
    }
  }

  setupNext = step => {
    if (step === 0) {
      this.setState({setupLoading: true})

      this.props.WebService.send2FAVerificationEmail()
        .then(res => {
          this.setState({step: 1, setupLoading: false})
        })
        .catch(err => {
          this.setState({setupLoading: false})
        })
    }
    if (step === 1) {
      this.setState({
        setupLoading: true,
        setupError: false,
        setupErrorPassword: false,
      })

      //check password vs cognito
      this.props.AuthService.login(this.props.user.primaryEmail, this.state.loginPassword)
        .then(res => {
          this.props.WebService.verify2FAVerificationEmail({
            pin: this.state.setupTwoFactor,
          })
            .then(res => {
              this.setState({step: 2, secret: res.data, setupLoading: false})
            })
            .catch(err => {
              this.setState({setupLoading: false, setupError: true})
            })
        })
        .catch(err => {
          this.setState({setupLoading: false, setupErrorPassword: true})
        })
      /*

        */
    }
    if (step === 2) {
      this.setState({
        setupLoading: true,
        setupError: false,
        rateLimitError: false,
      })
      this.props.WebService.verify2FACode({
        code: this.state.newTwoFactor,
      })
        .then(res => {
          //show success and close
          this.setState({setupSuccess: true, setupLoading: false})
          this.props.dispatch(getMe())
          this.closeSetup()
        })
        .catch(err => {
          if (err.data.body.error === 'Rate limit reached') {
            this.setState(
              {setupLoading: false, rateLimitError: true, timer: 30},
              () => (this.intervalHandle = setInterval(this.tick, 1000)),
            )
          } else {
            this.setState({setupLoading: false, setupError: true})
          }
          // rate limit
        })
    }
  }

  tick = () => {
    if (this.state.timer === 1) {
      this.setState({timer: 0, rateLimitError: false})
      clearInterval(this.intervalHandle)
    } else {
      this.setState({timer: this.state.timer - 1})
    }
  }

  changeDeviceNext = step => {
    if (step === 0) {
      this.setState({changeLoading: true, changeError: false})
      this.props.WebService.verify2FACode({
        code: this.state.currentTwoFactor,
      })
        .then(res => {
          if (res) {
            this.props.WebService.generateNewSecret().then(res => {
              this.setState({
                changeStep: 1,
                secret: res.data,
                changeLoading: false,
              })
            })
          }
        })
        .catch(err => {
          this.setState({
            changeError: err.data.body.error,
            changeLoading: false,
          })
        })
    }
    if (step === 1) {
      this.setState({changeLoading: true, changeError: false})

      const data = {
        secret: this.state.secret,
        oldCode: this.state.currentTwoFactor,
        code: this.state.newTwoFactor,
      }
      this.props.WebService.setNewSecret(data)
        .then(res => {
          this.props.dispatch(getMe())
          this.setState({changeLoading: false, changeSuccess: true})
          this.closeChangeDevice()
        })
        .catch(err => {
          let showError = err.data.body.error
          if (err.data.body.error === 'Invalid 2FA old code') {
            showError = 'Timeout - 1 minute window exceeded, please close and retry'
          }
          this.setState({
            changeLoading: false,
            changeError: showError,
          })
        })
    }
    //this.setState({ changeStep: step + 1 })
  }

  handleUpdateTwoFactor = text => {
    this.setState({currentTwoFactor: text})
  }

  handleUpdateNewTwoFactor = text => {
    this.setState({newTwoFactor: text})
  }

  handleUpdateSetupTwoFactor = text => {
    this.setState({setupTwoFactor: text})
  }

  handleUpdatePassword = text => {
    this.setState({loginPassword: text})
  }

  handleChangeStep0 = () => {
    Keyboard.dismiss()
    this.changeDeviceNext(0)
  }

  closeSuccessModal = () => {
    this.setState({setupSuccess: false})
  }

  closeChangeSuccessModal = () => {
    this.setState({changeSuccess: false})
  }

  copy2FACode = () => {
    Clipboard.setString(this.state.secret)
    this.setState({showCopied: true}, () => {
      setTimeout(() => {
        this.setState({showCopied: false})
      }, 1200)
    })
  }

  checkActiveReset = () => {
    const unit21Active = derivedStatusMap.get(this.props.loanData.status) === 'pending' && !this.props.pauseUnit21
    if (unit21Active) {
      this.props.navigation.goBack()
    }
    if (this.props.route.params && this.props.route.params.reset) {
      this.setState({activeReset: true}, () => {
        setTimeout(() => {
          this.setState({activeReset: false})
        }, 5000)
      })
      return
    }
    this.props.navigation.goBack()
  }

  render() {
    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Two-Factor Auth'} goBack={this.checkActiveReset} />

        {this.props.user.mfaEnabled ? (
          <View style={{alignItems: 'center'}}>
            <Card style={{paddingBottom: 20}} marginTop={14}>
              <View style={styles.changePasswordBox}>
                <TextBold style={styles.TwoFactorTitle}>2-Step Verification</TextBold>
                <TextReg style={styles.TwoFactorText}>
                  Did you get a new phone and need to set up 2-Step Verification on your new device?
                </TextReg>
                <Button style={{backgroundColor: '#00FFBD', marginTop: 12}} onPress={() => this.showChangeDevice()}>
                  <TextBold
                    style={{
                      color: '#000',
                      fontSize: 17,
                      marginBottom: -2,
                      marginTop: -2,
                    }}>
                    CHANGE DEVICE
                  </TextBold>
                </Button>
              </View>
            </Card>
            <SuccessModal
              title={'Successful 2FA Setup'}
              showModal={this.state.setupSuccess}
              showPinScreen={this.props.showPinScreen}
              closeModal={() => this.closeSuccessModal()}
            />
            <SuccessModal
              title={'Successful 2FA Device Change'}
              showModal={this.state.changeSuccess}
              showPinScreen={this.props.showPinScreen}
              closeModal={() => this.closeChangeSuccessModal()}
            />
          </View>
        ) : (
          <Card style={{paddingBottom: 20}} marginTop={14}>
            <View style={styles.changePasswordBox}>
              <TextBold style={styles.TwoFactorTitle}>2-Step Verification</TextBold>
              <TextReg style={styles.TwoFactorText}>
                2-Step Verification increases the security of your account. You are required to set it up before you are able to apply for a
                loan, send or receive any funds, or add any personal information.
              </TextReg>
              <Button
                style={{
                  backgroundColor: '#00FFBD',
                  marginTop: 10,
                  marginBottom: 20,
                }}
                onPress={() => this.showSetup()}>
                <TextBold
                  style={{
                    color: '#000',
                    fontSize: 17,
                    marginBottom: -2,
                    marginTop: -2,
                  }}>
                  SETUP 2FA
                </TextBold>
              </Button>
            </View>
          </Card>
        )}
        {this.state.activeReset && <TextReg style={{fontSize: 17, color: '#de4a2e'}}>Reset 2FA Active</TextReg>}
        <SetupModal
          showSetup={this.state.showSetup}
          showPinScreen={this.props.showPinScreen}
          closeSetup={this.closeSetup}
          setupNext={this.setupNext}
          step={this.state.step}
          handleUpdateSetupTwoFactor={this.handleUpdateSetupTwoFactor}
          setupTwoFactor={this.state.setupTwoFactor}
          secret={this.state.secret}
          handleUpdateNewTwoFactor={this.handleUpdateNewTwoFactor}
          newTwoFactor={this.state.newTwoFactor}
          setupLoading={this.state.setupLoading}
          setupError={this.state.setupError}
          showCopied={this.state.showCopied}
          copy2FACode={this.copy2FACode}
          handleUpdatePassword={this.handleUpdatePassword}
          loginPassword={this.state.loginPassword}
          setupErrorPassword={this.state.setupErrorPassword}
          rateLimitError={this.state.rateLimitError}
          timer={this.state.timer}
        />
        <ChangeDeviceModal
          showChangeDevice={this.state.showChangeDevice}
          showPinScreen={this.props.showPinScreen}
          closeChangeDevice={this.closeChangeDevice}
          changeDeviceNext={this.changeDeviceNext}
          changeStep={this.state.changeStep}
          handleUpdateTwoFactor={this.handleUpdateTwoFactor}
          handleUpdateNewTwoFactor={this.handleUpdateNewTwoFactor}
          currentTwoFactor={this.state.currentTwoFactor}
          secret={this.state.secret}
          newTwoFactor={this.state.newTwoFactor}
          handleChangeStep0={this.handleChangeStep0}
          changeLoading={this.state.changeLoading}
          showCopied={this.state.showCopied}
          copy2FACode={this.copy2FACode}
          changeError={this.state.changeError}
        />
      </View>
    )
  }
}

TwoFactor.navigationOptions = ({navigation}) => {
  let tabBarVisible = true
  if (route.params && route.params.reset) {
    tabBarVisible = false
  }
  return {
    title: null,
    header: <View style={{height: 40, position: 'absolute'}} />,
    headerBackTitle: null,
    tabBarVisible,
    gesturesEnabled: tabBarVisible,
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  AuthService: state.auth.AuthService,
  loanData: state.user.loanData || {},
  pauseUnit21: state.user.pauseUnit21,
  user: state.user.user,
  account: state.auth.account,
  showPinScreen: state.auth.pinScreen,
})

export default connect(mapStateToProps)(TwoFactor)
