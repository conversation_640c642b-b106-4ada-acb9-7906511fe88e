import React from 'react'
import { Modal, ScrollView, View, Text, Image, TouchableOpacity, TextInput } from 'react-native'

import { TextReg, Background, HeaderButtons, Button } from '../../../components'

import styles from '../styles'

const VerifyEmailModal = (props) => {
  const highlightInput = props.textInputActive ? props.code.length : false

  const codeValid = props.code.length === 6

  return (
    <Modal
      animationType="slide"
      transparent
      visible={props.showCodeScreen && !props.showPinScreen}
      onRequestClose={() => ({})}
    >
      <Background />
      <ScrollView
        style={{
          alignSelf: 'stretch',
          flexDirection: 'column',
          paddingTop: 20
        }}
        contentContainerStyle={{
          justifyContent: 'flex-start',
          alignItems: 'center'
        }}
      >
        <HeaderButtons
          title={props.showPrimary ? 'Make Email Primary' : 'Verify Email Address'}
          close={props.closeModal}
        />

        <TextReg style={styles.changeEmailInputTitle}>
          {props.showPrimary ? `Two-Factor Code` : `Verification Code`}
        </TextReg>

        <TextInput
          style={styles.signUpTextInputEmailVerificationCode}
          onChangeText={props.handleVerificationCode}
          returnKeyType={'done'}
          value={props.code}
          ref={(input) => (props.inputs.verificationInput = input)}
          onSubmitEditing={props.handleSubmitEmailVerification}
          keyboardType={'numeric'}
          onFocus={props.focusTextInput}
          onBlur={props.blurTextInput}
          keyboardAppearance="dark"
        />
        <TouchableOpacity activeOpacity={1} onPress={() => props.inputs.verificationInput.focus()}>
          <View style={styles.signUpVerifcationBlocksBox}>
            <View style={[styles.signUpVerifcationBlock, highlightInput === 0 && styles.signUpVerifcationBlockActive]}>
              <TextReg style={styles.signUpVerifcationBlockText}>{props.showBlock(0)}</TextReg>
            </View>
            <View style={[styles.signUpVerifcationBlock, highlightInput === 1 && styles.signUpVerifcationBlockActive]}>
              <TextReg style={styles.signUpVerifcationBlockText}>{props.showBlock(1)}</TextReg>
            </View>
            <View style={[styles.signUpVerifcationBlock, highlightInput === 2 && styles.signUpVerifcationBlockActive]}>
              <TextReg style={styles.signUpVerifcationBlockText}>{props.showBlock(2)}</TextReg>
            </View>
            <View style={[styles.signUpVerifcationBlock, highlightInput === 3 && styles.signUpVerifcationBlockActive]}>
              <TextReg style={styles.signUpVerifcationBlockText}>{props.showBlock(3)}</TextReg>
            </View>
            <View style={[styles.signUpVerifcationBlock, highlightInput === 4 && styles.signUpVerifcationBlockActive]}>
              <TextReg style={styles.signUpVerifcationBlockText}>{props.showBlock(4)}</TextReg>
            </View>
            <View style={[styles.signUpVerifcationBlock, highlightInput === 5 && styles.signUpVerifcationBlockActive]}>
              <TextReg style={styles.signUpVerifcationBlockText}>{props.showBlock(5)}</TextReg>
            </View>
          </View>
        </TouchableOpacity>

        <Button
          style={{
            marginBottom: 20,
            opacity: !codeValid ? 0.5 : 1
          }}
          disabled={!codeValid}
          onPress={props.sendVerifyEmail}
          isLoading={props.verifyLoading}
          theme={'secondary'}
        >
          SUBMIT
        </Button>
        {props.error && (
          <View>
            <TextReg style={{ color: '#fff' }}>{props.error}</TextReg>
          </View>
        )}
      </ScrollView>
    </Modal>
  )
}

export default VerifyEmailModal
