import React, {Component} from 'react';
import {View, Image, Linking} from 'react-native';
import {connect} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {Button, TextReg, BackgroundHeader} from '../../../components';
import commonStyles from '../../../styles/commonStyles';
import {updateUpholdHide} from '../../../store/user/user.actions';

class AccountDeletion extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  openEmail = () => {
    Linking.openURL(`mailto:<EMAIL>?subject=Account%20Deletion`);
  };

  render() {
    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Account Deletion'} goBack={this.props.navigation.goBack} />
        <View
          style={{
            alignSelf: 'stretch',
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: 40,
          }}>
          <Image
            source={require('../../../imgs/graphics/loanPending.png')}
            style={{
              width: 72,
              height: 77,
              marginBottom: 30,
            }}
          />
          <TextReg
            style={{
              textAlign: 'center',
              width: 300,
              marginBottom: 40,
              color: '#FFF',
              fontSize: 16,
            }}>
            {`If you need or want to delete your account for any reason, <NAME_EMAIL> with the subject line "Account Deletion" and request for your account to be deleted.  Verification may be requested for account deletion."`}
          </TextReg>

          <Button onPress={() => this.openEmail()}>Email</Button>
        </View>
      </View>
    );
  }
}

AccountDeletion.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
});

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  account: state.auth.account,
  showPinScreen: state.auth.pinScreen,
});

export default connect(mapStateToProps)(AccountDeletion);
