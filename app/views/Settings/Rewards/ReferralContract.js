import React, {Component} from 'react';
import {View, Dimensions} from 'react-native';
import {connect} from 'react-redux';
import {WebView} from 'react-native-webview';

import {BackgroundHeader, Card, TextBold, Button} from '../../../components';

const windowWidth = Dimensions.get('window').width;
import commonStyles from '../../../styles/commonStyles';
import styles from '../styles';

class ReferralContract extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }
  goBack = () => {
    this.props.navigation.goBack();
  };

  render() {
    console.log('contract props', this.props);
    const {contractStyling, bodyDecoded} = this.props?.route?.params;
    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Referrals'} goBack={this.goBack} />
        <View
          style={{
            alignSelf: 'stretch',
            alignItems: 'center',
            backgroundColor: '#FFF',
            flex: 1,
          }}>
          <WebView
            source={{
              html: `<div>${contractStyling} <div class="ps-contract-body ps-contract-full ps-contract">${bodyDecoded}</div></div>`,
            }}
            style={{height: 360, width: windowWidth}}
            useWebKit
          />
        </View>
      </View>
    );
  }
}

ReferralContract.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
});

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  showPinScreen: state.auth.pinScreen,
});

export default connect(mapStateToProps, {})(ReferralContract);
