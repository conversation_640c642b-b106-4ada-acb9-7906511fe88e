import React, {Component} from 'react'
import {
  View,
  Image,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Clipboard,
  Linking,
} from 'react-native'
import {WebView} from 'react-native-webview'
import {connect} from 'react-redux'
import {decode} from 'html-entities'

import {updateReferrals} from '../../../store/user/user.actions'
import {BackgroundHeader, TextReg, TextBold, Button} from '../../../components'
import commonStyles from '../../../styles/commonStyles'
import config from '../../../config.json'
import styles from '../styles'
import referBanner from '../../../imgs/graphics/referBanner.png'
import twitter from '../../../imgs/referrals/twitter.png'
import facebook from '../../../imgs/referrals/fb.png'
import instagram from '../../../imgs/referrals/in.png'

const windowWidth = Dimensions.get('window').width

class Referrals extends Component {
  constructor(props) {
    super(props)
    this.state = {
      tosChecked: false,
      referralCode: null,
      contract: {},
      loaded: false,
      openedContract: false,
      showCopied: false,
      showCopied2: false,
    }
  }

  componentDidMount() {
    this.checkReferralSigned()
  }

  openContract = (contractStyling, bodyDecoded) => {
    this.props.navigation.navigate('ReferralContract', {
      contractStyling,
      bodyDecoded,
    })
  }

  goTo = site => {
    if (site == 'twitter') {
      Linking.openURL('https://twitter.com/')
    }
    if (site == 'facebook') {
      Linking.openURL('https://facebook.com/')
    }
    if (site == 'instagram') {
      Linking.openURL('https://instagram.com/')
    }
  }

  checkReferralSigned = () => {
    const data = this.props.user
    console.log('checkReferralSigned', data)

    if (data.referralCode) {
      const {countryCode, province} = data?.address
      this.props.WebService.getUnsignedAgreement(
        'referral-terms',
        countryCode,
        province,
        config.pactSafe.accessId,
      ).then(res => {
        if (res.data.title === 'Referral Terms and Conditions') {
          this.props.dispatch(updateReferrals(false, res.data))
        } else {
          this.props.dispatch(updateReferrals(true))
        }
      })
    }
  }

  sign = () => {
    this.setState({loading: true})
    const {contract} = this.props.referrals
    this.props.WebService.signAgreement({
      contractId: contract.contract.toString(),
      versionId: contract.id,
      siteId: config.pactSafe.accessId,
    })
      .then(res => {
        this.setState({referralCode: this.props.user.referralCode, loading: false})
        this.props.dispatch(updateReferrals(true))
      })
      .catch(err => {
        this.setState({loading: false})
      })
  }

  toggleCheckmark = () => {
    this.setState({tosChecked: !this.state.tosChecked})
  }

  copyCode = (referralCode, linkNum) => {
    Clipboard.setString(referralCode)
    if (linkNum == 2) {
      this.setState({showCopied2: true}, () => {
        setTimeout(() => {
          this.setState({showCopied2: false})
        }, 1400)
      })
    } else {
      this.setState({showCopied: true}, () => {
        setTimeout(() => {
          this.setState({showCopied: false})
        }, 1400)
      })
    }
  }

  openTerms = () => {
    //Linking.openURL('https://saltlending.com/terms-and-conditions')
    this.props.navigation.navigate('ReferralTos')
  }

  render() {
    const referralCode = this.props.user?.referralCode || ''
    const referralLink = `https://borrower.saltlending.com/register?referralCode=${referralCode}`

    const bodyDecoded = this.props.referrals?.contract?.body
      ? decode(this.props.referrals.contract.body)
      : ''

    const contractStyling = `<style>
    div{
      overflow-x: hidden;
    }
    .ps-contract-body{
      margin-top: -6px;
    }
    .ps-section{
      margin-top: 10px;
      margin-bottom: 10px;
    }
    </style>`

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Referral Program'} goBack={this.props.navigation.goBack} />
        {this.props.referrals.signed ? (
          <ScrollView
            style={{
              alignSelf: 'stretch',
              backgroundColor: '#28283D',
              flex: 1,
            }}
            contentContainerStyle={{alignItems: 'center'}}>
            <Image
              source={referBanner}
              style={{height: 200, width: windowWidth - 20, marginBottom: 14, borderRadius: 10}}
            />
            <View style={{alignSelf: 'stretch', marginLeft: 14, marginRight: 14}}>
              <TextBold style={{marginTop: 8, fontSize: 24, color: '#FFF'}}>
                Welcome to our SALT Referral Program
              </TextBold>
              <TextReg style={{color: '#EFEFEF', fontSize: 16, marginTop: 10}}>
                {`Simply copy and share your referral code or link with anyone in your network!`}
              </TextReg>
              <TextReg style={{color: '#EFEFEF', fontSize: 16, marginTop: 10}}>
                {`When they create a SALT account, then secure an active SALT loan, we’ll send $50 in Bitcoin to both of you. So easy!`}
              </TextReg>
              <TextReg
                style={{
                  color: '#EFEFEF',
                  marginTop: 20,
                  alignSelf: 'flex-start',
                }}>{`Your referral code`}</TextReg>
              <View
                style={{
                  borderRadius: 8,
                  backgroundColor: '#3D3D50',
                  width: windowWidth - 30,
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginTop: 6,
                  borderWidth: 1,
                  borderColor: '#999',
                  overflow: 'hidden',
                }}>
                <TextReg
                  style={{
                    flex: 1,
                    marginLeft: 16,
                    marginRight: 16,
                    height: 48,
                    marginTop: 2,
                    paddingTop: 12,
                    fontSize: 17,
                  }}>{`${referralCode}`}</TextReg>
                <View
                  style={{
                    height: 50,
                    width: 50,
                    backgroundColor: this.state.showCopied ? '#00ffc1' : '#00FFBD',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  {this.state.showCopied ? (
                    <Image
                      style={{height: 32, width: 32}}
                      source={require('../../../imgs/checkmark.png')}
                    />
                  ) : (
                    <TouchableOpacity onPress={() => this.copyCode(referralCode)}>
                      <Image
                        style={{height: 20, width: 20}}
                        source={require('../../../imgs/copyDepositButton.png')}
                      />
                    </TouchableOpacity>
                  )}
                </View>
              </View>
              <TextReg style={{color: '#EFEFEF', marginTop: 20, alignSelf: 'flex-start'}}>
                Your referral link
              </TextReg>
              <View
                style={{
                  borderRadius: 8,
                  backgroundColor: '#3D3D50',
                  width: windowWidth - 30,
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginTop: 6,
                  borderWidth: 1,
                  borderColor: '#999',
                  overflow: 'hidden',
                }}>
                <TextReg
                  style={{
                    flex: 1,
                    marginLeft: 16,
                    marginRight: 2,
                    height: 48,
                    marginTop: 2,
                    paddingTop: 12,
                    fontSize: 17,
                  }}>{`${referralLink}`}</TextReg>
                <View
                  style={{
                    height: 50,
                    width: 50,
                    backgroundColor: this.state.showCopied2 ? '#00ffc1' : '#00FFBD',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  {this.state.showCopied2 ? (
                    <Image
                      style={{height: 32, width: 32}}
                      source={require('../../../imgs/checkmark.png')}
                    />
                  ) : (
                    <TouchableOpacity onPress={() => this.copyCode(referralLink, 2)}>
                      <Image
                        style={{height: 20, width: 20}}
                        source={require('../../../imgs/copyDepositButton.png')}
                      />
                    </TouchableOpacity>
                  )}
                </View>
              </View>
              <TextReg
                style={{
                  color: '#EFEFEF',
                  marginTop: 30,
                  alignSelf: 'flex-start',
                }}>{`Share your link to social media`}</TextReg>
              <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 16}}>
                <TouchableOpacity onPress={() => this.goTo('twitter')}>
                  <Image source={twitter} style={{height: 44, width: 44, marginRight: 16}} />
                </TouchableOpacity>
                <TouchableOpacity onPress={() => this.goTo('facebook')}>
                  <Image source={facebook} style={{height: 44, width: 44, marginRight: 16}} />
                </TouchableOpacity>
                <TouchableOpacity onPress={() => this.goTo('instagram')}>
                  <Image source={instagram} style={{height: 44, width: 44}} />
                </TouchableOpacity>
              </View>
              <TouchableOpacity onPress={() => this.openTerms()}>
                <TextReg
                  style={{
                    color: '#00FFBD',
                    marginTop: 16,
                    marginBottom: 60,
                    fontSize: 16,
                  }}>{`Terms and Conditions`}</TextReg>
              </TouchableOpacity>
            </View>
          </ScrollView>
        ) : (
          <View style={{flex: 1, alignItems: 'center'}}>
            <Image
              source={referBanner}
              style={{height: 200, width: windowWidth - 20, marginBottom: 14, borderRadius: 10}}
            />
            <View
              style={{
                alignSelf: 'stretch',
                marginLeft: 14,
                backgroundColor: '#28283D',
                marginBottom: 20,
              }}>
              <TextBold style={{marginTop: 2, fontSize: 24, color: '#FFF'}}>
                Welcome to our SALT Referral Program
              </TextBold>
              <TextReg style={{color: '#FFF', fontSize: 15, marginTop: 8}}>
                Before participating, please review and accept the Terms and Conditions below:
              </TextReg>
            </View>

            <View
              style={{
                alignSelf: 'stretch',
                alignItems: 'center',
                backgroundColor: '#FFF',
                flex: 1,
                borderWidth: 0,
                borderColor: '#28283D',
                borderLeftWidth: 10,
                borderRightWidth: 10,
              }}>
              <TouchableOpacity
                onPress={() => this.openContract(contractStyling, bodyDecoded)}
                style={{flex: 1, alignSelf: 'stretch', alignItems: 'center'}}>
                <WebView
                  source={{
                    html: `<div>${contractStyling} <div class="ps-contract-body ps-contract-full ps-contract">${bodyDecoded}</div></div>`,
                  }}
                  style={{height: 330, width: windowWidth - 20}}
                  useWebKit
                />
              </TouchableOpacity>
            </View>

            <View style={{alignSelf: 'stretch'}}>
              <TouchableOpacity onPress={() => this.toggleCheckmark()}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    alignSelf: 'stretch',
                    marginTop: 14,
                    marginBottom: 14,
                  }}>
                  {this.state.tosChecked ? (
                    <Image
                      source={require('../../../imgs/referrals/checkedBox.png')}
                      style={{height: 28, width: 28, marginLeft: 20, marginRight: 14}}
                    />
                  ) : (
                    <Image
                      source={require('../../../imgs/referrals/unchecked.png')}
                      style={{height: 28, width: 28, marginLeft: 20, marginRight: 14}}
                    />
                  )}
                  <TextReg style={{color: '#FFF', fontSize: 12, width: 290}}>
                    I have read, understand, and consent to the language and authorizations outlined
                    in SALT’s Referral Terms and Conditions.
                  </TextReg>
                </View>
              </TouchableOpacity>
              <Button
                isLoading={this.state.loading}
                disabled={!this.state.tosChecked}
                style={{marginBottom: 20, width: windowWidth - 40, borderRadius: 10}}
                onPress={() => this.sign()}>
                SUBMIT
              </Button>
            </View>
          </View>
        )}
      </View>
    )
  }
}

Referrals.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  //headerTintColor: '#FFF',
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    //shadowColor: 'transparent',
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  referrals: state.user.referrals,
})

export default connect(mapStateToProps)(Referrals)
