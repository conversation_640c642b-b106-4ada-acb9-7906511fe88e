import React, {Component} from 'react'
import {View, ScrollView, TouchableOpacity, Image, Dimensions, Platform, BackHandler} from 'react-native'
import {connect} from 'react-redux'

import SwiperFlatList from 'react-native-swiper-flatlist'
import {AnimatedCircularProgress} from 'react-native-circular-progress'

import {TextReg, BackgroundHeader, Card, TextBold, Button} from '../../../components'
import {updateLoansDrill, updateLoansInOneUser} from '../../../store/user/user.actions'
import {numberWithCommas} from '../../../util/helpers'
import commonStyles from '../../../styles/commonStyles'
const {width: ScreenWidth, height: ScreenHeight} = Dimensions.get('window')
import styles from '../styles'

class StackwiseRewards extends Component {
  constructor(props) {
    super(props)
    const rewardPreference = props.loanData?.reward?.rewardPreference || 'BTC'
    this.state = {
      rewardPreference,
      timer: 0,
    }
    this.tick = null
    this.backAction = this.backAction.bind(this)
  }
  componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.backAction)
  }

  componentWillUnmount() {
    clearInterval(this.tick)
    //BackHandler.removeEventListener('hardwareBackPress', this.backAction);
  }

  backAction = () => {
    this.checkGoBack()
    return true
  }

  onTick = () => {
    if (this.state.timer >= 4) {
      this.sendRewardChange()
    } else {
      this.setState({timer: this.state.timer + 1})
    }
  }

  changeReward = rewardPreference => {
    /*
    this.setState({rewardPreference})
    if (this.state.timer > 0) {
      this.setState({timer: 0});
    } else {
      clearInterval(this.tick);
      this.tick = setInterval(this.onTick, 1000);
    }
    this.sendRewardChange()
    */

    this.setState({rewardPreference}, () => this.sendRewardChange())
  }

  sendRewardChange = () => {
    clearInterval(this.tick)
    this.setState({timer: 0})
    const loanID = this.props.loanData.id
    this.props.WebService.updateRewardPreference(loanID, {
      newCurrencyPreference: this.state.rewardPreference,
    })
      .then(res => {
        let reward = {
          ...this.props?.loanData?.reward,
          rewardPreference: this.state.rewardPreference,
        }
        let loanData = {
          ...this.props?.loanData,
          reward,
        }
        this.props.dispatch(updateLoansDrill(loanData))

        let {accountRef} = this.props
        this.props.dispatch(updateLoansInOneUser(loanData, accountRef))
      })
      .catch(err => {
        console.log('updateRewardPreference err', err)
      })
  }

  openSchedule = () => {
    this.refs.swiper._scrollToIndex(1)
  }
  closeSchedule = () => {
    this.refs.swiper._scrollToIndex(0)
  }

  checkGoBack = () => {
    if (this.refs.swiper?.state?.paginationIndex == 1) {
      this.refs.swiper._scrollToIndex(0)
    } else {
      this.props.navigation.goBack()
    }
  }

  render() {
    console.log('stackwise rewards', this.props)
    const {rewardPreference} = this.state
    const {loanData} = this.props
    const btcButtonImg =
      rewardPreference == 'BTC' ? require('../../../imgs/stackWise/btc_dark.png') : require('../../../imgs/stackWise/btc_grey.png')

    const ethButtonImg =
      rewardPreference == 'ETH' ? require('../../../imgs/stackWise/eth_dark.png') : require('../../../imgs/stackWise/eth_grey.png')

    const usdcButtonImg =
      rewardPreference == 'USDC' ? require('../../../imgs/stackWise/usdc_dark.png') : require('../../../imgs/stackWise/usdc_grey.png')

    let stackwiseTotalRewards = 0
    let stackwiseLocked = 0
    if (loanData?.reward?.rewardSchedule?.length > 0) {
      loanData?.reward?.rewardSchedule.map(a => {
        if (a.earned) {
          stackwiseTotalRewards += Number(a.rewardInUsd)
        } else {
          stackwiseLocked += Number(a.rewardInUsd)
        }
      })
    }
    stackwiseTotalRewards = stackwiseTotalRewards.toFixed(2)
    stackwiseLocked = stackwiseLocked.toFixed(2)
    const totalRewards = (Number(stackwiseTotalRewards) + Number(stackwiseLocked)).toFixed(2)
    const showTotalRewards = numberWithCommas(totalRewards)
    const showTotalEarned = numberWithCommas(stackwiseTotalRewards)
    const showTotalLocked = numberWithCommas(stackwiseLocked)
    const showSaltRedeemed = numberWithCommas(loanData.saltRedeemed || 0)
    const showRewardRate = ((loanData.reward?.rewardRate || 0) * 100).toFixed(2)
    const currentDisbursementPeriod = loanData.reward?.currentDisbursementPeriod
    /*
		let nextReward =
			loanData.reward?.rewardSchedule[currentDisbursementPeriod - 1] || 0
      */
    let nextReward = Number(loanData.reward?.nextRewardAmount) || 0
    nextReward = numberWithCommas(nextReward.toFixed(2))

    let completedAmount = currentDisbursementPeriod - 1 || 0
    let totalAmount = loanData.reward?.totalDisbursementPeriods || 0

    const rewardSchedule = loanData.reward?.rewardSchedule || []

    const showSchedule = rewardSchedule.map((a, k) => {
      let backgroundColor = '#505061'
      if (k % 2 == 0) {
        backgroundColor = '#3D3D50'
      }

      let showCryptoAmount = Number(a.rewardInCrypto).toFixed(8)
      if (a.currency == 'USDC') {
        showCryptoAmount = Number(a.rewardInCrypto).toFixed(6)
      }
      let earnedString = 'locked'
      if (a.earned) {
        earnedString = 'earned'
      } else {
        showCryptoAmount = '?'
      }
      let myDate = new Date(a.date)
      if (Platform.OS === 'ios') {
        myDate = myDate.toLocaleString()
        myDate = myDate.split(',')[0]
      } else {
        let year = myDate.getFullYear()
        let month = (1 + myDate.getMonth()).toString()
        month = month.length > 1 ? month : '0' + month
        let day = myDate.getDate().toString()
        day = day.length > 1 ? day : '0' + day
        myDate = month + '/' + day + '/' + year
      }
      let showCurrency = earnedString == 'earned' ? a.currency : rewardPreference
      return (
        <View
          key={k}
          style={{
            alignSelf: 'stretch',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            height: 60,
            paddingLeft: 10,
            paddingRight: 10,
            backgroundColor,
            opacity: earnedString == 'earned' ? 1 : 0.8,
          }}>
          <View>
            <TextReg style={{marginBottom: 4}}>{myDate}</TextReg>
            <TextReg>{earnedString}</TextReg>
          </View>
          <View style={{alignItems: 'flex-end'}}>
            <TextBold style={{marginBottom: 4}}>{a.rewardInUsd}</TextBold>
            <View style={{flexDirection: 'row'}}>
              <TextReg style={{marginRight: 2}}>{showCryptoAmount}</TextReg>
              <TextReg>{showCurrency}</TextReg>
            </View>
          </View>
        </View>
      )
    })

    const totalDisbursementPeriods = loanData.reward?.totalDisbursementPeriods || 0

    const currentDisbursmentsCalc = loanData.reward?.rewardSchedule?.filter(a => a.earned).length
    let progressFill = (currentDisbursmentsCalc / totalDisbursementPeriods) * 100
    if (isNaN(progressFill)) {
      progressFill = 0
    }
    //const sectionWidth = 360 / totalDisbursementPeriods;
    //gaps not working library is weird
    //maybe its possible to justoverlay a dynamic gap image
    //dashedTint={{ width: sectionWidth - 4, gap: 4 }}
    //dashedBackground={{ width: sectionWidth - 4, gap: 4 }}

    let useRewardAsPayment = this.props.loanData?.reward?.useRewardAsPayment

    let stackwiseText =
      'Stackwise refunds a portion of your montly payment back to your wallet in the form of rewards. Choose the currency you would like to receive your rewards in and get paid every time you make a payment.'

    if (this.props.loanData?.reward?.useRewardAsPayment) {
      stackwiseText =
        'Stackwise refunds a portion of your monthly payment back in the form of rewards. You will get paid every time you make a payment.'
    }
    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Stackwise'} goBack={() => this.checkGoBack()} />
        <SwiperFlatList
          ref="swiper"
          onMomentumScrollEnd={this.onScrollEnd}
          index={this.state.slideIndex}
          scrollEventThrottle={16}
          showPagination={false}
          scrollEnabled>
          <View
            style={{
              alignSelf: 'stretch',
              width: ScreenWidth,
              alignItems: 'center',
              flex: 1,
            }}>
            <ScrollView style={{alignSelf: 'stretch', paddingTop: 10}} contentContainerStyle={{alignItems: 'center'}}>
              <Card>
                <Image
                  source={require('../../../imgs/stackWise/logoWhite.png')}
                  style={{
                    height: 46,
                    width: 100,
                    alignSelf: 'center',
                    marginTop: 18,
                  }}
                />
                <View style={{position: 'relative', marginTop: 4}}>
                  <AnimatedCircularProgress
                    style={{marginTop: 18}}
                    size={140}
                    width={18}
                    fill={progressFill}
                    rotation={0}
                    tintTransparency={false}
                    tintColor="#00FFBD"
                    onAnimationComplete={() => console.log('onAnimationComplete')}
                    backgroundColor="#777784"
                  />
                  <View style={{position: 'absolute', top: 63, left: 38}}>
                    <TextBold
                      style={{
                        color: '#FFF',
                        fontSize: 22,
                        textAlign: 'right',
                        width: 30,
                      }}>
                      {completedAmount}
                    </TextBold>
                  </View>
                  <View style={{position: 'absolute', top: 86, left: 54}}>
                    <View
                      style={{
                        borderBottomWidth: 2,
                        borderBottomColor: '#FFF',
                        width: 34,
                        transform: [{rotate: '-52deg'}],
                      }}
                    />
                  </View>
                  <View style={{position: 'absolute', top: 82, left: 72}}>
                    <TextBold style={{color: '#FFF', fontSize: 22}}>{totalAmount}</TextBold>
                  </View>
                </View>
                <View style={{flexDirection: 'row', marginTop: 18}}>
                  <View
                    style={{
                      flexDirection: 'column',
                      alignItems: 'center',
                      marginRight: 30,
                    }}>
                    <TextReg style={{fontSize: 20, color: '#FFF'}}>EARNED</TextReg>
                    <TextBold style={{fontSize: 22, color: '#FFF'}}>{`$${showTotalEarned}`}</TextBold>
                  </View>
                  <View style={{flexDirection: 'column', alignItems: 'center'}}>
                    <TextReg style={{fontSize: 20, color: '#FFF'}}>LOCKED</TextReg>
                    <TextBold style={{fontSize: 22, color: '#FFF'}}>{`$${showTotalLocked}`}</TextBold>
                  </View>
                </View>
                <Button
                  style={{
                    alignSelf: 'stretch',
                    marginTop: 20,
                    marginLeft: 10,
                    marginRight: 10,
                  }}
                  onPress={() => this.openSchedule()}>
                  <TextBold style={{color: '#000'}}>VIEW SCHEDULE</TextBold>
                </Button>
                {useRewardAsPayment ? (
                  <TextReg
                    style={{
                      marginTop: 16,
                      marginLeft: 12,
                      marginRight: 12,
                      alignSelf: 'flex-start',
                    }}>{`The next StackWise reward of $${nextReward} will be applied to your monthly payment.`}</TextReg>
                ) : (
                  <>
                    <TextReg
                      style={{
                        alignSelf: 'flex-start',
                        marginLeft: 10,
                        marginTop: 24,
                        fontSize: 16,
                        color: '#FFF',
                      }}>
                      Receive Rewards In:
                    </TextReg>
                    <View style={{flexDirection: 'row', marginTop: 6}}>
                      <TouchableOpacity
                        style={[
                          styles.stackwiseRewardTokenButton,
                          {
                            backgroundColor: rewardPreference == 'BTC' ? '#00FFBD' : '#E3E6ED',
                          },
                        ]}
                        onPress={() => this.changeReward('BTC')}>
                        <Image
                          source={btcButtonImg}
                          style={{
                            height: 25,
                            width: 20,
                            alignSelf: 'center',
                            marginRight: 10,
                          }}
                        />
                        <TextBold
                          style={{
                            fontSize: 20,
                            color: rewardPreference == 'BTC' ? '#000' : '#AFAFAF',
                          }}>
                          BTC
                        </TextBold>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[
                          styles.stackwiseRewardTokenButton,
                          {
                            backgroundColor: rewardPreference == 'ETH' ? '#00FFBD' : '#E3E6ED',
                          },
                        ]}
                        onPress={() => this.changeReward('ETH')}>
                        <Image
                          source={ethButtonImg}
                          style={{
                            height: 27,
                            width: 14,
                            alignSelf: 'center',
                            marginRight: 10,
                          }}
                        />
                        <TextBold
                          style={{
                            fontSize: 20,
                            color: rewardPreference == 'ETH' ? '#000' : '#AFAFAF',
                          }}>
                          ETH
                        </TextBold>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[
                          styles.stackwiseRewardTokenButton,
                          {
                            backgroundColor: rewardPreference == 'USDC' ? '#00FFBD' : '#E3E6ED',
                          },
                        ]}
                        onPress={() => this.changeReward('USDC')}>
                        <Image
                          source={usdcButtonImg}
                          style={{
                            height: 24,
                            width: 24,
                            alignSelf: 'center',
                            marginRight: 4,
                          }}
                        />
                        <TextBold
                          style={{
                            fontSize: 20,
                            color: rewardPreference == 'USDC' ? '#000' : '#AFAFAF',
                          }}>
                          USDC
                        </TextBold>
                      </TouchableOpacity>
                    </View>
                  </>
                )}
                <View
                  style={{
                    alignSelf: 'stretch',
                    height: 0.5,
                    backgroundColor: '#AFAFAF',
                    marginTop: 20,
                    marginLeft: 10,
                    marginRight: 10,
                  }}
                />
                <TextReg
                  style={{
                    fontSize: 24,
                    alignSelf: 'flex-start',
                    marginLeft: 10,
                    marginTop: 20,
                    color: '#FFF',
                  }}>
                  Details
                </TextReg>
                <TextReg
                  style={{
                    fontSize: 18,
                    alignSelf: 'flex-start',
                    marginLeft: 10,
                    marginTop: 20,
                    color: '#FFF',
                  }}>
                  {stackwiseText}
                </TextReg>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignSelf: 'stretch',
                    marginLeft: 10,
                    marginRight: 10,
                    marginTop: 30,
                  }}>
                  <TextReg style={{fontSize: 18, color: '#FFF'}}>Next Reward</TextReg>
                  <TextBold style={{fontSize: 20, color: '#FFF'}}>{`$${nextReward}`}</TextBold>
                </View>
                <View
                  style={{
                    alignSelf: 'stretch',
                    height: 0.5,
                    backgroundColor: '#AFAFAF',
                    marginTop: 10,
                    marginLeft: 10,
                    marginRight: 10,
                  }}
                />
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignSelf: 'stretch',
                    marginLeft: 10,
                    marginRight: 10,
                    marginTop: 10,
                  }}>
                  <TextReg style={{fontSize: 18, color: '#FFF'}}>Total Rewards</TextReg>
                  <TextBold style={{fontSize: 20, color: '#FFF'}}>{`$${showTotalRewards}`}</TextBold>
                </View>
                <View
                  style={{
                    alignSelf: 'stretch',
                    height: 0.5,
                    backgroundColor: '#AFAFAF',
                    marginTop: 10,
                    marginLeft: 10,
                    marginRight: 10,
                  }}
                />
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignSelf: 'stretch',
                    marginLeft: 10,
                    marginRight: 10,
                    marginTop: 10,
                  }}>
                  <TextReg style={{fontSize: 18, color: '#FFF'}}>Rewards Rate</TextReg>
                  <TextBold style={{fontSize: 20, color: '#FFF'}}>{`${showRewardRate}%`}</TextBold>
                </View>
                <View
                  style={{
                    alignSelf: 'stretch',
                    height: 0.5,
                    backgroundColor: '#AFAFAF',
                    marginTop: 10,
                    marginLeft: 10,
                    marginRight: 10,
                  }}
                />
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignSelf: 'stretch',
                    marginLeft: 10,
                    marginRight: 10,
                    marginTop: 10,
                    marginBottom: 30,
                  }}>
                  <TextReg style={{fontSize: 18, color: '#FFF'}}>Salt Redeemed</TextReg>
                  <TextBold style={{fontSize: 20, color: '#FFF'}}>{showSaltRedeemed}</TextBold>
                </View>
              </Card>
            </ScrollView>
          </View>
          <View
            style={{
              alignSelf: 'stretch',
              width: ScreenWidth,
              alignItems: 'center',
              flex: 1,
            }}>
            <ScrollView style={{alignSelf: 'stretch', paddingTop: 10}} contentContainerStyle={{alignItems: 'center'}}>
              <Card>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    alignSelf: 'stretch',
                    marginLeft: 10,
                    marginRight: 10,
                    marginBottom: 20,
                  }}>
                  <View style={{height: 30, width: 30}} />
                  <TextReg style={{fontSize: 24}}>Reward Schedule</TextReg>
                  <TouchableOpacity
                    onPress={() => {
                      this.closeSchedule()
                    }}>
                    <Image
                      source={require('../../../imgs/notifClose.png')}
                      style={{
                        height: 30,
                        width: 30,
                        alignSelf: 'center',
                        opacity: 0.5,
                      }}
                    />
                  </TouchableOpacity>
                </View>
                {showSchedule}
              </Card>
            </ScrollView>
          </View>
        </SwiperFlatList>
      </View>
    )
  }
}

StackwiseRewards.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  //headerTintColor: '#FFF',
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    //shadowColor: 'transparent',
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  loanData: state.user.loanData || {},
  accountRef: state.auth.account.ref,
})

export default connect(mapStateToProps)(StackwiseRewards)
