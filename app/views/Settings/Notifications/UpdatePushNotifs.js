import React from 'react'
import {Modal, View, Text, Image, TouchableOpacity} from 'react-native'

import styles from '../styles'

const UpdatePushNotifs = props => (
  <Modal
    animationType="fade"
    transparent
    visible={props.modalVisable}
    onRequestClose={() => props.closeModal()}>
    <View style={styles.helpModalBox}>
      <View style={styles.helpModalSquare}>
        <TouchableOpacity
          style={styles.helpModalX}
          onPress={() => {
            props.closeModal()
          }}>
          <Image
            source={require('../../../imgs/closeX.png')}
            style={styles.closeModalImg}
          />
        </TouchableOpacity>
        <View style={styles.helpModalTitleBox}>
          <Text style={styles.helpModalTitle}>{props.title}</Text>
        </View>
        <View style={styles.updateSettingsModalDescriptionBox}>
          <Text style={styles.updateSettingsModalDescription}>
            {props.text}
          </Text>
          <TouchableOpacity
            style={styles.helpModalButton}
            onPress={props.closeModal}>
            <Text style={styles.helpModalButtonText}>OK</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  </Modal>
)

export default UpdatePushNotifs
