import React, {Component} from 'react'
import {View, ScrollView, TouchableOpacity, Image, Platform, AppState} from 'react-native'
import {connect} from 'react-redux'
import DropDownPicker from 'react-native-dropdown-picker'
import messaging from '@react-native-firebase/messaging'

import AsyncStorage from '@react-native-async-storage/async-storage'

import {
  changeValue,
  updatePreferences,
  updatePrefID,
} from '../../../store/notifications/notifications.actions'

import Switch from 'react-native-switch-pro'

import {updateActiveTabListener, splitByCamelCase} from '../../../util/helpers'
import {screenView} from '../../../store/analytics/analytics.actions'
import {updatePushNotifPermission} from '../../../store/user/user.actions'

import {askingForPermissions, updateFCMToken} from '../../../store/auth/auth.actions'

import VerifyPhoneModal from '../Phone/VerifyPhoneModal'
import UpdatePushNotifs from './UpdatePushNotifs'

import {TextReg, BackgroundHeader, Card, Button, TextBold} from '../../../components'
import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'

import downArrow from '../../../imgs/downArrow.png'
import upArrow from '../../../imgs/closeX.png'

class UpdateNotificationSettings extends Component {
  constructor(props) {
    super(props)
    this.state = {
      prefArr: [],
      settingsType: '',
      showErr: '',
      headerText: 'Turn notifications on/off for the following events:',
      showVerifyPhoneModal: false,
      showUpdatePushNotifsInSettings: false,
      typeArr: ['push', 'email', 'text', 'call'],
      showType: {
        push: false,
        email: false,
        text: false,
        call: false,
      },
      items: [],
      value: null,
      drop: false,
      pushEnabled: 'enabled',
    }
    this.setValue = this.setValue.bind(this)
  }
  componentDidMount() {
    updateActiveTabListener(this.props.navigation)
    this.props.dispatch(screenView(`Update Notification xyz`))
    this.getNotifPrefs()
    this.checkPush()
    if (this.props.user?.accounts?.length) {
      this.createAccountList()
    }
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange)
  }

  componentWillUnmount() {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove()
    }
  }

  handleAppStateChange = nextAppState => {
    if (nextAppState === 'active') {
      console.log('background -> forground')
      this.checkPush()
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.user !== this.props.user && this.props.user?.accounts?.length) {
      this.createAccountList()
    }
  }

  setValue = callback => {
    this.setState(state => ({
      value: callback(state.value),
    }))
    const value = callback()
    console.log('New value:', value)
    this.getNotifPrefs(value)
    if (this.props.onSelect) {
      this.props.onSelect(value)
    }
  }

  createAccountList = () => {
    let accountList = this.props.user?.accounts
    let thisAcc = accountList?.filter(a => a.ref === this.props.accountRef)
    console.log('accountList', accountList)
    if (thisAcc?.length < 1) {
      console.log('no account')
      thisAcc = accountList[0]
    }

    console.log('thisAcc', thisAcc)

    let items = accountList?.map(a => {
      return {label: a.name, value: a.ref}
    })
    this.setState({items})
    this.setState({value: thisAcc?.ref})
  }

  getNotifPrefs = (value = 1) => {
    this.props.WebService.getNotificationPreferences(value).then(res => {
      const prefs = res.data.preferences
      console.log('ref', value, 'prefs-', prefs, res.data.id)
      this.props.dispatch(updatePreferences(prefs))
      this.props.dispatch(updatePrefID(res.data.id))
    })
  }

  onSwitchChange = item => {
    const field = item.title
    const newValue = !item.value

    if (this.props.pushNotifPermissions === 'denied') {
      // show modal and cannot update
      this.setState({showUpdatePushNotifsInSettings: true})
      return
    } else if (this.props.pushNotifPermissions === 'later') {
      /*
      this.props.dispatch(updatePushNotifPermission('show'))
      return
      */
    }
    this.props.dispatch(changeValue(field, newValue, item.type)).catch(err => {
      this.props.dispatch(changeValue(field, item.value, item.type, false))
    })
  }

  addPhone = item => {
    this.props.navigation.navigate('PhoneNumber', {type: item})
    this.setState({showVerifyPhoneModal: false})
  }

  checkPush = async () => {
    const authStatus = await messaging().hasPermission()

    console.log('checkPush - authStatus', authStatus)
    let pushEnabled = 'enabled'
    if (
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL
    ) {
      pushEnabled = 'enabled'
    }

    if (authStatus === messaging.AuthorizationStatus.DENIED || authStatus === 0) {
      pushEnabled = 'disabled'
    }

    if (authStatus === messaging.AuthorizationStatus.NOT_DETERMINED || authStatus === -1) {
      pushEnabled = 'unknown'
    }
    this.setState({pushEnabled})

    try {
      let notifSettings = await messaging().getNotificationSettings()
      console.log('notifSettings', notifSettings)
    } catch (err) {
      console.log(' notifSettings err', err)
    }
  }

  checkFirebaseToken = async () => {
    await messaging().registerDeviceForRemoteMessages()
    const fcmToken = await messaging().getToken()
    console.log('fcmToken', fcmToken)

    if (fcmToken && fcmToken !== this.props.fcmToken) {
      this.props.WebService.updateFCMTokenAPI(fcmToken)
        .then(res => {
          this.props.dispatch(updateFCMToken(fcmToken))
        })
        .catch(err => {
          console.log('updateFCMTokenAPI err', err)
        })
    }
  }

  requestPushNotifsPermission = async () => {
    this.props.dispatch(askingForPermissions(true))

    console.log('requestPushNotifsPermission')

    const authStatus = await messaging().requestPermission()
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL

    let refArr = this.props.user?.accounts?.map(a => a.ref)
    let pushEnabled = 'enabled'
    if (enabled) {
      this.props.dispatch(askingForPermissions(false))
      this.props.dispatch(updatePushNotifPermission('accepted'))
      AsyncStorage.setItem('SAVED_PUSH_PERMISSION', 'accepted')

      //save fcm
      this.checkFirebaseToken()

      this.setState({pushEnabled: 'enabled'})

      refArr.forEach(async ref => {
        try {
          const res = await this.props.WebService.getNotificationPreferences(ref)
          console.log('res', ref, res)
          const prefs = res.data.preferences
          for (const key in prefs) {
            prefs[key].push.value = true
          }
          const updateData = {
            preferenceId: res.data.id,
            preferences: prefs,
          }

          console.log('updateData', updateData)
          const updateRes = await this.props.WebService.updateNotificationPreferences(updateData)
          console.log('updateRes', updateRes)
          this.getNotifPrefs(ref)
        } catch (err) {
          this.props.dispatch(askingForPermissions(false))
        }
      })

      this.getNotifPrefs(this.state.value)
    } else if (authStatus === messaging.AuthorizationStatus.DENIED || authStatus === 0) {
      pushEnabled = 'disabled'
    } else if (authStatus === messaging.AuthorizationStatus.NOT_DETERMINED || authStatus === -1) {
      pushEnabled = 'unknown'
    } else {
      pushEnabled = 'unknown'
    }
    this.setState({pushEnabled})
  }

  static getDerivedStateFromProps = (newProps, state) => {
    const prefArr = {}

    state.typeArr.map((a, k) => {
      const prefsSubset = newProps.restructuredPrefs[a]

      let indexKey = 0

      if (!prefArr[a]) prefArr[a] = []

      for (const key of Object.keys(prefsSubset || {})) {
        const configurable = prefsSubset[key].configurable
        let value = prefsSubset[key].value

        if (a == 'email') {
          value = true
        }

        const title = key
        const display = splitByCamelCase(key)

        prefArr[a].push({
          configurable,
          title,
          display,
          value,
          type: a,
          key: indexKey.toString(),
        })
        indexKey += 1
      }
    })

    return {
      ...state,
      prefArr,
      //settingsType,
    }
  }

  toggleList = type => {
    const showType = this.state.showType
    showType[type] = !showType[type]
    this.setState({showType})
  }

  render() {
    const {hasVerifiedPhone, navigation} = this.props
    const {prefArr, settingsType} = this.state
    const disabledPhoneText = `To opt into notifictions, please add and verify your phone number.`
    const allowPushNotifsText =
      'Turn on push notifications for the SALT App in your phones settings'
    const showPhoneSwitchesOff =
      !hasVerifiedPhone && (settingsType === 'text' || settingsType === 'call')

    const showSettingsArr = []

    this.state.typeArr.map((a, b) => {
      //remove salt Purchse  && refund
      let filteredArr = prefArr[a]?.filter(b => b.title != 'saltPurchase' && b.title != 'refund')
      showSettingsArr[a] = filteredArr?.map((item, k) => (
        <View key={k} style={{paddingLeft: 10, alignSelf: 'stretch'}}>
          <View style={styles.listItem}>
            <View style={styles.listItemLeft}>
              <TextReg style={styles.listItemTextSub}>{item.display}</TextReg>
            </View>
            <Switch
              disabled={!item.configurable}
              value={item.value && !showPhoneSwitchesOff}
              backgroundActive={!item.configurable ? '#C4F1C6' : '#3BD141'}
              onAsyncPress={cb => {
                if (showPhoneSwitchesOff) {
                  this.setState({showVerifyPhoneModal: true})
                } else {
                  cb(true, () => this.onSwitchChange(item))
                }
              }}
            />
          </View>
        </View>
      ))
    })

    let headerHeight = 50
    if (Platform.OS === 'ios') {
      headerHeight = 88
    }

    let enableSms = false
    if (this.props.launchDarkly['enable-text-message-notifications']) {
      enableSms = true
    }

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Notifications'} goBack={this.props.navigation.goBack} />
        <VerifyPhoneModal
          modalVisable={this.state.showVerifyPhoneModal}
          closeModal={() => this.setState({showVerifyPhoneModal: false})}
          title="Add Phone Number"
          text={disabledPhoneText}
          toggleModal={this.addPhone}
        />
        <UpdatePushNotifs
          modalVisable={this.state.showUpdatePushNotifsInSettings}
          closeModal={() => this.setState({showUpdatePushNotifsInSettings: false})}
          title="Allow Push Notifications"
          text={allowPushNotifsText}
        />

        <ScrollView
          style={{alignSelf: 'stretch', paddingTop: 0}}
          contentContainerStyle={{alignItems: 'center'}}>
          {this.state.pushEnabled !== 'enabled' && (
            <View style={{flexDirection: 'column'}}>
              {this.state.pushEnabled === 'disabled' ? (
                <TextReg style={{fontSize: 16, marginTop: 20, color: '#E5705A'}}>
                  {`Push notifications are currently disabled, please enabled them in your device settings`}
                </TextReg>
              ) : (
                <>
                  <TextReg style={{fontSize: 16, marginTop: 20, color: '#E5705A'}}>
                    {`Push notifications are currently disabled, please enabled them to recieve notifications`}
                  </TextReg>
                  <Button
                    style={{
                      alignSelf: 'stretch',
                      marginTop: 10,
                      backgroundColor: '#28283D',
                      borderColor: '#00FFBD',
                      borderWidth: 1,
                    }}
                    onPress={() => this.requestPushNotifsPermission()}>
                    <TextReg style={{color: '#00FFBD', fontSize: 16}}>
                      Enable Push Notifications
                    </TextReg>
                  </Button>
                </>
              )}
              <View
                style={{
                  alignSelf: 'stretch',
                  marginTop: 20,
                  marginBottom: 10,
                  alignItems: 'center',
                }}>
                <View style={{height: 1, backgroundColor: '#474756', width: 40}} />
              </View>
            </View>
          )}
          <TextReg style={{alignSelf: 'flex-start', marginLeft: 20, fontSize: 16, marginTop: 20}}>
            Account:{' '}
          </TextReg>
          <View style={{padding: 14, paddingTop: 8}}>
            <DropDownPicker
              open={this.state.drop}
              value={this.state.value}
              items={this.state.items}
              setOpen={drop => this.setState({drop})}
              setValue={this.setValue}
              itemKey="key"
              theme="DARK"
              listMode={'MODAL'}
              searchable={false}
              closeAfterSelecting={true}
              placeholder={'select'}
              style={local.drop}
              textStyle={{fontSize: 16, color: '#FFF'}}
              placeholderStyle={{fontSize: 16, color: '#FFF'}}
              translation={{
                NOTHING_TO_SHOW: 'Not Found!',
              }}
            />
          </View>

          <Card style={{paddingBottom: -2, paddingTop: -2}}>
            <TouchableOpacity style={styles.listGroupTitle} onPress={() => this.toggleList('push')}>
              <TextReg style={styles.listItemText}>Push Notifications</TextReg>
              <Image
                source={this.state.showType.push ? upArrow : downArrow}
                style={this.state.showType.push ? styles.rightArrowIcon : styles.downArrowIcon}
              />
            </TouchableOpacity>
            {this.state.showType.push && showSettingsArr.push}
            <TouchableOpacity
              style={styles.listGroupTitle}
              onPress={() => this.toggleList('email')}>
              <TextReg style={styles.listItemText}>Email Notifications</TextReg>
              <Image
                source={this.state.showType.email ? upArrow : downArrow}
                style={this.state.showType.push ? styles.rightArrowIcon : styles.downArrowIcon}
              />
            </TouchableOpacity>
            {this.state.showType.email && showSettingsArr.email}
            {enableSms && (
              <>
                <TouchableOpacity
                  style={styles.listGroupTitle}
                  onPress={() => this.toggleList('text')}>
                  <TextReg style={styles.listItemText}>Text Notifications</TextReg>
                  <Image
                    source={this.state.showType.text ? upArrow : downArrow}
                    style={this.state.showType.push ? styles.rightArrowIcon : styles.downArrowIcon}
                  />
                </TouchableOpacity>
                {this.state.showType.text && showSettingsArr.text}
              </>
            )}

            <TouchableOpacity
              style={styles.listGroupTitleLast}
              onPress={() => this.toggleList('call')}>
              <TextReg style={styles.listItemText}>Call Notifications</TextReg>
              <Image
                source={this.state.showType.call ? upArrow : downArrow}
                style={this.state.showType.push ? styles.rightArrowIcon : styles.downArrowIcon}
              />
            </TouchableOpacity>
            {this.state.showType.call && showSettingsArr.call}
          </Card>
        </ScrollView>
      </View>
    )
  }
}

//{prefArr.length > 0 && showSettingsArr}

/*
<TextReg style={styles.notificationSettingsHeader}>
  {settingsType === 'email'
    ? `Cannot disable Email nofitications`
    : `Turn notifications on/off for the following events:`}
</TextReg>
*/

UpdateNotificationSettings.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  //headerTintColor: '#FFF',
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    //shadowColor: 'transparent',
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
})

const local = {
  drop: {
    height: 54,
    borderWidth: 1,
    borderRadius: 12,
    borderColor: '#474756',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    backgroundColor: '#474756', //'#3D3D50',
    fontSize: 16,
    marginBottom: 18,
    minHeight: 40,
  },
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  pushNotifPermissions: state.user.pushNotifPermissions,
  hasVerifiedPhone: (state.user.user.phone && state.user.user.phone.isVerified) || false,
  restructuredPrefs: state.notifications.restructuredPrefs,
  launchDarkly: state.launchDarkly,
  accountRef: state.auth.account.ref,
})

export default connect(mapStateToProps)(UpdateNotificationSettings)
