import React from 'react'
import {Modal, View, Image, TouchableOpacity, ScrollView, SafeAreaView} from 'react-native'
import {useNavigation} from '@react-navigation/native'
import {useDispatch, useSelector} from 'react-redux'
import {TextBold, TextReg} from '../../../components'
import bigX from '../../../imgs/graphics/bigX.png'
import styles from '../styles'

let NoDelete = ({route}) => {
  let navigation = useNavigation()
  let WebService = useSelector(state => state.auth.WebService || {})

  let name = route?.params?.name
  let id = route?.params?.id

  return (
    <SafeAreaView style={local.box}>
      <View />
      <View style={{alignItems: 'center'}}>
        <Image source={bigX} style={{height: 70, width: 70, marginBottom: 14}} />
        <TextBold style={{fontSize: 30}}>{`Cannot Delete This Entity`}</TextBold>
        <TextReg
          style={{
            width: 280,
            textAlign: 'center',
            marginTop: 10,
          }}>{`Your Entity, ${name}, cannot be deleted because it is in an active account.`}</TextReg>
      </View>
      <View style={{alignSelf: 'stretch'}}>
        <TouchableOpacity
          onPress={() => {
            navigation?.pop(3)
          }}
          style={{
            borderColor: '#00FFBD',
            borderWidth: 1,
            borderRadius: 5,
            alignSelf: 'stretch',
            height: 46,
            alignItems: 'center',
            marginLeft: 20,
            marginRight: 20,
            justifyContent: 'center',
            marginBottom: 30,
          }}>
          <TextReg style={{fontSize: 20, color: '#00FFBD', letterSpacing: 1.6}}>{`CLOSE`}</TextReg>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  )
}

export default NoDelete

let local = {
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
}
