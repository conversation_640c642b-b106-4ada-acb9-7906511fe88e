import {StyleSheet, Dimensions, PixelRatio} from 'react-native'

const {width} = Dimensions.get('window')

const $offWhite = '#eef0f0'

const styles = StyleSheet.create({
  swiperContainer: {
    flex: 1,
    alignSelf: 'stretch',
    flexDirection: 'column',
  },
  toggleAcknowledgeButton: {
    flexDirection: 'row',
    marginTop: 14,
    alignItems: 'center',
    paddingRight: 10,
  },
  toggleAcknowledgeView: {
    height: 40,
    width: 40,
    borderRadius: 6,
    backgroundColor: '#3D3D50',
    borderWidth: 1,
    borderColor: '#efefef',
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 6,
  },
  unit21ToggleCheckImg: {
    height: 40,
    width: 40,
    borderRadius: 6,
  },
  unit21AcknowledgeTitle: {
    color: '#FFF',
    width: 260,
  },
  unit21AcknowledgeTitleMilitary: {
    color: '#FFF',
    flexWrap: 'wrap',
    flex: 1,
  },

  //Unit 21
  unit21InfoInputTitle: {
    color: '#FFF',
    marginBottom: 6,
    width: 260,
  },
  unit21InfoInputTitleError: {
    color: '#E6705B',
    marginBottom: 6,
    width: 260,
  },
  unit21InfoInputTitleBusiness: {
    color: '#FFF',
    marginBottom: 6,
    flexWrap: 'wrap',
  },
  unit21InfoInputTitleBusinessError: {
    color: '#E6705B',
    marginBottom: 6,
    width: 340,
    marginTop: 12,
  },
  unit21AcknowledgeTitle: {
    color: '#FFF',
    width: 260,
  },
  unit21AcknowledgeTitleMilitary: {
    color: '#FFF',
    flexWrap: 'wrap',
    flex: 1,
  },
  unit21InfoInput: {
    height: 54,
    borderWidth: 1,
    borderRadius: 6,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#FFF',
    backgroundColor: '#474756',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 18,
  },
  unit21InfoInputHalf: {
    height: 54,
    borderWidth: 1,
    borderRadius: 6,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#FFF',
    backgroundColor: '#474756',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 18,
  },
  unit21InfoInputSmall: {
    width: 80,
    height: 54,
    borderWidth: 1,
    borderRadius: 6,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    marginRight: 10,
    color: '#FFF',
    backgroundColor: '#474756',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 18,
  },
  unit21InfoInputDate: {
    flex: 1,
    height: 54,
    borderWidth: 1,
    borderRadius: 6,
    borderColor: '#cbcbcb',
    marginRight: 6,
    color: '#FFF',
    backgroundColor: '#474756',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 18,
    textAlign: 'center',
    fontSize: 17,
  },
  unit21InfoInputDateError2: {
    flex: 1,
    height: 54,
    borderWidth: 1,
    borderRadius: 6,
    borderColor: '#E6705B',
    marginRight: 6,
    color: '#FFF',
    backgroundColor: '#474756',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 18,
    textAlign: 'center',
    fontSize: 17,
  },
  unit21InfoInputError: {
    height: 40,
    borderWidth: 3,
    borderRadius: 14,
    borderColor: '#E6705B',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    backgroundColor: '#FFF',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 4,
  },
  unit21InfoInputDateError: {
    height: 54,
    borderWidth: 1,
    borderRadius: 6,
    borderColor: '#E6705B',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#FFF',
    backgroundColor: '#474756',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 18,
  },
  unit21ShowFilesBox: {
    alignSelf: 'stretch',
    height: 40,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
    borderRadius: 4,
    backgroundColor: '#3D3D50',
  },
  unit21ShowFilesName: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignSelf: 'stretch',
    height: 40,
    backgroundColor: '#3D3D50',
    borderRadius: 4,
    alignItems: 'center',
    paddingLeft: 4,
    justifyContent: 'center',
    paddingRight: 4,
  },
  whiteDotsAnimation: {
    width: 60,
    height: 60,
    opacity: 0.9,
    alignSelf: 'center',
    zIndex: 5,
  },
})

export default styles
