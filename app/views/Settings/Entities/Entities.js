import React, {useEffect, useState, useRef} from 'react'
import {ActivityIndicator, Modal, View, Image, TouchableOpacity, ScrollView, SafeAreaView, RefreshControl} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import {useNavigation, useFocusEffect} from '@react-navigation/native'

import {TextBold, TextReg} from '../../../components'

import styles from '../styles'

let Entities = ({}) => {
  let navigation = useNavigation()
  let [arr, setArr] = useState([])
  let [loading, setLoading] = useState(true)

  let WebService = useSelector(state => state.auth.WebService || {})

  useEffect(() => {
    //getArr()
  }, [])

  useFocusEffect(
    React.useCallback(() => {
      getArr()
    }, []),
  )

  let whichNew = async () => {
    WebService.getEntitys().then(res => {
      let newArr = res.data
      let newObj = newArr.find(item2 => !arr.some(item1 => item1.id === item2.id))
      setArr(newArr)
      navigation?.navigate('EntityQuestions1', {...newObj})
    })
  }

  let getArr = async () => {
    setLoading(true)
    WebService.getEntitys()
      .then(res => {
        setArr(res.data)
        setTimeout(() => {
          setLoading(false)
        }, 300)
      })
      .catch(err => {
        console.log('getEntitys err', err)
        setTimeout(() => {
          setLoading(false)
        }, 300)
      })
  }

  let make = async () => {
    WebService.newEntity()
      .then(res => {
        console.log('newEntity res', res)
        whichNew()
      })
      .catch(err => {
        console.log(' newEntity err', err)
      })
  }

  let showEntities = arr?.map((a, k) => {
    const date = new Date(a.createdAt)
    let options = {}

    let showDate = ''
    if (Platform.OS === 'ios') {
      options = {month: 'short', day: 'numeric', year: 'numeric'}
      showDate = date.toLocaleDateString('en-US', options)
    } else {
      var dayOfWeek = ['Mon', 'Tue', 'Wed', 'Thur', 'Fri', 'Sat', 'Sun'],
        monthName = [
          'January',
          'February',
          'March',
          'April',
          'May',
          'June',
          'July',
          'August',
          'September',
          'October',
          'November',
          'December',
        ],
        utc = date.getTime() + date.getTimezoneOffset() * 60000,
        US_date = new Date(a.createdAt)

      showDate = monthName[US_date.getMonth()] + ' ' + US_date.getDate() + ', ' + US_date.getFullYear()
    }

    let showName = a.legalName
    if (showName == null) {
      showName = 'Unnamed'
    }
    let words = 'SEE DETAILS'
    let goTo = () => navigation?.navigate('EntityDetails', a)

    let bothDocs = a.documents?.length > 0 && a.address?.documents?.length > 0
    let allReject1 = true
    let allReject2 = true
    a.documents?.map(b => {
      if (!b.rejectedAt) {
        allReject1 = false
      }
    })
    a.address?.documents?.map(b => {
      if (!b.rejectedAt) {
        allReject2 = false
      }
    })

    if (!bothDocs || allReject1 || allReject2) {
      words = 'VERIFY'
      goTo = () => navigation.navigate('EntityDoc1', {...a, stackDepth: 1})
    }
    if (!a.isComplete) {
      words = 'CONTINUE'
      goTo = () => navigation?.navigate('EntityQuestions1', a)
    }


    let doc1 = a?.documents?.filter(b => b.verifiedAt)?.length > 0
    let doc2 = a?.address?.documents?.filter(b => b.verifiedAt)?.length > 0
    return (
      <View key={k} style={local.card}>
        <View style={{flexDirection: 'column', paddingLeft: 12, paddingRight: 12, marginBottom: 10, alignSelf: 'stretch'}}>
          <TextReg style={{fontSize: 20}}>{`${showName}`}</TextReg>
          <View style={{alignSelf: 'stretch', justifyContent: 'space-between', flexDirection: 'row'}}>
            <View>
              <TextReg style={{fontSize: 13, marginTop: 12}}>{`Creation Date`}</TextReg>
              <TextReg style={{fontSize: 16, marginTop: 0}}>{`${showDate}`}</TextReg>
            </View>
            <View style={{flexDirection: 'row', marginTop: 18}}>
              {doc1 && doc2 && (
                <Image source={require('../../../imgs/graphics/shield.png')} style={{height: 25, width: 20, marginRight: 8, marginTop:2}} />
              )}
              {a.isEntityAccredited && <Image source={require('../../../imgs/graphics/ribbon.png')} style={{height: 28, width: 20, marginRight: 5, marginBottom:6}} />}
            </View>
          </View>
        </View>

        <TouchableOpacity
          onPress={() => {
            goTo()
          }}
          style={{
            alignSelf: 'stretch',
            borderWidth: 0,
            borderColor: '#00FFBD',
            borderTopWidth: 1,
            padding: 12,
            alignItems: 'center',
            marginTop: 4,
          }}>
          <TextReg style={{fontSize: 18, color: '#00FFBD'}}>{words}</TextReg>
        </TouchableOpacity>
      </View>
    )
  })
  return (
    <SafeAreaView style={local.box}>
      <View style={{flexDirection: 'row', alignSelf: 'stretch', justifyContent: 'space-between', marginTop: 5, marginBottom: 5}}>
        <TouchableOpacity onPress={() => navigation?.goBack()} style={{height: 40, width: 40, marginLeft: 20}}>
          <Image source={require('../../../imgs/backToSettings.png')} style={{height: 25, width: 20}} />
        </TouchableOpacity>
        <TextReg style={{color: '#FFF', fontSize: 18}}>Entities</TextReg>
        <View style={{height: 40, width: 40, marginRight: 20}} />
      </View>
      {loading && <ActivityIndicator size="large" color="#fff" style={{marginBottom: 20}} />}
      <ScrollView
        style={{alignSelf: 'stretch'}}
        refreshControl={
          <RefreshControl
            refreshing={false}
            onRefresh={() => getArr()}
            colors={['#28283D']}
            progressBackgroundColor={['#28283D00']}
            tintColor={'#28283D'}
            style={{opacity: 0}}
          />
        }>
        <TouchableOpacity
          onPress={() => {
            make()
          }}
          style={{
            borderColor: '#00FFBD',
            borderWidth: 1,
            borderRadius: 5,
            alignSelf: 'stretch',
            height: 50,
            alignItems: 'center',
            marginLeft: 20,
            marginRight: 20,
            justifyContent: 'center',
            marginBottom: 10,
          }}>
          <TextReg style={{fontSize: 20, color: '#00FFBD'}}>{`+ CREATE NEW`}</TextReg>
        </TouchableOpacity>
        {showEntities}
      </ScrollView>
    </SafeAreaView>
  )
}

export default Entities

let local = {
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
  card: {
    backgroundColor: '#3D3D50',
    alignSelf: 'stretch',
    borderRadius: 10,
    marginLeft: 20,
    marginRight: 20,
    marginBottom: 10,
    paddingTop: 10,
  },
}
