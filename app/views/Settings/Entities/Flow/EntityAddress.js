import React, {useEffect, useState, useRef} from 'react'
import {
  StyleSheet,
  View,
  Platform,
  TouchableOpacity,
  Image,
  TextInput,
  Keyboard,
} from 'react-native'
import {KeyboardAwareScrollView} from '@codler/react-native-keyboard-aware-scroll-view'
import {TextReg, TextBold, BackgroundHeader, Button, LocationSelect} from '../../../../components'
import {useDispatch, useSelector} from 'react-redux'
import {useFocusEffect} from '@react-navigation/native'
import {updateFlowB1} from '../../../../store/user/user.actions'
import {formatDateText} from '../../../../util/helpers'
import iso3166 from 'iso-3166-2'
import countryCodes from '../../../../util/countryCodes'

import Progress from './Progress'

import styles from '../styles'

let EntityAddress = ({navigation, route}) => {
  let WebService = useSelector(state => state.auth.WebService || {})
  let dispatch = useDispatch()
  let b1Obj = useSelector(state => {})
  let [a, setA] = useState({...route?.params})
  let [space, setSpace] = useState(false)
  const scrollViewRef = useRef(null)

  console.log('space', space)

  //jurisdiction
  let [country, setCountry] = useState(null)
  let [countryCode, setCountryCode] = useState(null)
  let [region, setRegion] = useState(null)
  let [regionCode, setRegionCode] = useState(null)
  let [pickCodes, setPickCodes] = useState([])
  let [pickRegion, setPickRegion] = useState([])

  let inputs = []
  let entityTypeList = ['LLC', 'Corporation', 'Trust', 'Sole Proprietor', 'Non-Profit']

  let [address1, setAddress1] = useState(a?.address?.street1 || '')
  let [address2, setAddress2] = useState(a?.address?.street2 || '')
  let [postal, setPostal] = useState(a?.address?.postalCode || '')
  let [city, setCity] = useState(a?.address?.city || '')

  useFocusEffect(
    React.useCallback(() => {
      getA()
      // Reset space state when screen gains focus
      setSpace(false)
    }, []),
  )

  let getA = async () => {
    let res = await WebService.getEntitys()
    let arr = res.data
    let entity = arr?.filter(b => b.id == a.id)[0]
    setA({
      stackDepth: a.stackDepth,
      marketFlow: a.marketFlow,
      editAddressFlow: a.editAddressFlow,
      ...entity,
    })
    setAddress1(entity?.address?.street1 || '')
    setAddress2(entity?.address?.street2 || '')
    setPostal(entity?.address?.postalCode || '')
    setCity(entity?.address?.city || '')
    locationSetup(entity)
  }

  let id = route?.params?.id
  let stackDepth = a?.stackDepth || 4

  console.log('stackDepth', stackDepth)

  let locationSetup = async (entity = a) => {
    let pickableCountryCodes = countryCodes().map(a => a.name)
    pickableCountryCodes = ['United States'].concat(pickableCountryCodes)
    setPickCodes(pickableCountryCodes)

    let countryCode = entity?.address?.countryCode || ''
    let regionCode = entity?.address?.province || ''

    setCountryCode(countryCode)
    setRegionCode(regionCode)
    setRegion(regionCode)

    let country = countryCodes().filter(b => b.code == countryCode)[0]?.name || ''
    setCountry(country)

    if (countryCode != '') {
      const isoCountry = iso3166.country(countryCode)
      let pickableCountrySubs = []
      const subArr = Object.values(isoCountry.sub)
      const subData = iso3166.subdivision(countryCode, regionCode)
      let region = subData?.name
      setRegion(region)
      pickableCountrySubs = subArr.map(b => b.name)
      pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
        if (a < b) return -1
        if (b < a) return 1
        return 0
      })
      setPickRegion(pickableCountrySubs)
    }
  }

  let onSelect = selectedCountry => {
    const countryCode = countryCodes().filter(a => a.name == selectedCountry)[0].code
    const isoCountry = iso3166.country(countryCode)
    let pickableCountrySubs = []
    const subArr = Object.values(isoCountry.sub)
    pickableCountrySubs = subArr.map(a => a.name)
    pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
      if (a < b) return -1
      if (b < a) return 1
      return 0
    })
    setCountryCode(countryCode)
    setCountry(selectedCountry)
    setPickRegion(pickableCountrySubs)
    setRegion(null)
    setRegionCode(null)
  }

  let onRegionSelect = selectedProvince => {
    const countryCode = countryCodes().filter(a => a.name == country)[0]?.code
    const subData = iso3166.subdivision(countryCode, selectedProvince)
    const regionCode = subData?.regionCode
    let region = subData?.name

    setRegion(region)
    setRegionCode(regionCode)
  }

  let close = () => {
    console.log('close', stackDepth)
    navigation.pop(stackDepth)
  }

  let next = async () => {
    //let formationJurisdiction = `${countryCode}, ${regionCode}`
    let addressId = a?.address?.id

    let payload = {
      countryCode,
      province: regionCode,
      street1: address1,
      street2: address2,
      postalCode: postal,
      city,
    }

    if (a?.ref && a?.name) {
      let res2 = await WebService.getEntitys()
      let arr = res2.data
      console.log('res2', res2)
    }

    try {
      console.log('addressId, payload', addressId, payload)
      let res = await WebService.patchAddress(addressId, payload)
      navigation.navigate('EntityDoc1', {...a, stackDepth: stackDepth + 1})
    } catch (err) {
      console.log('err', err)
    }
  }

  let goBack = () => {
    Keyboard.dismiss()
    navigation.goBack()
  }

  // Function to handle keyboard appearance
  const handleFocus = () => {
    // Only set space to true on Android devices
    if (Platform.OS === 'android') {
      setSpace(true)

      // Scroll to the bottom of the page
      setTimeout(() => {
        if (scrollViewRef.current) {
          scrollViewRef.current.scrollToEnd({animated: true})
        }
      }, 100)
    }
  }

  const handleBlur = () => {
    if (Platform.OS === 'android') {
      setSpace(false)
    }
  }

  return (
    <View style={stylesLocal.box}>
      <KeyboardAwareScrollView
        ref={scrollViewRef}
        style={{flex: 1, alignSelf: 'stretch', paddingLeft: 24, paddingRight: 24}}
        enableResetScrollToCoords={true}>
        <BackgroundHeader
          title={'Create New Entity'}
          close
          closeFn={() => close()}
          goBack={() => goBack()}
        />
        <Progress complete={5} />
        <TextReg style={{fontSize: 24, marginBottom: 16}}>{`Entity Address`}</TextReg>
        <TextReg style={{marginBottom: 4, marginTop: 5}}>Address Line 1</TextReg>
        <TextInput
          style={{...styles.unit21InfoInput, marginBottom: 10}}
          onChangeText={text => setAddress1(text)}
          value={address1}
          underlineColorAndroid="transparent"
          blurOnSubmit
          returnKeyType={'next'}
          placeholder={''}
          placeholderTextColor={'#999'}
          onSubmitEditing={() => inputs.address2.focus()}
          keyboardAppearance="dark"
        />
        <TextReg style={{marginBottom: 4}}>Address Line 2</TextReg>
        <TextInput
          style={{...styles.unit21InfoInput, marginBottom: 12}}
          onChangeText={text => setAddress2(text)}
          ref={input => (inputs.address2 = input)}
          value={address2}
          underlineColorAndroid="transparent"
          blurOnSubmit
          returnKeyType={'next'}
          placeholder={''}
          placeholderTextColor={'#999'}
          onSubmitEditing={() => Keyboard.dismiss()}
          keyboardAppearance="dark"
        />
        <TextReg style={{marginBottom: 5}}>Country</TextReg>
        <LocationSelect
          options={pickCodes}
          onSelect={onSelect}
          placeholder={country ? country : 'Country'}
        />
        <TextReg style={{marginBottom: 5, marginTop: -4}}>State / Province</TextReg>
        <LocationSelect
          options={pickRegion}
          onSelect={onRegionSelect}
          placeholder={region ? region : 'State / Province'}
        />
        <View style={{flexDirection: 'row', marginTop: -4}}>
          <View style={{flex: 1, flexDirection: 'column', marginRight: 10}}>
            <TextReg style={{marginBottom: 4}}>City</TextReg>
            <TextInput
              style={styles.unit21InfoInputHalf}
              onChangeText={text => setCity(text)}
              ref={input => (inputs.city = input)}
              value={city}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              placeholder={'City'}
              onFocus={handleFocus}
              onBlur={handleBlur}
              placeholderTextColor={'#999'}
              onSubmitEditing={() => inputs.postal.focus()}
              keyboardAppearance="dark"
            />
          </View>
          <View style={{flex: 1, flexDirection: 'column'}}>
            <TextReg style={{marginBottom: 4}}>Zip Code</TextReg>
            <TextInput
              style={styles.unit21InfoInputHalf}
              onChangeText={text => setPostal(text)}
              ref={input => (inputs.postal = input)}
              value={postal}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              placeholder={'Zip Code'}
              placeholderTextColor={'#999'}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onSubmitEditing={() => Keyboard.dismiss()}
              keyboardAppearance="dark"
            />
          </View>
        </View>

        <Button
          isLoading={false}
          disabled={!postal || !city || !region || !country || !address1}
          style={{
            alignSelf: 'stretch',
            marginTop: 10,
            backgroundColor: '#00FFBD',
            marginBottom: 10,
          }}
          onPress={() => next()}
          theme={'secondary'}>
          <TextReg style={{color: '#000', fontSize: 18}}>CONTINUE</TextReg>
        </Button>
        <Button
          isLoading={false}
          style={{
            alignSelf: 'stretch',
            marginTop: 4,
            borderColor: '#00FFBD',
            borderWidth: 1,
            marginBottom: 30,
            backgroundColor: '#28283D',
          }}
          onPress={() => goBack()}>
          <TextReg style={{color: '#00FFBD', fontSize: 18}}>BACK</TextReg>
        </Button>
        {space && <View style={{height: 300}} />}
      </KeyboardAwareScrollView>
    </View>
  )
}

export default EntityAddress

const stylesLocal = StyleSheet.create({
  box: {
    flex: 1,
    paddingTop: 24,
    backgroundColor: '#28283D',
    justifyContent: 'space-between',
  },
})
