import React, {useState} from 'react'
import {TouchableOpacity, Image, View, ScrollView, SafeAreaView} from 'react-native'
import {TextReg, Button, Background} from '../../../components'
import styles from './styles'
import {useNavigation} from '@react-navigation/native'

import agreedYes from '../../../imgs/agreedYes.png'
import agreedNo from '../../../imgs/agreedNo.png'
import backToSettings from '../../../imgs/backToSettings.png'

const PlaidLinking = () => {
  const [agree, setAgree] = useState(false)
  const navigation = useNavigation()

  const toggleAgree = () => {
    setAgree(!agree)
  }

  const handleContinue = () => {
    console.log('Continue button pressed')
  }

  const handleBack = () => {
    console.log('goback')
    navigation.goBack()
  }

  return (
    <>
      <Background />
      <SafeAreaView
        style={{
          backgroundColor: '#28283D',
          flex: 1,
        }}>
        <View style={{alignItems: 'center'}}>
          <TouchableOpacity
            onPress={() => handleBack()}
            style={{position: 'absolute', top: 20, left: 10}}>
            <Image source={backToSettings} style={{height: 25, width: 25}} />
          </TouchableOpacity>
          <ScrollView
            style={{alignSelf: 'stretch', paddingBottom: 40}}
            contentContainerStyle={{alignItems: 'center'}}>
            <TextReg style={styles.banksDisclaimerTitle}>Plaid update</TextReg>
            <TextReg style={{width: 300, marginBottom: 20, fontSize: 17}}>
              This authorizes any of the herein listed parties (the "Company"): Salt Lending
              Holdings, Inc; Salt Platform, LLC; Salt Master Fund I, LLC; Salt Master Fund II, LLC;
              Salt Blockchain Asset Partners, LLC, to send credit entries (and appropriate debit and
              adjustment entries), electronically or by any other commercially accepted method, to
              my (our) account(s) indicated above and to other accounts I (we) identify in the
              future (the "Account"). This authorizes the financial institution holding the Account
              to post all such entries. I agree that the ACH transactions authorized herein shall
              comply with all applicable U.S. Law. This authorization will be in effect until the
              Company receives a written termination notice from myself and has a reasonable
              opportunity to act on it.
            </TextReg>
            <TouchableOpacity onPress={toggleAgree}>
              <View style={styles.banksDisclaimerToggleBox}>
                <Image
                  source={agree ? agreedYes : agreedNo}
                  style={styles.banksDisclaimerToggleImg}
                />
                <TextReg style={{fontSize: 20}}>I agree to the above terms</TextReg>
              </View>
            </TouchableOpacity>
            <View style={{marginBottom: 50}}>
              <Button disabled={!agree} onPress={handleContinue}>
                CONTINUE
              </Button>
            </View>
          </ScrollView>
        </View>
      </SafeAreaView>
    </>
  )
}

export default PlaidLinking
