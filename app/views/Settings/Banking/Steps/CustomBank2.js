import React, {Component} from 'react'
import {connect} from 'react-redux'
import {View, ScrollView, TextInput} from 'react-native'
import {TextReg, Button} from '../../../../components'

import styles from '../../styles'

class CustomBank2 extends Component {
  constructor(props) {
    super(props)
    this.inputs = []
  }

  focusNextField = field => {
    this.inputs[field].focus()
  }

  render() {
    const formCompleted = this.props.wireRouting !== '' && this.props.fcc !== '' && this.props.memo !== ''

    return (
      <View style={{flex: 1, alignItems: 'center'}}>
        <ScrollView style={{marginTop: 20, paddingBottom: 40}} contentContainerStyle={{alignItems: 'center'}}>
          <View style={{width: 300, marginBottom: 20}}>
            <TextReg style={styles.bankInputTitle}>Wire Routing Number *</TextReg>
            <TextInput
              style={styles.bankInput}
              onChangeText={text => this.props.handleUpdateWireRouting(text)}
              ref={input => (this.inputs.bankName = input)}
              value={this.props.wireRouting}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              onSubmitEditing={() => this.focusNextField('fieldAccount')}
              placeholder={''}
              keyboardAppearance="dark"
            />

            <TextReg style={styles.bankInputTitle}>{'For Further Credit (FFC) *'}</TextReg>
            <TextInput
              style={styles.bankInput}
              onChangeText={text => this.props.handleUpdateFcc(text)}
              ref={input => (this.inputs.fieldAccount = input)}
              value={this.props.fcc}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              onSubmitEditing={() => this.focusNextField('fieldRouting')}
              placeholder={''}
              keyboardAppearance="dark"
            />
            <TextReg style={{marginTop: -15, opacity: 0.7, marginBottom: 17}}>{`If not applicable, enter "N/A"`}</TextReg>

            <TextReg style={styles.bankInputTitle}>Memo *</TextReg>
            <TextInput
              style={styles.bankInput}
              onChangeText={text => this.props.handleUpdateMemo(text)}
              ref={input => (this.inputs.fieldRouting = input)}
              value={this.props.memo}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              placeholder={''}
              keyboardAppearance="dark"
            />
            <TextReg style={{marginTop: -15, opacity: 0.7, marginBottom: 17}}>{`If not applicable, enter "N/A"`}</TextReg>
          </View>
          <Button onPress={() => this.props.addCustomBank()} disabled={!formCompleted} isLoading={this.props.loadingAddBank}>
            CONTINUE
          </Button>
          {this.props.addBankError && (
            <View
              style={{
                alignSelf: 'stretch',
                justifyContent: 'center',
                marginBottom: 20,
                marginTop: 20,
              }}>
              <TextReg
                style={{
                  color: '#de4a2e',
                  fontSize: 17,
                  textAlign: 'center',
                }}>
                Error Adding Bank
              </TextReg>
            </View>
          )}
        </ScrollView>
      </View>
    )
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  account: state.auth.account,
  showPinScreen: state.auth.pinScreen,
})

export default connect(mapStateToProps)(CustomBank2)
