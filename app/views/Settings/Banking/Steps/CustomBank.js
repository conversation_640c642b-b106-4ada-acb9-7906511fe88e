import React, {Component} from 'react'
import {connect} from 'react-redux'
import {View, ScrollView, TextInput} from 'react-native'
import {TextReg, Button} from '../../../../components'

import styles from '../../styles'

class CustomBank extends Component {
  constructor(props) {
    super(props)
    this.inputs = []
  }

  focusNextField = field => {
    this.inputs[field].focus()
  }

  render() {
    const formCompleted = this.props.fieldAccount !== '' && this.props.fieldRouting !== '' && this.props.bankName !== ''

    return (
      <View style={{flex: 1, alignItems: 'center'}}>
        <ScrollView style={{marginTop: 20, paddingBottom: 40}} contentContainerStyle={{alignItems: 'center'}}>
          <View style={{width: 300, marginBottom: 20}}>
            <TextReg style={styles.bankInputTitle}>Bank Name</TextReg>
            <TextInput
              style={styles.bankInput}
              onChangeText={text => this.props.handleUpdatebankName(text)}
              ref={input => (this.inputs.bankName = input)}
              value={this.props.bankName}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              onSubmitEditing={() => this.focusNextField(this.props.location === 'Outside' ? 'fieldSwift' : 'fieldAccount')}
              placeholder={''}
              keyboardAppearance="dark"
            />

            {this.props.location === 'Outside' && (
              <View>
                <TextReg style={styles.bankInputTitle}>SWIFT</TextReg>
                <TextInput
                  style={styles.bankInput}
                  onChangeText={text => this.props.handleUpdateFieldSwift(text)}
                  ref={input => (this.inputs.fieldSwift = input)}
                  value={this.props.fieldSwift}
                  underlineColorAndroid="transparent"
                  blurOnSubmit
                  returnKeyType={'next'}
                  onSubmitEditing={() => this.focusNextField('fieldAccount')}
                  placeholder={''}
                  keyboardAppearance="dark"
                />
              </View>
            )}

            <TextReg style={styles.bankInputTitle}>
              {this.props.location === 'Outside' ? 'IBAN (Account Number)' : 'Account Number'}
            </TextReg>
            <TextInput
              style={styles.bankInput}
              onChangeText={text => this.props.handleUpdateFieldAccount(text)}
              ref={input => (this.inputs.fieldAccount = input)}
              value={this.props.fieldAccount}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              onSubmitEditing={() => this.focusNextField('fieldRouting')}
              placeholder={''}
              keyboardAppearance="dark"
            />

            <TextReg style={styles.bankInputTitle}>Routing Number</TextReg>
            <TextInput
              style={styles.bankInput}
              onChangeText={text => this.props.handleUpdateFieldRouting(text)}
              ref={input => (this.inputs.fieldRouting = input)}
              value={this.props.fieldRouting}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              placeholder={''}
              keyboardAppearance="dark"
            />
          </View>
          <Button onPress={() => this.props.addManualBank()} disabled={!formCompleted} isLoading={this.props.loadingAddBank}>
            CONTINUE
          </Button>
          {this.props.addBankError && (
            <View
              style={{
                alignSelf: 'stretch',
                justifyContent: 'center',
                marginBottom: 20,
                marginTop: 20,
              }}>
              <TextReg
                style={{
                  color: '#de4a2e',
                  fontSize: 17,
                  textAlign: 'center',
                }}>
                Error Adding Bank
              </TextReg>
            </View>
          )}
        </ScrollView>
      </View>
    )
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  account: state.auth.account,
  showPinScreen: state.auth.pinScreen,
})

export default connect(mapStateToProps)(CustomBank)
