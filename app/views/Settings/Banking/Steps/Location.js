import React from 'react'
import {TouchableOpacity, Image, View} from 'react-native'
import {TextReg, Card, Button} from '../../../../components'
import styles from '../styles'

const Location = ({where, location, next}) => (
  <View style={{flex: 1, alignItems: 'center'}}>
    <TextReg style={{fontSize: 18, marginTop: 20, marginBottom: 30}}>Where is your bank located?</TextReg>
    <View style={{flexDirection: 'row', marginBottom: 20}}>
      <TouchableOpacity activeOpacity={1} onPress={() => where('US')} style={{marginRight: 14}}>
        <Card
          style={{
            borderColor: location === 'US' ? '#00FFBD' : '#555',
            ...styles.banksLocationBox,
          }}>
          <Image source={require('../../../../imgs/banking/US.png')} style={styles.banksLocationUSImg} />
          <TextReg>United States</TextReg>
        </Card>
      </TouchableOpacity>
      <TouchableOpacity activeOpacity={1} onPress={() => where('Outside')}>
        <Card
          style={{
            ...styles.banksLocationBox,
            borderColor: location === 'Outside' ? '#00FFBD' : '#555',
          }}>
          <Image source={require('../../../../imgs/banking/globe.png')} style={styles.banksLocationWorldImg} />
          <TextReg style={{width: 100, textAlign: 'center'}}>Outside the United States</TextReg>
        </Card>
      </TouchableOpacity>
    </View>
    <Button disabled={!location} onPress={() => next()}>
      CONTINUE
    </Button>
  </View>
)

export default Location
