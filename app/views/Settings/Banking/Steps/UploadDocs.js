import React, {Component} from 'react'
import {connect} from 'react-redux'
import {View, ScrollView, Image, TouchableOpacity} from 'react-native'
import {TextReg, TextBold, Button} from '../../../../components'
import removeFileImg from '../../../../imgs/closeX.png'

import styles from '../styles'

class UploadDocs extends Component {
  render() {
    const showDocuments = this.props.documents.map((a, k) => (
      <View style={{flexDirection: 'row', alignItems: 'center'}} key={k}>
        <View style={styles.banksUploadDocumentBox}>
          <TextReg style={{color: '#FFF'}}>{a.name}</TextReg>
        </View>
        <TouchableOpacity onPress={() => this.props.removeDocument(a.id)}>
          <Image
            source={removeFileImg}
            style={{
              height: 30,
              width: 30,
              marginTop: -12,
            }}
          />
        </TouchableOpacity>
      </View>
    ))

    let isDisabled = this.props.refreshingUpload || this.props.documents.length < 1

    return (
      <View style={{flex: 1, alignItems: 'center'}}>
        <ScrollView style={styles.banksUploadScrollBox} contentContainerStyle={{alignItems: 'center'}}>
          <View style={{width: 300}}>
            <View style={{marginBottom: 20}}>
              <TextBold style={{fontSize: 18, marginBottom: 10}}>Proof of account</TextBold>
              <TextReg style={{marginBottom: 4}}>Provide ONE of the following:</TextReg>
              <TextReg>• Clear photo of voided check</TextReg>
              <TextReg>• Clear photo or screenshot of bank statement with account number visible</TextReg>
            </View>
            {showDocuments}
            {this.props.documents.length < 10 ? (
              <Button
                style={isDisabled ? styles.banksUploadDocumentButton : styles.banksUploadSubmitButtonSecond}
                onPress={() => this.props.openImageSelect(this.props.bankID)}>
                {this.props.refreshingUpload ? (
                  <Image source={require('../../../../imgs/loadingDots.gif')} style={styles.banksUploadLoadingDots} />
                ) : (
                  <TextReg style={{color: isDisabled ? '#000' : '#00FFBD'}}>
                    UPLOAD {this.props.documents.length === 0 ? 'DOCUMENT' : 'ANOTHER'}
                  </TextReg>
                )}
              </Button>
            ) : (
              <View style={styles.banksUploadTitleBox}>
                <TextReg style={styles.banksUploadTitle}>10 Document Maximum</TextReg>
              </View>
            )}
            {!isDisabled && (
              <Button style={[styles.banksUploadDocumentButton]} onPress={() => this.props.resetAndClose(true)}>
                <TextReg style={{color: '#000'}}>SUBMIT</TextReg>
              </Button>
            )}
            {this.props.imageError && (
              <View style={styles.banksUploadErrorBox}>
                <TextReg style={styles.banksUploadErrorText}>
                  {this.props.imageError === 'type' && 'Invalid Image Type - JPEG, JPG, PNG, TIF, PDF only'}
                  {this.props.imageError === 'size' && 'Image too large, 50MB max'}
                </TextReg>
              </View>
            )}
          </View>
        </ScrollView>
      </View>
    )
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  account: state.auth.account,
  showPinScreen: state.auth.pinScreen,
})

export default connect(mapStateToProps)(UploadDocs)
