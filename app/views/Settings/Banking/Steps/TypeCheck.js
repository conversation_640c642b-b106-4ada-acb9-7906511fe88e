import React, {useEffect, useState} from 'react'
import {connect} from 'react-redux'
import {View, ScrollView, TextInput, TouchableOpacity, Image} from 'react-native'
import {TextReg, Button} from '../../../../components'
import agreedYes from '../../../../imgs/agreedYes.png'

import styles from '../styles'

let TypeCheck = ({pick, setPick, next}) => {
  let toggle = num => {
    if (pick != num) {
      setPick(num)
    } else {
      setPick(null)
    }
  }

  let arr = [
    {num: 1, name: 'Checking'},
    {num: 2, name: 'Savings'},
    {num: 3, name: 'Investment'},
    {num: 4, name: 'Trust'},
    {num: 5, name: 'Other'},
  ]

  console.log('styles.toggleAcknowledgeButton', styles.toggleAcknowledgeButton)
  let showOptions = arr?.map((a, k) => {
    return (
      <TouchableOpacity key={k} onPress={() => toggle(a.num)} activeOpacity={1} style={styles.toggleAcknowledgeButton}>
        <View style={styles.toggleAcknowledgeView}>
          <Image
            source={agreedYes}
            style={[
              styles.unit21ToggleCheckImg,
              {
                opacity: pick == a.num ? 1 : 0,
              },
            ]}
          />
        </View>
        <TextReg style={styles.unit21AcknowledgeTitle}>{a.name}</TextReg>
      </TouchableOpacity>
    )
  })

  return (
    <View style={{flex: 1, alignItems: 'center'}}>
      <ScrollView style={{marginTop: 20, paddingBottom: 40}} contentContainerStyle={{alignItems: 'center'}}>
        <View style={{width: 300, marginBottom: 30}}>
          <TextReg style={{width: 300, textAlign: 'center', marginBottom: 10}}>What type of bank account is this?</TextReg>
          {showOptions}
        </View>

        <Button onPress={() => next()} disabled={pick === null}>
          CONTINUE
        </Button>
      </ScrollView>
    </View>
  )
}

export default TypeCheck
