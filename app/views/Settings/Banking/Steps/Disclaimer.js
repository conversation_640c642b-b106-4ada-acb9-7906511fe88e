import React, {useState, useEffect} from 'react'
import {TouchableOpacity, Image, View, ScrollView, TextInput, Linking} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import {TextReg, Button} from '../../../../components'
import styles from '../styles'

import agreedYes from '../../../../imgs/agreedYes.png'
import agreedNo from '../../../../imgs/agreedNo.png'

const Disclaimer = ({
  connection,
  location,
  next,
  toggleAgree,
  agree,
  openPlaidLink,
  loadingPlaidRes,
  plaidResError,
  dwollaLoading,
  error,
  dwollaCustomerError,
  user,
}) => {
  const [email, setEmail] = useState('')
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [loaded, setLoaded] = useState(false)
  const [hasCustomer, setHasCustomer] = useState(false)
  let WebService = useSelector(state => state.auth.WebService || {})

  useEffect(() => {
    let primaryemail = user?.emails?.filter(email => email.isPrimary)[0]?.address
    setEmail(primaryemail)
    setFirstName(user?.firstName)
    setLastName(user?.lastName)
  }, [])

  useEffect(() => {
    checkCustomer()
  }, [])

  const checkCustomer = async () => {
    console.log('checkCustomer - disclaimer try')
    let res = await WebService.getDwollaCustomers()
    console.log('checkCustomer - disclaimer', res.data)
    if (res?.data?.length >= 1) {
      console.log('has customer')
      setHasCustomer(true)
    }
    setLoaded(true)
  }

  let link1 = () => {
    Linking.openURL('https://saltlending.com/terms-of-use/')
  }

  let link2 = () => {
    Linking.openURL('https://saltlending.com/privacy-policy/')
  }

  let link3 = () => {
    Linking.openURL('https://www.dwolla.com/legal/dwolla-account-terms-of-service')
  }

  return (
    <View style={{alignItems: 'center'}}>
      {loadingPlaidRes || !loaded ? (
        <View>
          <TextReg style={styles.banksDisclaimerTitle}></TextReg>
          {plaidResError ? (
            <View style={styles.banksUploadErrorBox}>
              <TextReg style={styles.banksUploadErrorText}>
                Error connecting bank account - please contact support for additional information
                and assistance
              </TextReg>
            </View>
          ) : (
            <Image
              source={require('../../../../imgs/loadingDots.gif')}
              style={styles.banksUploadLoadingDots}
            />
          )}
        </View>
      ) : (
        <>
          {!hasCustomer && (
            <ScrollView
              style={{alignSelf: 'stretch', paddingBottom: 10}}
              contentContainerStyle={{alignItems: 'center'}}>
              <TextReg style={styles.banksDisclaimerTitle}>Sign up for a SALT ACH Account</TextReg>
              <TextReg style={{width: 330, marginBottom: 20, fontSize: 17}}>
                For U.S. residents only. Real name, please!
              </TextReg>
              <TextReg style={{width: 330, marginBottom: 4, fontSize: 17}}>Email Address</TextReg>
              <View style={local.input}>
                <TextReg style={{color: '#fff', fontSize: 17}}>{email}</TextReg>
              </View>

              <TextReg style={{width: 330, marginBottom: 4, fontSize: 17}}>First Name</TextReg>
              <View style={local.input}>
                <TextReg style={{color: '#fff', fontSize: 17}}>{firstName}</TextReg>
              </View>
              <TextReg style={{width: 330, marginBottom: 4, fontSize: 17}}>Last Name</TextReg>
              <View style={local.input}>
                <TextReg style={{color: '#fff', fontSize: 17}}>{lastName}</TextReg>
              </View>
              <View>
                <View style={styles.banksDisclaimerToggleBox}>
                  <TouchableOpacity onPress={() => toggleAgree()}>
                    <Image
                      source={agree ? agreedYes : agreedNo}
                      style={styles.banksDisclaimerToggleImg}
                    />
                  </TouchableOpacity>
                  <TextReg style={{fontSize: 12, maxWidth: 300}}>
                    By checking this box, you agree to{' '}
                    <TextReg style={{color: '#00ffDB'}} onPress={link1}>
                      Our Terms of Service
                    </TextReg>{' '}
                    and{' '}
                    <TextReg style={{color: '#00ffDB'}} onPress={link2}>
                      Privacy Policy
                    </TextReg>{' '}
                    , as well as our partner{' '}
                    <TextReg style={{color: '#00ffDB'}} onPress={link3}>
                      Dwolla's Terms of Service and Privacy Policy
                    </TextReg>
                  </TextReg>
                </View>
              </View>
              <View style={{marginBottom: 50}}>
                {dwollaLoading ? (
                  <Image
                    source={require('../../../../imgs/loadingDots.gif')}
                    style={styles.banksUploadLoadingDots}
                  />
                ) : (
                  <Button
                    disabled={!agree || email == '' || firstName == '' || lastName == ''}
                    onPress={() =>
                      connection === 'Manual' || location === 'Outside'
                        ? next()
                        : openPlaidLink(email, firstName, lastName)
                    }>
                    CONTINUE
                  </Button>
                )}
                {error && (
                  <View style={{marginTop: 10, display: 'flex', alignItems: 'center'}}>
                    <TextReg style={{color: '#E5705A'}}>{error}</TextReg>
                  </View>
                )}
                {dwollaCustomerError && (
                  <View style={{marginTop: 10, display: 'flex', alignItems: 'center'}}>
                    <TextReg style={{color: '#E5705A'}}>{dwollaCustomerError}</TextReg>
                  </View>
                )}
              </View>
            </ScrollView>
          )}
          {hasCustomer && (
            <View
              style={{
                alignSelf: 'stretch',
                height: '100%',
                flexDirection: 'column',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <View style={{flexDirection: 'column', alignItems: 'center'}}>
                <TextReg style={styles.banksDisclaimerTitle}>SALT ACH Account</TextReg>

                <View style={{flexDirection: 'column', opacity: 0.3}}>
                  <TextReg style={{width: 330, marginBottom: 4, fontSize: 17}}>
                    Email Address
                  </TextReg>
                  <View style={local.input}>
                    <TextReg style={{color: '#fff', fontSize: 17}}>{email}</TextReg>
                  </View>

                  <TextReg style={{width: 330, marginBottom: 4, fontSize: 17}}>First Name</TextReg>
                  <View style={local.input}>
                    <TextReg style={{color: '#fff', fontSize: 17}}>{firstName}</TextReg>
                  </View>
                  <TextReg style={{width: 330, marginBottom: 4, fontSize: 17}}>Last Name</TextReg>
                  <View style={local.input}>
                    <TextReg style={{color: '#fff', fontSize: 17}}>{lastName}</TextReg>
                  </View>
                </View>
              </View>

              <View style={{marginBottom: 100}}>
                <TextReg
                  style={{
                    width: 330,
                    marginBottom: 0,
                    fontSize: 16,
                    opacity: 0.9,
                    textAlign: 'center',
                  }}>
                  Account already created,
                </TextReg>
                <TextReg
                  style={{
                    width: 330,
                    marginBottom: 50,
                    fontSize: 16,
                    opacity: 0.9,
                    textAlign: 'center',
                  }}>
                  Please continue through plaid
                </TextReg>
                {dwollaLoading ? (
                  <Image
                    source={require('../../../../imgs/loadingDots.gif')}
                    style={styles.banksUploadLoadingDots}
                  />
                ) : (
                  <Button
                    onPress={() =>
                      connection === 'Manual' || location === 'Outside'
                        ? next()
                        : openPlaidLink(email, firstName, lastName)
                    }>
                    CONTINUE
                  </Button>
                )}
                {error && (
                  <View style={{marginTop: 10, display: 'flex', alignItems: 'center'}}>
                    <TextReg style={{color: '#E5705A'}}>{error}</TextReg>
                  </View>
                )}
                {dwollaCustomerError && (
                  <View style={{marginTop: 10, display: 'flex', alignItems: 'center'}}>
                    <TextReg style={{color: '#E5705A'}}>{dwollaCustomerError}</TextReg>
                  </View>
                )}
              </View>
            </View>
          )}
        </>
      )}
    </View>
  )
}

let local = {
  input: {
    width: 330,
    marginBottom: 20,
    fontSize: 17,
    borderColor: '#fff',
    borderWidth: 1,
    borderRadius: 10,
    padding: 10,
    color: '#fff',
  },
}

export default Disclaimer
