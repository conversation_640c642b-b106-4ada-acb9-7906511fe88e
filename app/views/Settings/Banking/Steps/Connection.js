import React from 'react'
import {TouchableOpacity, Image, View, Linking} from 'react-native'
import {TextReg, TextBold, Card, Button} from '../../../../components'
import styles from '../styles'

let openLinkPlaid = () => {
  console.log('openLinkPlaid')
  Linking.openURL('https://plaid.com/').catch(err => console.error('An error occurred', err))
}

const Connection = ({how, connection, next, location, flow, launchDarkly}) => {
  let showPlaid = false

  if (location != 'Outside' && flow != 'unit21') {
    showPlaid = true
  }
  console.log('launchDarkly - plaid', launchDarkly)
  if (launchDarkly['disable-ach']) {
    showPlaid = false
  }
  return (
    <View style={{flex: 1, alignItems: 'center'}}>
      <TextReg style={styles.banksConnectionTitle}>
        How do you want to connect your bank information?
      </TextReg>
      <View style={{flexDirection: 'row', marginBottom: 30}}>
        {showPlaid && (
          <View>
            <TouchableOpacity activeOpacity={1} onPress={() => how('Plaid')}>
              <Card
                style={{
                  ...styles.banksConnectionChoiceBox,
                  marginRight: 14,
                  borderColor: connection === 'Plaid' ? '#00FFBD' : '#555',
                }}>
                <Image
                  source={require('../../../../imgs/banking/bankPlaid.png')}
                  style={styles.banksConnectionPlaidImg}
                />

                <TextReg style={{fontSize: 17, marginBottom: 6, textAlign: 'center'}}>
                  Log into online bank account
                </TextReg>
              </Card>
            </TouchableOpacity>
            <TouchableOpacity style={{width: 150}} onPress={() => openLinkPlaid()}>
              <TextReg style={{fontSize: 14, textAlign: 'center'}}>
                Quickly and securely connect using{' '}
                <TextBold style={{color: '#00FFBD'}}>Plaid</TextBold>
              </TextReg>
            </TouchableOpacity>
          </View>
        )}
        <TouchableOpacity activeOpacity={1} onPress={() => how('Manual')}>
          <Card
            style={{
              ...styles.banksConnectionChoiceBox,
              borderColor: connection === 'Manual' ? '#00FFBD' : '#555',
            }}>
            <Image
              source={require('../../../../imgs/banking/bankManual.png')}
              style={styles.banksConnectionManualImg}
            />
            <TextReg style={{fontSize: 17, textAlign: 'center'}}>
              Manually enter account info
            </TextReg>
          </Card>
          <View style={{width: 150}}>
            <TextReg style={{fontSize: 14, textAlign: 'center'}}>
              You will also need to upload a photo for proof of account
            </TextReg>
          </View>
        </TouchableOpacity>
      </View>
      <Button disabled={!connection} onPress={() => next()}>
        NEXT
      </Button>
    </View>
  )
}

export default Connection
