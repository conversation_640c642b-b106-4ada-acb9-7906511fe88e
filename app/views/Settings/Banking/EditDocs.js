import React, {Component} from 'react'
import {
  View,
  Modal,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native'
import {connect} from 'react-redux'

import {Background, TextReg, TextBold, Button} from '../../../components'
import styles from './styles'

class EditDocs extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  render() {
    const {showPinScreen, showEditDocs} = this.props

    let documents = []
    let bank = null
    let bankName = null
    if (this.props.editDocuments) {
      bank = this.props.banksArr.filter(
        a => a.id === this.props.editDocuments,
      )[0]
      documents = bank.documents
      bankName = bank.name
    }

    const showBankName = <TextBold>{bankName}</TextBold>

    const showDocuments = documents.map((a, k) => (
      <View key={k}>
        <View style={styles.banksEditDocumentBox}>
          {a.rejectedAt && <View style={styles.banksEditRejectedDoc} />}
          <TextReg style={{color: '#000'}}>{a.name}</TextReg>
        </View>
        {a.rejectedAt && (
          <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
            <TextReg style={{fontSize: 15, color: '#fb1f63', marginBottom: 12}}>
              Please upload a new document.
            </TextReg>
          </View>
        )}
      </View>
    ))

    return (
      <Modal
        animationType="slide"
        transparent
        visible={showEditDocs && !showPinScreen}
        onRequestClose={() => this.props.closeEditDocs()}>
        <Background backgroundColor={'#28283D'} />
        <SafeAreaView>
          <View style={styles.banksEditHeaderBox}>
            <TouchableOpacity onPress={() => this.props.closeEditDocs()}>
              <Image
                source={require('../../../imgs/closeX.png')}
                style={styles.closeModalImg}
              />
            </TouchableOpacity>
          </View>
          <ScrollView
            style={{
              alignSelf: 'stretch',
            }}
            contentContainerStyle={{alignItems: 'center'}}>
            <View style={{alignItems: 'center', width: 300}}>
              <TextBold style={{fontSize: 17, marginBottom: 20}}>
                EDIT DOCS
              </TextBold>
              <View style={{marginBottom: 20}}>
                <TextBold style={{fontSize: 18, marginBottom: 10}}>
                  Proof of account
                </TextBold>
                <TextReg style={{marginBottom: 4}}>
                  Provide ONE of the following:
                </TextReg>
                <TextReg>• Clear photo of voided check</TextReg>
                <TextReg>
                  • Clear photo or screenshot of bank statement with account
                  number visible
                </TextReg>
              </View>

              <TextReg style={{fontSize: 16, marginBottom: 10}}>
                {`Documents for `}
                {showBankName}
                {`:`}
              </TextReg>
            </View>
            {showDocuments}
            {documents.length < 10 ? (
              <Button
                style={styles.banksEditUploadButton}
                onPress={() => this.props.openImageSelect(bank.id)}>
                {this.props.refreshingUpload ? (
                  <Image
                    source={require('../../../imgs/loadingDots.gif')}
                    style={styles.banksEditLoadingDots}
                  />
                ) : (
                  <TextReg style={{color: '#00FFBD'}}>
                    Upload {documents.length === 0 ? 'Document' : 'Another'}
                  </TextReg>
                )}
              </Button>
            ) : (
              <View style={styles.banksEditMaxBox}>
                <TextReg style={styles.banksEditMaxText}>
                  10 Document Maximum
                </TextReg>
              </View>
            )}

            <Button
              disabled={this.props.refreshingUpload}
              style={styles.banksEditRefreshButton}
              onPress={() => this.props.closeEditDocs()}>
              <TextReg style={{color: '#000'}}>Finish</TextReg>
            </Button>
            {this.props.imageError && (
              <View style={styles.banksEditErrorBox}>
                <TextReg style={styles.banksEditErrorText}>
                  {this.props.imageError === 'type' &&
                    'Invalid Image Type - JPEG, JPG, PNG, TIF, PDF only'}
                  {this.props.imageError === 'size' &&
                    'Image too large, 50MB max'}
                </TextReg>
              </View>
            )}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    )
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
})

export default connect(mapStateToProps)(EditDocs)
