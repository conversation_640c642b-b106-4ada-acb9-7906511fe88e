import {StyleSheet} from 'react-native'

const styles = StyleSheet.create({
  successModalX: {
    position: 'absolute',
    top: 22,
    right: 10,
    height: 30,
    width: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeModalImg: {
    height: 30,
    width: 30,
    margin: 4,
  },
  removeBox: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(74, 74, 74, 0.5)',
  },
  removeInnerBox: {
    width: '86%',
    backgroundColor: '#28283D',
    borderRadius: 8,
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: 30,
    padding: 20,
  },
  removeRow: {
    flexDirection: 'row',
    alignSelf: 'stretch',
    justifyContent: 'flex-end',
  },
  removeTextDisclaimer: {
    fontSize: 18,
    padding: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  removeButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignSelf: 'stretch',
    marginBottom: 8,
  },
  removeButtonText: {
    height: 40,
    width: 106,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: '#00FFBD',
    borderWidth: 2,
    marginRight: 30,
  },
  keepButtonText: {
    height: 40,
    width: 106,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#00FFBD',
  },
  banksBoxTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignSelf: 'stretch',
    alignItems: 'center',
  },
  banksBoxHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'stretch',
    marginLeft: 14,
    marginBottom: 6,
  },
  banksLogo: {
    height: 40,
    width: 40,
    marginRight: 14,
  },
  banksName: {
    fontSize: 19,
    width: 180,
    marginTop: 4,
  },
  banksTag: {
    borderRadius: 6,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -2,
  },
  banksMiddle: {
    backgroundColor: '#3D3D50',
    borderRadius: 14,
    alignSelf: 'stretch',
    margin: 6,
    padding: 10,
    paddingLeft: 16,
    borderWidth: 1,
    borderColor: '#555',
  },
  banksScroll: {
    flex: 1,
    alignSelf: 'stretch',
    flexDirection: 'column',
    paddingTop: 20,
  },
  banksLocationBox: {
    width: 150,
    height: 144,
    paddingTop: 20,
    borderWidth: 2,
  },
  banksLocationUSImg: {
    height: 51,
    width: 80,
    marginBottom: 20,
    marginTop: 4,
  },
  banksLocationWorldImg: {
    height: 60,
    width: 60,
    marginBottom: 14,
  },
  banksConnectionTitle: {
    fontSize: 18,
    marginTop: 20,
    marginBottom: 30,
    textAlign: 'center',
    width: 300,
  },
  banksConnectionChoiceBox: {
    width: 150,
    height: 160,
    paddingTop: 20,
    borderWidth: 2,
  },
  banksConnectionPlaidImg: {
    height: 56,
    width: 60,
    marginBottom: 20,
  },
  banksConnectionManualImg: {
    height: 62,
    width: 60,
    marginBottom: 14,
  },
  banksDisclaimerTitle: {
    fontSize: 18,
    marginTop: 20,
    marginBottom: 36,
    textAlign: 'center',
    width: 300,
  },
  banksDisclaimerToggleBox: {
    alignSelf: 'stretch',
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 26,
    alignItems: 'center',
  },
  banksDisclaimerToggleImg: {
    height: 24,
    width: 24,
    marginRight: 8,
  },
  banksUploadDocumentBox: {
    backgroundColor: '#3d3d50',
    borderRadius: 14,
    borderWidth: 1,
    borderColor: '#DDD',
    height: 50,
    width: '90%',
    marginBottom: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  banksUploadScrollBox: {
    alignSelf: 'stretch',
    marginTop: 10,
    paddingBottom: 40,
  },
  banksUploadDocumentButton: {
    backgroundColor: '#00FFBD',
    marginTop: 10,
  },
  banksUploadLoadingDots: {
    height: 40,
    width: 64,
    opacity: 0.6,
    alignSelf: 'center',
    zIndex: 5,
    marginBottom: 12,
  },
  banksUploadTitleBox: {
    alignSelf: 'stretch',
    justifyContent: 'center',
    marginBottom: 8,
    marginTop: 8,
  },
  banksUploadTitle: {
    color: '#de4a2e',
    fontSize: 17,
    textAlign: 'center',
  },
  banksUploadSubmitButton: {
    backgroundColor: '#00FFBD',
    marginBottom: 30,
    borderColor: '#00FFBD',
    borderWidth: 2,
    marginTop: 20,
  },
  banksUploadSubmitButtonSecond: {
    backgroundColor: '#28283D',
    borderColor: '#00FFBD',
    borderWidth: 2,
    marginTop: 20,
    color: '#00FFBD',
  },
  banksUploadErrorBox: {
    alignSelf: 'stretch',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 10,
  },
  banksUploadErrorText: {
    color: '#de4a2e',
    fontSize: 17,
    textAlign: 'center',
    width: 300,
  },
  banksEditDocumentBox: {
    backgroundColor: '#f3f3f3',
    borderRadius: 14,
    height: 50,
    width: 320,
    marginBottom: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  banksEditRejectedDoc: {
    position: 'absolute',
    left: 0,
    width: 10,
    height: 50,
    backgroundColor: '#fb1f63',
    borderTopLeftRadius: 4,
    borderBottomLeftRadius: 4,
  },
  banksEditHeaderBox: {
    position: 'absolute',
    top: 36,
    right: 18,
    height: 30,
    width: 30,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 5,
  },
  banksEditUploadButton: {
    backgroundColor: '#28283D',
    borderColor: '#00FFBD',
    borderWidth: 3,
    marginTop: 16,
    marginBottom: 20,
  },
  banksEditLoadingDots: {
    height: 40,
    width: 64,
    opacity: 0.6,
    alignSelf: 'center',
    zIndex: 5,
    marginBottom: 12,
  },
  banksEditMaxBox: {
    alignSelf: 'stretch',
    justifyContent: 'center',
    marginBottom: 8,
    marginTop: 8,
  },
  banksEditMaxText: {
    color: '#de4a2e',
    fontSize: 17,
    textAlign: 'center',
  },
  banksEditRefreshButton: {
    backgroundColor: '#00FFBD',
    marginBottom: 20,
    borderColor: '#00FFBD',
    borderWidth: 2,
  },
  banksEditErrorBox: {
    alignSelf: 'stretch',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 10,
  },
  banksEditErrorText: {
    color: '#de4a2e',
    fontSize: 17,
    textAlign: 'center',
    width: 300,
  },
  toggleAcknowledgeButton: {
    flexDirection: 'row',
    marginTop: 14,
    alignItems: 'center',
    paddingRight: 10,
  },
  unit21ToggleCheckImg: {
    height: 40,
    width: 40,
    borderRadius: 6,
  },
  toggleAcknowledgeView: {
    height: 40,
    width: 40,
    borderRadius: 6,
    backgroundColor: '#3D3D50',
    borderWidth: 1,
    borderColor: '#efefef',
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 6,
  },
  unit21AcknowledgeTitle: {
    color: '#FFF',
    width: 260,
  },
})

export default styles
