import React, {Component} from 'react'
import {View, Modal, TouchableOpacity, Image} from 'react-native'
import {connect} from 'react-redux'

import {TextReg, TextBold} from '../../../components'
import styles from './styles'

class AddBankStack extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  componentDidMount() {
    this.props.WebService.getLoans()
      .then(res => {
        console.log('getLoans - confirm remove page', res)
        if (res?.data?.length > 0) {
          let haBounce = res?.data[0]?.bounceFeeAmount
          haBounce = Number(haBounce || 0)
          this.setState({hasBounce: haBounce})
          console.log('haBounce', haBounce)
        }
      })
      .catch(err => console.log('getLoans - err', err))
  }

  render() {
    const {
      showConfirmRemove,
      showPinScreen,
      toggleConfirmRemove,
      removeBank,
      bankToGo,
      removeError,
      showIsAutoPay,
      pendingBankId,
      toggleBankId,
    } = this.props

    const bankName = <TextBold>{bankToGo ? bankToGo.name : ''}</TextBold>

    console.log('confirm remove', bankToGo, pendingBankId, toggleBankId)

    let disclaimPending = false
    if (pendingBankId == toggleBankId) {
      disclaimPending = true
    }

    return (
      <Modal
        animationType="fade"
        transparent
        visible={showConfirmRemove && !showPinScreen}
        onRequestClose={() => toggleConfirmRemove()}>
        {!showIsAutoPay && (
          <View style={styles.removeBox}>
            <View style={styles.removeInnerBox}>
              <View style={styles.removeRow}>
                <TouchableOpacity onPress={() => toggleConfirmRemove()}>
                  <Image
                    source={require('../../../imgs/closeX.png')}
                    style={{height: 26, width: 26}}
                  />
                </TouchableOpacity>
              </View>
              <View>
                <TextBold style={{fontSize: 20}}>Confirm Remove Bank</TextBold>
              </View>
              <TextReg style={styles.removeTextDisclaimer}>
                Are you sure you want to remove {bankName}?{' '}
                {disclaimPending && `Deleting this bank will cancel all scheduled bank payments.`}{' '}
                This will delete all account information and you will have to re-connect if you wish
                to use this bank account again.
              </TextReg>
              <View style={styles.removeButton}>
                <TouchableOpacity onPress={() => removeBank(bankToGo)}>
                  <View style={styles.removeButtonText}>
                    <TextReg style={{fontSize: 18}}>OK</TextReg>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity activeOpacity={0.8} onPress={() => toggleConfirmRemove()}>
                  <View style={styles.keepButtonText}>
                    <TextReg style={{color: '#000', fontSize: 18}}>No Keep</TextReg>
                  </View>
                </TouchableOpacity>
              </View>
              {removeError && (
                <TextReg style={{color: '#E5705A', fontSize: 18, marginTop: 6, marginBottom: 6}}>
                  Error removing bank.
                </TextReg>
              )}
            </View>
          </View>
        )}
        {showIsAutoPay && (
          <View style={styles.removeBox}>
            <View style={styles.removeInnerBox}>
              <View style={styles.removeRow}>
                <TouchableOpacity onPress={() => toggleConfirmRemove()}>
                  <Image
                    source={require('../../../imgs/closeX.png')}
                    style={{height: 26, width: 26}}
                  />
                </TouchableOpacity>
              </View>
              <View>
                <TextBold style={{fontSize: 20}}>Bank In Use</TextBold>
              </View>
              <TextReg style={styles.removeTextDisclaimer}>
                This bank is currently connected to your loan. To remove it, you must unlink it from
                autopay.
              </TextReg>
            </View>
          </View>
        )}
      </Modal>
    )
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
})

export default connect(mapStateToProps)(AddBankStack)
