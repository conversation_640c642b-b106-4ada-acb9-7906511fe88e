import React, {Component} from 'react'
import {View, Modal, TouchableOpacity, Image} from 'react-native'
import {connect} from 'react-redux'

import {TextReg, TextBold} from '../../../components'
import styles from './styles'

class AddBankStack extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  render() {
    const {showConfirmAuto, connectBankToLoan} = this.props

    return (
      <Modal
        animationType="fade"
        transparent
        visible={!this.props.showPinScreen}
        onRequestClose={() => this.setState({showConfirmAuto: null})}>
        <View style={styles.removeBox}>
          <View style={styles.removeInnerBox}>
            <View style={styles.removeRow}>
              <TouchableOpacity onPress={() => this.setState({showConfirmAuto: null})}>
                <Image source={require('../../imgs/closeX.png')} style={{height: 26, width: 26}} />
              </TouchableOpacity>
            </View>
            <View>
              <TextBold style={{fontSize: 20}}>Turn on ACH Payments</TextBold>
            </View>
            <TextReg
              style={
                styles.removeTextDisclaimer
              }>{`I agree that future ACH payments through SALT will be processed by the Dwolla payment system from the selected account above. In order to cancel this authorization, I will change my payment settings within my account.`}</TextReg>
            <View style={styles.removeButton}>
              <TouchableOpacity onPress={() => connectBankToLoan(this.state.showConfirmAuto)}>
                <View style={styles.removeButtonText}>
                  <TextReg style={{fontSize: 18}}>OK</TextReg>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    )
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
})

export default connect(mapStateToProps)(AddBankStack)
