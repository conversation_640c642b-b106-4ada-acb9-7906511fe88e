import React, {Component} from 'react'
import {
  View,
  Image,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Platform,
  Dimensions,
} from 'react-native'
import {connect} from 'react-redux'

import {updateActiveTabListener, dig} from '../../../util/helpers'
import {screenView} from '../../../store/analytics/analytics.actions'
import {updateLoans, updateBanks, increaseRefreshDataCount} from '../../../store/user/user.actions'
import {askingForPermissions} from '../../../store/auth/auth.actions'

import {derivedStatusMap} from '../../../util/enumerables'
import styles from './styles'

import * as ImagePicker from 'react-native-image-picker'

import {Button, TextReg, BackgroundHeader, Card, TextBold} from '../../../components'
import commonStyles from '../../../styles/commonStyles'
import AddBankStack from './AddBankStack'
import ConfirmRemove from './ConfirmRemove'
import EditDocs from './EditDocs'
const {width: ScreenWidth, height: ScreenHeight} = Dimensions.get('window')

class Banking extends Component {
  constructor(props) {
    super(props)
    this.state = {
      removeBank: null,
      refreshing: false,
      showAddBank: false,
      linkingBank: false,
      showConfirmRemove: false,
      editDocsModal: false,
      editDocuments: null,
      refreshingUpload: false,
      imageError: false,
      pendingAch: false,
    }
    this.inputs = {}
  }

  componentDidMount() {
    updateActiveTabListener(this.props.navigation)
    this.props.dispatch(screenView(`Banking`))
    this.getBanks()
    if (
      this.props?.route?.params?.flow === 'bankTransfer' ||
      this.props?.route?.params?.flow === 'autoPay' ||
      this.props?.route?.params?.flow === 'unit21'
    ) {
      this.showAddBank()
    }
    this.getPendingAch()
  }

  getPendingAch = () => {
    console.log('getPendingAch', this.props.loanData)

    this.props.WebService.getLoans()
      .then(res => {
        if (res.data[0]?.scheduledPayments?.length > 0) {
          let pendingBankId = res.data[0].scheduledPayments[0].bankAccount?.id
          this.setState({pendingBankId})
        }
      })
      .catch(err => {
        console.log('getLoans - err', err)
      })

    let payload = {}
    payload = {newAchAmount: '1953.17', date: '2025-03-19', nextPaymentAmount: 0}
    this.props.WebService.achLimit(payload)
      .then(res => {
        console.log('getAchLimit res', res)
      })
      .catch(err => {
        console.log('getAchLimit - err', err)
      })
  }

  getBanks = () => {
    this.setState({refreshing: true})
    this.props.WebService.getBank(this.props.loanData.id)
      .then(res => {
        /*
        accountNumber: undefined
        accountType: "checking"
        activeForDeposit: false
        activeForPayment: false
        documents: []
        furtherCredit: null
        iban: null
        id: "371cede8-c0e3-43c7-a014-81bb5d7f2209"
        isInUSA: true
        isPlaid: true
        memo: null
        name: "Plaid Saving"
        routingNumber: null
        swift: undefined
        verifiedAt: "2025-02-18T18:55:18.484Z"
        wireRoutingNumber: null
        */

        //if isPlaid true - but verifiedAt: null- it was removed

        this.props.dispatch(updateBanks(res.data))
        this.setState({refreshing: false})
      })
      .catch(err => {
        console.log('get banks', err)
        this.setState({refreshing: false})
      })

    //also need to get loanData - as long as we use it for bank tags
    this.props.WebService.getLoans().then(res => {
      const loanStatus = derivedStatusMap.get(dig(res.data[0], 'status'))
      if (res.data.length > 0 && loanStatus === 'active') {
        this.props.dispatch(updateLoans(res.data[0]))
      }
    })
  }

  editPlaidFlow = bank => {
    console.log('editPlaidFlow', bank)
    this.props.navigation.navigate('PlaidLinking', {bank})
  }

  successfulLink = e => {
    console.log('successfulLink', e)
    this.setState({linkingBank: true})
    const data = {
      plaidPublicToken: e.public_token,
      plaidBankAccountId: e.account.id,
      plaidBankAccountType: e.account.type,
      institutionName: e.institution.name,
      plaidInstitutionId: e.institution.institution_id,
    }

    return new Promise((resolve, reject) => {
      this.props.WebService.addBank(data)
        .then(res => {
          console.log('WebService.addBank', res)
          this.setState({linkingBank: false})
          this.getBanks()
          resolve(true)
        })
        .catch(err => {
          console.log('addBank err', err)
          this.setState({linkingBank: false})
          reject(false)
        })
    })
  }

  showAddBank = () => {
    this.setState({showAddBank: true})
  }

  closeAddBank = (refresh = false, returnToTransfer = false, returnToUnit21) => {
    console.log('closeAddBank', refresh, returnToTransfer, returnToUnit21)
    this.setState({showAddBank: false}, () => {
      if (refresh) {
        this.getBanks()
      }
      if (returnToTransfer) {
        //this.props.navigation.popToTop();
        //this.props.navigation.jumpTo('Loans')
        this.props.navigation.goBack()
      }
      if (returnToUnit21) {
        //this.props.navigation.popToTop();
        //this.props.navigation.jumpTo('Home');
        this.props.navigation.goBack()
        console.log('go back to unit21')
      }
    })
  }

  removeBank = async bank => {
    this.setState({removeError: false})
    console.log('removeBank', bank)

    this.props.WebService.deleteBank(bank.id)
      .then(res => {
        this.setState({showConfirmRemove: false})
        this.getBanks()
        this.props.dispatch(increaseRefreshDataCount())
      })
      .catch(err => {
        this.setState({removeError: true})
        console.log('delete bank err', err)
      })
  }

  toggleConfirmRemove = removeBank => {
    let toggleBankId = removeBank?.id
    console.log('toggleConfirmRemove', removeBank)
    if (removeBank?.activeForPayment) {
      this.setState({
        showConfirmRemove: !this.state.showConfirmRemove,
        showIsAutoPay: true,
        toggleBankId,
      })
      return
    }
    this.setState({
      removeBank,
      removeError: false,
      showConfirmRemove: !this.state.showConfirmRemove,
      showIsAutoPay: false,
      toggleBankId,
    })
  }

  editDocuments = a => {
    this.setState({editDocsModal: true, editDocuments: a.id})
  }

  closeEditDocs = () => {
    this.setState({
      editDocsModal: false,
      editDocuments: null,
      imageError: false,
    })
  }

  openImageSelect = bankID => {
    this.props.dispatch(askingForPermissions(true))

    const options = {
      title: 'Select Document',
      maxWidth: 2200,
      maxHeight: 2200,
      quality: 1.0,
      storageOptions: {
        skipBackup: true,
        path: 'images',
        cameraRoll: true,
        waitUntilSaved: true,
      },
      //takePhotoButtonTitle: null,
      noData: true,
      mediaType: 'photo',
    }

    this.setState({
      refreshingUpload: true,
      imageError: false,
    })

    ImagePicker.launchImageLibrary(options, async response => {
      if (response.didCancel) {
        console.log('User cancelled image picker')
        this.setState({refreshingUpload: false})
        return
      } else if (response.error) {
        console.log('ImagePicker Error: ', response.error)
      } else if (response.customButton) {
        console.log('User tapped custom button: ', response.customButton)
      } else {
        response = response.assets[0]
        const photo = {uri: response.uri}
        const formdata = new FormData()

        const validImage = this.validateImage(response.type, response.fileSize)
        if (validImage === 'type') {
          this.setState({refreshingUpload: false, imageError: 'type'})
          return
        } else if (validImage === 'size') {
          this.setState({refreshingUpload: false, imageError: 'size'})
          return
        }

        formdata.append('file', [
          {
            uri: Platform.OS === 'android' ? photo.uri : photo.uri.replace('file://', ''),
            name: response.fileName || 'testName.jpg',
            type: response.type,
            size: response.fileSize,
          },
        ])
        formdata.append('name', response.fileName)
        formdata.append('key', 'true')

        // show in list

        this.props.WebService.uploadDocument(
          bankID,
          'bank_account',
          'other_proof_of_bank_account',
          photo.uri.replace('file://', ''),
          response.fileName,
        )
          .then(res => {
            console.log('uploadDocument res', res)
            this.setState({refreshingUpload: false})
            this.getBanks()
            //this.closeEditDocs()
          })
          .catch(err => {
            this.setState({refreshingUpload: false})
            console.log('upload doc err', err)
          })
      }
    })
  }

  validateImage = (type, size) => {
    const fileTypes = [
      'application/pdf',
      'image/jpg',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/tif',
      'image/tiff',
    ]
    if (!fileTypes.includes(type)) {
      return 'type'
    }
    if (size / 1000000 > 50) {
      return 'size'
    }
    return true
  }

  getAccountStatus = bank => {
    const {documents} = bank
    const {loanData} = this.props

    const allDocsReviewed = documents.every(d => d.rejectedAt || d.verifiedAt)

    return [
      {
        class: 'Incomplete',
        backgroundColor: '#fb1f63',
        textColor: '#fff',
        width: 74,
        condition: !bank.isPlaid && (documents.length < 1 || documents.every(d => d.rejectedAt)),
      },
      {
        class: 'Active',
        backgroundColor: '#00ffc3',
        textColor: '#000',
        width: 64,
        condition:
          bank.id === loanData?.depositBankAccount?.id &&
          bank.id === loanData?.paymentBankAccount?.id &&
          allDocsReviewed,
      },
      {
        class: 'Pending',
        backgroundColor: '#ffad43',
        textColor: '#000',
        width: 64,
        condition: !allDocsReviewed,
      },
      {
        class: 'Deposit',
        backgroundColor: '#00ffc3',
        textColor: '#000',
        width: 64,
        condition: bank.id === loanData?.depositBankAccount?.id && allDocsReviewed,
      },
      {
        class: 'Payment',
        backgroundColor: '#00ffc3',
        textColor: '#000',
        width: 64,
        condition: bank.id === loanData?.paymentBankAccount?.id && allDocsReviewed,
      },
      {
        class: 'Not In Use',
        backgroundColor: '#AAA',
        textColor: '#000',
        width: 74,
        condition: true,
      },
    ].find(option => option.condition)
  }

  goBack = () => {
    console.log('bankingGoback', this.props)
    /*
    if (this.props.route?.params?.flow == 'home') {
      this.props.navigation.jumpTo('Home');
      return;
    }
    */
    this.props.navigation.goBack()
  }

  render() {
    const showBanks = this.props.banksArr.map((a, k) => {
      console.log('showBanks', a)
      const accountTag = this.getAccountStatus(a).class
      const accountTagColor = this.getAccountStatus(a).backgroundColor
      const accountTagWidth = this.getAccountStatus(a).width
      const accountTagTextColor = this.getAccountStatus(a).textColor
      if (!/^[a-zA-Z]+$/.test(a.name)) {
        a.name = a.name?.replace(/\W+/g, ' ')
        a.accountNumber = a.accountNumber?.replace(/\W+/g, ' ')
        a.swift = a.swift?.replace(/\W+/g, ' ')
      }

      let plaidFundingRemoved = false

      let isPlaid = a.isPlaid

      if (isPlaid) {
        a.routingNumber = '****'
      }

      let isDeactivated = false
      if (a?.dwolla?.exchange?.isReAuthRequired || a?.dwolla?.fundingSource?.isRemoved) {
        isDeactivated = true
      }

      return (
        <Card key={k} marginTop={10} cardMarginBottom={2}>
          <View style={styles.banksBoxTop}>
            <View style={styles.banksBoxHeader}>
              <Image
                source={require('../../../imgs/icons/bankIcon.png')}
                style={styles.banksLogo}
              />
              <TextBold style={styles.banksName}>{a.name}</TextBold>
            </View>
            <View
              style={{
                ...styles.banksTag,
                backgroundColor: accountTagColor,
                width: accountTagWidth,
              }}>
              <TextReg style={{color: accountTagTextColor}}>{accountTag}</TextReg>
            </View>
          </View>

          <View style={styles.banksMiddle}>
            {!isPlaid && (
              <TextReg style={{fontSize: 18, marginBottom: 6}}>
                Account Number: <TextReg style={{opacity: 0.7}}>**** **** </TextReg>
                {a.accountNumber}
              </TextReg>
            )}

            {!a.isInUSA && !isPlaid && (
              <View>
                <TextReg style={{fontSize: 18, marginBottom: 6}}>
                  IBAN: <TextReg style={{opacity: 0.7}}>**** **** </TextReg>
                  {a.iban}
                </TextReg>
                <TextReg style={{fontSize: 18, marginBottom: 6}}>
                  SWIFT: <TextReg style={{opacity: 0.7}}>**** **** </TextReg>
                  {a.swift}
                </TextReg>
              </View>
            )}
            {!isPlaid && (
              <TextReg style={{fontSize: 18, marginBottom: 16}}>
                Routing Number: {a.routingNumber}
              </TextReg>
            )}
            <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
              {accountTag.class !== 'Active' && (
                <TouchableOpacity
                  style={{marginBottom: 4}}
                  onPress={() => this.toggleConfirmRemove(a)}>
                  <TextBold style={{fontSize: 16, opacity: 0.8, color: '#00FFBD'}}>
                    REMOVE BANK
                  </TextBold>
                </TouchableOpacity>
              )}
              {plaidFundingRemoved && a.isPlaid && (
                <TouchableOpacity onPress={() => this.editPlaidFlow(a)}>
                  <TextBold style={{fontSize: 16, opacity: 0.8, color: '#FF8A33'}}>
                    UPDATE PLAID
                  </TextBold>
                </TouchableOpacity>
              )}
              {accountTag === 'Incomplete' && (
                <TouchableOpacity onPress={() => this.editDocuments(a)}>
                  <TextBold style={{fontSize: 16, opacity: 0.8, color: '#00FFBD'}}>
                    {a.documents.length < 1 ? `UPLOAD` : `EDIT DOCS`}
                  </TextBold>
                </TouchableOpacity>
              )}
            </View>
          </View>
          {isDeactivated && (
            <View>
              <TextReg style={{opacity: 0.7}}>
                {'Bank account is deactivated, please remove and add your bank account again.'}
              </TextReg>
            </View>
          )}
        </Card>
      )
    })

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Add Bank Account'} goBack={this.goBack} />
        <ScrollView
          style={styles.banksScroll}
          contentContainerStyle={{alignItems: 'center'}}
          refreshControl={
            <RefreshControl
              refreshing={this.state.refreshing}
              onRefresh={this.getBanks}
              colors={['#28283D']}
              tintColor={'#fff'}
            />
          }>
          {showBanks}
          <Button
            style={{
              marginTop: 20,
              marginBottom: 50,
              alignSelf: 'stretch',
              marginLeft: 20,
              marginRight: 20,
            }}
            disabled={this.state.refreshing}
            onPress={() => this.showAddBank()}>
            ADD BANK
          </Button>
        </ScrollView>
        <AddBankStack
          closeAddBank={this.closeAddBank}
          showAddBank={this.state.showAddBank}
          successfulLink={this.successfulLink}
          linkingBank={this.state.linkingBank}
          flow={this.props.route?.params?.flow || null}
        />
        <ConfirmRemove
          showConfirmRemove={this.state.showConfirmRemove}
          toggleConfirmRemove={this.toggleConfirmRemove}
          removeBank={this.removeBank}
          bankToGo={this.state.removeBank}
          removeError={this.state.removeError}
          showIsAutoPay={this.state.showIsAutoPay}
          pendingBankId={this.state.pendingBankId}
          toggleBankId={this.state.toggleBankId}
        />
        <EditDocs
          showEditDocs={this.state.editDocsModal}
          showPinScreen={this.props.showPinScreen}
          closeEditDocs={this.closeEditDocs}
          editDocuments={this.state.editDocuments}
          banksArr={this.props.banksArr}
          openImageSelect={this.openImageSelect}
          refreshingUpload={this.state.refreshingUpload}
          imageError={this.state.imageError}
        />
      </View>
    )
  }
}

Banking.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  loanData: state.user.loanData || {},
  account: state.auth.account,
  showPinScreen: state.auth.pinScreen,
  banksArr: state.user.banks,
  accountRef: state.auth.account.ref,
})

export default connect(mapStateToProps)(Banking)
