import React, {Component} from 'react'
import {View, Modal, SafeAreaView, TouchableOpacity, Image, Platform, AppState} from 'react-native'
import {connect} from 'react-redux'
import * as ImagePicker from 'react-native-image-picker'
import {
  create,
  open,
  LinkIOSPresentationStyle,
  LinkLogLevel,
  dismissLink,
} from 'react-native-plaid-link-sdk'
import {askingForPermissions} from '../../../store/auth/auth.actions'

import {Background, ProgressBar, TextReg} from '../../../components'
import Location from './Steps/Location'
import Connection from './Steps/Connection'
import Disclaimer from './Steps/Disclaimer'
import CustomBank from './Steps/CustomBank'
import CustomBank2 from './Steps/CustomBank2'
import UploadDocs from './Steps/UploadDocs'
import TypeCheck from './Steps/TypeCheck'

class AddBankStack extends Component {
  constructor(props) {
    super(props)
    this.state = {
      page: 0,
      location: null,
      connection: null,
      agree: false,
      documentUploaded: false,
      bankName: '',
      fieldRouting: '',
      fieldAccount: '',
      fieldSwift: '',
      wireRouting: '',
      fcc: '',
      memo: '',
      bankID: null,
      refreshingUpload: false,
      loadingAddBank: false,
      imageError: false,
      addBankError: false,
      documents: [],
      plaidLinkOpen: false,
      loadingPlaidRes: false,
      typePick: null,
      linkToken: null,
      customerId: null,
      dwollaLoading: false,
      error: false,
    }
  }

  componentDidMount() {
    this.appStateSubscription = AppState.addEventListener('change', nextAppState => {
      if (nextAppState === 'active') {
        this.setState({dwollaLoading: false})
      }
    })

    if (this.props.flow == 'autoPay' || this.props.flow == 'bankTransfer') {
      this.setState({location: 'US', page: 2})
    }
  }

  componentWillUnmount() {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove()
    }
  }

  next = () => {
    let {page, typePick} = this.state

    console.log('next-1', page, typePick, this.state.location, this.state.connection)

    if (page === 0 && this.state.location == 'Outside') {
      console.log('Outside')
      page += 2
    }

    if (page == 0 && this.props.flow == 'autoPay') {
      //only plaid - go to disclaimer
      page += 1
    }

    if (page == 4) {
      if (typePick == 3 || typePick == 4 || typePick == 5) {
        //goto page 5
      } else {
        //skip page 5
        page += 1
      }
    }

    //if manual, skip disclaimer page
    if (this.state.connection == 'Manual' && page == 1) {
      page += 1
    }

    let showPlaid = true
    if (this.state.location == 'Outside' || this.props.launchDarkly['disable-ach']) {
      showPlaid = false
    }

    if (page == 0 && (!showPlaid || this.state.location == 'Outside')) {
      //so straigh to disclaimer
      this.setState({how: 'Manual'})
      page += 2
    }

    page += 1

    console.log('next-2', page)
    this.setState({page})
  }

  back = () => {
    let {page, typePick} = this.state
    console.log('back', page, typePick)
    let showPlaid = false
    if (this.state.location != 'Outside' && this.props.flow != 'unit21') {
      showPlaid = true
    }
    if (this.props.launchDarkly['disable-ach']) {
      showPlaid = false
    }

    let nextPage = 0
    if (page == 0) {
      console.log('back 0')
      this.resetAndClose()
    } else if (page == 6 && !(typePick == 3 || typePick == 4 || typePick == 5)) {
      console.log('back 1')
      nextPage = page - 2
    } else if (page == 2 && (this.state.location == 'Outside' || !showPlaid)) {
      console.log('back 2')
      nextPage = page - 2
    } else if ((page == 3 && this.state.location == 'Outside') || (page == 3 && !showPlaid)) {
      console.log('back-outside')
      nextPage = page - 3
    } else if (page == 3 && this.state.connection == 'Manual') {
      console.log('back-manual')
      nextPage = page - 2
    } else if (page == 2 && (this.props.flow == 'autoPay' || this.props.flow == 'bankTransfer')) {
      console.log('back-reset')
      this.resetAndClose()
    } else {
      nextPage = page - 1
      console.log('back-nextPage, end', nextPage)
    }
    this.setState({page: nextPage})
  }

  where = location => {
    if (this.state.location === location) {
      location = null
    }
    this.setState({location})
  }

  how = connection => {
    if (this.state.connection === connection) {
      connection = null
    }
    this.setState({connection})
  }

  toggleAgree = () => {
    this.setState({agree: !this.state.agree})
  }

  addCustomBank = () => {
    console.log('addCustomBank')
    let {typePick, wireRouting, fcc, memo} = this.state
    this.setState({
      loadingAddBank: true,
      addBankError: false,
    })

    let accountType = 'investment' //3
    if (typePick == 4) {
      accountType = 'trust'
    }
    if (typePick == 5) {
      accountType = 'other'
    }

    const data = {
      name: this.state.bankName,
      isInUSA: this.state.location == 'US',
      accountType,
      routingNumber: this.state.fieldRouting,
      accountNumber: this.state.fieldAccount,
      swift: this.state.fieldSwift,
      iban: this.state.fieldAccount,
      wireRoutingNumber: wireRouting,
      furtherCredit: fcc,
      memo: memo,
    }
    this.props.WebService.addManualBank(data)
      .then(res => {
        this.setState({bankID: res.data.id, loadingAddBank: false})
        this.next()
      })
      .catch(err => {
        console.log('err', err)
        this.setState({addBankError: true, loadingAddBank: false})
      })
  }

  addManualBank = (document = false) => {
    let {typePick} = this.state
    if (typePick == 3 || typePick == 4 || typePick == 5) {
      //use custom bank 2 & addCustomBank() submit
      this.next()
      return
    }
    this.setState({
      loadingAddBank: true,
      addBankError: false,
    })
    let accountType = 'checking'
    if (typePick == 2) {
      accountType = 'savings'
    }

    const data = {
      name: this.state.bankName,
      isInUSA: this.state.location == 'US',
      accountType,
      routingNumber: this.state.fieldRouting,
      accountNumber: this.state.fieldAccount,
      swift: this.state.fieldSwift,
      iban: this.state.fieldAccount,
      wireRoutingNumber: '',
      furtherCredit: '',
      memo: '',
    }
    this.props.WebService.addManualBank(data)
      .then(res => {
        this.setState({bankID: res.data.id, loadingAddBank: false})
        this.next()
      })
      .catch(err => {
        this.setState({addBankError: true, loadingAddBank: false})
      })
  }

  removeDocument = id => {
    console.log('remove document', id)
    this.props.WebService.removeDocument(id)
      .then(res => {
        console.log('WebService.removeDocument', res)
        const documents = this.state.documents.filter(a => a.id != id)
        this.setState({documents})
      })
      .catch(err => {
        console.log('documents err', err)
      })
  }

  openImageSelect = bankID => {
    this.props.dispatch(askingForPermissions(true))

    const options = {
      title: 'Select Document',
      maxWidth: 2200,
      maxHeight: 2200,
      quality: 1.0,
      storageOptions: {
        skipBackup: true,
        path: 'images',
        cameraRoll: true,
        waitUntilSaved: true,
      },
      //takePhotoButtonTitle: null,
      noData: true,
      mediaType: 'photo',
    }

    this.setState({
      refreshingUpload: true,
      imageError: false,
    })

    ImagePicker.launchImageLibrary(options, async response => {
      if (response.didCancel) {
        this.setState({refreshingUpload: false})
        return
      } else if (response.error) {
        this.setState({refreshingUpload: false})
        return
      } else if (response.customButton) {
        console.log('User tapped custom button: ', response.customButton)
      } else {
        response = response.assets[0]
        const photo = {uri: response.uri}
        const formdata = new FormData()

        const validImage = this.validateImage(response.type, response.fileSize)
        if (validImage === 'type') {
          this.setState({refreshingUpload: false, imageError: 'type'})
          return
        } else if (validImage === 'size') {
          this.setState({refreshingUpload: false, imageError: 'size'})
          return
        }

        let path = response.uri
        if (Platform.OS === 'ios') {
          path = '~' + path.substring(path.indexOf('/Documents'))
        }
        if (!response.fileName) response.fileName = path.split('/').pop()

        formdata.append('file', [
          {
            uri: Platform.OS === 'android' ? photo.uri : photo.uri.replace('file://', ''),
            name: response.fileName || 'testName.jpg',
            type: response.type,
            size: response.fileSize,
          },
        ])
        formdata.append('name', response.fileName)
        formdata.append('key', 'true')

        this.props.WebService.uploadDocument(
          bankID,
          'bank_account',
          'other_proof_of_bank_account',
          photo.uri.replace('file://', ''),
          response.fileName,
        )
          .then(res => {
            console.log('uploaddocument res', res)
            const documents = this.state.documents
            const parsedData = JSON.parse(res.data)
            documents.push({
              name: response.fileName,
              id: parsedData.id,
            })
            this.setState({
              refreshingUpload: false,
              documents,
            })
          })
          .catch(err => {
            console.log('uploaddocument err', err)
            this.setState({refreshingUpload: false})
          })
      }
    })
  }

  validateImage = (type, size) => {
    const fileTypes = [
      'application/pdf',
      'image/jpg',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/tif',
      'image/tiff',
    ]
    if (!fileTypes.includes(type)) {
      return 'type'
    }
    if (size / 1000000 > 50) {
      return 'size'
    }
    return true
  }

  resetAndClose = (refresh = false) => {
    this.setState(
      {
        plaidLinkOpen: false,
        loadingPlaidRes: false,
        plaidResError: false,
        page: 0,
        location: null,
        connection: null,
        agree: false,
        bankName: '',
        fieldRouting: '',
        fieldAccount: '',
        fieldSwift: '',
        documents: [],
        wireRouting: '',
        fcc: '',
        memo: '',
        typePick: null,
        error: false,
      },
      () => {
        let returnToTransfer = false
        let returnToUnit21 = false
        if (this.props.flow === 'bankTransfer' || this.props.flow === 'autoPay') {
          returnToTransfer = true
        }
        if (this.props.flow === 'unit21') {
          returnToUnit21 = true
        }
        this.props.closeAddBank(refresh, returnToTransfer, returnToUnit21)
      },
    )
  }

  createLinkOpenProps = () => {
    return {
      onSuccess: success => {
        this.setState({dwollaLoading: true})

        console.log(' link success: ', success)
        let publicToken = success?.publicToken
        let name = success?.metadata?.accounts[0]?.name
        let type = 'checking'
        if (success?.metadata?.accounts && success?.metadata?.accounts[0]?.subtype == 'savings') {
          type = 'savings'
        }
        this.swapToken(publicToken, name, type)
      },
      onExit: linkExit => {
        // User exited Link session. There may or may not be an error depending on what occurred.
        console.log('link exit: ', linkExit)
        dismissLink()
        this.setState({dwollaLoading: false})
      },
      // MODAL or FULL_SCREEN presentation on iOS. Defaults to MODAL.
      iOSPresentationStyle: LinkIOSPresentationStyle.MODAL, //LinkIOSPresentationStyle.FULL_SCREEN, //LinkIOSPresentationStyle.MODAL,
      logLevel: LinkLogLevel.ERROR,
    }
  }

  swapToken = async (publicToken, name = 'name', type = 'checking') => {
    if (!publicToken || !this.state.customerId) {
      console.log('no public token or account id')
      return
    }

    let customerId = this.state.customerId
    console.log('swapToken', customerId, publicToken)

    try {
      console.log('dwollaExchange try', customerId, publicToken)
      let res = await this.props.WebService.dwollaExchange(customerId, {
        plaidPublicToken: publicToken,
      })

      let exchangeId = res?.data?.id

      try {
        console.log('dwollaFundingSource', {
          exchangeId,
          bankAccountType: type,
          name,
        })

        let res = await this.props.WebService.dwollaFundingSource(customerId, {
          exchangeId,
          bankAccountType: type,
          name,
        })
        console.log('dwollaFundingSource', res)
        this.setState({dwollaLoading: false})
        this.resetAndClose(true)

        let fundingSourceId = res?.data?.fundingSourceId
      } catch (err) {
        console.log('dwollaFundingSource err', err)
        if (err?.data?.body?.error?.includes('Bank already exists')) {
          this.setState({error: 'Bank already exists, please try again'})
        }
        this.setState({dwollaLoading: false})
      }
    } catch (err) {
      console.log('dwollaExchange err', err)
      this.setState({dwollaLoading: false})
    }
  }

  openPlaidLink = async (email, firstName, lastName) => {
    this.setState({dwollaLoading: true, error: false, dwollaCustomerError: false})

    //first get customer / session

    let customerId = ''

    console.log('1', email, firstName, lastName)

    //if not a customer, make
    try {
      let res = await this.props.WebService.getDwollaCustomers()
      console.log('getDwollaCustomers', res)

      //id from database
      //dwollaResourceId

      if (res?.data?.length < 1) {
        console.log('make dwolla customer', email)
        let res = await this.props.WebService.makeDwollaCustomer({
          email,
          isAgreementSigned: true,
          firstName,
          lastName,
        })
        console.log('makeDwollaCustomer', res)
        customerId = res?.data[0]?.id || res?.data?.id
        this.setState({customerId})
      } else {
        customerId = res?.data[0]?.id || res?.data?.id

        console.log('customerId', customerId)
        this.setState({customerId})
      }
    } catch (err) {
      console.log('make dwolla customer err', err)
      this.setState({dwollaCustomerError: err?.data?.body?.error, dwollaLoading: false})
    }

    console.log('create exchange session', customerId)

    let linkToken = ''

    try {
      let res = await this.props.WebService?.dwollaExchangeSession(customerId)
      console.log('dwollaExchangeSession', res)
      linkToken = res?.data?.plaidLinkToken
      this.setState({linkToken})
    } catch (err) {
      console.log('dwollaExchangeSession err', err)
    }

    console.log('customerId', customerId)

    if (!linkToken) {
      console.log('no link token')
      return
    }

    //signup the disclaimer

    try {
      console.log('openPlaidLink', linkToken)
      create({
        token: linkToken,
        noLoadingState: false,
      })

      const openProps = this.createLinkOpenProps()
      console.log('openProps', openProps)

      openProps.onExit(a => {
        console.log('onExit', a())
      })
      openProps.onSuccess(a => {
        console.log('onSuccess', a())
      })
      console.log('openProps', openProps)

      open(openProps)
      console.log('after open')
    } catch (err) {
      console.log('openPlaidLink err', err)
      this.setState({dwollaLoading: false})
    }
  }

  handleUpdatebankName = text => {
    this.setState({bankName: text, error: false, errorMessage: ''})
  }

  handleUpdateFieldRouting = text => {
    let alphaNumeric = new RegExp(`^[a-zA-Z0-9_]+$`)
    let isValid = text == '' || alphaNumeric.test(text)
    if (!isValid) return
    this.setState({fieldRouting: text, error: false, errorMessage: ''})
  }

  handleUpdateFieldAccount = text => {
    console.log('handleUpdateFieldAccount', text)
    let alphaNumeric = new RegExp(`^[a-zA-Z0-9_]+$`)
    let isValid = text == '' || alphaNumeric.test(text)
    if (!isValid) return
    this.setState({fieldAccount: text, error: false, errorMessage: ''})
  }

  handleUpdateFieldSwift = text => {
    let alphaNumeric = new RegExp(`^[a-zA-Z0-9_]+$`)
    let isValid = text == '' || alphaNumeric.test(text)
    if (!isValid) return
    this.setState({fieldSwift: text, error: false, errorMessage: ''})
  }

  handleUpdateWireRouting = text => {
    let alphaNumeric = new RegExp(`^[a-zA-Z0-9_]+$`)
    let isValid = text == '' || alphaNumeric.test(text)
    if (!isValid) return
    this.setState({wireRouting: text, error: false, errorMessage: ''})
  }

  handleUpdateFcc = text => {
    this.setState({fcc: text, error: false, errorMessage: ''})
  }

  handleUpdateMemo = text => {
    this.setState({memo: text, error: false, errorMessage: ''})
  }

  successfulLinkAndClose = e => {
    console.log('successfulLinkAndClose', e)
    this.setState({loadingPlaidRes: true}, () => {
      this.props
        .successfulLink(e)
        .then(res => {
          this.setState({plaidLinkOpen: false}, () => {
            this.resetAndClose(true)
          })
        })
        .catch(err => {
          this.setState({plaidResError: true})
          console.log('add banks reject err', err)
        })
    })
  }

  render() {
    //console.log('add bank stack', this.props)
    let {page} = this.state
    console.log('page- ', page)
    const {showAddBank, showPinScreen, linkingBank} = this.props
    let visible = showAddBank && !showPinScreen
    if (this.state.plaidLinkOpen) {
      visible = showAddBank
    }

    return (
      <Modal animationType="slide" visible={visible} onRequestClose={() => ({})}>
        <Background />
        <SafeAreaView
          style={{
            backgroundColor: '#28283D',
            flex: 1,
          }}>
          <View style={{alignSelf: 'stretch', marginBottom: 26, marginTop: 14}}>
            <View
              style={{
                flexDirection: 'row',
                alignSelf: 'stretch',
                justifyContent: 'space-between',
                paddingRight: 10,
                paddingLeft: 10,
                alignItems: 'center',
              }}>
              {page !== 5 ? (
                <TouchableOpacity onPress={() => this.back()}>
                  <Image
                    source={require('../../../imgs/backToSettings.png')}
                    style={{height: 25, width: 25}}
                  />
                </TouchableOpacity>
              ) : (
                <View style={{height: 25, width: 25}} />
              )}
              <View
                style={{
                  alignSelf: 'stretch',
                  flex: 1,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <TextReg style={{fontSize: 20}}>Add Bank Account</TextReg>
              </View>
              <TouchableOpacity onPress={() => this.resetAndClose()}>
                <Image
                  source={require('../../../imgs/closeX.png')}
                  style={{height: 26, width: 26}}
                />
              </TouchableOpacity>
            </View>
            <View
              style={{
                alignSelf: 'stretch',
                flex: 1,
                marginLeft: 16,
                marginRight: 16,
                marginTop: 6,
              }}>
              <ProgressBar complete={this.state.page + 1} total={7} />
            </View>
          </View>

          {page === 0 && (
            <Location location={this.state.location} where={this.where} next={this.next} />
          )}
          {page === 1 && (
            <Connection
              connection={this.state.connection}
              how={this.how}
              next={this.next}
              location={this.state.location}
              flow={this.props.flow}
              launchDarkly={this.props.launchDarkly}
            />
          )}
          {page === 2 && (
            <Disclaimer
              connection={this.state.connection}
              how={this.how}
              location={this.state.location}
              next={this.next}
              successfulLink={this.successfulLinkAndClose}
              toggleAgree={this.toggleAgree}
              agree={this.state.agree}
              linkingBank={linkingBank}
              resetAndClose={this.resetAndClose}
              openPlaidLink={this.openPlaidLink}
              loadingPlaidRes={this.state.loadingPlaidRes}
              plaidResError={this.state.plaidResError}
              dwollaLoading={this.state.dwollaLoading}
              error={this.state.error}
              dwollaCustomerError={this.state.dwollaCustomerError}
              user={this.props.user}
            />
          )}
          {page === 3 && (
            <TypeCheck
              pick={this.state.typePick}
              setPick={typePick => this.setState({typePick})}
              next={this.next}
            />
          )}
          {page === 4 && (
            <CustomBank
              location={this.state.location}
              addManualBank={this.addManualBank}
              bankName={this.state.bankName}
              fieldAccount={this.state.fieldAccount}
              fieldRouting={this.state.fieldRouting}
              fieldSwift={this.state.fieldSwift}
              handleUpdatebankName={this.handleUpdatebankName}
              handleUpdateFieldAccount={this.handleUpdateFieldAccount}
              handleUpdateFieldRouting={this.handleUpdateFieldRouting}
              handleUpdateFieldSwift={this.handleUpdateFieldSwift}
              loadingAddBank={this.state.loadingAddBank}
              addBankError={this.state.addBankError}
            />
          )}
          {page === 5 && (
            <CustomBank2
              location={this.state.location}
              addCustomBank={this.addCustomBank}
              bankName={this.state.bankName}
              wireRouting={this.state.wireRouting}
              fcc={this.state.fcc}
              memo={this.state.memo}
              handleUpdateWireRouting={this.handleUpdateWireRouting}
              handleUpdateFcc={this.handleUpdateFcc}
              handleUpdateMemo={this.handleUpdateMemo}
              loadingAddBank={false}
              addBankError={false}
            />
          )}

          {page === 6 && (
            <UploadDocs
              bankID={this.state.bankID}
              openImageSelect={this.openImageSelect}
              imageError={this.state.imageError}
              refreshingUpload={this.state.refreshingUpload}
              resetAndClose={this.resetAndClose}
              documents={this.state.documents}
              removeDocument={this.removeDocument}
            />
          )}
        </SafeAreaView>
      </Modal>
    )
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
  user: state.user.user,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(AddBankStack)
