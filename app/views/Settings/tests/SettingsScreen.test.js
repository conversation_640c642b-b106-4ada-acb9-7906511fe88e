/* eslint-disable import/no-namespace */
/* eslint-disable no-undef */

import React from 'react';
import * as enzyme from 'enzyme';
import thunk from 'redux-thunk';
import configureMockStore from 'redux-mock-store';
import SettingsScreen from './SettingsScreen';
import initialState from '../../store/initialState';

import DeviceInfo from 'react-native-device-info';

const mockStore = configureMockStore([thunk]);

const buildComponent = (renderType = enzyme.shallow, newProps = {}) => {
  const defaultProps = {
    dispatch: jest.fn(() => Promise.resolve({})),
    navigation: {
      addListener: jest.fn(),
    },
  };

  const props = {...defaultProps, ...newProps};
  return renderType(
    <SettingsScreen store={mockStore(initialState)} {...props} />,
  );
};

describe('Testing SettingsScreen', () => {
  it('renders component', () => {
    const wrapper = buildComponent();
    const render = wrapper.dive();
    expect(render.length).toBe(1);
  });
});
