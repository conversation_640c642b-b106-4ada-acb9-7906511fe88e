import React, {Component} from 'react'
import BigNumber from 'bignumber.js'
import {View, ScrollView, Image} from 'react-native'
import LinearGradient from 'react-native-linear-gradient'
import {connect} from 'react-redux'

import {<PERSON><PERSON>, TextReg, BackgroundHeader, TextBold} from '../../../components'
import stabilizeGraph from '../../../imgs/icons/stabilizeGraph.png'
import noStabilizeGraph from '../../../imgs/icons/noStabilizeGraph.png'
import greenCheck from '../../../imgs/icons/greenCheck.png'
import {dig, formatCurrency} from '../../../util/helpers'
import raftTriangle from '../../../imgs/icons/raftTriangle.png'
import raftTriangle2 from '../../../imgs/icons/raftTriangle2.png'
import {derivedStatusMap} from '../../../util/enumerables'
import {updateLoans} from '../../../store/user/user.actions'

import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'

class MarginCallSettings extends Component {
  constructor(props) {
    super(props)
    this.state = {
      bannerWidth: 100,
      selected: this.props.loanData?.marginManagementPreference || false,
      loading: false,
    }
  }

  getLoansData = async () => {
    this.setState({refreshing: true})
    this.props.WebService.getLoans()
      .then(async res => {
        const loanStatus = derivedStatusMap.get(dig(res.data[0], 'status'))
        if (res.data.length > 0 && loanStatus === 'active') {
          await this.props.dispatch(updateLoans(res.data[0]))
        } else if (loanStatus === 'pending') {
          await this.props.dispatch(updateLoans(res.data[0]))
          //this.setState({ pendingLoan: true })
        }
        if (res.data.length === 0) {
          this.props.dispatch(updateLoans({status: 'none'}))
          this.setState({noLoans: true})
        }
        //this.getWallets();
        this.setState({refreshing: false})
      })
      .catch(err => {
        console.log('err', err)
        this.setState({
          refreshing: false,
          connectedModalVisable: true,
        })
        if (err && err.status === 401) {
          this.props.dispatch(logout())
        }
      })
  }

  submit = async () => {
    this.setState({loading: true})
    try {
      await this.props.WebService.setMarginManagementPreference(this.props.loanData?.id, {
        marginManagementPreference: this.state.selected,
      })
      await this.getLoansData()
      this.setState({loading: false})
      this.props.navigation.goBack()
    } catch (error) {
      this.setState({loading: false})
      console.log('error: ', error)
    }
  }

  render() {
    let bannerHeight = (this.state.bannerWidth + 10) / 5.6
    const loan = this.props.loanData

    //new
    const currentBalance = new BigNumber(loan?.amortizationInfo?.currentBalance)
    const collatValProj = new BigNumber(currentBalance).dividedBy(0.9091)
    const feePercent = new BigNumber(0.05)

    const principleAmountNeeded = ltvGoal => {
      return ltvGoal
        .times(currentBalance.dividedBy(0.9091))
        .minus(currentBalance)
        .dividedBy(ltvGoal.times(feePercent.plus(1)).minus(1))
    }

    const suggestedDepositPrinciple = principleAmountNeeded(
      new BigNumber(loan.thresholds?.marginCure || 0.7),
    )
    const cureAmount = suggestedDepositPrinciple.gt(0)
      ? suggestedDepositPrinciple
      : new BigNumber(0)
    const collateral = loan?.collateralValue
    const stabilizationFee = new BigNumber(collatValProj).multipliedBy(0.03)
    const liquidationFee = new BigNumber(cureAmount).multipliedBy(0.05)
    const totalLiquidationAmount = cureAmount.plus(liquidationFee)
    const postStabilizationCollateral = new BigNumber(collatValProj).minus(stabilizationFee)
    const postLiquidationCollateral = new BigNumber(collatValProj).minus(totalLiquidationAmount)
    const postStabilizationLtv = new BigNumber(currentBalance)
      .dividedBy(postStabilizationCollateral)
      .multipliedBy(100)
    const postLiquidationLoanAmount = new BigNumber(currentBalance).minus(cureAmount)

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={' '} goBack={this.props.navigation.goBack} />
        <ScrollView style={styles.scrollView} contentContainerStyle={{alignItems: 'center'}}>
          <TextReg
            style={{
              fontSize: 24,
              color: '#fff',
              marginTop: 0,
              marginBottom: 10,
            }}>{`Confirm Your Risk Management`}</TextReg>
          <TextReg
            style={{fontSize: 16, color: '#fff', marginTop: 10, marginLeft: 16, marginRight: 16}}>
            {`Select which risk management option you’d like for your loan. This option can be adjusted once your loan is active by visiting the Stabilization tab in your loan’s dashboard.`}
          </TextReg>
          <TextReg style={{fontSize: 20, color: '#fff', marginTop: 20}}>{`Choose One:`}</TextReg>

          {/* 1st option */}
          <View
            style={[
              local.longBox,
              {
                position: 'relative',
                borderColor: this.state.selected === 'stabilization' ? '#00FFBD' : '#777',
              },
            ]}
            onLayout={event => {
              const {width} = event.nativeEvent.layout
              this.setState({bannerWidth: width})
            }}>
            <View style={{position: 'absolute', top: 0, left: 0, right: 0, bottom: 0}}>
              <LinearGradient
                colors={['rgba(101, 58, 216, 0.8)', 'rgba(101, 58, 216, 0)']}
                style={{
                  height: 140,
                  alignSelf: 'stretch',
                  borderRadius: 14,
                  alignItems: 'center',
                }}
                start={{x: 0, y: 0}}
                end={{x: 0, y: 1}}></LinearGradient>
            </View>

            <TextBold
              style={{fontSize: 20, color: '#fff', marginTop: 20}}>{`Stabilization`}</TextBold>
            <TextReg
              style={{
                marginLeft: 20,
                marginRight: 20,
                marginTop: 10,
              }}>{`If you select Stabilization and your LTV reaches 90.91%, then a Stabilization event will convert your assets to stablecoin. You can convert back once your LTV is reduced.`}</TextReg>

            <View style={{alignSelf: 'stretch', marginTop: 20, backgroundColor: '#3D3D50'}}>
              <Image
                source={raftTriangle}
                style={{
                  width: this.state.bannerWidth - 2,
                  height: bannerHeight,
                  marginTop: -2,
                }}
              />

              <View
                style={{
                  alignSelf: 'center',
                  width: 250,
                  height: 100,
                  borderRadius: 4,
                  marginTop: -15,
                  alignItems: 'center',
                }}>
                <Image source={stabilizeGraph} style={{width: 250, height: 102}} />
              </View>

              <View style={local.bubbleRow}>
                <View style={local.bubbleText}>
                  <TextBold style={{color: '#000', fontSize: 14}}>{`Pre-Stabilization`}</TextBold>
                </View>
                <View style={{height: 1, flex: 1, backgroundColor: '#FFF'}} />
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  alignSelf: 'stretch',
                  marginHorizontal: 20,
                  marginVertical: 6,
                }}>
                <TextReg style={{color: '#FFF', fontSize: 13}}>{`Collateral Value`}</TextReg>
                <TextBold style={{color: '#FFF', fontSize: 23}}>
                  ${formatCurrency(collateral)}
                </TextBold>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignSelf: 'stretch',
                  alignItems: 'center',
                  marginHorizontal: 20,
                  marginTop: 5,
                  marginBottom: 10,
                }}>
                <TextReg style={{color: '#FFF', fontSize: 13}}>{`Current Loan Amount`}</TextReg>
                <TextBold style={{color: '#FFF', fontSize: 18}}>
                  ${formatCurrency(loan?.amortizationInfo?.currentBalance)}
                </TextBold>
              </View>

              <View style={{...local.bubbleRow, marginTop: 4}}>
                <View style={local.bubbleTextLong}>
                  <TextBold
                    style={{
                      color: '#000',
                      fontSize: 14,
                    }}>{`Stabilization Event (LTV 90.91%)`}</TextBold>
                </View>
                <View style={{height: 1, flex: 1, backgroundColor: '#FFF'}} />
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignSelf: 'stretch',
                  marginHorizontal: 20,
                  marginVertical: 6,
                  alignItems: 'center',
                }}>
                <TextReg
                  style={{color: '#FFF', fontSize: 16}}>{`Trigger Collateral Value`}</TextReg>
                <TextBold style={{color: '#FFF', fontSize: 18}}>
                  ${formatCurrency(collatValProj)}
                </TextBold>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignSelf: 'stretch',
                  marginHorizontal: 20,
                  marginVertical: 10,
                }}>
                <TextReg style={{color: '#FFF', fontSize: 16}}>{`Stabilization Fee`}</TextReg>
                <TextBold style={{color: '#FFF', fontSize: 16}}>
                  -${formatCurrency(stabilizationFee)}
                </TextBold>
              </View>
              <View style={{...local.bubbleRow, marginTop: 4}}>
                <View style={local.bubbleText}>
                  <TextBold style={{color: '#000', fontSize: 14}}>{`Post-Stabilization`}</TextBold>
                </View>
                <View style={{height: 1, flex: 1, backgroundColor: '#FFF'}} />
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignSelf: 'stretch',
                  marginHorizontal: 20,
                  marginVertical: 6,
                  alignItems: 'center',
                }}>
                <TextReg style={{color: '#FFF', fontSize: 16}}>{`Collateral Value`}</TextReg>
                <TextBold style={{color: '#FFF', fontSize: 28}}>
                  ~${formatCurrency(postStabilizationCollateral)}
                </TextBold>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignSelf: 'stretch',
                  alignItems: 'center',
                  margin: 20,
                  marginVertical: 10,
                }}>
                <TextReg style={{color: '#FFF', fontSize: 13}}>{`Loan Amount`}</TextReg>
                <TextBold style={{color: '#FFF', fontSize: 18}}>
                  ~${formatCurrency(loan?.amortizationInfo?.currentBalance)}
                </TextBold>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignSelf: 'stretch',
                  alignItems: 'center',
                  margin: 20,
                  marginTop: 0,
                }}>
                <TextReg style={{color: '#FFF', fontSize: 16}}>{`LTV`}</TextReg>
                <TextBold style={{color: '#FFF', fontSize: 18}}>
                  ~{formatCurrency(postStabilizationLtv)}%
                </TextBold>
              </View>

              {this.state.selected === 'stabilization' && (
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    alignSelf: 'stretch',
                    justifyContent: 'center',
                    marginBottom: 34,
                    marginTop: 10,
                  }}>
                  <Image source={greenCheck} style={{width: 20, height: 20, marginRight: 6}} />
                  <TextReg
                    style={{
                      color: '#00FFBD',
                      fontSize: 20,
                      letterSpacing: 2,
                    }}>{`SELECTED`}</TextReg>
                </View>
              )}
              {this.state.selected != 'stabilization' && (
                <Button
                  onPress={() => {
                    this.setState({selected: 'stabilization'})
                  }}
                  style={{
                    alignSelf: 'stretch',
                    marginBottom: 20,
                    marginLeft: 20,
                    marginRight: 20,
                    borderRadius: 8,
                    backgroundColor: '#28283D',
                    borderWidth: 1,
                    borderColor: '#00FFBD',
                  }}>
                  <TextBold
                    style={{
                      fontSize: 18,
                      color: '#000',
                      letterSpacing: 2,
                      color: '#00FFBD',
                    }}>
                    {`SELECT OPTION`}
                  </TextBold>
                </Button>
              )}
            </View>
          </View>

          {/* 2nd option */}
          <View
            style={[
              local.longBox,
              {
                position: 'relative',
                borderColor: this.state.selected === 'liquidation' ? '#00FFBD' : '#777',
              },
            ]}
            onLayout={event => {
              const {width} = event.nativeEvent.layout
              this.setState({bannerWidth: width})
            }}>
            <View style={{position: 'absolute', top: 0, left: 0, right: 0, bottom: 0}}>
              <LinearGradient
                colors={['rgba(101, 58, 216, 0.8)', 'rgba(101, 58, 216, 0)']}
                style={{
                  height: 140,
                  alignSelf: 'stretch',
                  borderRadius: 14,
                  alignItems: 'center',
                }}
                start={{x: 0, y: 0}}
                end={{x: 0, y: 1}}></LinearGradient>
            </View>

            <TextBold
              style={{fontSize: 20, color: '#fff', marginTop: 20}}>{`Liquidation`}</TextBold>
            <TextReg
              style={{
                marginLeft: 20,
                marginRight: 20,
                marginTop: 10,
              }}>{`A liquidation event cures your LTV into a healthy state by immediately applying the proceeds of the sale of your collateral to the principal amount due on the loan.`}</TextReg>

            <View style={{alignSelf: 'stretch', marginTop: 20, backgroundColor: '#3D3D50'}}>
              <Image
                source={raftTriangle2}
                style={{
                  width: this.state.bannerWidth - 2,
                  height: bannerHeight,
                  marginTop: -6,
                }}
              />

              <View
                style={{
                  alignSelf: 'center',
                  width: 250,
                  height: 100,
                  borderRadius: 4,
                  marginTop: -15,
                  alignItems: 'center',
                }}>
                <Image source={noStabilizeGraph} style={{width: 250, height: 102}} />
              </View>

              <View style={local.bubbleRow}>
                <View style={local.bubbleText}>
                  <TextBold style={{color: '#000', fontSize: 14}}>{`Pre-Liquidation`}</TextBold>
                </View>
                <View style={{height: 1, flex: 1, backgroundColor: '#FFF'}} />
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  alignSelf: 'stretch',
                  marginHorizontal: 20,
                  marginVertical: 6,
                }}>
                <TextReg style={{color: '#FFF', fontSize: 13}}>{`Collateral Value`}</TextReg>
                <TextBold style={{color: '#FFF', fontSize: 23}}>
                  ${formatCurrency(collateral)}
                </TextBold>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignSelf: 'stretch',
                  marginHorizontal: 20,
                  marginTop: 5,
                  marginBottom: 10,
                }}>
                <TextReg style={{color: '#FFF', fontSize: 13}}>{`Current Loan Amount`}</TextReg>
                <TextBold style={{color: '#FFF', fontSize: 18}}>
                  ${formatCurrency(loan?.amortizationInfo?.currentBalance)}
                </TextBold>
              </View>

              <View style={{...local.bubbleRow, marginTop: 4}}>
                <View style={local.bubbleTextLong}>
                  <TextBold
                    style={{
                      color: '#000',
                      fontSize: 14,
                    }}>{`Liquidation Event (LTV 90.91%)`}</TextBold>
                </View>
                <View style={{height: 1, flex: 1, backgroundColor: '#FFF'}} />
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignSelf: 'stretch',
                  marginHorizontal: 20,
                  marginVertical: 6,
                  alignItems: 'center',
                }}>
                <TextReg
                  style={{color: '#FFF', fontSize: 16}}>{`Trigger Collateral Value`}</TextReg>
                <TextBold style={{color: '#FFF', fontSize: 18}}>
                  ${formatCurrency(collatValProj)}
                </TextBold>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignSelf: 'stretch',
                  marginHorizontal: 20,
                  marginVertical: 6,
                }}>
                <TextReg
                  style={{
                    color: '#FFF',
                    fontSize: 16,
                    width: '50%',
                  }}>{`Liquidation Amount to Cure Loan to 70% LTV`}</TextReg>
                <TextBold style={{color: '#FFF', fontSize: 18}}>
                  -${formatCurrency(cureAmount)}
                </TextBold>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignSelf: 'stretch',
                  marginHorizontal: 20,
                  marginBottom: 10,
                }}>
                <TextReg style={{color: '#FFF', fontSize: 16}}>{`Liquidation Fee`}</TextReg>
                <TextBold style={{color: '#FFF', fontSize: 16}}>
                  -${formatCurrency(liquidationFee)}
                </TextBold>
              </View>
              <View style={{...local.bubbleRow, marginTop: 4}}>
                <View style={local.bubbleText}>
                  <TextBold style={{color: '#000', fontSize: 14}}>{`Post-Liquidation`}</TextBold>
                </View>
                <View style={{height: 1, flex: 1, backgroundColor: '#FFF'}} />
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignSelf: 'stretch',
                  marginHorizontal: 20,
                  marginVertical: 6,
                  alignItems: 'center',
                }}>
                <TextReg style={{color: '#FFF', fontSize: 16}}>{`Collateral Value`}</TextReg>
                <TextBold style={{color: '#FFF', fontSize: 28}}>
                  ~${formatCurrency(postLiquidationCollateral)}
                </TextBold>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignSelf: 'stretch',
                  alignItems: 'center',
                  margin: 20,
                  marginVertical: 10,
                }}>
                <TextReg style={{color: '#FFF', fontSize: 13}}>{`Loan Amount`}</TextReg>
                <TextBold style={{color: '#FFF', fontSize: 18}}>
                  ~${formatCurrency(postLiquidationLoanAmount)}
                </TextBold>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignSelf: 'stretch',
                  margin: 20,
                  marginTop: 0,
                }}>
                <TextReg style={{color: '#FFF', fontSize: 13}}>{`LTV`}</TextReg>
                <TextBold style={{color: '#FFF', fontSize: 18}}>{`70.00%`}</TextBold>
              </View>

              {this.state.selected === 'liquidation' && (
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    alignSelf: 'stretch',
                    justifyContent: 'center',
                    marginBottom: 34,
                    marginTop: 10,
                  }}>
                  <Image source={greenCheck} style={{width: 20, height: 20, marginRight: 6}} />
                  <TextReg
                    style={{
                      color: '#00FFBD',
                      fontSize: 20,
                      letterSpacing: 2,
                    }}>{`SELECTED`}</TextReg>
                </View>
              )}
              {this.state.selected != 'liquidation' && (
                <Button
                  onPress={() => {
                    this.setState({selected: 'liquidation'})
                  }}
                  style={{
                    alignSelf: 'stretch',
                    marginBottom: 20,
                    marginLeft: 20,
                    marginRight: 20,
                    borderRadius: 8,
                    backgroundColor: '#28283D',
                    borderWidth: 1,
                    borderColor: '#00FFBD',
                  }}>
                  <TextBold
                    style={{
                      fontSize: 18,
                      color: '#000',
                      letterSpacing: 2,
                      color: '#00FFBD',
                    }}>
                    {`SELECT OPTION`}
                  </TextBold>
                </Button>
              )}
            </View>
          </View>
          {/* end */}
          <TextReg
            style={{
              fontSize: 16,
              color: '#fff',
              marginTop: 20,
              marginLeft: 16,
              marginRight: 16,
            }}>{`*The values set forth above are for indicative purposes only to illustrate the differences between these two options. Values applicable to your loan will be shown on your loan dashboard if your loan has a 90.91% LTV.`}</TextReg>
          <Button
            onPress={() => {
              this.submit()
            }}
            isLoading={this.state.loading}
            disabled={!this.state.selected}
            style={{
              marginTop: 20,
              alignSelf: 'stretch',
              borderRadius: 8,
              marginLeft: 16,
              marginRight: 16,
              marginBottom: 50,
            }}>
            <TextBold
              style={{
                fontSize: 18,
                color: '#000',
                letterSpacing: 2,
              }}>
              CONFIRM
            </TextBold>
          </Button>
        </ScrollView>
      </View>
    )
  }
}

let local = {
  longBox: {
    flexDirection: 'column',
    alignSelf: 'stretch',
    borderRadius: 14,
    marginLeft: 16,
    marginRight: 16,
    marginTop: 30,
    borderWidth: 1,

    alignItems: 'center',
    overflow: 'hidden',
  },
  bubbleText: {
    height: 30,
    width: 130,
    backgroundColor: '#FFF',
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bubbleTextLong: {
    height: 30,
    width: 220,
    backgroundColor: '#FFF',
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bubbleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'stretch',
    margin: 20,
  },
}

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  user: state.user.user,
  WebService: state.auth.WebService,
  showPinScreen: state.auth.pinScreen,
  prices: state.user.prices,
  prices24h: state.user.prices24h,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(MarginCallSettings)
