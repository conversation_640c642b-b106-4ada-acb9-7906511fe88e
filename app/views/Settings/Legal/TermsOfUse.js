import React, {Component} from 'react'
import {View, Text, ScrollView, Dimensions} from 'react-native'
import {connect} from 'react-redux'
import {WebView} from 'react-native-webview'
import {BackgroundHeader} from '../../../components'
import commonStyles from '../../../styles/commonStyles'
import config from '../../../config.json'

const {width: screenWidth} = Dimensions.get('window')

import styles from '../styles'

class TermsOfUse extends Component {
  componentDidMount() {
    this.getDynamic()
  }

  getDynamic = () => {
    let countryCode = this.props.user?.address?.countryCode || 'US'
    let regionCode = this.props.user?.address?.province || 'CO'
    this.props.WebService.getSignedAgreement(
      'membership-agreement',
      countryCode,
      regionCode,
      config.pactSafe.accessId,
    )
      .then(res => {
        console.log('getSignedAgreement res', res)
        this.setState({contract: res.data, loaded: true})
      })
      .catch(err => {
        console.log('getSignedAgreement err', err)
      })
  }

  render() {
    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader
          title={'Terms of Use'}
          goBack={this.props.navigation.goBack}
        />
        <WebView
          source={{uri: 'https://saltlending.com/terms-of-use#section-6-460'}}
          style={{height: 360, width: screenWidth}}
          scalesPageToFit
          useWebKit={false}
        />
      </View>
    )
  }
}

/*
TermsOfUse.navigationOptions = ({ navigation }) => ({
  title: 'Terms of Use',
  headerTintColor: '#FFF',
  headerStyle: {
    backgroundColor: '#05868e',
  },
})
*/

TermsOfUse.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  //headerTintColor: '#FFF',
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    //shadowColor: 'transparent',
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
})

export default connect(mapStateToProps)(TermsOfUse)
