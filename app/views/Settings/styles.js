import {StyleSheet, Dimensions, PixelRatio} from 'react-native'

const {width} = Dimensions.get('window')

const $offWhite = '#eef0f0'

const styles = StyleSheet.create({
  swiperContainer: {
    flex: 1,
    alignSelf: 'stretch',
    flexDirection: 'column',
  },
  header: {
    alignSelf: 'stretch',
    height: 10,
  },
  list: {
    flex: 1,
    alignSelf: 'stretch',
  },
  listGroupTitle: {
    paddingLeft: 8,
    paddingRight: 8,
    alignSelf: 'stretch',
    height: 50,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 0.5,
    borderColor: $offWhite,
  },
  listGroupTitleLast: {
    paddingLeft: 8,
    paddingRight: 8,
    alignSelf: 'stretch',
    height: 50,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderColor: $offWhite,
  },
  listItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'stretch',
    height: 54,
    borderBottomWidth: 0.5,
    borderColor: '#AFAFAF',
    backgroundColor: '#3D3D50',
    marginLeft: -20,
    paddingLeft: 30,
    marginRight: -20,
    paddingRight: 30,
  },
  listItemLast: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'stretch',
    height: 54,
    borderColor: '#AFAFAF',
    backgroundColor: '#3D3D50',
    marginLeft: -20,
    paddingLeft: 30,
    marginRight: -20,
    paddingRight: 30,
  },
  listItemText: {
    fontSize: 16,
    color: '#FFF',
  },
  listItemTextSub: {
    fontSize: 17,
    color: '#FFF',
    paddingLeft: 14,
  },
  listItemDate: {
    backgroundColor: '#eef0f0',
    height: 32,
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingBottom: 6,
    paddingLeft: 16,
  },
  listItemDateText: {
    fontSize: 12,
    color: '#e6e6e6',
  },
  accountListItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'stretch',
    height: 60,
    borderWidth: 0.5,
    borderColor: $offWhite,
    paddingLeft: 20,
    paddingRight: 25,
  },
  logoutBox: {
    justifyContent: 'flex-start',
    flexDirection: 'row',
    gap: 16,
    alignItems: 'center',
    alignSelf: 'stretch',
    height: 40,
  },
  logoutText: {
    color: '#00FFBD',
    fontSize: 18,
  },
  rightArrowIcon: {
    height: 18,
    width: 18,
    borderRadius: 0,
    marginLeft: 8,
  },
  downArrowIcon: {
    height: 12,
    width: 22,
    borderRadius: 0,
    marginLeft: 8,
    marginTop: 2,
  },

  /* Terms of Use */
  termsOfUseBox: {
    marginTop: 30,
    marginLeft: 20,
    marginRight: 20,
    marginBottom: 30,
  },
  termsOfUseBold: {
    fontWeight: 'bold',
    fontSize: 16,
    color: '#FFF',
  },
  termsOfUseTitle: {
    textAlign: 'left',
    fontSize: 19,
    color: '#FFF',
  },
  termsOfUseText: {
    color: '#FFF',
    marginBottom: 20,
  },
  termsOfUseTextNoMargin: {
    color: '#FFF',
  },

  /*Contact Support*/
  scrollViewContainer: {
    //backgroundColor: '#FFF',
    alignSelf: 'stretch',
  },
  description: {
    color: '#FFF',
    fontSize: 16,
    marginTop: 30,
    marginRight: 30,
    marginLeft: 30,
    marginBottom: 10,
  },
  inputDescription: {
    color: '#FFF',
    fontSize: 14,
    marginBottom: 8,
    alignSelf: 'flex-start',
    marginLeft: 30,
  },
  inputContainer: {
    alignSelf: 'stretch',
    paddingLeft: 30,
    paddingRight: 30,
    marginBottom: 20,
  },
  inputField: {
    height: 200,
    width: '100%',
    borderColor: '#cbcbcb',
    borderWidth: 1,
    borderRadius: 2,
    padding: 10,
    paddingTop: 8,
    textAlignVertical: 'top',
    fontSize: 16 / PixelRatio.getFontScale(),
    color: '#FFF',
  },
  submitButton: {
    marginBottom: 20,
  },
  callUsText: {
    color: '#FFF',
    fontSize: 16,
    marginRight: 30,
    marginLeft: 40,
    marginBottom: 10,
    alignSelf: 'flex-start',
  },
  phoneInput: {
    margin: 15,
    height: 40,
    borderWidth: 1,
    marginLeft: 40,
    marginRight: 40,
    marginTop: 2,
    borderRadius: 3,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    fontSize: 16 / PixelRatio.getFontScale(),
    color: '#FFF',
  },
  categoryInputBox: {
    marginLeft: 30,
    marginRight: 30,
    marginBottom: 0,
    marginTop: 10,
    alignSelf: 'stretch',
  },
  phoneErrorBox: {
    alignSelf: 'stretch',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  phoneErrorText: {
    color: '#E5705A',
    fontSize: 16,
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 24,
  },
  listItemPhone: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    margin: 10,
  },

  /* ID VERIFICATION */
  listItemID: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    margin: 10,
  },

  /* LOADING MODAL */
  helpModalBox: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(74, 74, 74, 0.5)',
  },
  helpModalSquare: {
    width: '86%',
    backgroundColor: '#28283D', //'#FFF',
    borderRadius: 8,
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: 30,
  },
  helpModalX: {
    position: 'absolute',
    top: 4,
    right: 4,
  },
  helpModalTitleBox: {
    marginTop: 34,
    marginBottom: 10,
  },
  helpModalTitle: {
    fontSize: 24,
    color: '#e6e6e6',
  },
  helpModalButton: {
    backgroundColor: '#05868e',
    borderRadius: 6,
    width: 160,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 30,
    marginBottom: 30,
  },
  helpModalButtonText: {
    fontSize: 22,
    color: '#FFF',
  },
  helpModalDescriptionBox: {
    marginLeft: 30,
    marginRight: 30,
  },
  errorModalDescriptionBox: {
    marginLeft: 30,
    marginRight: 30,
    marginBottom: 50,
  },
  helpModalDescription: {
    color: '#e6e6e6',
    textAlign: 'center',
    fontSize: 16,
  },
  closeModalImg: {
    height: 30,
    width: 30,
    margin: 4,
  },
  loadingDots: {
    height: 80,
    width: 80,
    marginTop: -10,
    opacity: 0.6,
  },
  checkmarkCircleImg: {
    height: 60,
    width: 60,
    marginBottom: 20,
  },
  notificationSettingsHeader: {
    marginTop: 24,
    marginBottom: 20,
    marginLeft: 20,
    marginRight: 20,
    color: '#e6e6e6',
    textAlign: 'center',
  },

  /* success modal */

  successModalTitleBox: {
    paddingTop: 40,
    paddingBottom: 40,
    flexDirection: 'column',
    alignItems: 'center',
  },

  /* VerifyPhoneNumber */
  verifyPhoneContainer: {
    flex: 1,
    backgroundColor: '#28283D',
  },
  verifyPhoneNumberContainerAvoidKeyboard: {
    flex: 1,
    backgroundColor: '#28283D',
  },
  verifyPhoneNumberContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#28283D',
  },
  verifyPhoneNumberGroup: {
    flexDirection: 'column',
    alignItems: 'center',
    backgroundColor: '#28283D',
    marginTop: 40,
  },
  verifyPhoneNumberTitle: {
    color: '#e6e6e6',
    fontSize: 20,
    marginBottom: 20,
  },
  verifyPhoneNumberDescription: {
    marginLeft: 30,
    marginRight: 30,
    marginBottom: 20,
    color: '#e6e6e6',
    fontSize: 16,
    textAlign: 'center',
  },
  verifyPhoneNumberCountrySelect: {
    borderRadius: 14,
    borderColor: '#cbcbcb',
    borderWidth: 1,
    width: 300,
    height: 46,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
    paddingRight: 20,
    paddingLeft: 12,
  },
  verifyPhoneNumberCountrySide: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  verifyPhoneNumberCountryText: {
    color: '#e6e6e6',
    fontSize: 16,
    marginLeft: 10,
    width: 220,
  },
  verifyPhoneNumberCountryCodeBox: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: '#cbcbcb',
    borderRightWidth: 1,
  },
  verifyPhoneNumberCountryCodeText: {
    color: '#e6e6e6',
    fontSize: 16,
    marginLeft: 10,
    marginRight: 10,
  },
  verifyPhoneNumberPhoneBox: {
    flexDirection: 'row',
    borderRadius: 14,
    borderColor: '#cbcbcb',
    borderWidth: 1,
    width: 300,
    height: 46,
    marginBottom: 20,
  },
  verifyPhoneNumberInput: {
    marginLeft: 10,
    color: '#e6e6e6',
    fontSize: 16 / PixelRatio.getFontScale(),
    flexGrow: 1,
  },
  verifyPhoneNumberNotNow: {
    color: '#e6e6e6',
    fontSize: 16,
    marginTop: 16,
  },

  //Pick country code
  pickCountryCodeInputBox: {
    marginTop: 8,
    marginBottom: 8,
    marginLeft: 14,
    marginRight: 12,
    height: 40,
    alignSelf: 'stretch',
    padding: 8,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    borderColor: '#cbcbcb',
    borderWidth: 1,
  },
  pickCountryCodeInput: {
    textAlign: 'left',
    marginLeft: 10,
    color: '#e6e6e6',
    fontSize: 18,
    flexGrow: 1,
  },
  pickCountryCodeListItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'stretch',
    height: 50,
    borderWidth: 0.5,
    borderColor: $offWhite,
    paddingLeft: 20,
    paddingRight: 20,
  },
  countryFlagImg: {
    height: 24,
    width: 24,
  },

  /*bank */
  bankInput: {
    height: 50,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 20,
  },
  bankInputTitle: {
    color: '#e6e6e6',
    fontSize: 15,
    marginBottom: 6,
  },

  /* Change Password */
  changePasswordBox: {
    margin: 15,
    marginLeft: 40,
    marginRight: 40,
    marginTop: 20,
    flexDirection: 'column',
    alignSelf: 'stretch',
  },
  changePasswordInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 20,
  },
  changePasswordInputTitle: {
    color: '#e6e6e6',
    fontSize: 15,
    marginBottom: 6,
  },
  TwoFactorTitle: {
    color: '#e6e6e6',
    fontSize: 16,
    marginBottom: 14,
  },
  TwoFactorText: {
    color: '#e6e6e6',
    fontSize: 15,
    marginBottom: 12,
  },
  changePasswordSubmitButton: {
    marginVertical: 10,
  },
  changePasswordErrorBox: {
    alignSelf: 'stretch',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  changePasswordErrorText: {
    color: '#E5705A',
    fontSize: 18,
    textAlign: 'center',
    marginTop: 20,
  },
  changePasswordSuccessText: {
    color: '#85c884',
    fontSize: 18,
    textAlign: 'center',
  },

  //Delete Number Modal
  deleteModalTitle: {
    color: '#e6e6e6',
    textAlign: 'center',
    fontSize: 20,
  },
  deleteModalDescription: {
    color: '#e6e6e6',
    textAlign: 'center',
    fontSize: 15,
    marginTop: 20,
    marginBottom: 30,
  },
  updateSettingsModalDescription: {
    color: '#e6e6e6',
    textAlign: 'center',
    fontSize: 15,
    marginTop: 10,
    marginBottom: 4,
  },
  updateSettingsModalDescriptionBox: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    marginLeft: 30,
    marginRight: 30,
  },
  deleteModalDescriptionBox: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    marginLeft: 30,
    marginRight: 30,
    marginBottom: 30,
  },
  removePhoneText: {
    color: '#e6e6e6',
    fontSize: 16,
  },

  //Phone Pin
  verifyPhonePinBox: {
    flexDirection: 'row',
    borderRadius: 14,
    borderColor: '#cbcbcb',
    borderWidth: 1,
    width: 160,
    height: 46,
    marginBottom: 20,
  },
  verifyPhonePinInput: {
    color: '#e6e6e6',
    fontSize: 16,
    flexGrow: 1,
    textAlign: 'center',
  },
  pinErrorText: {
    color: '#E5705A',
    fontSize: 18,
    marginBottom: 16,
  },
  accountsSwiper: {
    alignSelf: 'stretch',
    flex: 1,
  },
  accountNameText: {
    color: '#e6e6e6',
    fontSize: 18,
  },
  addAccountBox: {
    flex: 1,
    alignSelf: 'stretch',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  addAccountHeader: {
    alignSelf: 'stretch',
    height: 38,
    backgroundColor: '#05868e',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  addAccountHeaderClose: {
    alignSelf: 'stretch',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingLeft: 10,
    width: 50,
  },
  addAccountTypeBox: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 14,
    borderWidth: 2,
    borderColor: '#05868e',
    width: 264,
    height: 50,
  },
  addAccountBusinessType: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    width: 130,
  },
  addAccountNameBox: {
    width: 264,
    marginBottom: 10,
    marginTop: 20,
  },
  addAccountNameText: {
    fontSize: 19,
    color: '#e6e6e6',
    marginBottom: 12,
  },
  addAcountTextInputBox: {
    borderRadius: 14,
    height: 50,
    borderWidth: 0.5,
    borderColor: '#cbcbcb',
    width: 264,
    marginBottom: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addAccountCreateButton: {
    width: 200,
    height: 50,
    backgroundColor: '#05868e',
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  accountMenuUnselected: {
    height: 26,
    width: 26,
    borderRadius: 13,
    backgroundColor: '#eef0f0',
  },
  accountMenuAddAccountBox: {
    alignSelf: 'stretch',
    height: 50,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 0.8,
    marginTop: 10,
  },
  accountMenuPlusImg: {
    height: 14,
    width: 14,
    marginRight: 10,
    opacity: 0.7,
  },
  GradiantHeaderCenterBox: {
    alignSelf: 'stretch',
    alignItems: 'center',
    marginTop: 10,
  },

  // change email
  changeEmailBox: {
    flexDirection: 'column',
    alignSelf: 'stretch',
    alignItems: 'center',
    paddingTop: 10,
  },
  changeEmailTitle: {
    color: '#e6e6e6',
    fontSize: 15,
    marginTop: 16,
    marginBottom: 6,
    alignSelf: 'stretch',
    textAlign: 'center',
  },
  changeEmailDescription: {
    color: '#e6e6e6',
    fontSize: 15,
    marginBottom: 8,
    marginLeft: 30,
    marginRight: 30,
    marginTop: 28,
  },
  changeEmailDescriptionPin: {
    color: '#e6e6e6',
    fontSize: 15,
    marginBottom: 8,
    marginLeft: 30,
    marginRight: 30,
    marginTop: 18,
    lineHeight: 20,
  },
  changeEmailInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 26,
    marginLeft: 30,
    marginRight: 30,
    marginTop: 6,
    backgroundColor: '#28283D',
  },
  changeEmailInputTitle: {
    color: '#fff',
    fontSize: 18,
    marginBottom: 6,
    alignSelf: 'flex-start',
    marginLeft: 30,
    marginTop: 20,
  },
  signUpTextInputEmailVerificationCode: {
    opacity: 0,
  },
  signUpVerifcationBlocksBox: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 4,
    marginBottom: 30,
  },
  signUpVerifcationBlocksBox2FA: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
  },
  signUpVerifcationBlock: {
    borderRadius: 6,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 4,
    height: 46,
    width: 46,
  },
  signUpVerifcationBlockActive: {
    borderWidth: 2,
    borderColor: '#00ffc3',
  },
  signUpVerifcationBlockText: {
    fontSize: 20,
    color: '#000',
  },
  personalInfoInputTitle: {
    color: '#e6e6e6',
    fontSize: 15,
    marginBottom: 6,
  },
  personalInfoInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#cbcbcb',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 14,
  },
  personalQuestionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignSelf: 'stretch',
    marginTop: 6,
  },
  personalQuestionButton: {
    height: 40,
    width: 140,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#555',
    borderRadius: 14,
  },
  personalMultipleQuestionButton: {
    height: 50,
    alignSelf: 'stretch',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#999',
    borderRadius: 14,
    marginBottom: 14,
  },
  banksEditUploadButton: {
    backgroundColor: '#00FFBD',
    marginTop: 16,
    marginBottom: 20,
    alignSelf: 'stretch',
  },
  banksEditLoadingDots: {
    height: 40,
    width: 64,
    opacity: 0.6,
    alignSelf: 'center',
    zIndex: 5,
    marginBottom: 12,
  },
  banksUploadDocumentBox: {
    backgroundColor: '#3D3D50',
    borderWidth: 1,
    borderColor: '#FFF',
    borderRadius: 14,
    height: 50,
    width: '90%',
    marginBottom: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  banksUploadErrorBox: {
    alignSelf: 'stretch',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 10,
  },
  banksUploadErrorText: {
    color: '#de4a2e',
    fontSize: 17,
    textAlign: 'center',
    width: 300,
  },
  //errors
  showErrorBox: {
    marginTop: 4,
    marginBottom: 10,
    flexDirection: 'column',
    alignSelf: 'stretch',
    alignItems: 'center',
  },
  showErrorText: {
    fontSize: 16,
    color: '#e5705a',
  },

  //stackWise
  stackwiseRewardTokenButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 48,
    width: 100,
    borderRadius: 14,
    margin: 4,
  },
})

export default styles
