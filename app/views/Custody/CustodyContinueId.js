import React from 'react';
import {View, Image, ScrollView, FlatList, TouchableOpacity} from 'react-native';

import {TextBold, Card, TextReg, Button} from '../../components';

import styles from './styles';

const CustodyContinueId = props => {
  console.log('props', props);
  return (
    <Card cardMarginBottom={2} marginTop={10}>
      <View style={{alignSelf: 'stretch', paddingLeft: 10, paddingRight: 10}}>
        <TextReg
          style={{
            fontSize: 24,
            marginTop: 16,
            marginBottom: 10,
            color: '#fff',
          }}>
          Almost there!
        </TextReg>
        <TextReg style={{fontSize: 18, marginBottom: 18, color: '#fff'}}>
          You just have a couple more steps to complete in order to complete verification. Let’s keep going!
        </TextReg>
        <Button style={{alignSelf: 'stretch', marginBottom: 10}} onPress={() => props.navigation.navigate('Unit21Flow')}>
          CONTINUE
        </Button>
      </View>
    </Card>
  );
};

export default CustodyContinueId;
