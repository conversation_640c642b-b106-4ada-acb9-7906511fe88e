import React, {Component} from 'react'
import {
  View,
  StatusBar,
  ScrollView,
  RefreshControl,
  Image,
  TouchableOpacity,
  Platform,
} from 'react-native'
import {connect} from 'react-redux'
import {withNavigation} from 'react-navigation'

import {showNotifications} from '../../store/notifications/notifications.actions'
import {BackgroundHeader} from '../../components'
import CustodyContinueId from './CustodyContinueId'
import WalletsBox from '../Loans/WalletsBox'

import styles from './styles'
import commonStyles from '../../styles/commonStyles'

class CustodyPage extends Component {
  constructor(props) {
    super(props)
    this.state = {
      abc: 123,
      refreshing: false,
    }
  }

  componentDidMount() {
    console.log('componentDidMount')
    this.props.navigation.setParams({
      toggleNotifications: this.toggleNotifications,
    })
  }

  componentDidUpdate(prevProps) {
    console.log('componentDidUpdate', prevProps)
  }

  toggleNotifications = () => {
    this.props.dispatch(showNotifications(true))
  }

  refetchCustody = () => {
    console.log('refetchCustody')
  }

  goToWallets = ref => {
    this.props.navigation.navigate('Collateral', {ref})
  }

  render() {
    console.log('custody', this.props)
    const showPinIntro = this.props.storedPin === '0' || this.props.backToSettings
    let productRef = this.props?.route?.params?.ref

    return (
      <View key={'custodyScreen'} style={commonStyles.tileContainer}>
        {!showPinIntro && (
          <BackgroundHeader
            title={'WALLET'}
            toggleNotifications={this.toggleNotifications}
            goBack={() => this.props.navigation.goBack()}
            navigateSettings={() => this.props.navigation.navigate('Settings')}
          />
        )}
        <StatusBar barStyle="light-content" />
        <ScrollView
          style={styles.swiperContainer}
          contentContainerStyle={{alignItems: 'center', paddingTop: 0}}
          refreshControl={
            <RefreshControl
              refreshing={this.state.refreshing}
              onRefresh={this.refetchCustody}
              colors={['#28283D']}
              tintColor={'#fff'}
            />
          }>
          <WalletsBox
            user={this.props.user}
            productRef={productRef}
            goToWallets={this.goToWallets}
            tokenPrices24h={this.props.tokenPrices24h}
          />
          <CustodyContinueId navigation={this.props.navigation} />
        </ScrollView>
      </View>
    )
  }
}

CustodyPage.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
  primaryEmail: state.auth.account.email,
  refreshCount: state.user.refreshCount,
  accountCreated: state.user.accountCreated,
  pauseUnit21: state.user.pauseUnit21,
  showPinScreen: state.auth.pinScreen,
  backToSettings: state.user.backToSettings,
  storedPin: state.auth.pin,
  user: state.user.user,
  tokenPrices: state.user.prices,
  tokenPrices24h: state.user.prices24h,
  launchDarkly: state.launchDarkly,
  referrals: state.user.referrals,
})

export default connect(mapStateToProps)(CustodyPage)
