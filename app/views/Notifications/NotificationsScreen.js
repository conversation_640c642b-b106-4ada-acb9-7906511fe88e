import React, { Component } from 'react'
import { View, FlatList, TouchableOpacity, ScrollView, RefreshControl, Image } from 'react-native'
import { connect } from 'react-redux'
import { formatToAmPmTime, notificationFormatDate, updateActiveTabListener } from '../../util/helpers'
import { TextReg } from '../../components'
import { NavigationActions } from 'react-navigation'
import NotificationOptions from './NotificationOptions'
import { screenView } from '../../store/analytics/analytics.actions'
import { updateUnread } from '../../store/notifications/notifications.actions'
import commonStyles from '../../styles/commonStyles'
import styles from './styles'

class NotificationsScreen extends Component {
  constructor(props) {
    super(props)
    this.state = {
      notificationArr: null,
      error: false,
      refreshing: true,
      page: 1,
      totalNotifs: 0,
      showOptions: false
    }
  }

  componentDidMount() {
    updateActiveTabListener(this.props.navigation)
    this.props.dispatch(screenView(`Notifications Tab`))
    this.getNotifications()
    this.props.navigation.setParams({ toggleOptions: this.toggleOptions })
  }

  componentDidUpdate(prevProps, prevState) {
    if (this.props.unread > prevProps.unread) {
      this.getNotifications()
    }
  }

  componentWillUnmount() {
    this.setState({ showOptions: false })
  }

  getNotifications = () => {
    this.setState({ refreshing: true })
    return this.props.WebService.getNotifications(this.state.page)
      .then((res) => {
        this.setState({ totalNotifs: res.data.total })
        const notificationArr = res.data.notifications.map((a, k) => {
          const date = notificationFormatDate(a.createdAt)
          const time = formatToAmPmTime(a.createdAt)

          return {
            date,
            time,
            relationType: a.relationType,
            text: a.message,
            key: k.toString() + 100 * this.state.page,
            viewedAt: a.viewedAt,
            id: a.id
          }
        })
        const updateNotifArray = notificationArr || []
        this.setState({ notificationArr: updateNotifArray, refreshing: false })
      })
      .catch((err) => {
        if (err.response && err.response.status === 401) {
          throw err
        }
        this.setState({ error: true, refreshing: false })
      })
  }

  padStartTwo = (num) => {
    if (num.length === 1) {
      return `0${num}`
    }
  }

  clickedNotif = (notif) => {
    if (!notif.viewedAt) {
      this.props.WebService.readNotification(notif.id)
      this.props.dispatch(updateUnread(this.props.unread - 1))
      const notificationArr = this.state.notificationArr.map((a) => {
        if (a.id === notif.id) {
          a.viewedAt = 'read'
        }
        return a
      })
      this.setState({ notificationArr })
    }

    if (notif.relationType === 'loan') {
      this.props.navigation.navigate('Loans')
      return
    } else if (notif.relationType === 'wallet') {
      const arrOfTokens = ['BTC', 'ETH', 'SALT', 'LTC', 'DASH', 'DOGE', 'USDC', 'TUSD']

      let includesToken = ''
      arrOfTokens.map((a, k) => {
        if (notif.text.includes(a)) {
          includesToken = a
        }
      })

      if (includesToken !== '') {
        this.props.navigation.dispatch(
          NavigationActions.reset({
            index: 0,
            key: 'Collateral',
            actions: [
              NavigationActions.navigate({
                routeName: 'Collateral'
              })
            ]
          })
        )
        this.props.navigation.navigate('Detail', { title: includesToken })
      }
    }
  }

  toggleOptions = () => {
    this.setState({ showOptions: !this.state.showOptions })
  }

  readAllNotifs = () => {
    this.props.WebService.readAllNotifications()
      .then((res) => {
        const notificationArr = this.state.notificationArr.map((a) => {
          a.viewedAt = 'read'
          return a
        })
        this.setState({ notificationArr })
        this.props.dispatch(updateUnread(0))
        this.setState({ showOptions: false })
        this.getNotifications()
      })
      .catch(() => {
        this.setState({ showOptions: false })
      })
  }

  loadMore = () => {
    this.setState({ page: this.state.page + 1 }, () => this.getNotifications(this.state.page))
  }

  render() {
    let lastDate = ''
    return (
      <View style={commonStyles.container}>
        <NotificationOptions
          showOptions={this.state.showOptions}
          toggleOptions={this.toggleOptions}
          readAllNotifs={this.readAllNotifs}
          showPinScreen={this.props.showPinScreen}
        />
        <ScrollView
          style={styles.swiperContainer}
          refreshControl={
            <RefreshControl
              refreshing={this.state.refreshing}
              onRefresh={this.getNotifications}
              colors={['#28283D']}
              tintColor={'#fff'}
            />
          }
        >
          {this.state.error && (
            <View style={styles.showErrorBox}>
              <TextReg style={styles.showErrorText}>Error</TextReg>
            </View>
          )}
          {this.state.notificationArr && this.state.notificationArr.length === 0 ? (
            <View style={styles.noNotifsBox}>
              <TextReg style={styles.noNotifsText}>No Notifications</TextReg>
            </View>
          ) : (
            <FlatList
              style={styles.list}
              data={this.state.notificationArr}
              renderItem={({ item }) => {
                let dateLine = null
                if (item.date !== lastDate) {
                  dateLine = (
                    <View style={styles.listItemDate}>
                      <TextReg style={styles.listItemDateText}>{item.date}</TextReg>
                    </View>
                  )
                  lastDate = item.date
                }

                let showLoadMore = false
                if (
                  item === this.state.notificationArr[this.state.notificationArr.length - 1] &&
                  this.state.totalNotifs > 100 * this.state.page
                ) {
                  showLoadMore = true
                }
                return (
                  <View>
                    {dateLine}
                    <TouchableOpacity onPress={() => this.clickedNotif(item)}>
                      <View style={styles.listItem}>
                        {!item.viewedAt ? <View style={styles.unreadNotifDot} /> : <View style={styles.readNotifDot} />}
                        <View style={styles.listItemInner}>
                          <TextReg style={styles.listItemText}>{item.text}</TextReg>
                          <TextReg style={styles.listItemTimeText}>{item.time}</TextReg>
                        </View>
                      </View>
                    </TouchableOpacity>
                    {showLoadMore && (
                      <TouchableOpacity onPress={() => this.loadMore()}>
                        <View style={styles.listItemLoadMore}>
                          <TextReg style={styles.listItemTextLoadMore}>Load More</TextReg>
                        </View>
                      </TouchableOpacity>
                    )}
                  </View>
                )
              }}
            />
          )}
        </ScrollView>
      </View>
    )
  }
}

NotificationsScreen.navigationOptions = (props) => ({
  title: 'Notifications',
  headerTintColor: '#FFF',
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Bold',
    fontWeight: '200'
  },
  headerStyle: {
    backgroundColor: '#05868e'
  },
  headerBackTitle: null,
  headerRight: (
    <TouchableOpacity
      onPress={() => props.route.params.toggleOptions()}
      style={{ height: 30, justifyContent: 'center' }}
    >
      <Image source={require('../../imgs/moreDots.png')} style={styles.moreDots} />
    </TouchableOpacity>
  ),
  headerLeft: <View style={{ width: 28, marginLeft: 16 }} />
})

const mapStateToProps = (state) => ({
  WebService: state.auth.WebService,
  accountRef: state.auth.account.ref,
  unread: state.notifications.unread,
  showPinScreen: state.auth.pinScreen
})

export default connect(mapStateToProps)(NotificationsScreen)
