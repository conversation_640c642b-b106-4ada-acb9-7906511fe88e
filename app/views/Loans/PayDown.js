import React, {useState} from 'react'
import {View, TouchableOpacity, Image} from 'react-native'
import {useNavigation} from '@react-navigation/native'

import {TextBold, TextReg, Card} from '../../components'
import {numberWithCommas} from '../../util/helpers'

import styles from './styles'

const PayDown = ({isStabilized, minPrincipal, suggestPrincipal}) => {
  let navigation = useNavigation()

  let [open, setOpen] = useState(false)

  let withCommas = numberWithCommas(suggestPrincipal)
  let withCommas2 = numberWithCommas(minPrincipal)

  let goToPay = () => {
    navigation.navigate('OneTimeOptions')
  }
  return (
    <Card cardMarginBottom={0} marginTop={0}>
      {!open && (
        <TouchableOpacity onPress={() => setOpen(true)} style={{alignSelf: 'stretch'}} activeOpacity={1}>
          <View style={{flexDirection: 'row', alignSelf: 'stretch', alignItems: 'center', justifyContent: 'space-between'}}>
            <View style={{alignItems: 'center', flexDirection: 'row'}}>
              <Image source={require('../../imgs/graphics/give.png')} style={{height: 32, width: 32}} />
              <TextBold style={{fontSize: 18, marginLeft: 10}}>{`Pay Down Principal`}</TextBold>
              <View
                style={{
                  width: 90,
                  backgroundColor: '#00FFBD',
                  borderRadius: 10,
                  height: 30,
                  alignItems: 'center',
                  marginLeft: 6,
                  justifyContent: 'center',
                }}>
                <TextReg style={{color: '#000', fontSize: 12}}>{`Recommended`}</TextReg>
              </View>
            </View>
            <Image source={require('../../imgs/nav/arrowRightW.png')} style={{height: 20, width: 24}} />
          </View>
          <TextReg
            style={{
              padding: 10,
              paddingBottom: 6,
            }}>{`Best way to fix your loan's health is to make a payment to your principal loan balance.`}</TextReg>
        </TouchableOpacity>
      )}
      {open && (
        <View style={{alignSelf: 'stretch'}}>
          <View style={{flexDirection: 'row', alignSelf: 'stretch', alignItems: 'center', justifyContent: 'space-between'}}>
            <View style={{alignItems: 'center', flexDirection: 'row'}}>
              <Image source={require('../../imgs/graphics/give.png')} style={{height: 32, width: 32}} />
              <TextBold style={{fontSize: 18, marginLeft: 10}}>{`Pay Down Principal`}</TextBold>
              <View
                style={{
                  width: 90,
                  backgroundColor: '#00FFBD',
                  borderRadius: 10,
                  height: 30,
                  alignItems: 'center',
                  marginLeft: 6,
                  justifyContent: 'center',
                }}>
                <TextReg style={{color: '#000', fontSize: 12}}>{`Recommended`}</TextReg>
              </View>
            </View>
            <TouchableOpacity onPress={() => setOpen(false)} activeOpacity={1}>
              <Image source={require('../../imgs/nav/arrowDownW.png')} style={{height: 20, width: 24}} />
            </TouchableOpacity>
          </View>
          <TextReg
            style={{
              padding: 10,
              paddingBottom: 6,
            }}>{`Best way to fix your loan's health is to make a payment to your principal loan balance.`}</TextReg>
          <View style={{flexDirection: 'row'}}>
            <TouchableOpacity
              onPress={() => goToPay()}
              style={{
                alignSelf: 'stretch',
                borderWidth: 2,
                borderColor: '#00FFBD',
                marginTop: 10,
                borderRadius: 4,
                alignItems: 'center',
                flex: 1,
              }}>
              <TextBold style={{color: '#00FFBD', fontSize: 22, marginTop: 30}}>{`$${withCommas}`}</TextBold>
              <TextReg style={{marginTop: 20, width: isStabilized ? 120 : 200, textAlign: 'center'}}>{`Suggested Amount to Cure`}</TextReg>
              <TextReg style={{marginTop: 6, marginBottom: 30}}>{`LTV < 70%`}</TextReg>
            </TouchableOpacity>
            {isStabilized && (
              <TouchableOpacity
                onPress={() => goToPay()}
                style={{
                  alignSelf: 'stretch',
                  borderWidth: 2,
                  borderColor: '#28283D',
                  marginTop: 10,
                  borderRadius: 4,
                  alignItems: 'center',
                  marginLeft: 6,
                  flex: 1,
                }}>
                <TextBold style={{color: '#E5705A', fontSize: 22, marginTop: 30}}>{`$${withCommas2}`}</TextBold>
                <TextReg
                  style={{marginTop: 20, width: isStabilized ? 120 : 200, textAlign: 'center'}}>{`Minimum Amount to Convert`}</TextReg>
                <TextReg style={{marginTop: 6, marginBottom: 30}}>{`LTV < 83.3%`}</TextReg>
              </TouchableOpacity>
            )}
          </View>
        </View>
      )}
    </Card>
  )
}
export default PayDown
