import React from 'react'
import {View, Image, TouchableOpacity} from 'react-native'

import {TextBold, Card, TextReg} from '../../components'
import {formatCurrency} from '../../util/helpers'

const MarginCallSettingsBox = props => {
  let {goToSettings, stabilizationOn, triggerAmount, postStabilizationValue} = props

  return (
    <Card cardMarginBottom={12} marginTop={0}>
      <TouchableOpacity style={{alignSelf: 'stretch', padding: 10}} onPress={() => goToSettings()}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          <View
            style={{
              flexDirection: 'column',
              alignSelf: 'stretch',
              gap: 4,
            }}>
            <TextBold
              style={{
                fontSize: 20,
                opacity: 0.6,
              }}>
              Margin Call Settings
            </TextBold>
            <View style={{flexDirection: 'row', gap: 4, marginTop: 8}}>
              <TextBold>
                {stabilizationOn ? 'Stabilization' : 'Liquidation'} Trigger Amount:
              </TextBold>
              <TextBold>${formatCurrency(triggerAmount)}</TextBold>
            </View>
            <View style={{flexDirection: 'row', gap: 4}}>
              <TextBold>
                Collateral Post {stabilizationOn ? 'Stabilization:' : 'Liquidation:'}
              </TextBold>
              <TextBold>${formatCurrency(postStabilizationValue)}</TextBold>
            </View>
          </View>
          <Image
            source={require('../../imgs/rightArrow.png')}
            style={{height: 24, width: 24, opacity: 0.8, marginRight: -6}}
          />
        </View>
      </TouchableOpacity>
    </Card>
  )
}

export default MarginCallSettingsBox
