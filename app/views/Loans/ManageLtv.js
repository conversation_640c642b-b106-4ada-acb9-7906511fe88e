import React, {useEffect, useState, useRef} from 'react'
import {View, TouchableOpacity, Image, ScrollView} from 'react-native'

import {useSelector} from 'react-redux'
import {TextReg, TextBold, BackgroundHeader, Card} from '../../components'

import closeX from '../../imgs/closeX.png'
import PayDown from './PayDown'
import DepositDrop from './DepositDrop'

import styles from './styles'

let ManageLtv = ({navigation}) => {
  let loanData = useSelector(state => state.user.loanData || {})
  let accountRef = useSelector(state => state.auth.account.ref)
  let user = useSelector(state => state.user.user)

  const {loanValue, collateralValue, thresholds, ltv, payments} = loanData
  const marginCure = thresholds?.marginCure
  const marginCall = thresholds?.marginCall

  let showPriceCure = ((loanValue - Number(collateralValue) * marginCure) / marginCure).toFixed(2)
  if (showPriceCure < 0) {
    showPriceCure = '0.00'
  }

  let showPriceCall = ((loanValue - Number(collateralValue) * marginCall) / marginCall).toFixed(2)
  if (showPriceCall < 0) {
    showPriceCall = '0.00'
  }

  let principalNeeded = (ltvGoal, collatUSD, loanAmount, payments) => {
    console.log('principalNeeded', ltvGoal, collatUSD, loanAmount, payments)
    if (ltvGoal > ltv) {
      return 0
    } else {
      const principalGoal = ltvGoal * collatUSD
      const currentPrincipal = payments
        .map(p => p.principal)
        .reduce((prev, curr) => new Number(prev) - curr, loanAmount)
      console.log('else', currentPrincipal, principalGoal)
      return currentPrincipal - principalGoal
    }
  }

  let minPrincipal = principalNeeded(
    thresholds?.marginCall || 0.83,
    collateralValue,
    loanValue,
    payments,
  )
  let suggestPrincipal = principalNeeded(
    thresholds?.marginCure || 0.7,
    collateralValue,
    loanValue,
    payments,
  )

  minPrincipal = minPrincipal.toFixed(2)
  suggestPrincipal = suggestPrincipal.toFixed(2)
  console.log('minPrincipal', minPrincipal)
  console.log('suggestPrincipal', suggestPrincipal)

  console.log('marginCure', marginCure)
  console.log('marginCall', marginCall)
  console.log('showPriceCure', showPriceCure)
  console.log('showPriceCall', showPriceCall)
  console.log('ltv', ltv)
  console.log('thresholds', thresholds)

  let showLtv = (ltv * 100).toFixed(2)
  let isStabilized = user?.accounts?.filter(a => a.ref == accountRef)[0]?.product?.loan
    ?.isStabilized

  let borderColor = '#AFAFAF'
  let var1 = 'Stabilization'
  if (ltv < thresholds?.marginCall) {
    borderColor = '#F7D956'
    var1 = 'Warning'
  }
  if (ltv >= thresholds?.marginCall) {
    borderColor = '#E5705A'
    var1 = 'Margin Call'
  }
  if (isStabilized) {
    borderColor = '#AFAFAF'
    var1 = 'Stabilization'
  }

  return (
    <View style={localStyles.box}>
      <BackgroundHeader
        title={'Manage LTV'}
        goBack={() => navigation.goBack()}
        navigateSettings={() => navigation.navigate('Settings')}
      />
      <ScrollView style={{alignSelf: 'stretch'}} contentContainerStyle={{alignItems: 'center'}}>
        <Card
          cardMarginBottom={0}
          marginTop={8}
          style={{alignSelf: 'stretch', borderRadius: 14, borderColor, borderWidth: 1}}>
          <View style={{alignSelf: 'stretch'}}>
            <TextBold style={{fontSize: 18, marginLeft: 4}}>{`Manage Loan to Value`}</TextBold>
            <TextReg
              style={{
                marginTop: 10,
                fontSize: 16,
                marginLeft: 4,
              }}>
              {`Your loan is in a `}
              <TextBold style={{color: borderColor}}>{`${var1}`}</TextBold>
              {` state, and your LTV is `}
              <TextBold style={{color: borderColor}}>{`${showLtv}%`}</TextBold>
              {`. We recommend making a`}
              <TextBold>{` stablecoin `}</TextBold>
              {`payment now to cure your LTV to a healthy status (< 70% LTV).`}
            </TextReg>
          </View>
        </Card>
        <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
          <PayDown
            isStabilized={isStabilized}
            minPrincipal={minPrincipal}
            suggestPrincipal={suggestPrincipal}
          />
          <DepositDrop
            showPriceCure={showPriceCure}
            showPriceCall={showPriceCall}
            isStabilized={isStabilized}
          />
        </View>
      </ScrollView>
    </View>
  )
}

export default ManageLtv

let localStyles = {
  box: {
    flex: 1,
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
}
