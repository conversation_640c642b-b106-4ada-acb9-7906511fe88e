import React, {useEffect, useState} from 'react'
import {Image, View} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import {useNavigation, useFocusEffect} from '@react-navigation/native'
import {Button, TextReg, TextBold, Card} from '../../components'

let EntityWarn = () => {
  const navigation = useNavigation()
  let WebService = useSelector(state => state.auth.WebService || {})
  let AccountRef = useSelector(state => state.auth.account.ref)
  let user = useSelector(state => state.user.user || {})

  let [a, setA] = useState({})

  useFocusEffect(
    React.useCallback(() => {
      getA()
    }, []),
  )

  let getA = async () => {
    let res = await WebService.getEntitys()
    let arr = res.data
    let acc = user?.accounts?.filter(b => b.ref == AccountRef)[0]
    let entityId = acc.entityProfile?.id
    let entity = arr?.filter(b => b.id == entityId)[0]
    setA(entity)
  }

  let go = () => {
    navigation.navigate('Entities')
  }

  let doc1 = false
  let doc2 = false
  //true if alteast 1 is not rejected
  if (a?.documents?.length > 0) {
    if (a?.documents.some(item => item.rejectedAt === null || item.rejectedAt === false)) {
      //
    } else {
      doc1 = true
    }
  }
  if (a?.address?.documents?.length > 0) {
    if (a?.address?.documents.some(item => item.rejectedAt === null || item.rejectedAt === false)) {
      //
    } else {
      doc2 = true
    }
  }

  if (!doc1 && !doc2) return null

  return (
    <Card marginTop={14} cardMarginBottom={1}>
      <View style={{flexDirection: 'row', alignItems: 'center', marginLeft: -30}}>
        <Image source={require('../../imgs/graphics/warn.png')} style={{width: 26, height: 24, marginRight: 4}} />
        <TextBold style={{color: '#FFF', fontSize: 22}}>{`Entity Document Rejected`}</TextBold>
      </View>
      <TextReg
        style={{
          width: 320,
          fontSize: 15,
          marginBottom: 16,
          marginTop: 14,
        }}>{`One of your documents was rejected, please upload another.`}</TextReg>
      <Button title={'getLoan'} onPress={() => go()} style={{marginBottom: 16, alignSelf: 'stretch', marginLeft: 10, marginRight: 10}}>
        <TextReg style={{fontSize: 18, letterSpacing: 0.7, color: '#000'}}>{`ENTITIES`}</TextReg>
      </Button>
    </Card>
  )
}

export default EntityWarn
