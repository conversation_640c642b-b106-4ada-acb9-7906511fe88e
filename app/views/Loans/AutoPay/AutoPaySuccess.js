import React, {Component} from 'react'
import {View, Image, TouchableOpacity, StatusBar, ScrollView} from 'react-native'
import {connect} from 'react-redux'

import {TextBold, Card, TextReg, Button, BackgroundHeader} from '../../../components'
import commonStyles from '../../../styles/commonStyles'
import {getTokenPic} from '../../../util/tokens'
import {increaseRefreshDataCount} from '../../../store/user/user.actions'

class AutoPayConfirm extends Component {
  constructor(props) {
    super(props)
    this.state = {
      banksArr: [],
      refreshing: false,
      chooseBank: false,
      showPayCollateral: false,
      agree: false,
      loadingConfirm: false,
    }
  }

  goToBanks = () => {
    this.props.navigation.navigate('Banking')
  }

  closeAndRefresh = () => {
    const source = this.props.route?.params?.source || null
    const type = this.props.route?.params?.type || null
    console.log('source - autopay success', source)
    console.log('type - autopay success', type)

    this.props.dispatch(increaseRefreshDataCount())

    if (source == 'change') {
      if (type == 'collateral') {
        //start from change, collat 4x go back
        this.props.navigation.goBack()
        this.props.navigation.goBack()
        this.props.navigation.goBack()
        this.props.navigation.goBack()
      }
      if (type == 'bank') {
        //start from change, bank 5x go back
        this.props.navigation.goBack()
        this.props.navigation.goBack()
        this.props.navigation.goBack()
        this.props.navigation.goBack()
        this.props.navigation.goBack()
      }
    } else {
      //no change
      if (type == 'collateral') {
        this.props.navigation.goBack()
        this.props.navigation.goBack()
        this.props.navigation.goBack()
      }
      if (type == 'bank') {
        this.props.navigation.goBack()
        this.props.navigation.goBack()
        this.props.navigation.goBack()
        this.props.navigation.goBack()
      }
    }
  }

  render() {
    let noBank =
      this.props.launchDarkly[`disable-ach`] != null && this.props.launchDarkly[`disable-ach`]

    console.log('autopay - launchDarkly', this.props.launchDarkly)

    let getLoansData = this.props.route?.params?.getLoansData
    const {automatedPaymentType, paymentBankAccount} = this.props.loanData
    const source = this.props.route?.params?.source || null
    const type = this.props.route?.params?.type || null
    console.log('source - autopay success', source)
    console.log('type - autopay success', type)

    const showBanks = this.props.banksArr.map((a, k) => (
      <TouchableOpacity key={k} onPress={() => this.connectBankToLoan(a.id)}>
        <Card cardWidth={280}>
          <TextReg style={{alignSelf: 'flex-start', paddingLeft: 8}}>
            {a.name} - ********{a.accountNumber}
          </TextReg>
        </Card>
      </TouchableOpacity>
    ))

    let currencyImg = getTokenPic('USDC')

    return (
      <View key={'loanScreen'} style={commonStyles.tileContainer}>
        <StatusBar barStyle="light-content" />
        <View style={{marginRight: 10, alignSelf: 'stretch'}}>
          <BackgroundHeader
            title={' '}
            toggleNotifications={this.toggleNotifications}
            close
            closeFn={() => this.closeAndRefresh()}
          />
        </View>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            alignSelf: 'stretch',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
          style={{
            alignSelf: 'stretch',
          }}>
          <View style={{alignItems: 'center', alignSelf: 'stretch'}}>
            <View
              style={{
                borderRadius: 100,
                backgroundColor: '#3D3D50',
                width: 200,
                height: 200,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <View style={{backgroundColor: '#28283D', borderRadius: 50}}>
                <Image
                  source={require('../../../imgs/graphics/nonLendCheck.png')}
                  style={{width: 100, height: 100}}
                />
              </View>
            </View>
            <TextBold
              style={{
                marginTop: 20,
                fontSize: 30,
                width: 200,
                textAlign: 'center',
              }}>{`Autopayment Activated Successfully!`}</TextBold>
          </View>
          <View style={{alignItems: 'center', alignSelf: 'stretch'}}>
            <Button
              style={{width: 330, marginBottom: 30, borderRadius: 4}}
              onPress={() => this.closeAndRefresh()}>
              <TextBold
                style={{
                  color: '#000',
                  fontSize: 16,
                  letterSpacing: 1.2,
                }}>{`CLOSE`}</TextBold>
            </Button>
          </View>
        </ScrollView>
      </View>
    )
  }
}

let local = {
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderBottomWidth: 0.5,
    borderColor: '#ccc',
    paddingBottom: 6,
    paddingTop: 6,
  },
  rowBottom: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 6,
    paddingTop: 6,
  },
}

/*
 <AutomaticPayments getLoansData={getLoansData} navigation={this.props.navigation} />
 {!noBank && <EditBanksBox goToBanks={this.goToBanks} banksArr={this.props.banksArr} />}
*/

AutoPayConfirm.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  WebService: state.auth.WebService,
  showPinScreen: state.auth.pinScreen,
  banksArr: state.user.banks,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(AutoPayConfirm)
