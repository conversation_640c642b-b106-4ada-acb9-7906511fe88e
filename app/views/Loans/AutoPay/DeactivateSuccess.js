import React from 'react'
import {View, Text, StyleSheet, TouchableOpacity, Image} from 'react-native'
import {Card, TextReg, TextBold, BackgroundHeader} from '../../../components'
import commonStyles from '../../../styles/commonStyles'
import {useNavigation} from '@react-navigation/native'
import {localLogout} from '../../../store/auth/auth.actions'

const DeactivateSuccess = ({route}) => {
  const navigation = useNavigation()
  const {source} = route.params

  console.log('source', source)

  return (
    <View style={commonStyles.tileContainer}>
      <BackgroundHeader
        title={' '}
        goBack={() => {
          navigation.goBack()
          navigation.goBack()
          navigation.goBack()
        }}
      />

      <View style={styles.box}>
        <View>
          <View style={styles.roundGrey}>
            <Image
              source={require('../../../imgs/graphics/circleCheck.png')}
              style={styles.cancelDark}
            />
          </View>

          <TextBold style={styles.title}>Autopayment</TextBold>
          <TextBold style={styles.title}>Deactivated</TextBold>
          <TextBold style={styles.title}>Successfully!</TextBold>
        </View>
        <View style={{marginBottom: 40}}>
          <TouchableOpacity
            onPress={() => {
              navigation.goBack()
              navigation.goBack()
              navigation.goBack()
            }}>
            <View style={styles.acceptButton}>
              <TextBold style={styles.acceptText}>CLOSE</TextBold>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  box: {
    flex: 1,
    alignSelf: 'stretch',
    justifyContent: 'space-between',
  },
  row: {
    flexDirection: 'row',
    alignSelf: 'stretch',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: '#ccc',
    marginLeft: 6,
    marginRight: 6,
  },
  label: {
    fontSize: 16,
    color: '#FFF',
  },
  value: {
    fontSize: 16,
    color: '#FFF',
  },
  cancelText: {
    fontSize: 20,
    color: '#E5705A',
    marginTop: 10,
    marginBottom: 10,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  roundGrey: {
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: '#3D3D50',
    alignSelf: 'center',
    marginTop: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  cancelDark: {
    width: 70,
    height: 70,
    alignSelf: 'center',
  },
  title: {
    fontSize: 34,
    color: '#FFF',
    textAlign: 'center',
    marginTop: 2,
  },
  description: {
    fontSize: 14,
    color: '#FFF',
    textAlign: 'center',
    marginTop: 10,
    marginLeft: 30,
    marginRight: 30,
  },
  acceptButton: {
    backgroundColor: '#00FFBD',
    alignSelf: 'stretch',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    marginLeft: 20,
    marginRight: 20,
    borderRadius: 4,
  },
  acceptText: {
    fontSize: 18,
    color: '#3D3D50',
    textAlign: 'center',
    textTransform: 'uppercase',
  },
})

export default DeactivateSuccess
