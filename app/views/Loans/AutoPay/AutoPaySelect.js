import React, {Component} from 'react'
import {View, TouchableOpacity, StatusBar, ScrollView, Image} from 'react-native'
import {connect} from 'react-redux'
import {getTokenPic} from '../../../util/tokens'
import {TextBold, Card, TextReg, Button, BackgroundHeader} from '../../../components'
import commonStyles from '../../../styles/commonStyles'

class AutoPaySelect extends Component {
  constructor(props) {
    super(props)
    this.state = {
      banksArr: [],
      refreshing: false,
      chooseBank: false,
      showPayCollateral: false,
      agree: false,
      loadingConfirm: false,
      selectedPayment: null,
      loading1: true,
      overLimit: false,
    }
  }

  componentDidMount() {
    this.checkAchLimit()
  }

  checkAchLimit = () => {
    let loanType = 'principal'
    if (this.props.loanData.interestOnly) {
      loanType = 'interest'
    }

    let paymentDate = this.props.loanData?.amortizationInfo?.amortizationSchedule?.find(
      payment => Number(payment.principalPaid) < Number(payment.principalDue),
    )?.date

    if (loanType == 'interest') {
      paymentDate = this.props.loanData?.amortizationInfo?.amortizationSchedule?.find(
        payment => Number(payment.interestPaid) < Number(payment.interestDue),
      )?.date
    }

    console.log('this.props.loanData', this.props.loanData)

    let nextPaymentAmount = this.props.loanData?.amortizationInfo?.nextPaymentAmount

    console.log('paymentDate', paymentDate)
    if (paymentDate) {
      const date = new Date(paymentDate)
      date.setDate(date.getDate() - 1)
      paymentDate = date.toISOString().split('T')[0]
    }

    let payload = {
      newAchAmount: 0,
      date: paymentDate,
      nextPaymentAmount: nextPaymentAmount || null,
    }

    console.log('checkAchLimit payload', payload)

    this.props.WebService.achLimit(payload)
      .then(res => {
        console.log('achLimit res', res)
        //true = allowed
        //false = over limit
        if (res.data?.isWithinLimit !== true) {
          console.log('over limit')
          this.setState({overLimit: true})
        }
        this.setState({loading1: false})
      })
      .catch(err => {
        console.log('achLimit err', err)
        this.setState({loading1: false})
      })
  }

  goToBanks = () => {
    this.props.navigation.navigate('Banking')
  }

  selectPaymentType = type => {
    this.setState({selectedPayment: type})
  }

  next = () => {
    if (this.state.selectedPayment === 'collateral') {
      this.props.navigation.navigate('AutoPayConfirm', {
        selectedPayment: this.state.selectedPayment,
        getLoansData: this.props.route?.params?.getLoansData,
        source: this.props.route?.params?.source,
        type: 'collateral',
      })
    }
    if (this.state.selectedPayment === 'bank') {
      this.props.navigation.navigate('AutoPayBank', {
        getLoansData: this.props.route?.params?.getLoansData,
        source: this.props.route?.params?.source,
        type: 'bank',
      })
    }
  }

  render() {
    let noBank =
      this.props.launchDarkly[`disable-ach`] != null && this.props.launchDarkly[`disable-ach`]

    //let getLoansData = this.props.route?.params?.getLoansData
    const {automatedPaymentType, paymentBankAccount} = this.props.loanData
    console.log('automatedPaymentType', automatedPaymentType)

    const showBanks = this.props.banksArr.map((a, k) => (
      <TouchableOpacity key={k} onPress={() => this.connectBankToLoan(a.id)}>
        <Card cardWidth={280}>
          <TextReg style={{alignSelf: 'flex-start', paddingLeft: 8}}>
            {a.name} - ********{a.accountNumber}
          </TextReg>
        </Card>
      </TouchableOpacity>
    ))

    return (
      <View key={'loanScreen'} style={commonStyles.tileContainer}>
        <StatusBar barStyle="light-content" />
        <View style={{marginLeft: 10, alignSelf: 'stretch'}}>
          <BackgroundHeader
            title={' '}
            toggleNotifications={this.toggleNotifications}
            goBack={() => this.props.navigation.goBack()}
          />
        </View>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            alignSelf: 'stretch',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
          style={{
            alignSelf: 'stretch',
          }}>
          <View style={{alignItems: 'center', alignSelf: 'stretch'}}>
            <TextReg
              style={{
                textAlign: 'center',
                marginTop: 0,
                fontSize: 20,
              }}>{`Set Up Automatic Payments`}</TextReg>
            <TextReg
              style={{
                width: 330,
                marginTop: 20,
              }}>{`In case you don't transfer your monthly payment in time, we'll use your selected method to auto-pay your loan's payment on the 15th of each month.`}</TextReg>

            <View style={{width: 330, marginTop: 30}}>
              {false && (
                <TouchableOpacity
                  onPress={() => this.selectPaymentType('wallet')}
                  style={{
                    borderRadius: 10,
                    backgroundColor: '#3D3D50',
                    padding: 15,
                    flexDirection: 'row',
                    borderWidth: 1.5,
                    borderColor: this.state.selectedPayment === 'wallet' ? '#00FFBD' : '#3D3D50',
                  }}>
                  <Image source={getTokenPic('USDC')} style={{width: 40, height: 40}} />
                  <View style={{flexDirection: 'column', marginLeft: 10, marginBottom: 2}}>
                    <TextBold style={{color: '#FFF', fontSize: 17}}>{`WALLET`}</TextBold>
                    <TextReg
                      style={{
                        color: '#FFF',
                        width: 250,
                      }}>{`We’ll use your additional collateral from your loan to make these payments.`}</TextReg>
                  </View>
                </TouchableOpacity>
              )}
              {automatedPaymentType != 'crypto' && (
                <TouchableOpacity
                  onPress={() => this.selectPaymentType('collateral')}
                  style={{
                    borderRadius: 10,
                    backgroundColor: '#3D3D50',
                    padding: 15,
                    flexDirection: 'row',
                    marginTop: 10,
                    borderWidth: 1.5,
                    borderColor:
                      this.state.selectedPayment === 'collateral' ? '#00FFBD' : '#3D3D50',
                  }}>
                  <Image
                    source={require('../../../imgs/graphics/collatStack.png')}
                    style={{width: 40, height: 40}}
                  />
                  <View style={{flexDirection: 'column', marginLeft: 10, marginBottom: 2}}>
                    <TextBold style={{color: '#FFF', fontSize: 17}}>{`Collateral`}</TextBold>
                    <TextReg
                      style={{
                        color: '#FFF',
                        width: 250,
                      }}>{`We’ll use your additional collateral from your loan to make these payments.`}</TextReg>
                  </View>
                </TouchableOpacity>
              )}
              {!noBank && (
                <>
                  <TouchableOpacity
                    onPress={() => this.selectPaymentType('bank')}
                    style={{
                      borderRadius: 10,
                      backgroundColor: '#3D3D50',
                      padding: 15,
                      flexDirection: 'row',
                      marginTop: 10,
                      borderWidth: 1.5,
                      borderColor: this.state.selectedPayment === 'bank' ? '#00FFBD' : '#3D3D50',
                    }}>
                    <Image
                      source={require('../../../imgs/graphics/bank.png')}
                      style={{width: 40, height: 40}}
                    />
                    <View style={{flexDirection: 'column', marginLeft: 10, marginBottom: 2}}>
                      <TextBold style={{color: '#FFF', fontSize: 17}}>{`Bank Account`}</TextBold>
                      <TextReg
                        style={{
                          color: '#FFF',
                          width: 250,
                        }}>{`We’ll use your Connected Bank Account to make these payments.`}</TextReg>
                      <View
                        style={{
                          flexDirection: 'row',
                          marginTop: 6,
                          borderWidth: 0.5,
                          borderColor: '#ccc',
                          borderRadius: 6,
                          padding: 4,
                          width: 250,
                        }}>
                        <Image
                          source={require('../../../imgs/graphics/info2.png')}
                          style={{width: 14, height: 14, marginRight: 4}}
                        />
                        <TextReg
                          style={{
                            color: '#FFF',
                            width: 220,
                            fontSize: 12,
                          }}>{`Limited to one ACH at a time per loan account and $10,000 per calendar week (Sunday - Saturday).`}</TextReg>
                      </View>
                    </View>
                  </TouchableOpacity>
                  {this.state.overLimit && this.state.selectedPayment === 'bank' && (
                    <TextReg style={{color: 'red', fontSize: 12, marginTop: 10}}>
                      {`Since your payment is over $10,000, please set up ACH through your bank.`}
                    </TextReg>
                  )}
                </>
              )}
            </View>
          </View>

          <View style={{alignItems: 'center', alignSelf: 'stretch'}}>
            <Button
              disabled={
                this.state.selectedPayment === null || this.state.loading1 || this.state.overLimit
              }
              style={{width: 330, marginBottom: 30, borderRadius: 4}}
              isLoading={this.state.loading1}
              onPress={() => this.next()}>
              <TextBold
                style={{
                  color: '#000',
                  fontSize: 16,
                  letterSpacing: 1.2,
                }}>{`NEXT`}</TextBold>
            </Button>
          </View>
        </ScrollView>
      </View>
    )
  }
}

let local = {
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderBottomWidth: 0.5,
    borderColor: '#ccc',
    paddingBottom: 6,
    paddingTop: 6,
  },
  rowBottom: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 6,
    paddingTop: 6,
  },
}

AutoPaySelect.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  WebService: state.auth.WebService,
  showPinScreen: state.auth.pinScreen,
  banksArr: state.user.banks,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(AutoPaySelect)
