import React, {Component} from 'react'
import {View, TouchableOpacity, StatusBar, ScrollView, Image} from 'react-native'
import {connect} from 'react-redux'
import {getTokenPic} from '../../../util/tokens'
import {TextBold, Card, TextReg, Button, BackgroundHeader} from '../../../components'
import commonStyles from '../../../styles/commonStyles'

class AutoPayBank extends Component {
  constructor(props) {
    super(props)
    this.state = {
      banksArr: [],
      refreshing: false,
      chooseBank: false,
      showPayCollateral: false,
      agree: false,
      loadingConfirm: false,
      selectedPayment: null,
      bank: null,
    }
  }

  goToBanks = () => {
    this.props.navigation.navigate('Banking')
  }

  next = () => {
    if (this.state.bank === 'new') {
      this.props.navigation.navigate('Banking', {flow: 'autoPay'})
      return
    }
    //else
    let bank = this.props.banksArr.find(a => a.id === this.state.bank)
    console.log('autopay bank next - bank', bank)
    this.props.navigation.navigate('AutoPayConfirm', {
      selectedPayment: 'bank',
      getLoansData: this.props.route?.params?.getLoansData,
      bank,
      source: this.props.route?.params?.source,
      type: this.props.route?.params?.type,
    })
  }

  render() {
    let noBank =
      this.props.launchDarkly[`disable-ach`] != null && this.props.launchDarkly[`disable-ach`]

    const {automatedPaymentType, paymentBankAccount} = this.props.loanData
    console.log('autopay bank - paymentBankAccount', paymentBankAccount)

    const showBanks = this.props.banksArr.map((a, k) => {
      if (!a.isPlaid) {
        return null
      }
      if (a.name == paymentBankAccount?.name) {
        return null
      }

      return (
        <TouchableOpacity
          key={k}
          onPress={() => this.setState({bank: a.id})}
          style={{
            borderRadius: 10,
            backgroundColor: '#3D3D50',
            padding: 7,
            flexDirection: 'row',
            alignItems: 'center',
            marginTop: 10,
            borderWidth: 1.5,
            borderColor: this.state.bank === a.id ? '#00FFBD' : '#3D3D50',
          }}>
          <View
            style={{
              borderRadius: 20,
              height: 40,
              width: 40,
              backgroundColor: '#28283D',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Image
              source={require('../../../imgs/graphics/bank.png')}
              style={{width: 20, height: 20, marginBottom: 2}}
            />
          </View>

          <TextBold style={{paddingLeft: 10}}>{a.name}</TextBold>
        </TouchableOpacity>
      )
    })

    return (
      <View key={'loanScreen'} style={commonStyles.tileContainer}>
        <StatusBar barStyle="light-content" />
        <View style={{marginLeft: 10, alignSelf: 'stretch'}}>
          <BackgroundHeader
            title={' '}
            toggleNotifications={this.toggleNotifications}
            goBack={() => this.props.navigation.goBack()}
          />
        </View>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            alignSelf: 'stretch',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
          style={{
            alignSelf: 'stretch',
          }}>
          <View style={{alignItems: 'center', alignSelf: 'stretch'}}>
            <TextReg
              style={{
                textAlign: 'center',
                marginTop: 0,
                fontSize: 20,
              }}>{`Select a Bank Account`}</TextReg>
            <View style={{width: 330, marginTop: 30}}>
              {showBanks}
              <TouchableOpacity
                onPress={() => this.setState({bank: 'new'})}
                style={{
                  borderRadius: 10,
                  backgroundColor: '#28283D',
                  padding: 7,
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginTop: 10,
                  borderWidth: 1.5,
                  borderColor: this.state.bank === 'new' ? '#00FFBD' : '#555',
                }}>
                <View
                  style={{
                    borderRadius: 20,
                    height: 40,
                    width: 40,
                    borderColor: '#00FFBD',
                    borderWidth: 0.5,
                    backgroundColor: '#28283D',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Image
                    source={require('../../../imgs/graphics/greenPlus.png')}
                    style={{width: 20, height: 20, marginBottom: 2}}
                  />
                </View>

                <TextBold style={{paddingLeft: 10}}>{`Connect New Bank Account`}</TextBold>
              </TouchableOpacity>
            </View>
          </View>

          <View style={{alignItems: 'center', alignSelf: 'stretch'}}>
            <Button
              disabled={this.state.bank === null}
              style={{width: 330, marginBottom: 30, borderRadius: 4}}
              onPress={() => this.next()}>
              <TextBold
                style={{
                  color: '#000',
                  fontSize: 16,
                  letterSpacing: 1.2,
                }}>{`NEXT`}</TextBold>
            </Button>
          </View>
        </ScrollView>
      </View>
    )
  }
}

let local = {
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderBottomWidth: 0.5,
    borderColor: '#ccc',
    paddingBottom: 6,
    paddingTop: 6,
  },
  rowBottom: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 6,
    paddingTop: 6,
  },
}

AutoPayBank.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  WebService: state.auth.WebService,
  showPinScreen: state.auth.pinScreen,
  banksArr: state.user.banks,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(AutoPayBank)
