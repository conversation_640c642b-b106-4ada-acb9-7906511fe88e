import React from 'react';
import {View, Image, TouchableOpacity} from 'react-native';

import {TextBold, TextReg, Card, Button} from '../../components';

import styles from './styles';

const EditBanksBox = props => (
  <TouchableOpacity onPress={() => props.goToBanks()}>
    <Card marginTop={0} cardMarginBottom={0}>
      <View
        style={{
          alignSelf: 'stretch',
          paddingBottom: 7,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        <TextReg
          style={{
            fontSize: 22,
            marginTop: 6,
            marginLeft: 8,
          }}>{`Manage Bank Accounts`}</TextReg>
        <View
          style={{
            marginTop: 6,
            marginRight: 8,
          }}>
          <Image source={require('../../imgs/rightArrow.png')} style={{height: 24, width: 24}} />
        </View>
      </View>
    </Card>
  </TouchableOpacity>
);

export default EditBanksBox;
