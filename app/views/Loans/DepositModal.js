import React, {Component} from 'react'
import {
  Modal,
  View,
  Image,
  TouchableOpacity,
  Dimensions,
  ScrollView,
} from 'react-native'

import LottieView from 'lottie-react-native'
import {TextReg, TextBold, DepositCollateralList, Card} from '../../components'
import {numberWithCommas} from '../../util/helpers'
import commonStyles from '../../styles/commonStyles'
import styles from './styles'

const {height: ScreenHeight} = Dimensions.get('window')

class DepositModal extends Component {
  constructor(props) {
    super(props)
    this.state = {
      showConfirmDeposit: false,
      depositToken: 'BTC',
      optionSelect: 70,
      screenWidth: 400,
    }
  }
  showConfirmDeposit = token => {
    this.setState({showConfirmDeposit: true, depositToken: token})
  }

  chooseOption = (optionSelect, isDisabled) => {
    if (isDisabled) return
    this.setState({optionSelect})
  }

  getScreenWidth = layout => {
    const {x, y, width, height} = layout
    this.setState({screenWidth: width})
  }

  render() {
    const {loanValue, collateralValue, thresholds} = this.props.loanData
    const marginCure = thresholds?.marginCure
    const marginCall = thresholds?.marginCall

    let showPriceCure = (
      (loanValue - Number(collateralValue) * marginCure) /
      marginCure
    ).toFixed(2)

    if (showPriceCure < 0) {
      showPriceCure = '0.00'
    }

    let showPriceCall = (
      (loanValue - Number(collateralValue) * marginCall) /
      marginCall
    ).toFixed(2)

    if (showPriceCall < 0) {
      showPriceCall = '0.00'
    }

    const tokenUSD = `${this.state.depositToken}-USD`
    const price = this.props.prices[tokenUSD].price

    const valuePrice =
      this.state.optionSelect == 70 ? showPriceCure : showPriceCall

    const showValue = (valuePrice / price).toFixed(4)

    return (
      <Modal
        animationType="slide"
        transparent
        visible={this.props.depositModalVisible && !this.props.showPinScreen}
        onRequestClose={() => this.props.toggleDepositModal()}>
        <View
          style={{
            flex: 1,
            alignItems: 'center',
            backgroundColor: '#28283D',
          }}>
          <View
            style={{
              height: 90,
              backgroundColor: '#28283d',
              alignSelf: 'stretch',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingRight: 10,
              paddingTop: 20,
            }}>
            <View style={{width: 30}} />
            <TextReg style={{fontSize: 20, color: '#fff', marginTop: 4}}>
              Deposit Collateral
            </TextReg>
            <TouchableOpacity
              style={{width: 30, marginBottom: -4}}
              onPress={() => {
                this.props.toggleDepositModal()
              }}>
              <Image
                source={require('../../imgs/closeX.png')}
                style={commonStyles.closeModalImg}
              />
            </TouchableOpacity>
          </View>
          <ScrollView
            style={{alignSelf: 'stretch', paddingBottom: 20}}
            contentContainerStyle={{alignItems: 'center'}}>
            <View style={commonStyles.modalSquare}>
              <TextReg style={{marginBottom: 24, marginTop: 20, fontSize: 20}}>
                Select a collateral type:
              </TextReg>
              <DepositCollateralList
                showConfirmDeposit={this.showConfirmDeposit}
              />
            </View>
          </ScrollView>
        </View>
        {this.state.showConfirmDeposit && (
          <View
            style={{
              position: 'absolute',
              width: '100%',
              alignSelf: 'stretch',
              height: ScreenHeight,
            }}>
            <TouchableOpacity
              onPress={() => this.setState({showConfirmDeposit: false})}>
              <View
                style={{
                  height: ScreenHeight - 380,
                  opacity: 0.8,
                  backgroundColor: '#333',
                }}
              />
            </TouchableOpacity>

            <View
              style={{
                alignSelf: 'stretch',
                height: 380,
                backgroundColor: '#28283D',
                alignItems: 'center',
                paddingTop: 20,
              }}>
              <TextReg
                style={{
                  fontSize: 16,
                  marginBottom: 20,
                  marginLeft: 30,
                  marginRight: 30,
                }}>
                {`We recommend depositing the suggested amount of collateral to cure your LTV to a Healthy Status (<70% LTV).`}
              </TextReg>
              <View
                style={{
                  flexDirection: 'row',
                  alignSelf: 'stretch',
                  justifyContent: 'center',
                }}
                onLayout={event => {
                  this.getScreenWidth(event.nativeEvent.layout)
                }}>
                <TouchableOpacity
                  style={{
                    opacity: showPriceCure <= 0 ? 0.4 : 1,
                    marginRight: 10,
                  }}
                  onPress={() => this.chooseOption(70, showPriceCure <= 0)}>
                  <Card
                    cardWidth={(this.state.screenWidth - 50) / 2}
                    cardMarginBottom={12}
                    style={[
                      styles.depositOptionCard,
                      {
                        borderColor:
                          this.state.optionSelect == 70 ? '#00FFBD' : '#FFF',
                      },
                    ]}>
                    <TextBold
                      style={{
                        fontSize: 22,
                        color: '#34e89e',
                      }}>{`$${numberWithCommas(showPriceCure)}`}</TextBold>
                    <TextReg>Suggested Amount</TextReg>
                    <TextReg>{`LTV < 70%`}</TextReg>
                  </Card>
                </TouchableOpacity>
                <TouchableOpacity
                  style={{opacity: showPriceCall <= 0 ? 0.4 : 1}}
                  onPress={() => this.chooseOption(83, showPriceCall <= 0)}>
                  <Card
                    cardWidth={(this.state.screenWidth - 50) / 2}
                    cardMarginBottom={12}
                    style={[
                      styles.depositOptionCard,
                      {
                        borderColor:
                          this.state.optionSelect == 83 ? '#00FFBD' : '#FFF',
                      },
                    ]}>
                    <TextBold
                      style={{
                        fontSize: 22,
                        color: '#e5705a',
                      }}>{`$${numberWithCommas(showPriceCall)}`}</TextBold>
                    <TextReg>Minimum Amount</TextReg>
                    <TextReg>{`LTV < 83.33%`}</TextReg>
                  </Card>
                </TouchableOpacity>
              </View>

              <TextBold style={{fontSize: 26, marginTop: 12}}>
                {showValue} {this.state.depositToken}
              </TextBold>
              <TouchableOpacity
                onPress={() => {
                  this.props.continueToDeposit(
                    valuePrice,
                    showValue,
                    this.state.depositToken,
                  )
                }}
                style={{
                  height: 52,
                  width: 240,
                  backgroundColor: '#00FFBD',
                  borderRadius: 14,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 18,
                }}>
                {this.props.creatingWallet ? (
                  <LottieView
                    ref={animation => {
                      this.animation = animation
                    }}
                    style={{
                      width: 60,
                      height: 60,
                      opacity: 0.9,
                      alignSelf: 'center',
                      zIndex: 5,
                    }}
                    source={require('../../imgs/lotti/loading-white-dots.json')}
                    autoPlay
                  />
                ) : (
                  <TextBold style={{fontSize: 17, color: '#000'}}>
                    NEXT
                  </TextBold>
                )}
              </TouchableOpacity>
            </View>
          </View>
        )}
      </Modal>
    )
  }
}

export default DepositModal
