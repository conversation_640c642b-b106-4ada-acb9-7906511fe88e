/* eslint-disable import/no-namespace */
/* eslint-disable no-undef */

import React from 'react'
import * as enzyme from 'enzyme'
import thunk from 'redux-thunk'
import configureMockStore from 'redux-mock-store'
import DeviceInfo from 'react-native-device-info'
import { LoanScreen } from './LoanScreen'
import initialState from '../../store/initialState'

jest.mock('../../util/pushNotifs', () => ({
  loadPushNotifs: jest.fn(),
  notifDisplayed: jest.fn(),
  openPushNotifs: jest.fn(),
}))

const mockStore = configureMockStore([thunk])

const WebService = {
  getLoans: jest.fn(() =>
    Promise.resolve({
      data: { loanValue: '123', status: 'active' },
    })
  ),
  getPrices: jest.fn(() =>
    Promise.resolve({
      data: { loanValue: '123', status: 'active' },
    })
  ),
  getSaltUser: jest.fn(() =>
    Promise.resolve({
      data: { loanValue: '123', status: 'active' },
    })
  ),
  getWallets: jest.fn(() =>
    Promise.resolve({
      data: [{ currency: 'BTC' }],
    })
  ),
}

const buildComponent = (renderType = enzyme.shallow, newProps = {}) => {
  const defaultProps = {
    dispatch: jest.fn(() => Promise.resolve({})),
    navigation: {
      addListener: jest.fn(),
    },
    showPinScreen: false,
    loanData: {
      ltv: '0',
      loanValue: '0',
      collateralValue: '0',
      loans: [],
      collaterals: [
        {
          currency: 'BTC',
          value: '0',
          price: '0',
          balance: '0',
        },
        {
          currency: 'ETH',
          value: '0',
          price: '0',
          balance: '0',
        },
        {
          currency: 'SALT',
          value: '0',
          price: '0',
          balance: '0',
        },
        {
          currency: 'LTC',
          value: '0',
          price: '0',
          balance: '0',
        },
        {
          currency: 'DOGE',
          value: '0',
          price: '0',
          balance: '0',
        },
      ],
    },
    prices: {
      'BTC-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.336363Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'ETH-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'SALT-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'LTC-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'DOGE-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
    },
    WebService,
  }

  const props = { ...defaultProps, ...newProps }
  return renderType(<LoanScreen store={mockStore(initialState)} {...props} />)
}

describe('Testing LoanScreen', () => {
  it('renders component', () => {
    const wrapper = buildComponent().dive()
    expect(
      wrapper.findWhere(node => node.prop('testID') === 'loansContainer').length
    ).toBe(1)
  })

  it('should initialize with the correct state', () => {
    const wrapper = buildComponent()
    expect(wrapper.state()).toEqual({
      infoToggleState: 0,
      helpModalVisable: false,
      connectedModalVisable: false,
      depositModalVisible: false,
      refreshing: true,
      noLoans: false,
      pendingLoan: false,
      firstTimeLoading: true,
    })
  })

  it('should show no loans component if user has no loans', () => {
    const wrapper = buildComponent()
    expect(
      wrapper.findWhere(node => node.prop('testID') === 'noLoans').length
    ).toBe(0)
    wrapper.setState({ firstTimeLoading: false, noLoans: true })
    expect(
      wrapper.findWhere(node => node.prop('testID') === 'noLoans').length
    ).toBe(1)
    expect(
      wrapper.findWhere(node => node.prop('testID') === 'pendingLoan').length
    ).toBe(0)
  })

  it('should show pending loan if user has requested a loan', () => {
    const wrapper = buildComponent()
    wrapper.setState({
      firstTimeLoading: false,
      noLoans: false,
      pendingLoan: true,
    })
    expect(
      wrapper.findWhere(node => node.prop('testID') === 'noLoans').length
    ).toBe(0)
    expect(
      wrapper.findWhere(node => node.prop('testID') === 'pendingLoan').length
    ).toBe(1)
  })

  it('should show LTVList and LTVInfo if user has an active loan', () => {
    const wrapper = buildComponent()
    wrapper.setState({
      firstTimeLoading: false,
      noLoans: false,
      pendingLoan: false,
    })
    expect(
      wrapper.findWhere(node => node.prop('testID') === 'noLoans').length
    ).toBe(0)
    expect(
      wrapper.findWhere(node => node.prop('testID') === 'pendingLoan').length
    ).toBe(0)
    expect(
      wrapper.findWhere(node => node.prop('testID') === 'LTVList').length
    ).toBe(1)
    expect(
      wrapper.findWhere(node => node.prop('testID') === 'LTVInfo').length
    ).toBe(1)
  })

  it('getPrices(): should call WebService getPrices()', () => {
    const wrapper = buildComponent()

    const getPrices = jest.spyOn(
      wrapper.instance().props.WebService,
      'getPrices'
    )

    wrapper.instance().fetchApiData()

    expect(getPrices).toHaveBeenCalled()
  })

  it('getLoans(): should call WebService getLoans()', () => {
    const wrapper = buildComponent()

    const getLoans = jest.spyOn(wrapper.instance().props.WebService, 'getLoans')

    wrapper.instance().fetchApiData()

    expect(getLoans).toHaveBeenCalled()
  })

  it('getSaltUser(): should call WebService getSaltUser()', () => {
    const wrapper = buildComponent()

    const getSaltUser = jest.spyOn(
      wrapper.instance().props.WebService,
      'getSaltUser'
    )

    wrapper.instance().fetchApiData()

    expect(getSaltUser).toHaveBeenCalled()
  })

  it('getSaltUser(): should call WebService getSaltUser()', () => {
    const wrapper = buildComponent()

    const getSaltUser = jest.spyOn(
      wrapper.instance().props.WebService,
      'getSaltUser'
    )

    wrapper.instance().fetchApiData()

    expect(getSaltUser).toHaveBeenCalled()
  })

  it('should not show error modal by default', () => {
    const wrapper = buildComponent()

    expect(
      wrapper.findWhere(node => node.prop('testID') === 'serverErrorModal')
        .length
    ).toBe(0)
  })

  it('should not show error modal if a loan data request fails', () => {
    const getLoansMock = jest.fn(() =>
      Promise.reject(new Error({ status: 500 }))
    )

    const wrapper = buildComponent(enzyme.shallow, {
      WebService: {
        ...WebService,
        getLoans: getLoansMock,
      },
    })

    expect(getLoansMock).toHaveBeenCalled()

    process.nextTick(() => {
      expect(
        wrapper.findWhere(node => node.prop('testID') === 'serverErrorModal')
          .length
      ).toBe(1)
    })
  })
})
