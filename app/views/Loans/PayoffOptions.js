import React, {useState} from 'react'
import {View, Image, TouchableOpacity, Dimensions} from 'react-native'
import {connect} from 'react-redux'
import CalendarPicker from 'react-native-calendar-picker'
import moment from 'moment'

import {TextBold, Card, TextReg, Button, BackgroundHeader} from '../../components'
import commonStyles from '../../styles/commonStyles'
import {calculateLoanPayoffAmount} from '../../util/helpers'

import styles from './styles'

const {width: screenWidth} = Dimensions.get('window')

const PayoffOptions = props => {
  const [optionSelect, setOptionSelect] = useState(null)
  const [payoffDate, setPayoffDate] = useState()
  const [openCalendar, setOpenCalendar] = useState(false)
  const [loading, setLoading] = useState(false)

  const {navigation, loanData, WebService, launchDarkly} = props

  const selectOption = selection => {
    if (optionSelect === selection) {
      setOptionSelect(null)
    } else {
      setOptionSelect(selection)
    }
  }

  const next = async () => {
    if (optionSelect == 'Wire') {
      if (!payoffDate) {
        setOpenCalendar(true)
      } else {
        setLoading(true)
        await WebService.getLender(loanData.lenderId)
          .then(res => {
            console.log('get lender res', res)
            setLoading(false)
            const payonDate = moment(payoffDate).format('MM/DD/YYYY')
            console.log('loanData', loanData)
            console.log('payonDate', payonDate)
            const payoffAmount = calculateLoanPayoffAmount(loanData, payoffDate).toFormat(2)
            console.log('payoffAmount', payoffAmount)

            navigation.navigate('WireTransfer', {
              lender: res.data,
              payoffAmount,
              payonDate,
              payoff: true,
            })
          })
          .catch(err => {
            setLoading(false)
            console.log('get lender err', err)
          })
      }
    }

    switch (optionSelect) {
      case 'Stablecoin':
        navigation.navigate('StablecoinChoice', {payoff: true})
        break
      case 'Bank':
        navigation.navigate('BankTransfer', {payoff: true})
        break
      case 'Collateral':
        navigation.navigate('CollateralPayment', {getLoansData: props?.route?.params?.getLoansData})
        break
    }
  }

  const disableDates = date => {
    const dayValue = moment(date).day()
    //testing today is 4/14/2025
    const isWeekend = dayValue === 0 || dayValue === 6

    const isWithinRange =
      moment(loanData?.amortizationInfo?.nextPaymentDate).subtract(0, 'd').startOf('day') >= date

    return (moment().add(1, 'd').startOf('d') <= date && !isWeekend && isWithinRange) === false
      ? true
      : false
  }

  let noTransfer = launchDarkly['disable-wire-transfer'] || false

  let ldAchOff = launchDarkly['disable-ach'] || false

  return (
    <View style={commonStyles.tileContainer}>
      <BackgroundHeader
        title={'Pay Off Loan'}
        goBack={openCalendar ? () => setOpenCalendar(false) : navigation.goBack}
      />

      {!openCalendar ? (
        <React.Fragment>
          <TextReg style={{marginTop: 24, marginBottom: 24, fontSize: 18}}>
            How would you like to pay?
          </TextReg>
          {!ldAchOff && false && (
            <TouchableOpacity onPress={() => selectOption('Bank')} disabled={ldAchOff}>
              <Card
                cardWidth={300}
                cardMarginBottom={12}
                style={[
                  styles.oneTimeOptionCard,
                  {
                    borderColor: optionSelect === 'Bank' ? '#00FFBD' : '#FFF',
                    opacity: ldAchOff ? 0.5 : 1,
                  },
                ]}>
                <View style={styles.oneTimeOptionCardBox}>
                  <Image
                    source={require('../../imgs/graphics/bank.png')}
                    style={styles.oneTimeOptionImg}
                  />
                  <TextBold style={styles.oneTimeOptionTitle}>Bank Transfer</TextBold>
                </View>
              </Card>
            </TouchableOpacity>
          )}
          <TouchableOpacity onPress={() => selectOption('Wire')} disabled={noTransfer}>
            <Card
              cardWidth={300}
              cardMarginBottom={12}
              style={[
                styles.oneTimeOptionCard,
                {
                  borderColor: optionSelect === 'Wire' ? '#00FFBD' : '#FFF',
                  opacity: noTransfer ? 0.5 : 1,
                },
              ]}>
              <View style={styles.oneTimeOptionCardBox}>
                <Image
                  source={require('../../imgs/graphics/globe.png')}
                  style={styles.oneTimeOptionImg}
                />
                <TextBold style={styles.oneTimeOptionTitle}>Wire Transfer</TextBold>
              </View>
            </Card>
          </TouchableOpacity>

          <TouchableOpacity onPress={() => selectOption('Stablecoin')}>
            <Card
              cardWidth={300}
              cardMarginBottom={12}
              style={[
                styles.oneTimeOptionCard,
                {
                  borderColor: optionSelect === 'Stablecoin' ? '#00FFBD' : '#FFF',
                },
              ]}>
              <View style={styles.oneTimeOptionCardBox}>
                <Image
                  source={require('../../imgs/loanHealth/healthyLoan.png')}
                  style={styles.oneTimeOptionImg}
                />
                <TextBold style={styles.oneTimeOptionTitle}>Stablecoin</TextBold>
              </View>
            </Card>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => selectOption('Collateral')}>
            <Card
              cardWidth={300}
              cardMarginBottom={12}
              style={[
                styles.oneTimeOptionCard,
                {
                  borderColor: optionSelect === 'Collateral' ? '#00FFBD' : '#FFF',
                },
              ]}>
              <View style={styles.oneTimeOptionCardBox}>
                <Image
                  source={require('../../imgs/bitcoinStack.png')}
                  style={styles.oneTimeOptionImg}
                />
                <TextBold style={styles.oneTimeOptionTitle}>Collateral</TextBold>
              </View>
            </Card>
          </TouchableOpacity>
        </React.Fragment>
      ) : (
        <CalendarPicker
          width={screenWidth - 40}
          marginTop={20}
          selectedDayColor={'#00FFBD'}
          selectedDayTextColor={'#000'}
          onDateChange={selectedDate => setPayoffDate(selectedDate)}
          dayShape={'square'}
          disabledDates={disableDates}
          todayBackgroundColor={'#555'}
          textStyle={{color: '#FFF'}}
          disabledDatesTextStyle={{color: '#777'}}
        />
      )}

      <Button
        style={{marginTop: 16, width: 300}}
        disabled={openCalendar ? !payoffDate : !optionSelect}
        onPress={() => next()}
        isLoading={loading}>
        Next
      </Button>
    </View>
  )
}

const mapStateToProps = state => ({
  accountRef: state.auth.account.ref,
  loanData: state.user.loanData,
  user: state.user.user,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
  refreshCount: state.user.refreshCount,
  WebService: state.auth.WebService,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(PayoffOptions)
