import React from 'react'
import { View, Text, TouchableOpacity, Image } from 'react-native'

import { TextBold } from '../../components'
import styles from './styles'

const TwoFactorSetup = props => (
  <View style={styles.twoFactorBox}>
    <View style={styles.twoFactorSquare}>
      <Image
        source={require('../../imgs/authenticator.png')}
        style={{ width: 200, height: 127, marginBottom: 30 }}
      />
      <TextBold style={styles.notificationTitle}>Two Factor</TextBold>
      <Text style={styles.notificationDescription}>
        Please set up Two-Factor Authentication for security
      </Text>

      <TouchableOpacity
        style={styles.notificationModalActivatePromptButton}
        onPress={() => props.goToTwoFactor()}
      >
        <TextBold style={styles.notificationModalActivatePromptText}>
          SETUP TWO-FACTOR
        </TextBold>
      </TouchableOpacity>

      <TouchableOpacity onPress={() => props.skipTwoFactor()}>
        <TextBold style={styles.notificationNotNow}>Skip</TextBold>
      </TouchableOpacity>
    </View>
  </View>
)

export default TwoFactorSetup
