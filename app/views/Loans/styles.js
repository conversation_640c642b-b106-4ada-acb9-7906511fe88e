import {StyleSheet, Dimensions, PixelRatio} from 'react-native'

const {width} = Dimensions.get('window')

const styles = StyleSheet.create({
  openDepositModalButton: {
    backgroundColor: '#00FFBD',
    borderRadius: 14,
    alignSelf: 'stretch',
    height: 46,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 20,
  },
  openDepositModalButtonTwo: {
    backgroundColor: '#3D3D50',
    borderRadius: 14,
    borderWidth: 2,
    borderColor: '#FFF',
    alignSelf: 'stretch',
    height: 46,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 20,
  },
  depositModalButtonBox: {
    marginTop: 22,
    marginBottom: 2,
    alignSelf: 'stretch',
    marginLeft: 10,
    marginRight: 10,
  },
  liquidationInfoBox: {
    marginLeft: 20,
    marginRight: 20,
    marginTop: 24,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#008487',
    borderRadius: 14,
    paddingTop: 14,
    paddingBottom: 14,
    paddingLeft: 14,
    paddingRight: 14,
  },
  liquidationInfoText: {
    marginBottom: 10,
  },
  depositModalButton: {
    backgroundColor: '#05868e',
    borderRadius: 14,
    width: 220,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 10,
  },
  depositModalButtonText: {
    fontSize: 17,
    color: '#000',
    textAlign: 'center',
    letterSpacing: 0.75,
  },
  depositModalButtonTextTwo: {
    fontSize: 17,
    color: '#FFF',
    textAlign: 'center',
    letterSpacing: 0.75,
  },
  depositModalNeeded: {
    color: '#e6e6e6',
    fontSize: 18,
    fontWeight: 'bold',
  },
  depositModalNeededPlain: {
    color: '#e6e6e6',
    fontSize: 20,
  },
  depositModalSeparator: {
    color: '#e6e6e6',
    fontSize: 20,
    marginBottom: 20,
  },
  loanStatusGreyBar: {
    height: 6,
    width,
    marginBottom: 6,
    backgroundColor: '#afafaf',
  },
  loansContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    backgroundColor: '#f3f3f3',
    alignItems: 'center',
    paddingTop: 6,
  },
  infoToggleContainer: {
    alignSelf: 'stretch',
    height: 46,
    flexDirection: 'row',
  },
  infoToggleOption: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoToggleOptionBackground: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoToggleText: {
    color: '#05868e',
    fontSize: 16,
  },
  infoToggleTextActive: {
    color: '#05868e',
    fontSize: 18,
    fontWeight: 'bold',
  },
  infoToggleTextBox: {
    flex: 1,
    alignSelf: 'stretch',
    justifyContent: 'center',
    alignItems: 'center',

    marginLeft: 30,
    marginRight: 30,
  },
  infoToggleTextBoxActive: {
    flex: 1,
    alignSelf: 'stretch',
    justifyContent: 'center',
    alignItems: 'center',

    marginLeft: 30,
    marginRight: 30,

    borderColor: '#eef0f0',
    borderBottomColor: '#05868e',
    borderBottomWidth: 2,
  },
  ltvContainer: {
    flex: 1,
    width: width - 40,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    marginTop: 20,
  },
  ltvListItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'stretch',
    height: 50,
    borderWidth: 0.5,
    borderColor: '#eef0f0',
    paddingLeft: 16,
    paddingRight: 16,
  },
  ltvBottomBorder: {
    borderBottomWidth: 3,
  },
  ltvListItemText: {
    fontSize: 16,
    color: '#e6e6e6',
  },
  ltvListItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ltvListBox: {
    flex: 1,
    alignSelf: 'stretch',
    marginTop: 20,
  },
  currentPricesHeader: {
    paddingLeft: 20,
    paddingTop: 4,
    paddingBottom: 6,
    backgroundColor: '#eef0f0',
  },
  currentPricesText: {
    fontSize: 12,
    color: '#e6e6e6',
  },
  ltvInfoPage: {
    flex: 1,
    width,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    marginTop: 6,
  },
  ltvInfoHeader: {
    height: 100,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  ltvInfoHeaderTitle: {
    marginTop: 10,
    textAlign: 'center',
    color: '#FFF', //'#e6e6e6',
    fontSize: 14,
  },
  ltvInfoHeaderAmount: {
    textAlign: 'center',
    color: '#e6e6e6',
    fontSize: 34,
    fontWeight: '600',
    marginBottom: 24,
  },
  ltvInfoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'stretch',
    height: 50,
    borderWidth: 0.5,
    borderColor: '#eef0f0',
    paddingLeft: 16,
    paddingRight: 16,
    borderLeftWidth: 0,
    borderRightWidth: 0,
  },
  ltvInfoItemText: {
    fontSize: 16,
    color: '#e6e6e6',
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'stretch',
    borderWidth: 0.5,
    borderColor: '#eef0f0',
    paddingLeft: 16,
    paddingRight: 16,
    borderLeftWidth: 0,
    borderRightWidth: 0,
    paddingTop: 4,
    borderBottomWidth: 0,
  },
  paymentItemText: {
    fontSize: 16,
    color: '#e6e6e6',
    marginBottom: 6,
  },
  ltvInfoDisclaimerBox: {
    marginTop: 24,
    marginBottom: 16,
  },
  ltvInfoDisclaimer: {
    fontSize: 9,
    color: '#e6e6e6',
    textAlign: 'center',
  },
  tabBarIcon: {
    height: 25,
    width: 25,
  },
  progressTitle: {
    alignSelf: 'stretch',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 18,
    flexDirection: 'row',
    marginTop: 10,
    zIndex: 10,
  },
  progressTitleText: {
    fontSize: 20,
    color: '#e6e6e6',
  },
  absoluteProgressInfoBox: {
    position: 'absolute',
    height: 254,
    paddingTop: 114,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'flex-start',
    zIndex: 5,
  },
  progressInfoStanding: {
    fontSize: 20,
    color: '#e6e6e6',
  },
  progressInfoPercentage: {
    fontFamily: 'Europa-Bold',
    fontSize: 52,
    height: 64,
    color: '#3a3e42',
  },
  progressInfoResult: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#e6e6e6',
    zIndex: 5,
    width: 230,
    textAlign: 'center',
    marginTop: 16,
  },
  progressInfoDescripton: {
    fontFamily: 'Europa-Bold',
    fontSize: 20,
    color: '#3a3e42',
    marginBottom: 10,
  },
  progressInfoDescriptonPercentage: {
    fontFamily: 'Europa-Regular',
    fontSize: 20,
    color: '#3a3e42',
  },
  progressDisclaimerBox: {
    width: 280,
    marginBottom: 18,
    marginTop: 18,
  },
  progressDisclaimer: {
    fontSize: 9,
    color: '#e6e6e6',
    textAlign: 'center',
  },
  progressHighLowBox: {
    width: 234,
    height: 30,
    paddingTop: 2,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 3,
  },
  progressHighLowText: {
    color: '#3a3e42',
  },
  progressCircleBox: {
    height: 116,
    width: 238,
    zIndex: 20,
  },
  swiperContainer: {
    flex: 1,
    //marginTop: -16,
    alignSelf: 'stretch',
  },
  Swiper: {
    width: '100%',
  },
  progressCircleContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignSelf: 'stretch',
    alignItems: 'center',
    marginBottom: 50,
  },
  progressOverlay: {
    position: 'absolute',
    flexDirection: 'column',
    alignItems: 'center',
    zIndex: 3,
  },
  helpModalBox: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  helpModalSquare: {
    width: '86%',
    backgroundColor: '#3D3D50',
    borderRadius: 8,
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: 30,
    borderWidth: 1,
    borderColor: '#AAA',
  },
  twoFactorBox: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(74, 74, 74, 0.5)',
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 50,
  },
  twoFactorSquare: {
    width: '90%',
    backgroundColor: '#28283D',
    borderRadius: 14,
    borderWidth: 1,
    borderColor: '#777',
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: 30,
    padding: 10,
    paddingTop: 60,
    paddingBottom: 40,
  },
  helpModalX: {
    position: 'absolute',
    top: 4,
    right: 4,
  },
  helpModalTitleBox: {
    marginTop: 24,
    marginBottom: 14,
  },
  helpModalTitle: {
    fontSize: 24,
    color: '#e6e6e6',
  },
  helpModalButton: {
    backgroundColor: '#00ffc3',
    borderRadius: 6,
    width: 160,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 30,
    marginBottom: 30,
  },
  helpModalButtonText: {
    fontSize: 22,
    color: '#000',
  },
  helpModalDescriptionBox: {
    marginLeft: 15,
    marginRight: 15,
  },
  helpModalDescription: {
    color: '#e6e6e6',
    //textAlign: 'center',
    fontSize: 15,
    marginBottom: 6,
  },
  closeModalImg: {
    height: 30,
    width: 30,
    margin: 4,
  },

  //Notifcations Modal
  notificationModalContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#28283D', //#F5FCFF
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 50,
  },
  notificationModalActivatePromptButton: {
    backgroundColor: '#00FFBD',
    width: 250,
    height: 54,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 14,
    marginTop: 20,
  },
  notificationModalActivatePromptText: {
    color: '#000',
    fontSize: 16,
    letterSpacing: 0.75,
  },
  notificationTitle: {
    color: '#FFF',
    fontSize: 21,
    marginBottom: 24,
  },
  notificationDescription: {
    color: '#e6e6e6',
    fontSize: 15,
    marginBottom: 24,
    width: 260,
    textAlign: 'center',
  },
  notificationListItem: {
    color: '#e6e6e6',
    fontSize: 16,
    marginBottom: 12,
    marginLeft: 7,
  },
  notificationQuestion: {
    color: '#e6e6e6',
    fontSize: 15,
    marginTop: 10,
    marginBottom: 18,
  },
  notificationNotNow: {
    color: '#00FFBD',
    fontSize: 16,
    marginBottom: 2,
  },

  // First Time Loading
  firstTimeLoading: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'stretch',
    height: 100,
    width: 100,
  },

  // No Active Loans
  noLoansInnerBox: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  noLoansBox: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'stretch',
  },
  swiperContainernoLoans: {
    flex: 1,
    alignSelf: 'stretch',
    width,
  },
  tagLineText: {
    fontSize: 24,
    color: '#e6e6e6',
    marginTop: 25,
    marginBottom: 15,
    fontWeight: 'bold',
  },
  creditScoreText: {
    fontSize: 16,
    color: '#e6e6e6',
    marginTop: 15,
  },
  loanImage: {
    height: 135,
    width: 150,
  },
  noActiveLoanText: {
    marginTop: 60,
    marginBottom: 26,
    fontSize: 22,
  },

  //Pin Intro
  pinContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 20,
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 100,
  },
  pinTopBox: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  pinInputBox: {
    flexDirection: 'column',
    justifyContent: 'flex-end',
    alignSelf: 'stretch',
    marginBottom: 20,
  },
  pinInputRow: {
    alignSelf: 'stretch',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  pinInputRowLast: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingLeft: 90,
  },
  pinInputButton: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: 80,
    width: 90,
    borderRadius: 14,
  },
  pinInputNum: {
    fontSize: 22,
    color: '#FFF',
    alignSelf: 'center',
    borderRadius: 140,
    height: 26,
    width: 40,
    textAlign: 'center',
    marginBottom: 2,
  },
  pinInputNumDelete: {
    fontSize: 14,
    color: '#FFF',
    alignSelf: 'center',
    borderRadius: 140,
    height: 22,
    width: 80,
    textAlign: 'center',
    paddingTop: 5,
  },
  pinInputDetails: {
    fontSize: 12,
    color: '#FFF',
    alignSelf: 'center',
    borderRadius: 140,
    height: 22,
    width: 40,
    textAlign: 'center',
    opacity: 0.7,
    marginTop: 4,
  },
  pinTitle: {
    fontSize: 20,
    marginTop: 30,
    color: '#FFF',
  },
  pinTitleInsecure: {
    fontSize: 20,
    marginTop: 10,
    color: '#FFF',
  },
  pinIncorrectMatchBox: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  pinIncorrectMatch1: {
    fontSize: 16,
    marginTop: 32,
    color: '#FFF',
  },
  pinIncorrectMatch2: {
    fontSize: 16,
    marginTop: 6,
    color: '#FFF',
  },
  pinDone: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginLeft: 20,
    marginRight: 20,
    marginTop: 20,
    color: '#FFF',
  },
  pinBubbleBox: {
    flexDirection: 'row',
    marginTop: 40,
  },
  pinBubbleActive: {
    height: 20,
    width: 20,
    marginLeft: 8,
    marginRight: 8,
    backgroundColor: '#00ffc3',
    borderRadius: 10,
  },
  pinBubble: {
    height: 20,
    width: 20,
    marginLeft: 8,
    marginRight: 8,
    backgroundColor: '#ffffff30',
    borderRadius: 10,
  },
  saltLogoWhite: {
    height: 30,
    width: 150,
    marginTop: 50,
  },
  pinForgot: {
    color: '#FFF',
    fontSize: 14,
    marginBottom: 0,
  },
  pinBackBox: {
    position: 'absolute',
    bottom: 0,
    alignSelf: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  pinBack: {
    color: '#FFF',
    textAlign: 'center',
  },
  backToSettingsButton: {
    position: 'absolute',
    left: 18,
    top: 72,
    height: 24,
    width: 28,
  },
  backToSettingsImg: {
    height: 24,
    width: 28,
  },

  // Payments
  statusTitlePayment: {
    fontSize: 22,
    marginBottom: 6,
    paddingLeft: 10,
    marginTop: 4,
  },
  autoPayPayment: {
    fontSize: 16,
    marginBottom: 6,
    paddingRight: 10,
    marginTop: 9,
    opacity: 0.7,
  },
  nextPaymentTitle: {
    fontSize: 36,
    paddingLeft: 10,
  },
  nextPaymentDate: {
    paddingLeft: 10,
  },
  oneTimeOptionCard: {
    justifyContent: 'center',
    height: 100,
    borderWidth: 2,
  },
  oneTimeOptionCardBox: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: -20,
  },
  oneTimeOptionImg: {
    height: 40,
    width: 40,
    marginRight: 14,
  },
  oneTimeOptionTitle: {
    fontSize: 18,
    width: 120,
    textAlign: 'center',
  },
  wireTransferText: {
    fontSize: 16,
  },
  wireTransferTitle: {
    fontSize: 17,
    marginBottom: 4,
  },
  wireTransferBox: {
    marginBottom: 16,
  },
  stablecoinTitle: {
    fontSize: 17,
    textAlign: 'center',
    marginBottom: 8,
  },
  stablecoinBox: {
    alignItems: 'center',
  },
  stablecoinText: {
    width: 280,
    textAlign: 'center',
  },
  depositQrBox: {
    overflow: 'hidden',
    backgroundColor: '#FFF',
    padding: 10,
    borderRadius: 14,
  },
  stablecoinImg: {
    height: 40,
    width: 40,
    marginRight: 4,
  },
  loadingDots: {
    height: 40,
    width: 64,
    opacity: 0.6,
    alignSelf: 'center',
    zIndex: 5,
    marginBottom: 8,
    marginTop: 4,
  },
  loadingDotsUnlink: {
    height: 30,
    width: 50,
    opacity: 0.6,
    alignSelf: 'center',
    zIndex: 5,
    marginTop: -8,
    marginLeft: -8,
  },

  //stablecoin desposit
  depositAddressCopyButton: {
    height: 50,
    width: 50,
    backgroundColor: '#05868e',
    alignItems: 'center',
    justifyContent: 'center',
  },
  depositCopyAddress: {
    height: 50,
    width: 260,
    borderRadius: 14,
    backgroundColor: '#FFF',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  changeAmountInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#cbcbcb',
    paddingLeft: 14,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 10,
    marginTop: 16,
  },
  depositOptionCard: {
    justifyContent: 'center',
    alignSelf: 'stretch',
    height: 100,
    borderWidth: 2,
  },
  //WalletsBox
  walletBoxTokens: {
    marginRight: 4,
    height: 50,
    width: 50,
    borderRadius: 14,
    opacity: 0.9,
    borderWidth: 1.5,
    borderColor: '#777',
  },

  //loanStatus
  statusBox: {
    flexDirection: 'row',
    alignSelf: 'stretch',
    justifyContent: 'space-between',
  },
  statusTitle: {
    fontSize: 20,
    marginBottom: 10,
    paddingLeft: 10,
    marginTop: 4,
  },
  statusImg: {
    height: 38,
    width: 38,
    marginRight: 10,
  },
  statusImgWarning: {
    height: 34,
    width: 42,
    marginRight: 10,
  },
  statusImgApple: {
    height: 38,
    width: 32,
    marginRight: 10,
  },
  statusCover: {
    height: 8,
    alignSelf: 'stretch',
    marginTop: -10,
    marginLeft: -10,
    marginRight: -10,
    marginBottom: 10,
  },
  statusMain: {
    paddingLeft: 10,
    marginTop: 6,
    fontSize: 15,
    marginBottom: 10,
  },
  statusButtonBox: {
    marginTop: 12,
    marginBottom: 10,
    alignSelf: 'stretch',
    marginLeft: 10,
    marginRight: 10,
  },
  statusCure: {
    paddingLeft: 10,
    fontSize: 16,
    color: '#00FFBD',
    marginBottom: 16,
  },
  statusDate: {
    paddingLeft: 10,
    fontSize: 15,
    marginBottom: 2,
    textDecorationLine: 'underline',
    textDecorationStyle: 'solid',
    textDecorationColor: '#00ffc3',
  },
  statusPrevent: {
    paddingLeft: 10,
    fontSize: 16,
    color: '#00FFBD',
    marginBottom: 16,
    marginTop: 6,
  },
  allAccountsStatusImg: {
    height: 20,
    width: 20,
    marginRight: 14,
  },
  allAccountsWarningImg: {
    height: 20,
    width: 24,
    marginRight: 12,
    marginLeft: -2,
  },
  allAccountsBox: {
    alignSelf: 'stretch',
    marginLeft: 42,
    marginRight: 42,
    marginTop: 10,
    marginBottom: 10,
  },
  allAccountsAddButton: {
    marginTop: 10,
    alignSelf: 'stretch',
    marginLeft: 16,
    marginRight: 16,
  },
  successModalX: {
    position: 'absolute',
    top: 22,
    right: 10,
    height: 30,
    width: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeModalImg: {
    height: 30,
    width: 30,
    margin: 4,
  },
  removeBox: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(74, 74, 74, 0.5)',
  },
  removeInnerBox: {
    width: '86%',
    backgroundColor: '#28283D',
    borderRadius: 8,
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: 30,
    padding: 20,
  },
  removeRow: {
    flexDirection: 'row',
    alignSelf: 'stretch',
    justifyContent: 'flex-end',
  },
  removeTextDisclaimer: {
    fontSize: 18,
    padding: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  removeButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignSelf: 'stretch',
    marginBottom: 8,
  },
  removeButtonText: {
    height: 40,
    width: 106,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: '#00FFBD',
    borderWidth: 2,
  },
  keepButtonText: {
    height: 40,
    width: 106,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#00FFBD',
  },
  banksBoxTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignSelf: 'stretch',
    alignItems: 'center',
  },
})

export default styles
