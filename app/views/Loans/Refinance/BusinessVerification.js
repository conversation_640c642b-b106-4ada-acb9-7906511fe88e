import React, {useEffect, useState} from 'react'
import {View, Image, Dimensions, TouchableOpacity, ScrollView, SafeAreaView} from 'react-native'
import {connect, useDispatch} from 'react-redux'
import * as ImagePicker from 'react-native-image-picker'

import {askingForPermissions} from '../../../store/auth/auth.actions'
import removeFileImg from '../../../imgs/closeX.png'
import {TextReg, Button, TextBold} from '../../../components'
import {useNavigation} from '@react-navigation/native'
import {increaseRefreshDataCount} from '../../../store/user/user.actions'
import {navigateRefinanceLoan} from './helpers'

const {width: screenWidth} = Dimensions.get('window')

const BusinessVerification = props => {
  const [loading, setLoading] = useState(false)
  const navigation = useNavigation()
  const dispatch = useDispatch()

  const currentAccount = props?.currentAccount
  const entityProfile = currentAccount?.entityProfile

  const businessDocs = entityProfile?.documents

  const [documents, setDocuments] = useState(businessDocs)
  const [refreshingUpload, setRefreshingUpload] = useState(false)
  const [imageError, setImageError] = useState(false)

  const next = async () => {
    setLoading(true)
    await dispatch(increaseRefreshDataCount())
    await props?.WebService.getSaltAccount()
      .then(async res => {
        const entityProfile = res?.data?.find(a => a?.ref === currentAccount?.ref)?.entityProfile
        const selectedAccount = {...currentAccount, entityProfile}
        await props?.WebService.getPendingRefinanceLoanRef(selectedAccount?.ref)
          .then(async res => {
            setLoading(false)
            navigateRefinanceLoan(selectedAccount, props?.user, navigation, res?.data)
          })
          .catch(err => {
            setLoading(false)
            console.log('err', err)
          })
      })
      .catch(err => {
        setLoading(false)
        console.log('err', err)
      })
  }

  const validateImage = (type, size) => {
    const fileTypes = [
      'application/pdf',
      'image/jpg',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/tif',
      'image/tiff',
    ]
    if (!fileTypes.includes(type)) {
      return 'type'
    }
    if (size / 1000000 > 50) {
      return 'size'
    }
    return true
  }

  const openImageSelect = () => {
    dispatch(askingForPermissions(true))
    const options = {
      title: 'Select Document',
      maxWidth: 2200,
      maxHeight: 2200,
      quality: 1.0,
      storageOptions: {
        skipBackup: true,
        path: 'images',
        cameraRoll: true,
        waitUntilSaved: true,
      },
      //takePhotoButtonTitle: null,
      noData: false,
      mediaType: 'photo',
    }

    ImagePicker.launchImageLibrary(options, async response => {
      if (response.didCancel) {
        return
      } else if (response.error) {
        setRefreshingUpload(false)
        return
      } else if (response.customButton) {
        //console.log('User tapped custom button: ', response.customButton)
      } else {
        response = response.assets[0]
        const photo = {uri: response.uri}
        const formdata = new FormData()

        const validImage = validateImage(response.type, response.fileSize)
        if (validImage === 'type') {
          setRefreshingUpload(false)
          setImageError('type')
          return
        } else if (validImage === 'size') {
          setRefreshingUpload(false)
          setImageError('size')
          return
        }

        let path = response.uri
        if (Platform.OS === 'ios') {
          path = '~' + path.substring(path.indexOf('/Documents'))
        }
        if (!response.fileName) response.fileName = path.split('/').pop()

        formdata.append('file', [
          {
            uri: Platform.OS === 'android' ? photo.uri : photo.uri.replace('file://', ''),
            name: response.fileName || 'testName.jpg',
            type: response.type,
            size: response.fileSize,
          },
        ])
        formdata.append('name', response.fileName)
        formdata.append('key', 'true')

        //https://borrower-portal.stage.saltlending.tech/api/v0/documents/0be4cfb6-fa28-44b1-92a6-d065e796233f/address/other_proof_of_address?ref=2

        const docType = 'verification_of_business'
        const referenceType = 'business_profile'
        const referenceId = currentAccount?.entityProfile?.id

        setRefreshingUpload(true)

        await props?.WebService.uploadDocument(
          referenceId,
          referenceType,
          docType,
          photo.uri.replace('file://', ''),
          response.fileName,
        )
          .then(res => {
            const parsedData = JSON.parse(res.data)

            setRefreshingUpload(false)
            setDocuments([
              ...documents,
              {
                name: response.fileName,
                id: parsedData.id,
                type: docType,
              },
            ])

            //refresh user data - so docs show up on back and forth
            //getAccounts()
          })
          .catch(err => {
            console.log('upload doc err', err)
            setRefreshingUpload(false)
          })
      }
    })
  }

  const removeDocument = async id => {
    await props?.WebService.removeDocument(id)
      .then(async res => {
        let newDocs = documents?.filter(a => a.id != id)
        if (!newDocs) {
          newDocs = []
        }
        await dispatch(increaseRefreshDataCount())
        setDocuments(newDocs)
      })
      .catch(err => {
        console.log('documents err', err)
      })
  }

  let filteredBusinessDocs = documents?.filter(b => !b?.rejectedAt) || []
  let businessDocsRejected = false
  if (documents?.length > 0 && filteredBusinessDocs?.length < 1) {
    businessDocsRejected = true
  }

  const showJurisDocs = documents?.map((a, k) => {
    return (
      <View
        style={{
          alignSelf: 'stretch',
          height: 40,
          alignItems: 'center',
          flexDirection: 'row',
          justifyContent: 'space-between',
          marginTop: 10,
        }}
        key={k}>
        <View
          style={{
            alignSelf: 'stretch',
            height: 40,
            backgroundColor: '#FFF',
            borderRadius: 14,
            alignItems: 'center',
            paddingLeft: 4,
            justifyContent: 'center',
            paddingRight: 4,
            width: !a?.rejectedAt ? '90%' : '100%',
          }}>
          <TextReg style={{color: '#000', maxWidth: 230}}>{a.name}</TextReg>
        </View>
        {!a?.rejectedAt && (
          <TouchableOpacity onPress={() => removeDocument(a.id)}>
            <Image
              source={removeFileImg}
              style={{
                height: 30,
                width: 30,
              }}
            />
          </TouchableOpacity>
        )}
      </View>
    )
  })

  return (
    <SafeAreaView style={local.box}>
      <View style={{flex: 1}}>
        <View
          style={{
            flexDirection: 'row',
            alignSelf: 'center',
            justifyContent: 'space-between',
            marginTop: 5,
            marginBottom: 5,
            width: screenWidth - 40,
          }}>
          <TouchableOpacity onPress={() => navigation?.goBack()} style={{height: 40, width: 40}}>
            <Image
              source={require('../../../imgs/backToSettings.png')}
              style={{height: 25, width: 20}}
            />
          </TouchableOpacity>
          <TextReg style={{color: '#FFF', fontSize: 18}}>Refinance</TextReg>
          <View style={{height: 40, width: 40}} />
        </View>
        <ScrollView
          contentContainerStyle={{justifyContent: 'space-between'}}
          style={{
            alignSelf: 'stretch',
            paddingHorizontal: 20,
            flex: 1,
          }}>
          <View>
            <TextReg style={{fontSize: 18, marginTop: 60, marginBottom: 32}}>
              Business Verification
            </TextReg>

            <TextBold
              style={{
                marginBottom: 24,
                fontSize: 16,
              }}>
              Provide ONE of the following:
            </TextBold>
            <View style={{opacity: 1, alignSelf: 'stretch', marginLeft: 28}}>
              <TextReg style={{marginBottom: 14, fontSize: 16}}>
                • Corporation Certification
              </TextReg>
              <TextReg style={{marginBottom: 14, fontSize: 16}}>
                • Articles of Incorporation
              </TextReg>
              <TextReg style={{marginBottom: 14, fontSize: 16}}>• Registration</TextReg>
            </View>

            <TextReg style={{fontStyle: 'italic', fontSize: 14, marginTop: 18, marginBottom: 32}}>
              Address information on documents must match the information provided.
            </TextReg>
            <Button
              disabled={showJurisDocs.length > 8}
              isLoading={refreshingUpload}
              style={{
                alignSelf: 'stretch',
                backgroundColor: '#28283D',
                borderColor: '#00FFBD',
                borderWidth: 2,
                borderRadius: 4,
              }}
              theme={'secondary'}
              onPress={() => openImageSelect()}>
              <TextBold style={{color: '#00FFBD', fontSize: 18, letterSpacing: 2.16}}>
                UPLOAD DOCUMENT
              </TextBold>
            </Button>
            {showJurisDocs}
          </View>
          {businessDocsRejected && (
            <View>
              <TextReg style={{color: '#e5705a', fontSize: 18, padding: 10}}>
                {'The previous document was rejected by our team. Please upload new document.'}
              </TextReg>
            </View>
          )}
          <View
            style={{
              alignSelf: 'center',
              marginTop: 40,
              width: screenWidth - 40,
            }}>
            <Button
              style={{alignSelf: 'stretch', height: 60}}
              disabled={documents?.length < 1 || businessDocsRejected}
              isLoading={loading}
              onPress={() => next()}>
              <TextBold
                style={{
                  fontSize: 18,
                  letterSpacing: 2.16,
                  color: '#000',
                  alignSelf: 'stretch',
                }}>
                CONTINUE
              </TextBold>
            </Button>
            <Button
              style={{
                alignSelf: 'stretch',
                backgroundColor: 'transparent',
                height: 60,
                marginBottom: 20,
              }}
              isLoading={false}
              onPress={() => navigation.goBack()}>
              <TextBold style={{color: '#00FFBD', fontSize: 18, letterSpacing: 2.16}}>
                BACK
              </TextBold>
            </Button>
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  )
}

const mapStateToProps = (state, props) => {
  const user = state?.user?.user
  const currentAccount = user?.accounts?.find(a => a.ref == state.auth.account.ref) || {}
  const forRefi = props?.route?.params?.forRefi
  const loanData = forRefi ? state.user.loanData.refinanceLoan || {} : state.user.loanData || {}
  return {
    loanData,
    tokenPrices: state.user.prices,
    WebService: state.auth.WebService,
    user,
    currentAccount,
  }
}

export default connect(mapStateToProps)(BusinessVerification)

let local = {
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
  actionTitleTxt: {
    marginTop: 20,
    fontSize: 18,
    marginBottom: 32,
  },
  descriptionBox: {
    borderWidth: 1,
    borderColor: '#FFF',
    borderRadius: 8,
    alignSelf: 'stretch',
    backgroundColor: '#535364',
    marginTop: 12,
    paddingVertical: 12,
    paddingHorizontal: 12,
  },
  buttonContainer: {
    justifyContent: 'flex-end',
    alignItems: 'stretch',
    alignSelf: 'stretch',
    paddingLeft: 30,
    paddingRight: 30,
    paddingBottom: 20,
    gap: 12,
  },
  continueButton: {
    width: screenWidth - 40,
    backgroundColor: '#00FFBD',
    height: 60,
    borderRadius: 15,
  },
  backButton: {
    width: screenWidth - 40,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  backButtonText: {color: '#00FFBD', fontSize: 18, letterSpacing: 2.16},
  handImage: {height: 60, width: 60},

  card: {
    backgroundColor: '#3D3D50',
    width: screenWidth - 40,
    alignSelf: 'center',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    marginBottom: 12,
  },

  loanRequestSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    borderColor: '#818181',
    gap: 12,
  },
  loanRequestSummaryText: {
    fontSize: 19,
    color: '#FFF',
    flexWrap: 'wrap',
  },
}
