import React from 'react'
import {View, SafeAreaView, TouchableOpacity, Image, Dimensions} from 'react-native'
import {useNavigation} from '@react-navigation/native'

import styles from './styles'
import docMagnify from '../../../imgs/unit21/docMagnify.png'
import commonStyles from '../../../styles/commonStyles'
import RefinanceApprovalCard from './components/RefinanceApprovalCard'
import {TextReg} from '../../../components'

const {width: screenWidth} = Dimensions.get('window')

const RefinanceUnderReview = () => {
  const navigation = useNavigation()
  return (
    <SafeAreaView style={styles.box}>
      <View
        style={{
          flexDirection: 'row',
          alignSelf: 'stretch',
          justifyContent: 'space-between',
          marginTop: 5,
          marginBottom: 5,
          width: screenWidth - 40,
          alignSelf: 'center',
        }}>
        <TouchableOpacity
          onPress={() => navigation?.navigate('Home')}
          style={{height: 40, width: 40}}>
          <Image
            source={require('../../../imgs/backToSettings.png')}
            style={{height: 25, width: 20}}
          />
        </TouchableOpacity>
        <TextReg style={{color: '#FFF', fontSize: 18}}>Refinance</TextReg>
        <View style={{height: 40, width: 40}} />
      </View>
      <View style={{...commonStyles.tileContainer, marginBottom: 30}}>
        <RefinanceApprovalCard
          icon={docMagnify}
          title="Your Refinance Request is under review."
          subject="Refinance Request Inquiry."
          description="We will get back to you shortly within no more than 3-5 business days. Once approved, you will receive an email with your refinance documents to review and sign."
        />
      </View>
    </SafeAreaView>
  )
}

export default RefinanceUnderReview
