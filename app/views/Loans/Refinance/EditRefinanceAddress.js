import React, {useEffect, useState} from 'react'
import {
  StyleSheet,
  View,
  TextInput,
  Keyboard,
  SafeAreaView,
  TouchableOpacity,
  Image,
  Dimensions,
  PixelRatio,
  ScrollView,
  Linking,
} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import {useFocusEffect} from '@react-navigation/native'
import iso3166 from 'iso-3166-2'
import _ from 'lodash'

import {TextReg, TextBold, Button, LocationSelect} from '../../../components'
import countryCodes from '../../../util/countryCodes'

const {width: screenWidth} = Dimensions.get('window')

const EditRefinanceAddress = ({navigation, route}) => {
  const WebService = useSelector(state => state.auth.WebService || {})
  const user = useSelector(state => state.user.user)

  const accountId = route?.params?.accountId
  const selectedAccount =
    user?.accounts?.find(
      a =>
        a.productType === 'loan' &&
        a.loans?.length > 0 &&
        a.loans[0]?.status === 'active' &&
        a.accountId === accountId,
    ) || null

  const selectedAddress =
    selectedAccount?.type === 'personal' ? user?.address : selectedAccount?.entityProfile?.address

  //jurisdiction
  const [country, setCountry] = useState(null)
  const [countryCode, setCountryCode] = useState(null)
  const [region, setRegion] = useState(null)
  const [regionCode, setRegionCode] = useState(null)
  const [pickCodes, setPickCodes] = useState([])
  const [pickRegion, setPickRegion] = useState([])
  const [lendableArea, setLendableArea] = useState(false)
  const [loadingAreas, setLoadingAreas] = useState(true)
  const [info, setInfo] = useState(null)

  const inputs = []

  const [address1, setAddress1] = useState(selectedAddress?.street1 || '')
  const [address2, setAddress2] = useState(selectedAddress?.street2 || '')
  const [postal, setPostal] = useState(selectedAddress?.postalCode || '')
  const [city, setCity] = useState(selectedAddress?.city || '')

  useFocusEffect(
    React.useCallback(() => {
      getA()
      getLendableAreas(
        selectedAddress?.countryCode?.toLowerCase(),
        selectedAddress?.province?.toLowerCase(),
      )
    }, []),
  )

  const getLoanFee = async () => {
    const {data: Fee} = await WebService.getAdminFee()
    return Fee.originationFee
  }

  const getBaseRates = async () => {
    await WebService.getLoanRatesMaps().then(async res => {
      const baseLTV = Object.keys(Object.values(res.data.baseRates)[0]).sort((a, b) => b - a)[0]
      const fee = await getLoanFee()

      const payload = {
        interestRate: selectedAccount?.loans[0]?.interestRate.toString(),
        interestOnly: selectedAccount?.loans[0]?.interestOnly,
        originationFeeRate: fee,
        term: selectedAccount?.loans[0]?.term,
        saltRedeemed: selectedAccount?.loans[0]?.saltRedeemed,
        fundingDate: new Date().toString(),
        ltv: baseLTV,
      }

      await WebService.getRefinanceInfo(selectedAccount?.loans[0]?.id, payload).then(res =>
        setInfo(res.data),
      )
    })
  }

  useEffect(() => {
    getBaseRates()
  }, [])
  const getA = async () => {
    setAddress1(selectedAddress?.street1 || '')
    setAddress2(selectedAddress?.street2 || '')
    setPostal(selectedAddress?.postalCode || '')
    setCity(selectedAddress?.city || '')
    locationSetup(selectedAddress)
  }

  const locationSetup = async (address = selectedAddress) => {
    let pickableCountryCodes = countryCodes().map(a => a.name)
    pickableCountryCodes = ['United States'].concat(pickableCountryCodes)
    setPickCodes(pickableCountryCodes)
    const countryCode = address?.countryCode || ''
    const regionCode = address?.province || ''

    setCountryCode(countryCode)
    setRegionCode(regionCode)
    setRegion(regionCode)

    const country = countryCodes().filter(b => b.code == countryCode)[0]?.name || ''
    setCountry(country)

    if (countryCode != '') {
      const isoCountry = iso3166.country(countryCode)
      let pickableCountrySubs = []
      const subArr = Object.values(isoCountry.sub)
      const subData = iso3166.subdivision(countryCode, regionCode)
      const region = subData?.name
      setRegion(region)
      pickableCountrySubs = subArr.map(b => b.name)
      pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
        if (a < b) return -1
        if (b < a) return 1
        return 0
      })
      setPickRegion(pickableCountrySubs)
    }
  }

  const getLendableAreas = async (country, province) => {
    console.log('country: ', country, province)
    await WebService.getLendableAreas()
      .then(res => {
        if (country != 'us') {
          province = 'default'
        }
        console.log('res: ', res.data)
        let lendableRes = res.data[country]?.[province] || 'non_lendable'

        const accountType = selectedAccount?.type

        if (accountType == 'personal') {
          if (lendableRes == 'lendable') {
            setLendableArea(true)
          } else {
            setLendableArea(false)
          }
        } else if (accountType == 'business') {
          if (lendableRes == 'lendable' || lendableRes == 'business_only') {
            setLendableArea(true)
          } else {
            setLendableArea(false)
          }
        }
        setLoadingAreas(false)
      })
      .catch(err => {
        setLoadingAreas(false)

        console.log('getLendableAreas err', country, err)
      })
  }

  const onSelect = selectedCountry => {
    const countryCode = countryCodes().filter(a => a.name == selectedCountry)[0].code
    const isoCountry = iso3166.country(countryCode)
    let pickableCountrySubs = []
    const subArr = Object.values(isoCountry.sub)
    pickableCountrySubs = subArr.map(a => a.name)
    pickableCountrySubs = pickableCountrySubs.sort((a, b) => {
      if (a < b) return -1
      if (b < a) return 1
      return 0
    })
    setCountryCode(countryCode)
    setCountry(selectedCountry)
    setPickRegion(pickableCountrySubs)
    setRegion(null)
    setRegionCode(null)
    getLendableAreas(selectedCountry.toLowerCase(), null)
  }

  const onRegionSelect = selectedProvince => {
    const countryCode = countryCodes().filter(a => a.name == country)[0]?.code
    const subData = iso3166.subdivision(countryCode, selectedProvince)
    const regionCode = subData?.regionCode
    const region = subData?.name

    setRegion(region)
    setRegionCode(regionCode)
    getLendableAreas(countryCode.toLowerCase(), regionCode.toLowerCase())
  }

  const next = async () => {
    const addressId = selectedAddress?.id
    const previousAddress = {
      countryCode: selectedAddress?.countryCode,
      province: selectedAddress?.province,
      street1: selectedAddress?.street1,
      street2: selectedAddress?.street2,
      postalCode: selectedAddress?.postalCode,
      city: selectedAddress?.city,
    }
    const payload = {
      countryCode,
      province: regionCode,
      street1: address1,
      street2: address2,
      postalCode: postal,
      city,
    }

    const updatedAddress = {...payload, id: addressId}
    navigation.navigate('RefinanceType', {
      ...route?.params,
      info,
      address: updatedAddress,
      isAddressChanged: !_.isEqual(previousAddress, payload),
    })
  }

  const goBack = () => {
    Keyboard.dismiss()
    navigation.goBack()
  }
  console.log('lendableArea: ', lendableArea)
  return (
    <SafeAreaView style={local.box}>
      <View style={{flex: 1}}>
        <View
          style={{
            flexDirection: 'row',
            alignSelf: 'center',
            justifyContent: 'space-between',
            marginTop: 5,
            marginBottom: 5,
            width: screenWidth - 40,
          }}>
          <TouchableOpacity onPress={() => navigation?.goBack()} style={{height: 40, width: 40}}>
            <Image
              source={require('../../../imgs/backToSettings.png')}
              style={{height: 25, width: 20}}
            />
          </TouchableOpacity>
          <TextReg style={{color: '#FFF', fontSize: 18}}>Refinance</TextReg>
          <View style={{height: 40, width: 40}} />
        </View>
        <ScrollView
          style={{flex: 1, alignSelf: 'stretch', paddingHorizontal: 20}}
          enableResetScrollToCoords={true}>
          <View style={{marginTop: 40}}>
            <TextReg style={{fontSize: 18, marginBottom: 32}}>Confirm Address</TextReg>
            <TextReg style={{marginBottom: 4, marginTop: 5}}>Address Line 1</TextReg>
            <TextInput
              style={local.unit21InfoInput}
              onChangeText={text => setAddress1(text)}
              value={address1}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              placeholder={'Enter Address Line 1'}
              placeholderTextColor={'#AFAFAF'}
              onSubmitEditing={() => inputs.address2.focus()}
              keyboardAppearance="dark"
            />
            <TextReg style={{marginBottom: 4}}>Address Line 2</TextReg>
            <TextInput
              style={local.unit21InfoInput}
              onChangeText={text => setAddress2(text)}
              ref={input => (inputs.address2 = input)}
              value={address2}
              underlineColorAndroid="transparent"
              blurOnSubmit
              returnKeyType={'next'}
              placeholder={'Enter Address Line 2'}
              placeholderTextColor={'#AFAFAF'}
              onSubmitEditing={() => Keyboard.dismiss()}
              keyboardAppearance="dark"
            />
            <TextReg style={{marginBottom: 5}}>Country</TextReg>
            <LocationSelect
              options={pickCodes}
              onSelect={onSelect}
              placeholder={country ? country : 'Country'}
              style={local.unit21InfoInput}
            />
            <View style={{marginBottom: 32}}>
              <TextReg style={{marginBottom: 5, marginTop: -4}}>State / Province</TextReg>
              <LocationSelect
                options={pickRegion}
                onSelect={onRegionSelect}
                placeholder={region ? region : 'State / Province'}
                style={{
                  ...local.unit21InfoInput,
                  marginBottom: 0,
                  borderColor: !lendableArea && !loadingAreas ? '#E5705A' : '#cbcbcb',
                }}
              />
              {!lendableArea && !loadingAreas && (
                <View>
                  <TextReg style={{marginTop: 4, color: '#E5705A', fontSize: 16}}>
                    Location is unavailable to refinance
                  </TextReg>
                  <View
                    style={{
                      backgroundColor: '#E5705A40',
                      borderWidth: 2,
                      borderColor: '#E5705A',
                      paddingVertical: 10,
                      paddingLeft: 20,
                      paddingRight: 30,
                      paddingHorizontal: 10,
                      borderRadius: 8,
                      marginTop: 32,
                    }}>
                    <TextReg style={{fontSize: 16}}>
                      Unfortunately, we currently can’t offer refinance in the selected
                      jurisdiction.
                    </TextReg>
                    <TouchableOpacity
                      onPress={() =>
                        Linking.openURL('https://saltlending.com/map-list/').catch(err =>
                          console.error('An error occurred', err),
                        )
                      }>
                      <TextBold style={{color: '#00FFBD', fontSize: 16}}>
                        Check available jurisdictions.
                      </TextBold>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => navigation.navigate('Home')}>
                      <TextReg style={{fontSize: 18, color: '#E5705A', marginTop: 8}}>
                        Cancel Request
                      </TextReg>
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            </View>
            <View style={{marginTop: -4}}>
              <TextReg style={{marginBottom: 4}}>City</TextReg>
              <TextInput
                style={local.unit21InfoInput}
                onChangeText={text => setCity(text)}
                ref={input => (inputs.city = input)}
                value={city}
                underlineColorAndroid="transparent"
                blurOnSubmit
                returnKeyType={'next'}
                placeholder={'City'}
                placeholderTextColor={'#AFAFAF'}
                onSubmitEditing={() => inputs.postal.focus()}
                keyboardAppearance="dark"
              />

              <TextReg style={{marginBottom: 4}}>Zip Code</TextReg>
              <TextInput
                style={local.unit21InfoInput}
                onChangeText={text => setPostal(text)}
                ref={input => (inputs.postal = input)}
                value={postal}
                underlineColorAndroid="transparent"
                blurOnSubmit
                returnKeyType={'next'}
                placeholder={'Zip Code'}
                placeholderTextColor={'#AFAFAF'}
                onSubmitEditing={() => Keyboard.dismiss()}
                keyboardAppearance="dark"
              />
            </View>
          </View>
        </ScrollView>
        <View style={{paddingBottom: 20, marginTop: 10}}>
          <Button
            isLoading={false}
            disabled={
              !postal || !city || !region || !country || !address1 || !lendableArea || !info
            }
            style={local.continueButton}
            onPress={() => next()}
            theme={'secondary'}>
            <TextBold
              style={{color: !country ? '#FFF' : '#000', letterSpacing: 2.16, fontSize: 18}}>
              CONTINUE
            </TextBold>
          </Button>
          <Button
            isLoading={false}
            style={local.backButton}
            onPress={() => goBack()}
            disabled={false}>
            <TextBold style={local.backButtonText}>BACK</TextBold>
          </Button>
        </View>
      </View>
    </SafeAreaView>
  )
}

export default EditRefinanceAddress

const local = StyleSheet.create({
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
  unit21InfoInput: {
    height: 57,
    borderWidth: 1,
    borderRadius: 15,
    borderColor: '#EFEFEF',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#FFFFFF',
    backgroundColor: '#3D3D50',
    fontSize: 16 / PixelRatio.getFontScale(),
    marginBottom: 32,
    minHeight: 40,
  },
  continueButton: {
    width: screenWidth - 40,
    backgroundColor: '#00FFBD',
    height: 60,
    borderRadius: 15,
  },
  backButton: {
    width: screenWidth - 40,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  backButtonText: {color: '#00FFBD', fontSize: 18, letterSpacing: 2.16},
})
