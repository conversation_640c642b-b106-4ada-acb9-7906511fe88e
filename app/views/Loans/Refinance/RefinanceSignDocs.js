import React from 'react'
import {View, SafeAreaView, Dimensions, TouchableOpacity, Image} from 'react-native'
import {useNavigation} from '@react-navigation/native'

import styles from './styles'
import docMagnify from '../../../imgs/openLetter.png'
import commonStyles from '../../../styles/commonStyles'
import RefinanceApprovalCard from './components/RefinanceApprovalCard'
import {TextReg} from '../../../components'

const {width: screenWidth} = Dimensions.get('window')

const RefinanceSignDocs = () => {
  const navigation = useNavigation()

  return (
    <SafeAreaView style={styles.box}>
      <View
        style={{
          flexDirection: 'row',
          alignSelf: 'stretch',
          justifyContent: 'space-between',
          marginTop: 5,
          marginBottom: 5,
          width: screenWidth - 40,
          alignSelf: 'center',
        }}>
        <TouchableOpacity
          onPress={() => navigation?.navigate('Home')}
          style={{height: 40, width: 40}}>
          <Image
            source={require('../../../imgs/backToSettings.png')}
            style={{height: 25, width: 20}}
          />
        </TouchableOpacity>
        <TextReg style={{color: '#FFF', fontSize: 18}}>Refinance</TextReg>
        <View style={{height: 40, width: 40}} />
      </View>
      <View style={{...commonStyles.tileContainer, marginBottom: 30}}>
        <RefinanceApprovalCard
          icon={docMagnify}
          title="Please sign your LEND Account documents."
          subject="LEND Document Inquiry."
          description="SALT has completed your LEND Account review, and you have been emailed your LEND documents. Once you have completed and returned the documents back to SALT, we will review, and you will then be notified via email of your status."
        />
      </View>
    </SafeAreaView>
  )
}

export default RefinanceSignDocs
