import React, {useEffect, useState} from 'react'
import {
  View,
  Image,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  ActivityIndicator,
  TextInput,
  PixelRatio,
  ScrollView,
} from 'react-native'
import {useSelector} from 'react-redux'
import DropDownPicker from 'react-native-dropdown-picker'
import {useNavigation} from '@react-navigation/native'

import {TextBold, TextReg, Button} from '../../../components'
import {formatCurrency, numberWithCommas} from '../../../util/helpers'
import BigNumber from 'bignumber.js'

const {width: screenWidth} = Dimensions.get('window')

const AdditionalAmount = ({route}) => {
  const navigation = useNavigation()
  const [additionalAmount, setAdditionalAmount] = useState('')

  const user = useSelector(state => state.user.user)

  const info = route?.params?.info
  const allActiveAccountsWithLoans =
    user?.accounts?.filter(a => a.loans?.length > 0 && a.loans[0]?.status === 'active') || null

  const next = async () => {
    navigation.navigate('TermsAndApr', {...route.params, newAmount: additionalAmount})
  }

  const updateLoanAmount = (text = additionalAmount) => {
    var letterRegExp = /[a-zA-Z]/g
    if (letterRegExp.test(text)) {
      return
    }
    let parsedText = text.replace('$', '')
    parsedText = parsedText.split(' ').join('')
    parsedText = parsedText.split(',').join('')

    if (parsedText == '.') {
      return
    }
    if (+parsedText >= 0) {
      setAdditionalAmount(parsedText)
    }
  }

  return (
    <SafeAreaView style={local.box}>
      {!allActiveAccountsWithLoans ? (
        <ActivityIndicator size="large" color="#fff" style={{marginTop: 40, marginBottom: 10}} />
      ) : (
        <React.Fragment>
          <View
            style={{
              flexDirection: 'row',
              alignSelf: 'center',
              justifyContent: 'space-between',
              marginTop: 5,
              marginBottom: 5,
              width: screenWidth - 40,
            }}>
            <TouchableOpacity onPress={() => navigation?.goBack()} style={{height: 40, width: 40}}>
              <Image
                source={require('../../../imgs/backToSettings.png')}
                style={{height: 25, width: 20}}
              />
            </TouchableOpacity>
            <TextReg style={{color: '#FFF', fontSize: 18}}>Refinance</TextReg>
            <View style={{height: 40, width: 40}} />
          </View>
          <ScrollView
            style={{
              backgroundColor: '#28283D',
              alignSelf: 'stretch',
            }}
            contentContainerStyle={{alignItems: 'center'}}>
            <View style={local.descriptionBox}>
              <View style={local.titleContainer}>
                <Image source={require('../../../imgs/piggybank.png')} style={local.handImage} />
              </View>
              <TextReg style={local.actionTitleTxt}>How much more money do you want?</TextReg>
              <TextReg style={{marginBottom: 8}}>Enter Additional Amount Only</TextReg>
              <TextInput
                style={local.additionalAmountInput}
                onChangeText={text => updateLoanAmount(text)}
                value={`$ ${numberWithCommas(additionalAmount)}`}
                underlineColorAndroid="transparent"
                blurOnSubmit
                returnKeyType={'next'}
                placeholder={'$'}
                keyboardType={'numeric'}
                keyboardAppearance="dark"
              />
              <TextReg
                style={{
                  flexWrap: 'wrap',
                }}>
                Max Available: $
                {info?.maximumCashInHand < 0 ? 0 : formatCurrency(info?.maximumCashInHand)} USD
              </TextReg>
            </View>
            <TextReg
              style={{
                width: screenWidth - 100,
                flexWrap: 'wrap',
                alignSelf: 'center',
                marginTop: 20,
              }}>
              NOTE: Max Available is determined by {new BigNumber(info?.ltv).times(100).toNumber()}%
              LTV with your current collateral holdings. Increasing or surpassing this amount might
              affect the LTV and/or rate and necessitate the deposit of additional collateral.
            </TextReg>

            <View style={local.buttonContainer}>
              {info?.maximumCashInHand < +additionalAmount && (
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'flex-start',
                    backgroundColor: '#1E5C5C',
                    borderWidth: 1,
                    borderColor: '#00FFBD',
                    padding: 12,
                    borderRadius: 12,
                    width: screenWidth - 44,
                    gap: 10,
                  }}>
                  <Image
                    source={require('../../../imgs/graphics/info.png')}
                    style={{width: 22, height: 22}}
                  />

                  <TextReg style={{fontSize: 14, width: '95%'}}>
                    To borrow this amount, you’ll need to deposit additional collateral.
                  </TextReg>
                </View>
              )}
              <Button
                isLoading={false}
                disabled={!additionalAmount || !info}
                onPress={() => next()}
                style={local.continueButton}>
                <TextBold
                  style={{
                    color: !additionalAmount || !info ? '#FFF' : '#000',
                    letterSpacing: 2.16,
                  }}>
                  CONTINUE
                </TextBold>
              </Button>
              <Button
                isLoading={false}
                onPress={() => navigation?.goBack()}
                style={local.backButton}>
                <TextBold style={local.backButtonText}>BACK</TextBold>
              </Button>
            </View>
          </ScrollView>
        </React.Fragment>
      )}
    </SafeAreaView>
  )
}

export default AdditionalAmount

let local = {
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
  titleContainer: {
    width: 140,
    height: 140,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#535364',
    borderRadius: 70,
    alignSelf: 'center',
  },
  actionTitleTxt: {
    marginTop: 30,
    fontSize: 18,
    marginBottom: 32,
  },
  descriptionBox: {
    width: screenWidth - 40,
  },
  buttonContainer: {
    justifyContent: 'flex-end',
    alignItems: 'center',
    alignSelf: 'stretch',
    paddingLeft: 30,
    paddingRight: 30,
    paddingBottom: 20,
    marginTop: 30,
  },
  continueButton: {
    width: screenWidth - 40,
    backgroundColor: '#00FFBD',
    height: 60,
    borderRadius: 15,
    marginTop: 20,
    marginBottom: 12,
  },
  backButton: {
    width: screenWidth - 40,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  backButtonText: {color: '#00FFBD', fontSize: 18, letterSpacing: 2.16},
  handImage: {height: 81, width: 81},
  card: {
    backgroundColor: '#535364',
    borderWidth: 2,
    flexDirection: 'row',
    alignSelf: 'stretch',
    alignItems: 'center',
    gap: 22,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  drop: {
    height: 54,
    borderWidth: 1,
    borderRadius: 12,
    borderColor: '#474756',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    backgroundColor: '#474756',
    fontSize: 16,
    marginBottom: 18,
    minHeight: 40,
  },
  additionalAmountInput: {
    height: 57,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#cbcbcb',
    backgroundColor: '#3D3D50',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#fff',
    marginBottom: 5,
    fontSize: 16 / PixelRatio.getFontScale(),
  },
}
