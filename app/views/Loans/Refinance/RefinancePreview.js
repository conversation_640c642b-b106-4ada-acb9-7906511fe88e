import React, {useEffect, useState} from 'react'
import {
  ActivityIndicator,
  Dimensions,
  Image,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import moment from 'moment'
import BigNumber from 'bignumber.js'
import {useNavigation} from '@react-navigation/native'
import AsyncStorage from '@react-native-async-storage/async-storage'

import {Button, TextBold, TextReg} from '../../../components'
import {formatCurrency, getOriginationFeeAmount} from '../../../util/helpers'
import {increaseRefreshDataCount} from '../../../store/user/user.actions'
import {navigateRefinanceLoan} from './helpers'

const {width: screenWidth} = Dimensions.get('window')

const RefinancePreview = ({route}) => {
  const [checked, setChecked] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [dataLoading, setDataLoading] = useState(true)
  const [refiInfo, setRefiInfo] = useState(null)
  const accountId = route?.params?.accountId
  const repaymentType = route?.params?.repaymentType
  const terms = route?.params?.terms
  const originationFee = route?.params?.originationFee
  const maxFee = route?.params?.maxFee
  const apr = route?.params?.apr
  const affiliateCode = route?.params?.affiliateCode || ''
  const newAmount = route?.params?.newAmount || 0

  const user = useSelector(state => state.user.user)
  const WebService = useSelector(state => state.auth.WebService)
  const navigation = useNavigation()
  const dispatch = useDispatch()

  const allActiveAccountsWithLoans =
    user?.accounts?.filter(a => a.loans?.length > 0 && a.loans[0]?.status === 'active') || null

  const selectedAccount = allActiveAccountsWithLoans?.find(a => a.accountId === accountId)

  useEffect(() => {
    const getRefinanceInfo = async () => {
      const loan = selectedAccount?.loans[0]
      const payload = {
        interestRate: terms?.interestRate,
        interestOnly: repaymentType === 'io',
        originationFeeRate: originationFee.toString(),
        term: terms.term,
        saltRedeemed: '0',
        cashInHand: newAmount,
        fundingDate: new Date().toString(),
        ltv: new BigNumber(terms.ltv).toString(),
      }
      try {
        await WebService.getRefinanceInfo(loan.id, payload).then(res => {
          setRefiInfo(res.data)
        })
        setDataLoading(false)
      } catch (e) {
        setDataLoading(false)
        console.log('Error getting refinance info: ', e)
      }
    }
    getRefinanceInfo()
  }, [])

  const originationFeeAmount = () => {
    const calcFeeAmount = getOriginationFeeAmount(
      new BigNumber(newAmount).toNumber(),
      new BigNumber(originationFee).toNumber(),
    )
    return new BigNumber(calcFeeAmount).isGreaterThan(new BigNumber(maxFee))
      ? new BigNumber(maxFee)
      : new BigNumber(calcFeeAmount)
  }

  let collaterals = user?.allWallets[selectedAccount?.ref - 1]

  const collateralTotalWithSalt = collaterals?.reduce(
    (sum, collateral) => parseFloat(sum) + parseFloat(collateral.value),
    0,
  )
  const additionalCollateral = new BigNumber(newAmount ? newAmount : 0)
    .plus(originationFeeAmount())
    .plus(refiInfo?.interestAccrued || 0)
    .plus(selectedAccount?.loans[0]?.amortizationInfo?.currentBalance)
    .dividedBy(terms?.ltv)
    .minus(collateralTotalWithSalt)
    .toFixed(2)

  const getRefrenceId = () => {
    let referenceId = {}
    if (selectedAccount?.type === 'business') {
      referenceId = {
        id: selectedAccount?.entityProfile?.address?.id,
        id2: selectedAccount?.entityProfile?.id,
      }
    } else {
      referenceId = {id: selectedAccount?.loans[0]?.id}
    }

    return referenceId
  }
  const handleRefinance = async () => {
    setIsLoading(true)
    try {
      const res = await WebService.fetchJurisdictionInfo({
        type: selectedAccount?.type,
        countryCode: route?.params?.address?.countryCode,
        province: route?.params?.address?.state || route?.params?.address?.province,
      })

      const payload = {
        interestRate: terms?.interestRate,
        amount: new BigNumber(newAmount)
          .plus(originationFeeAmount())
          .plus(refiInfo?.interestAccrued)
          .plus(selectedAccount?.loans[0]?.amortizationInfo?.currentBalance)
          .toNumber(),
        originationFeeAmount: originationFeeAmount()?.toFixed(2) || '0',
        originationFeeRate: new BigNumber(originationFee).dp(6),
        term: terms?.term,
        saltStaked: selectedAccount?.loans[0]?.saltStaked,
        saltRedeemed: selectedAccount?.loans[0]?.saltRedeemed,
        interestOnly: repaymentType === 'io',
        affiliateCode,
        baseLTV: refiInfo?.ltv,
        contractDate: new Date(),
        fundingDate: new Date(),
        amountToCustomer: newAmount === '' ? 0 : newAmount,
        apr: apr?.toString(),
        jurisdictionId: res?.data?.id,
      }
      console.log('paypay: ', payload)
      let refiLoan = null
      await WebService.loanRefinance(selectedAccount?.loans[0]?.id, payload).then(async res => {
        refiLoan = res.data
        const refinancedLoans = (await AsyncStorage.getItem('refinancedLoans'))
          ? JSON.parse(await AsyncStorage.getItem('refinancedLoans'))
          : null
        const saveLoan = refinancedLoans ? [...refinancedLoans, res.data.id] : [res.data.id]
        await AsyncStorage.setItem('refinancedLoans', JSON.stringify(saveLoan))
      })

      if (route?.params?.isAddressChanged) {
        const {id, id2} = getRefrenceId()
        const addressPayload = {
          street1: route?.params?.address?.street1,
          street2: route?.params?.address?.street2,
          countryCode: route?.params?.address?.countryCode,
          province: route?.params?.address?.state || route?.params?.address?.province,
          city: route?.params?.address?.city,
          postalCode: route?.params?.address?.postalCode,
        }

        await WebService.patchAddress(route?.params?.address.id, addressPayload)
        if (selectedAccount?.type === 'business') {
          await WebService.deleteDocument(id)
          if (id2) {
            await WebService.deleteDocument(id2)
          }
        }
      }
      await dispatch(increaseRefreshDataCount())
      setIsLoading(false)
      navigateRefinanceLoan(selectedAccount, user, navigation, refiLoan)
    } catch (e) {
      setIsLoading(false)
      console.log('Error creating refinance loan: ', e)
    }
  }
  const summary = [
    {
      title: 'Additional Funds (Cash in Hand)',
      value: '$' + formatCurrency(newAmount),
    },
    {
      title: 'New Loan Amount',
      value:
        '$' +
        formatCurrency(
          new BigNumber(newAmount)
            .plus(originationFeeAmount())
            .plus(refiInfo?.interestAccrued)
            .plus(selectedAccount?.loans[0]?.amortizationInfo?.currentBalance)
            .toFixed(2),
        ),
    },
    {
      title: 'Interest Accrued Amount',
      value: '$' + formatCurrency(refiInfo?.interestAccrued),
    },
    {
      title: 'Origination Fee Amount',
      value: '$' + formatCurrency(originationFeeAmount()),
    },
    {
      title: 'Origination Fee',
      value: new BigNumber(originationFee).times(100).toFixed(2) + '%',
    },
    {
      title: 'New OLTV',
      value: new BigNumber(refiInfo?.ltv).times(100).toFixed(2) + '%',
    },
    {
      title: 'Interest Rate',
      value: new BigNumber(terms?.interestRate).times(100).toFixed(2) + '%',
    },
    {
      title: 'Additional Collateral Needed',
      value: `$${+additionalCollateral > 0 ? formatCurrency(+additionalCollateral) : 0}`,
    },
    {
      title: 'APR',
      value: new BigNumber(refiInfo?.apr).times(100).toFixed(2) + '%',
    },
    {
      title: 'Term Extension',
      value: `${terms.term} ${Number(terms.term) > 1 ? 'Months' : 'Month'}`,
    },
    {
      title: 'New Monthly Payment',
      value: '$' + formatCurrency(terms.monthlyPayment),
    },
    {
      title: 'Est. Origination Date',
      value: moment().format('ll'),
    },
    {
      title: 'Payment Type',
      value: repaymentType === 'io' ? 'Interest Only' : 'Principal & Interest',
    },
  ]

  const hasPromo = route?.params?.terms?.oldApr

  return (
    <SafeAreaView style={local.box}>
      <View>
        <View
          style={{
            flexDirection: 'row',
            alignSelf: 'center',
            justifyContent: 'space-between',
            marginTop: 5,
            marginBottom: 5,
            width: screenWidth - 40,
          }}>
          <TouchableOpacity onPress={() => navigation?.goBack()} style={{height: 40, width: 40}}>
            <Image
              source={require('../../../imgs/backToSettings.png')}
              style={{height: 25, width: 20}}
            />
          </TouchableOpacity>
          <TextReg style={{color: '#FFF', fontSize: 18}}>Refinance</TextReg>
          <View style={{height: 40, width: 40}} />
        </View>
        <ScrollView
          style={{alignSelf: 'stretch', height: 200, paddingHorizontal: 20}}
          contentContainerStyle={{alignItems: 'center'}}>
          <View style={local.card}>
            <Image
              source={require('../../../imgs/RefinancePreview.png')}
              style={{width: '100%', height: 158}}
              resizeMode="cover"
            />

            <View style={local.descriptionBox}>
              {summary.map((item, index) => (
                <View key={index}>
                  <View
                    style={{
                      ...local.loanRequestSummaryRow,
                      borderBottomWidth:
                        index === summary.length - 1
                          ? 0
                          : hasPromo && item?.title === 'APR'
                          ? 0
                          : 0.5,
                      paddingBottom: index === summary.length - 1 ? 0 : 16,
                      paddingTop: index === 0 ? 12 : 16,
                      flexWrap: 'wrap',
                    }}>
                    <TextBold
                      style={{
                        ...local.loanRequestSummaryText,
                        maxWidth: '65%',
                      }}>
                      {item.title}
                    </TextBold>
                    {dataLoading ? (
                      <ActivityIndicator size="small" />
                    ) : (
                      <TextReg style={local.loanRequestSummaryText}>{item.value}</TextReg>
                    )}
                  </View>
                  {hasPromo && item?.title === 'APR' && (
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        gap: 8,
                        backgroundColor: '#00FFBD4D',
                        paddingVertical: 8,
                        paddingHorizontal: 12,
                        borderRadius: 8,
                      }}>
                      <View style={{flexDirection: 'row', alignItems: 'center', gap: 8}}>
                        <Image
                          source={require('../../../imgs/promoCode.png')}
                          style={{height: 20, width: 20, tintColor: '#FFFFFF'}}
                        />
                        <TextReg
                          style={{
                            ...local.loanRequestSummaryText,
                            fontSize: 16,
                          }}>
                          Promotion Code applied
                        </TextReg>
                      </View>
                      <TextBold style={{...local.loanRequestSummaryText}}>
                        {new BigNumber(route?.params?.terms?.apr)
                          .minus(route?.params?.terms?.oldApr)
                          .times(100)
                          .toFixed(2)}
                        %
                      </TextBold>
                    </View>
                  )}
                </View>
              ))}
            </View>
          </View>
          <View
            style={{
              alignSelf: 'center',
              marginTop: 38,
              width: screenWidth - 40,
            }}>
            <TouchableOpacity onPress={() => setChecked(!checked)}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'stretch',
                  alignSelf: 'stretch',
                  marginTop: 14,
                  marginBottom: 14,
                  width: screenWidth - 40,
                }}>
                {checked ? (
                  <Image
                    source={require('../../../imgs/referrals/checkedBox.png')}
                    style={{height: 25, width: 25, marginRight: 14, tintColor: '#00FFBD'}}
                  />
                ) : (
                  <Image
                    source={require('../../../imgs/referrals/unchecked.png')}
                    style={{height: 25, width: 25, marginRight: 14}}
                  />
                )}
                <TextReg style={{fontSize: 12, width: '82%'}}>
                  By clicking CONFIRM, you agree to the displayed terms and authorize SALT to review
                  your request and send your REFINANCE documents via email for your review.
                </TextReg>
              </View>
            </TouchableOpacity>

            <Button
              style={{alignSelf: 'stretch', height: 60, marginTop: 40}}
              disabled={!checked || dataLoading}
              isLoading={isLoading}
              onPress={() => handleRefinance()}>
              <TextBold
                style={{
                  fontSize: 18,
                  letterSpacing: 2.16,
                  color: '#000',
                  alignSelf: 'stretch',
                }}>
                CONTINUE
              </TextBold>
            </Button>
            <Button
              style={{
                alignSelf: 'stretch',
                backgroundColor: 'transparent',
                height: 60,
                marginBottom: 20,
              }}
              disabled={isLoading}
              isLoading={false}
              onPress={() => navigation.goBack()}>
              <TextBold style={{color: '#00FFBD', fontSize: 18, letterSpacing: 2.16}}>
                BACK
              </TextBold>
            </Button>
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  )
}

export default RefinancePreview

let local = {
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
  actionTitleTxt: {
    marginTop: 20,
    fontSize: 18,
    marginBottom: 32,
  },
  descriptionBox: {
    borderWidth: 1,
    borderColor: '#FFF',
    borderRadius: 8,
    alignSelf: 'stretch',
    backgroundColor: '#535364',
    marginTop: 12,
    paddingVertical: 12,
    paddingHorizontal: 12,
  },
  buttonContainer: {
    justifyContent: 'flex-end',
    alignItems: 'stretch',
    alignSelf: 'stretch',
    paddingLeft: 30,
    paddingRight: 30,
    paddingBottom: 20,
    gap: 12,
  },
  continueButton: {
    width: screenWidth - 40,
    backgroundColor: '#00FFBD',
    height: 60,
    borderRadius: 15,
  },
  backButton: {
    width: screenWidth - 40,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  backButtonText: {color: '#00FFBD', fontSize: 18, letterSpacing: 2.16},
  handImage: {height: 60, width: 60},

  card: {
    backgroundColor: '#3D3D50',
    width: screenWidth - 40,
    alignSelf: 'center',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    marginBottom: 12,
  },

  loanRequestSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    borderColor: '#818181',
    gap: 12,
  },
  loanRequestSummaryText: {
    fontSize: 19,
    color: '#FFF',
    flexWrap: 'wrap',
  },
}
