import React, {useState} from 'react'
import {
  View,
  Image,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  ActivityIndicator,
} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import DropDownPicker from 'react-native-dropdown-picker'
import {useNavigation} from '@react-navigation/native'

import {TextBold, TextReg, Button} from '../../../components'
import {isAuthed} from '../../../store/auth/auth.actions'
import {updateLoans} from '../../../store/user/user.actions'

const {width: screenWidth} = Dimensions.get('window')

const SelectRefinanceLoan = () => {
  const navigation = useNavigation()
  const [dropOpen, setDropOpen] = useState(false)
  const [selected, setSelected] = useState()

  const dispatch = useDispatch()
  const user = useSelector(state => state.user.user)
  const WebService = useSelector(state => state.auth.WebService)

  const allActiveAccountsWithLoans =
    user?.accounts?.filter(
      a =>
        !a?.refinanceLoan &&
        a.loans?.length > 0 &&
        a.loans[0]?.status === 'active' &&
        !a?.product?.loan?.pendingLoanId &&
        Number(a.loans[0]?.amortizationInfo?.currentBalance) !== 0,
    ) || null

  const items = allActiveAccountsWithLoans?.map(account => ({
    label: account.name,
    value: account.accountId,
    key: account.accountId,
  }))

  const next = async () => {
    const product = allActiveAccountsWithLoans?.find(a => a.id === selected)
    await dispatch(isAuthed({ref: product.ref, email: user.primaryEmail}))
    await WebService.updateRef(product.ref)
    await WebService.getLoans().then(res => {
      if (res.data?.length > 0) {
        const loanData = res.data[0]
        dispatch(updateLoans({...loanData, refinanceLoan: null}))
      }
    })
    navigation.navigate('EditRefinanceAddress', {accountId: selected})
  }

  return (
    <SafeAreaView style={local.box}>
      {!allActiveAccountsWithLoans ? (
        <ActivityIndicator size="large" color="#fff" style={{marginTop: 40, marginBottom: 10}} />
      ) : (
        <React.Fragment>
          <View>
            <View
              style={{
                flexDirection: 'row',
                alignSelf: 'stretch',
                justifyContent: 'space-between',
                marginTop: 5,
                marginBottom: 5,
              }}>
              <TouchableOpacity
                onPress={() => navigation?.goBack()}
                style={{height: 40, width: 40}}>
                <Image
                  source={require('../../../imgs/backToSettings.png')}
                  style={{height: 25, width: 20}}
                />
              </TouchableOpacity>
              <TextReg style={{color: '#FFF', fontSize: 18}}>Refinance</TextReg>
              <View style={{height: 40, width: 40}} />
            </View>
            <View style={local.descriptionBox}>
              <View style={local.titleContainer}>
                <View style={{position: 'relative', width: 71, height: 71}}>
                  <Image
                    source={require('../../../imgs/referrals/hand.png')}
                    style={local.handImage}
                  />
                  <View style={local.iconContainer}>
                    <Image
                      source={require('../../../imgs/homeActivities/repayment.png')}
                      style={local.iconImage}
                    />
                  </View>
                </View>
              </View>
              <TextReg style={local.actionTitleTxt}>
                Which existing loan would you like to Refinance?
              </TextReg>
              <TextReg style={{marginBottom: 8}}>Select Existing Loan</TextReg>
              <DropDownPicker
                open={dropOpen}
                value={selected}
                items={items}
                setOpen={() => setDropOpen(!dropOpen)}
                setValue={value => setSelected(value)}
                itemKey="key"
                theme="DARK"
                listMode={'MODAL'}
                searchable={false}
                closeAfterSelecting={true}
                placeholder={'Choose...'}
                style={local.drop}
                textStyle={{fontSize: 16, color: '#FFF'}}
                placeholderStyle={{fontSize: 16, color: '#FFF'}}
                translation={{
                  NOTHING_TO_SHOW: 'Not Found!',
                }}
              />
            </View>
          </View>

          <View style={local.buttonContainer}>
            <Button
              isLoading={false}
              disabled={!selected}
              onPress={() => next()}
              style={local.continueButton}>
              <TextBold style={{color: !selected ? '#FFF' : '#000', letterSpacing: 2.16}}>
                CONTINUE
              </TextBold>
            </Button>
            <Button isLoading={false} onPress={() => navigation?.goBack()} style={local.backButton}>
              <TextBold style={local.backButtonText}>BACK</TextBold>
            </Button>
          </View>
        </React.Fragment>
      )}
    </SafeAreaView>
  )
}

export default SelectRefinanceLoan

let local = {
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
  titleContainer: {
    width: 140,
    height: 140,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#535364',
    borderRadius: 70,
    alignSelf: 'center',
  },
  actionTitleTxt: {
    marginTop: 48,
    fontSize: 18,
    marginBottom: 32,
  },
  descriptionBox: {
    width: screenWidth - 40,
  },
  buttonContainer: {
    justifyContent: 'flex-end',
    alignItems: 'stretch',
    alignSelf: 'stretch',
    paddingLeft: 30,
    paddingRight: 30,
    paddingBottom: 20,
    gap: 12,
  },
  continueButton: {
    width: screenWidth - 40,
    backgroundColor: '#00FFBD',
    height: 60,
    borderRadius: 15,
  },
  backButton: {
    width: screenWidth - 40,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  backButtonText: {color: '#00FFBD', fontSize: 18, letterSpacing: 2.16},
  handImage: {tintColor: '#FFF', height: 71, width: 71},
  iconContainer: {
    position: 'absolute',
    right: -10,
    top: -10,
    backgroundColor: '#00FFBD',
    borderRadius: 20.5,
    width: 41,
    height: 41,
    borderWidth: 3,
    borderColor: '#535364',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  iconImage: {tintColor: '#535364', height: 18, width: 20},
  card: {
    backgroundColor: '#535364',
    borderWidth: 2,
    flexDirection: 'row',
    alignSelf: 'stretch',
    alignItems: 'center',
    gap: 22,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  drop: {
    height: 54,
    borderWidth: 1,
    borderRadius: 12,
    borderColor: '#474756',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    backgroundColor: '#474756',
    fontSize: 16,
    marginBottom: 18,
    minHeight: 40,
  },
}
