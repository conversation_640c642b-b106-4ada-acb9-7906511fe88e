import BigNumber from 'bignumber.js'

export const navigateRefinanceLoan = async (account, user, navigation, refinanceLoan) => {
  const product = refinanceLoan || account?.refinanceLoan
  let deposits = user?.allWallets[account.ref - 1]

  const collateralTotalWithSalt =
    deposits?.reduce((sum, collateral) => parseFloat(sum) + parseFloat(collateral.value), 0) || 0

  const collateralNeeded = new BigNumber(product?.amount).dividedBy(product?.baseLTV).toFixed(2)
  let totalRemaining = collateralNeeded - collateralTotalWithSalt
  const depositCompleted = totalRemaining <= 0

  const addressDocs = account?.entityProfile?.address?.documents
  const businessDocs = account?.entityProfile?.documents

  const refiLoanDocs = product?.loanDocuments?.filter(
    doc => doc.type === 'verification_of_residency',
  )

  const unverifiedStates = ['retry', 'required', 'manual_verification', 'pending', 'failed']

  const isNotVerified = (step, forManual) =>
    forManual
      ? unverifiedStates.filter(val => val !== 'manual_verification').includes(step)
      : unverifiedStates.includes(step)

  let addressDocsRejected = false
  let businessDocsRejected = false
  const residencyDocsRejected =
    refiLoanDocs?.length > 0 ? refiLoanDocs?.every(doc => doc.rejectedAt) : true

  if (account?.type === 'business') {
    addressDocsRejected = addressDocs?.length > 0 ? addressDocs?.every(doc => doc.rejectedAt) : true
    businessDocsRejected =
      businessDocs?.length > 0 ? businessDocs?.every(doc => doc.rejectedAt) : true
  }

  const params = {
    collateralRequired: !depositCompleted,
    forRefi: true,
    refinanceLoan: product,
  }

  let {useOfFunds, isInMilitary} = product?.loanQuestionnaire || {}
  let hasFundsUse = useOfFunds
  let isMilitaryAnswered = isInMilitary !== null

  const identityVerificationStatus = isNotVerified(
    product?.verification?.identityVerificationStatus,
  )

  const photoIdVerificationStatus = isNotVerified(product?.verification?.photoIdVerificationStatus)
  const beneficiariesStatus = isNotVerified(product?.verification?.beneficiariesStatus, true)
  const bankruptcyVerificationStatus = isNotVerified(product?.verification?.bankruptcyStatus, true)
  const marginManagementPreference = product?.marginManagementPreference
  const hasLoanBankingInformation = !!product?.depositBankAccount || product?.isPartnerLoan

  const bankInfoNotVerified =
    product?.depositBankAccount &&
    product?.depositBankAccount?.address &&
    !product?.depositBankAccount?.verifiedAt

  if (!hasFundsUse) {
    navigation.navigate('FlowL1Funds', params)
  } else if (!isMilitaryAnswered) {
    navigation.navigate('FlowL1Military', params)
  } else if (account?.type === 'business' && beneficiariesStatus) {
    navigation.navigate('Beneficiaries', params)
  } else if (identityVerificationStatus) {
    navigation.push('FlowL1PhoneNumber', params)
  } else if (photoIdVerificationStatus) {
    navigation.push('JumioVerification', params)
  } else if (residencyDocsRejected) {
    navigation.navigate('JurisdictionDocs', params)
  } else if (bankruptcyVerificationStatus) {
    navigation.navigate('Unit21Bankruptcy', params)
  } else if (bankInfoNotVerified || !hasLoanBankingInformation) {
    navigation.navigate('LoanPayout', params)
  } else if (addressDocsRejected && account?.type === 'business') {
    navigation.navigate('AddressVerification', params)
  } else if (businessDocsRejected && account?.type === 'business') {
    navigation.navigate('BusinessVerification', params)
  } else if (!depositCompleted) {
    navigation.navigate('RefinanceCollateral')
  } else if (depositCompleted && !marginManagementPreference) {
    navigation.navigate('ConfirmRiskManagement', params)
  } else if (
    (depositCompleted && !product?.contract) ||
    product?.verification?.bankruptcyStatus === 'manual_verification'
  ) {
    navigation.navigate('RefinanceUnderReview')
  } else if (depositCompleted && product?.contract && !!product?.contract?.completedAt === false) {
    navigation.navigate('RefinanceSignDocs')
  } else {
    navigation.navigate('Loans', {id: account.id, ref: account.ref})
  }
}
