import React, {useEffect, useState} from 'react'
import {
  View,
  Image,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  ActivityIndicator,
  PixelRatio,
  ScrollView,
  Linking,
} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import {useNavigation} from '@react-navigation/native'
import BigNumber from 'bignumber.js'

import {TextBold, TextReg, Button} from '../../../components'
import {
  formatCurrency,
  getAprForRefinance,
  getBaseRate,
  getFeeRateWithDiscount,
  getOriginationFeeAmount,
  getOriginationFeeRate,
  getPaymentForRefinance,
} from '../../../util/helpers'
import PromoCodeModal from '../../../components/Modal/PromoCodeModal'
import {askingForPermissions} from '../../../store/auth/auth.actions'

const {width: screenWidth} = Dimensions.get('window')

const TermsAndApr = ({route}) => {
  const navigation = useNavigation()
  const [loadingTerms, setLoadingTerms] = useState(true)
  const [codeLoading, setCodeLoading] = useState(false)
  const [codeError, setCodeError] = useState(false)
  const [dataLoading, setDataLoading] = useState(true)
  const [openPromoModal, setOpenPromoModal] = useState(false)
  const [promoData, setPromoData] = useState({
    affliateValue: '',
    data: {rateDiscount: 0, originationFeeDiscount: 0},
  })
  const [refiInfo, setRefiInfo] = useState(null)
  const [rates, setRates] = useState(null)
  const [originationFee, setOriginationFee] = useState(0)
  const [maxFee, setMaxFee] = useState(0)
  const [realOriginationFee, setRealOriginationFee] = useState(0)
  const [termsPaymentValue, setTermsPaymentValue] = useState([])
  const [promotermsPaymentValue, setPromoTermsPaymentValue] = useState([])
  const [newAPR, setNewAPR] = useState([])
  const [selectedCard, setSelectedCard] = useState(-1)
  const [selectedTerm, setSelectedTerm] = useState({
    apr: 0,
    monthlyPayment: 0,
    term: 12,
    oldApr: 0,
    ltv: 0,
  })

  const dispatch = useDispatch()

  const user = useSelector(state => state.user.user)
  const WebService = useSelector(state => state.auth.WebService)
  const accountId = route?.params?.accountId
  const currentAddress = route?.params?.address
  const newAmount = route?.params?.newAmount || 0
  const isInterestOnly = route?.params?.repaymentType === 'io'

  const allActiveAccountsWithLoans =
    user?.accounts?.filter(a => a.loans?.length > 0 && a.loans[0]?.status === 'active') || null
  const selectedAccount = allActiveAccountsWithLoans?.find(a => a.accountId === accountId)

  useEffect(() => {
    maxFeeGet()
    return () => {}
  }, [])

  useEffect(() => {
    maxFeeGet()
    const getRefinanceInfo = async () => {
      const loan = selectedAccount?.loans[0]
      const adminFee = originationFee - +promoData.data.originationFeeDiscount
      const payload = {
        interestRate: selectedTerm?.interestRate,
        interestOnly: isInterestOnly,
        cashInHand: newAmount,
        originationFeeRate: adminFee.toString(),
        term: selectedTerm?.term,
        saltRedeemed: '0',
        amount: newAmount,
        fundingDate: new Date().toString(),
        ltv: new BigNumber(selectedTerm?.ltv).toString(),
      }
      await WebService.getRefinanceInfo(loan.id, payload)
        .then(res => {
          setRefiInfo(res.data)
          setDataLoading(false)
        })
        .catch(err => {
          setDataLoading(false)
          console.log('Error getting refinance info: ', err)
        })
    }
    if (selectedCard !== -1) {
      getRefinanceInfo()
    }
  }, [selectedTerm])
  const termOptions = baseRates => {
    const termOptions = baseRates
      ? Object.keys(baseRates)
          ?.map(name => ({
            value: +name,
            label: `${name / 12} ${name / 12 === 1 ? 'year' : 'years'}`,
          }))
          .sort((a, b) => {
            return a.value - b.value
          })
      : []
    return termOptions
  }

  const ltvOptionsPromise = async (isNonLeveragedOn, terms, baseRates) => {
    const ltvOptions = terms.map(term => {
      const options = baseRates
        ? Object.keys(baseRates[term.value] || [])
            .map(name => {
              return {
                value: +name,
                label: `${Math.floor(parseFloat(name) * 100)}%`,
                term: term.value,
              }
            })
            .filter(item => (isNonLeveragedOn ? item.label === '50%' : true))
            .sort((a, b) => a.value - b.value)
        : []

      return options.map(({value, label, term}) => ({term, ltv: value, label}))
    })

    // Flatten and resolve the options
    return Promise.resolve(ltvOptions.flat())
  }

  const getFeeRate = () => {
    if (+promoData?.data?.originationFeeDiscount !== 0) {
      const feeRate = getFeeRateWithDiscount(
        new BigNumber(originationFee).toNumber(),
        new BigNumber(promoData?.data?.originationFeeDiscount).toNumber(),
      )
      return new BigNumber(feeRate).isLessThan(0) ? 0 : feeRate
    } else {
      return originationFee
    }
  }
  const getAprForTerms = (value, baseRates) => {
    return new Promise((resolve, reject) => {
      try {
        const aprResults = value.map(item => {
          const loanAmount = new BigNumber(
            selectedAccount?.loans[0]?.amortizationInfo?.currentBalance,
          )
            .plus(route?.params?.info?.interestAccrued || 0)
            .plus(newAmount)
            .toNumber()

          let interestRate = getBaseRate(item.ltv, baseRates, new BigNumber(item.term).toNumber())
          interestRate = interestRate - promoData.data.rateDiscount
          interestRate = Math.round(interestRate * 1000000) / 1000000
          let adminFee = new BigNumber(newAmount).isZero()
            ? 0
            : originationFee - +promoData.data.originationFeeDiscount
          let loanFee = newAmount / (1 - adminFee) - newAmount
          loanFee = Number((Math.ceil(loanFee * 100) / 100).toFixed(2))

          const apr = getAprForRefinance(
            loanAmount,
            newAmount,
            interestRate,
            item.term,
            isInterestOnly,
            adminFee,
            0,
          )

          //payment
          const payment = getPaymentForRefinance(
            new BigNumber(loanAmount).plus(loanFee).toNumber(),
            interestRate,
            item.term,
            isInterestOnly,
          )
          return {
            term: item.term,
            apr,
            monthlyPayment: payment,
            ltv: item.ltv,
            interestRate,
          }
        })
        resolve(aprResults)
      } catch (error) {
        reject(error)
      }
    })
  }

  useEffect(() => {
    maxFeeGet()
    const getEverything = async () =>
      await WebService.getLoanRatesMaps().then(res => {
        const baseRates = res.data.baseRates
        const termsArray = termOptions(baseRates) // Example terms
        ltvOptionsPromise(false, termsArray, baseRates).then(result => {
          getAprForTerms(result, baseRates)
            .then(aprResults => {
              if (promoData.affliateValue) {
                setPromoTermsPaymentValue(aprResults)
              } else {
                setTermsPaymentValue(aprResults)
              }
            })
            .catch(error => {
              console.error('Error calculating APR:', error)
            })
        })
        setRates(baseRates)
      })

    getEverything()
  }, [promoData, originationFee])

  const next = async () => {
    let baseRate = getBaseRate(selectedTerm.ltv, rates, new BigNumber(selectedTerm.term).toNumber())
    baseRate = Math.round(baseRate * 1000000) / 1000000
    baseRate = baseRate - +promoData.data.rateDiscount
    let adminFee = new BigNumber(getFeeRate()).toNumber()
    console.log('adminFee: ', baseRate, promoData.data.rateDiscount)
    navigation.navigate('RefinancePreview', {
      ...route?.params,
      terms: selectedTerm,
      originationFee: adminFee,
      affiliateCode: promoData?.affliateValue,
      apr: getApr().toString(),
      maxFee,
    })
  }
  const adminFeeGet = async maxFee => {
    await WebService.getAdminFee()
      .then(res => {
        let adminFee = Number(res.data?.originationFee || '0.0')
        if (adminFee) {
          const originationFeeAmount = getOriginationFeeAmount(
            new BigNumber(newAmount).toNumber(),
            adminFee,
          )

          if (new BigNumber(originationFeeAmount).isLessThan(new BigNumber(maxFee))) {
            setOriginationFee(adminFee)
          } else {
            const feeRate = getOriginationFeeRate(
              new BigNumber(newAmount).toNumber(),
              new BigNumber(maxFee).toNumber(),
            )
            setOriginationFee(feeRate)
          }
        }
      })
      .catch(err => {
        console.log('admin fee err', err)
      })
  }
  const maxFeeGet = async () => {
    const payload = {
      type: selectedAccount?.type,
      country: route?.params?.address?.countryCode.toLowerCase(),
      province: route?.params?.address?.province
        ? route?.params?.address?.province.toLowerCase()
        : route?.params?.address?.state?.toLowerCase(),
      apr: selectedTerm.apr.toString(),
      amount: newAmount,
      loanInterestType: route?.params?.repaymentType === 'pi' ? 'pni' : 'io',
    }
    console.log('maxFeePayload: ', payload)

    await WebService.fetchLoanConstraints('fee', payload)
      .then(({data}) => {
        console.log('data: ', data)
        let maxFee
        if (data?.length) {
          maxFee = data?.find(item => item.constraint === 'LTE')?.value
          setMaxFee(data?.find(item => item.constraint === 'LTE')?.value)
        } else {
          maxFee = 0
          setMaxFee(0)
          setOriginationFee(0)
        }

        adminFeeGet(maxFee)
      })
      .catch(err => {
        console.log('fee err', err)
      })
  }

  const getApr = () => {
    return new BigNumber(
      getAprForRefinance(
        new BigNumber(selectedAccount?.loans[0]?.amortizationInfo?.currentBalance)
          .plus(
            route?.params?.info?.interestAccrued ||
              selectedAccount?.loans[0]?.amortizationInfo?.interestBalance,
          )
          .plus(newAmount)
          .toNumber(), // cuurent bal + unpaid interest
        new BigNumber(newAmount),
        selectedTerm?.interestRate,
        new BigNumber(selectedTerm.term).toNumber(),
        isInterestOnly,
        new BigNumber(getFeeRate()).toNumber(),
        new BigNumber(selectedAccount?.loans[0]?.saltRedeemed).toNumber(),
      ),
    )
  }

  const submitCode = () => {
    setCodeLoading(true)
    setCodeError(false)
    setTimeout(async () => {
      await WebService.validateAffiliate(promoData.affliateValue)
        .then(res => {
          setCodeLoading(false)
          setPromoData({
            affliateValue: promoData.affliateValue,
            data: res.data,
          })
          setSelectedCard(-1)
          setSelectedTerm({
            apr: 0,
            monthlyPayment: 0,
            term: 12,
            oldApr: 0,
            ltv: 0,
          })
          setOpenPromoModal(false)
        })
        .catch(err => {
          console.log('validateAffiliate err', err)
          setCodeLoading(false)
          setCodeError(true)
        })
    }, 1000)
  }

  const updateCode = text => {
    text = text.replace(' ', '')
    setPromoData({
      affliateValue: text,
      data: {rateDiscount: 0, originationFeeDiscount: 0},
    })
  }

  const removeCode = () => {
    setCodeLoading(false)
    setPromoData({
      affliateValue: '',
      data: {rateDiscount: 0, originationFeeDiscount: 0},
    })
    setSelectedCard(-1)
    setSelectedTerm({
      apr: 0,
      monthlyPayment: 0,
      term: 12,
      oldApr: 0,
      ltv: 0,
    })
    setCodeError(false)
  }

  useEffect(() => {
    function updateAPR(data1, data2) {
      // Map data2 for faster lookup by ltv and apr
      const data2Map = new Map()
      data2.forEach(item => {
        const key = `${item.ltv}-${item.term}`
        data2Map.set(key, item.apr)
      })

      // Add oldApr field to data1 objects
      return data1.map(item => {
        const key = `${item.ltv}-${item.term}`
        return {
          ...item,
          oldApr: data2Map.get(key) || null, // If no match found, set oldApr to null
        }
      })
    }
    const updatedAPR = updateAPR(promotermsPaymentValue, termsPaymentValue)
    setNewAPR(updatedAPR)
    setLoadingTerms(false)
  }, [termsPaymentValue, promotermsPaymentValue])

  const cardList = promoData?.affliateValue ? newAPR : termsPaymentValue

  const openPromoTerms = () => {
    dispatch(askingForPermissions(true))
    Linking.openURL(`https://saltlending.com/promo-code-terms-and-conditions/`)
  }
  const showAdditionalInfo =
    +refiInfo?.maximumCashInHand < +newAmount && selectedCard !== -1 && !dataLoading

  return (
    <SafeAreaView style={local.box}>
      <View
        style={{
          flexDirection: 'row',
          alignSelf: 'center',
          justifyContent: 'space-between',
          marginTop: 5,
          marginBottom: 5,
          width: screenWidth - 40,
        }}>
        <TouchableOpacity onPress={() => navigation?.goBack()} style={{height: 40, width: 40}}>
          <Image
            source={require('../../../imgs/backToSettings.png')}
            style={{height: 25, width: 20}}
          />
        </TouchableOpacity>
        <TextReg style={{color: '#FFF', fontSize: 18}}>Refinance</TextReg>
        <View style={{height: 40, width: 40}} />
      </View>
      {!allActiveAccountsWithLoans ? (
        <ActivityIndicator size="large" color="#fff" style={{marginTop: 40, marginBottom: 10}} />
      ) : (
        <ScrollView
          style={{alignSelf: 'stretch', height: 200}}
          contentContainerStyle={{alignItems: 'center'}}>
          <View>
            <View style={local.descriptionBox}>
              <View style={local.titleContainer}>
                <Image source={require('../../../imgs/clock2.png')} style={local.handImage} />
              </View>
              <TextReg style={local.actionTitleTxt}>
                How long do you want to extend your current loan?
              </TextReg>
              <TextReg style={{...local.actionTitleTxt, marginTop: 0}}>
                Select the additional time only.
              </TextReg>

              {loadingTerms ? (
                <ActivityIndicator
                  size="large"
                  color="#fff"
                  style={{marginTop: 40, marginBottom: 10}}
                />
              ) : (
                cardList?.map((card, index) => (
                  <TouchableOpacity
                    onPress={() => {
                      setSelectedCard(index), setSelectedTerm(card)
                    }}
                    key={card.ltv}>
                    <View
                      style={{...local.termOption, borderWidth: selectedCard === index ? 2 : 0}}>
                      <View style={local.term}>
                        <TextBold style={{fontSize: 40}}>{card.term}</TextBold>
                        <TextReg style={{fontSize: 18}}>months</TextReg>
                      </View>
                      <View style={local.term}>
                        <TextBold style={{fontSize: 30}}>
                          {new BigNumber(card.ltv).times(100).toString()}%
                        </TextBold>
                        <TextReg style={{fontSize: 16}}>LTV</TextReg>
                      </View>
                      <View>
                        {card.oldApr && (
                          <TextReg
                            style={{
                              fontSize: 14,
                              color: '#AFAFAF',
                              alignSelf: 'center',
                              textDecorationLine: 'line-through',
                              marginBottom: 4,
                            }}>
                            {new BigNumber(card.oldApr).multipliedBy(100).toFixed(2)}% APR
                          </TextReg>
                        )}
                        <View style={{gap: 12}}>
                          <View
                            style={{
                              backgroundColor: !card.oldApr ? '#28283D' : '#00FFBD',
                              paddingVertical: 4,
                              paddingHorizontal: 16,
                              borderRadius: 4,
                              alignItems: 'center',
                            }}>
                            <TextReg
                              style={{fontSize: 14, color: !card.oldApr ? '#00FFBD' : '#3D3D50'}}>
                              {new BigNumber(card.apr).multipliedBy(100).toFixed(2)}% APR
                            </TextReg>
                          </View>
                          <TextBold style={{fontSize: 14}}>
                            ${formatCurrency(card.monthlyPayment)} / month
                          </TextBold>
                        </View>
                      </View>
                    </View>
                  </TouchableOpacity>
                ))
              )}
            </View>
            {promoData?.affliateValue ? (
              <View style={{gap: 8, alignItems: 'center', marginTop: 30}}>
                <View style={{flexDirection: 'row', alignItems: 'center', gap: 8}}>
                  <Image
                    source={require('../../../imgs/promoCodeApplied.png')}
                    style={{height: 20, width: 20}}
                  />
                  <TextReg>Promotion code applied:</TextReg>
                </View>
                <View style={{flexDirection: 'row', alignItems: 'center', gap: 8}}>
                  <TextReg style={{fontSize: 16}}>{promoData.affliateValue}</TextReg>
                  <TouchableOpacity onPress={() => removeCode()}>
                    <TextReg style={{color: '#E5705A', fontSize: 16}}>Remove</TextReg>
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              <TouchableOpacity
                onPress={() => setOpenPromoModal(!openPromoModal)}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginTop: 18,
                  gap: 8,
                }}>
                <Image
                  source={require('../../../imgs/promoCode.png')}
                  style={{height: 20, width: 20}}
                />
                <TextBold
                  style={{
                    flexWrap: 'wrap',
                    color: '#00FFBD',
                  }}>
                  I have a Promotional Code
                </TextBold>
              </TouchableOpacity>
            )}

            {showAdditionalInfo && (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'flex-start',
                  backgroundColor: '#1E5C5C',
                  borderWidth: 1,
                  borderColor: '#00FFBD',
                  padding: 12,
                  borderRadius: 12,
                  width: screenWidth - 44,
                  gap: 10,
                  marginTop: 20,
                }}>
                <Image
                  source={require('../../../imgs/graphics/info.png')}
                  style={{width: 22, height: 22}}
                />

                <TextReg style={{fontSize: 14, width: '95%'}}>
                  To borrow this amount, you’ll need to deposit additional collateral.
                </TextReg>
              </View>
            )}
          </View>

          <View style={local.buttonContainer}>
            <Button
              isLoading={false}
              disabled={selectedCard === -1 || dataLoading}
              onPress={() => next()}
              style={local.continueButton}>
              <TextBold style={{color: !selectedTerm ? '#FFF' : '#000', letterSpacing: 2.16}}>
                CONTINUE
              </TextBold>
            </Button>
            <Button isLoading={false} onPress={() => navigation?.goBack()} style={local.backButton}>
              <TextBold style={local.backButtonText}>BACK</TextBold>
            </Button>
          </View>
        </ScrollView>
      )}
      <PromoCodeModal
        modalVisable={openPromoModal}
        closeContactSupport={() => {
          removeCode()
          setOpenPromoModal(false)
        }}
        codeError={codeError}
        codeLoading={codeLoading}
        promoData={promoData}
        submitCode={submitCode}
        updateCode={updateCode}
        removeCode={removeCode}
        openPromoTerms={openPromoTerms}
      />
    </SafeAreaView>
  )
}

export default TermsAndApr

let local = {
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
  titleContainer: {
    width: 140,
    height: 140,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#535364',
    borderRadius: 70,
    alignSelf: 'center',
  },
  actionTitleTxt: {
    marginTop: 30,
    fontSize: 18,
    marginBottom: 25,
  },
  termOption: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#535364',
    borderRadius: 8,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderColor: '#00FFBD',
  },
  term: {alignItems: 'center', justifyContent: 'center'},
  descriptionBox: {
    width: screenWidth - 40,
  },
  buttonContainer: {
    justifyContent: 'flex-end',
    alignItems: 'center',
    alignSelf: 'stretch',
    paddingLeft: 30,
    paddingRight: 30,
    paddingBottom: 20,
  },
  continueButton: {
    width: screenWidth - 40,
    backgroundColor: '#00FFBD',
    height: 60,
    borderRadius: 15,
    marginTop: 20,
    marginBottom: 12,
  },
  backButton: {
    width: screenWidth - 40,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  backButtonText: {color: '#00FFBD', fontSize: 18, letterSpacing: 2.16},
  handImage: {height: 81, width: 81},
  card: {
    backgroundColor: '#535364',
    borderWidth: 2,
    flexDirection: 'row',
    alignSelf: 'stretch',
    alignItems: 'center',
    gap: 22,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  drop: {
    height: 54,
    borderWidth: 1,
    borderRadius: 12,
    borderColor: '#474756',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#e6e6e6',
    backgroundColor: '#474756',
    fontSize: 16,
    marginBottom: 18,
    minHeight: 40,
  },
  additionalAmountInput: {
    height: 57,
    borderWidth: 1,
    borderRadius: 14,
    borderColor: '#cbcbcb',
    backgroundColor: '#3D3D50',
    paddingLeft: 10,
    alignSelf: 'stretch',
    color: '#fff',
    marginBottom: 5,
    fontSize: 16 / PixelRatio.getFontScale(),
  },
}
