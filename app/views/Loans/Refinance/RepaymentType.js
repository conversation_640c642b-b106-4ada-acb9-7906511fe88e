import React, {useState} from 'react'
import {View, Image, TouchableOpacity, SafeAreaView, Keyboard, Dimensions} from 'react-native'
import {useDispatch} from 'react-redux'
import {useNavigation} from '@react-navigation/native'
import {TextBold, TextReg, Button} from '../../../components'

const {width: screenWidth} = Dimensions.get('window')

const RefinanceType = ({close, route}) => {
  const dispatch = useDispatch()
  const navigation = useNavigation()
  const [repaymentType, setRepaymentType] = useState('')
  const [loading, setLoading] = useState(false)

  // const user = useSelector(state => state.user.user)
  // const account = useSelector(state => state.auth.account)
  // const WebService = useSelector(state => state.auth.WebService)

  const accountId = route?.params?.accountId
  const refinanceType = route?.params?.refinanceType

  console.log('route2', route)

  const next = async () => {
    const params = {...route?.params, repaymentType}
    if (refinanceType === 'money') {
      navigation.navigate('AdditionalAmount', params)
    } else navigation.navigate('TermsAndApr', params)
  }

  return (
    <SafeAreaView style={local.box}>
      <View>
        <View
          style={{
            flexDirection: 'row',
            alignSelf: 'stretch',
            justifyContent: 'space-between',
            marginTop: 5,
          }}>
          <TouchableOpacity onPress={() => navigation?.goBack()} style={{height: 40, width: 40}}>
            <Image
              source={require('../../../imgs/backToSettings.png')}
              style={{height: 25, width: 20}}
            />
          </TouchableOpacity>
          <TextReg style={{color: '#FFF', fontSize: 18}}>Refinance</TextReg>
          <View style={{height: 40, width: 40}} />
        </View>
        <View style={local.descriptionBox}>
          <TextReg style={local.actionTitleTxt}>Select your preferred repayment type:</TextReg>

          <TouchableOpacity
            onPress={() => {
              Keyboard.dismiss()
              setRepaymentType('pi')
            }}
            activeOpacity={0.5}
            style={{
              ...local.card,
              borderColor: repaymentType === 'pi' ? '#00FFBD' : 'transparent',
            }}>
            <Image
              source={require('../../../imgs/principleAndInterest.png')}
              style={local.handImage}
            />

            <View style={{flex: 1, gap: 17}}>
              <TextBold style={{fontSize: 16}}>Principal & Interest</TextBold>
              <TextReg style={{fontSize: 12}}>
                Larger payments which are applied to both your original loan balance and your
                interest balance.
              </TextReg>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              Keyboard.dismiss()
              setRepaymentType('io')
            }}
            activeOpacity={0.5}
            style={{
              ...local.card,
              borderColor: repaymentType === 'io' ? '#00FFBD' : 'transparent',
            }}>
            <Image source={require('../../../imgs/interestOnly.png')} style={local.handImage} />
            <View style={{flex: 1, gap: 17}}>
              <TextBold style={{fontSize: 16}}>Interest Only</TextBold>
              <TextReg style={{fontSize: 12}}>
                Smaller payments applied only to the interest balance with larger balloon payment at
                end of loan term.
              </TextReg>
            </View>
          </TouchableOpacity>
        </View>
      </View>

      <View style={local.buttonContainer}>
        <Button
          isLoading={loading}
          disabled={repaymentType === ''}
          onPress={() => next()}
          style={local.continueButton}>
          <TextBold style={{color: repaymentType === '' ? '#FFF' : '#000', letterSpacing: 2.16}}>
            CONTINUE
          </TextBold>
        </Button>
        <Button isLoading={loading} onPress={() => navigation?.goBack()} style={local.backButton}>
          <TextBold style={local.backButtonText}>BACK</TextBold>
        </Button>
      </View>
    </SafeAreaView>
  )
}

export default RefinanceType

let local = {
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
  actionTitleTxt: {
    marginTop: 20,
    fontSize: 18,
    marginBottom: 32,
  },
  descriptionBox: {
    width: screenWidth - 40,
    marginTop: 60,
  },
  buttonContainer: {
    justifyContent: 'flex-end',
    alignItems: 'stretch',
    alignSelf: 'stretch',
    paddingLeft: 30,
    paddingRight: 30,
    paddingBottom: 20,
    gap: 12,
  },
  continueButton: {
    width: screenWidth - 40,
    backgroundColor: '#00FFBD',
    height: 60,
    borderRadius: 15,
  },
  backButton: {
    width: screenWidth - 40,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  backButtonText: {color: '#00FFBD', fontSize: 18, letterSpacing: 2.16},
  handImage: {height: 60, width: 60},
  card: {
    backgroundColor: '#535364',
    borderWidth: 2,
    flexDirection: 'row',
    alignSelf: 'stretch',
    alignItems: 'center',
    gap: 22,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
}
