import React from 'react'
import {View, Image, Linking, Dimensions} from 'react-native'
import {Button, TextBold, TextReg} from '../../../../components'

const {width: ScreenWidth} = Dimensions.get('window')

const RefinanceApprovalCard = props => {
  const contactUs = () => {
    Linking.openURL(`mailto:<EMAIL>?subject=${props.subject}`)
  }

  return (
    <View
      style={{
        marginHorizontal: 14,
        alignSelf: 'stretch',
        alignItems: 'center',
        marginTop: 40,
        justifyContent: 'space-between',
        flex: 1,
      }}>
      <View
        style={{
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#3D3D50',
          width: 140,
          height: 140,
          borderRadius: 70,
        }}>
        <Image source={props.icon} style={{height: 57, width: 57}} resizeMode="contain" />
      </View>
      <TextBold style={{fontSize: 24, marginVertical: '8%', textAlign: 'center'}}>
        {props.title}
      </TextBold>
      <TextReg style={{fontSize: 16, marginBottom: '12%', textAlign: 'center'}}>
        {props.description}
      </TextReg>
      <TextReg
        style={{fontSize: 16, marginBottom: 24, textAlign: 'center', width: ScreenWidth - 140}}>
        Have a question in the meantime? Get it touch with us.
      </TextReg>
      <Button
        style={{
          alignSelf: 'stretch',
          height: 60,
        }}
        onPress={() => contactUs()}>
        <TextBold style={{color: '#000', fontSize: 18, letterSpacing: 2}}>CONTACT SUPPORT</TextBold>
      </Button>
    </View>
  )
}

export default RefinanceApprovalCard
