import React, {useState} from 'react'
import {View, Image, TouchableOpacity, SafeAreaView, Keyboard, Dimensions} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import {useNavigation} from '@react-navigation/native'
import {TextBold, TextReg, Button} from '../../../components'

const {width: screenWidth} = Dimensions.get('window')

const RefinanceType = ({close, route}) => {
  const dispatch = useDispatch()
  const navigation = useNavigation()
  const [refinanceType, setRefinanceType] = useState('')
  const [loading, setLoading] = useState(false)

  const user = useSelector(state => state.user.user)
  // const account = useSelector(state => state.auth.account)
  // const WebService = useSelector(state => state.auth.WebService)

  const accountId = route?.params?.accountId
  const selectedAccount =
    user?.accounts?.find(
      a => a.loans?.length > 0 && a.loans[0]?.status === 'active' && a.accountId === accountId,
    ) || null

  const next = async () => {
    navigation.navigate('RepaymentType', {...route?.params, refinanceType})
  }
  console.log('route: ', route)

  return (
    <SafeAreaView style={local.box}>
      <View>
        <View
          style={{
            flexDirection: 'row',
            alignSelf: 'stretch',
            justifyContent: 'space-between',
            marginTop: 5,
          }}>
          <TouchableOpacity onPress={() => navigation?.goBack()} style={{height: 40, width: 40}}>
            <Image
              source={require('../../../imgs/backToSettings.png')}
              style={{height: 25, width: 20}}
            />
          </TouchableOpacity>
          <TextReg style={{color: '#FFF', fontSize: 18}}>Refinance</TextReg>
          <View style={{height: 40, width: 40}} />
        </View>
        <View style={local.descriptionBox}>
          <TextReg style={local.actionTitleTxt}>Type of Refinance:</TextReg>

          <TouchableOpacity
            onPress={() => {
              Keyboard.dismiss()
              setRefinanceType('money')
            }}
            activeOpacity={0.5}
            style={{
              ...local.card,
              borderColor: refinanceType === 'money' ? '#00FFBD' : 'transparent',
            }}>
            <Image source={require('../../../imgs/piggybank.png')} style={local.handImage} />

            <View style={{flex: 1, gap: 17}}>
              <TextBold style={{fontSize: 16}}>I Want More Money</TextBold>
              <TextReg style={{fontSize: 12}}>
                You will still extend your term and get a new rate by selecting this option.
              </TextReg>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              Keyboard.dismiss()
              setRefinanceType('extend')
            }}
            activeOpacity={0.5}
            style={{
              ...local.card,
              borderColor: refinanceType === 'extend' ? '#00FFBD' : 'transparent',
            }}>
            <Image source={require('../../../imgs/clock2.png')} style={local.handImage} />
            <View style={{flex: 1, gap: 17}}>
              <TextBold style={{fontSize: 16}}>I Want to Extend My Term or Get a New Rate</TextBold>
              <TextReg style={{fontSize: 12}}>
                If you do not want more money, please select this option.
              </TextReg>
            </View>
          </TouchableOpacity>
        </View>
      </View>

      <View style={local.buttonContainer}>
        <Button
          isLoading={loading}
          disabled={refinanceType === ''}
          onPress={() => next()}
          style={local.continueButton}>
          <TextBold style={{color: refinanceType === '' ? '#FFF' : '#000', letterSpacing: 2.16}}>
            CONTINUE
          </TextBold>
        </Button>
        <Button isLoading={loading} onPress={() => navigation?.goBack()} style={local.backButton}>
          <TextBold style={local.backButtonText}>BACK</TextBold>
        </Button>
      </View>
    </SafeAreaView>
  )
}

export default RefinanceType

let local = {
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
  actionTitleTxt: {
    marginTop: 20,
    fontSize: 18,
    marginBottom: 32,
  },
  descriptionBox: {
    width: screenWidth - 40,
    marginTop: 60,
  },
  buttonContainer: {
    justifyContent: 'flex-end',
    alignItems: 'stretch',
    alignSelf: 'stretch',
    paddingLeft: 30,
    paddingRight: 30,
    paddingBottom: 20,
    gap: 12,
  },
  continueButton: {
    width: screenWidth - 40,
    backgroundColor: '#00FFBD',
    height: 60,
    borderRadius: 15,
  },
  backButton: {
    width: screenWidth - 40,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  backButtonText: {color: '#00FFBD', fontSize: 18, letterSpacing: 2.16},
  handImage: {height: 60, width: 60},

  card: {
    backgroundColor: '#535364',
    borderWidth: 2,
    flexDirection: 'row',
    alignSelf: 'stretch',
    alignItems: 'center',
    gap: 22,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
}
