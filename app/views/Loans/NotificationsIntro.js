import React from 'react'
import { View, Text, TouchableOpacity, Image } from 'react-native'

import { TextBold } from '../../components'
import styles from './styles'
import notificationsBell from '../../imgs/notifBell-white2.png'
import checkmarkCircle from '../../imgs/checkmark.png'

const NotificationsIntro = (props) => (
  <View style={styles.notificationModalContainer}>
    <Image
      source={notificationsBell}
      style={{
        height: 90,
        width: 79,
        marginBottom: 24
      }}
    />
    <TextBold style={styles.notificationTitle}>Get Updates</TextBold>
    <Text style={styles.notificationDescription}>Before we start, SALT would like to send you updates about:</Text>

    <View style={{ flexDirection: 'row', width: 126 }}>
      <Image source={checkmarkCircle} style={{ height: 20, width: 20 }} />
      <TextBold style={styles.notificationListItem}>Loan Status</TextBold>
    </View>

    <View style={{ flexDirection: 'row', width: 126 }}>
      <Image source={checkmarkCircle} style={{ height: 20, width: 20 }} />
      <TextBold style={styles.notificationListItem}>Margin Calls</TextBold>
    </View>

    <View style={{ flexDirection: 'row', width: 126 }}>
      <Image source={checkmarkCircle} style={{ height: 20, width: 20 }} />
      <TextBold style={styles.notificationListItem}>Liquidation</TextBold>
    </View>

    <TouchableOpacity
      style={styles.notificationModalActivatePromptButton}
      onPress={() => props.requestPushNotifsPermission()}
    >
      <TextBold style={styles.notificationModalActivatePromptText}>TURN ON NOTIFICATIONS</TextBold>
    </TouchableOpacity>

    {props.savedPushPermission === 'later' ? (
      <TouchableOpacity onPress={() => props.notificationsRequestNever()}>
        <TextBold style={styles.notificationNotNow}>Never</TextBold>
      </TouchableOpacity>
    ) : (
      <TouchableOpacity onPress={() => props.notificationsRequestLater()}>
        <TextBold style={styles.notificationNotNow}>Not Now</TextBold>
      </TouchableOpacity>
    )}
  </View>
)

export default NotificationsIntro
