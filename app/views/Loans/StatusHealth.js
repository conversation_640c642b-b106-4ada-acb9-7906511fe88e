import React, {Component} from 'react'
import {View, Image, TextInput, TouchableOpacity} from 'react-native'
import {connect} from 'react-redux'
import AsyncStorage from '@react-native-async-storage/async-storage'

import {tourCount} from '../../store/user/user.actions'
import {TextBold, Card, TextReg, Button} from '../../components'

import styles from './styles'

class StatusHealth extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  openTour = async type => {
    if (type == 'warning') {
      await AsyncStorage.setItem('WARNING_TOUR', 'false')
    }
    if (type == 'convert') {
      await AsyncStorage.setItem('CONVERT_TOUR', 'false')
    }
    if (type == 'stabilization') {
      await AsyncStorage.setItem('STABILIZATION_TOUR', 'false')
    }
    if (type == 'margin') {
      await AsyncStorage.setItem('MARGIN_TOUR', 'false')
    }
    if (type == 'healthy') {
      await AsyncStorage.setItem('HEALTHY_TOUR', 'false')
    }
    this.props.dispatch(tourCount())
  }

  goToManage = () => {
    this.props.navigation.navigate('ManageLtv')
  }

  render() {
    const {ltv, thresholds} = this.props.loanData
    let collateralValue = this.props.loanData.collateralValue
    collateralValue = Number(collateralValue).toFixed(2)
    const {accountRef} = this.props
    const canRequestConversion = this.props?.user?.accounts?.filter(a => a.ref == accountRef)[0]
      ?.product?.loan?.canRequestConversion
    const marginCallThresh = thresholds?.marginCall
    let showConvertBack = false
    if (Number(ltv) < Number(marginCallThresh) && canRequestConversion) {
      showConvertBack = true
    }
    const marginCure = Number(thresholds?.marginCure) * 100
    let liquidationNumber = Number(thresholds?.liquidation) * 100
    liquidationNumber = liquidationNumber.toFixed(2)

    const isStabilized = this.props.user?.accounts?.filter(a => a.ref == this.props.accountRef)[0]
      ?.product?.loan?.isStabilized

    const awaitingLiquidation = this.props.loanData?.awaitingLiquidation || false
    const forStabilization = this.props.loanData?.marginManagementPreference === 'stabilization'

    let launchDarkly = this.props.launchDarkly
    let warningQ = launchDarkly['show-warning-tour'] || false
    let healthyQ = launchDarkly['show-healthy-loan-tour'] || false
    let marginQ = launchDarkly['show-margin-call-tour'] || false
    let stabilizationQ = launchDarkly['show-stabilization-tour'] || false
    let convertQ = launchDarkly['show-convert-tour'] || false

    const showCollateralValue = (
      <TextBold
        style={{
          color: '#00FFBD',
          textDecorationLine: 'underline',
        }}>{`$${collateralValue}`}</TextBold>
    )

    let showHealth = (
      <View style={{alignSelf: 'stretch'}}>
        <View style={styles.statusBox}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <TextBold style={{...styles.statusTitle, marginBottom: 2}}>{`Loan Healthy`}</TextBold>
            {healthyQ && (
              <TouchableOpacity
                onPress={() => this.openTour('healthy')}
                style={{
                  height: 20,
                  width: 20,
                  backgroundColor: '#00FFBD',
                  borderRadius: 10,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginLeft: 8,
                }}>
                <TextReg style={{color: '#000'}}>{`?`}</TextReg>
              </TouchableOpacity>
            )}
          </View>
          <Image
            source={require('../../imgs/graphics/appleLTV.png')}
            style={styles.statusImgApple}
          />
        </View>
        <TextReg style={styles.statusMain}>
          Your LTV is within the healthy range, keep monitoring your loan and don’t forget your
          monthly payments.
        </TextReg>
      </View>
    )

    if (awaitingLiquidation) {
      showHealth = (
        <View style={{alignSelf: 'stretch'}}>
          <View style={[styles.statusCover, {backgroundColor: '#afafaf'}]} />
          <View style={styles.statusBox}>
            <TextBold style={styles.statusTitle}>{`Stabilization Pending`}</TextBold>
            <Image
              source={require('../../imgs/loanHealth/lifePreserver.png')}
              style={styles.statusImg}
            />
          </View>
          <TextReg style={styles.statusMain}>
            {forStabilization
              ? `Your account is currently being stabilized. Once the stabilization is complete, you will be eligible to manage your LTV by
            depositing more collateral or paying down your principal balance.`
              : `Your account is currently being liquidated. Once the liquidation is complete, it cures your LTV into a healthy state by immediately applying the proceeds of the sale of your collateral to the principal amount due on the loan.`}
          </TextReg>
        </View>
      )
    } else if (showConvertBack) {
      showHealth = (
        <View style={{alignSelf: 'stretch'}}>
          <View
            style={[
              styles.statusCover,
              {
                backgroundColor: '#989898',
              },
            ]}
          />
          <View style={styles.statusBox}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <TextBold style={{...styles.statusTitle, marginBottom: 2}}>
                Eligible to Convert
              </TextBold>
              {convertQ && (
                <TouchableOpacity
                  onPress={() => this.openTour('convert')}
                  style={{
                    height: 20,
                    width: 20,
                    backgroundColor: '#00FFBD',
                    borderRadius: 10,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginLeft: 8,
                  }}>
                  <TextReg style={{color: '#000'}}>{`?`}</TextReg>
                </TouchableOpacity>
              )}
            </View>

            <Image
              source={require('../../imgs/loanHealth/lifePreserver.png')}
              style={styles.statusImg}
            />
          </View>
          <TextReg style={styles.statusMain}>
            Please note, this is a one-time conversion. You recently went through a loan
            stabilization during which your collateral was converted into USDC. You are now able to
            convert back into your original collateral assets.
          </TextReg>
          <View style={styles.statusButtonBox}>
            <Button
              style={{alignSelf: 'stretch', marginTop: 8}}
              onPress={() => this.props.goToConvert()}>
              <TextBold style={{fontSize: 16, color: '#000'}}>CONVERT</TextBold>
            </Button>
          </View>
        </View>
      )
    } else if (isStabilized) {
      showHealth = (
        <View style={{alignSelf: 'stretch'}}>
          <View style={[styles.statusCover, {backgroundColor: '#afafaf'}]} />
          <View style={styles.statusBox}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <TextBold
                style={{
                  ...styles.statusTitle,
                  marginBottom: 2,
                }}>
                You've been Stabilized!
              </TextBold>
              {stabilizationQ && (
                <TouchableOpacity
                  onPress={() => this.openTour('stabilization')}
                  style={{
                    height: 20,
                    width: 20,
                    backgroundColor: '#00FFBD',
                    borderRadius: 10,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginLeft: 8,
                  }}>
                  <TextReg style={{color: '#000'}}>{`?`}</TextReg>
                </TouchableOpacity>
              )}
            </View>
            <Image
              source={require('../../imgs/loanHealth/lifePreserver.png')}
              style={styles.statusImg}
            />
          </View>
          <TextReg style={styles.statusMain}>
            Your LTV has exceeded {liquidationNumber}%. A Stabilization Event has occurred. Your
            collateral value is estimated at {showCollateralValue}. To be eligible to convert back
            to collateral mode, please manage your LTV.
          </TextReg>
          <View style={styles.statusButtonBox}>
            <Button style={{alignSelf: 'stretch'}} onPress={() => this.goToManage()}>
              <TextBold style={{fontSize: 16, color: '#000'}}>MANAGE MY LTV</TextBold>
            </Button>
          </View>
        </View>
      )
    } else if (ltv < this.props.loanData.thresholds?.warning) {
      //default
    } else if (ltv < this.props.loanData.thresholds?.marginCall) {
      showHealth = (
        <View style={{alignSelf: 'stretch'}}>
          <View
            style={[
              styles.statusCover,
              {
                backgroundColor: '#f7d956',
              },
            ]}
          />
          <View style={styles.statusBox}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <TextBold style={{...styles.statusTitle, marginBottom: 2}}>
                Margin Call Warning
              </TextBold>
              {warningQ && (
                <TouchableOpacity
                  onPress={() => this.openTour('warning')}
                  style={{
                    height: 20,
                    width: 20,
                    backgroundColor: '#00FFBD',
                    borderRadius: 10,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginLeft: 8,
                  }}>
                  <TextReg style={{color: '#000'}}>{`?`}</TextReg>
                </TouchableOpacity>
              )}
            </View>
            <Image
              source={require('../../imgs/loanHealth/warningLoan.png')}
              style={styles.statusImgWarning}
            />
          </View>
          <TextReg style={styles.statusMain}>
            Your loan status has moved into the margin call warning zone. A margin call notice may
            be issued soon. Please continue to monitor your account closely.
          </TextReg>
          <View style={styles.statusButtonBox}>
            <Button style={{alignSelf: 'stretch'}} onPress={() => this.goToManage()}>
              <TextBold style={{fontSize: 16, color: '#000'}}>MANAGE MY LTV</TextBold>
            </Button>
          </View>
        </View>
      )
    } else if (ltv < this.props.loanData.thresholds?.liquidation) {
      showHealth = (
        <View style={{alignSelf: 'stretch'}}>
          <View
            style={[
              styles.statusCover,
              {
                backgroundColor: '#e5705a',
              },
            ]}
          />
          <View style={styles.statusBox}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <TextBold
                style={{...styles.statusTitle, marginBottom: 2}}>{`Margin Trigger`}</TextBold>
              {marginQ && (
                <TouchableOpacity
                  onPress={() => this.openTour('margin')}
                  style={{
                    height: 20,
                    width: 20,
                    backgroundColor: '#00FFBD',
                    borderRadius: 10,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginLeft: 8,
                  }}>
                  <TextReg style={{color: '#000'}}>{`?`}</TextReg>
                </TouchableOpacity>
              )}
            </View>
            <Image
              source={require('../../imgs/loanHealth/alertLoan.png')}
              style={styles.statusImg}
            />
          </View>

          <TextReg style={styles.statusMain}>
            Deposit more assets or pay down your balance to {marginCure}% LTV within 48 hours to
            avoid collateral {forStabilization ? 'stabilization' : 'liquidation'}.
          </TextReg>
          <View style={styles.statusButtonBox}>
            <Button style={{alignSelf: 'stretch'}} onPress={() => this.goToManage()}>
              <TextBold style={{fontSize: 16, color: '#000'}}>MANAGE MY LTV</TextBold>
            </Button>
          </View>
        </View>
      )
    } else {
      showHealth = (
        <View style={{alignSelf: 'stretch'}}>
          <View style={[styles.statusCover, {backgroundColor: '#afafaf'}]} />
          <View style={styles.statusBox}>
            <TextBold style={styles.statusTitle}>
              {forStabilization ? 'Stabilization' : 'Liquidation'} in Process
            </TextBold>
            <Image
              source={require('../../imgs/loanHealth/lifePreserver.png')}
              style={styles.statusImg}
            />
          </View>
          <TextReg style={styles.statusMain}>
            Your LTV has exceeded {liquidationNumber}%. A{' '}
            {forStabilization ? 'Stabilization' : 'Liquidation'} Event is in progress. Your
            collateral value is estimated at {showCollateralValue}. To be eligible to convert back
            to collateral mode, please manage your LTV.
          </TextReg>
        </View>
      )
    }

    return (
      <View>
        <Card cardMarginBottom={0} marginTop={8}>
          {showHealth}
        </Card>
      </View>
    )
  }
}

StatusHealth.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  accountRef: state.auth.account.ref,
  loanData: state.user.loanData || {},
  WebService: state.auth.WebService,
  user: state.user.user,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(StatusHealth)
