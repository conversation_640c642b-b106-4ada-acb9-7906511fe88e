import React, {Component} from 'react'
import {View, <PERSON>Bar, ScrollView, RefreshControl, Animated, Linking} from 'react-native'
import {connect} from 'react-redux'
import {BigNumber} from 'bignumber.js'
import AsyncStorage from '@react-native-async-storage/async-storage'

import {updateActiveTabListener, numberWithCommas, dig} from '../../util/helpers'
import {derivedStatusMap} from '../../util/enumerables'

import {logout} from '../../store/auth/auth.actions'
import {screenView} from '../../store/analytics/analytics.actions'
import {showNotifications} from '../../store/notifications/notifications.actions'
import {
  updateLoans,
  updateWallets,
  showDepositModal,
  increaseRefreshDataCount,
  updateBanks,
  pauseUnit21,
} from '../../store/user/user.actions'

import styles from './styles'
import commonStyles from '../../styles/commonStyles'
import {BackgroundHeader} from '../../components'
import HelpModal from './HelpModal'
import DepositModal from './DepositModal'
import NoLoansBox from './NoLoansBox'
import LoanStatus from './LoanStatus'
import NonLendableLoan from './NonLendableLoan'
import CancelLoanRequest from '../Home/CancelLoanRequest'
import LoanRequestDetails from '../Home/LoanRequestDetails'
import LoanBalance from './LoanBalance'
import LoanPayments from './LoanPayments'
import PaymentDue from './PaymentDue'
import WalletsBox from './WalletsBox'
import PaymentCombined from './PaymentCombined'
import EntityWarn from './EntityWarn'

import StackWise from './StackWise'
import EditBanksBox from './EditBanksBox'
import StatusHealth from './StatusHealth'
import Unit21Flow from '../Home/Unit21/Unit21Flow'
import ContinueUnit21Box from '../Home/Unit21/ContinueUnit21Box'
import CaNotice from './CaNotice'
import MarginCallSettingsBox from './MarginCallSettingsBox'

export class LoanScreen extends Component {
  constructor() {
    super()
    this.state = {
      infoToggleState: 0,
      helpModalVisable: false,
      connectedModalVisable: false,
      depositModalVisible: false,
      refreshing: false,
      firstTimeLoading: false,
      creatingWallet: false,
      pendingID: false,
      resetMFA: false,
      twoFactorSkip: true,
      loadingCancel: false,
      isPending: false,
    }

    this.xOffset = 0
    this.scrollValue = new Animated.Value(0)
  }

  componentDidMount() {
    updateActiveTabListener(this.props.navigation)
    this.props.navigation.setParams({
      toggleNotifications: this.toggleNotifications,
    })

    this.props.dispatch(screenView('Loan Screen'))
    if (this.props.showDepositModal) {
      this.toggleDepositModal()
      this.props.dispatch(showDepositModal(false))
    }
    this.getBanks()
  }

  componentDidUpdate(prevProps) {
    if (prevProps.showDepositModal !== this.props.showDepositModal && this.props.showDepositModal) {
      this.toggleDepositModal()
      this.props.dispatch(showDepositModal(false))
    }

    if (prevProps.loanData?.id !== this.props.loanData?.id) {
      console.log('loan id change')
      if (this.props.loanData.status === 'active') {
        this.setState({isPending: false})
      } else if (derivedStatusMap.get(this.props.loanData.status) === 'pending') {
        this.setState({isPending: true})
      } else {
        this.setState({isPending: false})
      }
    }

    if (this.props.refreshCount !== prevProps.refreshCount) {
      this.getLoansData()
    }

    if (
      prevProps.route?.params?.nextPage != this.props.route?.params?.nextPage &&
      this.props.route?.params?.nextPage.includes('unit21')
    ) {
      this.continueUnit21()
    }

    if (prevProps.postNewLoan != this.props.postNewLoan) {
      this.continueUnit21()
    }
  }

  getBanks = () => {
    this.props.WebService.getBank(this.props.loanData.id)
      .then(res => {
        this.props.dispatch(updateBanks(res.data))
      })
      .catch(err => {
        console.log('get banks', err)
      })
  }

  get neededEth() {
    const {loanData, prices} = this.props
    return loanData
      ? new BigNumber(loanData.loanValue - 0.7 * loanData.collateralValue)
          .dividedBy(prices['ETH-USD'].price)
          .toFormat(4)
      : 0
  }

  get neededBtc() {
    const {loanData, prices} = this.props
    return loanData
      ? new BigNumber(loanData.loanValue - 0.7 * loanData.collateralValue)
          .dividedBy(prices['BTC-USD'].price)
          .toFormat(4)
      : 0
  }

  onScrollEnd = index => {
    this.setState({infoToggleState: index})
  }

  toggleToLTV = () => {
    this.refs.swiper._scrollToIndex(0)
    this.setState({infoToggleState: 0})
  }

  toggleToInfo = () => {
    this.refs.swiper._scrollToIndex(1)
    this.setState({infoToggleState: 1})
  }

  toggleDepositModal = () => {
    this.setState({depositModalVisible: !this.state.depositModalVisible})
  }

  toggleHelpModal = () => {
    this.setState({helpModalVisable: !this.state.helpModalVisable})
  }

  toggleConnectedModal = () => {
    this.setState({connectedModalVisable: !this.state.connectedModalVisable})
  }

  goToPayment = () => {
    this.props.navigation.navigate('OneTimeOptions')
  }

  continueToDeposit = (price, value, token) => {
    const hasWallet = this.props.loanData.collaterals.filter(a => a.currency === token)[0]

    //if doesnt have a wallet
    if (!hasWallet || !hasWallet.address) {
      // we assume ID is complete because loan is active
      this.setState({creatingWallet: true})
      this.props.WebService.createWallet(token)
        .then(res => {
          this.props.dispatch(updateWallets(res.data))
          this.props.navigation.navigate('Detail', {
            title: token,
            depositActive: true,
          })
          this.setState({depositModalVisible: false, creatingWallet: false})
        })
        .catch(err => {
          this.setState({creatingWallet: false})
        })
    } else {
      this.props.navigation.navigate('Detail', {
        title: token,
        depositActive: true,
      })
      this.setState({depositModalVisible: false})
    }
  }

  onScroll = e => {
    this.xOffset = e.nativeEvent.contentOffset.x

    Animated.timing(this.scrollValue, {
      toValue: this.xOffset,
      useNativeDriver: true,
      duration: 0,
    }).start()
  }

  goToWallet = item => {
    this.props.navigation.navigate('Detail', {title: item.ticker})
  }

  toggleNotifications = () => {
    this.props.dispatch(showNotifications(true))
  }

  getLoansData = async () => {
    this.setState({refreshing: true})
    this.props.WebService.getLoans()
      .then(async res => {
        const loanStatus = derivedStatusMap.get(dig(res.data[0], 'status'))
        if (res.data.length > 0 && loanStatus === 'active') {
          await this.props.dispatch(updateLoans(res.data[0]))
        } else if (loanStatus === 'pending') {
          await this.props.dispatch(updateLoans(res.data[0]))
          //this.setState({ pendingLoan: true })
        }
        if (res.data.length === 0) {
          this.props.dispatch(updateLoans({status: 'none'}))
          this.setState({noLoans: true})
        }
        //this.getWallets();
        this.setState({refreshing: false})
      })
      .catch(err => {
        console.log('err', err)
        this.setState({
          refreshing: false,
          connectedModalVisable: true,
        })
        if (err && err.status === 401) {
          this.props.dispatch(logout())
        }
      })
  }

  /*
  getWallets = () => {
    this.props.WebService.getWallets(this.props.accountRef)
      .then(res => {
        res.data.map(wallet => this.props.dispatch(updateWallets(wallet)));
        if (res.data.length === 0) {
          this.props.dispatch(updateWallets([]));
        }
      })
      .catch(err => {
        throw err;
      });
  };
  */

  checkTwoFactorSetup = async () => {
    const twoFactorSkip = await AsyncStorage.getItem(
      `TWO_FACTOR_SKIP-${this.props.user.primaryEmail}`,
    )
    if (twoFactorSkip || this.props.user.mfaEnabled) {
      this.setState({twoFactorSkip: true})
    } else {
      this.setState({twoFactorSkip: false})
    }
  }

  goToTwoFactor = () => {
    this.props.navigation.navigate('TwoFactor')
    AsyncStorage.setItem(`TWO_FACTOR_SKIP-${this.props.user.primaryEmail}`, 'true')
    this.setState({twoFactorSkip: true})
  }

  skipTwoFactor = () => {
    AsyncStorage.setItem(`TWO_FACTOR_SKIP-${this.props.user.primaryEmail}`, 'true')
    this.setState({twoFactorSkip: true})
  }

  openLoanRequest = () => {
    let product = {
      id: this.props?.route?.params?.id,
      ref: this.props?.route?.params?.ref,
      nextPage: '',
    }
    this.props.navigation.navigate('LoanRequest', product)
  }

  cancelNonLendable = id => {
    this.setState({loadingCancel: true})
    this.props.WebService.cancelNonLendable(id)
      .then(res => {
        this.setState({loadingCancel: false})
        this.props.dispatch(increaseRefreshDataCount())
      })
      .catch(err => {
        this.setState({loadingCancel: false})
      })
  }

  goToWallets = ref => {
    this.props.navigation.navigate('Collateral', {ref})
  }

  goToSettings = ref => {
    this.props.navigation.navigate('MarginCallSettings')
  }

  goToBanks = () => {
    this.props.navigation.navigate('Banking')
  }

  gotoStackwiseRewards = () => {
    this.props.navigation.navigate('StackwiseRewards', {getLoansData: this.getLoansData})
  }

  depositCollateral = () => {
    this.props.dispatch(showDepositModal(true))
  }

  goToConvert = () => {
    this.props.navigation.navigate('Convert')
  }

  continueUnit21 = (verify = false) => {
    //AsyncStorage.removeItem('PAUSE_UNIT_21');
    //this.props.dispatch(pauseUnit21(false));
    //console.log('navigatTo xd', this.props.route);
    //let routeKey = this.props.route?.key;
    if (verify) {
      this.props.navigation.navigate('Unit21Flow', {custom: 'LoanPayout'})
    } else {
      this.props.navigation.navigate('Unit21Flow')
    }
    /*
    {!showPinIntro && showUnit21 && (
      <Unit21Flow navigation={this.props.navigation} />
    )}
    */
  }

  render() {
    console.log('loanData', this.props.loanData)
    let productRef = this.props?.route?.params?.ref

    const userAccounts = this.props.user?.accounts || ['test']
    const account = userAccounts.filter(a => a.ref == productRef)[0]

    const nonLendableLoanReqId = false //account?.product?.loan?.nonLendableLoanReqId

    const showPinIntro = this.props.storedPin === '0' || this.props.backToSettings

    let isPending =
      derivedStatusMap.get(this.props.loanData?.status) === 'pending' || this.state.isPending

    /*
    const loanData = this.props.user?.accounts.filter(
      a => a.ref == productRef,
    )[0]?.product?.loan;
    */
    let loanData = account?.loans ? account?.loans[0] : []
    //let loanData = this.props.loanData;
    if (!loanData) {
      loanData = []
    }

    let stackwiseTotalRewards = 0
    if (loanData?.reward?.rewardSchedule?.length > 0) {
      loanData?.reward?.rewardSchedule.map(a => {
        if (a.earned) {
          stackwiseTotalRewards += Number(a.rewardInUsd)
        }
      })
    }
    stackwiseTotalRewards = stackwiseTotalRewards.toFixed(2)

    let currentBalance = ''
    if (loanData?.amortizationInfo) {
      currentBalance = loanData?.amortizationInfo.currentBalance
    }
    const displayLoanValue = numberWithCommas(Number(currentBalance).toFixed(2))

    let loanMainContent
    let noData = loanData?.length == 0 || (loanData?.status === 'none' && !nonLendableLoanReqId)
    let showWalletsMain = !noData && !nonLendableLoanReqId && !isPending

    let rewardsSoon =
      this.props?.loanData.reward?.currentDisbursementPeriod == 1 &&
      Number(this.props?.loanData.reward?.nextRewardAmount) > 0

    const loan = this.props.loanData
    const stabilizationOn = loan?.marginManagementPreference === 'stabilization'
    const currentAmount = new BigNumber(loan?.amortizationInfo?.currentBalance)
    const collatValProj = new BigNumber(currentAmount).dividedBy(0.9091)
    const feePercent = new BigNumber(0.05)

    const principleAmountNeeded = ltvGoal => {
      return ltvGoal
        .times(currentAmount.dividedBy(0.9091))
        .minus(currentAmount)
        .dividedBy(ltvGoal.times(feePercent.plus(1)).minus(1))
    }

    const suggestedDepositPrinciple = principleAmountNeeded(
      new BigNumber(loan.thresholds?.marginCure || 0.7),
    )
    const cureAmount = suggestedDepositPrinciple.gt(0)
      ? suggestedDepositPrinciple
      : new BigNumber(0)
    const marginManagementPreference = loan?.marginManagementPreference
    const stabilizationFee = new BigNumber(collatValProj).multipliedBy(0.03)
    const liquidationFee = new BigNumber(cureAmount).multipliedBy(0.05)

    const totalLiquidationAmount = cureAmount.plus(liquidationFee)
    const postStabilizationCollateral = new BigNumber(collatValProj).minus(stabilizationFee)
    const postLiquidationCollateral = new BigNumber(collatValProj).minus(totalLiquidationAmount)

    if (noData) {
      loanMainContent = <NoLoansBox openLoanRequest={this.openLoanRequest} />
    } else if (nonLendableLoanReqId) {
      loanMainContent = (
        <NonLendableLoan
          loadingCancel={this.state.loadingCancel}
          cancelNonLendable={this.cancelNonLendable}
          nonLendableLoanReqId={nonLendableLoanReqId}
        />
      )
    } else if (isPending) {
      loanMainContent = (
        <>
          <EntityWarn />
          <ContinueUnit21Box continueUnit21={this.continueUnit21} />
          <LoanRequestDetails loanData={loanData} navigation={this.props.navigation} />
          <CancelLoanRequest goBack={() => this.props.navigation.goBack()} id={loanData?.id} />
        </>
      )
    } else {
      loanMainContent = (
        <View style={{paddingBottom: 20}}>
          <StatusHealth
            depositCollateral={this.depositCollateral}
            goToConvert={this.goToConvert}
            navigation={this.props.navigation}
          />
          <LoanStatus
            toggleHelpModal={this.toggleHelpModal}
            toggleDepositModal={this.toggleDepositModal}
            goToPayment={this.goToPayment}
          />
          <MarginCallSettingsBox
            goToSettings={this.goToSettings}
            stabilizationOn={stabilizationOn}
            triggerAmount={collatValProj}
            postStabilizationValue={
              marginManagementPreference === 'stabilization'
                ? postStabilizationCollateral
                : postLiquidationCollateral
            }
          />
          <WalletsBox
            user={this.props.user}
            productRef={productRef}
            goToWallets={this.goToWallets}
            tokenPrices24h={this.props.tokenPrices24h}
          />
          {(stackwiseTotalRewards > 0 || rewardsSoon) && (
            <StackWise
              totalRewards={stackwiseTotalRewards}
              gotoStackwiseRewards={this.gotoStackwiseRewards}
            />
          )}
          <PaymentCombined navigation={this.props.navigation} getLoansData={this.getLoansData} />
          <LoanBalance displayLoanValue={displayLoanValue} />
          <LoanPayments
            amortizationInfo={loanData?.amortizationInfo}
            navigation={this.props.navigation}
          />
          <DepositModal
            title="Deposit Collateral"
            depositModalVisible={this.state.depositModalVisible}
            toggleDepositModal={this.toggleDepositModal}
            depositBtconPress={this.depositBtconPress}
            depositEthonPress={this.depositEthonPress}
            neededBtc={this.neededBtc}
            neededEth={this.neededEth}
            showPinScreen={this.props.showPinScreen}
            loanData={loanData}
            prices={this.props.prices}
            continueToDeposit={this.continueToDeposit}
            creatingWallet={this.state.creatingWallet}
          />
        </View>
      )
    }
    return (
      <View key={'loanScreen'} style={commonStyles.tileContainer}>
        <BackgroundHeader
          title={'My Loan'}
          toggleNotifications={this.toggleNotifications}
          goBack={() => this.props.navigation.goBack()}
          navigateSettings={() => this.props.navigation.navigate('Settings')}
        />
        <StatusBar barStyle="light-content" />
        <HelpModal
          title={'Loan Status'}
          text={
            'The Loan-to-Value (LTV) Ratio is a calculation used to assess the risk of a loan. LTV is calculated as the amount of the loan divided by the value of the collateral, expressed as a percentage. A loan with a higher LTV ratio is generally seen as higher risk and a loan with a lower LTV ratio as lower risk. The level of risk is directly tied to the likelihood that the collateral will need to be liquidated.'
          }
          helpModalVisable={this.state.helpModalVisable}
          toggleHelpModal={this.toggleHelpModal}
          showPinScreen={this.props.showPinScreen}
        />
        <HelpModal
          title={'Sorry'}
          testID="serverErrorModal"
          text={
            "Looks like we weren't able to connect to our server. \n\n Please try again in a few minutes."
          }
          pinIntroVisible={this.props.storedPin === '0'}
          helpModalVisable={this.state.connectedModalVisable}
          toggleHelpModal={this.toggleConnectedModal}
          showPinScreen={this.props.showPinScreen}
        />

        <ScrollView
          style={styles.swiperContainer}
          contentContainerStyle={{alignItems: 'center'}}
          refreshControl={
            <RefreshControl
              refreshing={this.state.refreshing}
              onRefresh={this.getLoansData}
              colors={['#28283D']}
              tintColor={'#fff'}
            />
          }>
          {!showWalletsMain && (
            <WalletsBox
              user={this.props.user}
              productRef={productRef}
              goToWallets={this.goToWallets}
              tokenPrices24h={this.props.tokenPrices24h}
            />
          )}
          <CaNotice user={this.props.user} productRef={productRef} />
          {loanMainContent}
        </ScrollView>
      </View>
    )
  }
}

LoanScreen.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  loanData: state.user.loanData || {},
  accountRef: state.auth.account.ref,
  prices: state.user.prices,
  fcmToken: state.auth.fcmToken,
  account: state.auth.account,
  storedPin: state.auth.pin,
  showPinScreen: state.auth.pinScreen,
  pauseUnit21: state.user.pauseUnit21,
  backToSettings: state.user.backToSettings,
  refreshCount: state.user.refreshCount,
  user: state.user.user,
  showDepositModal: state.user.showDepositModal,
  tokenPrices: state.user.prices,
  tokenPrices24h: state.user.prices24h,
  banksArr: state.user.banks,
  postNewLoan: state.user.postNewLoan,
})

export default connect(mapStateToProps)(LoanScreen)
