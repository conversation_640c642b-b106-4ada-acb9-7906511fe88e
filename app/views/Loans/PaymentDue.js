import React, {Component} from 'react';
import {View} from 'react-native';
import {connect} from 'react-redux';

import {TextBold, Card, TextReg, Button} from '../../components';

import {nextPaymentFormatDate} from '../../util/helpers';

import styles from './styles';

class PaymentDue extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  goToPayment = () => {
    this.props.navigation.navigate('OneTimeOptions');
  };

  render() {
    let nextPaymentAmount = this.props.loanData?.amortizationInfo?.nextPaymentAmount || null;
    nextPaymentAmount = Number(nextPaymentAmount).toFixed(2);

    let nextPaymentDate = this.props.loanData?.amortizationInfo?.nextPaymentDate || null;
    nextPaymentDate = nextPaymentFormatDate(nextPaymentDate);

    return (
      <Card marginTop={10}>
        <View style={{alignSelf: 'stretch', marginBottom: 12}}>
          <TextBold style={styles.statusTitle}>Payment Due</TextBold>
        </View>
        <View
          style={{
            flexDirection: 'row',
            alignSelf: 'stretch',
            justifyContent: 'space-between',
            marginLeft: 10,
            marginRight: 10,
            marginBottom: 24,
          }}>
          <View>
            <TextReg>Next Payment Due</TextReg>
            <TextBold>{`${nextPaymentDate}`}</TextBold>
          </View>
          <View>
            <TextReg>Next Payment Amount</TextReg>
            <TextBold>{`$${nextPaymentAmount}`}</TextBold>
          </View>
        </View>
        <View
          style={{
            marginBottom: 14,
            marginLeft: 10,
            alignSelf: 'stretch',
          }}>
          <Button style={{alignSelf: 'stretch'}} onPress={() => this.goToPayment()}>
            <TextBold
              style={{
                fontSize: 17,
                color: '#000',
                marginTop: 4,
                letterSpacing: 1,
              }}>
              MAKE A ONE TIME PAYMENT
            </TextBold>
          </Button>
        </View>
      </Card>
    );
  }
}

/*
<TextBold
  style={{
    paddingLeft: 10,
    fontSize: 16,
    color: '#00FFBD',
    marginBottom: 16,
    marginTop: 12,
    alignSelf: 'flex-start',
  }}
>
  HOW ARE MY PAYMENTS CALCULATED?
</TextBold>
*/

PaymentDue.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
});

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  WebService: state.auth.WebService,
});

export default connect(mapStateToProps)(PaymentDue);
