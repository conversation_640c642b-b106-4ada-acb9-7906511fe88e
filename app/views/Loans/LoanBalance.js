import React, {Component} from 'react';
import {View} from 'react-native';
import {connect} from 'react-redux';

import {Card, TextReg, TextBold} from '../../components';

import {dig, numberWithCommas} from '../../util/helpers';

import styles from './styles';

class LoanBalance extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    const currentLoan = this.props.loanData;

    let aprName = 'interestRate';
    if (currentLoan.amortizationInfo && currentLoan.amortizationInfo.apr) {
      aprName = 'apr';
    }

    let LTVInfoData = [
      {title: 'Next Payment Date', amount: '', key: 'item1'},
      {title: 'Monthly Payment Amount', amount: '', key: 'item2'},
      {title: 'Term Remaining', amount: '', key: 'item3'},
      {title: 'APR', amount: '', key: 'item4'},
      {title: 'Starting Amount', amount: '', key: 'item5'},
      {title: 'Loan Term', amount: '', key: 'item6'},
    ];

    if (currentLoan.amortizationInfo) {
      const nextPaymentDate = currentLoan.amortizationInfo.nextPaymentDate;
      let nextPaymentIndex = 0;

      const amortizationSchedule = currentLoan.amortizationInfo.amortizationSchedule.sort((a, b) => (a.date > b.date ? 1 : -1));

      amortizationSchedule.map((a, k) => {
        if (a.date === nextPaymentDate) {
          nextPaymentIndex = k;
        }
      });
      const termsRemaining = amortizationSchedule.length - nextPaymentIndex;

      let termsRemainingUnit = 'months';
      if (termsRemaining === 1) {
        termsRemainingUnit = 'month';
      }
      const loanTerm = dig(currentLoan, 'amortizationInfo', 'term') ? currentLoan.amortizationInfo.term : 0;
      let termUnits = 'months';
      if (loanTerm === '1') {
        termUnits = 'month';
      }

      const displayStartingAmount = dig(currentLoan, 'amortizationInfo', 'beginningBalance')
        ? numberWithCommas(Number(currentLoan.amortizationInfo.beginningBalance).toFixed(2))
        : 0;

      const displayNextPaymentAmount = dig(currentLoan, 'amortizationInfo', 'nextPaymentAmount')
        ? numberWithCommas(Number(currentLoan.amortizationInfo.nextPaymentAmount)?.toFixed(2))
        : 0;

      const displayAPR = (Number(currentLoan.apr) * 100).toFixed(2);

      let displayNextPaymentDate = dig(currentLoan, 'amortizationInfo')
        ? new Date(currentLoan.amortizationInfo.nextPaymentDate)
        : new Date();

      displayNextPaymentDate = displayNextPaymentDate.toLocaleDateString('en-US', {month: 'short', day: 'numeric', year: 'numeric'});

      LTVInfoData = [
        {
          title: 'Next Payment Date',
          amount: `${displayNextPaymentDate}`,
          key: 'item1',
        },
        {
          title: 'Monthly Payment Amount',
          amount: `$${displayNextPaymentAmount}`,
          key: 'item2',
        },
        {
          title: 'Term Remaining',
          amount: `${termsRemaining} ${termsRemainingUnit}`,
          key: 'item3',
        },
        {title: 'APR', amount: `${displayAPR}%`, key: 'item4'},
        {
          title: 'Starting Amount',
          amount: `$${displayStartingAmount}`,
          key: 'item5',
        },
        {
          title: 'Loan Term',
          amount: `${loanTerm} ${termUnits}`,
          key: 'item6',
        },
      ];
    }

    const ShowLTVInfo = LTVInfoData.map((item, k) => (
      <View style={styles.ltvInfoItem} key={k}>
        <TextReg style={styles.ltvInfoItemText}>{item.title}</TextReg>
        <TextReg style={styles.ltvInfoItemText}>{item.amount}</TextReg>
      </View>
    ));

    return (
      <Card>
        <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
          <TextReg style={{fontSize: 20, margin: 10}}>Loan Balance</TextReg>
          <TextBold style={styles.ltvInfoHeaderAmount}>{`$${this.props.displayLoanValue}`}</TextBold>
          {ShowLTVInfo}
          <View style={styles.ltvInfoDisclaimerBox}>
            <TextReg style={styles.ltvInfoDisclaimer}>Loan balance and remaining term updates may be delayed.</TextReg>
          </View>
        </View>
      </Card>
    );
  }
}

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
});

export default connect(mapStateToProps)(LoanBalance);
