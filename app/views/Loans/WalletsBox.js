import React from 'react'
import {View, Image, ScrollView, FlatList, TouchableOpacity} from 'react-native'

import {TextBold, Card, TextReg, Button} from '../../components'
import {numberWithCommas} from '../../util/helpers'
import {getTokenPic, getDepositCollateralPic} from '../../util/tokens'

import styles from './styles'

const WalletsBox = props => {
  let {user, productRef} = props
  let wallets = user?.allWallets[productRef - 1]
  let sumValue = 0
  let sum24Value = 0

  wallets?.map(a => {
    let {currency} = a
    let titleUSD = `${currency}-USD`
    let price24h = props.tokenPrices24h[titleUSD]?.price
    let value24h = price24h * Number(a.projectedBalance)
    sum24Value += value24h
    sumValue += Number(a.value)
  })
  let showSumValue = numberWithCommas(sumValue.toFixed(2))
  let difference = ((sumValue - sum24Value) / sum24Value) * 100 || 0
  let difColor = '#00FFBD'
  let difSymbol = '+'
  if (difference < 0) {
    difColor = '#E5705A'
    difSymbol = ''
  }
  if (difference == 0) {
    difSymbol = ''
    difColor = '#999'
  }
  let showDifference = difference.toFixed(2)

  let hasValue = wallets?.filter(a => a.value > 0)
  let orderValue = hasValue.sort((a, b) => Number(b.value) - Number(a.value))

  let showTokens = orderValue.map((a, k) => {
    let showImg = getDepositCollateralPic(a.currency)
    return <Image key={k} style={styles.walletBoxTokens} source={showImg} />
  })

  let showDots = showTokens.length > 5

  let tokensLength = showTokens.length
  if (showDots) {
    showTokens = showTokens.filter((a, k) => k < 4)
  } else {
    showTokens = showTokens.filter((a, k) => k < 5)
  }

  let showDiff = tokensLength - 4

  return (
    <Card cardMarginBottom={2} marginTop={0}>
      <TouchableOpacity
        style={{alignSelf: 'stretch', padding: 10}}
        onPress={() => props.goToWallets(productRef)}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          <View
            style={{
              flexDirection: 'column',
              alignSelf: 'stretch',
            }}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignSelf: 'stretch',
                marginBottom: wallets?.length < 1 ? 0 : 8,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'flex-end',
                }}>
                <TextBold
                  style={{
                    fontSize: 20,
                    opacity: 0.6,
                  }}>{`Wallets: `}</TextBold>
                <TextBold style={{fontSize: 20}}>{`$${showSumValue}`}</TextBold>
                <TextReg
                  style={{
                    opacity: 0.9,
                    color: difColor,
                    marginLeft: 6,
                    fontSize: 16,
                  }}>{`${difSymbol}${showDifference}%`}</TextReg>
              </View>
            </View>

            <ScrollView horizontal style={{flexDirection: 'row'}}>
              <View style={{flexDirection: 'row'}}>
                {showTokens}
                {showDots && (
                  <View
                    style={{
                      marginRight: 4,
                      height: 50,
                      width: 50,
                      borderRadius: 14,
                      opacity: 0.9,
                      borderWidth: 1.5,
                      borderColor: '#777',
                      backgroundColor: '#505061',
                      alignItems: 'center',
                      justifyContent: 'center',
                      flexDirection: 'row',
                    }}>
                    <TextReg style={{fontSize: 22, marginTop: -3, marginLeft: -2}}>+</TextReg>
                    <TextReg style={{fontSize: 22, opacity: 0.84}}>{showDiff}</TextReg>
                  </View>
                )}
              </View>
            </ScrollView>
          </View>
          <Image
            source={require('../../imgs/rightArrow.png')}
            style={{height: 24, width: 24, opacity: 0.8, marginRight: -6}}
          />
        </View>
      </TouchableOpacity>
    </Card>
  )
}

export default WalletsBox

/*
<View
  style={{
    height: 50,
    width: 50,
    borderRadius: 14,
    backgroundColor: 'red',
    marginRight: 6,
  }}
/>
<View
  style={{
    height: 50,
    width: 50,
    borderRadius: 14,
    backgroundColor: 'red',
    marginRight: 6,
  }}
/>
<View
  style={{
    height: 50,
    width: 50,
    borderRadius: 14,
    backgroundColor: 'red',
    marginRight: 6,
  }}
/>
<View
  style={{
    height: 50,
    width: 50,
    borderRadius: 14,
    backgroundColor: 'red',
    marginRight: 6,
  }}
/>
*/
