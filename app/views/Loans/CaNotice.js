import React, {useState, useEffect} from 'react';
import {View, Image, ScrollView, FlatList, TouchableOpacity, Linking} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {TextBold, Card, TextReg, Button} from '../../components';
import {numberWithCommas} from '../../util/helpers';

import styles from './styles';

const CaNotice = props => {
  let {user, productRef} = props;
  const [caNotif, setCaNotif] = useState(false);

  useEffect(() => {
    AsyncStorage.getItem(`CaNotif`).then(CaNotif => {
      if (CaNotif) {
        setCaNotif(true);
      }
    });
  });

  const linkToCali = () => {
    Linking.openURL('https://saltlending.com/california-dfpi-notice/');
  };

  const linkToFaq = () => {
    Linking.openURL('https://saltlending.com/platform-servicing-updates-faqs/');
  };

  const hideCaNotif = async () => {
    await AsyncStorage.setItem(`CaNotif`, 'true');
    setCaNotif(true);
  };

  const account = user.accounts?.filter(a => a.ref == productRef)[0];
  let jurisdiction = account?.loans ? account?.loans[0]?.jurisdiction : {};
  if (jurisdiction?.countryCode != 'US' || jurisdiction?.province != 'CA') return null;
  if (caNotif) return null;

  return (
    <Card cardMarginBottom={2} marginTop={16}>
      <View style={{marginLeft: 9, marginRight: 9, alignSelf: 'stretch', position: 'relative'}}>
        <TouchableOpacity
          style={{position: 'absolute', right: -8, top: -2, height: 36, width: 36, zIndex: 20}}
          onPress={() => hideCaNotif()}>
          <Image source={require('../../imgs/closeX.png')} style={styles.closeModalImg} />
        </TouchableOpacity>
        <TextBold style={{fontSize: 20, marginBottom: 10, marginTop: 6}}>California Users</TextBold>
        <TextReg style={{fontSize: 16, marginBottom: 10}}>
          An important update for California borrowers{' '}
          <TouchableOpacity onPress={() => linkToCali()} style={{marginBottom: -2}}>
            <TextBold style={{textDecorationLine: 'underline', color: '#00FFBD', fontSize: 16}}>click here</TextBold>
          </TouchableOpacity>
          .{' '}
        </TextReg>
        <TextReg style={{fontSize: 16, marginBottom: 10}}>
          Additional Platform Servicing Updates & FAQs{' '}
          <TouchableOpacity onPress={() => linkToFaq()} style={{marginBottom: -2}}>
            <TextBold style={{textDecorationLine: 'underline', color: '#00FFBD', fontSize: 16}}>click here</TextBold>
          </TouchableOpacity>
          .
        </TextReg>
      </View>
    </Card>
  );
};

export default CaNotice;
