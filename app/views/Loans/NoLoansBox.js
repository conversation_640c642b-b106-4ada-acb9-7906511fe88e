import React, {Component} from 'react'
import {Image} from 'react-native'
import {connect} from 'react-redux'

import {Button, TextReg, TextBold, Card} from '../../components'

class NoLoansBox extends Component {
  render() {
    let launchDarkly = this.props.launchDarkly

    let banned = {
      BTC: launchDarkly['disable-btc-deposit'] || false,
      ETH: launchDarkly['disable-eth-deposit'] || false,
      BCH: launchDarkly['disable-bch-deposit'] || false,
      LTC: launchDarkly['disable-ltc-deposit'] || false,
      USDC: launchDarkly['disable-usdc-deposit'] || false,
      USDP: launchDarkly['disable-usdp-deposit'] || false,
      SALT: launchDarkly['disable-salt-deposit'] || false,
      TUSD: launchDarkly['disable-tusd-deposit'] || false,
      XRP: launchDarkly['disable-xrp-deposit'] || true,
      DASH: launchDarkly['disable-dash-deposit'] || true,
      DOGE: launchDarkly['disable-doge-deposit'] || true,
      PAXG: launchDarkly['disable-paxg-deposit'] || true,
    }

    let tokenArr = ['BTC', 'ETH', 'LTC', 'BCH', 'USDT', 'USDP', 'USDC', 'TUSD']

    //removed banned tokens
    tokenArr = tokenArr.filter(a => !banned[a])
    let tokenText = tokenArr.slice(0, -1).join(', ') + ', or ' + tokenArr.slice(-1)

    return (
      <Card marginTop={14}>
        <Image
          source={require('../../imgs/graphics/noLoanImg.png')}
          style={{width: 132, height: 131, marginTop: 36, marginBottom: 20}}
        />
        <TextBold
          style={{
            color: '#FFF',
            fontSize: 22,
            marginBottom: 14,
          }}>{`Hold your crypto, get cash.`}</TextBold>
        <TextReg
          style={{
            width: 252,
            fontSize: 15,
            marginBottom: 36,
          }}>{`With 12 months term, competitive rates, and loans available for any combination of ${tokenText}.`}</TextReg>
        <Button
          title={'getLoan'}
          onPress={this.props.openLoanRequest}
          style={{marginBottom: 16, alignSelf: 'stretch', marginLeft: 20, marginRight: 20}}>
          <TextReg
            style={{fontSize: 18, letterSpacing: 0.7, color: '#000'}}>{`GET A LOAN`}</TextReg>
        </Button>
      </Card>
    )
  }
}

const mapStateToProps = state => ({
  account: state.auth.account,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(NoLoansBox)
