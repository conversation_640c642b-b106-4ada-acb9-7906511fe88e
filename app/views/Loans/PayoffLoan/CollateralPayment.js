import React, {useEffect, useState} from 'react'
import {
  View,
  Image,
  Dimensions,
  TextInput,
  ScrollView,
  Platform,
  ActivityIndicator,
  TouchableOpacity,
  Linking,
} from 'react-native'
import BigNumber from 'bignumber.js'
import * as pb from 'google-protobuf/google/protobuf/empty_pb'
import {connect, useDispatch} from 'react-redux'

import {TextBold, TextReg, Button, BackgroundHeader} from '../../../components'
import commonStyles from '../../../styles/commonStyles'
import {cryptoMeta} from '../../../util/enumerables'
import {formatCrypto, formatCurrency} from '../../../util/helpers'
import {
  PriceClient,
  UnaryPriceRequest,
  AssetPairId,
} from '../../Home/grpc/proto/api/trading/v1/price_grpc_web_pb'
import config from '../../../config.json'
import {
  updateAccountPlus,
  updateLoans,
  updatePrices,
  updateUser,
} from '../../../store/user/user.actions'

const {width: screenWidth} = Dimensions.get('window')

const CollateralPayment = props => {
  const [investmentAmounts, setInvestmentAmounts] = useState([])
  const [step, setStep] = useState('balances')
  const [reward, setReward] = useState(0)
  const [submitting, setSubmitting] = useState(false)
  const [paidOff, setPaidOff] = useState(false)
  const [submitError, setSubmitError] = useState(null)
  const [validationError, setValidationError] = useState(false)
  const [loading, setLoading] = useState(false)
  const [loanHistory, setLoanHistory] = useState(null)
  const [bounceFee, setBounceFee] = useState(0)
  const {loanData, tokenPrices, navigation, accountRef, WebService} = props

  const dispatch = useDispatch()
  const loanBalance = new BigNumber(loanData?.amortizationInfo?.currentBalance)
  const interestBalance = new BigNumber(loanData?.amortizationInfo?.interestBalance)
  const totalBalance = loanBalance.plus(interestBalance).minus(reward)
  const fee = totalBalance.multipliedBy('1.5').dividedBy('100').dp(2) //here the fee is fixed to 1.5%
  let totalRequired = totalBalance.plus(fee).dp(2)

  if (bounceFee > 0) {
    totalRequired = totalRequired.plus(bounceFee)
  }

  const filterPayoffLoan = loanHistory?.liquidations?.filter(item => item.reason === 'close_out')
  const filterLiquidation = filterPayoffLoan?.[0]?.trades?.filter(item => {
    return (
      new Date(item.createdAt).toISOString().split('T')[0] == new Date().toISOString().split('T')[0]
    )
  })

  const getLoanHistory = async () => {
    setLoading(true)
    const {data} = await WebService.getLoanEventHistory(loanData.id)
    setLoanHistory(data)
    setLoading(false)
  }

  useEffect(() => {
    getMyLoan()
  }, [])

  let getMyLoan = () => {
    WebService.getLoans()
      .then(res => {
        if (res?.data?.length > 0) {
          let haBounce = res?.data[0]?.bounceFeeAmount
          haBounce = Number(haBounce || 0)
          setBounceFee(haBounce)
        }
      })
      .catch(err => {
        console.log('getMyLoan - err', err)
      })
  }

  console.log('collat payment bounceFee', bounceFee)

  useEffect(() => {
    rewardCalCulation()
    getLoanHistory()

    const timeoutId = setInterval(() => {
      getPrices()
    }, 10000)

    if (step === 'finish') clearInterval(timeoutId)

    return () => {
      clearInterval(timeoutId)
      setSubmitError(null)
    }
  }, [step])

  useEffect(() => {
    if (filterPayoffLoan?.length > 0) {
      setPaidOff(true)
      setStep('finish')
    }
  }, [loanHistory])

  let convertTime = timeIn => {
    const date = new Date(timeIn)
    const isoString = date.toISOString()
    return isoString
  }

  let pClient = new PriceClient(config.grpc)

  const getPrices = () => {
    const request = new pb.Empty()
    pClient.getAssetPairs(request, {}, (err, response) => {
      //usdp pre filter  50211
      let filterList = response.toObject()?.assetPairsList

      if (filterList?.length < 1) {
        filterList = response
          .toObject()
          ?.assetPairsList?.filter(a => a.baseCurrency.assetId != 50211)
      }

      let arrAssetPairId = filterList?.map(a => {
        return new AssetPairId()
          .setBaseCurrencyId(a.baseCurrency.assetId)
          .setQuoteCurrencyId(a.quoteCurrency.assetId)
      })

      const setReq = new UnaryPriceRequest().setAssetPairsList(arrAssetPairId)

      let priceArr = [
        'BCH-USD',
        'BTC-USD',
        'DASH-USD',
        'DOGE-USD',
        'ETH-USD',
        'LTC-USD',
        'PAXG-USD',
        'SALT-USD',
        'USDC-USD',
        'USDP-USD',
        'USDT-USD',
        'XRP-USD',
      ]

      pClient.getPrice(setReq, {}, (err, response) => {
        let formatToday = response.toObject()?.pricesList?.map(a => {
          let asset = a.assetPair?.baseCurrency?.ticker

          return {
            asset: a.assetPair?.baseCurrency?.ticker,
            metric: 'USD',
            price: asset == 'SALT' ? 0.15 : a.price,
            time: convertTime(a.time.seconds),
          }
        })

        const today = priceArr.reduce((acc, pair) => {
          const [asset, metric] = pair.split('-')
          let i = formatToday?.filter(b => b.asset == asset)[0]
          acc[pair] = {
            asset,
            atomic: false,
            isStale: false,
            metric,
            price: i?.price || 0,
            time: i?.time,
          }
          return acc
        }, {})

        dispatch(updatePrices(today))
      })
    })
  }

  const rewardCalCulation = () => {
    const currentReward = loanData?.reward
      ? loanData?.reward.rewardSchedule.find(
          r => r.disbursementPeriod === loanData?.reward.currentDisbursementPeriod,
        )
      : null

    const hasCurrentRewardBeenEarned =
      // check if loan is stackwise loan or not
      // AND
      // check if reward allocation is towards monthly payment
      currentReward && loanData?.reward?.useRewardAsPayment
        ? // if true, check if it has been earned from the reward schedule
          currentReward.earned
        : // if false (meaning if its a prestackwise loan or reward allocation IS NOT towards monthly payment), return true so that the constant 'upcomingRewardTowardsMonthlyPayment' will default to 0
          true

    const upcomingRewardTowardsMonthlyPayment = hasCurrentRewardBeenEarned
      ? new BigNumber(0)
      : new BigNumber(loanData?.reward?.nextRewardAmount)

    const totalDaysOfRewardsEarned = new BigNumber(
      loanData?.reward?.daysOfRewardEarnedInCurrentPeriod || 0,
    )

    const amountOfRewardsEarnedForMontlyPayment = upcomingRewardTowardsMonthlyPayment
      .div(currentReward?.days || 1)
      .times(totalDaysOfRewardsEarned)

    setReward(amountOfRewardsEarnedForMontlyPayment.toNumber())
  }

  async function handlePayoff() {
    try {
      setSubmitting(true)
      const payoffData = investmentAmounts
        .map(item => {
          if (+item.usdAmount > 0) {
            const {projectBalance, ...rest} = item // Destructure to remove projectBalance
            return rest
          }
        })
        .filter(item => item)

      await WebService.payoffLoans(loanData.id, {payments: payoffData, feeRate: '0.015'})
      const {data} = await WebService.getLoanEventHistory(loanData.id)
      dispatch(updateLoans({...loanData, loanHistory: data}))
      setSubmitting(false)
      setStep('finish')
    } catch (err) {
      if (err?.data?.body?.error) {
        if (
          err.data.body.error === 'Cannot closeout loan. Amount submitted is less than loan balance'
        ) {
          setSubmitError({
            text: 'You need to resolve the pending payment first or reach out to ',
            type: 'link',
          })
        } else setSubmitError({text: err.data.body.error})
      }
      setSubmitting(false)
    }
  }

  const handleValueChange = (value, currency, projectBalance) => {
    if (validationError) {
      setValidationError(false)
    }

    var letterRegExp = /[a-zA-Z]/g
    if (letterRegExp.test(value)) {
      return
    }
    let parsedText = value.replace('$', '')
    parsedText = parsedText.split(' ').join('')
    parsedText = parsedText.split(',').join('')

    if (parsedText == '.') {
      return
    }

    if (parsedText.endsWith('.')) {
      // Do nothing, just add it as normal
    } else if (parsedText.includes('.') && parsedText.split('.')[1].length >= 2) {
      parsedText = Number(parsedText).toFixed(2)
    } else {
      parsedText = parsedText
    }

    setInvestmentAmounts(prevAmounts => {
      // Check if an entry with the same currency already exists
      const existingEntry = prevAmounts.find(item => item.currency === currency)

      if (existingEntry) {
        // Update the amount if the entry exists
        return prevAmounts.map(item =>
          item.currency === currency ? {...item, usdAmount: parsedText, projectBalance} : item,
        )
      } else {
        // Add a new entry if it doesn't exist
        return [...prevAmounts, {usdAmount: parsedText, currency, projectBalance}]
      }
    })
  }

  const getInvestmentAmount = currency => {
    const entry = investmentAmounts.find(item => item.currency === currency)
    return entry ? entry.usdAmount : ''
  }

  const sumInvestmentAmounts = () => {
    let amount = investmentAmounts.reduce((total, item) => {
      let amount = parseFloat(item.usdAmount) || 0 // Convert amount to a number, default to 0 if not valid
      return total + amount
    }, 0)
    return investmentAmounts.length > 0 ? new BigNumber(amount).dp(2) : amount // Initialize total as 0
  }

  const collateral = loanData.collaterals.reduce((total, collateral) => {
    return (
      total +
      new BigNumber(collateral.projectedBalance)
        .multipliedBy(tokenPrices[`${collateral.currency}-USD`]?.price)
        .toNumber()
    )
  }, 0) // Initialize total as 0

  const next = () => {
    if (amountError) {
      setValidationError(true)
    } else {
      setValidationError(false)
      setStep('confirm')
    }
  }

  const getAllAccounts = async () => {
    await WebService.getAllAccounts().then(async res => {
      await dispatch(updateAccountPlus(res?.data))
    })
  }

  const getUser = async () => {
    await WebService.getSaltUser().then(res => {
      dispatch(updateUser(res.data))
      getAllAccounts()
    })
  }

  const done = async () => {
    await props?.route?.params?.getLoansData()
    await getUser()
    navigation.navigate('Loans', {id: loanData.id, ref: accountRef, nextPage: ''})
  }

  const collaterals = loanData?.collaterals?.filter(item =>
    new BigNumber(item.projectedBalance).isGreaterThan(0),
  )
  const aboveRequired = totalRequired.toNumber() < sumInvestmentAmounts()
  const belowRequired =
    !!investmentAmounts.length && totalRequired.toNumber() > sumInvestmentAmounts()

  console.log('totalRequired', totalRequired, sumInvestmentAmounts(), aboveRequired, belowRequired)

  const amountError = belowRequired || aboveRequired

  const getTitle = () => {
    switch (step) {
      case 'balances':
        return {
          title: 'Select Balances',
          subTitle: 'Select which collateral types you would like to use to pay off your loan:',
        }
      case 'confirm':
        return {title: 'Confirm Details', subTitle: 'Paying off loan with collateral:'}
      case 'finish':
        return {
          title: paidOff ? 'Your Loan is Paid Off' : 'Total Paid',
          subTitle: 'Your Loan is Paid Off',
        }
    }
  }

  const getCollateralOptions = () => {
    if (step === 'balances') {
      return collaterals
    } else {
      return investmentAmounts
    }
  }

  if (loading) {
    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader
          title={'Pay Off Loan'}
          goBack={
            step === 'balances'
              ? navigation.goBack
              : step === 'confirm'
              ? () => setStep('balances')
              : () => setStep('confirm')
          }
        />
        <ActivityIndicator size="large" color="#fff" style={{marginVertical: 10}} />
      </View>
    )
  }

  const renderSubmitError = () => (
    <TextReg
      style={{
        fontSize: 14,
        color: '#E5705A',
        textAlign: 'center',
        marginTop: 20,
        marginHorizontal: 30,
      }}>
      {submitError.text}
    </TextReg>
  )

  let goSupport = () => {
    Linking.openURL('mailto:<EMAIL>')
  }

  return (
    <View style={commonStyles.tileContainer}>
      <BackgroundHeader
        title={'Pay Off Loan'}
        goBack={
          step === 'balances'
            ? navigation.goBack
            : step === 'confirm'
            ? () => setStep('balances')
            : () => setStep('confirm')
        }
      />
      {step === 'finish' && (
        <Image
          source={require('../../../imgs/graphics/successImg.png')}
          style={{width: 54, height: 54}}
        />
      )}
      <TextReg style={{marginTop: 24, marginBottom: paidOff ? 20 : 0, fontSize: 24}}>
        {getTitle().title}
      </TextReg>
      {!paidOff && (
        <TextReg
          style={{
            marginTop: 12,
            marginBottom: step !== 'balances' ? 24 : 35,
            fontSize: 16,
            textAlign: 'center',
            width: '85%',
          }}>
          {getTitle().subTitle}
        </TextReg>
      )}

      <ScrollView style={{paddingHorizontal: 20}}>
        {getCollateralOptions()?.map((collateral, index) => {
          if (+collateral.projectedBalance <= 0) {
            return false
          }
          if (['SALT', 'PREF'].includes(collateral.currency)) {
            return false
          }
          const icon = cryptoMeta.get(collateral?.currency)?.icon

          const maxAmount = new BigNumber(collateral.projectedBalance).multipliedBy(
            tokenPrices[`${collateral.currency}-USD`]?.price,
          )
          const currencyName = cryptoMeta.get(collateral.currency)?.name
          const currencyValue = new BigNumber(tokenPrices[`${collateral.currency}-USD`]?.price)
          const investmentAmount = new BigNumber(getInvestmentAmount(collateral.currency))
          const cryptoInvestmentAmount = investmentAmount.dividedBy(currencyValue)

          return (
            <React.Fragment key={index}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: step !== 'balances' ? 20 : 0,
                }}>
                {step !== 'balances' ? (
                  <View style={{flexDirection: 'row', gap: 10}}>
                    <Image source={icon} style={{width: 35, height: 35}} />
                    <View style={{}}>
                      <TextReg style={{fontSize: 16, color: '#FFF'}}>{currencyName}</TextReg>
                      <TextReg style={{fontSize: 12, color: '#FFF'}}>
                        1 {collateral.currency} = $
                        {step === 'finish'
                          ? formatCurrency(
                              filterLiquidation?.filter(
                                item => item.currencyOut == collateral.currency,
                              )[0]?.exchangeRate,
                              2,
                            )
                          : formatCurrency(currencyValue, 2)}
                      </TextReg>
                    </View>
                  </View>
                ) : (
                  <Image source={icon} style={{width: 35, height: 35}} />
                )}
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'flex-end',
                    gap: 13,
                  }}>
                  {step !== 'balances' ? (
                    <View
                      style={{
                        flexDirection: 'column',
                        alignItems: 'flex-end',
                        justifyContent: 'flex-end',
                        gap: 4,
                      }}>
                      <TextBold style={{fontSize: 16, color: '#FFF'}}>
                        - ${formatCurrency(investmentAmount)}
                      </TextBold>
                      <TextReg style={{fontSize: 12, color: '#FFF'}}>
                        ≈
                        {step === 'finish'
                          ? formatCrypto(
                              filterLiquidation?.filter(
                                item => item.currencyOut == collateral.currency,
                              )[0]?.amount,
                              collateral.currency,
                            )
                          : formatCrypto(cryptoInvestmentAmount, collateral.currency)}{' '}
                        {collateral.currency}
                      </TextReg>
                    </View>
                  ) : (
                    <React.Fragment>
                      <TextReg style={{fontSize: 16, color: '#FFF'}}>
                        $
                        {formatCurrency(
                          new BigNumber(collateral.projectedBalance).multipliedBy(
                            tokenPrices[`${collateral.currency}-USD`]?.price,
                          ),
                        )}
                      </TextReg>

                      <View
                        style={{
                          flexDirection: 'row',
                          borderColor: '#FFF',
                          borderWidth: 0.5,
                          borderRadius: 15,
                          paddingHorizontal: 12,
                          paddingVertical: Platform.OS === 'ios' ? 12 : 0,
                          justifyContent: 'flex-start',
                          alignItems: 'center',
                        }}>
                        <TextReg>$</TextReg>
                        <TextInput
                          style={{
                            color: '#FFF',
                            width: 100,
                          }}
                          onChangeText={text =>
                            handleValueChange(
                              text,
                              collateral.currency,
                              collateral.projectedBalance,
                            )
                          }
                          value={getInvestmentAmount(collateral.currency)}
                          underlineColorAndroid="transparent"
                          blurOnSubmit
                          returnKeyType={'next'}
                          placeholder={''}
                          onBlur={() => {}}
                          onSubmitEditing={() => {}}
                          keyboardType={'numeric'}
                          keyboardAppearance="dark"
                        />
                      </View>
                    </React.Fragment>
                  )}
                </View>
              </View>
              {investmentAmount.gt(maxAmount) && (
                <TextReg
                  style={{
                    color: 'red',
                    fontSize: 14,
                    marginTop: 8,
                    textAlign: 'right',
                  }}>
                  Investment amount exceeds the available balance.
                </TextReg>
              )}
              {step === 'balances' && (
                <View
                  style={{
                    height: 1,
                    backgroundColor: '#AFAFAF',
                    marginVertical: 10,
                  }}
                />
              )}
            </React.Fragment>
          )
        })}
        {step !== 'balances' && (
          <React.Fragment>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginRight: 10,
                marginTop: 10,
                marginBottom: 20,
              }}>
              <TextReg style={{fontSize: 16}}>Remaining Collateral</TextReg>
              <TextReg style={{fontSize: 16}}>
                ${formatCurrency(collateral - sumInvestmentAmounts())}
              </TextReg>
            </View>
            <View
              style={{
                height: 1,
                backgroundColor: '#AFAFAF',
              }}
            />
          </React.Fragment>
        )}
        {step === 'confirm' && (
          <TextReg style={{marginVertical: 20}}>Prices are estimated and may vary.</TextReg>
        )}
        <View
          style={{
            marginTop: step === 'confirm' ? 0 : 30,
            marginBottom: step === 'confirm' ? 30 : 40,
          }}>
          <View style={{flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12}}>
            <TextReg style={{fontSize: 14, color: '#FFF'}}>Principal Loan Balance</TextReg>
            <TextBold style={{fontSize: 16, color: '#FFF'}}>
              ${formatCurrency(loanBalance)}
            </TextBold>
          </View>
          <View style={{flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12}}>
            <TextReg style={{fontSize: 14, color: '#FFF'}}>Interest</TextReg>
            <TextReg style={{fontSize: 16, color: '#FFF'}}>
              ${formatCurrency(interestBalance)}
            </TextReg>
          </View>
          {reward > 0 && (
            <View style={{flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12}}>
              <TextReg style={{fontSize: 14, color: '#FFF'}}>Rewards</TextReg>
              <TextReg style={{fontSize: 16, color: '#FFF'}}>-${formatCurrency(reward)}</TextReg>
            </View>
          )}
          <View style={{flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12}}>
            <TextReg style={{fontSize: 14, color: '#FFF'}}>Fee (1.5%)</TextReg>
            <TextReg style={{fontSize: 16, color: '#FFF'}}>${formatCurrency(fee)}</TextReg>
          </View>

          {bounceFee > 0 && (
            <View style={{flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12}}>
              <TextReg style={{fontSize: 14, color: '#FFF'}}>Bounce Fee</TextReg>
              <TextReg style={{fontSize: 16, color: '#FFF'}}>${formatCurrency(bounceFee)}</TextReg>
            </View>
          )}

          <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
            <View style={{flexDirection: 'row', gap: 6, alignItems: 'center'}}>
              <TextReg style={{fontSize: 14, color: amountError ? '#E5705A' : '#FFF'}}>
                Total Required
              </TextReg>
              {amountError && (
                <Image
                  source={require('../../../imgs/loanHealth/alertLoan.png')}
                  style={{width: 20, height: 20}}
                />
              )}
            </View>
            <TextReg style={{fontSize: 16, color: amountError ? '#E5705A' : '#FFF'}}>
              ${formatCurrency(totalRequired)}
            </TextReg>
          </View>
          {amountError && validationError && (
            <TextReg style={{fontSize: 14, color: '#E5705A', textAlign: 'center', marginTop: 20}}>
              {aboveRequired
                ? 'The entered amount is greater than the required amount.'
                : 'The entered amount is less than the required amount.'}
            </TextReg>
          )}
        </View>
        {step === 'finish' ? (
          <React.Fragment>
            <TextReg style={{fontSize: 8, width: screenWidth - 40}}>
              Your remaining Stackwise Rewards will be paid out to you at this time. Your remaining
              crypto will remain in your collateral wallet until you choose to move it elsewhere.
            </TextReg>
            <Button
              style={{marginTop: 37, marginBottom: 24, width: screenWidth - 40}}
              disabled={false}
              onPress={() => done()}
              isLoading={false}>
              Done
            </Button>
          </React.Fragment>
        ) : step === 'confirm' ? (
          <View style={{width: screenWidth - 40}}>
            <TextReg style={{fontSize: 8}}>
              By clicking "Confirm," you agree to the terms of this transaction at the above-quoted
              price. The proceeds from the sale will be credited to your principal loan balance in
              the amounts set forth above. Salt acts as principal and may retain the digital assets
              and/or sell the assets to a third-party liquidity provider.
            </TextReg>
            {submitError ? (
              submitError.type === 'link' ? (
                <View
                  style={{
                    flexDirection: 'row',
                    flexWrap: 'wrap',
                    justifyContent: 'center',
                  }}>
                  {renderSubmitError()}
                  <TouchableOpacity onPress={() => goSupport()} activeOpacity={0.8}>
                    <TextBold
                      style={{
                        fontSize: 14,
                        color: '#00FFBD',
                        textAlign: 'center',
                      }}>
                      <EMAIL>
                    </TextBold>
                  </TouchableOpacity>
                </View>
              ) : (
                renderSubmitError()
              )
            ) : null}
            <Button
              style={{marginTop: 22, marginBottom: 24, width: screenWidth - 40}}
              disabled={amountError || submitError?.type === 'link'}
              onPress={() => handlePayoff()}
              isLoading={submitting}>
              Confirm and Pay
            </Button>
          </View>
        ) : (
          <Button
            style={{marginTop: 7, marginBottom: 24, width: screenWidth - 40}}
            disabled={
              totalRequired.toNumber() == 0 || sumInvestmentAmounts() === 0 || validationError
            }
            onPress={() => next()}
            isLoading={false}>
            Next
          </Button>
        )}
      </ScrollView>
    </View>
  )
}

CollateralPayment.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    //shadowColor: 'transparent',
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  accountRef: state.auth.account.ref,
  loanData: state.user.loanData,
  user: state.user.user,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
  refreshCount: state.user.refreshCount,
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(CollateralPayment)
