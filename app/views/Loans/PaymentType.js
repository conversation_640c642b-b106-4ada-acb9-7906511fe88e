import React, {Component, useState} from 'react'
import {View, Image, TouchableOpacity} from 'react-native'
import {connect} from 'react-redux'

import {TextBold, Card, TextReg, Button, BackgroundHeader} from '../../components'

import commonStyles from '../../styles/commonStyles'
import styles from './styles'

const PaymentType = props => {
  const [typeSelected, setTypeSelected] = useState(null)
  const [loading, setLoading] = useState(false)

  const {loanData, navigation, launchDarkly} = props
  const selectOption = selection => {
    if (typeSelected === selection) {
      setTypeSelected(null)
    } else {
      setTypeSelected(selection)
    }
  }

  const next = () => {
    if (typeSelected === 'OneTimeOptions') {
      navigation.navigate('OneTimeOptions')
    } else {
      navigation.navigate('PayoffOptions', {getLoansData: props?.route?.params?.getLoansData})
    }
  }

  let ldAchOff = launchDarkly['disable-ach'] || false
  let noTransfer = launchDarkly['disable-wire-transfer'] || false

  return (
    <View style={commonStyles.tileContainer}>
      <BackgroundHeader title={'Payment Type'} goBack={navigation.goBack} />
      <TextReg style={{marginTop: 24, marginBottom: 24, fontSize: 18}}>
        How would you like to pay?
      </TextReg>

      <>
        <TouchableOpacity onPress={() => selectOption('OneTimeOptions')}>
          <Card
            cardWidth={300}
            cardMarginBottom={12}
            style={[
              styles.oneTimeOptionCard,
              {
                borderColor: typeSelected === 'OneTimeOptions' ? '#00FFBD' : '#FFF',
              },
            ]}>
            <View style={styles.oneTimeOptionCardBox}>
              <Image
                source={require('../../imgs/loanHealth/healthyLoan.png')}
                style={styles.oneTimeOptionImg}
              />
              <TextBold style={{...styles.oneTimeOptionTitle, width: 'auto'}}>
                One Time Payment
              </TextBold>
            </View>
          </Card>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => selectOption('PayOffLoan')}>
          <Card
            cardWidth={300}
            cardMarginBottom={12}
            style={[
              styles.oneTimeOptionCard,
              {
                borderColor: typeSelected === 'PayOffLoan' ? '#00FFBD' : '#FFF',
              },
            ]}>
            <View style={styles.oneTimeOptionCardBox}>
              <Image
                source={require('../../imgs/graphics/bank.png')}
                style={styles.oneTimeOptionImg}
              />
              <TextBold style={{...styles.oneTimeOptionTitle, width: 'auto'}}>
                Pay Off Loan
              </TextBold>
            </View>
          </Card>
        </TouchableOpacity>
      </>

      <Button
        style={{marginTop: 16, width: 300}}
        disabled={!typeSelected}
        onPress={() => next()}
        isLoading={loading}>
        Next
      </Button>
    </View>
  )
}

PaymentType.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  //headerTintColor: '#FFF',
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    //shadowColor: 'transparent',
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  accountRef: state.auth.account.ref,
  loanData: state.user.loanData,
  user: state.user.user,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
  refreshCount: state.user.refreshCount,
  WebService: state.auth.WebService,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(PaymentType)
