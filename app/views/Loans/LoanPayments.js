import React, {Component} from 'react'
import {View, Animated, Dimensions, TouchableOpacity, Image, Platform, Share} from 'react-native'
import {connect} from 'react-redux'
import SwiperFlatList from 'react-native-swiper-flatlist'
import RNFS from 'react-native-fs'
import <PERSON> from 'papaparse'
import BigNumber from 'bignumber.js'

import {Card, TextReg, TextBold} from '../../components'
import {
  formatCurrency,
  formatToAmPmTime,
  notificationFormatDate,
  numberWithCommas,
} from '../../util/helpers'

import styles from './styles'

let {width} = Dimensions.get('window')
width -= 60

class LoanPayments extends Component {
  constructor(props) {
    super(props)
    this.state = {
      paymentToggleState: 0,
      historySeeMore: false,
      futureSeeMore: false,
    }
    this.xOffset = 0
    this.scrollValue = new Animated.Value(0)
  }

  onScroll = e => {
    this.xOffset = e.nativeEvent.contentOffset.x

    Animated.timing(this.scrollValue, {
      toValue: this.xOffset,
      duration: 0,
    }).start()
  }

  toggleToHistory = () => {
    this.showLessPayments(0)
    this.refs.swiper._scrollToIndex(0)
    this.setState({paymentToggleState: 0})
  }

  toggleToUpcoming = () => {
    this.showLessPayments(1)
    this.refs.swiper._scrollToIndex(1)
    this.setState({paymentToggleState: 1})
  }

  onScrollEnd = index => {
    this.setState({paymentToggleState: index})
    this.showLessPayments(index)
  }

  showMorePayments = scrollValue => {
    if (scrollValue < 100) {
      this.setState({historySeeMore: true})
    } else {
      this.setState({futureSeeMore: true})
    }
  }

  showLessPayments = index => {
    if (index === 1) {
      this.setState({historySeeMore: false})
    }
    if (index === 0) {
      this.setState({futureSeeMore: false})
    }
  }

  requestStoragePermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          {
            title: 'Storage Permission Required',
            message: 'This app needs access to your storage to save files',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        )
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Storage permission granted')
          return true
        } else {
          console.log('Storage permission denied')
          return false
        }
      } catch (err) {
        console.warn(err)
        return false
      }
    } else {
      // No need to request permission on iOS
      return true
    }
  }

  clickRow = item => {
    console.log('item', item)

    if (item.type === 'scheduled') {
      this.props.navigation.navigate('PaymentInfo', {
        payment: item,
      })
    }
  }

  saveAndroid = async (data, title = 'data.csv') => {
    // Check for permission first
    const hasPermission = await this.requestStoragePermission()
    if (!hasPermission) {
      console.log('Permission denied')
      return
    }

    const csv = Papa.unparse(data)
    const path = `${RNFS.ExternalStorageDirectoryPath}/Documents/SALT/${title}`

    try {
      await RNFS.mkdir(`${RNFS.ExternalStorageDirectoryPath}/Documents/SALT/`)
      await RNFS.writeFile(path, csv, 'utf8')
      console.log(`File written to ${path}`)
    } catch (error) {
      console.error('Error saving or sharing CSV file', error)
    }
  }

  saveIOS = async (data, title = 'data.csv') => {
    const csv = Papa.unparse(data)
    const path = `${RNFS.DocumentDirectoryPath}/${title}`

    try {
      await RNFS.writeFile(path, csv, 'utf8')
      console.log(`File written to ${path}`)

      // Share the file
      const shareResponse = await Share.share({
        title: title,
        url: `file://${path}`,
        type: 'text/csv',
      })

      console.log('File shared:', shareResponse)
    } catch (error) {
      console.error('Error saving or sharing CSV file', error)
    }
  }

  cap = str => {
    return str.charAt(0).toUpperCase() + str.slice(1)
  }

  down = () => {
    const paymentsData = this.props.loanData.payments || []

    let downData = paymentsData.map((item, k) => {
      let totalPaid = (Number(item.principal) + Number(item.interest)).toFixed(2)
      totalPaid = numberWithCommas(totalPaid)
      const showDate = notificationFormatDate(item.paidAt)

      let type = this.cap(item.type)

      const itemPrincipal = Number(item.principal).toFixed(2)
      const itemInterest = Number(item.interest).toFixed(2)
      const itemFee = Number(item.fee).toFixed(2)
      let reward = Number(item.reward).toFixed(2)
      return {
        //Date: showDate,
        Date: item.paidAt,
        Type: type,
        Amount: totalPaid,
        Fees: itemFee,
        Principal: itemPrincipal,
        Interest: itemInterest,
      }
    })

    let title = `Payments-History.csv`
    if (Platform.OS === 'android') {
      this.saveAndroid(downData, title)
    } else {
      this.saveIOS(downData, title)
    }
  }

  render() {
    const halfWidth = width / 2
    const xInterpolated = this.scrollValue.interpolate({
      inputRange: [0, width],
      outputRange: [0, halfWidth - 12],
    })

    const colorInterpolated = this.scrollValue.interpolate({
      inputRange: [0, width],
      outputRange: ['rgba(0,0,0,1)', 'rgba(74,79,84,1)'],
    })

    const colorInterpolatedInverse = this.scrollValue.interpolate({
      inputRange: [0, width],
      outputRange: ['rgba(74,79,84,1)', 'rgba(0,0,0,1)'],
    })

    let upcomingPayment = []
    if (this.props.amortizationInfo) {
      upcomingPayment = this.props.amortizationInfo.amortizationSchedule

      let scheduledPayments = []
      if (this.props.loanData.scheduledPayments?.length > 0) {
        scheduledPayments = this.props.loanData.scheduledPayments?.map(a => {
          console.log('a', a)
          return {
            ...a,
            type: 'scheduled',
            date: a.effectiveDate,
            principalDue: a.amount,
          }
        })
      }
      if (scheduledPayments) {
        upcomingPayment = upcomingPayment.concat(scheduledPayments)
      }
      console.log('upcomingPayment-1', upcomingPayment)

      //sort
      const today = new Date()
      upcomingPayment = upcomingPayment
        .filter(a => {
          let tempDate = new Date(a.date)
          return tempDate > today || a.type === 'scheduled'
        })
        .sort((a, b) => {
          // Sort scheduled payments to top
          if (a.type === 'scheduled' && b.type !== 'scheduled') return -1
          if (a.type !== 'scheduled' && b.type === 'scheduled') return 1
          // Then sort by date
          return new Date(a.date) - new Date(b.date)
        })
    }

    const paymentsData = this.props.loanData.payments || []

    let showPaymentHistory = paymentsData.map((item, k) => {
      console.log('showPaymentHistory item', item)
      let totalPaid = new BigNumber(item.principal)
        .plus(new BigNumber(item.interest))
        .plus(new BigNumber(item.fee))
      totalPaid = formatCurrency(totalPaid)
      const showDate = notificationFormatDate(item.paidAt)
      let type = item.type === 'liquidation' ? 'Liquidation' : 'Payment'
      let thirdRow = 'Interest'
      if (item.type == 'reward') {
        type = 'Reward'
        thirdRow = 'Deposit'
      }
      const itemPrincipal = formatCurrency(item.principal)
      const itemInterest = formatCurrency(item.interest)
      const itemFee =
        typeof item.fee === 'string' ? Number(item.fee).toFixed(2) : item.fee.toFixed(2)
      let reward = Number(item.reward).toFixed(2)

      let showBounce = false
      if (item.bounceFee && item.bounceFee != '0') {
        showBounce = Number(item.bounceFee).toFixed(2)
      }

      return (
        <View style={[styles.paymentRow]} key={k}>
          <View
            style={{
              flexDirection: 'column',
              justifyContent: 'flex-start',
            }}>
            <TextBold style={styles.paymentItemText}>{`${type}`}</TextBold>
            <TextReg style={styles.paymentItemText}>{'Principal'}</TextReg>
            <TextReg style={styles.paymentItemText}>{thirdRow}</TextReg>
            {showBounce && <TextReg style={styles.paymentItemText}>{`Bounce Fee`}</TextReg>}
            <TextReg style={styles.paymentItemText}>{`Fees`}</TextReg>
            <TextReg style={styles.paymentItemText}>{`Total`}</TextReg>
          </View>
          <View style={{flexDirection: 'column', alignItems: 'flex-end'}}>
            <TextReg style={styles.paymentItemText}>{`${showDate}`}</TextReg>
            <TextReg style={styles.paymentItemText}>{`$${itemPrincipal}`}</TextReg>
            <TextReg style={styles.paymentItemText}>{`$${itemInterest}`}</TextReg>
            {showBounce && <TextReg style={styles.paymentItemText}>{`$${showBounce}`}</TextReg>}
            <TextReg style={styles.paymentItemText}>{`$${itemFee}`}</TextReg>
            <TextBold style={styles.paymentItemText}>{`$${totalPaid}`}</TextBold>
          </View>
        </View>
      )
    })
    // <TextReg style={styles.paymentItemText}>{item.feesPaid}</TextReg>

    let showFuturePayments = upcomingPayment.map((item, k) => {
      let totalDue = (
        Number(item.principalDue) +
        Number(item.interestDue) -
        Number(item.reward)
      ).toFixed(2)
      totalDue = numberWithCommas(totalDue)

      let itemPrincipalDue = Number(item.principalDue).toFixed(2)
      itemPrincipalDue = numberWithCommas(itemPrincipalDue)
      let itemInterestDue = Number(item.interestDue).toFixed(2)
      itemInterestDue = numberWithCommas(itemInterestDue)
      let reward = Number(item.reward).toFixed(2)
      if (reward == '0.00' && item.type !== 'scheduled') {
        reward = null
      }

      const showDueDate = notificationFormatDate(item.date)
      let totalName = 'P+I'
      if (reward > 0) {
        totalName = 'P+I-R'
      }

      let title = 'Upcoming Payment'
      if (item.type === 'scheduled') {
        title = 'Scheduled Payment'
      }

      let showBounceFee = ''
      if (item.bounceFeeAmount) {
        showBounceFee = Number(item.bounceFeeAmount).toFixed(2)
      }

      let principalName = 'Principal'
      if (item.type === 'scheduled') {
        principalName = 'Amount'
      }

      return (
        <TouchableOpacity
          activeOpacity={item.type == 'scheduled' ? 0.5 : 1}
          style={[styles.paymentRow]}
          key={k}
          onPress={() => this.clickRow(item)}>
          <View
            style={{
              flexDirection: 'column',
              justifyContent: 'flex-start',
              marginTop: 4,
            }}>
            <TextBold style={styles.paymentItemText}>{title}</TextBold>
            <TextReg style={styles.paymentItemText}>{principalName}</TextReg>

            {item.type !== 'scheduled' && (
              <TextReg style={styles.paymentItemText}>{`Interest`}</TextReg>
            )}
            {reward && item.type !== 'scheduled' && (
              <TextReg style={styles.paymentItemText}>{`Reward`}</TextReg>
            )}
            {item.type == 'scheduled' && (
              <TextReg style={styles.paymentItemText}>{`Bounce Fee`}</TextReg>
            )}
            {item.type == 'scheduled' && (
              <TextReg style={styles.paymentItemText}>{`Processing Fee`}</TextReg>
            )}
            {item.type !== 'scheduled' && (
              <TextReg style={[styles.paymentItemText, {letterSpacing: 2}]}>{totalName}</TextReg>
            )}
          </View>
          <View style={{flexDirection: 'column', alignItems: 'flex-end'}}>
            <TextReg style={styles.paymentItemText}>{showDueDate}</TextReg>
            <TextReg style={styles.paymentItemText}>{`$${itemPrincipalDue}`}</TextReg>

            {item.type !== 'scheduled' && (
              <TextReg style={styles.paymentItemText}>{`$${itemInterestDue}`}</TextReg>
            )}
            {item.type == 'scheduled' && (
              <TextReg style={styles.paymentItemText}>{`$${showBounceFee}`}</TextReg>
            )}
            {item.type == 'scheduled' && (
              <TextReg style={styles.paymentItemText}>{`$1.00`}</TextReg>
            )}

            {reward && item.type !== 'scheduled' && (
              <TextReg style={styles.paymentItemText}>{`$${reward}`}</TextReg>
            )}
            {item.type !== 'scheduled' && (
              <TextBold style={styles.paymentItemText}>{`$${totalDue}`}</TextBold>
            )}
          </View>
        </TouchableOpacity>
      )
      //   <TextReg style={styles.paymentItemText}>{item.feesDue}</TextReg>
    })

    let historyShowMore = 0
    let futureShowMore = 0
    let historyShowMoreOpacity = 0
    let futureShowMoreOpacity = 0
    let historyPlaceholder = false
    let futurePlaceholder = false

    if (showPaymentHistory.length === 0) {
      historyPlaceholder = true
      showPaymentHistory = (
        <View
          style={{
            height: 120,
            alignSelf: 'stretch',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <TextReg>No Payment History</TextReg>
        </View>
      )
    }

    if (showFuturePayments.length === 0) {
      futurePlaceholder = true
      showFuturePayments = (
        <View
          style={{
            height: 120,
            alignSelf: 'stretch',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <TextReg>No Upcoming Payments</TextReg>
        </View>
      )
    }

    let historyLength = 60 + showPaymentHistory.length * 120
    let futureLength = 60 + showFuturePayments.length * 120

    //rewardRate > 0
    if (Number(this.props.loanData?.reward?.rewardRate || 0) > 0) {
      futureLength = 60 + showFuturePayments.length * 148
    }

    if (historyPlaceholder) {
      historyLength = 180
    }
    if (futurePlaceholder) {
      futureLength = 180
    }

    if (!historyPlaceholder && showPaymentHistory.length > 3) {
      historyShowMore = true
      if (!this.state.historySeeMore) {
        historyShowMore = 40
        historyShowMoreOpacity = 100
        showPaymentHistory = showPaymentHistory.slice(0, 3)
        historyLength = 20 + 3 * 120
        historyLength += 140
      }
    }

    if (!futurePlaceholder && showFuturePayments.length > 3) {
      futureShowMore = true
      if (!this.state.futureSeeMore) {
        futureShowMore = 40
        futureShowMoreOpacity = 100
        showFuturePayments = showFuturePayments.slice(0, 3)
        futureLength = 20 + 3 * 120
        futureLength += 140
      }
    }

    const heightInterpolated = this.scrollValue.interpolate({
      inputRange: [0, width],
      outputRange: [historyLength, futureLength],
    })

    const marginBottomInterpolated = this.scrollValue.interpolate({
      inputRange: [0, width],
      outputRange: [80, 80],
    })

    const showMoreOpacityInterpolated = this.scrollValue.interpolate({
      inputRange: [0, width],
      outputRange: [historyShowMore, futureShowMore],
    })

    //enable download for ios or android later
    let enableDown = false

    return (
      <Animated.View
        style={{
          height: this.state.paymentToggleState === 0 ? 'auto' : heightInterpolated,
          marginBottom: marginBottomInterpolated,
        }}>
        <Card>
          <View
            style={{
              alignSelf: 'stretch',
              flexDirection: 'column',
              alignItems: 'center',
            }}>
            <View
              style={{
                width,
                borderRadius: 20,
                height: 40,
                overflow: 'hidden',
                backgroundColor: '#f3f3f3',
              }}>
              <Animated.View>
                <Animated.View
                  style={{
                    width: halfWidth - 4,
                    position: 'absolute',
                    backgroundColor: '#00FFBD',
                    height: 34,
                    top: 3,
                    left: 4,
                    borderRadius: 20,
                    alignSelf: 'flex-start',
                    transform: [{translateX: xInterpolated}],
                    zIndex: 10,
                  }}
                />
              </Animated.View>

              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  zIndex: 30,
                }}>
                <TouchableOpacity onPress={this.toggleToHistory} style={styles.infoToggleOption}>
                  <Animated.Text
                    style={[
                      styles.ltvInfoHeaderTitle,
                      {
                        color: colorInterpolated,
                        fontFamily: 'Europa-Regular',
                      },
                    ]}>
                    {`Payment History`}
                  </Animated.Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={this.toggleToUpcoming} style={styles.infoToggleOption}>
                  <Animated.Text
                    style={[
                      styles.ltvInfoHeaderTitle,
                      {
                        color: colorInterpolatedInverse,
                        fontFamily: 'Europa-Regular',
                      },
                    ]}>
                    {`Upcoming Payments`}
                  </Animated.Text>
                </TouchableOpacity>
              </View>
            </View>

            <SwiperFlatList
              ref="swiper"
              onMomentumScrollEnd={this.onScrollEnd}
              index={0}
              scrollEventThrottle={16}
              onScroll={this.onScroll}>
              <View style={styles.ltvContainer}>
                {paymentsData?.length > 0 && (
                  <View
                    style={{
                      alignSelf: 'stretch',
                      justifyContent: 'space-between',
                      flexDirection: 'row',
                      alignItems: 'center',
                      marginTop: -10,
                      marginBottom: 4,
                    }}>
                    <TextBold style={{marginLeft: 10}}>{`Payments History`}</TextBold>
                    {enableDown && (
                      <TouchableOpacity
                        onPress={() => this.down()}
                        style={{
                          height: 40,
                          width: 40,
                          borderRadius: 12,
                          justifyContent: 'center',
                          alignItems: 'center',
                          marginRight: 10,
                        }}>
                        <Image
                          source={require('../../imgs/icons/cloud.png')}
                          style={{height: 34, width: 34, marginBottom: 2}}
                        />
                      </TouchableOpacity>
                    )}
                  </View>
                )}
                {showPaymentHistory}
              </View>
              <View style={styles.ltvContainer}>
                <TextBold
                  style={{
                    marginLeft: 10,
                    marginTop: -10,
                    marginBottom: 4,
                  }}>{`Upcoming Payments`}</TextBold>
                {showFuturePayments}
              </View>
            </SwiperFlatList>
          </View>
        </Card>
        <Animated.View
          style={{
            height: showMoreOpacityInterpolated,
            alignSelf: 'stretch',
            justifyContent: 'center',
            alignItems: 'center',
            opacity:
              this.state.paymentToggleState === 0 ? historyShowMoreOpacity : futureShowMoreOpacity,
          }}>
          <TouchableOpacity onPress={() => this.showMorePayments(this.scrollValue._value)}>
            <TextBold style={{color: '#00FFBD', fontSize: 18}}>Show More</TextBold>
          </TouchableOpacity>
        </Animated.View>
      </Animated.View>
    )
  }
}

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
})

export default connect(mapStateToProps)(LoanPayments)
