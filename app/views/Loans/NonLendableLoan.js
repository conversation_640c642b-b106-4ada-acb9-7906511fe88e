import React, {Component} from 'react'
import {Image, View} from 'react-native'
import {connect} from 'react-redux'

import {Button, TextReg, TextBold, Card} from '../../components'

class NonLendableLoan extends Component {
  render() {
    return (
      <Card marginTop={14}>
        <View
          style={{
            alignSelf: 'stretch',
            alignItems: 'center',
            marginLeft: 30,
            marginRight: 30,
          }}>
          <Image
            source={require('../../imgs/graphics/mapPin.png')}
            style={{
              height: 60,
              width: 44,
              marginBottom: 20,
              marginTop: 20,
            }}
          />
          <TextBold style={{fontSize: 18, textAlign: 'center'}}>We can’t currently LEND in your area.</TextBold>
          <TextReg style={{fontSize: 17, marginTop: 20, textAlign: 'center'}}>
            {`We have received your request, but we prioritize adding new locations by demand. We are constantly working to add new jurisdictions and will notify you when we can service you.`}
          </TextReg>
          <TextBold style={{fontSize: 17, marginTop: 20, textAlign: 'center'}}>
            {`In certain cases, we may still be able to service your loan request. Contact us to find out if you're eligible.`}
          </TextBold>
          <TextBold style={{fontSize: 17, marginTop: 20, color: '#00FFBD'}}><EMAIL></TextBold>
          <Button
            isLoading={this.props.loadingCancel}
            style={{marginTop: 20, marginBottom: 30}}
            onPress={() => this.props.cancelNonLendable(this.props.nonLendableLoanReqId)}>
            <TextReg
              style={{
                fontSize: 17,
                letterSpacing: 0.7,
                color: '#000',
              }}>{`CANCEL REQUEST`}</TextReg>
          </Button>
        </View>
      </Card>
    )
  }
}

const mapStateToProps = state => ({
  account: state.auth.account,
})

export default connect(mapStateToProps)(NonLendableLoan)
