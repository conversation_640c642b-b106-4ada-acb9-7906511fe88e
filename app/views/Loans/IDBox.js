import React, { Component } from 'react';
import { Image } from 'react-native';
import { connect } from 'react-redux';

import { Button, TextReg, TextBold, Card } from '../../components';
import IDPending from '../../imgs/IDPending.png';
import IDFailed from '../../imgs/IDFailed.png';

class IDBox extends Component {
	handleApply = () => {
	  this.props.navigation.navigate('IdentityVerification');
	}

	render () {
	  let showImg = IDFailed;
	  let reason = 'failed';
	  let imgHeight = 110;
	  let descripion = 'Error in processing verification';
	  if (this.props.idVerificationStatus === 'pending') {
	    showImg = IDPending;
	    reason = 'pending';
	    imgHeight = 124;
	    descripion = 'Estimated time 3-5 mins';
	  }
	  if (this.props.idVerificationStatus === 'rejected') {
	    descripion = 'Please contact customer support';
	  }
	  return (
	    <Card marginTop={14}>
	      <Image
	        source={showImg}
	        style={{
	          width: 173,
	          height: imgHeight,
	          marginTop: 50,
	          marginBottom: 40
	        }}
	      />
	      <TextBold style={{ color: '#000', fontSize: 22, marginBottom: 24 }}>{`ID Verification ${reason}`}</TextBold>
	      <TextReg
	        style={{
	          width: 270,
	          fontSize: 15,
	          marginBottom: 30,
	          textAlign: 'center'
	        }}
	      >{`${descripion}`}</TextReg>
	      {this.props.idVerificationStatus === 'retry' && (
	        <Button onPress={this.handleApply} style={{ marginBottom: 40 }}>
	          <TextBold style={{ fontSize: 17, letterSpacing: 0.7, color: '#fff' }}>{`RETRY ID VERIFICATION`}</TextBold>
	        </Button>
	      )}
	    </Card>
	  );
	}
}

const mapStateToProps = (state) => ({
  account: state.auth.account
});

export default connect(mapStateToProps)(IDBox);
