import React from 'react'
import {Modal, View, Image, TouchableOpacity} from 'react-native'

import {TextReg} from '../../components'

import styles from './styles'

const HelpModal = props => (
  <Modal
    animationType="fade"
    transparent
    visible={
      props.helpModalVisable && !props.showPinScreen && !props.pinIntroVisible
    }
    onRequestClose={() => props.toggleHelpModal()}>
    <View style={styles.helpModalBox}>
      <View style={styles.helpModalSquare}>
        <TouchableOpacity
          style={styles.helpModalX}
          onPress={() => {
            props.toggleHelpModal()
          }}>
          <Image
            source={require('../../imgs/closeX.png')}
            style={styles.closeModalImg}
          />
        </TouchableOpacity>
        <View style={styles.helpModalTitleBox}>
          <TextReg style={styles.helpModalTitle}>{props.title}</TextReg>
        </View>
        <View style={styles.helpModalDescriptionBox}>
          <TextReg style={styles.helpModalDescription}>{props.text}</TextReg>
        </View>
        <TouchableOpacity
          style={styles.helpModalButton}
          onPress={() => {
            props.toggleHelpModal()
          }}>
          <TextReg style={styles.helpModalButtonText}>OK</TextReg>
        </TouchableOpacity>
      </View>
    </View>
  </Modal>
)

export default HelpModal
