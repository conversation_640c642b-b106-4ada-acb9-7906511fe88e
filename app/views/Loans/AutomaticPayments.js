import React, {Component} from 'react'
import {View, Image, TouchableOpacity, Modal} from 'react-native'
import {connect} from 'react-redux'

import Switch from 'react-native-switch-pro'

import {TextBold, Card, TextReg, Button} from '../../components'
import PayCollateralDisclaimer from './OneTimePayment/PayCollateralDisclaimer'
import {updateBanks} from '../../store/user/user.actions'
import coinWhite from '../../imgs/icons/coinWhite.png'

import styles from './styles'

class AutomaticPayments extends Component {
  constructor(props) {
    super(props)
    this.state = {
      banksArr: [],
      refreshing: false,
      chooseBank: false,
      showPayCollateral: false,
      agree: false,
      loadingConfirm: false,
      justTurnedOn: false,
      showConfirmAuto: null,
    }
  }

  componentDidMount() {
    this.checkAchLimit()
  }

  checkAchLimit = () => {
    let payload = {
      newAchAmount: 0,
      date: '2025-04-15', //next payment date
      nextPaymentAmount: 1953.17, //monthly payment
    }
    console.log('autopay - payload', payload)
    this.props.WebService.achLimit(payload)
      .then(res => {
        console.log('autopay - achLimit res', res)
      })
      .catch(err => {
        console.log('autopay - achLimit err', err)
      })
  }

  removeBankFromLoan = (bankAccountId, type) => {
    this.setState({refreshing: true})
    this.props.WebService.removeBankFromLoan({
      purpose: type,
      referenceId: bankAccountId,
      loanId: this.props.loanData.id,
      referenceType: 'bank_account',
    })
      .then(res => {
        this.props.getLoansData()
        this.setPaymentPreference(null)
        this.setState({refreshing: false, chooseBank: false})
      })
      .catch(err => {
        this.setState({refreshing: false})
      })
  }

  connectBankToLoan = id => {
    this.props.WebService.connectBankToLoan({
      purpose: 'payment',
      loanId: this.props.loanData.id,
      referenceId: id,
      referenceType: 'bank_account',
    })
      .then(res => {
        console.log('successfully connected bank res', res)
        this.props.WebService.setPaymentPreference(this.props.loanData.id, 'ach').then(res => {
          console.log('success update type', res)
          this.props.getLoansData()
        })
      })
      .catch(err => {
        console.log('error connecting bank ', err)
      })

    this.setState({showConfirmAuto: null})
  }

  cancelChooseBank = () => {
    this.setState({chooseBank: false})
  }

  getBanks = () => {
    this.setState({refreshing: true})
    this.props.WebService.getBank(this.props.loanData.id)
      .then(res => {
        this.props.dispatch(updateBanks(res.data))
        this.setState({
          refreshing: false,
          chooseBank: true,
        })
      })
      .catch(err => {
        console.log('get banks', err)
        this.setState({refreshing: false})
      })
  }

  goToBanks = () => {
    this.props.navigation.navigate('Banking', {flow: 'autoPay'})
  }

  togglePayCollateralModal = () => {
    this.setState({showPayCollateral: !this.state.showPayCollateral, error: false})
  }

  setPaymentPreference = type => {
    this.props.WebService.setPaymentPreference(this.props.loanData.id, type)
      .then(res => {
        console.log('success update type', res)
        this.props.getLoansData()
      })
      .catch(err => {
        console.log('err updating type', err)
      })
  }

  confirmPayWithCollateral = () => {
    this.setState({error: false})
    this.setState({loadingConfirm: true})
    try {
      this.props.WebService.setPaymentPreference(this.props.loanData.id, 'crypto')
        .then(res => {
          if (res.url?.includes('setPaymentPreference') && res.status === 400) {
            this.setState({error: 'Cannot change within 72 hours of next loan'})
            setTimeout(() => {
              this.setState({error: false})
            }, 5000)
          }
          console.log('success -', res)
          this.setState({
            loadingConfirm: false,
            showPayCollateral: false,
          })
          this.props.getLoansData()
        })
        .catch(err => {
          this.setState({loadingConfirm: false})
          console.log('err updating type', err)
        })
    } catch (err) {
      console.log('outside err', err)
    }
  }

  toggleAgree = () => {
    this.setState({agree: !this.state.agree})
  }

  render() {
    let {justTurnedOn} = this.state
    const {automatedPaymentType, paymentBankAccount} = this.props.loanData

    const showBanks = this.props.banksArr.map((a, k) => {
      let showText = `${a.name} - *******${a.accountNumber}`
      if (a.isPlaid) {
        showText = `${a.name}`
      }
      if (!a.isPlaid) {
        return null
      }

      let badDwolla = false
      if (a?.dwolla?.exchange?.isReAuthRequired) {
        badDwolla = true
      }
      if (a?.dwolla?.fundingSource?.isRemoved) {
        badDwolla = true
      }
      if (a?.dwolla?.fundingSource?.status != 'verified') {
        badDwolla = true
      }
      if (badDwolla) {
        return null
      }
      return (
        <TouchableOpacity key={k} onPress={() => this.setState({showConfirmAuto: a.id})}>
          <Card cardWidth={280} style={{borderColor: '#00FFBD', borderWidth: 1, height: 50}}>
            <TextReg style={{alignSelf: 'flex-start', paddingLeft: 8, fontSize: 16, marginTop: 4}}>
              {showText}
            </TextReg>
          </Card>
        </TouchableOpacity>
      )
    })

    let noBank =
      this.props.launchDarkly[`disable-ach`] != null && this.props.launchDarkly[`disable-ach`]

    console.log('this.state.error', this.state.error)

    let showBank = <View />
    if (paymentBankAccount?.isInUSA) {
      showBank = (
        <View>
          <TextReg
            style={{
              marginBottom: 6,
            }}>{`Account Number: ****${paymentBankAccount?.accountNumber}`}</TextReg>
          <TextReg>{`Routing Number: ${paymentBankAccount?.routingNumber}`}</TextReg>
        </View>
      )
    } else {
      showBank = (
        <View>
          <TextReg
            style={{
              marginBottom: 6,
            }}>{`SWIFT: ****${paymentBankAccount?.swift}`}</TextReg>
          <TextReg>{`Account Number: ****${paymentBankAccount?.accountNumber}`}</TextReg>
        </View>
      )
    }

    if (paymentBankAccount?.isPlaid) {
      showBank = <View></View>
    }

    return (
      <>
        <Card marginTop={10}>
          <PayCollateralDisclaimer
            showPinScreen={this.props.showPinScreen}
            loadingConfirm={this.state.loadingConfirm}
            agree={this.state.agree}
            toggleAgree={this.toggleAgree}
            togglePayCollateralModal={this.togglePayCollateralModal}
            visible={this.state.showPayCollateral}
            confirmPayWithCollateral={this.confirmPayWithCollateral}
          />
          <View
            style={{
              alignSelf: 'stretch',
              marginBottom: 10,
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            <TextReg style={styles.statusTitlePayment}>AutoPay</TextReg>
            {(automatedPaymentType == 'ach' || automatedPaymentType == 'crypto') && (
              <View
                style={{
                  flexDirection: 'row',
                  marginTop: -1,
                  marginRight: 6,
                  alignItems: 'center',
                }}>
                <Switch
                  value={automatedPaymentType == 'ach' || automatedPaymentType == 'crypto'}
                  backgroundActive={'#00FFBD'}
                  backgroundInactive={'#666'}
                  style={{
                    height: 30,
                    width: 50,
                    borderRadius: 15,
                    borderWidth: 1,
                    borderColor: '#555',
                  }}
                  circleStyle={{height: 24, width: 24, borderRadius: 12, margin: 2, back: '#fff'}}
                  onAsyncPress={cb => {
                    if (automatedPaymentType) {
                      cb(true, () => this.setPaymentPreference(null))
                    } else {
                      cb(true, () => this.setPaymentPreference('ach'))
                    }
                  }}
                />
              </View>
            )}
          </View>

          {paymentBankAccount ? (
            <View
              style={{
                backgroundColor: '#28283D',
                borderRadius: 14,
                width: 300,
                paddingTop: 14,
                paddingLeft: 20,
                paddingBottom: 20,
                paddingRight: 20,
                marginBottom: 20,
                borderWidth: 1,
                borderColor: '#AAA',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <TextBold
                  style={{
                    fontSize: 17,
                    marginBottom: 8,
                    width: 200,
                  }}>
                  {paymentBankAccount.name}
                </TextBold>
                {this.state.refreshing ? (
                  <Image
                    source={require('../../imgs/loadingDots.gif')}
                    style={styles.loadingDotsUnlink}
                  />
                ) : (
                  <View style={{alignItems: 'center'}}>
                    <TouchableOpacity
                      onPress={() => this.removeBankFromLoan(paymentBankAccount.id, 'payment')}>
                      <TextBold style={{color: '#00FFBD', fontSize: 17}}>UNLINK</TextBold>
                    </TouchableOpacity>
                  </View>
                )}
              </View>

              {showBank}
            </View>
          ) : (
            <View style={{alignSelf: 'stretch', marginLeft: 20, marginRight: 20}}>
              {automatedPaymentType == 'crypto' ? (
                <View
                  style={{
                    backgroundColor: '#28283D',
                    borderRadius: 14,
                    width: 300,
                    paddingTop: 14,
                    paddingLeft: 20,
                    paddingBottom: 20,
                    paddingRight: 20,
                    marginBottom: 14,
                    alignItems: 'center',
                    borderWidth: 1,
                    borderColor: '#AAA',
                    marginTop: 10,
                  }}>
                  <TextBold style={{fontSize: 17, marginBottom: 10}}>Pay with Collateral</TextBold>
                  <TextReg style={{marginBottom: 14, textAlign: 'center'}}>
                    You’re making payments using your liquidated collateral.
                  </TextReg>
                  {!noBank && (
                    <TouchableOpacity onPress={() => this.setPaymentPreference(null)}>
                      <TextBold style={{color: '#00FFBD', fontSize: 17}}>
                        PAY WITH BANK INSTEAD
                      </TextBold>
                    </TouchableOpacity>
                  )}
                </View>
              ) : (
                <View style={{alignItems: 'center', alignSelf: 'stretch'}}>
                  {noBank ? (
                    <Image
                      source={coinWhite}
                      style={{
                        height: 64,
                        width: 64,
                        marginBottom: 30,
                        marginTop: 4,
                      }}
                    />
                  ) : (
                    <Image
                      source={require('../../imgs/graphics/bank.png')}
                      style={{
                        height: 80,
                        width: 80,
                        marginBottom: 24,
                        marginTop: 4,
                      }}
                    />
                  )}
                  {this.state.chooseBank ? (
                    <View style={{alignItems: 'center'}}>
                      <View
                        style={{
                          width: 280,
                          alignItems: 'center',
                        }}>
                        {showBanks}
                        <TouchableOpacity onPress={() => this.goToBanks()}>
                          <Card
                            cardWidth={280}
                            style={{borderColor: '#00FFBD', borderWidth: 1, height: 50}}>
                            <TextReg style={{color: '#00FFBD', fontSize: 16, marginTop: 4}}>
                              Add New Bank Account
                            </TextReg>
                          </Card>
                        </TouchableOpacity>

                        <TouchableOpacity onPress={() => this.cancelChooseBank()}>
                          <TextReg style={{fontSize: 16, marginTop: 4, marginBottom: 8}}>
                            Cancel
                          </TextReg>
                        </TouchableOpacity>
                      </View>
                    </View>
                  ) : (
                    <View style={{alignItems: 'center', alignSelf: 'stretch'}}>
                      {!noBank && (
                        <>
                          {this.state.refreshing ? (
                            <Image
                              source={require('../../imgs/loadingDots.gif')}
                              style={styles.loadingDots}
                            />
                          ) : (
                            <Button style={{alignSelf: 'stretch'}} onPress={() => this.getBanks()}>
                              <TextReg style={{color: '#000', fontSize: 17}}>CONNECT BANK</TextReg>
                            </Button>
                          )}
                        </>
                      )}
                      {noBank ? (
                        <TouchableOpacity
                          style={{
                            marginTop: !noBank ? 20 : 0,
                            marginBottom: 20,
                            borderColor: '#00FFBD',
                            borderWidth: 2,
                            padding: 10,
                            paddingLeft: 20,
                            paddingRight: 20,
                            borderRadius: 14,
                          }}
                          onPress={() => this.togglePayCollateralModal()}>
                          <TextBold style={{color: '#00FFBD', fontSize: 17}}>
                            PAY WITH COLLATERAL
                          </TextBold>
                        </TouchableOpacity>
                      ) : (
                        <TouchableOpacity
                          style={{marginTop: !noBank ? 20 : 0, marginBottom: 20}}
                          onPress={() => this.togglePayCollateralModal()}>
                          <TextBold style={{color: '#00FFBD', fontSize: 17}}>
                            PAY WITH COLLATERAL
                          </TextBold>
                        </TouchableOpacity>
                      )}
                      {this.state.error && (
                        <TextReg style={{color: '#FF7074', marginBottom: 10}}>
                          {this.state.error}
                        </TextReg>
                      )}
                    </View>
                  )}
                </View>
              )}
            </View>
          )}
        </Card>
        {this.state.showConfirmAuto && (
          <Modal
            animationType="fade"
            transparent
            visible={!this.props.showPinScreen}
            onRequestClose={() => this.setState({showConfirmAuto: null})}>
            <View style={styles.removeBox}>
              <View style={styles.removeInnerBox}>
                <View style={styles.removeRow}>
                  <TouchableOpacity onPress={() => this.setState({showConfirmAuto: null})}>
                    <Image
                      source={require('../../imgs/closeX.png')}
                      style={{height: 26, width: 26}}
                    />
                  </TouchableOpacity>
                </View>
                <View>
                  <TextBold style={{fontSize: 20}}>Turn on ACH Payments</TextBold>
                </View>
                <TextReg
                  style={
                    styles.removeTextDisclaimer
                  }>{`I agree that future ACH payments through SALT will be processed by the Dwolla payment system from the selected account above. In order to cancel this authorization, I will change my payment settings within my account.`}</TextReg>
                <View style={styles.removeButton}>
                  <TouchableOpacity
                    onPress={() => this.connectBankToLoan(this.state.showConfirmAuto)}>
                    <View style={styles.removeButtonText}>
                      <TextReg style={{fontSize: 18}}>OK</TextReg>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>
        )}
        {automatedPaymentType == 'crypto' && justTurnedOn && (
          <View style={{alignSelf: 'stretch', marginTop: 0, alignItems: 'center'}}>
            <TouchableOpacity
              onPress={() => this.props.navigation.goBack()}
              style={{
                height: 44,
                width: 120,
                borderRadius: 14,
                borderColor: '#999',
                borderWidth: 1,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <TextReg style={{fontSize: 17}}>back</TextReg>
            </TouchableOpacity>
          </View>
        )}
      </>
    )
  }
}

AutomaticPayments.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  WebService: state.auth.WebService,
  showPinScreen: state.auth.pinScreen,
  banksArr: state.user.banks,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(AutomaticPayments)
