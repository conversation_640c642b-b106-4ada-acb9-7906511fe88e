import React, {Component} from 'react'
import {View, Text, TouchableOpacity, Image, TouchableHighlight, Dimensions} from 'react-native'
import {connect} from 'react-redux'

import * as Keychain from 'react-native-keychain'
const {height: ScreenHeight} = Dimensions.get('window')

import {updatePin, showPinScreen} from '../../store/auth/auth.actions'
import {allowBackToSettings} from '../../store/user/user.actions'
import {Background, TextReg} from '../../components'

import styles from './styles'

class PinIntro extends Component {
  constructor(props) {
    super(props)
    this.state = {
      pin: '',
      confirmPinState: false,
      confirmPin: '',
      pinError: false,
      activePin: -1,
      confirmCurrentPin: false,
      incorrectMatch: false,
    }
  }

  goBackToSettings = () => {
    this.props.dispatch(allowBackToSettings(false))
    //this.props.navigation.navigate('Settings')
  }

  handleEnterPin = num => {
    let pin = ''
    if (this.state.confirmPinState) {
      pin = this.state.confirmPin
    } else {
      pin = this.state.pin
    }
    let updatePin = false
    if (num === 'back' && pin.length > 0) {
      pin = pin.substring(0, pin.length - 1)
      updatePin = true
    } else if (num !== 'back' && pin.length < 3) {
      pin = `${pin}${num}`
      updatePin = true
    } else if (pin.length === 3) {
      pin = `${pin}${num}`
      this.handleFinishPin(pin)
    }

    if (updatePin && this.state.confirmPinState) {
      this.setState({confirmPin: pin, pinError: false, activePin: pin.length})
    } else if (updatePin) {
      this.setState({
        pin,
        incorrectMatch: false,
        pinError: false,
        activePin: pin.length,
      })
    }
  }

  handleFinishPin = pin => {
    if (this.state.confirmPinState) {
      if (pin === this.state.pin && pin.length === 4) {
        this.setState({activePin: 4})
        this.savePin(pin.toString())

        this.props.dispatch(updatePin(pin))
        this.props.dispatch(showPinScreen(false))
        this.props.dispatch(allowBackToSettings(false))
        this.setState({
          pin: '',
          confirmPin: '',
          confirmPinState: false,
          activePin: -1,
        })
      } else {
        this.setState({
          pin: '',
          confirmPin: '',
          confirmPinState: false,
          activePin: -1,
          incorrectMatch: true,
        })
      }
    } else {
      const isPinSecure = this.checkForInsecurePin(pin)
      if (isPinSecure) {
        this.setState({pin, confirmPinState: true, activePin: -1})
      } else {
        this.setState({pin: '', activePin: -1, pinError: 'insecure'})
      }
    }
  }

  checkForInsecurePin = pin => {
    const insecurePins = [
      '0000',
      '1111',
      '2222',
      '3333',
      '4444',
      '5555',
      '6666',
      '7777',
      '8888',
      '9999',
      '0000',
      '1234',
      '4321',
      '2580',
      '0852',
      '1337',
    ]
    if (insecurePins.includes(pin)) {
      return false
    }
    return true
  }

  async savePin(pin) {
    const {account} = this.props
    const lowerCaseEmail = account.email?.toLowerCase()
    try {
      await Keychain.setInternetCredentials(`pin-${lowerCaseEmail}`, 'user', pin, {
        securityLevel: Keychain.SECURITY_LEVEL.ANY,
        storage: Keychain.STORAGE_TYPE.AES,
      })

      console.log('saved intro', `${lowerCaseEmail}-pin`, 'user', pin)
    } catch (err) {
      console.log('saved intro err', err)
    }
  }

  handleBackPin = () => {
    this.setState({
      confirmPinState: false,
      confirmPin: '',
      pin: '',
      activePin: -1,
    })
  }

  render() {
    const underlayColor = '#ffffff30'

    let description = this.state.confirmPinState ? `Confirm PIN` : `Choose PIN`
    if (this.state.confirmCurrentPin) {
      description = `Current PIN`
    }

    console.log('pinintro')

    return (
      <View style={{alignSelf: 'stretch', height: ScreenHeight, zIndex: 70}}>
        <View style={[styles.pinContainer, {paddingTop: 20}]}>
          {this.props.backToSettings && (
            <TouchableOpacity style={styles.backToSettingsButton} onPress={() => this.goBackToSettings()}>
              <Image source={require('../../imgs/backToSettings.png')} style={styles.backToSettingsImg} />
            </TouchableOpacity>
          )}
          <View style={styles.pinTopBox}>
            <Image source={require('../../imgs/saltLogoWhite.png')} style={styles.saltLogoWhite} />

            <TextReg style={styles.pinTitle}>{description}</TextReg>

            <View style={styles.pinBubbleBox}>
              <View style={this.state.activePin > 0 ? styles.pinBubbleActive : styles.pinBubble} />
              <View style={this.state.activePin > 1 ? styles.pinBubbleActive : styles.pinBubble} />
              <View style={this.state.activePin > 2 ? styles.pinBubbleActive : styles.pinBubble} />
              <View style={this.state.activePin > 3 ? styles.pinBubbleActive : styles.pinBubble} />
            </View>
            {this.state.pinError && <Text style={styles.pinTitle}>Error</Text>}
            {this.state.pinError === 'insecure' && <Text style={styles.pinTitleInsecure}>Insecure PIN</Text>}
            {this.state.incorrectMatch && (
              <View style={styles.pinIncorrectMatchBox}>
                <Text style={styles.pinIncorrectMatch1}>{`PINs did not match`}</Text>
                <Text style={styles.pinIncorrectMatch2}>{`Try Again`}</Text>
              </View>
            )}
          </View>
          <View>
            <View style={styles.pinInputBox}>
              <View style={styles.pinInputRow}>
                <TouchableHighlight style={styles.pinInputButton} underlayColor={underlayColor} onPress={() => this.handleEnterPin(1)}>
                  <View>
                    <Text style={styles.pinInputNum}>1</Text>
                    <Text style={styles.pinInputDetails} />
                  </View>
                </TouchableHighlight>
                <TouchableHighlight style={styles.pinInputButton} underlayColor={underlayColor} onPress={() => this.handleEnterPin(2)}>
                  <View>
                    <Text style={styles.pinInputNum}>2</Text>
                    <Text style={styles.pinInputDetails}>ABC</Text>
                  </View>
                </TouchableHighlight>
                <TouchableHighlight style={styles.pinInputButton} underlayColor={underlayColor} onPress={() => this.handleEnterPin(3)}>
                  <View>
                    <Text style={styles.pinInputNum}>3</Text>
                    <Text style={styles.pinInputDetails}>DEF</Text>
                  </View>
                </TouchableHighlight>
              </View>
              <View style={styles.pinInputRow}>
                <TouchableHighlight style={styles.pinInputButton} underlayColor={underlayColor} onPress={() => this.handleEnterPin(4)}>
                  <View>
                    <Text style={styles.pinInputNum}>4</Text>
                    <Text style={styles.pinInputDetails}>GHI</Text>
                  </View>
                </TouchableHighlight>
                <TouchableHighlight style={styles.pinInputButton} underlayColor={underlayColor} onPress={() => this.handleEnterPin(5)}>
                  <View>
                    <Text style={styles.pinInputNum}>5</Text>
                    <Text style={styles.pinInputDetails}>JKL</Text>
                  </View>
                </TouchableHighlight>
                <TouchableHighlight style={styles.pinInputButton} underlayColor={underlayColor} onPress={() => this.handleEnterPin(6)}>
                  <View>
                    <Text style={styles.pinInputNum}>6</Text>
                    <Text style={styles.pinInputDetails}>MNO</Text>
                  </View>
                </TouchableHighlight>
              </View>
              <View style={styles.pinInputRow}>
                <TouchableHighlight style={styles.pinInputButton} underlayColor={underlayColor} onPress={() => this.handleEnterPin(7)}>
                  <View>
                    <Text style={styles.pinInputNum}>7</Text>
                    <Text style={styles.pinInputDetails}>PQRS</Text>
                  </View>
                </TouchableHighlight>
                <TouchableHighlight style={styles.pinInputButton} underlayColor={underlayColor} onPress={() => this.handleEnterPin(8)}>
                  <View>
                    <Text style={styles.pinInputNum}>8</Text>
                    <Text style={styles.pinInputDetails}>TUV</Text>
                  </View>
                </TouchableHighlight>
                <TouchableHighlight style={styles.pinInputButton} underlayColor={underlayColor} onPress={() => this.handleEnterPin(9)}>
                  <View>
                    <Text style={styles.pinInputNum}>9</Text>
                    <Text style={styles.pinInputDetails}>WXYZ</Text>
                  </View>
                </TouchableHighlight>
              </View>
              <View style={styles.pinInputRowLast}>
                <TouchableHighlight style={styles.pinInputButton} underlayColor={underlayColor} onPress={() => this.handleEnterPin(0)}>
                  <View>
                    <Text style={styles.pinInputNum}>0</Text>
                    <Text style={styles.pinInputDetails} />
                  </View>
                </TouchableHighlight>
                <TouchableHighlight style={styles.pinInputButton} underlayColor={underlayColor} onPress={() => this.handleEnterPin('back')}>
                  <View>
                    <Text style={styles.pinInputNumDelete}>{'Delete'}</Text>
                    <Text style={styles.pinInputDetails} />
                  </View>
                </TouchableHighlight>
              </View>
            </View>

            {this.state.confirmPinState && (
              <View style={styles.pinBackBox}>
                <TouchableOpacity onPress={() => this.handleBackPin()}>
                  <Text style={styles.pinBack}>Back</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
        <Background />
      </View>
    )
  }
}

const mapStateToProps = state => ({
  pin: state.auth.pin,
  account: state.auth.account,
  storedPin: state.auth.pin,
  backToSettings: state.user.backToSettings,
})

export default connect(mapStateToProps)(PinIntro)
