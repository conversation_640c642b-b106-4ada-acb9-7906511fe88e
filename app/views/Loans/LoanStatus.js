import React, {Component} from 'react'
import {View, TouchableOpacity, Image} from 'react-native'
import {connect} from 'react-redux'

import {Card, TextReg, TextBold, LoanHealthBar, ShowThresholds} from '../../components'

import styles from './styles'

class LoanStatus extends Component {
  constructor(props) {
    super(props)
    this.state = {
      barWidth: 320,
    }
  }

  getBarWidth = layout => {
    const {x, y, width, height} = layout
    this.setState({barWidth: width - 20})
  }

  render() {
    const {ltv, thresholds, marginManagementPreference} = this.props.loanData
    const forStabilization = marginManagementPreference === 'stabilization' ? true : false
    const defaultThresholds = [
      {color: '#f7d956', name: 'Warning', percentage: '75%', type: 'warning'},
      {
        color: '#e5705a',
        name: 'Margin Call',
        percentage: '75%',
        type: 'marginCall',
      },
      {
        color: '#afafaf',
        name: forStabilization ? 'Stabilization' : 'Liquidation',
        percentage: '75%',
        type: 'liquidation',
      },
    ]

    const thresholdsArr = []
    for (const property in thresholds) {
      thresholdsArr.push({
        type: property,
        percentage: thresholds[property],
      })
    }
    const formattedThresholds = defaultThresholds.map(a => {
      const decimalPercentage = thresholdsArr.filter(b => b.type === a.type)[0]?.percentage || []
      let percentage = (decimalPercentage * 100).toFixed(2)
      percentage = `${percentage}%`
      return {
        ...a,
        percentage,
        decimalPercentage,
      }
    })

    const awaitingLiquidation = this.props.loanData?.awaitingLiquidation || false

    let decimalLtv = this.props.loanData?.ltv || 0.0

    const accounts = this.props.user?.accounts || ['test']
    if (this.props.accountRef == '0') return null
    const isStabilized = accounts.filter(a => a.ref == this.props.accountRef)[0]?.product?.loan
      ?.isStabilized

    if (decimalLtv > this.props.loanData.thresholds?.liquidation && !isStabilized) {
      decimalLtv = this.props.loanData.thresholds?.liquidation
    }

    if (awaitingLiquidation) {
      decimalLtv = this.props.loanData.thresholds?.liquidation
    }

    const {barWidth} = this.state
    let ltvWidth = decimalLtv * barWidth
    if (ltvWidth > barWidth) {
      ltvWidth = barWidth
    }

    const formattedltv = (Number(decimalLtv) * 100).toFixed(2)

    let barColor = '#34e89e'
    if (decimalLtv > formattedThresholds[0].decimalPercentage) {
      barColor = formattedThresholds[0].color
    }
    if (decimalLtv > formattedThresholds[1].decimalPercentage) {
      barColor = formattedThresholds[1].color
    }
    if (decimalLtv >= formattedThresholds[2].decimalPercentage) {
      barColor = formattedThresholds[2].color
    }

    if (isStabilized) {
      barColor = '#afafaf'
    }

    const liquidationNumber = (Number(this.props.loanData.thresholds?.liquidation) * 100).toFixed(2)

    const marginCureNumber = (Number(this.props.loanData.thresholds?.marginCure) * 100).toFixed(2)

    return (
      <Card style={{paddingTop: 0}} marginTop={0}>
        {ltv >= this.props.loanData.thresholds?.warning && (
          <View style={styles.loanStatusGreyBar} />
        )}
        <View style={styles.progressTitle}>
          <View style={{width: 18, height: 18, marginLeft: 22}} />
          <TextReg style={styles.progressTitleText}>Loan Health</TextReg>
          <TouchableOpacity onPress={() => this.props.toggleHelpModal()}>
            <Image
              source={require('../../imgs/info.png')}
              style={{
                height: 20,
                width: 20,
                borderRadius: 0,
                marginRight: 22,
                opacity: 0.7,
              }}
            />
          </TouchableOpacity>
        </View>
        <View
          style={{alignSelf: 'stretch'}}
          onLayout={event => {
            this.getBarWidth(event.nativeEvent.layout)
          }}>
          <LoanHealthBar
            barWidth={barWidth}
            ltv={formattedltv}
            barColor={barColor}
            ltvWidth={ltvWidth}
            forStabilization={forStabilization}
          />
        </View>
        <ShowThresholds thresholds={formattedThresholds} />

        {awaitingLiquidation && (
          <View style={styles.liquidationInfoBox}>
            <TextReg style={styles.liquidationInfoText}>
              <TextReg>{`Your Loan-to-Value (LTV) ratio has `}</TextReg>
              <TextBold>{`exceeded ${liquidationNumber}%`}</TextBold>
              <TextReg>{`, and a ${
                forStabilization ? 'stabilization' : 'liquidation'
              } of collateral is in progress. A portion of your collateral will be ${
                forStabilization ? 'stabilized' : 'liquidated'
              } to preserve value.`}</TextReg>
            </TextReg>
            <TextReg
              style={
                styles.liquidationInfoText
              }>{`Transfers to and from your wallet will be momentarily disabled during this process.`}</TextReg>
            <TextReg
              style={
                styles.liquidationInfoText
              }>{`For more information about this policy, please refer to your Master Loan Terms. If you have any questions, our Customer Support team will be happy to assist you.`}</TextReg>
          </View>
        )}

        <View style={styles.progressDisclaimerBox}>
          <TextReg style={styles.progressDisclaimer}>
            Image provided for representative purposes only. Loan data may be delayed, inaccurate,
            or stale. Please see Borrower Portal for updates.
          </TextReg>
        </View>
      </Card>
    )
  }
}

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  user: state.user.user,
  accountRef: state.auth.account.ref,
})

export default connect(mapStateToProps)(LoanStatus)
