import React from 'react'
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native'
import {Card, TextReg, TextBold, BackgroundHeader} from '../../../components'
import commonStyles from '../../../styles/commonStyles'
import {useNavigation} from '@react-navigation/native'
import moment from 'moment'

const PaymentInfo = ({route}) => {
  const navigation = useNavigation()
  const {payment} = route.params

  let next = () => {
    navigation.navigate('CancelPayment', {payment})
  }

  console.log('payment - details ', payment)
  let showIntent = payment.intent
  showIntent = showIntent.toUpperCase()

  let showAmount = payment.amount
  showAmount = showAmount.replace(/,/g, '')
  showAmount = Number(showAmount).toFixed(2)
  console.log('showAmount', showAmount)

  let showBank = payment?.bankAccount?.name
  showBank = showBank.toUpperCase()

  let showCreated = payment.createdAt
  showCreated = moment(showCreated).format('MMM DD, YYYY HH:mm')

  let showEffective = payment.date
  showEffective = moment(showEffective).format('MMM DD, YYYY')

  let showBounced = payment.bounceFeeAmount
  if (showBounced == '0') {
    showBounced = false
  } else {
    showBounced = showBounced.replace(/,/g, '')
    showBounced = Number(showBounced).toFixed(2)
  }

  return (
    <View style={commonStyles.tileContainer}>
      <BackgroundHeader title={'Scheduled Payment Details'} goBack={() => navigation.goBack()} />

      <Card style={{marginTop: 10}}>
        <View style={styles.row}>
          <Text style={styles.label}>Created At</Text>
          <Text style={styles.value}>{showCreated}</Text>
        </View>

        <View style={styles.row}>
          <Text style={styles.label}>Effective Date</Text>
          <Text style={styles.value}>{showEffective}</Text>
        </View>

        <View style={styles.row}>
          <Text style={styles.label}>Intent</Text>
          <Text style={styles.value}>{showIntent}</Text>
        </View>

        {showBank && (
          <View style={styles.row}>
            <Text style={styles.label}>Bank Account</Text>
            <Text
              style={{
                fontSize: 16,
                color: '#FFF',
                flex: 2,
                textAlign: 'right',
                flexWrap: 'wrap',
              }}>
              {showBank}
            </Text>
          </View>
        )}

        <View style={styles.row}>
          <Text style={styles.label}>Processing Fee</Text>
          <Text style={styles.value}>{'$1.00'}</Text>
        </View>

        {showBounced && (
          <View style={styles.row}>
            <Text style={styles.label}>Bounced ACH Fee</Text>
            <Text style={styles.value}>{`$${showBounced}`}</Text>
          </View>
        )}

        <View style={[styles.row, {borderBottomWidth: 0}]}>
          <Text style={styles.label}>Amount:</Text>
          <Text style={styles.value}>${showAmount}</Text>
        </View>
      </Card>
      <TouchableOpacity style={styles.cancelButton} onPress={() => next()}>
        <TextBold style={styles.cancelText}>CANCEL PAYMENT</TextBold>
      </TouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  row: {
    flexDirection: 'row',
    alignSelf: 'stretch',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: '#ccc',
    marginLeft: 6,
    marginRight: 6,
  },
  label: {
    fontSize: 16,
    color: '#FFF',
  },
  value: {
    fontSize: 16,
    color: '#FFF',
  },
  cancelText: {
    fontSize: 20,
    color: '#E5705A',
    marginTop: 10,
    marginBottom: 10,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
})

export default PaymentInfo
