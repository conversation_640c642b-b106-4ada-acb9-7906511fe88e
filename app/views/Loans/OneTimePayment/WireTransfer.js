import React, {Component} from 'react'
import {View, Image, TouchableOpacity, ScrollView} from 'react-native'
import {connect} from 'react-redux'

import {TextBold, Card, TextReg, Button, BackgroundHeader} from '../../../components'
import {numberWithCommas} from '../../../util/helpers'

import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'

class WireTransfer extends Component {
  constructor(props) {
    super(props)
    this.state = {
      optionSelect: null,
      hasBounce: false,
    }
  }

  componentDidMount() {
    this.getMyLoan()
  }

  getMyLoan = () => {
    this.props.WebService.getLoans()
      .then(res => {
        if (res?.data?.length > 0) {
          let haBounce = res?.data[0]?.bounceFeeAmount
          haBounce = Number(haBounce || false)
          if (haBounce === 0) {
            haBounce = false
          }
          this.setState({hasBounce: haBounce})
        }
      })
      .catch(err => {
        console.log('getMyLoan - err', err)
      })
  }

  close = () => {
    this.props.navigation.popToTop()
  }

  render() {
    const {ltv, thresholds, automatedPaymentType} = this.props.loanData
    const lender = this.props.route.params.lender
    const isPayoff = this.props.route.params.payoff ? true : false
    const payoffAmount = this.props.route.params.payoffAmount
    const payonDate = this.props.route.params.payonDate
    const first8 = this.props.loanData.id.slice(0, 8)

    let bounceFee = this.props.loanData?.bounceFeeAmount || 0

    let payoffNumber = payoffAmount?.split('$').join('')?.split(',').join('')

    let totalAmount = Number(payoffNumber) + Number(this.state.hasBounce)

    totalAmount = numberWithCommas(totalAmount?.toFixed(2))

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Wire Transfer'} goBack={this.props.navigation.goBack} />
        {isPayoff && (
          <TextReg style={{marginTop: 24, marginBottom: 12, fontSize: 24, alignSelf: 'center'}}>
            Confirm Details
          </TextReg>
        )}
        <ScrollView style={{alignSelf: 'stretch'}} contentContainerStyle={{alignItems: 'center'}}>
          <View style={{width: 300, marginTop: 30}}>
            {isPayoff && (
              <React.Fragment>
                <TextBold style={styles.wireTransferTitle}>Payoff Amount</TextBold>
                <View style={styles.wireTransferBox}>
                  <TextReg style={styles.wireTransferText}>${payoffAmount}</TextReg>
                </View>

                {this.state.hasBounce && (
                  <>
                    <TextBold style={styles.wireTransferTitle}>Bounce Fee</TextBold>
                    <View style={styles.wireTransferBox}>
                      <TextReg style={styles.wireTransferText}>${this.state.hasBounce}</TextReg>
                    </View>
                    <TextBold style={styles.wireTransferTitle}>Total Amount</TextBold>
                    <View style={styles.wireTransferBox}>
                      <TextReg style={styles.wireTransferText}>${totalAmount}</TextReg>
                    </View>
                  </>
                )}
                <TextBold style={styles.wireTransferTitle}>Date to send payment</TextBold>
                <View style={styles.wireTransferBox}>
                  <TextReg style={styles.wireTransferText}>{payonDate}</TextReg>
                </View>
              </React.Fragment>
            )}
            <TextBold style={styles.wireTransferTitle}>Bank</TextBold>
            <View style={styles.wireTransferBox}>
              <TextReg style={styles.wireTransferText}>{lender.bankName}</TextReg>
              <TextReg style={styles.wireTransferText}>{lender.bankAddress1}</TextReg>
              {lender.bankAddress2 && (
                <TextReg style={styles.wireTransferText}>{lender.bankAddress2}</TextReg>
              )}
              <TextReg
                style={
                  styles.wireTransferText
                }>{`${lender.bankCity}, ${lender.bankProvince} ${lender.bankPostalCode}`}</TextReg>
            </View>
            <TextBold style={styles.wireTransferTitle}>ABA Number</TextBold>
            <View style={styles.wireTransferBox}>
              <TextReg style={styles.wireTransferText}>{lender.bankABANumber}</TextReg>
            </View>
            <TextBold style={styles.wireTransferTitle}>Beneficiary</TextBold>
            <View style={styles.wireTransferBox}>
              <TextReg style={styles.wireTransferText}>{lender.bankCreditTo}</TextReg>
              <TextReg style={styles.wireTransferText}>{lender.address1}</TextReg>
              {lender.address2 && (
                <TextReg style={styles.wireTransferText}>{lender.address2}</TextReg>
              )}
              <TextReg
                style={
                  styles.wireTransferText
                }>{`${lender.city}, ${lender.province} ${lender.postalCode}`}</TextReg>
            </View>
            <TextBold style={styles.wireTransferTitle}>Account Number</TextBold>
            <View style={styles.wireTransferBox}>
              <TextReg style={styles.wireTransferText}>{lender.bankAccountNumber}</TextReg>
            </View>
            <TextBold style={styles.wireTransferTitle}>SWIFT</TextBold>
            <View style={styles.wireTransferBox}>
              <TextReg style={styles.wireTransferText}>{lender.bankSwift}</TextReg>
            </View>

            {isPayoff ? (
              <React.Fragment>
                <TextBold style={styles.wireTransferTitle}>Memo</TextBold>
                <TextReg style={styles.wireTransferText}>
                  Include the following code in the Memo/Notes field, depending on the purpose of
                  your payment:
                </TextReg>
                <TextBold style={{...styles.wireTransferTitle, marginVertical: 16}}>
                  • PO-{first8}
                </TextBold>
              </React.Fragment>
            ) : (
              <React.Fragment>
                <TextBold style={styles.wireTransferTitle}>FFC/Ref</TextBold>
                <View style={styles.wireTransferBox}>
                  <TextReg style={styles.wireTransferText}>
                    Include one of the following codes in the FFC/Ref field, depending on the
                    purpose of your payment:
                  </TextReg>
                  <TextReg style={{marginLeft: 20, fontSize: 16, marginTop: 4}}>
                    - Monthly Payment: <TextBold style={{fontSize: 16}}>MP-{first8}</TextBold>
                  </TextReg>
                  <TextReg style={{marginLeft: 20, fontSize: 16, marginTop: 4}}>
                    - Principal Payment: <TextBold style={{fontSize: 16}}>PR-{first8}</TextBold>
                  </TextReg>
                </View>
              </React.Fragment>
            )}
          </View>

          {bounceFee > 0 && (
            <View style={{marginTop: 16, marginBottom: 30, width: 300}}>
              <TextReg style={{fontSize: 16, color: 'red', textAlign: 'center'}}>
                {`Please include an additional $${bounceFee} as a bounce fee due to the failure of your last ACH payment`}
              </TextReg>
            </View>
          )}

          <Button
            style={{marginTop: 16, marginBottom: 30, width: 300}}
            onPress={() => this.close()}>
            Close
          </Button>
        </ScrollView>
      </View>
    )
  }
}

WireTransfer.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  //headerTintColor: '#FFF',
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    //shadowColor: 'transparent',
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  accountRef: state.auth.account.ref,
  loanData: state.user.loanData,
  user: state.user.user,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
  refreshCount: state.user.refreshCount,
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(WireTransfer)
