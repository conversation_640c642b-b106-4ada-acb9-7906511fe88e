import React from 'react'
import {Modal, View, Image, TouchableOpacity, ScrollView, Linking} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'

import {Button, TextReg, TextBold} from '../../../components'
import {askingForPermissions} from '../../../store/auth/auth.actions'

import styles from '../styles'
import agreedYes from '../../../imgs/agreedYes.png'
import agreedNo from '../../../imgs/agreedNo.png'

const PayCollateralDisclaimer = props => {
  let dispatch = useDispatch()
  let goLink = () => {
    dispatch(askingForPermissions(true))
    Linking.openURL('https://saltlending.com/rates-and-fees').catch(err => console.error('An error occurred', err))
  }
  return (
    <Modal animationType="fade" transparent visible={props.visible && !props.showPinScreen} onRequestClose={() => ({})}>
      <View style={styles.helpModalBox}>
        <ScrollView
          style={{alignSelf: 'stretch', paddingTop: 80}}
          contentContainerStyle={{
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <View style={styles.helpModalSquare}>
            <TouchableOpacity
              style={styles.helpModalX}
              onPress={() => {
                props.togglePayCollateralModal()
              }}>
              <Image source={require('../../../imgs/closeX.png')} style={styles.closeModalImg} />
            </TouchableOpacity>
            <View style={styles.helpModalTitleBox}>
              <TextBold style={styles.helpModalTitle}>Pay with Collateral</TextBold>
            </View>
            <View style={[styles.helpModalDescriptionBox, {marginBottom: 10}]}>
              <TextReg style={styles.helpModalDescription}>
                By agreeing, you are authorizing SALT to accept your cryptocurrency collateral as a form of payment toward your loan balance
                on each monthly payment due date. Payments from collateral will be applied, first, from SALT tokens and/or Stablecoins (at
                SALT’s election) at no fee, then from other cryptocurrency assets held as collateral, which will be subject to a liquidation
                fee. Please check our{' '}
                <TouchableOpacity onPress={() => goLink()} style={{marginTop: -3}}>
                  <TextReg style={{textDecorationLine: 'underline', color: '#00FFBD', fontSize: 15}}>Website </TextReg>
                </TouchableOpacity>
                for additional information regarding fees.
              </TextReg>
              <TextReg>
                Please note the reduction in your collateral will impact your Loan-to-Value ratio (LTV). You are responsible for monitoring
                the health of your loan and making margin calls as necessary. Payments with collateral will continue until you select a
                different payment method. You can opt-out at any time.
              </TextReg>
            </View>
            <TouchableOpacity onPress={() => props.toggleAgree()}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  alignSelf: 'stretch',
                  marginTop: 4,
                  marginBottom: 4,
                }}>
                <Image source={props.agree ? agreedYes : agreedNo} style={{height: 20, width: 20, marginRight: 8}} />
                <TextReg style={{fontSize: 20}}>I agree to the above terms</TextReg>
              </View>
            </TouchableOpacity>
            <Button
              disabled={!props.agree}
              style={{marginBottom: 30, marginTop: 20}}
              onPress={() => props.confirmPayWithCollateral()}
              isLoading={props.loadingConfirm}>
              <TextBold style={{color: '#000', fontSize: 17}}>SUBMIT</TextBold>
            </Button>
          </View>
        </ScrollView>
      </View>
    </Modal>
  )
}
export default PayCollateralDisclaimer
