import React, {Component} from 'react'
import {View, TouchableOpacity, ScrollView} from 'react-native'
import {connect} from 'react-redux'

import {TextBold, Card, TextReg, Button, BackgroundHeader} from '../../../components'

//import DatePicker from 'react-native-date-picker'
import CalendarPicker from 'react-native-calendar-picker'
import moment from 'moment'

import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'

class BankTransferDate extends Component {
  constructor(props) {
    super(props)
    this.state = {
      optionSelect: null,
      selectedDate: null,
    }
  }

  selectOption = selection => {
    if (this.state.optionSelect === selection) {
      this.setState({optionSelect: null})
    } else {
      this.setState({optionSelect: selection})
    }
  }

  next = () => {
    console.log('bank transfer date next', this.props.route.params.intent)
    this.props.navigation.navigate('BankTransferPick', {
      date: this.state.selectedDate,
      intent: this.props.route.params.intent,
      amount: this.props.route.params.amount,
    })
  }

  isItWeekend = date => {
    const day = moment(date).isoWeekday()
    return day === 6 || day === 7
  }

  onDateChange = selectedDate => {
    this.setState({selectedDate})
  }

  render() {
    const {ltv, thresholds, automatedPaymentType} = this.props.loanData

    const nextPaymentAmount = this.props.loanData?.amortizationInfo?.nextPaymentAmount || null

    const nextPaymentDate = this.props.loanData?.amortizationInfo?.nextPaymentDate || null

    let todayX = new Date()
    let nextPaymentDate1 = this.props.loanData?.amortizationInfo?.nextPaymentDate || null

    let amSchedule = this.props.loanData?.amortizationInfo?.amortizationSchedule || []

    let notPaid = amSchedule.filter(a => a.principalPaid == '0')
    if (this.props.loanData?.interestOnly) {
      notPaid = amSchedule.filter(a => a.interestPaid == '0')
    }

    let earliestDate = notPaid.sort((a, b) => new Date(a.date) - new Date(b.date))[0]
    if (earliestDate && new Date(earliestDate?.date) < new Date(nextPaymentDate1)) {
      nextPaymentDate1 = earliestDate?.date
    }

    let nextPaymentDateObj = new Date(nextPaymentDate1)
    let xDaysAway = Math.ceil((nextPaymentDateObj - todayX) / (1000 * 60 * 60 * 24))
    console.log('xDaysAway - date', xDaysAway)

    let liqAlarm = false
    if (xDaysAway < 0) {
      liqAlarm = true
    }

    let today = moment().startOf('day')
    // testing different dates

    //today = moment('2025-05-19').startOf('day')
    console.log('today', today.format('YYYY-MM-DD HH:mm:ss'))

    //console.log('nextPaymentDate', nextPaymentDate, this.props.loanData?.amortizationInfo)

    let maxDate = moment(nextPaymentDate).subtract(1, 'days')
    let maxDateOriginal = maxDate

    //console.log('maxDateOriginal', maxDateOriginal.format('YYYY-MM-DD'))

    // testing different dates
    // Set max date to next 14th of the month
    let nextFourteenth =
      today.date() >= 14 ? today.clone().add(1, 'month').date(14) : today.clone().date(14)
    maxDate = nextFourteenth

    if (maxDateOriginal.month() > today.month()) {
      nextFourteenth = maxDateOriginal.clone().date(14)
      maxDate = nextFourteenth
    }

    let minDate = today

    // If today is the 14th, only allow selecting tomorrow
    if (today.date() === 14) {
      maxDate = today.clone().add(1, 'days')
      minDate = maxDate
    }

    if (this.props.route.params.intent == 'principal' && today.date() === 15) {
      maxDate = today.clone().subtract(1, 'days') // Set maxDate to yesterday so no dates are selectable
    } else if (liqAlarm) {
      if (today.date() === 15 || today.date() === 16 || today.date() === 17) {
        console.log('+1')
        maxDate = today.clone().add(1, 'days')
        minDate = maxDate
      }
      if (today.date() === 18) {
        console.log('-1')
        maxDate = today.clone().subtract(100, 'days')
        minDate = maxDate
      }
    } else {
      if (today.date() === 15) {
        console.log('15 +1')
        maxDate = today.clone().add(1, 'days')
        minDate = maxDate
      }
    }

    console.log('minDate', minDate.format('YYYY-MM-DD HH:mm:ss'))
    console.log('maxDate', maxDate.format('YYYY-MM-DD HH:mm:ss'))

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Bank Transfer'} goBack={this.props.navigation.goBack} />
        <ScrollView style={{alignSelf: 'stretch'}} contentContainerStyle={{alignItems: 'center'}}>
          <TextReg
            style={{
              fontSize: 17,
              marginTop: 40,
              marginBottom: 30,
              width: 260,
              textAlign: 'center',
            }}>
            Which day do you want your payment to be made?
          </TextReg>
          <View>
            <CalendarPicker
              width={340}
              selectedDayColor={'#00FFBD'}
              selectedDayTextColor={'#000'}
              minDate={minDate}
              maxDate={maxDate}
              onDateChange={this.onDateChange}
              dayShape={'square'}
              todayBackgroundColor={'#555'}
              textStyle={{color: '#FFF'}}
              disabledDatesTextStyle={{color: '#777'}}
            />
          </View>
          {this.state.selectedDate ? (
            <View style={{height: 26, marginTop: 20}}>
              <TextReg style={{fontSize: 16}}>
                {moment(this.state.selectedDate).format('MMMM Do, YYYY')}
              </TextReg>
            </View>
          ) : (
            <View style={{height: 26, marginTop: 20}} />
          )}
          <Button
            style={{marginTop: 10, marginBottom: 20, width: 300}}
            disabled={!this.state.selectedDate}
            onPress={() => this.next()}>
            Next
          </Button>
        </ScrollView>
      </View>
    )
  }
}

BankTransferDate.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  accountRef: state.auth.account.ref,
  loanData: state.user.loanData,
  user: state.user.user,
  showPinScreen: state.auth.pinScreen,
  refreshCount: state.user.refreshCount,
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(BankTransferDate)
