import React from 'react'
import {View, Text, StyleSheet, TouchableOpacity, Image} from 'react-native'
import {Card, TextReg, TextBold, BackgroundHeader} from '../../../components'
import commonStyles from '../../../styles/commonStyles'
import {useNavigation} from '@react-navigation/native'
import {useSelector} from 'react-redux'
import {increaseRefreshDataCount} from '../../../store/user/user.actions'
import {useDispatch} from 'react-redux'
const CancelPayment = ({route}) => {
  const navigation = useNavigation()
  const dispatch = useDispatch()
  const {payment} = route.params
  let WebService = useSelector(state => state.auth.WebService || {})

  let sendCancel = async () => {
    try {
      console.log('sendCancel', payment)
      //DELETE /scheduledPayments/:scheduledPaymentId

      let res = await WebService.cancelPayment(payment.id)
      dispatch(increaseRefreshDataCount())
      console.log('cancelPayment - res', res)
      navigation.navigate('CancelPaymentSuccess')
    } catch (err) {
      console.error('cancelPayment - err:', err)
    }
  }

  return (
    <View style={commonStyles.tileContainer}>
      <BackgroundHeader title={' '} goBack={() => navigation.goBack()} />

      <View style={styles.box}>
        <View>
          <View style={styles.roundGrey}>
            <Image
              source={require('../../../imgs/graphics/cancelDark.png')}
              style={styles.cancelDark}
            />
          </View>

          <TextBold style={styles.title}>Cancel Scheduled Payment?</TextBold>

          <TextReg style={styles.description}>
            Are you sure you want to cancel this scheduled automatic payment? This action cannot be
            undone.
          </TextReg>

          <TextReg style={styles.description}>
            You will be responsible for making your monthly payments on time.
          </TextReg>
        </View>
        <View style={{marginBottom: 40}}>
          <TouchableOpacity onPress={() => sendCancel()}>
            <View style={styles.acceptButton}>
              <TextBold style={styles.acceptText}>YES, CANCEL</TextBold>
            </View>
          </TouchableOpacity>
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <TextBold style={styles.backText}>BACK</TextBold>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  box: {
    flex: 1,
    alignSelf: 'stretch',
    justifyContent: 'space-between',
  },
  row: {
    flexDirection: 'row',
    alignSelf: 'stretch',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: '#ccc',
    marginLeft: 6,
    marginRight: 6,
  },
  label: {
    fontSize: 16,
    color: '#FFF',
  },
  value: {
    fontSize: 16,
    color: '#FFF',
  },
  cancelText: {
    fontSize: 20,
    color: '#E5705A',
    marginTop: 10,
    marginBottom: 10,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  roundGrey: {
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: '#3D3D50',
    alignSelf: 'center',
    marginTop: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelDark: {
    width: 70,
    height: 70,
    alignSelf: 'center',
  },
  title: {
    fontSize: 20,
    color: '#FFF',
    textAlign: 'center',
    marginTop: 20,
  },
  description: {
    fontSize: 14,
    color: '#FFF',
    textAlign: 'center',
    marginTop: 10,
    marginLeft: 30,
    marginRight: 30,
  },
  acceptButton: {
    backgroundColor: '#E5705A',
    alignSelf: 'stretch',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    marginLeft: 20,
    marginRight: 20,
    borderRadius: 4,
  },
  acceptText: {
    fontSize: 18,
    color: '#FFF',
    textAlign: 'center',
    textTransform: 'uppercase',
  },
  backText: {
    fontSize: 18,
    color: '#00FFBD',
    textAlign: 'center',
    marginTop: 10,
    textTransform: 'uppercase',
  },
})

export default CancelPayment
