import React, {Component} from 'react'
import {View, TouchableOpacity, ScrollView, Image} from 'react-native'
import {connect} from 'react-redux'

import {TextBold, Card, TextReg, Button, BackgroundHeader} from '../../../components'

import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'

class StablecoinChoice extends Component {
  constructor(props) {
    super(props)
    this.state = {
      optionSelect: null,
      hasBounce: false,
    }
  }

  componentDidMount() {
    this.getMyLoan()
  }

  getMyLoan = () => {
    this.props.WebService.getLoans()
      .then(res => {
        if (res?.data?.length > 0) {
          let haBounce = res?.data[0]?.bounceFeeAmount
          haBounce = Number(haBounce || 0)
          this.setState({hasBounce: haBounce})
        }
      })
      .catch(err => {
        console.log('getMyLoan - err', err)
      })
  }

  selectOption = selection => {
    if (this.state.optionSelect === selection) {
      this.setState({optionSelect: null})
    } else {
      this.setState({optionSelect: selection})
    }
  }

  next = async () => {
    const forPayoff = this.props.route.params?.payoff || false

    if (forPayoff) {
      await this.props.WebService.payoffWithStablecoin(this.props.loanData.id, {
        currency: this.state.optionSelect,
      }).then(res => {
        console.log('res: ', res)
        this.props.navigation.navigate('StablecoinPayment', {
          payoff: true,
          address: res.data.payment.address,
          amount: res.data.payoffAmt,
          currency: res.data.payment.currency,
          intent: res.data.payment.intent,
          hasBounce: this.state.hasBounce,
        })
      })
    } else {
      this.props.navigation.navigate('StablecoinAmount', {
        currency: this.state.optionSelect,
        hasBounce: this.state.hasBounce,
      })
    }
  }

  render() {
    console.log('this.props.launchDarkly', this.props.launchDarkly)
    let usdcDisable = this.props.launchDarkly[`disable-usdc-deposit`] || false
    let tusdDisable = true //this.props.launchDarkly[`disable-tusd-deposit`] || false
    let usdpDisable = true //this.props.launchDarkly[`disable-usdp-deposit`] || false
    let usdtDisable = this.props.launchDarkly[`disable-usdt-deposit`] || false

    const {ltv, thresholds, automatedPaymentType} = this.props.loanData

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Stablecoin Selection'} goBack={this.props.navigation.goBack} />
        <ScrollView style={{alignSelf: 'stretch'}} contentContainerStyle={{alignItems: 'center'}}>
          <TextReg style={{marginTop: 24, marginBottom: 24, fontSize: 18}}>
            Select stablecoin:
          </TextReg>
          {!usdcDisable && (
            <TouchableOpacity onPress={() => this.selectOption('USDC')}>
              <Card
                cardWidth={300}
                cardMarginBottom={12}
                style={[
                  styles.oneTimeOptionCard,
                  {
                    borderColor: this.state.optionSelect === 'USDC' ? '#00FFBD' : '#FFF',
                  },
                ]}>
                <View style={styles.oneTimeOptionCardBox}>
                  <Image
                    source={require('../../../imgs/logos/main/usdc.png')}
                    style={styles.stablecoinImg}
                  />
                  <View style={{alignItems: 'center', marginTop: 6}}>
                    <TextBold style={styles.oneTimeOptionTitle}>USD Coin</TextBold>
                    <TextReg>(USDC)</TextReg>
                  </View>
                </View>
              </Card>
            </TouchableOpacity>
          )}
          {!tusdDisable && (
            <TouchableOpacity onPress={() => this.selectOption('TUSD')}>
              <Card
                cardWidth={300}
                cardMarginBottom={12}
                style={[
                  styles.oneTimeOptionCard,
                  {
                    borderColor: this.state.optionSelect === 'TUSD' ? '#00FFBD' : '#FFF',
                  },
                ]}>
                <View style={styles.oneTimeOptionCardBox}>
                  <Image
                    source={require('../../../imgs/logos/main/tusd.png')}
                    style={styles.stablecoinImg}
                  />
                  <View style={{alignItems: 'center', marginTop: 6}}>
                    <TextBold style={styles.oneTimeOptionTitle}>True USD</TextBold>
                    <TextReg>(TUSD)</TextReg>
                  </View>
                </View>
              </Card>
            </TouchableOpacity>
          )}
          {!usdpDisable && (
            <TouchableOpacity onPress={() => this.selectOption('USDP')}>
              <Card
                cardWidth={300}
                cardMarginBottom={12}
                style={[
                  styles.oneTimeOptionCard,
                  {
                    borderColor: this.state.optionSelect === 'USDP' ? '#00FFBD' : '#FFF',
                  },
                ]}>
                <View style={styles.oneTimeOptionCardBox}>
                  <Image
                    source={require('../../../imgs/logos/main/pax.png')}
                    style={styles.stablecoinImg}
                  />
                  <View style={{alignItems: 'center', marginTop: 6}}>
                    <TextBold style={styles.oneTimeOptionTitle}>Paxos</TextBold>
                    <TextReg>(USDP)</TextReg>
                  </View>
                </View>
              </Card>
            </TouchableOpacity>
          )}
          {!usdtDisable && (
            <TouchableOpacity onPress={() => this.selectOption('USDT')}>
              <Card
                cardWidth={300}
                cardMarginBottom={12}
                style={[
                  styles.oneTimeOptionCard,
                  {
                    borderColor: this.state.optionSelect === 'USDT' ? '#00FFBD' : '#FFF',
                  },
                ]}>
                <View style={styles.oneTimeOptionCardBox}>
                  <Image
                    source={require('../../../imgs/logos/main/usdt.png')}
                    style={styles.stablecoinImg}
                  />
                  <View style={{alignItems: 'center', marginTop: 6}}>
                    <TextBold style={styles.oneTimeOptionTitle}>Tether</TextBold>
                    <TextReg>(USDT)</TextReg>
                  </View>
                </View>
              </Card>
            </TouchableOpacity>
          )}
          <TextReg
            style={{
              color: '#E5705A',
              width: 300,
              marginTop: 10,
              marginBottom: 10,
              textAlign: 'center',
            }}>{`Note: SALT only supports stablecoins sent on the ETH Mainnet. Stablecoins sent on other networks will not be recognized and may be unrecoverable.`}</TextReg>
          <Button
            style={{marginTop: 16, marginBottom: 20, width: 300}}
            disabled={!this.state.optionSelect}
            onPress={() => this.next()}>
            Next
          </Button>
        </ScrollView>
      </View>
    )
  }
}

StablecoinChoice.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  //headerTintColor: '#FFF',
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    //shadowColor: 'transparent',
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  accountRef: state.auth.account.ref,
  loanData: state.user.loanData,
  user: state.user.user,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
  refreshCount: state.user.refreshCount,
  WebService: state.auth.WebService,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(StablecoinChoice)
