import React, {Component} from 'react'
import {View, TouchableOpacity, ScrollView, Image, Clipboard} from 'react-native'
import {connect} from 'react-redux'

import QRCode from 'react-native-qrcode-svg'

import {TextBold, TextReg, Button, BackgroundHeader} from '../../../components'

import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'
import {OneOf} from 'protobufjs'

class StablecoinPayment extends Component {
  constructor(props) {
    super(props)
    this.state = {
      optionSelect: null,
      showCopied: false,
    }
  }

  selectOption = selection => {
    if (this.state.optionSelect === selection) {
      this.setState({optionSelect: null})
    } else {
      this.setState({optionSelect: selection})
    }
  }

  next = () => {
    console.log('next')
  }

  copyAddress = address => {
    Clipboard.setString(address)
    this.setState({showCopied: true})
  }

  render() {
    const {ltv, thresholds, automatedPaymentType} = this.props.loanData

    const address = this.props.route.params.address
    let amount = this.props.route.params.amount || null
    let amountOriginal = amount
    const currency = this.props.route.params.currency
    const intent = this.props.route.params.intent
    const forPayoff = this.props.route.params?.payoff || false

    let intentTitle = 'Monthly Payment Amount'
    if (forPayoff) {
      intentTitle = 'Loan Payoff Amount'
      console.log('forPayoff', amount, forPayoff)
    } else if (intent == 'prepayment') {
      intentTitle = 'Prepayment'
    } else if (intent == 'principal') {
      intentTitle = 'Principal Payment'
    }

    if (this.props.route.params.hasBounce) {
      console.log('hasBounce', amount, this.props.route.params.hasBounce)
      amount = Number(amount) + Number(this.props.route.params.hasBounce)
      console.log('amount', amount)
    }

    let hasBounce = this.props.route.params.hasBounce

    if (hasBounce === 0 || hasBounce === '0' || hasBounce == 0) {
      hasBounce = false
    }

    let showAmount = (
      <>
        <TextBold style={{fontSize: 62, color: '#FFF', marginBottom: 0}}>{`$${amount}`}</TextBold>
        <TextBold style={{fontSize: 24, color: '#FFF'}}>{`${amount} ${currency}`}</TextBold>
      </>
    )

    if (forPayoff) {
      showAmount = (
        <>
          <TextBold
            style={{fontSize: 62, color: '#FFF', marginBottom: 0}}>{`$${amountOriginal}`}</TextBold>
          <TextBold
            style={{fontSize: 24, color: '#FFF'}}>{`${amountOriginal} ${currency}`}</TextBold>
        </>
      )
    }

    if (amount == 25 && hasBounce) {
      showAmount = (
        <>
          <TextBold
            style={{
              color: '#FFF',
              marginTop: 10,
              marginBottom: 10,
              width: 300,
              textAlign: 'center',
            }}>
            {`Please include additional $25 as bounce fee due to the failure of last ACH payment`}
          </TextBold>
        </>
      )
    }

    return (
      <View style={[commonStyles.tileContainer, {backgroundColor: '#28283D'}]}>
        <BackgroundHeader
          title={'Stablecoin Payment'}
          goBack={this.props.navigation.goBack}
          close
          navigation={this.props.navigation}
        />
        <ScrollView style={{alignSelf: 'stretch'}} contentContainerStyle={{alignItems: 'center'}}>
          <TextReg
            style={{
              fontSize: 18,
              color: '#FFF',
              marginTop: 16,
            }}>
            {intentTitle}
          </TextReg>

          {amount || forPayoff ? (
            <View style={{alignItems: 'center'}}>
              {showAmount}
              <TextReg
                style={{
                  fontSize: 17,
                  color: '#FFF',
                  width: 280,
                  textAlign: 'center',
                }}>
                Please send your <TextBold style={{color: '#FFF'}}>{`${currency}`}</TextBold> to the
                following address:
              </TextReg>
            </View>
          ) : (
            <View style={{alignItems: 'center', marginTop: 10}}>
              <TextReg
                style={{
                  fontSize: 17,
                  color: '#FFF',
                  width: 280,
                  textAlign: 'center',
                }}>
                Please send your <TextBold style={{color: '#FFF'}}>{`${currency}`}</TextBold> to the
                following address:
              </TextReg>
            </View>
          )}
          <View style={{height: 10, width: 300}} />
          <View style={styles.depositQrBox}>
            <QRCode value={address || ''} size={130} backgroundColor={'black'} color={'white'} />
          </View>

          <TextReg
            style={{
              color: '#FFF',
              marginTop: 10,
              fontSize: 16,
              marginBottom: 6,
            }}>
            Wallet Address:
          </TextReg>
          <TextBold
            style={{
              color: '#FFF',
              fontSize: 16,
              marginBottom: 6,
              width: 280,
              textAlign: 'center',
            }}>
            {address || ''}
          </TextBold>
          {this.state.showCopied ? (
            <Button
              style={{
                marginTop: 16,
                marginBottom: 20,
                backgroundColor: '#00ffc3',
              }}
              onPress={() => this.next()}>
              <Image
                source={require('../../../imgs/blackCheckmark.png')}
                style={{height: 20, width: 26, marginTop: 8}}
              />
            </Button>
          ) : (
            <Button
              style={{
                marginTop: 16,
                marginBottom: 20,
                backgroundColor: '#fff',
              }}
              onPress={() => this.copyAddress(address)}>
              <TextBold style={{fontSize: 20, color: '#008487'}}>Copy Address</TextBold>
            </Button>
          )}

          {hasBounce && (
            <TextReg
              style={{
                color: '#E5705A',
                width: 300,
                textAlign: 'center',
                fontSize: 12,
                marginBottom: 10,
              }}>
              {`This payment includes a bounce fee of $${hasBounce}`}
            </TextReg>
          )}

          <TextReg
            style={{
              color: '#E5705A',
              width: 300,
              textAlign: 'center',
              fontSize: 12,
              marginBottom: 8,
            }}>
            {`Please Note: This is an address on the Ethereum mainnet.  Sending anything other than ${currency} to this address may result in a loss of funds. ${currency} sent on other networks will not be recognized and may be unrecoverable.`}
          </TextReg>

          <TextReg
            style={{
              color: '#E5705A',
              width: 300,
              textAlign: 'center',
              fontSize: 12,
            }}>
            Warning: This address was generated for this specific payment. Once completed, do not
            send future payments to this address. Any additional payments sent to this address will
            result in a delay in accounting and recognition in our system.
          </TextReg>
          <View style={{width: 30, height: 50}} />
        </ScrollView>
      </View>
    )
  }
}

StablecoinPayment.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  //headerTintColor: '#FFF',
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    //shadowColor: 'transparent',
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  accountRef: state.auth.account.ref,
  loanData: state.user.loanData,
  user: state.user.user,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
  refreshCount: state.user.refreshCount,
  WebService: state.auth.WebService,
})

export default connect(mapStateToProps)(StablecoinPayment)
