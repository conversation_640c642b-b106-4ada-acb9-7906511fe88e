import React, {Component} from 'react'
import {View, TouchableOpacity, ScrollView} from 'react-native'
import {connect} from 'react-redux'

import {TextBold, Card, TextReg, Button, BackgroundHeader} from '../../../components'

import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'

class StablecoinAmount extends Component {
  constructor(props) {
    super(props)
    this.state = {
      optionSelect: null,
      loading: false,
      amount: null,
      error: false,
    }
  }

  selectOption = (selection, nextPaymentAmount = null) => {
    if (this.state.optionSelect === selection) {
      this.setState({optionSelect: null})
    } else {
      this.setState({optionSelect: selection, amount: nextPaymentAmount})
    }
  }

  next = () => {
    const data = {
      intent: this.state.optionSelect,
      currency: this.props.route.params.currency,
    }
    this.setState({loading: true})
    this.props.WebService.stablecoinPayment(this.props.loanData.id, data)
      .then(res => {
        console.log('stableCoin Payment', res, {
          address: res.data.address,
          amount: this.state.amount,
          currency: this.props.route.params.currency,
          intent: this.state.optionSelect,
          hasBounce: this.props.route.params.hasBounce,
        })
        this.setState({loading: false}, () => {
          this.props.navigation.navigate('StablecoinPayment', {
            address: res.data.address,
            amount: this.state.amount,
            currency: this.props.route.params.currency,
            intent: this.state.optionSelect,
            hasBounce: this.props.route.params.hasBounce,
          })
        })
      })
      .catch(err => {
        console.log('stablecoin payment', err)
        this.setState({loading: false, error: true})
      })
    //this.props.navigation.navigate('StablecoinPayment')
  }

  render() {
    const {ltv, thresholds, automatedPaymentType} = this.props.loanData

    let nextPaymentAmount = this.props.loanData?.amortizationInfo?.nextPaymentAmount || null

    let currentlyDisabled = this.props.launchDarkly['disable-deposit'] || false

    let pasDueInfo = this.props.loanData?.amortizationInfo?.pastDueInstallmentInfo || null
    if (pasDueInfo) {
      nextPaymentAmount = Number(pasDueInfo?.interestDue) || 0
      nextPaymentAmount += Number(pasDueInfo?.principalDue) || 0
      if (this.props.loanData?.bounceFeeAmount) {
        nextPaymentAmount += Number(this.props.loanData?.bounceFeeAmount) || 0
      }
      nextPaymentAmount += 1 // processing fee
    }

    let showMonthly = true
    if (nextPaymentAmount == '0.00' || nextPaymentAmount == '0') {
      showMonthly = false
    }

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Stablecoin Payment'} goBack={this.props.navigation.goBack} />
        <ScrollView style={{alignSelf: 'stretch'}} contentContainerStyle={{alignItems: 'center'}}>
          <TextReg style={{marginTop: 24, marginBottom: 24, fontSize: 18}}>
            Choose stablecoin payment:
          </TextReg>
          {showMonthly && (
            <TouchableOpacity onPress={() => this.selectOption('payment', nextPaymentAmount)}>
              <Card
                cardWidth={300}
                cardMarginBottom={12}
                style={[
                  styles.oneTimeOptionCard,
                  {
                    height: 120,
                    borderColor: this.state.optionSelect === 'payment' ? '#00FFBD' : '#FFF',
                  },
                ]}>
                <View style={styles.stablecoinBox}>
                  <TextBold
                    style={
                      styles.stablecoinTitle
                    }>{`Monthly Payment ($${nextPaymentAmount})`}</TextBold>
                  <TextReg style={styles.stablecoinText}>
                    Any amount over your monthly payment amount will be automatically applied as a
                    principal payment.
                  </TextReg>
                </View>
              </Card>
            </TouchableOpacity>
          )}

          <TouchableOpacity onPress={() => this.selectOption('principal')}>
            <Card
              cardWidth={300}
              cardMarginBottom={12}
              style={[
                styles.oneTimeOptionCard,
                {
                  height: 140,
                  borderColor: this.state.optionSelect === 'principal' ? '#00FFBD' : '#FFF',
                },
              ]}>
              <View style={styles.stablecoinBox}>
                <TextBold style={styles.stablecoinTitle}>Principal Payment</TextBold>
                <TextReg style={styles.stablecoinText}>
                  Any funds you send with this payment type will be applied to your principal
                  balance. If you have not made your monthly payment, that will still be due.
                </TextReg>
              </View>
            </Card>
          </TouchableOpacity>
          <Button
            style={{marginTop: 16, marginBottom: 20, width: 300}}
            disabled={!this.state.optionSelect || currentlyDisabled}
            onPress={() => this.next()}
            isLoading={this.state.loading}>
            Next
          </Button>

          {currentlyDisabled && (
            <TextReg style={{color: 'red', marginTop: -10}}>Payment address disabled</TextReg>
          )}
        </ScrollView>
      </View>
    )
  }
}

StablecoinAmount.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  //headerTintColor: '#FFF',
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    //shadowColor: 'transparent',
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  accountRef: state.auth.account.ref,
  loanData: state.user.loanData,
  user: state.user.user,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
  refreshCount: state.user.refreshCount,
  WebService: state.auth.WebService,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(StablecoinAmount)
