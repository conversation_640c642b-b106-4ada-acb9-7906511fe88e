import React, {Component} from 'react'
import {View, TouchableOpacity, ScrollView, Image} from 'react-native'
import {connect} from 'react-redux'

import {Card, TextReg, Button, BackgroundHeader} from '../../../components'
import {updateBanks} from '../../../store/user/user.actions'

import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'

class BankTransferPick extends Component {
  constructor(props) {
    super(props)
    this.state = {
      refreshing: false,
      selectedBank: null,
      hasBounce: 0,
    }
  }

  componentDidMount() {
    this.getBanks()
    this.getMyLoan()
  }

  getMyLoan = () => {
    this.props.WebService.getLoans()
      .then(res => {
        if (res?.data?.length > 0) {
          let haBounce = res?.data[0]?.bounceFeeAmount
          haBounce = Number(haBounce || 0)
          this.setState({hasBounce: haBounce})
        }
      })
      .catch(err => {
        console.log('getMyLoan - err', err)
      })
  }

  getBanks = () => {
    this.setState({refreshing: true})
    this.props.WebService.getBank(this.props.loanData.id)
      .then(res => {
        this.props.dispatch(updateBanks(res.data))
        this.setState({
          refreshing: false,
          selectedBank: null,
        })
      })
      .catch(err => {
        console.log('get banks', err)
        this.setState({refreshing: false})
      })
  }

  pickBank = (bankId, name, accountNumber, isPlaid, bank) => {
    console.log('pickBank', bank, this.props.user)
    let fundingSourceId = bank?.dwolla?.fundingSource?.id || null
    if (this.state.selectedBank == bankId) {
      this.setState({
        selectedBank: null,
        bankName: null,
        bankAccountNumber: null,
        isPlaid: isPlaid,
        fundingSourceId,
      })
    } else {
      this.setState({
        selectedBank: bankId,
        bankName: name,
        bankAccountNumber: accountNumber,
        isPlaid: isPlaid,
        fundingSourceId,
      })
    }
  }

  next = () => {
    this.props.navigation.navigate('BankTransferConfirm', {
      date: this.props.route.params.date,
      intent: this.props.route.params.intent,
      bankId: this.state.selectedBank,
      bankName: this.state.bankName,
      bankAccountNumber: this.state.bankAccountNumber,
      amount: this.props.route.params.amount,
      isPlaid: this.state.isPlaid,
      hasBounce: this.state.hasBounce,
      fundingSourceId: this.state.fundingSourceId,
    })
  }

  goToBanks = () => {
    this.props.navigation.navigate('Banking', {flow: 'bankTransfer'})
  }

  render() {
    const showBanks = this.props.banksArr.map((a, k) => {
      if (!a.isPlaid) {
        return null
      }

      let badDwolla = false
      if (a?.dwolla?.exchange?.isReAuthRequired) {
        badDwolla = true
      }
      if (a?.dwolla?.fundingSource?.isRemoved) {
        badDwolla = true
      }
      if (a?.dwolla?.fundingSource?.status != 'verified') {
        badDwolla = true
      }
      if (badDwolla) {
        return null
      }

      let showText = `${a.name} - *******${a.accountNumber}`
      if (a.isPlaid) {
        showText = `${a.name}`
      }
      return (
        <TouchableOpacity
          key={k}
          onPress={() => this.pickBank(a.id, a.name, a.accountNumber, a.isPlaid, a)}>
          <Card
            cardWidth={300}
            style={{
              borderWidth: 2,
              borderColor: this.state.selectedBank === a.id ? '#00FFBD' : '#FFF',
            }}>
            <TextReg style={{alignSelf: 'flex-start', paddingLeft: 8}}>{showText}</TextReg>
          </Card>
        </TouchableOpacity>
      )
    })

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'Bank Transfer'} goBack={this.props.navigation.goBack} />

        <ScrollView style={{alignSelf: 'stretch'}} contentContainerStyle={{alignItems: 'center'}}>
          <TextReg
            style={{
              fontSize: 17,
              marginTop: 40,
              marginBottom: 30,
              width: 260,
              textAlign: 'center',
            }}>
            Choose the bank account you would like to pay with:
          </TextReg>
          {this.state.refreshing && (
            <Image source={require('../../../imgs/loadingDots.gif')} style={styles.loadingDots} />
          )}

          {showBanks}
          <TouchableOpacity onPress={() => this.goToBanks()}>
            <Card cardWidth={300}>
              <TextReg style={{color: '#00FFBD'}}>Add New Bank Account</TextReg>
            </Card>
          </TouchableOpacity>

          <Button
            style={{marginTop: 10, marginBottom: 20, width: 300}}
            disabled={!this.state.selectedBank}
            onPress={() => this.next()}>
            Next
          </Button>
        </ScrollView>
      </View>
    )
  }
}

BankTransferPick.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  //headerTintColor: '#FFF',
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    //shadowColor: 'transparent',
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  accountRef: state.auth.account.ref,
  loanData: state.user.loanData,
  user: state.user.user,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
  refreshCount: state.user.refreshCount,
  WebService: state.auth.WebService,
  banksArr: state.user.banks,
})

export default connect(mapStateToProps)(BankTransferPick)
