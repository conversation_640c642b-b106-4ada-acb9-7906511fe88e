import React, {Component} from 'react'
import {View, Image, TouchableOpacity} from 'react-native'
import {connect} from 'react-redux'

import {TextBold, Card, TextReg, Button, BackgroundHeader} from '../../../components'

import commonStyles from '../../../styles/commonStyles'
import styles from '../styles'

class OneTimeOptions extends Component {
  constructor(props) {
    super(props)
    this.state = {
      optionSelect: null,
      loading: false,
    }
  }

  selectOption = selection => {
    if (this.state.optionSelect === selection) {
      this.setState({optionSelect: null})
    } else {
      this.setState({optionSelect: selection})
    }
  }

  next = () => {
    if (this.state.optionSelect == 'Wire') {
      this.setState({loading: true})
      this.props.WebService.getLender(this.props.loanData.lenderId)
        .then(res => {
          console.log('get lender res', res)
          this.setState({loading: false}, () => {
            this.props.navigation.navigate('WireTransfer', {lender: res.data})
          })
        })
        .catch(err => {
          this.setState({loading: false})

          console.log('get lender err', err)
        })
    }
    switch (this.state.optionSelect) {
      case 'Stablecoin':
        this.props.navigation.navigate('StablecoinChoice')
        break
      case 'Bank':
        this.props.navigation.navigate('BankTransfer')
        break
    }
  }

  render() {
    const {ltv, thresholds, automatedPaymentType} = this.props.loanData
    let ldAchOff = this.props.launchDarkly['disable-ach'] || false
    let noTransfer = this.props.launchDarkly['disable-wire-transfer'] || false

    return (
      <View style={commonStyles.tileContainer}>
        <BackgroundHeader title={'One Time Payment'} goBack={this.props.navigation.goBack} />
        <TextReg style={{marginTop: 24, marginBottom: 24, fontSize: 18}}>How would you like to pay?</TextReg>
        {!ldAchOff && (
          <TouchableOpacity onPress={() => this.selectOption('Bank')}>
            <Card
              cardWidth={300}
              cardMarginBottom={12}
              style={[
                styles.oneTimeOptionCard,
                {
                  borderColor: this.state.optionSelect === 'Bank' ? '#00FFBD' : '#FFF',
                },
              ]}>
              <View style={styles.oneTimeOptionCardBox}>
                <Image source={require('../../../imgs/graphics/bank.png')} style={styles.oneTimeOptionImg} />
                <TextBold style={styles.oneTimeOptionTitle}>Bank Transfer</TextBold>
              </View>
            </Card>
          </TouchableOpacity>
        )}
        <TouchableOpacity onPress={() => this.selectOption('Stablecoin')}>
          <Card
            cardWidth={300}
            cardMarginBottom={12}
            style={[
              styles.oneTimeOptionCard,
              {
                borderColor: this.state.optionSelect === 'Stablecoin' ? '#00FFBD' : '#FFF',
              },
            ]}>
            <View style={styles.oneTimeOptionCardBox}>
              <Image source={require('../../../imgs/loanHealth/healthyLoan.png')} style={styles.oneTimeOptionImg} />
              <TextBold style={styles.oneTimeOptionTitle}>Stablecoin</TextBold>
            </View>
          </Card>
        </TouchableOpacity>
        {!noTransfer && (
          <TouchableOpacity onPress={() => this.selectOption('Wire')}>
            <Card
              cardWidth={300}
              cardMarginBottom={12}
              style={[
                styles.oneTimeOptionCard,
                {
                  borderColor: this.state.optionSelect === 'Wire' ? '#00FFBD' : '#FFF',
                },
              ]}>
              <View style={styles.oneTimeOptionCardBox}>
                <Image source={require('../../../imgs/graphics/globe.png')} style={styles.oneTimeOptionImg} />
                <TextBold style={styles.oneTimeOptionTitle}>Wire Transfer</TextBold>
              </View>
            </Card>
          </TouchableOpacity>
        )}
        <Button
          style={{marginTop: 16, width: 300}}
          disabled={!this.state.optionSelect}
          onPress={() => this.next()}
          isLoading={this.state.loading}>
          Next
        </Button>
      </View>
    )
  }
}

OneTimeOptions.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
  //headerTintColor: '#FFF',
  headerTitleStyle: {
    flex: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Europa-Regular',
    fontWeight: '200',
    fontSize: 19,
  },
  headerStyle: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    //shadowColor: 'transparent',
    paddingBottom: 6,
    height: 240,
  },
  headerBackTitle: null,
})

const mapStateToProps = state => ({
  accountRef: state.auth.account.ref,
  loanData: state.user.loanData,
  user: state.user.user,
  tokenPrices: state.user.prices,
  showPinScreen: state.auth.pinScreen,
  refreshCount: state.user.refreshCount,
  WebService: state.auth.WebService,
  launchDarkly: state.launchDarkly,
})

export default connect(mapStateToProps)(OneTimeOptions)
