import React from 'react';
import {View, Image, TouchableOpacity} from 'react-native';

import {TextBold, TextReg, Card, Button} from '../../components';

import styles from './styles';

const StackWise = props => (
  <Card marginTop={10} cardMarginBottom={0}>
    <View
      style={{
        alignSelf: 'stretch',
        marginBottom: 12,
        flexDirection: 'row',
        justifyContent: 'space-between',
      }}>
      <Image
        source={require('../../imgs/stackWise/logoWhite.png')}
        style={{
          height: 48,
          width: 104,
          marginLeft: 10,
          marginTop: 8,
          opacity: 0.82,
        }}
      />
      <View style={{alignItems: 'flex-end', marginRight: 10}}>
        <TextBold style={{fontSize: 28, marginTop: 8}}>${props.totalRewards}</TextBold>
        <TextReg style={{marginTop: -6}}>Total Rewards</TextReg>
      </View>
    </View>
    <Button style={{alignSelf: 'stretch', marginTop: 10}} onPress={() => props.gotoStackwiseRewards()}>
      <TextBold
        style={{
          fontSize: 17,
          color: '#000',
          letterSpacing: 1,
        }}>
        VIEW
      </TextBold>
    </Button>
  </Card>
);

export default StackWise;
