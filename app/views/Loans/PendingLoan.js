import React from 'react'
import { Image } from 'react-native'

import { TextReg, TextBold, Card, Button } from '../../components'

const LoadingLoan = props => (
  <Card marginTop={14}>
    <Image
      source={require('../../imgs/graphics/loanPending.png')}
      style={{ width: 100, height: 106, marginTop: 50, marginBottom: 40 }}
    />
    <TextBold
      style={{ color: '#000', fontSize: 22, marginBottom: 18 }}
    >{`Loan request pending`}</TextBold>
    <TextReg
      style={{ width: 270, fontSize: 15, marginBottom: 40 }}
    >{`Before you can be approved, make sure all necessary profile information is complete and sufficient collateral has been deposited on the online portal.`}</TextReg>
    <Button
      onPress={props.openNewLoanLink}
      style={{ marginBottom: 36 }}
    >{`Check Status`}</Button>
  </Card>
)

export default LoadingLoan
