import React, {useState, useEffect, useRef} from 'react'
import {View, TouchableOpacity, Image, Clipboard} from 'react-native'
import {useSelector, useDispatch} from 'react-redux'
import LottieView from 'lottie-react-native'
import QRCode from 'react-native-qrcode-svg'

import {TextBold, TextReg, Card} from '../../components'
import {numberWithCommas, listPossibleWallets} from '../../util/helpers'
import {getTokenPic} from '../../util/tokens'
import {updateLoansDrill, updateAllWallets} from '../../store/user/user.actions'
import {showToast} from '../../store/notifications/notifications.actions'

import styles from './styles'

const DepositDrop = ({showPriceCure, showPriceCall, isStabilized}) => {
  let dispatch = useDispatch()
  let [open, setOpen] = useState(false)
  let [rows, setRows] = useState([])
  let loanData = useSelector(state => state.user.loanData || {})
  let user = useSelector(state => state.user.user)
  let tokenPrices = useSelector(state => state.user.prices)
  let accountRef = useSelector(state => state.auth.account.ref)
  let WebService = useSelector(state => state.auth.WebService)
  let [side, setSide] = useState('left')
  let launchDarkly = useSelector(state => state.launchDarkly)

  console.log('depositDrop LD', launchDarkly)

  let lottieRef = useRef()
  let lottieRef2 = useRef()

  useEffect(() => {
    tokens()
  }, [])

  let banned = {
    BTC: launchDarkly['disable-btc-deposit'] || false,
    ETH: launchDarkly['disable-eth-deposit'] || false,
    BCH: launchDarkly['disable-bch-deposit'] || false,
    LTC: launchDarkly['disable-ltc-deposit'] || false,
    USDC: launchDarkly['disable-usdc-deposit'] || false,
    USDT: launchDarkly['disable-usdt-deposit'] || false,
    USDP: launchDarkly['disable-usdp-deposit'] || false,
    SALT: launchDarkly['disable-salt-deposit'] || false, //default on = false banned
    TUSD: launchDarkly['disable-tusd-deposit'] || false,
    XRP: launchDarkly['disable-xrp-deposit'] || true,
    DASH: launchDarkly['disable-dash-deposit'] || true,
    DOGE: launchDarkly['disable-doge-deposit'] || true,
    PAXG: launchDarkly['disable-paxg-deposit'] || true,
  }

  console.log(' depositDrop banned', banned)

  let tokens = () => {
    let possibleWallets = listPossibleWallets()
    let collaterals = possibleWallets.map((a, k) => {
      let collateralRow = loanData?.collaterals.filter(b => b.currency == a)[0] || []
      if (collateralRow.length > 0) {
        return collateralRow
      } else {
        return {
          currency: a,
          creatingWallet: false,
          height: 46,
          open: false,
          showCopied: false,
          refreshing: false,
        }
      }
    })
    collaterals = collaterals.filter(a => !banned[a.currency])
    let rowsInit = collaterals.map(a => ({
      currency: a.currency,
      height: 46,
      open: false,
      showCopied: false,
      creatingWallet: false,
    }))
    const order = ['USDC', 'USDT', 'USDP', 'TUSD', 'BTC']
    rowsInit = rowsInit.sort((a, b) => {
      // Get the order index, if the currency is not found, set the index to a large number to ensure it is sorted at the end
      const indexA = order.indexOf(a.currency) !== -1 ? order.indexOf(a.currency) : 1000
      const indexB = order.indexOf(b.currency) !== -1 ? order.indexOf(b.currency) : 1000

      // Compare the indices to determine the order
      if (indexA < indexB) {
        return -1
      }
      if (indexA > indexB) {
        return 1
      }

      // If both have the same index, keep their relative order (stable sort)
      return 0
    })
    setRows(rowsInit)
  }

  let expandRow = async (a, open) => {
    if (!user.mfaEnabled) {
      dispatch(showToast(true))
      return
    }
    const walletRes = await checkForWallet(a)

    let rowsNew = rows.map(b => {
      if (!open && b.currency == a.currency) {
        return {...b, height: walletRes == 'idVerify' ? 200 : 390, open: true}
      }
      if (open && b.currency == a.currency) {
        return {...b, height: 46, open: false}
      }

      return {...b, height: 46, open: false}
    })
    setRows(rowsNew)
  }

  let loadingCreateWallet = (a, trueFalse) => {
    let rowsNew = rows
    rowsNew = rowsNew.map(b => {
      if (b.currency == a.currency) {
        return {...b, creatingWallet: trueFalse}
      }
      return {...b}
    })
    setRows(rowsNew)
  }

  let checkForWallet = a =>
    new Promise((resolve, reject) => {
      const hasWallet = loanData.collaterals.filter(b => b.currency === a.currency)[0]
      const {amount, verification} = loanData
      let verified = false
      if (
        verification?.photoIdVerificationStatus == 'passed' ||
        verification?.photoIdVerificationStatus == 'completed_prior'
      ) {
        verified = true
      }
      if (Number(amount) <= 25000 && verification?.identityVerificationStatus == 'passed') {
        verified = true
      }
      //if doesnt have a wallet
      if (!hasWallet?.address || !verified) {
        // create the wallet

        // if user has not finished signup
        if (verified) {
          loadingCreateWallet(a, true)
          WebService.createWallet(a.currency)
            .then(res => {
              let {collaterals} = loanData
              collaterals.push(res.data)
              loanData = {
                ...loanData,
                collaterals: collaterals,
              }
              dispatch(updateLoansDrill(loanData))
              //this.props.dispatch(updateWallets(res.data));

              loadingCreateWallet(a, false)
              getWalletData()
              resolve()
            })
            .catch(err => {
              //this.setState({creatingWallet: false})
              reject()
            })
        } else {
          //show Needs to verify ID
          //this.setState({verifiedError: true})
          resolve('idVerify')
        }
      } else {
        resolve()
      }
    })

  let getWalletData = async () => {
    //this.setState({refreshing: true})
    let accountArr = user?.accounts || []
    let walletsRes = []

    await Promise.all(
      accountArr.map(async a => {
        await WebService.getWallets(a.ref).then(res => {
          //walletsRes.push(res.data);
          walletsRes[a.ref - 1] = res.data
          return true
        })
      }),
    )
    dispatch(updateAllWallets(walletsRes))
    //this.setState({refreshing: false})
  }

  let copyAddress = (a, address) => {
    const copiedRows = rows.map(b => {
      if (b.currency == a.currency) {
        return {...b, showCopied: true}
      }
      return b
    })
    Clipboard.setString(address)

    setRows(copiedRows)
    setTimeout(() => {
      let unCopied = rows.map(b => {
        return {...b, showCopied: false}
      })
      setRows(unCopied)
    }, 1400)
  }

  let withCommas = numberWithCommas(showPriceCure)
  let withCommas2 = numberWithCommas(showPriceCall)

  let showRows = rows.map((a, k) => {
    const showImg = getTokenPic(a.currency)
    let thisData = loanData?.collaterals?.filter(b => b.currency == a.currency)[0]
    let priceName = `${a.currency}-USD`
    let price = tokenPrices[priceName]?.price || 0
    let address = thisData?.address || '0x'
    //let price = thisData?.price || 0
    let amountLeft = (showPriceCure / price).toFixed(2)
    if (side == 'right') {
      amountLeft = (showPriceCall / price).toFixed(2)
    }
    amountLeft = numberWithCommas(amountLeft)

    return (
      <View key={k}>
        {a.currency == 'BTC' && (
          <TextReg style={{marginBottom: 20}}>{`Volatile Collateral`}</TextReg>
        )}
        <View
          marginTop={-4}
          style={{
            backgroundColor: '#48485A',
            borderRadius: 16,
            minHeight: 60,
            marginBottom: 14,
            padding: 8,
          }}>
          <View style={{alignSelf: 'stretch', height: rows[k].height}}>
            <View
              style={{
                alignSelf: 'stretch',
                flexDirection: 'row',
                paddingLeft: 8,
                paddingRight: 8,
                alignItems: 'center',
                justifyContent: 'space-between',
                height: 46,
              }}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Image style={{height: 30, width: 30}} source={showImg} />
                <TextReg
                  style={{
                    fontSize: 20,
                    width: 60,
                    marginLeft: 16,
                    marginRight: 4,
                    color: '#FFF',
                  }}>
                  {a.currency}
                </TextReg>
                <TextBold style={{fontSize: 20, color: side == 'left' ? '#34E89E' : '#E5705A'}}>
                  {amountLeft}
                </TextBold>
              </View>
              <TouchableOpacity
                disabled={rows[k].creatingWallet}
                onPress={() => expandRow(a, rows[k].open)}>
                {rows[k].open ? (
                  <View style={{height: 30, width: 34}}>
                    <Image
                      style={{
                        height: 30,
                        width: 30,
                        opacity: 0.5,
                      }}
                      source={require('../../imgs/closeX.png')}
                    />
                  </View>
                ) : (
                  <View>
                    {rows[k].creatingWallet ? (
                      <LottieView
                        ref={lottieRef}
                        style={{
                          width: 44,
                          height: 44,
                          marginLeft: 20,
                          opacity: 0.9,
                          alignSelf: 'center',
                          zIndex: 5,
                        }}
                        source={require('../../imgs/lotti/loading-white-dots.json')}
                        autoPlay
                      />
                    ) : (
                      <Image
                        style={{height: 40, width: 40}}
                        source={require('../../imgs/unit21/qrCodeSymbol.png')}
                      />
                    )}
                  </View>
                )}
              </TouchableOpacity>
            </View>
            {rows[k]?.open && (
              <View style={{marginTop: 14, alignItems: 'center'}}>
                <View
                  style={{
                    width: 260,
                    height: 1,
                    borderTopWidth: 1,
                    borderColor: '#CCC',
                    marginBottom: 6,
                  }}
                />
                <View
                  style={{
                    alignSelf: 'stretch',
                    paddingTop: 18,
                    alignItems: 'center',
                  }}>
                  <View
                    style={{
                      height: 160,
                      width: 160,
                      borderRadius: 14,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 10,
                    }}>
                    <View
                      style={{
                        overflow: 'hidden',
                        backgroundColor: '#48485A',
                        padding: 10,
                        borderRadius: 14,
                      }}>
                      {address ? (
                        <QRCode
                          value={a.address}
                          size={158}
                          backgroundColor={'#48485A'}
                          color={'#FFF'}
                        />
                      ) : (
                        <View
                          style={{
                            height: 158,
                            width: 158,
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}>
                          <LottieView
                            ref={lottieRef2}
                            style={{
                              width: 44,
                              height: 44,
                              opacity: 0.9,
                              alignSelf: 'center',
                              zIndex: 5,
                              marginLeft: 0,
                            }}
                            source={require('../../imgs/lotti/loading-white-dots.json')}
                            autoPlay
                          />
                        </View>
                      )}
                    </View>
                  </View>
                  <View
                    style={{
                      width: 300,
                      alignItems: 'center',
                    }}>
                    <TextReg
                      style={{
                        textAlign: 'center',
                        marginBottom: 16,
                        marginTop: 7,
                        width: 260,
                        color: '#FFF',
                      }}>
                      {`To deposit ${a.currency}, please send it to the following address:`}
                    </TextReg>
                    <View
                      style={{
                        borderRadius: 10,
                        backgroundColor: '#eef0f0',
                        width: 274,
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <TextBold
                        style={{
                          color: '#000',
                          width: 201,
                          marginLeft: 16,
                          marginRight: 16,
                          textAlign: 'center',
                          height: 40,
                          marginTop: 2,
                        }}>{`${address}`}</TextBold>
                      <View
                        style={{
                          height: 40,
                          width: 40,
                          borderRadius: 10,
                          backgroundColor: rows[k].showCopied ? '#00ffc1' : '#00FFBD',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}>
                        {rows[k].showCopied ? (
                          <Image
                            style={{height: 32, width: 32}}
                            source={require('../../imgs/checkmark.png')}
                          />
                        ) : (
                          <TouchableOpacity onPress={() => copyAddress(a, address)}>
                            <Image
                              style={{height: 26, width: 26}}
                              source={require('../../imgs/copyDepositButton.png')}
                            />
                          </TouchableOpacity>
                        )}
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            )}
          </View>
        </View>
      </View>
    )
  })

  return (
    <Card cardMarginBottom={0} marginTop={0}>
      {!open && (
        <TouchableOpacity
          onPress={() => setOpen(true)}
          style={{alignSelf: 'stretch'}}
          activeOpacity={1}>
          <View
            style={{
              flexDirection: 'row',
              alignSelf: 'stretch',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <View style={{alignItems: 'center', flexDirection: 'row'}}>
              <Image
                source={require('../../imgs/graphics/stack.png')}
                style={{height: 32, width: 36}}
              />
              <TextBold style={{fontSize: 18, marginLeft: 6}}>{`Deposit Collateral`}</TextBold>
            </View>
            <Image
              source={require('../../imgs/nav/arrowRightW.png')}
              style={{height: 20, width: 24}}
            />
          </View>
          <TextReg
            style={{
              padding: 10,
              paddingBottom: 6,
            }}>
            {`An alternative to making a payment on your principal. `}
            <TextBold>{`SALT recommends using stablecoins`}</TextBold>
            {` to deposit collateral over using more volatile collateral for this option.`}
          </TextReg>
        </TouchableOpacity>
      )}
      {open && (
        <View style={{alignSelf: 'stretch'}}>
          <View
            style={{
              flexDirection: 'row',
              alignSelf: 'stretch',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <View style={{alignItems: 'center', flexDirection: 'row'}}>
              <Image
                source={require('../../imgs/graphics/stack.png')}
                style={{height: 32, width: 36}}
              />
              <TextBold style={{fontSize: 18, marginLeft: 6}}>{`Deposit Collateral`}</TextBold>
            </View>
            <TouchableOpacity onPress={() => setOpen(false)} activeOpacity={1}>
              <Image
                source={require('../../imgs/nav/arrowDownW.png')}
                style={{height: 20, width: 24}}
              />
            </TouchableOpacity>
          </View>
          <TextReg
            style={{
              padding: 10,
              paddingBottom: 6,
            }}>
            {`An alternative to making a payment on your principal. `}
            <TextBold>{`SALT recommends using stablecoins`}</TextBold>
            {` to deposit collateral over using more volatile collateral for this option.`}
          </TextReg>
          <View style={{flexDirection: 'row'}}>
            <TouchableOpacity
              onPress={() => setSide('left')}
              style={{
                alignSelf: 'stretch',
                borderWidth: 2,
                borderColor: side == 'left' ? '#00FFBD' : '#28283D',
                marginTop: 10,
                borderRadius: 4,
                alignItems: 'center',
                flex: 1,
              }}>
              <TextBold
                style={{
                  color: '#00FFBD',
                  fontSize: 22,
                  marginTop: 30,
                }}>{`$${withCommas}`}</TextBold>
              <TextReg
                style={{
                  marginTop: 20,
                  fontSize: 14,
                  width: isStabilized ? 120 : 200,
                  textAlign: 'center',
                }}>{`Suggested Amount to Cure`}</TextReg>
              <TextReg style={{marginTop: 6, marginBottom: 30}}>{`LTV < 70%`}</TextReg>
            </TouchableOpacity>
            {isStabilized && (
              <TouchableOpacity
                onPress={() => setSide('right')}
                style={{
                  alignSelf: 'stretch',
                  borderWidth: 2,
                  borderColor: side == 'right' ? '#00FFBD' : '#28283D',
                  marginTop: 10,
                  borderRadius: 4,
                  alignItems: 'center',
                  marginLeft: 6,
                  flex: 1,
                }}>
                <TextBold
                  style={{
                    color: '#E5705A',
                    fontSize: 22,
                    marginTop: 30,
                  }}>{`$${withCommas2}`}</TextBold>
                <TextReg
                  style={{
                    marginTop: 20,
                    width: isStabilized ? 120 : 200,
                    textAlign: 'center',
                  }}>{`Minimum Amount to Convert`}</TextReg>
                <TextReg style={{marginTop: 6, marginBottom: 30}}>{`LTV < 83.3%`}</TextReg>
              </TouchableOpacity>
            )}
          </View>
          <TextReg style={{marginTop: 20, marginBottom: 20}}>{`Stablecoin collateral`}</TextReg>
          <View style={{alignSelf: 'stretch'}}>{showRows}</View>
        </View>
      )}
    </Card>
  )
}
export default DepositDrop
