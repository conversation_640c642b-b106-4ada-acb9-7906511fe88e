import {StyleSheet, Dimensions, PixelRatio} from 'react-native';

const {width, height} = Dimensions.get('window');

const styles = StyleSheet.create({
  accountListImg: {
    height: 34,
    width: 34,
  },
  accountListImgExchange: {
    width: 24,
    height: 24,
    marginLeft: 1,
  },
  accountListImgBox: {
    height: 48,
    width: 48,
    borderRadius: 14,
    backgroundColor: '#28283D',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 2,
  },
  accountListBox: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignSelf: 'stretch',
  },
  depositQrBox: {
    overflow: 'hidden',
    padding: 10,
    borderRadius: 14,
    alignSelf: 'stretch',
    alignItems: 'center',
  },
  whiteDotsAnimation: {
    width: 40,
    height: 40,
    opacity: 0.9,
    alignSelf: 'center',
    zIndex: 5,
    marginTop: -4,
    marginRight: -8,
  },
  whiteDotsAnimationLoading: {
    width: 50,
    height: 50,
    opacity: 0.9,
    alignSelf: 'center',
    zIndex: 5,
  },
});

export default styles;
