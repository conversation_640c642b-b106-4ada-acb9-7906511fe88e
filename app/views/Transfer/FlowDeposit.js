import React from 'react'
import {View, Dimensions, ScrollView, TouchableOpacity, Image} from 'react-native'
import QRCode from 'react-native-qrcode-svg'
import {Card, TextBold, TextReg} from '../../components'

import styles from './styles'

const FlowDeposit = props => {
  let {
    accountList,
    address,
    capitalize,
    copyAddress,
    currency,
    fullName,
    productType,
    showCopiedButton,
    step,
    wallets,
    walletsList,
    chosenAccName,
    launchDarkly,
  } = props
  console.log('deposit props', props)

  //check for ld flag - disable-xyz-deposit
  let ldDisableName = `disable-${currency.toLowerCase()}-deposit`
  let ldSingleDisabled = launchDarkly[ldDisableName] || false

  let showEthWarning = false
  if (['ETH', 'SALT', 'USDT', 'USDP', 'USDC'].includes(currency)) {
    showEthWarning = true
  }

  return (
    <View
      style={{
        alignSelf: 'stretch',
      }}>
      {step == 0 && (
        <ScrollView
          style={{
            paddingTop: 30,
            paddingLeft: 20,
            paddingRight: 20,
            postition: 'relative',
          }}>
          <TextReg style={{fontSize: 17, marginBottom: 14, marginLeft: 10}}>
            Where would you like to deposit funds?
          </TextReg>
          {accountList}

          <View style={{alignSelf: 'stretch', height: 240, widht: 300}} />
        </ScrollView>
      )}
      {step == 1 && (
        <ScrollView style={{paddingTop: 30, paddingLeft: 20, paddingRight: 20, marginBottom: 100}}>
          <TextReg style={{fontSize: 16, marginBottom: 14, marginLeft: 10}}>
            Select which asset you would like to deposit.
          </TextReg>
          {walletsList.length > 0 && walletsList}
          <View style={{alignSelf: 'stretch', height: 240, widht: 300}} />
        </ScrollView>
      )}
      {step == 2 && (
        <View
          style={{
            paddingTop: 30,
            paddingLeft: 20,
            paddingRight: 20,
          }}>
          {ldSingleDisabled ? (
            <TextReg
              style={{
                color: '#fff',
                marginTop: 30,
                fontSize: 17,
                textAlign: 'center',
                width: 340,
              }}>{`Deposits for ${currency} are currently unavailable. <NAME_EMAIL> with any questions.`}</TextReg>
          ) : (
            <>
              <TextReg
                style={{
                  fontSize: 16,
                  marginBottom: 14,
                  textAlign: 'center',
                }}>{`Deposit ${currency} to my ${chosenAccName}`}</TextReg>

              <View style={styles.depositQrBox}>
                <QRCode
                  value={address || ''}
                  size={160}
                  color={'#FFF'}
                  backgroundColor={'#3D3D50'}
                />
              </View>

              <View
                style={{
                  alignSelf: 'stretch',
                  alignItems: 'center',
                  marginTop: 14,
                }}>
                <View
                  style={{
                    borderRadius: 14,
                    width: 318,
                    flexDirection: 'row',
                    alignItems: 'center',
                    borderColor: '#EEEFF3',
                    borderWidth: 1,
                    height: 54,
                  }}>
                  <TextReg
                    style={{
                      color: '#FFF',
                      width: 241,
                      marginLeft: 16,
                      marginRight: 16,
                      textAlign: 'center',
                      height: 40,
                      marginTop: 2,
                    }}>{`${address}`}</TextReg>
                  <View
                    style={{
                      height: 54,
                      width: 46,
                      borderTopRightRadius: 14,
                      borderBottomRightRadius: 14,
                      backgroundColor: showCopiedButton ? '#00ffc1' : '#00FFBD',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    {showCopiedButton ? (
                      <Image
                        style={{height: 38, width: 38}}
                        source={require('../../imgs/checkmark.png')}
                      />
                    ) : (
                      <TouchableOpacity onPress={() => copyAddress()}>
                        <Image
                          style={{height: 24, width: 24}}
                          source={require('../../imgs/copyDepositButton.png')}
                        />
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
              </View>
              {showEthWarning ? (
                <TextReg
                  style={{
                    marginTop: 10,
                    fontSize: 13,
                    marginLeft: 10,
                    marginRight: 20,
                    color: '#E5705A',
                  }}>
                  {`Please note: This is an ETH Mainnet address, sending anything other than ${currency} to this address may result in a loss of funds. ${currency} sent on other networks will not be recognized and may be unrecoverable.`}
                </TextReg>
              ) : (
                <TextReg
                  style={{
                    marginTop: 10,
                    fontSize: 13,
                    marginLeft: 10,
                    marginRight: 20,
                    color: '#E5705A',
                  }}>
                  {`Please note: Sending anything other than ${currency} to this address may result in a loss of funds.`}
                </TextReg>
              )}
            </>
          )}
        </View>
      )}
    </View>
  )
}

export default FlowDeposit
