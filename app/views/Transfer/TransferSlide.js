import React, {Component} from 'react'
import {
  View,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  Animated,
  Clipboard,
  TextInput,
  Keyboard,
  Platform,
} from 'react-native'
import {connect} from 'react-redux'
import LinearGradient from 'react-native-linear-gradient'
import {BigNumber} from 'bignumber.js'
import <PERSON>tie<PERSON>iew from 'lottie-react-native'
import DeviceInfo from 'react-native-device-info'

import qRcodeScanner from '../../imgs/qRCodeScanner.png'
import WithdrawQRScanner from '../../views/Collateral/Withdraw/WithdrawQRScanner'

import {Card, TextBold, TextReg, Button, DisabledModal} from '../../components'
import {
  numberWithCommas,
  listPossibleWallets,
  collateralMarketCapSort,
  dig,
  validateTokenAddress,
} from '../../util/helpers'
import {getTokenPic} from '../../util/tokens'
import {cryptoNameMap} from '../../util/enumerables'
import {
  updateAllWallets,
  increaseRefreshDataCount,
  walletAction,
} from '../../store/user/user.actions'
import {askingForPermissions} from '../../store/auth/auth.actions'
import {showToast} from '../../store/notifications/notifications.actions'

const AnimatedTouchable = Animated.createAnimatedComponent(TouchableOpacity)
const {width: ScreenWidth, height: ScreenHeight} = Dimensions.get('window')
import FlowDeposit from './FlowDeposit'
import FlowTransfer from './FlowTransfer'

import noLoanImg from '../../imgs/graphics/noLoanImg.png'
import custodyAccount from '../../imgs/graphics/custodyAccount.png'

import agreedYes from '../../imgs/agreedYes.png'
import agreedNo from '../../imgs/agreedNo.png'
import styles from './styles'

class TransferSlide extends Component {
  constructor(props) {
    super(props)
    this.initState = {
      slideHidden: true,
      flow: 'menu',
      step: 0,
      wallets: [],
      address: '',
      currency: '',
      productType: '',
      fullName: '',
      showCopiedButton: false,
      toggleWithdrawal: 'USD',
      withdrawAddress: '',
      withdrawAddressInput: '',
      agreeYes: false,
      amountInput: 0,
      fiatInput: 0,
      withdrawButtonActive: false,
      price: 0,
      loanID: 0,
      transferAcc1: '',
      transferAcc2: '',
      transferSumValue1: '',
      transferSumValue2: '',
      transferName1: '',
      transferName2: '',
      transferID: '',
      transferPayloadAccount: '',
      chosenRef: 0,
      chosenAccName: '',
      createLoading: false,
      transferErr: false,
      fromWalletAcc: null,
      keyboardIsOpen: false,
      isValidAddress: true,
      oltv: 'max',
      projectedBalance: null,
      twoFactor: '',
      loanStatus: 'none',
      isStabilized: false,
      scannerModalVisable: false,
      inputHeight: 54,
      hasPendingWithdrawal: false,
      belowMinimum: false,
      hasAch: false,
      promptVisible: false,
      promptType: 'withdrawal',
      overMax: false,
    }
    this.state = this.initState
    this.menuHeight = 600
    this.slideValue = new Animated.Value(this.menuHeight)
    this.backgroundDarkness = new Animated.Value(0)
    this.transferOpacity = new Animated.Value(1)
    this.closeOpacity = new Animated.Value(0)
  }

  componentDidMount() {
    if (Platform.OS != 'ios') {
      this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this._keyboardDidShow)
      this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide)
    }
  }
  componentWillUnmount() {
    if (Platform.OS != 'ios') {
      this.keyboardDidShowListener.remove()
      this.keyboardDidHideListener.remove()
    }
  }

  componentDidUpdate = async prevProps => {
    if (
      prevProps.externalSlideFn != this.props.externalSlideFn &&
      this.props.externalSlideFn?.action
    ) {
      let currency = this.props.externalSlideFn?.currency
      //let price = this.props.externalSlideFn?.price
      let address = this.props.externalSlideFn?.address
      let chosenRef = this.props.externalSlideFn?.chosenRef
      let transferWalletFrom = this.props.externalSlideFn?.transferWalletFrom
      let loanID = this.props.externalSlideFn?.loanID
      let projectedBalance = this.props.externalSlideFn?.projectedBalance
      let loanStatus = this.props.externalSlideFn?.loanStatus

      let fullName = cryptoNameMap.get(currency?.toLowerCase() || '') || ''
      let withdrawName =
        this.props.externalSlideFn?.accountName === 'Custody Account'
          ? 'WALLET Account'
          : this.props.externalSlideFn?.accountName || ''

      //get account
      let allAccounts = this.props.user?.accounts
      allAccounts = allAccounts?.map(a => {
        let walletRef = a.ref - 1
        let thisAccWallet =
          (this.props.user?.allWallets && this.props.user?.allWallets[walletRef]) || []
        return {
          ...a,
          wallets: thisAccWallet,
        }
      })

      let account = allAccounts.filter(a => a.ref == chosenRef)[0]
      let wallets1 = account.wallets

      let price = wallets1?.filter(a => a.currency == currency)[0]?.price

      if (this.props.externalSlideFn?.action == 'deposit') {
        this.setState(
          {
            flow: 'deposit',
            step: 1,
            wallets: account.wallets,
            productType: account.productType,
            chosenRef: account.ref,
            chosenAccName: account.name,
          },
          () => {
            this.toggleSlide(1)
          },
        )
        return
      }

      //

      this.setState(
        {
          flow: 'walletMenuWithdrawal',
          currency,
          fullName,
          address,
          withdrawAddress: `${currency} Address`,
          price,
          chosenRef,
          transferWalletFrom,
          loanID,
          projectedBalance,
          withdrawName,
          wallets1,
          wallets: wallets1,
          productType: account.productType,
        },
        () => {
          this.toggleSlide()
        },
      )

      let oltv = 'max'
      if (loanID != 0 && loanStatus == 'active') {
        oltv = await this.props.WebService.getWithdrawalOLTVMax(loanID, currency)
        oltv = oltv?.data || 'max'
        if (account.productType == 'exchange') {
          oltv = 'max'
        }
        this.setState({oltv})
      } else {
        this.setState({oltv: 'max'})
      }
    }
  }

  _keyboardDidShow = () => {
    this.setState({keyboardIsOpen: true})
  }

  _keyboardDidHide = () => {
    this.setState({keyboardIsOpen: false})
  }

  toggleSlide = (menuNum = 0) => {
    if (!this.props?.user?.mfaEnabled) {
      this.props.dispatch(showToast(true))
      return
    }

    let {slideHidden} = this.state
    this.setState({slideHidden: !slideHidden}, () => {
      let newSlide = 0 //0 is 0 px downwards = showing
      if (menuNum == 0) {
        newSlide = 200
      }
      if (menuNum == 1) {
        newSlide = 100
      }
      let newDarkness = 0.7 //dark background = showing
      let newTransferOpacity = 0
      let newCloseOpacity = 1
      if (!slideHidden) {
        newSlide = this.menuHeight
        newDarkness = 0
        newTransferOpacity = 1
        newCloseOpacity = 0
      }
      Animated.parallel([
        Animated.timing(this.slideValue, {
          toValue: newSlide,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(this.backgroundDarkness, {
          toValue: newDarkness,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.timing(this.transferOpacity, {
          toValue: newTransferOpacity,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(this.closeOpacity, {
          toValue: newCloseOpacity,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start(() => {
        if (!slideHidden) {
          this.setState(this.initState)
        }
      })
    })
  }

  continue = type => {
    if (type == 'transfer') {
      ///
      let allAccounts = this.props.user?.accounts
      allAccounts = allAccounts?.map(a => {
        let walletRef = a.ref - 1
        let thisAccWallet =
          (this.props.user?.allWallets && this.props.user?.allWallets[walletRef]) || []
        return {
          ...a,
          wallets: thisAccWallet,
        }
      })

      let {chosenRef} = this.state
      let account = allAccounts.filter(a => a.ref == chosenRef)[0]

      this.checkAch(account)
      let {tokenPrices24h} = this.props
      let sumValue = 0
      let sum24Value = 0

      account.wallets.map(b => {
        let {currency} = b
        let titleUSD = `${currency}-USD`
        let price24h = tokenPrices24h[titleUSD]?.price
        let value24h = price24h * Number(b.projectedBalance)
        sum24Value += value24h
        sumValue += Number(b.value)
      })

      this.setState({
        flow: 'transfer',
        step: 0,
        transferAcc1: this.capitalize(account.productType),
        transferName1: account.name,
        transferID: account.id,
        transferSumValue1: numberWithCommas(sumValue.toFixed(2)),
        productType: account.productType,
      })
      Animated.timing(this.slideValue, {
        toValue: 40,
        duration: 300,
        useNativeDriver: true,
      }).start()
    }

    if (type == 'withdrawal') {
      let {currency, chosenRef} = this.state
      this.checkPending(currency, chosenRef)
      this.setState({
        flow: 'withdrawal',
        step: 2,
        withdrawAddress: `${currency} Address`,
      })
      Animated.timing(this.slideValue, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start()
    }
  }

  start = flow => {
    if (flow == 'transfer') {
      this.setState({flow: 'transfer', step: 0})
      Animated.timing(this.slideValue, {
        toValue: 100,
        duration: 300,
        useNativeDriver: true,
      }).start()
    }
    if (flow == 'deposit') {
      this.setState({flow: 'deposit', step: 0})
      Animated.timing(this.slideValue, {
        toValue: 100,
        duration: 300,
        useNativeDriver: true,
      }).start()
    }
    if (flow == 'withdrawal') {
      this.setState({flow: 'withdrawal', step: 0})
      Animated.timing(this.slideValue, {
        toValue: 100,
        duration: 300,
        useNativeDriver: true,
      }).start()
    }
  }

  chooseAccount = (account, sumValue = 0) => {
    if (this.state.flow == 'deposit') {
      this.setState({
        step: 1,
        wallets: account.wallets,
        productType: account.productType,
        chosenRef: account.ref,
        chosenAccName: account.name,
      })
    }
    if (this.state.flow == 'withdrawal') {
      let loanID = account?.loans[0]?.id || 0
      let loanStatus = account?.loans[0]?.status || 'none'
      let isStabilized = account?.product?.loan?.isStabilized || false
      this.setState({
        step: 1,
        wallets: account.wallets,
        productType: account.productType,
        withdrawAcc: this.capitalize(account.productType),
        withdrawName: account.name === 'Custody Account' ? 'WALLET Account' : account.name,
        loanID,
        loanStatus,
        isStabilized,
        chosenRef: account.ref,
      })
    }
    if (this.state.flow == 'transfer') {
      this.checkAch(account)
      let nextStep = 2
      if (this.state.step == 3) {
        nextStep = 0
        this.setState({
          step: nextStep,
          wallets: account.wallets,
          transferAcc2: this.capitalize(account.productType),
          transferName2: account.name,
          transferSumValue2: sumValue,
          transferID: account.id,
          transferPayloadAccount: account.id,
          //productType: account.productType,
        })
      } else {
        let loanID = account?.loans[0]?.id || 0
        let loanStatus = account?.loans[0]?.status || 'none'
        let isStabilized = account?.product?.loan?.isStabilized || false

        this.setState({
          step: nextStep,
          transferAcc1: this.capitalize(account.productType),
          transferName1: account.name,
          transferSumValue1: sumValue,
          transferID: account.id,
          wallets: account.wallets,
          wallets1: account.wallets,
          chosenRef: account.ref,
          loanID,
          loanStatus,
          isStabilized,
          //productType: account.productType,
        })
      }
    }
  }

  capitalize = string => {
    return string.charAt(0).toUpperCase() + string.slice(1)
  }

  copyAddress = () => {
    Clipboard.setString(this.state.address)
    this.setState({showCopiedButton: true}, () => {
      setTimeout(() => {
        this.setState({showCopiedButton: false})
      }, 1400)
    })
  }

  createAndUpdate = currency => {
    this.setState({createLoading: currency})
    return this.props.WebService.createWallet(currency, this.state.chosenRef)
      .then(res => {
        return res.data.address
      })
      .catch(err => {
        this.setState({createLoading: false})
        console.log('create wallet err', err)
      })
  }

  toggleScannerModal = () => {
    this.props.dispatch(askingForPermissions(!this.state.scannerModalVisable))
    this.setState({scannerModalVisable: !this.state.scannerModalVisable})
  }

  refreshWallets = async () => {
    let accountArr = this.props.user?.accounts || []
    let walletsRes = []

    await Promise.all(
      accountArr.map(async a => {
        await this.props.WebService.getWallets(a.ref).then(res => {
          //walletsRes.push(res.data);
          walletsRes[a.ref - 1] = res.data
          return true
        })
      }),
    )
    this.props.dispatch(updateAllWallets(walletsRes))
    this.setState({createLoading: false})
    return true
  }

  checkPending = (currency, chosenRef) => {
    this.props.WebService.getTransactionHistory(currency, chosenRef).then(res => {
      let pendingTxArr = res.data?.transactions?.filter(b => !b.status)
      if (pendingTxArr.length > 0) {
        this.setState({hasPendingWithdrawal: true})
      }
    })
  }

  checkAch = account => {
    if (account?.productType == 'loan') {
      let loanId = account?.loans[0]?.id
      this.props.WebService.checkAch(loanId)
        .then(res => {
          let hasAch = false
          if (res.data) {
            hasAch = true
          }
          this.setState({hasAch})
        })
        .catch(err => {
          console.log('checkAch err', err)
        })
    }
  }

  clickWallet = async (address, currency, fullName, price, id, possible = false, a = null) => {
    if (possible) {
      address = await this.createAndUpdate(currency)
      this.refreshWallets()
    }
    if (this.state.flow == 'deposit') {
      this.setState({
        step: 2,
        address: address,
        currency: currency,
        fullName,
      })
    }
    if (this.state.flow == 'withdrawal') {
      this.checkPending(currency, this.state.chosenRef)
      this.setState({
        step: 2,
        address: address,
        currency: currency,
        fullName,
        withdrawAddress: `${currency} Address`,
        price,
        projectedBalance: a.projectedBalance,
      })

      Animated.timing(this.slideValue, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start()

      if (this.state.loanID != 0 && this.state.loanStatus == 'active') {
        let oltv = await this.props.WebService.getWithdrawalOLTVMax(this.state.loanID, currency)
        oltv = oltv?.data || 'max'
        this.setState({oltv})
      } else {
        this.setState({oltv: 'max'})
      }
    }
    if (this.state.flow == 'transfer') {
      this.setState({
        step: 0,
        address: address,
        currency: currency,
        fullName,
        transferWalletFrom: id,
        price,
        projectedBalance: a.projectedBalance,
      })

      let {amountInput, fiatInput = 0} = this.state
      if (amountInput) {
        this.state.toggleWithdrawal == 'USD'
          ? this.setState({
              amountInput:
                Number(fiatInput) > 0
                  ? new BigNumber(fiatInput).dividedBy(price).toFormat(8)
                  : '0.0000000',
            })
          : this.setState({
              fiatInput:
                amountInput > 0
                  ? new BigNumber(amountInput).multipliedBy(price).toFormat(2)
                  : '0.00',
            })
      }

      Animated.timing(this.slideValue, {
        toValue: 10,
        duration: 300,
        useNativeDriver: true,
      }).start()

      if (this.state.loanID != 0 && this.state.loanStatus == 'active') {
        let oltv = await this.props.WebService.getWithdrawalOLTVMax(this.state.loanID, currency)
        oltv = oltv?.data || 'max'
        this.setState({oltv})
      } else {
        this.setState({oltv: 'max'})
      }
    }
  }

  toggleWithdrawal = toggleWithdrawal => {
    this.setState({toggleWithdrawal})
  }

  handleAddressInput = text => {
    let isValidAddress = validateTokenAddress(this.state.currency, text)
    this.setState({withdrawAddressInput: text, isValidAddress})
  }

  updateAddressFromQR = text => {
    let isValidAddress = validateTokenAddress(this.state.currency, text)
    this.setState({withdrawAddressInput: text, isValidAddress})
  }

  handleTwoFactor = text => {
    this.setState({twoFactor: text})
  }

  toggleAgreeWithdrawal = () => {
    this.setState({agreeYes: !this.state.agreeYes})
  }

  handleAmountInput = input => {
    input = input.toString()
    input = input.replace('$', '')
    input = input.replace('-', '')
    input = input.replace(/[, ]+/g, '')

    if (input.charAt(0) == '0' && input.charAt(1) != '.') {
      input = input.substring(1)
    }

    let price = this.state.price
    //price = price.replace('$', '');
    let test1 = new BigNumber(input).dividedBy(price).toFormat(8)
    this.state.toggleWithdrawal == 'USD'
      ? this.setState(
          {
            amountInput:
              price != 0 && Number(input) > 0
                ? new BigNumber(input).dividedBy(price).toFormat(8)
                : '0.0000000',
            fiatInput: input,
            withdrawButtonActive: Number(input) > 0,
          },
          () => this.checkMinimum(),
        )
      : this.setState(
          {
            amountInput: input,
            fiatInput:
              price != 0 && input > 0
                ? new BigNumber(input).multipliedBy(price).toFormat(2)
                : '0.00',
            withdrawButtonActive: Number(input) > 0,
          },
          () => this.checkMinimum(),
        )
  }

  checkMinimum = () => {
    let {fiatInput, projectedBalance, price} = this.state
    fiatInput = fiatInput.split(',').join('')

    if (Number(fiatInput) < 15) {
      this.setState({belowMinimum: true})
    } else {
      this.setState({belowMinimum: false})
    }

    let balanceXPrice = BigNumber(projectedBalance).multipliedBy(price).toFormat(2)
    balanceXPrice = balanceXPrice.split(',').join('')

    //also check max
    if (Number(fiatInput) > Number(balanceXPrice)) {
      this.setState({overMax: true})
    } else {
      this.setState({overMax: false})
    }
  }

  transferAccountPick = num => {
    if (num == 1) {
      this.setState({step: 1})
    }
    if (num == 2) {
      this.setState({step: 3})
    }
  }

  transferReview = () => {
    this.setState({step: 4})
    Animated.timing(this.slideValue, {
      toValue: 200,
      duration: 300,
      useNativeDriver: true,
    }).start()
  }

  withdrawalReview = () => {
    if (!this.state.isValidAddress) return
    this.setState({step: 3})
    Animated.timing(this.slideValue, {
      toValue: 140,
      duration: 300,
      useNativeDriver: true,
    }).start()
  }

  transferSend = () => {
    this.setState({trasnferLoading: true, transferErr: false})

    let inputNoComma = '0'
    if (this.state.amountInput != 0) {
      inputNoComma = this.state.amountInput?.split(',').join('')
    }

    let payload = {
      fromWalletId: this.state.transferWalletFrom,
      toAccountId: this.state.transferPayloadAccount,
      amount: inputNoComma,
    }
    this.props.WebService.transferSend(payload, this.state.chosenRef)
      .then(res => {
        this.setState({step: 5, trasnferLoading: false})
        this.props.dispatch(increaseRefreshDataCount())
      })
      .catch(err => {
        this.setState({trasnferLoading: false, transferErr: 'Transfer Error'})
        console.log('transferSend err', err)
      })
  }

  withdrawSend = async () => {
    this.setState({trasnferLoading: true, transferErr: false})
    let {currency} = this.state

    if (this.state.loanID != 0 && this.state.loanStatus == 'active') {
      let oltv = await this.props.WebService.getWithdrawalOLTVMax(this.state.loanID, currency)
      oltv = oltv?.data || 'max'
      let numberInput = Number(this.state.amountInput)
      if (oltv != 'max' && oltv < numberInput) {
        this.setState({trasnferLoading: false, transferErr: false, oltv})
        return
      }
    }

    let {twoFactor, chosenRef} = this.state

    let inputNoComma = '0'
    if (this.state.amountInput != 0) {
      inputNoComma = this.state.amountInput?.split(',').join('')
    }

    const withdrawData = {
      amount: inputNoComma,
      currency: currency,
      toAddress: this.state.withdrawAddressInput,
      twoFactorCode: twoFactor,
    }

    this.props.WebService.withdraw(withdrawData, chosenRef)
      .then(res => {
        this.setState({step: 4, trasnferLoading: false})
        this.props.dispatch(increaseRefreshDataCount())
      })
      .catch(err => {
        this.setState({trasnferLoading: false, transferErr: 'Withdraw Error'})
        console.log('withdrawSend err', err)
      })
  }

  showPrompt = promptType => {
    this.setState({promptVisible: true, promptType})
  }

  getStepDescription = a => {
    if (!a.loans) {
      return <TextReg style={{color: '#CCC'}}>-</TextReg>
    }
    let loan = a.loans[0] || []
    let applicationStep = <TextReg style={{color: '#CCC'}}>-</TextReg>
    if (a?.productType == 'loan') {
      applicationStep = <TextReg style={{color: '#CCC'}}>Request a loan</TextReg>
    }
    if (a?.productType == 'exchange') {
      applicationStep = <TextReg style={{color: '#CCC'}}>Deposit Funds</TextReg>
    }
    if (loan?.status && loan?.status != 'active') {
      const hasLoanBankingInfo =
        dig(loan, 'depositBankAccount', 'documents', 'length') > 0 ||
        dig(loan, 'depositBankAccount', 'verifiedAt') ||
        (dig(loan, 'depositBankAccount', 'upholdId') &&
          !dig(loan, 'depositBankAccount', 'document', 'rejectedAt'))
      if (!this.props.personalProfileComplete) {
        applicationStep = <TextReg style={{color: '#CCC'}}>Complete your profile</TextReg>
      } else if (a.type == 'business' && !a.businessProfile.isComplete) {
        applicationStep = <TextReg style={{color: '#CCC'}}>Complete your business profile</TextReg>
      } else if (!this.props.idVerificationStatus) {
        applicationStep = <TextReg style={{color: '#CCC'}}>Complete ID verification</TextReg>
      } else if (!hasLoanBankingInfo) {
        applicationStep = <TextReg style={{color: '#CCC'}}>Choosing a payout option</TextReg>
      } else {
        applicationStep = <TextReg style={{color: '#CCC'}}>Awaiting review</TextReg>
        this.isCollatUnderTotal(a, loan)
        if (!this.state.collatIsEnough[a.accountRef]) {
          applicationStep = <TextReg style={{color: '#CCC'}}>Deposit collateral</TextReg>
        }
      }
    }
    if (loan?.status == 'active') {
      let ltv = (Number(loan.ltv) * 100).toFixed(2)

      if (loan?.thresholds) {
        const warning = Number(loan?.thresholds.warning) * 100
        const marginCall = Number(loan?.thresholds.marginCall) * 100
        const liquidation = Number(loan?.thresholds.liquidation) * 100
        if (ltv > warning) {
          //
        }
        if (ltv > marginCall) {
          //
        }
        if (ltv > liquidation) {
          //
        }
        if (a.isStabilized || loan?.awaitingLiquidation) {
          //
        }
      }

      applicationStep = (
        <View style={styles.allAccountsActive}>
          <View style={{flexDirection: 'row', alignItems: 'flex-end'}}>
            <TextReg
              style={{
                fontSize: 16,
                paddingTop: 2,
              }}>{`LTV ${ltv}%`}</TextReg>
          </View>
        </View>
      )
    }

    return applicationStep
  }

  withdrawMax = async () => {
    let {price, projectedBalance, loanID, amountInput, currency, productType, transferID, flow} =
      this.state
    if (!price) return

    let oltv = {}
    try {
      oltv = await this.props.WebService.getWithdrawalOLTVMax(loanID, currency)
    } catch (err) {
      //console.log('getWithdrawalOLTVMax err', err)
    }
    oltv = oltv?.data || 'max'

    if (productType == 'exchange') {
      oltv = 'max'
    }

    amountInput = oltv || amountInput

    let oltvFiatPrice = BigNumber(oltv).multipliedBy(price).toFormat(2)
    let oltvFiatPricePrime = oltvFiatPrice
    let balanceXPrice = BigNumber(projectedBalance).multipliedBy(price).toFormat(2)

    if (Number(amountInput) > Number(projectedBalance) || amountInput == 'max') {
      amountInput = Number(projectedBalance)
    }

    if (Number(oltvFiatPrice.replace(',', '')) > Number(balanceXPrice.replace(',', ''))) {
      oltvFiatPrice = balanceXPrice
    }

    //let amountInputFloor = Number(amountInput).toFixed(8).toString();

    let amountInputFloor = amountInput.toString()
    amountInputFloor = amountInputFloor.slice(0, amountInputFloor.indexOf('.') + 8)

    if (oltv == 'max') {
      this.setState({
        amountInput: projectedBalance,
        fiatInput: price != 0 && projectedBalance > 0 ? balanceXPrice : '0.00',
        withdrawButtonActive: Number(projectedBalance) > 0,
        overMax: false,
      })
      return
    }

    //check for max when salt <= 20%
    if (productType != 'exchange' && currency != 'SALT' && oltv > 0) {
      let allValue = 0
      let saltValue = 0
      let thisValue = 0

      let calcWallets = this.state.wallets
      if (flow == 'transfer') {
        calcWallets = this.state.wallets1
      }

      calcWallets?.map(a => {
        if (a.currency == 'SALT') {
          saltValue = a.value
        }
        if (a.currency == currency) {
          thisValue = Number(a.value) || 0
        }
        allValue += Number(a.value) || 0
      })

      let oltvMath = Number(oltvFiatPricePrime?.replace(/,/g, ''))

      let per80 = (allValue - oltvMath) * 0.8
      per80 = per80.toFixed(2)

      let max80 = thisValue - per80

      if (max80 > 0) {
        oltvFiatPrice = Number(max80).toFixed(2)
        amountInputFloor = oltvFiatPrice / price
        amountInputFloor = amountInputFloor.toString()
        amountInputFloor = amountInputFloor.slice(0, amountInputFloor.indexOf('.') + 8)
        //format
        oltvFiatPrice = numberWithCommas(oltvFiatPrice)
      }
      if (amountInputFloor > oltv) {
        amountInputFloor = oltv.toString()
        amountInputFloor = amountInputFloor.slice(0, amountInputFloor.indexOf('.') + 8)
        oltvFiatPrice = numberWithCommas((price * amountInputFloor).toFixed(2))
        console.log('eh', amountInputFloor, oltvFiatPrice, oltv)
      }
    }

    if (oltv < 0) {
      this.setState({amountInput: 0, fiatInput: 0})
    } else {
      this.setState({
        amountInput: amountInputFloor,
        fiatInput: price != 0 && oltv > 0 ? oltvFiatPrice : '0.00',
        withdrawButtonActive: Number(oltv) > 0,
        overMax: false,
      })
    }
  }

  //
  render() {
    let launchDarkly = this.props.launchDarkly

    let banned = {
      BTC: launchDarkly['disable-btc-deposit'] || false,
      ETH: launchDarkly['disable-eth-deposit'] || false,
      BCH: launchDarkly['disable-bch-deposit'] || false,
      LTC: launchDarkly['disable-ltc-deposit'] || false,
      USDC: launchDarkly['disable-usdc-deposit'] || false,
      USDT: launchDarkly['disable-usdt-deposit'] || false,
      USDP: launchDarkly['disable-usdp-deposit'] || false,
      SALT: launchDarkly['disable-salt-deposit'] || false,
      TUSD: launchDarkly['disable-tusd-deposit'] || false,
      XRP: launchDarkly['disable-xrp-deposit'] || true,
      DASH: launchDarkly['disable-dash-deposit'] || true,
      DOGE: launchDarkly['disable-doge-deposit'] || true,
      PAXG: launchDarkly['disable-paxg-deposit'] || true,
    }

    let allAccounts = this.props.user?.accounts
    let {tokenPrices24h} = this.props
    let lowerSlideButton = Platform.OS != 'ios'
    const model = DeviceInfo.getModel() || ''
    if (
      model.includes('iPhone 6') ||
      model.includes('iPhone SE') ||
      model == 'iPhone 8' ||
      model == 'iPhone 8 Plus'
    ) {
      lowerSlideButton = true
    }

    allAccounts = allAccounts?.map(a => {
      let walletRef = a.ref - 1
      let thisAccWallet =
        (this.props.user?.allWallets && this.props.user?.allWallets[walletRef]) || []
      return {
        ...a,
        wallets: thisAccWallet,
      }
    })

    //walletsList
    let walletsList = <View />
    walletsList = this.state.wallets.sort((a, b) => {
      if (Number(a.value) < Number(b.value)) {
        return 1
      }
      return -1
    })
    let possibleWallets = listPossibleWallets()
    walletsList.map(a => {
      possibleWallets = possibleWallets.filter(b => b != a.currency)
    })
    possibleWallets = possibleWallets.map(a => {
      return {
        currency: a,
        value: 0,
        address: '',
        price: 0,
        id: 0,
        possible: true,
      }
    })
    walletsList = walletsList.concat(possibleWallets)
    walletsList = walletsList.map(a => ({...a, title: a.currency}))

    let zeroValue = walletsList.filter(a => a.value == 0)
    let hasValue = walletsList.filter(a => a.value > 0)

    zeroValue = zeroValue.sort(collateralMarketCapSort)
    walletsList = hasValue.concat(zeroValue)

    if (
      (this.state.flow == 'transfer' && this.state.step == 2) ||
      this.state.flow == 'withdrawal'
    ) {
      walletsList = walletsList.filter(a => a.value > 0 && a.currency != 'PREF')
    }

    if (this.state.flow == 'deposit') {
      walletsList = walletsList.filter(a => a.currency != 'PREF')
      walletsList = walletsList.filter(a => a.currency != 'PAXG')
      walletsList = walletsList.filter(a => !banned[a.currency])
    }

    let paxGError = false

    //no paxg
    walletsList = walletsList.filter(a => a.currency != 'PAXG')

    if (this.state.flow == 'deposit' && this.state.productType == 'investment') {
      let assetType = allAccounts?.filter(a => a.name == this.state.chosenAccName)[0]
      assetType = assetType?.product?.investment?.investmentAsset
      if (assetType) {
        walletsList = walletsList?.filter(a => a.currency == assetType)
      }
    }

    walletsList = walletsList.map((a, k) => {
      let pic = getTokenPic(a.currency)
      let fullName = cryptoNameMap.get(a.currency.toLowerCase())
      let showValue = numberWithCommas(Number(a.value).toFixed(2))
      return (
        <TouchableOpacity
          key={k}
          onPress={() =>
            this.clickWallet(a.address, a.currency, fullName, a.price, a.id, a.possible, a)
          }
          disabled={this.state.createLoading == a.currency}>
          <Card
            style={{
              backgroundColor: '#28283D',
              //opacity: a.possible ? 0.7 : 1,
            }}
            cardMarginBottom={10}
            cardWidth={ScreenWidth - 40}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                alignSelf: 'stretch',
                marginLeft: 8,
                marginRight: 10,
              }}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Image
                  source={pic}
                  style={{
                    height: 34,
                    width: 34,
                    marginRight: 8,
                    opacity: 0.9,
                  }}
                />
                <View>
                  <TextReg style={{fontSize: 17, marginBottom: 4}}>{a.currency}</TextReg>
                  <TextReg style={{opacity: 0.6}}>{fullName}</TextReg>
                </View>
              </View>
              {this.state.createLoading == a.currency ? (
                <LottieView
                  ref={animation => {
                    this.animation = animation
                  }}
                  style={styles.whiteDotsAnimation}
                  source={require('../../imgs/lotti/loading-white-dots.json')}
                  autoPlay
                />
              ) : (
                <View>
                  <TextReg style={{fontSize: 16}}>{`$${showValue}`}</TextReg>
                </View>
              )}
            </View>
          </Card>
        </TouchableOpacity>
      )
    })

    //if investment to - get Asset type, only show after
    let investAsset = false
    if (this.state.flow == 'transfer' && this.state.transferName2) {
      let toAcc = allAccounts?.filter(a => a.name == this.state.transferName2)
      investAsset = toAcc[0]?.product?.investment?.investmentAsset
      console.log('investAsset', investAsset)
    }

    if (this.state.flow == 'transfer' && this.state.transferName1) {
      investAsset = this.state.currency
    }

    allAccounts = allAccounts?.sort((a, b) => {
      if (b?.account?.productType == 'exchange') {
        return 1
      }
      return -1
    })

    //accountsList
    if (this.state.transferID != '') {
      allAccounts = allAccounts.filter(a => a.id != this.state.transferID)
    }

    allAccounts = allAccounts?.sort((a, b) => {
      let aValue = a.loans?.length > 0 ? a.loans[0].collateralValue : 0
      let bValue = b.loans?.length > 0 ? b.loans[0].collateralValue : 0
      aValue = aValue || 0
      bValue = bValue || 0
      return bValue - aValue
    })

    let exchangeAcc = allAccounts?.filter(b => b?.productType == 'exchange') || []
    let nonExchangeAcc = allAccounts?.filter(b => b?.productType != 'exchange') || []

    allAccounts = exchangeAcc.concat(nonExchangeAcc)

    //transfer paxg only to custody
    if (this.state.flow == 'transfer' && this.state.step == 3 && this.state.currency == 'PAXG') {
      allAccounts = allAccounts.filter(a => a.name == 'Custody Account')
    }

    console.log('allAccounts', allAccounts, this.props.user?.accounts)

    let oneAccountPendingAch = false

    let accountList = allAccounts?.map((a, k) => {
      let accountType = a.productType
      accountType = accountType.charAt(0).toUpperCase() + accountType.slice(1)
      let showAccountName = a?.name
      if (accountType == 'Exchange') {
        showAccountType = 'WALLET Account'
        showAccountName = 'WALLET Account'
      }

      console.log('a', a)
      let hasPending = false
      if (a?.loans?.length > 0 && a?.loans[0]?.scheduledPayments?.length > 0) {
        hasPending = true
        oneAccountPendingAch = true
      }

      if (hasPending && (this.state.flow == 'transfer' || this.state.flow == 'withdrawal')) {
        return null
      }

      //only investment awaiting_collateral
      if (a.productType == 'investment') {
        if (a.product?.investment?.status != 'awaiting_collateral') return null
        if (this.state.flow == 'withdrawal') return null
        if (this.state.step == 1) return null
      }

      if (investAsset && this.state.transferName2) {
        let assetWallet = a?.wallets?.filter(b => b.currency == investAsset)[0]
        if (!assetWallet || assetWallet?.projectedBalance == '0') return null
      }

      if (a.productType == 'investment' && investAsset && this.state.transferName1) {
        if (a?.product?.investment?.investmentAsset != investAsset) return null
      }

      let sum24Value = 0
      let sumValue = 0
      a.wallets.map(b => {
        let {currency} = b
        let titleUSD = `${currency}-USD`
        let price24h = tokenPrices24h[titleUSD]?.price
        let value24h = price24h * Number(b.projectedBalance)
        sum24Value += value24h
        sumValue += Number(b.value)
      })
      let difference = ((sumValue - sum24Value) / sum24Value) * 100 || 0
      let difColor = '#00FFBD'
      let difSymbol = '+'
      if (difference < 0) {
        difColor = '#E5705A'
        difSymbol = ''
      }
      if (difference == 0) {
        difSymbol = ''
        difColor = '#999'
      }
      //portfolioBalanceNum += sumValue;
      sumValue = numberWithCommas(Number(sumValue).toFixed(2))
      let showDifference = difference.toFixed(2)
      let showAccountType = accountType
      if (accountType == 'Exchange') {
        showAccountType = 'WALLET Account'
      }
      let productImg = noLoanImg
      let productImgStyle = styles.accountListImg
      if (accountType == 'Exchange') {
        productImg = custodyAccount
        productImgStyle = styles.accountListImgExchange
      }
      return (
        <Card
          key={k}
          style={{backgroundColor: '#28283D'}}
          cardMarginBottom={10}
          cardWidth={ScreenWidth - 40}>
          <TouchableOpacity
            onPress={() => this.chooseAccount(a, sumValue)}
            style={styles.accountListBox}>
            <View style={{flexDirection: 'row'}}>
              <View style={styles.accountListImgBox}>
                <Image source={productImg} style={productImgStyle} />
              </View>
              <View>
                <View style={{flexDirection: 'row'}}>
                  <TextReg
                    style={{
                      marginLeft: 3,
                      marginTop: 4,
                      flexWrap: 'wrap',
                      maxWidth: 190,
                    }}>
                    {showAccountName}
                  </TextReg>
                </View>
                <TextReg style={{opacity: 0.5, marginLeft: 2, marginTop: 2}}>
                  {this.getStepDescription(a)}
                </TextReg>
              </View>
            </View>
            <View
              style={{
                marginRight: 4,
                marginTop: 5,
                alignItems: 'flex-end',
              }}>
              <TextBold style={{marginBottom: 2}}>{`$${sumValue}`}</TextBold>
              <TextReg
                style={{
                  opacity: 0.9,
                  color: difColor,
                }}>{`${difSymbol}${showDifference}%`}</TextReg>
            </View>
          </TouchableOpacity>
        </Card>
      )
    })

    let {slideHidden, hasPendingWithdrawal, belowMinimum, hasAch} = this.state

    var transferButtonColor = this.backgroundDarkness.interpolate({
      inputRange: [0, 0.7],
      outputRange: ['rgba(90, 51, 227, 1)', 'rgba(225, 255, 225, 1)'],
    })
    const showPinIntro = this.props.storedPin === '0' || this.props.backToSettings

    let transferWalletPic = getTokenPic(this.state.currency)

    let withdrawMaxError = false
    let numberInput = Number(this.state.amountInput)
    if (this.state.oltv != 'max' && numberInput > this.state.oltv) {
      withdrawMaxError = true
    }
    if (this.state.oltv == 'max' && numberInput > this.state.projectedBalance) {
      withdrawMaxError = true
    }
    if (this.state.isStabilized) {
      withdrawMaxError = true
    }
    if (hasPendingWithdrawal) {
      withdrawMaxError = true
    }
    if (hasAch) {
      withdrawMaxError = true
    }

    let showTransferContinue =
      this.state.step != 5 &&
      this.state.transferAcc1 != '' &&
      this.state.transferAcc2 != '' &&
      numberInput != 0 &&
      !withdrawMaxError

    let showWithdrawalContinue =
      !this.state.overMax &&
      this.state.step != 4 &&
      this.state.withdrawAddressInput != '' &&
      this.state.agreeYes != '' &&
      numberInput != 0 &&
      !withdrawMaxError &&
      !belowMinimum

    let flowTitle = 'Transfer'
    let flowTitleSmall = 'transfer'
    if (this.state.flow == 'withdrawal') {
      flowTitle = 'Withdrawal'
      flowTitleSmall = 'withdrawal'
    }

    let walletsEnabled = this.props.user?.walletsEnabled || false
    let ldWithdrawalOff = this.props.launchDarkly['disable-withdraw'] || false
    let ldDepositOff = this.props.launchDarkly['disable-deposit'] || false

    let inputNoComma = '0'
    if (this.state.amountInput != 0) {
      inputNoComma = this.state.amountInput?.split(',').join('')
    }

    return (
      <View style={{display: showPinIntro ? 'none' : 'flex'}}>
        <Animated.View
          pointerEvents={slideHidden ? 'none' : 'auto'}
          style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            width: ScreenWidth,
            height: ScreenHeight,
            backgroundColor: '#000',
            opacity: this.backgroundDarkness,
          }}>
          {!slideHidden && (
            <TouchableOpacity
              onPress={() => this.toggleSlide()}
              style={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                width: ScreenWidth,
                height: ScreenHeight,
              }}
            />
          )}
        </Animated.View>
        <WithdrawQRScanner
          toggleScannerModal={this.toggleScannerModal}
          scannerModalVisable={this.state.scannerModalVisable}
          showPinScreen={this.props.showPinScreen}
          updateAddressFromQR={this.updateAddressFromQR}
        />
        <DisabledModal
          showSetup={this.state.promptVisible || false}
          closeSetup={() => this.setState({promptVisible: false})}
          showPinScreen={this.props.showPinScreen}
          promptType={this.state.promptType}
          launchDarkly={this.props.launchDarkly}
        />
        <View
          pointerEvents="box-none"
          style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            width: ScreenWidth,
            height: lowerSlideButton ? 78 : 100,
            alignItems: 'center',
            zIndex: 65,
            display: this.state.keyboardIsOpen
              ? 'none'
              : this.props?.newAgreement?.body
              ? 'none'
              : this.props.unit21Showing
              ? 'none'
              : 'flex',
          }}>
          {!showTransferContinue && !showWithdrawalContinue && (
            <AnimatedTouchable
              onPress={() => {
                this.toggleSlide()
              }}
              style={{
                height: 66,
                width: 66,
                backgroundColor: transferButtonColor,
                borderRadius: 33,
                borderWidth: 0.5,
                borderColor: slideHidden ? '#6F59BC' : '#3D3D50',
                zIndex: 50,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <View
                style={{
                  position: 'relative',
                  lignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Animated.View style={{opacity: this.transferOpacity}}>
                  <Image
                    source={require('../../imgs/icons/transfer.png')}
                    style={{height: 22, width: 32}}
                  />
                </Animated.View>
                <Animated.View
                  style={{
                    opacity: this.closeOpacity,
                    position: 'absolute',
                    marginLeft: 4,
                    marginTop: 4,
                  }}>
                  <Image
                    source={require('../../imgs/icons/closeDark.png')}
                    style={{height: 24, width: 24}}
                  />
                </Animated.View>
              </View>
            </AnimatedTouchable>
          )}
        </View>

        <Animated.View
          style={{
            backgroundColor: '#3D3D50',
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            width: ScreenWidth,
            height: 600,
            borderTopLeftRadius: 14,
            borderTopRightRadius: 14,
            alignItems: 'center',
            transform: [{translateY: this.slideValue}],
          }}>
          {this.state.flow == 'walletMenuWithdrawal' && (
            <View
              style={{
                marginTop: 14,
                alignSelf: 'stretch',
                marginLeft: 30,
                marginRight: 30,
              }}>
              <View style={{alignSelf: 'stretch', alignItems: 'center'}}>
                <TextReg style={{fontSize: 22, marginBottom: 10, opacity: 0.8}}>
                  Send {this.state.currency}
                </TextReg>
              </View>
              <TouchableOpacity
                onPress={() => this.continue('transfer')}
                style={{
                  height: 80,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  borderBottomWidth: 0.5,
                  borderColor: '#FFF',
                }}>
                <View>
                  <TextReg style={{fontSize: 26}}>Transfer</TextReg>
                  <TextReg style={{fontSize: 15}}>to my other SALT accounts</TextReg>
                </View>
                <Image
                  source={require('../../imgs/icons/transferRight.png')}
                  style={{
                    height: 22,
                    width: 19,
                    marginRight: 4,
                    marginTop: 2,
                  }}
                />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => this.continue('withdrawal')}
                style={{
                  height: 80,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                <View>
                  <TextReg style={{fontSize: 26}}>Withdraw</TextReg>
                  <TextReg style={{fontSize: 15}}>to an external wallet</TextReg>
                </View>
                <Image
                  source={require('../../imgs/icons/withdrawal.png')}
                  style={{
                    height: 23,
                    width: 23,
                    marginRight: 2,
                    marginTop: 1,
                  }}
                />
              </TouchableOpacity>
            </View>
          )}
          {this.state.flow == 'menu' && (
            <View
              style={{
                marginTop: 20,
                alignSelf: 'stretch',
                marginLeft: 30,
                marginRight: 30,
              }}>
              {walletsEnabled ? (
                <>
                  <TouchableOpacity
                    onPress={() => this.start('transfer')}
                    style={{
                      height: 80,
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      borderBottomWidth: 0.5,
                      borderColor: '#FFF',
                    }}>
                    <View>
                      <TextReg style={{fontSize: 26}}>Transfer</TextReg>
                      <TextReg style={{fontSize: 15}}>to my other SALT accounts</TextReg>
                    </View>
                    <Image
                      source={require('../../imgs/icons/transferRight.png')}
                      style={{
                        height: 22,
                        width: 19,
                        marginRight: 4,
                        marginTop: 2,
                      }}
                    />
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() =>
                      ldDepositOff ? this.showPrompt('deposit') : this.start('deposit')
                    }
                    style={{
                      height: 80,
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      borderBottomWidth: 0.5,
                      borderColor: '#FFF',
                      opacity: ldDepositOff ? 0.3 : 1,
                    }}>
                    <View>
                      <TextReg style={{fontSize: 26}}>Deposit</TextReg>
                      <TextReg style={{fontSize: 15}}>to my SALT account</TextReg>
                    </View>
                    <Image
                      source={require('../../imgs/icons/deposit.png')}
                      style={{
                        height: 23,
                        width: 23,
                        marginRight: 2,
                        marginBottom: -4,
                      }}
                    />
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() =>
                      ldWithdrawalOff ? this.showPrompt('withdrawal') : this.start('withdrawal')
                    }
                    style={{
                      height: 80,
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      opacity: ldWithdrawalOff ? 0.3 : 1,
                    }}>
                    <View>
                      <TextReg style={{fontSize: 26}}>Withdraw</TextReg>
                      <TextReg style={{fontSize: 15}}>to an external wallet</TextReg>
                    </View>
                    <Image
                      source={require('../../imgs/icons/withdrawal.png')}
                      style={{
                        height: 23,
                        width: 23,
                        marginRight: 2,
                        marginTop: 1,
                      }}
                    />
                  </TouchableOpacity>
                </>
              ) : (
                <View>
                  <TextReg
                    style={{
                      fontSize: 19,
                      textAlign: 'center',
                      marginTop: 24,
                      opacity: 1,
                      color: '#e5705a',
                    }}>
                    {'Wallets are not enabled yet, please finish user verification flow'}
                  </TextReg>
                </View>
              )}
            </View>
          )}
          {this.state.flow == 'deposit' && (
            <FlowDeposit
              chosenAccName={
                this.state.chosenAccName === 'Custody Account'
                  ? 'WALLET Account'
                  : this.state.chosenAccName
              }
              step={this.state.step}
              accountList={accountList}
              wallets={this.state.wallets}
              walletsList={walletsList}
              capitalize={this.capitalize}
              currency={this.state.currency}
              productType={this.state.productType}
              address={this.state.address}
              copyAddress={this.copyAddress}
              fullName={this.state.fullName}
              showCopiedButton={this.state.showCopiedButton}
              launchDarkly={this.props.launchDarkly}
            />
          )}
          {this.state.flow == 'withdrawal' && (
            <View
              style={{
                alignSelf: 'stretch',
              }}>
              {this.state.step == 0 && (
                <ScrollView
                  style={{
                    paddingTop: 30,
                    paddingLeft: 20,
                    paddingRight: 20,
                  }}>
                  <TextReg style={{fontSize: 17, marginBottom: 14, marginLeft: 10}}>
                    Where would you like to withdraw funds from?
                  </TextReg>
                  {accountList}

                  {oneAccountPendingAch && (
                    <TextReg style={{color: '#E5705A', textAlign: 'center'}}>
                      {`Note: Account with pending ACH payments cannot be used for withdrawals, and are not listed.`}
                    </TextReg>
                  )}

                  <View style={{alignSelf: 'stretch', height: 240, widht: 300}} />
                </ScrollView>
              )}
              {this.state.step == 1 && (
                <ScrollView style={{paddingTop: 30, paddingLeft: 20, paddingRight: 20}}>
                  <TextReg style={{fontSize: 16, marginBottom: 14, marginLeft: 10}}>
                    Select which asset you would like to withdraw.
                  </TextReg>
                  {walletsList.length > 0 ? (
                    walletsList
                  ) : (
                    <TextReg
                      style={{
                        fontSize: 20,
                        color: '#E5705A',
                        alignSelf: 'center',
                        marginTop: 20,
                      }}>
                      No wallets with balance
                    </TextReg>
                  )}
                  <View style={{alignSelf: 'stretch', height: 240, widht: 300}} />
                </ScrollView>
              )}
              {this.state.step == 2 && (
                <TouchableOpacity
                  onPress={() => Keyboard.dismiss()}
                  activeOpacity={1}
                  style={{paddingTop: 20, paddngLeft: 20, paddingRight: 20}}>
                  <View
                    style={{
                      alignSelf: 'stretch',
                      alignItems: 'center',
                      marginBottom: 14,
                    }}>
                    <View
                      style={{
                        flexDirection: 'row',
                      }}>
                      <TextInput
                        onChangeText={text => this.handleAmountInput(text)}
                        keyboardType="numeric"
                        style={{
                          fontSize: 38,
                          color: '#FFF',
                        }}
                        underlineColorAndroid="transparent"
                        blurOnSubmit
                        value={
                          this.state.toggleWithdrawal == 'USD'
                            ? this.state.fiatInput
                              ? `$${
                                  this.state.fiatInput.toString().includes(',')
                                    ? this.state.fiatInput
                                    : numberWithCommas(this.state.fiatInput)
                                }`
                              : ''
                            : this.state.amountInput
                            ? this.state.price == 0
                              ? '--'
                              : this.state.amountInput
                            : ''
                        }
                        multiline
                        returnKeyType={'done'}
                        onSubmitEditing={() => Keyboard.dismiss()}
                        keyboardAppearance="dark"
                        placeholder={this.state.toggleWithdrawal == 'USD' ? `$0.00` : `0.0000000`}
                        placeholderTextColor={'#DDD'}
                      />
                      {this.state.toggleWithdrawal == 'CRYPTO' && (
                        <TextReg
                          style={{
                            fontSize: 38,
                            marginLeft: 6,
                            marginTop: 3,
                          }}>
                          {this.state.currency.toLowerCase()}
                        </TextReg>
                      )}
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignSelf: 'stretch',
                      justifyContent: 'center',
                    }}>
                    <TouchableOpacity
                      onPress={() => this.toggleWithdrawal('USD')}
                      style={{
                        height: 30,
                        width: 110,
                        backgroundColor:
                          this.state.toggleWithdrawal == 'USD' ? '#00FFBD' : '#3D3D50',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderTopLeftRadius: 4,
                        borderBottomLeftRadius: 4,
                        borderWidth: 1,
                        borderColor: '#00FFBD',
                      }}>
                      <TextReg
                        style={{
                          color: this.state.toggleWithdrawal == 'USD' ? '#3D3D50' : '#00FFBD',
                          fontSize: 16,
                        }}>
                        USD
                      </TextReg>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => this.toggleWithdrawal('CRYPTO')}
                      style={{
                        height: 30,
                        width: 110,
                        backgroundColor:
                          this.state.toggleWithdrawal == 'CRYPTO' ? '#00FFBD' : '#3D3D50',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderTopRightRadius: 4,
                        borderBottomRightRadius: 4,
                        borderWidth: 1,
                        borderColor: '#00FFBD',
                      }}>
                      <TextReg
                        style={{
                          color: this.state.toggleWithdrawal == 'CRYPTO' ? '#3D3D50' : '#00FFBD',
                          fontSize: 16,
                        }}>
                        CRYPTO
                      </TextReg>
                    </TouchableOpacity>
                  </View>
                  <TouchableOpacity
                    onPress={() => this.withdrawMax()}
                    style={{alignSelf: 'stretch', alignItems: 'center'}}>
                    <TextBold style={{color: '#00FFBD', fontSize: 18, marginTop: 14}}>
                      WITHDRAW MAX
                    </TextBold>
                  </TouchableOpacity>

                  <View style={{position: 'relative'}}>
                    <TextInput
                      onChangeText={text => this.handleAddressInput(text)}
                      onLayout={event => {
                        var {x, y, width, height} = event.nativeEvent.layout
                        this.setState({inputHeight: height})
                      }}
                      style={{
                        minHeight: 54,
                        color: '#FFF',
                        fontSize: 18,
                        textAlign: 'left',
                        paddingTop: 14,
                        alignSelf: 'stretch',
                        alignItems: 'center',
                        marginTop: 18,
                        borderRadius: 14,
                        borderWidth: 1,
                        borderColor: '#EEEFF3',
                        paddingLeft: 20,
                        alignSelf: 'stretch',
                        marginLeft: 20,
                        paddingRight: 60,
                        paddingBottom: 14,
                      }}
                      underlineColorAndroid="transparent"
                      blurOnSubmit
                      value={this.state.withdrawAddressInput}
                      multiline
                      returnKeyType={'done'}
                      onSubmitEditing={() => Keyboard.dismiss()}
                      keyboardAppearance="dark"
                      placeholder={this.state.withdrawAddress}
                      placeholderTextColor={'#AAA'}
                    />
                    <TouchableOpacity
                      style={{
                        position: 'absolute',
                        right: -5,
                        top: 6 + this.state.inputHeight / 2,
                      }}
                      onPress={() => this.toggleScannerModal()}>
                      <Image
                        source={qRcodeScanner}
                        style={{height: 24, width: 24, marginRight: 20}}
                      />
                    </TouchableOpacity>
                  </View>

                  <TouchableOpacity
                    style={{marginTop: 20, marginLeft: 20, marginRight: 20}}
                    onPress={() => this.toggleAgreeWithdrawal()}>
                    <View style={{flexDirection: 'row'}}>
                      <Image
                        source={this.state.agreeYes ? agreedYes : agreedNo}
                        style={{height: 26, width: 26, marginRight: 8}}
                      />
                      <TextReg style={{fontSize: 18}}>I Agree</TextReg>
                    </View>
                    <TextReg style={{marginLeft: 36, marginTop: 3, fontSize: 12}}>
                      {`that if I am sending ${this.state.fullName} (${this.state.currency}) to a smart contract, I must ensure that the receiving address can support such tokens and that I will have the ability to access such tokens after moving from the SALT platform. SALT Lending is not able to confirm or ensure that the smart contract address will support the tokens or that you will have access to such tokens after transfer. If you have any questions, please contact <NAME_EMAIL>.`}
                    </TextReg>
                  </TouchableOpacity>
                  {(withdrawMaxError || hasPendingWithdrawal || belowMinimum) &&
                    !this.state.overMax && (
                      <View
                        style={{
                          alignSelf: 'stretch',
                          alignItems: 'center',
                          marginTop: 14,
                        }}>
                        <TextReg
                          style={{
                            color: '#E5705A',
                            fontSize: 18,
                            textAlign: 'center',
                            width: 250,
                          }}>
                          {this.state.isStabilized
                            ? `Cannot ${flowTitleSmall} while loan is stabilized`
                            : hasPendingWithdrawal
                            ? 'Wallet currently has a pending withdrawal'
                            : belowMinimum
                            ? 'Minimum Withdrawal Amount of $15'
                            : `${flowTitle} cannot exceed OLTV or ${this.state.currency} balance`}
                        </TextReg>
                      </View>
                    )}

                  {this.state.overMax && (
                    <View
                      style={{
                        alignSelf: 'stretch',
                        alignItems: 'center',
                        marginTop: 14,
                      }}>
                      <TextReg
                        style={{
                          color: '#E5705A',
                          fontSize: 18,
                          textAlign: 'center',
                          width: 250,
                        }}>
                        {`${flowTitle} cannot exceed ${this.state.currency} balance`}
                      </TextReg>
                    </View>
                  )}
                  {showWithdrawalContinue && (
                    <View style={{flexDirection: 'column', alignItems: 'center'}}>
                      {!this.state.isValidAddress && (
                        <TextReg
                          style={{
                            marginBottom: -60,
                            marginTop: 38,
                            fontSize: 18,
                            color: '#E5705A',
                          }}>
                          Invalid Address
                        </TextReg>
                      )}
                      {withdrawMaxError && (
                        <TextReg
                          style={{
                            marginBottom: -60,
                            marginTop: 38,
                            fontSize: 18,
                            color: '#E5705A',
                          }}>
                          Cannot withdraw this amount
                        </TextReg>
                      )}

                      <TouchableOpacity
                        onPress={() => this.withdrawalReview()}
                        style={{
                          height: 60,
                          alignSelf: 'stretch',
                          backgroundColor: '#00FFBD',
                          marginTop: 30,
                          marginLeft: 30,
                          marginRight: 10,
                          borderRadius: 4,
                          alignItems: 'center',
                          justifyContent: 'center',
                          zIndex: 70,
                          marginTop: 70,
                        }}>
                        <TextReg
                          style={{
                            color: '#000',
                            fontSize: 20,
                            letterSpacing: 2,
                          }}>
                          WITHDRAW
                        </TextReg>
                      </TouchableOpacity>
                    </View>
                  )}
                </TouchableOpacity>
              )}
              {this.state.step == 3 && (
                <View
                  style={{
                    paddingTop: 30,
                    paddingLeft: 20,
                    paddingRight: 20,
                  }}>
                  <TextReg style={{fontSize: 20, marginBottom: 24, marginLeft: 10}}>
                    Confirm Withdrawal
                  </TextReg>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginLeft: 10,
                      marginRight: 10,
                      marginBottom: 18,
                    }}>
                    <TextReg style={{fontSize: 18}}>Two-Factor:</TextReg>
                    <TextInput
                      onChangeText={text => this.handleTwoFactor(text)}
                      style={{
                        borderRadius: 4,
                        borderWidth: 1,
                        height: 40,
                        borderColor: '#EEEFF3',
                        width: 100,
                        color: '#FFF',
                        fontSize: 17,
                        textAlign: 'center',
                        padding: 4,
                      }}
                      underlineColorAndroid="transparent"
                      blurOnSubmit
                      value={this.state.twoFactor}
                      returnKeyType={'done'}
                      onSubmitEditing={() => Keyboard.dismiss()}
                      keyboardAppearance="dark"
                      keyboardType={'numeric'}
                      placeholder={'000000'}
                      placeholderTextColor={'#AAA'}
                    />
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginLeft: 10,
                      marginRight: 10,
                      marginBottom: 20,
                    }}>
                    <TextReg style={{fontSize: 18}}>Withdraw From:</TextReg>
                    <View style={{flexDirection: 'row'}}>
                      <TextReg style={{fontSize: 18, marginRight: 4}}>{''}</TextReg>
                      <TextReg style={{fontSize: 18, opacity: 0.6}}>
                        {this.state.withdrawName}
                      </TextReg>
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginLeft: 10,
                      marginRight: 10,
                      marginBottom: 14,
                    }}>
                    <TextReg style={{fontSize: 18}}>Withdraw To:</TextReg>
                    <View style={{flexDirection: 'row'}}>
                      <TextReg
                        style={{
                          fontSize: 16,
                          opacity: 1,
                          textAlign: 'right',
                          width: 190,
                        }}>
                        {this.state.withdrawAddressInput}
                      </TextReg>
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginLeft: 10,
                      marginRight: 10,
                      marginBottom: 16,
                    }}>
                    <TextReg style={{fontSize: 18}}>Currency:</TextReg>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <Image
                        source={transferWalletPic}
                        style={{
                          height: 24,
                          width: 24,
                          marginRight: 4,
                          opacity: 0.9,
                          marginBottom: -12,
                        }}
                      />
                      <TextReg
                        style={{
                          fontSize: 17,
                          marginTop: 14,
                          marginRight: 6,
                        }}>
                        {`${this.state.currency}`}
                      </TextReg>
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginLeft: 10,
                      marginRight: 10,
                      marginBottom: 20,
                    }}>
                    <TextReg style={{fontSize: 18}}>Amount:</TextReg>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <TextReg
                        style={{
                          fontSize: 18,
                          marginRight: 6,
                        }}>{`$${numberWithCommas(this.state.fiatInput)}`}</TextReg>
                      <TextReg style={{fontSize: 18, opacity: 0.7}}>
                        {Number(inputNoComma).toFixed(8)}
                      </TextReg>
                    </View>
                  </View>
                  {this.state.transferErr && (
                    <View
                      style={{
                        alignSelf: 'stretch',
                        marginBottom: -10,
                        alignItems: 'center',
                      }}>
                      <TextReg
                        style={{
                          color: '#E5705A',
                          fontSize: 18,
                          textAlign: 'center',
                        }}>
                        {this.state.transferErr}
                      </TextReg>
                    </View>
                  )}
                  <Button
                    disabled={!this.state.twoFactor || this.state.twoFactor?.length < 6}
                    isLoading={this.state.trasnferLoading}
                    onPress={() => this.withdrawSend()}
                    style={{
                      height: 60,
                      alignSelf: 'stretch',
                      backgroundColor: '#00FFBD',
                      marginTop: this.state.transferErr ? 20 : 40,
                      marginLeft: 10,
                      marginRight: 10,
                      borderRadius: 4,
                      alignItems: 'center',
                      justifyContent: 'center',
                      zIndex: 70,
                    }}>
                    <TextReg
                      style={{
                        color: '#000',
                        fontSize: 20,
                        letterSpacing: 2,
                      }}>
                      CONFIRM
                    </TextReg>
                  </Button>
                </View>
              )}
              {this.state.step == 4 && (
                <View
                  style={{
                    paddingTop: 30,
                    paddingLeft: 20,
                    paddingRight: 20,
                    alignSelf: 'stretch',
                    alignItems: 'center',
                  }}>
                  <TextReg
                    style={{
                      fontSize: 20,
                      marginBottom: 24,
                      marginLeft: 10,
                      marginTop: 20,
                    }}>
                    Successful Withdrawal
                  </TextReg>
                  <Image
                    source={require('../../imgs/graphics/successImg.png')}
                    style={{height: 80, width: 80}}
                  />
                  <TextReg
                    style={{
                      marginTop: 30,
                      width: 300,
                      textAlign: 'center',
                      fontSize: 17,
                    }}>
                    You may view your withdrawal details in your transaction history of your
                    wallets.
                  </TextReg>
                </View>
              )}
            </View>
          )}
          {this.state.flow == 'transfer' && (
            <FlowTransfer
              step={this.state.step}
              handleAmountInput={this.handleAmountInput}
              toggleWithdrawal={this.state.toggleWithdrawal}
              fiatInput={this.state.fiatInput}
              amountInput={this.state.amountInput}
              price={this.state.price}
              currency={this.state.currency}
              toggleWithdrawalFn={this.toggleWithdrawal}
              withdrawMax={this.withdrawMax}
              transferAccountPick={this.transferAccountPick}
              transferName1={
                this.state.transferName1 === 'Custody Account'
                  ? 'WALLET Account'
                  : this.state.transferName1
              }
              transferAcc1={this.state.transferAcc1}
              transferSumValue1={this.state.transferSumValue1}
              transferName2={
                this.state.transferName2 === 'Custody Account'
                  ? 'WALLET Account'
                  : this.state.transferName2
              }
              transferAcc2={this.state.transferAcc2}
              transferSumValue2={this.state.transferSumValue2}
              withdrawMaxError={withdrawMaxError}
              flowTitleSmall={flowTitleSmall}
              flowTitle={flowTitle}
              showTransferContinue={showTransferContinue}
              transferReview={this.transferReview}
              accountList={accountList}
              walletsList={walletsList}
              transferWalletPic={transferWalletPic}
              paxGError={paxGError}
              transferErr={this.state.transferErr}
              isStabilized={this.state.isStabilized}
              trasnferLoading={this.state.trasnferLoading}
              transferSend={this.transferSend}
              hasAch={this.state.hasAch}
              wallets1={this.state.wallets1}
            />
          )}
        </Animated.View>
      </View>
    )
  }
}

const mapStateToProps = state => ({
  WebService: state.auth.WebService,
  user: state.user.user,
  backToSettings: state.user.backToSettings,
  storedPin: state.auth.pin,
  tokenPrices: state.user.prices,
  tokenPrices24h: state.user.prices24h,
  externalSlideFn: state.user.externalSlideFn,
  newAgreement: state.user.newAgreement,
  unit21Showing: state.user.unit21Showing,
  showPinScreen: state.auth.pinScreen,
  launchDarkly: state.launchDarkly,
  showPinScreen: state.auth.pinScreen,
})

export default connect(mapStateToProps)(TransferSlide)
