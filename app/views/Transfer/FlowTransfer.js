import React from 'react'
import {
  View,
  Dimensions,
  ScrollView,
  TouchableOpacity,
  Image,
  Keyboard,
  TextInput,
} from 'react-native'
import {Card, TextBold, TextReg, Button, DisabledModal} from '../../components'
import {numberWithCommas} from '../../util/helpers'

import styles from './styles'

const FlowTransfer = props => {
  let {
    step,
    handleAmountInput,
    toggleWithdrawal,
    fiatInput,
    amountInput,
    price,
    currency,
    toggleWithdrawalFn,
    withdrawMax,
    transferAccountPick,
    transferName1,
    transferSumValue1,
    transferName2,
    transferAcc1,
    transferAcc2,
    transferSumValue2,
    withdrawMaxError,
    flowTitleSmall,
    flowTitle,
    showTransferContinue,
    transferReview,
    accountList,
    walletsList,
    transferWalletPic,
    paxGError,
    transferErr,
    isStabilized,
    trasnferLoading,
    transferSend,
    hasAch,
    wallets1,
  } = props

  let inputNoComma = '0'
  if (amountInput != 0) {
    inputNoComma = amountInput?.split(',').join('')
  }

  let walletAmount = 0
  if (currency && wallets1?.length > 0) {
    walletAmount = wallets1?.filter(a => a.currency == currency)[0]?.value || 0
    walletAmount = numberWithCommas(Number(walletAmount)?.toFixed(2))
  }

  console.log('transferName1', transferName1)
  console.log('accountList', accountList)
  accountList = accountList?.filter(a => a)

  return (
    <View
      style={{
        alignSelf: 'stretch',
      }}>
      {step == 0 && (
        <TouchableOpacity
          onPress={() => Keyboard.dismiss()}
          activeOpacity={1}
          style={{paddingTop: 20, paddngLeft: 20, paddingRight: 20}}>
          <TextReg style={{marginLeft: 20, fontSize: 17}}>Transfer</TextReg>
          <View
            style={{
              alignSelf: 'stretch',
              alignItems: 'center',
              marginBottom: 14,
            }}>
            <View
              style={{
                flexDirection: 'row',
              }}>
              <TextInput
                onChangeText={text => handleAmountInput(text)}
                keyboardType="numeric"
                style={{
                  fontSize: 40,
                  color: '#FFF',
                  textAlign: 'center',
                  minWidth: 100,
                }}
                underlineColorAndroid="transparent"
                blurOnSubmit
                value={
                  toggleWithdrawal == 'USD'
                    ? fiatInput
                      ? `$${
                          fiatInput.toString().includes(',')
                            ? fiatInput
                            : numberWithCommas(fiatInput)
                        }`
                      : ''
                    : amountInput
                    ? price == 0
                      ? '--'
                      : amountInput
                    : ''
                }
                multiline
                returnKeyType={'done'}
                onSubmitEditing={() => Keyboard.dismiss()}
                keyboardAppearance="dark"
                placeholder={toggleWithdrawal == 'USD' ? `$0.00` : `0.0000000`}
                placeholderTextColor={'#DDD'}
              />
              {toggleWithdrawal == 'CRYPTO' && (
                <TextReg
                  style={{
                    fontSize: 38,
                    marginLeft: 6,
                    marginTop: 3,
                  }}>
                  {currency.toLowerCase()}
                </TextReg>
              )}
            </View>
          </View>
          <View
            style={{
              flexDirection: 'row',
              alignSelf: 'stretch',
              justifyContent: 'center',
              marginTop: 4,
            }}>
            <TouchableOpacity
              onPress={() => toggleWithdrawalFn('USD')}
              style={{
                height: 30,
                width: 110,
                backgroundColor: toggleWithdrawal == 'USD' ? '#00FFBD' : '#3D3D50',
                alignItems: 'center',
                justifyContent: 'center',
                borderTopLeftRadius: 4,
                borderBottomLeftRadius: 4,
                borderWidth: 1,
                borderColor: '#00FFBD',
              }}>
              <TextReg
                style={{
                  color: toggleWithdrawal == 'USD' ? '#3D3D50' : '#00FFBD',
                  fontSize: 16,
                }}>
                USD
              </TextReg>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => toggleWithdrawalFn('CRYPTO')}
              style={{
                height: 30,
                width: 110,
                backgroundColor: toggleWithdrawal == 'CRYPTO' ? '#00FFBD' : '#3D3D50',
                alignItems: 'center',
                justifyContent: 'center',
                borderTopRightRadius: 4,
                borderBottomRightRadius: 4,
                borderWidth: 1,
                borderColor: '#00FFBD',
              }}>
              <TextReg
                style={{
                  color: toggleWithdrawal == 'CRYPTO' ? '#3D3D50' : '#00FFBD',
                  fontSize: 16,
                }}>
                CRYPTO
              </TextReg>
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            onPress={() => withdrawMax()}
            style={{
              alignSelf: 'stretch',
              alignItems: 'center',
              marginTop: 20,
            }}>
            <TextReg style={{color: '#00FFBD', fontSize: 22}}>TRANSFER MAX</TextReg>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => transferAccountPick(1)}
            style={{
              alignSelf: 'stretch',
              borderTopRightRadius: 14,
              borderTopLeftRadius: 14,
              backgroundColor: '#28283D',
              borderWidth: 1,
              borderColor: '#E3E6ED',
              marginLeft: 30,
              marginRight: 10,
              marginTop: 24,
              paddingTop: 16,
              paddingBottom: 16,
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'flex-start',
                justifyContent: 'space-between',
              }}>
              <TextReg style={{marginLeft: 30, fontSize: 17, marginRight: 4}}>
                Transfer From:
              </TextReg>
              {transferName1 ? (
                <TextReg
                  style={{
                    fontSize: 17,
                    opacity: 0.7,
                    flexWrap: 'wrap',
                    flex: 1,
                    marginRight: 4,
                  }}>
                  {transferName1}
                </TextReg>
              ) : (
                <Image
                  source={require('../../imgs/rightArrow.png')}
                  style={{height: 22, width: 22, marginRight: 20}}
                />
              )}
            </View>
            {transferAcc1 != '' && (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}>
                <TextReg
                  style={{
                    marginLeft: 30,
                    fontSize: 17,
                    marginTop: 14,
                  }}>
                  Account Balance:
                </TextReg>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  <TextReg
                    style={{
                      fontSize: 17,
                      marginTop: 14,
                      marginRight: 8,
                    }}>
                    {`${''}`}
                  </TextReg>
                  <TextReg
                    style={{
                      fontSize: 17,
                      marginTop: 14,
                      marginRight: 20,
                      opacity: 0.6,
                      marginBottom: -2,
                    }}>
                    {`$${walletAmount}`}
                  </TextReg>
                </View>
              </View>
            )}
            {currency != '' && (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}>
                <TextReg
                  style={{
                    marginLeft: 30,
                    fontSize: 17,
                    marginTop: 14,
                  }}>
                  Wallet:
                </TextReg>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  <Image
                    source={transferWalletPic}
                    style={{
                      height: 24,
                      width: 24,
                      marginRight: 4,
                      opacity: 0.9,
                      marginBottom: -12,
                    }}
                  />
                  <TextReg
                    style={{
                      fontSize: 17,
                      marginTop: 14,
                      marginRight: 20,
                    }}>
                    {`${currency}`}
                  </TextReg>
                </View>
              </View>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => transferAccountPick(2)}
            style={{
              alignSelf: 'stretch',
              borderBottomRightRadius: 14,
              borderBottomLeftRadius: 14,
              backgroundColor: '#28283D',
              borderWidth: 1,
              borderColor: '#E3E6ED',
              marginLeft: 30,
              marginRight: 10,
              paddingTop: 16,
              paddingBottom: 16,
              marginTop: -1,
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'flex-start',
                justifyContent: 'space-between',
              }}>
              <TextReg style={{marginLeft: 30, fontSize: 17, marginRight: 4}}>Transfer To:</TextReg>
              {transferName2 ? (
                <TextReg
                  style={{
                    fontSize: 17,
                    opacity: 0.7,
                    flexWrap: 'wrap',
                    flex: 1,
                    marginRight: 4,
                  }}>
                  {transferName2}
                </TextReg>
              ) : (
                <Image
                  source={require('../../imgs/rightArrow.png')}
                  style={{height: 22, width: 22, marginRight: 20}}
                />
              )}
            </View>

            {transferAcc2 != '' && (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}>
                <TextReg
                  style={{
                    marginLeft: 30,
                    fontSize: 17,
                    marginTop: 14,
                  }}>
                  Account Balance:
                </TextReg>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  <TextReg
                    style={{
                      fontSize: 17,
                      marginTop: 14,
                      marginRight: 8,
                    }}>
                    {`${''}`}
                  </TextReg>

                  <TextReg
                    style={{
                      fontSize: 17,
                      marginTop: 14,
                      marginRight: 20,
                      opacity: 0.6,
                      marginBottom: -2,
                    }}>
                    {`$${transferSumValue2}`}
                  </TextReg>
                </View>
              </View>
            )}
          </TouchableOpacity>
          {withdrawMaxError && (
            <View
              style={{
                alignSelf: 'stretch',
                alignItems: 'center',
                marginTop: 14,
              }}>
              <TextReg
                style={{
                  color: '#E5705A',
                  fontSize: 18,
                  textAlign: 'center',
                  width: 250,
                }}>
                {isStabilized
                  ? `Cannot ${flowTitleSmall} while loan is stabilized`
                  : hasAch
                  ? 'Cannot transfer assets from account with current ACH pending payment'
                  : `${flowTitle} cannot exceed OLTV or ${currency} balance`}
              </TextReg>
            </View>
          )}

          {showTransferContinue && (
            <TouchableOpacity
              onPress={() => transferReview()}
              style={{
                height: 60,
                alignSelf: 'stretch',
                backgroundColor: '#00FFBD',
                marginTop: 30,
                marginLeft: 30,
                marginRight: 10,
                borderRadius: 4,
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 70,
              }}>
              <TextReg
                style={{
                  color: '#000',
                  fontSize: 20,
                  letterSpacing: 2,
                }}>
                TRANSFER
              </TextReg>
            </TouchableOpacity>
          )}
        </TouchableOpacity>
      )}
      {step == 1 && (
        <ScrollView
          style={{
            paddingTop: 30,
            paddingLeft: 20,
            paddingRight: 20,
            postition: 'relative',
          }}>
          <TextReg style={{fontSize: 17, marginBottom: 14, marginLeft: 10}}>
            Where would you like to transfer from?
          </TextReg>
          {accountList}
          {accountList?.length < 1 && (
            <TextReg
              style={{
                textAlign: 'center',
                marginLeft: 20,
                marginRight: 20,
                marginTop: 40,
                color: '#E5705A',
                fontSize: 17,
              }}>{`No accounts with corresponding assets`}</TextReg>
          )}
          <View style={{alignSelf: 'stretch', height: 240, widht: 300}} />
        </ScrollView>
      )}
      {step == 2 && (
        <ScrollView style={{paddingTop: 30, paddingLeft: 20, paddingRight: 20}}>
          <TextReg style={{fontSize: 16, marginBottom: 14, marginLeft: 10}}>
            Select which asset you would like to transfer.
          </TextReg>
          {walletsList.length > 0 ? (
            walletsList
          ) : (
            <TextReg
              style={{
                fontSize: 20,
                color: '#E5705A',
                alignSelf: 'center',
                marginTop: 20,
              }}>
              No wallets with balance
            </TextReg>
          )}
          <View style={{alignSelf: 'stretch', height: 240, widht: 300}} />
        </ScrollView>
      )}
      {step == 3 && (
        <ScrollView
          style={{
            paddingTop: 30,
            paddingLeft: 20,
            paddingRight: 20,
            postition: 'relative',
          }}>
          <TextReg style={{fontSize: 17, marginBottom: 14, marginLeft: 10}}>
            Where would you like to transfer to?
          </TextReg>
          {accountList}
          {paxGError && (
            <TextReg
              style={{
                textAlign: 'center',
                marginLeft: 30,
                marginRight: 30,
                marginTop: 10,
                color: '#E5705A',
              }}>
              PAXG can not be transferred to a Loan account at this time
            </TextReg>
          )}
          <View style={{alignSelf: 'stretch', height: 240, widht: 300}} />
        </ScrollView>
      )}
      {step == 4 && (
        <View
          style={{
            paddingTop: 30,
            paddingLeft: 20,
            paddingRight: 20,
          }}>
          <TextReg style={{fontSize: 20, marginBottom: 24, marginLeft: 10}}>
            Confirm Transfer
          </TextReg>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginLeft: 10,
              marginRight: 10,
              marginBottom: 20,
              alignItems: 'flex-start',
            }}>
            <TextReg style={{fontSize: 18}}>Transfer From:</TextReg>
            <View style={{flexDirection: 'row'}}>
              <TextReg style={{fontSize: 18, marginRight: 4}}>{''}</TextReg>
              <TextReg style={{fontSize: 18, opacity: 0.6, flexWrap: 'wrap', textAlign: 'right'}}>
                {transferName1}
              </TextReg>
            </View>
          </View>

          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginLeft: 10,
              marginRight: 10,
              marginBottom: 16,
              alignItems: 'flex-start',
            }}>
            <TextReg style={{fontSize: 18}}>Transfer To:</TextReg>
            <View style={{flexDirection: 'row', flex: 1, flexWrap: 'wrap'}}>
              <TextReg style={{fontSize: 18, marginRight: 4}}>{''}</TextReg>
              <TextReg
                style={{fontSize: 18, opacity: 0.6, flex: 1, flexWrap: 'wrap', textAlign: 'right'}}>
                {transferName2}
              </TextReg>
            </View>
          </View>

          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginLeft: 10,
              marginRight: 10,
              marginBottom: 16,
            }}>
            <TextReg style={{fontSize: 18}}>Currency:</TextReg>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              <Image
                source={transferWalletPic}
                style={{
                  height: 24,
                  width: 24,
                  marginRight: 4,
                  opacity: 0.9,
                  marginBottom: -12,
                }}
              />
              <TextReg
                style={{
                  fontSize: 17,
                  marginTop: 14,
                  marginRight: 6,
                }}>
                {`${currency}`}
              </TextReg>
            </View>
          </View>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginLeft: 10,
              marginRight: 10,
              marginBottom: 20,
            }}>
            <TextReg style={{fontSize: 18}}>Amount:</TextReg>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              <TextReg
                style={{
                  fontSize: 18,
                  marginRight: 6,
                }}>{`$${numberWithCommas(fiatInput)}`}</TextReg>
              <TextReg style={{fontSize: 18, opacity: 0.7}}>
                {Number(inputNoComma || 0).toFixed(8)}
              </TextReg>
            </View>
          </View>
          {transferErr && (
            <View
              style={{
                alignSelf: 'stretch',
                marginBottom: -10,
                marginTop: -10,
                alignItems: 'center',
              }}>
              <TextReg
                style={{
                  color: '#E5705A',
                  fontSize: 18,
                  textAlign: 'center',
                }}>
                {transferErr}
              </TextReg>
            </View>
          )}
          <Button
            isLoading={trasnferLoading}
            onPress={() => transferSend()}
            style={{
              height: 60,
              alignSelf: 'stretch',
              backgroundColor: '#00FFBD',
              marginTop: transferErr ? 20 : 40,
              marginLeft: 10,
              marginRight: 10,
              borderRadius: 4,
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 70,
            }}>
            <TextReg
              style={{
                color: '#000',
                fontSize: 20,
                letterSpacing: 2,
              }}>
              CONFIRM
            </TextReg>
          </Button>
        </View>
      )}
      {step == 5 && (
        <View
          style={{
            paddingTop: 30,
            paddingLeft: 20,
            paddingRight: 20,
            alignSelf: 'stretch',
            alignItems: 'center',
          }}>
          <TextReg
            style={{
              fontSize: 20,
              marginBottom: 24,
              marginLeft: 10,
              marginTop: 20,
            }}>
            Successful Transfer
          </TextReg>
          <Image
            source={require('../../imgs/graphics/successImg.png')}
            style={{height: 80, width: 80}}
          />
          <TextReg
            style={{
              marginTop: 30,
              width: 300,
              textAlign: 'center',
              fontSize: 17,
            }}>
            You may view your transfer details in your transaction history of your wallets.
          </TextReg>
        </View>
      )}
    </View>
  )
}

export default FlowTransfer
