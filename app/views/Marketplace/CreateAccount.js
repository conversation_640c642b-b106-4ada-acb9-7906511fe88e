import React, {useState, useEffect, useRef} from 'react'
import {
  Modal,
  View,
  Image,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  TextInput,
  Keyboard,
} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import {useNavigation} from '@react-navigation/native'
import {increaseRefreshDataCount} from '../../store/user/user.actions'
import {isAuthed} from '../../store/auth/auth.actions'

import {TextBold, TextReg, Button} from '../../components'

import styles from './styles'

let CreateAccount = ({close, route}) => {
  let dispatch = useDispatch()
  let navigation = useNavigation()
  let [accountType, setAccountType] = useState('')
  let [accountName, setAccountName] = useState('')
  let [loading, setLoading] = useState(false)
  let [error, setError] = useState(false)
  let user = useSelector(state => state.user.user)
  let account = useSelector(state => state.auth.account)
  let WebService = useSelector(state => state.auth.WebService)

  let leveraged = route?.params?.leveraged
  console.log('leveraged', leveraged)

  useEffect(() => {
    whichAcc()
  }, [])
  let accounts = user?.accounts?.filter(a => a.productType == 'loan')
  let personalAcc = accounts?.filter(a => a.type == 'personal') || []

  let whichAcc = () => {
    let accountType = 'Personal'
    if (personalAcc.length > 0) {
      accountType = 'Business'
    }
    setAccountType(accountType)
    setAccountName('')
    setLoading(false)
  }

  //constructor
  /*
  let accounts = this.props?.user?.accounts
  console.log('accounts', accounts)
  let accountType = 'Personal'
  let personalAcc = accounts.filter(a => a.type == 'personal' && a.productType == 'loan') || []
  if (personalAcc.length > 0) {
    accountType = 'Business'
  }

  this.state = {
    accountType,
    accountName: '',
    newAccountLoading: false,
    typeLeverage: props.typeLeverage || null,
  }
  this.inputs = []
  */

  let createNewAccount = async () => {
    let isTestAcc = user?.primaryEmail == '<EMAIL>'
    if (isTestAcc) {
      navigation.navigate('LoanRequest')
      return
    }
    if (accounts?.length >= 8) {
      setError('Over the maximum number of accounts')
      return
    }
    if (accountType == 'Personal') {
      setLoading(true)
      setError(false)
      WebService.createSaltAccount({
        type: 'personal',
        leveraged: leveraged,
      })
        .then(async res => {
          await WebService.setAccountName(res.data?.accountId, res.data?.ref)
          navigation?.popToTop()
          navigation.navigate('Home')
          dispatch(isAuthed({ref: res.data?.ref, email: user.primaryEmail}))
          dispatch(increaseRefreshDataCount('auto'))
          console.log('eh?')
        })
        .catch(err => {
          const errorText = err.data.body.error
          setError(errorText || 'true')
          setLoading(false)
        })
    }
    if (accountType == 'Business') {
      navigation.navigate('EntitySelectSet', {leveraged})
    }
    /*

    WebService.createSaltAccount({
      type: accountType.toLowerCase(),
      leveraged: false,
    })
      .then(async res => {
        console.log('res', res.data)
        let gotUser = await WebService.getSaltUser()
        let accs = gotUser?.data?.accounts
        let newId = accs?.filter(b => b.createdAt == res.data?.createdAt)[0]?.id
        let setPayload = {
          type: accountType.toLowerCase(),
        }
        console.log('setPayload', setPayload)

        WebService.setAccountName(newId) //setPayload
          .then(res2 => {
            console.log('res2', res2)

            //do after entity select
            dispatch(increaseRefreshDataCount(accountName))
            setLoading(false)

            if (accountType == 'Business') {
              navigation.navigate('EntitySelect', {ref: res.data?.ref})
            } else {
              navigation?.popToTop()
              navigation.navigate('Home')
            }
          })
          .catch(err => {
            console.log('err', err)
          })
      })
      .catch(err => {
        console.log('catch err?', err)
        const errorText = err.data.body.error
        setError(errorText || 'true')
        setLoading(false)
      })
      */
  }

  return (
    <SafeAreaView style={local.box}>
      <View
        style={{
          flexDirection: 'row',
          alignSelf: 'stretch',
          justifyContent: 'space-between',
          marginTop: 5,
          marginBottom: 5,
        }}>
        <TouchableOpacity
          onPress={() => navigation?.goBack()}
          style={{height: 40, width: 40, marginLeft: 20}}>
          <Image
            source={require('../../imgs/backToSettings.png')}
            style={{height: 25, width: 20}}
          />
        </TouchableOpacity>
        <TextReg style={{color: '#FFF', fontSize: 18}}>Marketplace</TextReg>
        <View style={{height: 40, width: 40, marginRight: 20}} />
      </View>
      <View style={styles.accountModalBox}>
        {false && (
          <>
            <TextReg style={styles.accountNameTxt}>Account Name:</TextReg>
            <TextInput
              onChangeText={text => setAccountName(text)}
              onSubmitEditing={() => Keyboard.dismiss()}
              onBlur={() => Keyboard.dismiss()}
              returnKeyType={'next'}
              textContentType="none"
              underlineColorAndroid="transparent"
              value={accountName}
              style={styles.accountInput}
              autoFocus
              keyboardAppearance="dark"
            />
          </>
        )}

        <TextReg style={styles.accountTypeTxt}>Account Type:</TextReg>
        <View
          style={{
            opacity: accountType === 'Business' ? 0.3 : 1,
            alignSelf: 'stretch',
            marginBottom: 10,
            alignItems: 'center',
          }}>
          <TouchableOpacity
            onPress={() => {
              Keyboard.dismiss()
              setAccountType('Personal')
            }}
            activeOpacity={1}
            style={styles.accountPersonalButton}>
            {accountType === 'Personal' ? (
              <Image
                source={require('../../imgs/checkmarkCircle.png')}
                style={styles.checkmarkCircleImg}
              />
            ) : (
              <Image
                source={require('../../imgs/accountPersonal.png')}
                style={styles.accountPersonalImg}
              />
            )}
            <View style={{width: 170}}>
              <TextBold style={{color: '#00FFBD', fontSize: 17, marginBottom: 4}}>
                Personal Loan
              </TextBold>
              <TextReg>For indivduals leveraging their crypto</TextReg>
            </View>
          </TouchableOpacity>
        </View>
        <View
          style={{
            opacity: accountType === 'Personal' ? 0.3 : 1,
            alignSelf: 'stretch',
            marginBottom: 20,
            alignItems: 'center',
          }}>
          <TouchableOpacity
            onPress={() => {
              Keyboard.dismiss()
              setAccountType('Business')
            }}
            activeOpacity={1}
            style={styles.accountBusinessButton}>
            {accountType === 'Business' ? (
              <Image
                source={require('../../imgs/checkmarkCircle.png')}
                style={styles.checkmarkCircleImg}
              />
            ) : (
              <Image
                source={require('../../imgs/accountBusiness.png')}
                style={styles.accountBusinessImg}
              />
            )}
            <View style={{width: 170}}>
              <TextBold style={{color: '#00FFBD', fontSize: 17, marginBottom: 4}}>
                Business Loan
              </TextBold>
              <TextReg style={{width: 140}}>Startups or companies that need growth</TextReg>
            </View>
          </TouchableOpacity>
        </View>

        <View
          style={{
            alignItems: 'center',
            opacity: accountType !== '' ? 1 : 0.3,
            alignSelf: 'stretch',
            paddingLeft: 30,
            paddingRight: 30,
          }}>
          <Button
            isLoading={loading}
            disabled={
              accounts?.length >= 8 || (accountType == 'Personal' && personalAcc.length > 0)
            }
            onPress={() => createNewAccount()}
            style={styles.accountCreateButton}>
            <TextReg style={{color: '#000'}}>CREATE</TextReg>
          </Button>
        </View>
        {accounts?.length >= 8 && (
          <TextReg
            style={{
              color: '#E5705A',
              fontSize: 18,
              marginTop: 10,
              width: 260,
              textAlign: 'center',
            }}>{`Maximum number of accounts`}</TextReg>
        )}
        {accountType == 'Personal' && personalAcc.length > 0 && (
          <TextReg
            style={{
              color: '#E5705A',
              fontSize: 18,
              marginTop: 10,
              width: 260,
              textAlign: 'center',
            }}>{` 1 Personal Account Maximum`}</TextReg>
        )}
      </View>
    </SafeAreaView>
  )
}

export default CreateAccount

let local = {
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
  card: {
    backgroundColor: '#3D3D50',
    alignSelf: 'stretch',
    borderRadius: 10,
    marginLeft: 20,
    marginRight: 20,
    marginBottom: 10,
    paddingTop: 10,
  },
}
