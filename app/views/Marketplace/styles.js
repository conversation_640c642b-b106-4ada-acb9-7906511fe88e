import {StyleSheet, Dimensions, PixelRatio} from 'react-native'

const {width, height} = Dimensions.get('window')

const styles = StyleSheet.create({
  scrollView: {
    display: 'flex',
    flexDirection: 'row',
    overflow: 'hidden',
  },
  //createAccount
  button: {
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    zIndex: 5,
    minWidth: 220,
    height: 50,
    fontSize: 22,
    paddingVertical: 9,
    paddingHorizontal: 21,
  },
  accountModalBox: {
    alignSelf: 'stretch',
    alignItems: 'center',
    paddingBottom: 30,
    backgroundColor: '#3D3D50',
    marginLeft: 20,
    marginRight: 20,
    borderRadius: 20,
  },
  investmentAccountModalBox: {
    alignSelf: 'stretch',
    alignItems: 'left',
    marginLeft: 20,
    marginRight: 20,
  },
  accountNameTxt: {
    marginTop: 14,
    fontSize: 16,
    marginBottom: 4,
    color: '#fff',
  },
  accountInput: {
    fontSize: 19,
    width: 260,
    borderColor: '#FFF',
    borderWidth: 1,
    height: 50,
    borderRadius: 14,
    backgroundColor: '#3D3D50',
    padding: 6,
    paddingLeft: 14,
    fontFamily: 'Europa-Regular',
    color: '#e6e6e6',
    marginTop: 6,
  },
  accountTypeTxt: {
    marginTop: 30,
    fontSize: 16,
    marginBottom: 10,
    color: '#fff',
  },
  accountPersonalButton: {
    width: 260,
    borderRadius: 14,
    height: 90,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    backgroundColor: '#3D3D50',
    borderColor: '#FFF',
  },
  investmentAccountPersonalButton: {
    width: '100%',
    borderRadius: 14,
    height: 92,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'start',
    backgroundColor: '#535364',
  },
  checkmarkCircleImg: {
    height: 36,
    width: 36,
    marginRight: 20,
    marginLeft: 12,
  },
  accountPersonalImg: {
    height: 36,
    width: 36,
    marginRight: 20,
    marginLeft: 12,
  },
  accountBusinessImg: {
    height: 50,
    width: 30,
    marginRight: 22,
    marginLeft: 16,
  },
  investmentAccountPersonalImg: {
    height: 36,
    width: 36,
    marginRight: 26,
    marginLeft: 24,
  },
  investmentAccountBusinessImg: {
    height: 50,
    width: 30,
    marginRight: 38,
    marginLeft: 24,
  },
  accountBusinessButton: {
    width: 260,
    borderRadius: 14,
    height: 90,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    marginTop: 10,
    backgroundColor: '#3D3D50',
    borderColor: '#FFF',
  },
  investmentAccountBusinessButton: {
    width: '100%',
    borderRadius: 12,
    height: 92,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'start',
    backgroundColor: '#535364',
  },
  accountCreateButton: {
    alignSelf: 'stretch',
    height: 50,
    borderRadius: 14,
    backgroundColor: '#00ffc3',
    justifyContent: 'center',
    alignItems: 'center',
  },

  ////switch Accounts accountModa
  switchContainer: {
    flex: 1,
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#********',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 200,
  },
  switchBox: {
    backgroundColor: '#3D3D50',
    borderRadius: 14,
    zIndex: 210,
    padding: 16,
    minWidth: 260,
  },
  switchHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: 260,
    borderBottomWidth: 0.5,
    borderColor: '#f0f0f0',
    paddingBottom: 4,
  },
  switchInput: {
    fontSize: 19,
    width: 240,
    borderColor: '#000',
    borderWidth: 1,
    height: 40,
    borderRadius: 14,
    backgroundColor: '#fff',
    padding: 6,
    paddingLeft: 14,
    fontFamily: 'Europa-Regular',
    color: '#e6e6e6',
    marginTop: 6,
  },
  switchPersonalButton: {
    width: 240,
    borderRadius: 14,
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
  },
  switchBusinessButton: {
    width: 240,
    borderRadius: 14,
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    marginTop: 10,
  },
  switchAccountsTitle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: 260,
    borderBottomWidth: 0.5,
    borderColor: '#f0f0f0',
    paddingBottom: 4,
  },
  switchCreateAccountText: {
    fontSize: 16,
    color: '#00FFBD',
  },
  rowOptionView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 14,
  },
  eyeIcon: {
    height: 15,
    width: 22,
    marginLeft: 8,
    opacity: 0.8,
    marginRight: 10,
  },
  plusIcon: {
    height: 18,
    width: 18,
    marginLeft: 10,
    opacity: 0.7,
    marginRight: 12,
    marginTop: -2,
  },
})

export default styles
