import React, {useState, useEffect} from 'react'
import {
  Modal,
  View,
  Image,
  TouchableOpacity,
  SafeAreaView,
  Keyboard,
  ActivityIndicator,
} from 'react-native'
import {useDispatch, useSelector} from 'react-redux'
import {useNavigation} from '@react-navigation/native'
import {increaseRefreshDataCount, updateAccount} from '../../store/user/user.actions'

import {Button, TextBold, TextReg} from '../../components'

import styles from './styles'
import {isAuthed} from '../../store/auth/auth.actions'

let CreateInvestmentAccount = ({close, route}) => {
  let dispatch = useDispatch()
  let navigation = useNavigation()
  let [accountType, setAccountType] = useState('')
  let [loading, setLoading] = useState(false)
  let [dataLoading, setDataLoading] = useState(false)
  let [error, setError] = useState(false)
  let user = useSelector(state => state.user.user)
  let WebService = useSelector(state => state.auth.WebService)
  let [entitiesAccredited, setEntitiesAccredited] = useState(false)

  useEffect(() => {
    getEntities()
  }, [])

  let getEntities = async () => {
    console.log('123')
    setDataLoading(true)
    try {
      let res = await WebService.getEntitys()
      let arr = res.data?.filter(a => a.isEntityAccredited)
      if (arr?.length >= 1) {
        setEntitiesAccredited(true)
      }
      setDataLoading(false)
    } catch (err) {
      setDataLoading(false)
      console.log('getEntities err ', err)
    }
  }

  let investmentAccounts = user?.accounts?.filter(acc => acc.productType === 'investment')
  let personalAcc = investmentAccounts?.filter(a => a.type == 'personal') || []

  let createNewAccount = async () => {
    if (accountType == 'Personal') {
      setLoading(true)
      setError(false)

      WebService.createSaltAccount({
        type: 'personal',
        leveraged: false,
        productType: 'investment',
      })
        .then(async res => {
          console.log('eh', res.data?.accountId)
          await WebService.setAccountName(res.data?.accountId, res.data?.ref)
          const account = await WebService.getSaltAccount()
          dispatch(updateAccount(account?.data))
          await dispatch(isAuthed({ref: res.data.ref, email: user.primaryEmail}))
          await WebService.updateRef(res.data.ref)
          dispatch(increaseRefreshDataCount('auto'))
          navigation?.popToTop()
          navigation.navigate('CreateInvestment')
        })
        .catch(err => {
          console.log('createSaltAccount err,', err)
          const errorText = err.data.body.error
          setError(errorText || 'true')
          setLoading(false)
        })
    }
    if (accountType === 'Business') {
      navigation.navigate('EntitySelectSet', {leveraged: false, forInvestment: true})
    }
  }

  return (
    <SafeAreaView style={local.box}>
      <View
        style={{
          flexDirection: 'row',
          alignSelf: 'stretch',
          justifyContent: 'space-between',
          marginTop: 5,
          marginBottom: 5,
        }}>
        <TouchableOpacity
          onPress={() => navigation?.goBack()}
          style={{height: 40, width: 40, marginLeft: 20}}>
          <Image
            source={require('../../imgs/backToSettings.png')}
            style={{height: 25, width: 20}}
          />
        </TouchableOpacity>
        <TextReg style={{color: '#FFF', fontSize: 18}}>Open LEND Account</TextReg>
        <View style={{height: 40, width: 40, marginRight: 20}} />
      </View>
      <TextBold style={{fontSize: 24, marginLeft: 20, marginRight: 20}}>
        What type of LEND account do you want to open?
      </TextBold>
      {dataLoading ? (
        <ActivityIndicator size="large" color="#fff" style={{marginVertical: 10}} />
      ) : (
        <>
          {personalAcc?.length < 4 && user?.isUserAccredited && (
            <View style={{...styles.investmentAccountModalBox, marginTop: 37, marginBottom: 12}}>
              <TouchableOpacity
                onPress={() => {
                  Keyboard.dismiss()
                  setAccountType('Personal')
                }}
                activeOpacity={0.4}
                style={{
                  ...styles.investmentAccountPersonalButton,
                  borderColor: accountType === 'Personal' ? '#00FFBD' : undefined,
                  borderWidth: accountType === 'Personal' ? 1 : 0,
                }}>
                <Image
                  source={require('../../imgs/accountPersonal.png')}
                  style={styles.investmentAccountPersonalImg}
                />
                <TextBold style={{fontSize: 24, marginBottom: 4}}>Individual</TextBold>
              </TouchableOpacity>
            </View>
          )}
          <View
            style={{
              ...styles.investmentAccountModalBox,
              marginTop: personalAcc?.length == 4 ? 37 : 20,
              marginBottom: 40,
            }}>
            <TouchableOpacity
              onPress={() => {
                Keyboard.dismiss()
                setAccountType('Business')
              }}
              disabled={!entitiesAccredited}
              activeOpacity={0.4}
              style={{
                ...styles.investmentAccountBusinessButton,
                borderColor: accountType === 'Business' ? '#00FFBD' : undefined,
                borderWidth: accountType === 'Business' ? 1 : 0,
                opacity: entitiesAccredited ? 1 : 0.5,
              }}>
              <Image
                source={require('../../imgs/accountBusiness.png')}
                style={styles.investmentAccountBusinessImg}
              />
              <TextBold style={{fontSize: 24, marginBottom: 4}}>Entity</TextBold>
            </TouchableOpacity>
          </View>
          {personalAcc?.length == 4 && user?.isUserAccredited && !entitiesAccredited && (
            <TextReg
              style={{
                fontSize: 18,
                color: '#E5705A',
                textAlign: 'center',
                paddingBottom: 20,
                marginHorizontal: 20,
              }}>
              You have reached max. number of personal LEND accounts. Get your entity qualified.
            </TextReg>
          )}
          {personalAcc?.length == 4 && user?.isUserAccredited && !entitiesAccredited ? (
            <Button
              style={{alignSelf: 'stretch', marginLeft: 20, marginRight: 20}}
              isLoading={false}
              onPress={() => navigation.navigate('GetAccredited')}>
              <TextReg
                style={{
                  fontSize: 18,
                  letterSpacing: 0.7,
                  color: '#000',
                  alignSelf: 'stretch',
                }}>
                GET QUALIFIED
              </TextReg>
            </Button>
          ) : (
            <Button
              style={{alignSelf: 'stretch', marginLeft: 20, marginRight: 20}}
              disabled={!accountType}
              isLoading={loading}
              onPress={createNewAccount}>
              <TextReg
                style={{
                  fontSize: 18,
                  letterSpacing: 0.7,
                  color: '#000',
                  alignSelf: 'stretch',
                }}>
                CREATE
              </TextReg>
            </Button>
          )}
        </>
      )}
    </SafeAreaView>
  )
}

export default CreateInvestmentAccount

let local = {
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
  card: {
    backgroundColor: '#3D3D50',
    alignSelf: 'stretch',
    borderRadius: 10,
    marginLeft: 20,
    marginRight: 20,
    marginBottom: 10,
    paddingTop: 10,
  },
}
