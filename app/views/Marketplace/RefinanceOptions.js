import React, {useState} from 'react'
import {View, Image, TouchableOpacity, SafeAreaView, Keyboard, Dimensions} from 'react-native'
import {useNavigation} from '@react-navigation/native'
import {TextBold, TextReg, Button} from '../../components'

const {width: screenWidth} = Dimensions.get('window')

const RefinanceOptions = ({route}) => {
  const navigation = useNavigation()
  const [action, setAction] = useState('')

  const leveraged = route?.params?.leveraged

  const next = async () => {
    if (action === 'refinance') {
      navigation.navigate('SelectRefinanceLoan')
    } else navigation.navigate('CreateAccount', {leveraged})
  }

  return (
    <SafeAreaView style={local.box}>
      <View>
        <View
          style={{
            flexDirection: 'row',
            alignSelf: 'stretch',
            justifyContent: 'space-between',
            marginTop: 5,
            marginBottom: 5,
          }}>
          <TouchableOpacity onPress={() => navigation?.goBack()} style={{height: 40, width: 40}}>
            <Image
              source={require('../../imgs/backToSettings.png')}
              style={{height: 25, width: 20}}
            />
          </TouchableOpacity>
          <TextReg style={{color: '#FFF', fontSize: 18}}>Borrow</TextReg>
          <View style={{height: 40, width: 40}} />
        </View>
        <View style={local.descriptionBox}>
          <TextReg style={local.actionTitleTxt}>
            Select which action are you are wanting to do below:
          </TextReg>

          <TouchableOpacity
            onPress={() => {
              Keyboard.dismiss()
              setAction('new')
            }}
            activeOpacity={0.5}
            style={{...local.card, borderColor: action === 'new' ? '#00FFBD' : 'transparent'}}>
            <View style={{position: 'relative'}}>
              <Image source={require('../../imgs/referrals/hand.png')} style={local.handImage} />
              <View style={local.iconContainer}>
                <Image source={require('../../imgs/icons/plusWhite.png')} style={local.iconImage} />
              </View>
            </View>
            <TextBold style={{fontSize: 16}}>Apply for a New Loan</TextBold>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              Keyboard.dismiss()
              setAction('refinance')
            }}
            activeOpacity={0.5}
            style={{
              ...local.card,
              borderColor: action === 'refinance' ? '#00FFBD' : 'transparent',
            }}>
            <View>
              <Image source={require('../../imgs/referrals/hand.png')} style={local.handImage} />
              <View style={local.iconContainer}>
                <Image
                  source={require('../../imgs/homeActivities/repayment.png')}
                  style={local.iconImage}
                />
              </View>
            </View>
            <TextBold style={{fontSize: 16}}>Refinance an Existing Loan</TextBold>
          </TouchableOpacity>
        </View>
      </View>

      <View style={local.buttonContainer}>
        <Button
          isLoading={false}
          disabled={action === ''}
          onPress={() => next()}
          style={local.continueButton}>
          <TextBold style={{color: action === '' ? '#FFF' : '#000', letterSpacing: 2.16}}>
            CONTINUE
          </TextBold>
        </Button>
        <Button isLoading={false} onPress={() => navigation?.goBack()} style={local.backButton}>
          <TextBold style={local.backButtonText}>BACK</TextBold>
        </Button>
      </View>
    </SafeAreaView>
  )
}

export default RefinanceOptions

let local = {
  box: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    backgroundColor: '#28283D',
    alignItems: 'center',
  },
  actionTitleTxt: {
    marginTop: 20,
    fontSize: 18,
    marginBottom: 32,
  },
  descriptionBox: {
    width: screenWidth - 40,
  },
  buttonContainer: {
    justifyContent: 'flex-end',
    alignItems: 'stretch',
    alignSelf: 'stretch',
    paddingLeft: 30,
    paddingRight: 30,
    paddingBottom: 20,
    gap: 12,
  },
  continueButton: {
    width: screenWidth - 40,
    backgroundColor: '#00FFBD',
    height: 60,
    borderRadius: 15,
  },
  backButton: {
    width: screenWidth - 40,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  backButtonText: {color: '#00FFBD', fontSize: 18, letterSpacing: 2.16},
  handImage: {tintColor: '#FFF', height: 48, width: 48},
  iconContainer: {
    position: 'absolute',
    right: -10,
    top: -10,
    backgroundColor: '#00FFBD',
    borderRadius: 14.5,
    width: 29,
    height: 29,
    borderWidth: 3,
    borderColor: '#535364',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  iconImage: {tintColor: '#535364', height: 11, width: 13},
  card: {
    backgroundColor: '#535364',
    borderWidth: 2,
    flexDirection: 'row',
    alignSelf: 'stretch',
    alignItems: 'center',
    gap: 22,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
}
