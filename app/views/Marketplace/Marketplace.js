import React, {Component} from 'react'
import {
  View,
  StatusBar,
  ScrollView,
  RefreshControl,
  Image,
  TouchableOpacity,
  Platform,
  Dimensions,
  Linking,
} from 'react-native'
import {connect} from 'react-redux'
import LinearGradient from 'react-native-linear-gradient'

import {showNotifications} from '../../store/notifications/notifications.actions'
import {askingForPermissions} from '../../store/auth/auth.actions'

import {
  Card,
  TextReg,
  TextBold,
  BackgroundHeader,
  Button,
  AddTokenModal,
  CreateAccountModal,
} from '../../components'
const {width: ScreenWidth} = Dimensions.get('window')
import convert from '../../imgs/graphics/convert.png'
import marketBorrow from '../../imgs/graphics/marketBorrow.png'
import marketLend from '../../imgs/graphics/marketLend.png'
import marketCard from '../../imgs/graphics/marketCard.png'

import saltCC from '../../imgs/graphics/saltCC.png'
import checkmark from '../../imgs/checkmark.png'
import commonStyles from '../../styles/commonStyles'
import styles from './styles'

class Marketplace extends Component {
  constructor(props) {
    super(props)
    this.state = {
      intervals: 3,
      interval: 1,
      createAccountShow: false,
      typeLeverage: false,
      isAccredited: false,
    }
  }

  componentDidMount() {
    this.props.navigation.setParams({
      toggleNotifications: this.toggleNotifications,
    })

    this.getEntities()

    this._unsubscribeFocus = this.props.navigation?.addListener('focus', () => {
      this.getEntities()
    })
  }

  componentWillUnmount() {
    if (this._unsubscribeFocus) {
      this._unsubscribeFocus()
    }
  }

  getEntities = async () => {
    try {
      let res = await this.props.WebService.getEntitys()
      let isAccredited = false
      let arr = res.data?.filter(a => a.isEntityAccredited)
      if (arr?.length >= 1) {
        isAccredited = true
      }
      if (this.props.user?.isUserAccredited) {
        isAccredited = true
      }

      console.log('isAccredited', isAccredited, arr, this.props.user?.isUserAccredited)

      this.setState({isAccredited})
    } catch (err) {
      console.log('getEntities err ', err)
    }
  }

  toggleNotifications = () => {
    this.props.dispatch(showNotifications(true))
  }

  getInterval = offset => offset / ScreenWidth

  setInterval = i => {
    this.setState({interval: i})
  }

  applyFor = (type, leveraged = true) => {
    if (type == 'loan') {
      const allActiveAccountsWithLoans =
        this.props.user?.accounts?.filter(
          a =>
            !a?.refinanceLoan &&
            a.loans?.length > 0 &&
            a.loans[0]?.status === 'active' &&
            !a?.product?.loan?.pendingLoanId &&
            Number(a.loans[0]?.amortizationInfo?.currentBalance) !== 0,
        ) || null
      if (allActiveAccountsWithLoans?.length > 0) {
        this.props.navigation.navigate('RefinanceOptions', {leveraged})
      } else {
        this.props.navigation.navigate('CreateAccount', {leveraged})
      }
    }

    if (type == 'investment') {
      if (!this.state.isAccredited) {
        this.props.navigation.navigate('GetAccredited')
      } else {
        this.props.navigation.navigate('CreateInvestmentAccount')
      }
    }

    if (type == 'card') {
      this.props.dispatch(askingForPermissions(true))
      if (Platform.OS === 'ios') {
        Linking.openURL('https://saltlending.com/credit-card-buy-anything/')
      } else {
        Linking.canOpenURL('https://saltlending.com/credit-card-buy-anything/').then(supported => {
          if (supported) {
            Linking.openURL('https://saltlending.com/credit-card-buy-anything/')
          }
        })
      }
    }
  }

  render() {
    const showPinIntro = this.props.storedPin === '0' || this.props.backToSettings
    const showUnit21 = this.state.pendingLoan && !this.props.pauseUnit21
    let marketplaceCardWidth = ScreenWidth - 40

    let ldNon = this.props.launchDarkly['non-leveraged-loans'] || false

    let {intervals, typeLeverage, isAccredited} = this.state

    let lendTitle = isAccredited ? 'GET STARTED' : 'GET QUALIFIED'

    return (
      <View style={commonStyles.tileContainer}>
        {!showPinIntro && !showUnit21 && (
          <BackgroundHeader
            notification
            title={'Marketplace'}
            toggleNotifications={this.toggleNotifications}
            leftIcon={'homeScreen'}
            navigateSettings={() => this.props.navigation.navigate('Settings')}
          />
        )}
        <StatusBar barStyle="light-content" />
        {this.state.createAccountShow ? (
          <Card marginTop={10}>
            <CreateAccountModal
              navigation={this.props.navigation}
              close={() => this.setState({createAccountShow: false})}
              typeLeverage={typeLeverage}
            />
          </Card>
        ) : (
          <View>
            <View
              style={{
                overflow: 'show',
                alignSelf: 'stretch',
                height: ldNon ? 504 : 484,
                marginTop: 20,
              }}>
              <ScrollView
                horizontal={true}
                contentContainerStyle={{
                  ...styles.scrollView,
                  width: ScreenWidth * intervals,
                }}
                onScroll={data => {
                  this.setInterval(this.getInterval(data.nativeEvent.contentOffset.x) + 1)
                }}
                scrollEventThrottle={200}
                pagingEnabled
                decelerationRate="fast">
                <View style={{borderRadius: 18, overflow: 'hidden'}}>
                  <View style={{alignSelf: 'stretch', alignItems: 'center', zIndex: 50}}>
                    <Image
                      source={marketBorrow}
                      style={{height: 80, width: 80, marginBottom: -70}}
                    />
                  </View>
                  <LinearGradient
                    colors={['#653AD8', '#E23BD0']}
                    style={{
                      margin: 20,
                      height: Platform.OS === 'ios' ? 430 : 434,
                      width: marketplaceCardWidth,
                      borderRadius: 14,
                      paddingTop: 24,
                      justifyContent: 'space-between',
                    }}
                    start={{x: 0, y: 0.5}}
                    end={{x: 1, y: 0.5}}>
                    <View style={{borderRadius: 14, overflow: 'hidden'}}>
                      <View style={{marginLeft: 20, marginTop: 30}}>
                        <TextBold style={{fontSize: 26, marginBottom: 12}}>BORROW</TextBold>
                        <TextReg style={{fontSize: 20, marginBottom: 2}}>
                          Don't sell your crypto; give it a
                        </TextReg>
                        <TextReg style={{fontSize: 20, marginBottom: 16}}>
                          purpose and borrow against it.
                        </TextReg>
                      </View>
                      <View
                        style={{
                          alignSelf: 'stretch',
                          backgroundColor: '#3D3D50',
                          marginLeft: 4,
                          marginRight: 4,
                          height: 257,
                          borderBottomLeftRadius: 14,
                          borderBottomRightRadius: 14,
                          justifyContent: 'space-between',
                        }}>
                        <View
                          style={{
                            alignItems: 'flex-start',
                            alignSelf: 'stretch',
                            marginLeft: 16,
                            marginTop: 40,
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              marginBottom: 10,
                            }}>
                            <Image
                              source={checkmark}
                              style={{height: 14, width: 14, marginRight: 5}}
                            />
                            <TextReg>Competitive Interest Rates</TextReg>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              marginBottom: 10,
                            }}>
                            <Image
                              source={checkmark}
                              style={{height: 14, width: 14, marginRight: 5}}
                            />
                            <TextReg>Funded in 48 hours or less</TextReg>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              marginBottom: 10,
                            }}>
                            <Image
                              source={checkmark}
                              style={{height: 14, width: 14, marginRight: 5}}
                            />
                            <TextReg>No hidden fees</TextReg>
                          </View>
                          <View style={{flexDirection: 'row', alignItems: 'center'}}>
                            <Image
                              source={checkmark}
                              style={{height: 14, width: 14, marginRight: 5}}
                            />
                            <TextReg>Stablecoin or Fiat Payout Options</TextReg>
                          </View>
                        </View>
                        <Button
                          style={{
                            marginTop: 30,
                            alignSelf: 'stretch',
                            margin: 14,
                          }}
                          onPress={() => this.applyFor('loan', true)}>
                          <TextReg style={{fontSize: 18, color: '#000'}}>GET STARTED</TextReg>
                        </Button>
                      </View>
                    </View>
                  </LinearGradient>
                </View>

                <View style={{borderRadius: 18, overflow: 'hidden'}}>
                  <View style={{alignSelf: 'stretch', alignItems: 'center', zIndex: 50}}>
                    <Image source={marketLend} style={{height: 80, width: 80, marginBottom: -70}} />
                  </View>
                  <LinearGradient
                    colors={['#4F47D8', '#6DEDC2']}
                    style={{
                      margin: 20,
                      height: Platform.OS === 'ios' ? 430 : 434,
                      width: marketplaceCardWidth,
                      borderRadius: 14,
                      paddingTop: 24,
                      justifyContent: 'space-between',
                    }}
                    start={{x: 0, y: 0.5}}
                    end={{x: 1, y: 0.5}}>
                    <View style={{borderRadius: 14, overflow: 'hidden'}}>
                      <View style={{marginLeft: 20, marginTop: 30}}>
                        <TextBold style={{fontSize: 26, marginBottom: 12}}>LEND</TextBold>
                        <TextReg style={{fontSize: 20, marginBottom: 2}}>
                          Build wealth by earning fixed
                        </TextReg>
                        <TextReg style={{fontSize: 20, marginBottom: 16}}>
                          returns on idle assets.
                        </TextReg>
                      </View>
                      <View
                        style={{
                          alignSelf: 'stretch',
                          backgroundColor: '#3D3D50',
                          marginLeft: 4,
                          marginRight: 4,
                          height: 257,
                          borderBottomLeftRadius: 14,
                          borderBottomRightRadius: 14,
                          justifyContent: 'space-between',
                        }}>
                        <View
                          style={{
                            alignItems: 'flex-start',
                            alignSelf: 'stretch',
                            marginLeft: 16,
                            marginTop: 40,
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              marginBottom: 10,
                            }}>
                            <Image
                              source={checkmark}
                              style={{height: 14, width: 14, marginRight: 5}}
                            />
                            <TextReg>Annual Percentage Yield</TextReg>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              marginBottom: 10,
                            }}>
                            <Image
                              source={checkmark}
                              style={{height: 14, width: 14, marginRight: 5}}
                            />
                            <TextReg>{`$10,000 Minimum`}</TextReg>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              marginBottom: 10,
                            }}>
                            <Image
                              source={checkmark}
                              style={{height: 14, width: 14, marginRight: 5}}
                            />
                            <TextReg>{`Industry Leading Custody`}</TextReg>
                          </View>
                          <View style={{flexDirection: 'row', alignItems: 'center'}}>
                            <Image
                              source={checkmark}
                              style={{height: 14, width: 14, marginRight: 5}}
                            />
                            <TextReg>{`Cryptocurrency Payouts`}</TextReg>
                          </View>
                        </View>

                        <View style={{alignSelf: 'stretch', alignItems: 'center', marginTop: 30}}>
                          <TextReg
                            style={{
                              color: '#FFF',
                              fontSize: 10,
                            }}>{`*For Accredited Investors Only`}</TextReg>
                        </View>
                        <Button
                          style={{
                            marginTop: 5,
                            alignSelf: 'stretch',
                            margin: 14,
                          }}
                          onPress={() => this.applyFor('investment', true)}>
                          <TextReg style={{fontSize: 18, color: '#000'}}>{lendTitle}</TextReg>
                        </Button>
                      </View>
                    </View>
                  </LinearGradient>
                </View>

                <View style={{borderRadius: 18, overflow: 'hidden'}}>
                  <View style={{alignSelf: 'stretch', alignItems: 'center', zIndex: 50}}>
                    <Image source={marketCard} style={{height: 80, width: 90, marginBottom: -70}} />
                  </View>
                  <LinearGradient
                    colors={['#533FDB', '#6179E4']}
                    style={{
                      margin: 20,
                      height: Platform.OS === 'ios' ? 430 : 434,
                      width: marketplaceCardWidth,
                      borderRadius: 14,
                      paddingTop: 24,
                      justifyContent: 'space-between',
                    }}
                    start={{x: 0, y: 0.5}}
                    end={{x: 1, y: 0.5}}>
                    <View style={{borderRadius: 14, overflow: 'hidden'}}>
                      <View style={{marginLeft: 20, marginTop: 30}}>
                        <TextBold style={{fontSize: 26, marginBottom: 12}}>
                          SALT Credit Card
                        </TextBold>
                        <TextReg style={{fontSize: 20, marginBottom: 2}}>
                          Introducing the first card backed by
                        </TextReg>
                        <TextReg style={{fontSize: 20, marginBottom: 16}}>
                          your crypto not your credit score.
                        </TextReg>
                      </View>
                      <View
                        style={{
                          alignSelf: 'stretch',
                          backgroundColor: '#3D3D50',
                          marginLeft: 4,
                          marginRight: 4,
                          height: Platform.OS === 'ios' ? 257 : 232,
                          borderBottomLeftRadius: 14,
                          borderBottomRightRadius: 14,
                          justifyContent: 'space-between',
                        }}>
                        <View
                          style={{
                            alignItems: 'flex-start',
                            alignSelf: 'stretch',
                            marginLeft: 16,
                            marginTop: 40,
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              marginBottom: 10,
                            }}>
                            <Image
                              source={checkmark}
                              style={{height: 14, width: 14, marginRight: 5}}
                            />
                            <TextReg>Crypto Rewards on Every Purchase</TextReg>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              marginBottom: 10,
                            }}>
                            <Image
                              source={checkmark}
                              style={{height: 14, width: 14, marginRight: 5}}
                            />
                            <TextReg>{`Flexible Credit Limit with Low APRs`}</TextReg>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              marginBottom: 10,
                            }}>
                            <Image
                              source={checkmark}
                              style={{height: 14, width: 14, marginRight: 5}}
                            />
                            <TextReg>{`Downside Protection on Collateral`}</TextReg>
                          </View>
                          <View style={{flexDirection: 'row', alignItems: 'center'}}>
                            <Image
                              source={checkmark}
                              style={{height: 14, width: 14, marginRight: 5}}
                            />
                            <TextReg>{`Instant Credit Access`}</TextReg>
                          </View>
                        </View>

                        <View
                          style={{alignSelf: 'stretch', alignItems: 'center', marginBottom: 30}}>
                          <TextBold style={{fontSize: 18, color: '#00FFBD'}}>COMING SOON</TextBold>
                        </View>
                      </View>
                    </View>
                  </LinearGradient>
                </View>
              </ScrollView>
            </View>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignSelf: 'stretch',
                backgroundColor: '#28283D',
                zIndex: 50,
                marginTop: -10,
                paddingTop: 4,
              }}>
              <View
                style={{
                  height: 10,
                  width: 10,
                  borderRadius: 5,
                  backgroundColor: this.state.interval == 1 ? '#FFF' : '#777',
                  margin: 6,
                }}
              />
              <View
                style={{
                  height: 10,
                  width: 10,
                  borderRadius: 5,
                  backgroundColor: this.state.interval == 2 ? '#FFF' : '#777',
                  margin: 6,
                }}
              />
              <View
                style={{
                  height: 10,
                  width: 10,
                  borderRadius: 5,
                  backgroundColor: this.state.interval == 3 ? '#FFF' : '#777',
                  margin: 6,
                }}
              />
            </View>
          </View>
        )}
      </View>
    )
  }
}

Marketplace.navigationOptions = ({navigation}) => ({
  title: null,
  header: <View style={{height: 40, position: 'absolute'}} />,
})

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  WebService: state.auth.WebService,
  pauseUnit21: state.user.pauseUnit21,
  showPinScreen: state.auth.pinScreen,
  backToSettings: state.user.backToSettings,
  storedPin: state.auth.pin,
  launchDarkly: state.launchDarkly,
  user: state.user.user,
})

export default connect(mapStateToProps)(Marketplace)
