import * as React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import NotificationsScreen from '../views/Notifications/NotificationsScreen';

const HomeStack = createNativeStackNavigator();

function NotificationStack () {
  return (
    <HomeStack.Navigator
      screenOptions={{
        headerShown: false,
        lazy: true
      }}
    >
      <HomeStack.Screen name="Notifications" component={NotificationsScreen} />
    </HomeStack.Navigator>
  );
}

export default NotificationStack;
