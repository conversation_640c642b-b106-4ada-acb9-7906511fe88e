import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { connect } from 'react-redux';

const styles = StyleSheet.create({
  container: {
    height: 18,
    width: 18,
    borderRadius: 9,
    backgroundColor: '#E5705A',
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    right: -6,
    top: -2
  },
  notifText: {
    color: '#FFF',
    paddingLeft: 1,
    fontSize: 14
  },
  notifTextOver9: {
    color: '#FFF',
    paddingLeft: 1,
    fontSize: 11,
    textAlign: 'center'
  }
});

const TabNotif = (props) => (
  <React.Fragment>
    {props.unread > 0 && (
      <View style={styles.container}>
        <Text style={props.unread > 9 ? styles.notifTextOver9 : styles.notifText}>
          {props.unread > 9 ? '9+' : props.unread}
        </Text>
      </View>
    )}
  </React.Fragment>
);

const mapStateToProps = (state) => ({
  unread: state.notifications.unread
});

export default connect(mapStateToProps)(TabNotif);
