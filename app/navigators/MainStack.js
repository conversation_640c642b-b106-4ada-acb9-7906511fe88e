import * as React from 'react'
import {createNativeStackNavigator} from '@react-navigation/native-stack'

import HomeScreen from '../views/Home/HomeScreen'
import LoanRequest from '../views/Home/LoanRequest'
import DepositCollateral from '../views/Home/DepositCollateral'
import LoanPayout from '../views/Home/LoanPayout/LoanPayout'
import BankSelect from '../views/Home/LoanPayout/BankSelect'
import StablecoinSelect from '../views/Home/LoanPayout/StablecoinSelect'
import UpholdSelect from '../views/Home/LoanPayout/UpholdSelect'
import Convert from '../views/Home/Convert/Convert'
import ConfirmConvert from '../views/Home/Convert/ConfirmConvert'
import ConvertSuccess from '../views/Home/Convert/ConvertSuccess'
import RedeemSalt from '../views/Home/RedeemSalt'
import IDVerification from '../views/Home/Unit21/IDVerification'
import IDVerificationPassport from '../views/Home/Unit21/IDVerificationPassport'
import Unit21Bankruptcy from '../views/Home/Unit21/Unit21Bankruptcy'
import ManualCompliance from '../views/Home/Unit21/ManualCompliance'
import Unit21Flow from '../views/Home/Unit21/Unit21Flow'
import Unit21DepositCollateral from '../views/Home/Unit21/Unit21DepositCollateral'
import ConfirmRiskManagement from '../views/Home/Unit21/ConfirmRiskManagement'
import Unit21LoanDocs from '../views/Home/Unit21/LoanDocs'
import Unit21LoanPending from '../views/Home/Unit21/LoanPending'
import Unit21SaltRedeem from '../views/Home/Unit21/Unit21SaltRedeem'
import Beneficiaries from '../views/Home/Unit21/Beneficiaries'
import JurisdictionDocs from '../views/Home/Unit21/JurisdictionDocs'
import MerchantTos from '../views/Home/Unit21/MerchantTos'
import JumioVerification from '../views/Home/Unit21/JumioVerification'
import ExchangePending from '../views/Home/Unit21/ExchangePending'
import L1Flow from '../views/Home/Unit21/L1Flow'
import FlowL1Address from '../views/Home/Unit21/flowL1/FlowL1Address'
import FlowL1DateOfBirth from '../views/Home/Unit21/flowL1/FlowL1DateOfBirth'
import FlowL1PhoneNumber from '../views/Home/Unit21/flowL1/FlowL1PhoneNumber'
import FlowL1Citizen from '../views/Home/Unit21/flowL1/FlowL1Citizen'
import FlowL1Social from '../views/Home/Unit21/flowL1/FlowL1Social'
import FlowL1Funds from '../views/Home/Unit21/flowL1/FlowL1Funds'
import FlowL1Military from '../views/Home/Unit21/flowL1/FlowL1Military'
import FlowL1Statements from '../views/Home/Unit21/flowL1/FlowL1Statements'
import FlowL1Summary from '../views/Home/Unit21/flowL1/FlowL1Summary'
import AllocationSelection from '../views/Home/Unit21/AllocationSelection'
import BusinessVerification1 from '../views/Home/Unit21/BusinessVerification1'
import AwaitingCollateral from '../views/Home/Unit21/AwaitingCollateral'

//bussinessL1
import FlowB1Entity from '../views/Home/Unit21/flowB1/FlowB1Entity'
import FlowB1Entity2 from '../views/Home/Unit21/flowB1/FlowB1Entity2'
import FlowB1Name from '../views/Home/Unit21/flowB1/FlowB1Name'
import FlowB1Formation from '../views/Home/Unit21/flowB1/FlowB1Formation'
import FlowB1Jurisdiction from '../views/Home/Unit21/flowB1/FlowB1Jurisdiction'
import FlowB1Address from '../views/Home/Unit21/flowB1/FlowB1Address'
import FlowB1DocA from '../views/Home/Unit21/flowB1/FlowB1DocA'
import FlowB1DocB from '../views/Home/Unit21/flowB1/FlowB1DocB'
import FlowB1Summary from '../views/Home/Unit21/flowB1/FlowB1Summary'

//settings
import SettingsScreen from '../views/Settings/SettingsScreen'
import ContactSupport from '../views/Settings/Support/ContactSupport'
import TermsOfUse from '../views/Settings/Legal/TermsOfUse'
import PrivacyPolicy from '../views/Settings/Legal/PrivacyPolicy'
import ChangePassword from '../views/Settings/Account/ChangePassword'
import UpdateNotificationSettings from '../views/Settings/Notifications/UpdateNotificationSettings'
import VerifyPhoneNumber from '../views/Settings/Phone/VerifyPhoneNumber'
import PickCountryCode from '../views/Settings/Phone/PickCountryCode'
import PhoneMenu from '../views/Settings/Phone/PhoneMenu'
import ChangeEmail from '../views/Settings/Account/ChangeEmail'
import IdentityVerification from '../views/Settings/Account/IdentityVerification'
import UpholdPrefs from '../views/Settings/Account/UpholdPrefs'
import Referrals from '../views/Settings/Rewards/Referrals'
import ReferralTos from '../views/Settings/Rewards/ReferralTos'
import PlaidLinking from '../views/Settings/Banking/PlaidLinking'
import MarginCallSettings from '../views/Settings/Margin/MarginCallSettings'

import StackwiseRewards from '../views/Settings/Rewards/StackwiseRewards'
import Account from '../views/Settings/Account/Account'
import Banking from '../views/Settings/Banking/Banking'
import TwoFactor from '../views/Settings/Account/TwoFactor'
import PersonalInfo from '../views/Settings/Account/PersonalInfo'
import BusinessEntity from '../views/Settings/Account/BusinessEntity'
import ReferralContract from '../views/Settings/Rewards/ReferralContract'
import AccountDeletion from '../views/Settings/Account/AccountDeletion'
import Entities from '../views/Settings/Entities/Entities'
import EntityDetails from '../views/Settings/Entities/Details'
import EntityDelete from '../views/Settings/Entities/Delete'
import NoDelete from '../views/Settings/Entities/NoDelete'
import EntityQuestions1 from '../views/Settings/Entities/Flow/EntityQuestions1'
import EntityQuestions2 from '../views/Settings/Entities/Flow/EntityQuestions2'
import EntityInfo1 from '../views/Settings/Entities/Flow/EntityInfo1'
import EntityAddress from '../views/Settings/Entities/Flow/EntityAddress'
import EntityDoc1 from '../views/Settings/Entities/Flow/EntityDoc1'
import EntityDoc2 from '../views/Settings/Entities/Flow/EntityDoc2'
import EntitySummary from '../views/Settings/Entities/Flow/EntitySummary'

//loans
import LoanScreen from '../views/Loans/LoanScreen'
import PaymentType from '../views/Loans/PaymentType'
import PayoffOptions from '../views/Loans/PayoffOptions'
import OneTimeOptions from '../views/Loans/OneTimePayment/OneTimeOptions'
import WireTransfer from '../views/Loans/OneTimePayment/WireTransfer'
import StablecoinAmount from '../views/Loans/OneTimePayment/StablecoinAmount'
import BankTransfer from '../views/Loans/OneTimePayment/BankTransfer'
import CollateralPayment from '../views/Loans/PayoffLoan/CollateralPayment'
import BankTransferDate from '../views/Loans/OneTimePayment/BankTransferDate'
import BankTransferPick from '../views/Loans/OneTimePayment/BankTransferPick'
import BankTransferConfirm from '../views/Loans/OneTimePayment/BankTransferConfirm'
import StablecoinPayment from '../views/Loans/OneTimePayment/StablecoinPayment'
import StablecoinChoice from '../views/Loans/OneTimePayment/StablecoinChoice'
import AutoPay from '../views/Loans/AutoPay'
import AutoPaySelect from '../views/Loans/AutoPay/AutoPaySelect'
import AutoPayConfirm from '../views/Loans/AutoPay/AutoPayConfirm'
import AutoPaySuccess from '../views/Loans/AutoPay/AutoPaySuccess'
import AutoPayBank from '../views/Loans/AutoPay/AutoPayBank'
import DeleteAcc from '../views/Home/Shell/Delete/DeleteAcc'
import PaymentInfo from '../views/Loans/OneTimePayment/PaymentInfo'
import CancelPayment from '../views/Loans/OneTimePayment/CancelPayment'
import CancelPaymentSuccess from '../views/Loans/OneTimePayment/CancelPaymentSuccess'
import DeactivateConfirm from '../views/Loans/AutoPay/DeactivateConfirm'
import DeactivateSuccess from '../views/Loans/AutoPay/DeactivateSuccess'

//investment
import LendDashboard from '../views/Investments/LendDashboard'
import InvestmentRequest from '../views/Investments/InvestmentRequest'
import DepositAssets from '../views/Investments/DepositAssets'
import ConfirmDetails from '../views/Investments/ConfirmDetails'
import PendingApproval from '../views/Investments/PendingApproval'
import AwaitingSignatures from '../views/Investments/AwaitingSignatures'
import EarningsHistory from '../views/Investments/EarningsHistory'
import EarningDestinationOption from '../views/Investments/EarningDestinationOption'
import CloseAccount from '../views/Investments/CloseAccount'
import AssetWallet from '../views/Investments/AssetWallet'

//wallets
import CollateralScreen from '../views/Collateral/CollateralScreen'
import CollateralDetail from '../views/Collateral/CollateralDetail'
import DepositScreen from '../views/Collateral/DepositScreen'
import WithdrawConfirm from '../views/Collateral/WithdrawConfirm'
import WithdrawSuccess from '../views/Collateral/WithdrawSuccess'
import TransactionHistory from '../views/Collateral/TransactionHistory'
import PurchaseSalt from '../views/Collateral/PurchaseSalt'
import ManageLtv from '../views/Loans/ManageLtv'

//custody
import CustodyPage from '../views/Custody/CustodyPage'
import GetAccredited from '../views/Home/Shell/GetAccredited'

//refinance
import RefinanceOptions from '../views/Marketplace/RefinanceOptions'
import SelectRefinanceLoan from '../views/Loans/Refinance/SelectRefinanceLoan'
import EditRefinanceAddress from '../views/Loans/Refinance/EditRefinanceAddress'
import RefinanceType from '../views/Loans/Refinance/RefinanceType'
import RepaymentType from '../views/Loans/Refinance/RepaymentType'
import AdditionalAmount from '../views/Loans/Refinance/AdditionalAmount'
import TermsAndApr from '../views/Loans/Refinance/TermsAndApr'
import RefinancePreview from '../views/Loans/Refinance/RefinancePreview'
import RefinanceCollateral from '../views/Loans/Refinance/RefinanceCollateral'
import RefinanceUnderReview from '../views/Loans/Refinance/RefinanceUnderReview'
import RefinanceSignDocs from '../views/Loans/Refinance/RefinanceSignDocs'
import AddressVerification from '../views/Loans/Refinance/AddressVerification'
import BusinessVerification from '../views/Loans/Refinance/BusinessVerification'

const HomeStack = createNativeStackNavigator()

function MainStack() {
  return (
    <HomeStack.Navigator
      screenOptions={{
        headerShown: false,
        lazy: true,
      }}>
      <HomeStack.Screen name="Home" component={HomeScreen} />
      <HomeStack.Screen name="LoanRequest" component={LoanRequest} />
      <HomeStack.Screen name="DepositCollateral" component={DepositCollateral} />
      <HomeStack.Screen name="LoanPayout" component={LoanPayout} />
      <HomeStack.Screen name="BankSelect" component={BankSelect} />
      <HomeStack.Screen name="StablecoinSelect" component={StablecoinSelect} />
      <HomeStack.Screen name="UpholdSelect" component={UpholdSelect} />
      <HomeStack.Screen name="Convert" component={Convert} />
      <HomeStack.Screen name="ConfirmConvert" component={ConfirmConvert} />
      <HomeStack.Screen name="ConvertSuccess" component={ConvertSuccess} />
      <HomeStack.Screen name="RedeemSalt" component={RedeemSalt} />
      <HomeStack.Screen name="IDVerification" component={IDVerification} />
      <HomeStack.Screen name="IDVerificationPassport" component={IDVerificationPassport} />
      <HomeStack.Screen name="Unit21Flow" component={Unit21Flow} />
      <HomeStack.Screen name="Unit21Bankruptcy" component={Unit21Bankruptcy} />
      <HomeStack.Screen name="ManualCompliance" component={ManualCompliance} />
      <HomeStack.Screen name="Unit21DepositCollateral" component={Unit21DepositCollateral} />
      <HomeStack.Screen name="ConfirmRiskManagement" component={ConfirmRiskManagement} />
      <HomeStack.Screen name="Unit21LoanDocs" component={Unit21LoanDocs} />
      <HomeStack.Screen name="Unit21LoanPending" component={Unit21LoanPending} />
      <HomeStack.Screen name="JumioVerification" component={JumioVerification} />
      <HomeStack.Screen name="Unit21SaltRedeem" component={Unit21SaltRedeem} />
      <HomeStack.Screen name="JurisdictionDocs" component={JurisdictionDocs} />
      <HomeStack.Screen name="ExchangePending" component={ExchangePending} />
      <HomeStack.Screen name="MerchantTos" component={MerchantTos} />
      <HomeStack.Screen name="Beneficiaries" component={Beneficiaries} />
      <HomeStack.Screen name="Settings" component={SettingsScreen} />
      <HomeStack.Screen name="Account" component={Account} />
      <HomeStack.Screen name="Contact" component={ContactSupport} />
      <HomeStack.Screen name="Terms" component={TermsOfUse} />
      <HomeStack.Screen name="Privacy" component={PrivacyPolicy} />
      <HomeStack.Screen name="ChangePassword" component={ChangePassword} />
      <HomeStack.Screen name="UpdateNotificationSettings" component={UpdateNotificationSettings} />
      <HomeStack.Screen name="PhoneNumber" component={VerifyPhoneNumber} />
      <HomeStack.Screen name="PickCountryCode" component={PickCountryCode} />
      <HomeStack.Screen name="PhoneMenu" component={PhoneMenu} />
      <HomeStack.Screen name="ChangeEmail" component={ChangeEmail} />
      <HomeStack.Screen name="IdentityVerification" component={IdentityVerification} />
      <HomeStack.Screen name="UpholdPrefs" component={UpholdPrefs} />
      <HomeStack.Screen name="Banking" component={Banking} />
      <HomeStack.Screen name="TwoFactor" component={TwoFactor} />
      <HomeStack.Screen name="Entities" component={Entities} />
      <HomeStack.Screen name="EntityDetails" component={EntityDetails} />
      <HomeStack.Screen name="EntityDelete" component={EntityDelete} />
      <HomeStack.Screen name="NoDelete" component={NoDelete} />
      <HomeStack.Screen name="EntityQuestions1" component={EntityQuestions1} />
      <HomeStack.Screen name="EntityQuestions2" component={EntityQuestions2} />
      <HomeStack.Screen name="EntityInfo1" component={EntityInfo1} />
      <HomeStack.Screen name="EntityAddress" component={EntityAddress} />
      <HomeStack.Screen name="EntityDoc1" component={EntityDoc1} />
      <HomeStack.Screen name="EntityDoc2" component={EntityDoc2} />
      <HomeStack.Screen name="EntitySummary" component={EntitySummary} />
      <HomeStack.Screen name="DeleteAcc" component={DeleteAcc} />
      <HomeStack.Screen name="PaymentInfo" component={PaymentInfo} />
      <HomeStack.Screen name="CancelPayment" component={CancelPayment} />
      <HomeStack.Screen name="CancelPaymentSuccess" component={CancelPaymentSuccess} />
      <HomeStack.Screen name="DeactivateConfirm" component={DeactivateConfirm} />
      <HomeStack.Screen name="DeactivateSuccess" component={DeactivateSuccess} />
      <HomeStack.Screen name="PersonalInfo" component={PersonalInfo} />
      <HomeStack.Screen name="BusinessEntity" component={BusinessEntity} />
      <HomeStack.Screen name="Referrals" component={Referrals} />
      <HomeStack.Screen name="ReferralTos" component={ReferralTos} />

      <HomeStack.Screen name="StackwiseRewards" component={StackwiseRewards} />
      <HomeStack.Screen name="ReferralContract" component={ReferralContract} />
      <HomeStack.Screen name="AccountDeletion" component={AccountDeletion} />
      <HomeStack.Screen name="L1Flow" component={L1Flow} />
      <HomeStack.Screen name="FlowL1Address" component={FlowL1Address} />
      <HomeStack.Screen name="AllocationSelection" component={AllocationSelection} />
      <HomeStack.Screen name="FlowL1DateOfBirth" component={FlowL1DateOfBirth} />
      <HomeStack.Screen name="FlowL1PhoneNumber" component={FlowL1PhoneNumber} />
      <HomeStack.Screen name="FlowL1Citizen" component={FlowL1Citizen} />
      <HomeStack.Screen name="FlowL1Social" component={FlowL1Social} />
      <HomeStack.Screen name="FlowL1Funds" component={FlowL1Funds} />
      <HomeStack.Screen name="FlowL1Military" component={FlowL1Military} />
      <HomeStack.Screen name="FlowL1Statements" component={FlowL1Statements} />
      <HomeStack.Screen name="BusinessVerification1" component={BusinessVerification1} />
      <HomeStack.Screen name="FlowL1Summary" component={FlowL1Summary} />
      <HomeStack.Screen name="AwaitingCollateral" component={AwaitingCollateral} />

      <HomeStack.Screen name="FlowB1Entity" component={FlowB1Entity} />
      <HomeStack.Screen name="FlowB1Entity2" component={FlowB1Entity2} />
      <HomeStack.Screen name="FlowB1Name" component={FlowB1Name} />
      <HomeStack.Screen name="FlowB1Formation" component={FlowB1Formation} />
      <HomeStack.Screen name="FlowB1Jurisdiction" component={FlowB1Jurisdiction} />
      <HomeStack.Screen name="FlowB1Address" component={FlowB1Address} />
      <HomeStack.Screen name="FlowB1DocA" component={FlowB1DocA} />
      <HomeStack.Screen name="FlowB1DocB" component={FlowB1DocB} />
      <HomeStack.Screen name="FlowB1Summary" component={FlowB1Summary} />

      <HomeStack.Screen name="Loans" component={LoanScreen} />
      <HomeStack.Screen name="PaymentType" component={PaymentType} />
      <HomeStack.Screen name="PayoffOptions" component={PayoffOptions} />
      <HomeStack.Screen name="OneTimeOptions" component={OneTimeOptions} />
      <HomeStack.Screen name="WireTransfer" component={WireTransfer} />
      <HomeStack.Screen name="StablecoinAmount" component={StablecoinAmount} />
      <HomeStack.Screen name="BankTransfer" component={BankTransfer} />
      <HomeStack.Screen name="CollateralPayment" component={CollateralPayment} />
      <HomeStack.Screen name="StablecoinPayment" component={StablecoinPayment} />
      <HomeStack.Screen name="StablecoinChoice" component={StablecoinChoice} />
      <HomeStack.Screen name="BankTransferDate" component={BankTransferDate} />
      <HomeStack.Screen name="BankTransferPick" component={BankTransferPick} />
      <HomeStack.Screen name="BankTransferConfirm" component={BankTransferConfirm} />
      <HomeStack.Screen name="PlaidLinking" component={PlaidLinking} />
      <HomeStack.Screen name="MarginCallSettings" component={MarginCallSettings} />

      <HomeStack.Screen name="Collateral" component={CollateralScreen} />
      <HomeStack.Screen name="Detail" component={CollateralDetail} />
      <HomeStack.Screen name="Deposit" component={DepositScreen} />
      <HomeStack.Screen name="WithdrawConfirm" component={WithdrawConfirm} />
      <HomeStack.Screen name="WithdrawSuccess" component={WithdrawSuccess} />
      <HomeStack.Screen name="TransactionHistory" component={TransactionHistory} />
      <HomeStack.Screen name="PurchaseSalt" component={PurchaseSalt} />
      <HomeStack.Screen name="AutoPay" component={AutoPay} />
      <HomeStack.Screen name="AutoPaySelect" component={AutoPaySelect} />
      <HomeStack.Screen name="AutoPayConfirm" component={AutoPayConfirm} />
      <HomeStack.Screen name="AutoPaySuccess" component={AutoPaySuccess} />
      <HomeStack.Screen name="AutoPayBank" component={AutoPayBank} />
      <HomeStack.Screen name="ManageLtv" component={ManageLtv} />

      <HomeStack.Screen name="LendDashboard" component={LendDashboard} />
      <HomeStack.Screen name="CreateInvestment" component={InvestmentRequest} />
      <HomeStack.Screen name="DepositAssets" component={DepositAssets} />
      <HomeStack.Screen name="ConfirmDetails" component={ConfirmDetails} />
      <HomeStack.Screen name="PendingApproval" component={PendingApproval} />
      <HomeStack.Screen name="AwaitingSignatures" component={AwaitingSignatures} />
      <HomeStack.Screen name="EarningsHistory" component={EarningsHistory} />
      <HomeStack.Screen name="EarningDestinationOption" component={EarningDestinationOption} />
      <HomeStack.Screen name="CloseAccount" component={CloseAccount} />
      <HomeStack.Screen name="AssetWallet" component={AssetWallet} />

      <HomeStack.Screen name="CustodyPage" component={CustodyPage} />
      <HomeStack.Screen name="GetAccredited" component={GetAccredited} />

      {/* Refinance */}
      <HomeStack.Screen name="RefinanceOptions" component={RefinanceOptions} />
      <HomeStack.Screen name="SelectRefinanceLoan" component={SelectRefinanceLoan} />
      <HomeStack.Screen name="EditRefinanceAddress" component={EditRefinanceAddress} />
      <HomeStack.Screen name="RefinanceType" component={RefinanceType} />
      <HomeStack.Screen name="RepaymentType" component={RepaymentType} />
      <HomeStack.Screen name="AdditionalAmount" component={AdditionalAmount} />
      <HomeStack.Screen name="TermsAndApr" component={TermsAndApr} />
      <HomeStack.Screen name="RefinancePreview" component={RefinancePreview} />
      <HomeStack.Screen name="AddressVerification" component={AddressVerification} />
      <HomeStack.Screen name="BusinessVerification" component={BusinessVerification} />
      <HomeStack.Screen name="RefinanceCollateral" component={RefinanceCollateral} />
      <HomeStack.Screen name="RefinanceUnderReview" component={RefinanceUnderReview} />
      <HomeStack.Screen name="RefinanceSignDocs" component={RefinanceSignDocs} />
    </HomeStack.Navigator>
  )
}

export default MainStack
