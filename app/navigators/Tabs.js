import React, {Component} from 'react'
import {connect} from 'react-redux'
import {View, Text, Image, Dimensions} from 'react-native'
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs'
import {StackActions} from '@react-navigation/native'

import {createNativeStackNavigator} from '@react-navigation/native-stack'

import DeviceInfo from 'react-native-device-info'

import MainStack from './MainStack'
import MarketplaceStack from './MarketplaceStack'

import {updateUnread} from '../store/notifications/notifications.actions'
import {updateTab} from '../store/user/user.actions'
import {TextReg} from '../components'
import {derivedStatusMap} from '../util/enumerables'

import commonStyles from '../styles/commonStyles'

class Tabs extends Component {
  constructor(props) {
    super(props)
    this.navigator = null
    this.marketplaceNavigatorRef = React.createRef()
  }
  componentDidMount() {
    this.props.WebService.getNotifications(1)
      .then(res => {
        const unread = res.data.unread
        this.props.dispatch(updateUnread(unread))
      })
      .catch(err => {
        console.log('getNotifications err', err)
      })
    this.props.onRef(this)
  }

  shouldComponentUpdate(nextProps, nextState) {
    if (this.props.pauseUnit21 !== nextProps.pauseUnit21) {
      return true
    }
    if (this.props.refreshCount !== nextProps.refreshCount) {
      return true
    }
    if (this.props.storedPin !== nextProps.storedPin) {
      return true
    }
    if (this.props.backToSettings !== nextProps.backToSettings) {
      return true
    }
    return false
  }

  componentDidUpdate(prevProps) {
    if (this.props.refreshCount !== prevProps.refreshCount) {
      this.props.WebService.getNotifications(1)
        .then(res => {
          const unread = res.data.unread
          this.props.dispatch(updateUnread(unread))
        })
        .catch(err => {
          console.log('getNotifications err', err)
        })
    }
    if (this.props.accountRef !== prevProps.accountRef) {
      console.log('did update - account ref', this.props.accountRef)
    }
  }

  componentWillUnmount() {
    this.props.onRef(undefined)
  }

  changed = tab => {
    this.props.dispatch(updateTab(tab))
  }

  render() {
    const tabMarginBottom = this.props.storedPin == '0' || this.props.backToSettings ? -200 : 0
    const Tab = createBottomTabNavigator()

    let testAcc = this.props.user?.primaryEmail == '<EMAIL>'
    console.log('testAcc tabs', testAcc, this.props.user)
    let homeText = testAcc ? '' : 'Home'
    let marketText = testAcc ? '' : 'Marketplace'

    console.log('homeText', homeText)
    console.log('marketText', marketText)

    let screenWidth = Dimensions.get('window').width
    console.log('screenWidth', screenWidth)
    if (screenWidth > 700) {
      homeText = ''
      marketText = ''
    }

    return (
      <Tab.Navigator
        backBehavior={'history'}
        screenOptions={{
          headerShown: false,
          lazy: false,
          tabBarStyle: {
            backgroundColor: '#28283D',
            paddingLeft: 10,
            paddingRight: 10,
            marginBottom: tabMarginBottom,
          },
          tabBarActiveTintColor: '#00FFBD',
          tabBarInactiveTintColor: '#a3a5a8',
          tabBarLabelStyle: {
            fontFamily: 'Europa-Regular',
          },
          tabBarHideOnKeyboard: true,
          keyboardHidesTabBar: true,
        }}>
        <Tab.Screen
          name="HomeTab"
          component={MainStack}
          options={({navigation, route}) => {
            const tabImg = require('../imgs/icons/dark/home.png')
            const tabActive = require('../imgs/icons/dark/home-active.png')
            return {
              tabBarIcon: ({focused}) => (
                <Image
                  source={focused ? tabActive : tabImg}
                  style={{height: 22, width: 22, marginLeft: -30}}
                />
              ),
              tabBarLabel: ({focused, tintColor}) =>
                focused ? (
                  <TextReg style={commonStyles.tabTextLeft}>{homeText}</TextReg>
                ) : (
                  <TextReg style={commonStyles.tabTextLeft} />
                ),
            }
          }}
          listeners={{
            focus: () => {
              this.changed('Home')
            },
          }}
        />

        <Tab.Screen
          name="MarketplaceTab"
          component={MarketplaceStack}
          options={({navigation, route}) => {
            const tabImg = require('../imgs/icons/dark/fbmarket.png')
            const tabActive = require('../imgs/icons/dark/fbmarket-Active.png')
            return {
              tabBarIcon: ({focused}) => (
                <Image
                  source={focused ? tabActive : tabImg}
                  style={{
                    height: 29,
                    width: 29,
                    marginRight: -30,
                  }}
                />
              ),
              tabBarLabel: ({focused, tintColor}) =>
                focused ? (
                  <TextReg style={commonStyles.tabTextRight}>{marketText}</TextReg>
                ) : (
                  <TextReg style={commonStyles.tabTextRight} />
                ),
            }
          }}
          listeners={({navigation, route}) => ({
            blur: () => {
              if (route.state && route.state.index > 0) {
                navigation.dispatch(StackActions.popToTop())
              }
            },
            focus: () => {
              this.changed('Marketplace')
            },
          })}></Tab.Screen>
      </Tab.Navigator>
    )
  }
}

const mapStateToProps = state => ({
  loanData: state.user.loanData || {},
  WebService: state.auth.WebService,
  refreshCount: state.user.refreshCount,
  accountRef: state.auth.account.ref,
  backToSettings: state.user.backToSettings,
  storedPin: state.auth.pin,
  user: state.user.user,
})

export default connect(mapStateToProps)(Tabs)
