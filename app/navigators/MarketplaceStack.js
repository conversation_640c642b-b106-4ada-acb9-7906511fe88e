import * as React from 'react'
import {createNativeStackNavigator} from '@react-navigation/native-stack'

import Marketplace from '../views/Marketplace/Marketplace'
import CreateAccount from '../views/Marketplace/CreateAccount'
import CreateInvestmentAccount from '../views/Marketplace/CreateInvestmentAccount'
import EntitySelectSet from '../views/Settings/EntitiesSet/EntitySelectSet'

//dupe
import EntitiesSet from '../views/Settings/EntitiesSet/Entities'
import EntityDetailsSet from '../views/Settings/EntitiesSet/Details'
import EntityDeleteSet from '../views/Settings/EntitiesSet/Delete'
import NoDeleteSet from '../views/Settings/EntitiesSet/NoDelete'
import EntityQuestions1Set from '../views/Settings/EntitiesSet/Flow/EntityQuestions1'
import EntityQuestions2Set from '../views/Settings/EntitiesSet/Flow/EntityQuestions2'
import EntityInfo1Set from '../views/Settings/EntitiesSet/Flow/EntityInfo1'
import EntityAddressSet from '../views/Settings/EntitiesSet/Flow/EntityAddress'
import EntityDoc1Set from '../views/Settings/EntitiesSet/Flow/EntityDoc1'
import EntityDoc2Set from '../views/Settings/EntitiesSet/Flow/EntityDoc2'
import EntitySummarySet from '../views/Settings/EntitiesSet/Flow/EntitySummary'
import InvestmentRequest from '../views/Investments/InvestmentRequest'
import Proof from '../views/Investments/Proof'
import Attestation1 from '../views/Investments/Attestation1'
import Attestation2 from '../views/Investments/Attestation2'
import AttestationLetter from '../views/Investments/AttestationLetter'
import AttestationForm from '../views/Investments/AttestationForm'

//import SettingsScreen from '../views/Settings/SettingsScreen'

const MarketStack = createNativeStackNavigator()

function MarketplaceStack() {
  return (
    <MarketStack.Navigator
      screenOptions={{
        headerShown: false,
        lazy: true,
      }}>
      <MarketStack.Screen name="Marketplace" component={Marketplace} />
      <MarketStack.Screen name="CreateAccount" component={CreateAccount} />
      <MarketStack.Screen name="CreateInvestmentAccount" component={CreateInvestmentAccount} />
      <MarketStack.Screen name="EntitySelectSet" component={EntitySelectSet} />

      <MarketStack.Screen name="EntitiesSet" component={EntitiesSet} />
      <MarketStack.Screen name="EntityDetailsSet" component={EntityDetailsSet} />
      <MarketStack.Screen name="EntityDeleteSet" component={EntityDeleteSet} />
      <MarketStack.Screen name="NoDeleteSet" component={NoDeleteSet} />
      <MarketStack.Screen name="EntityQuestions1Set" component={EntityQuestions1Set} />
      <MarketStack.Screen name="EntityQuestions2Set" component={EntityQuestions2Set} />
      <MarketStack.Screen name="EntityInfo1Set" component={EntityInfo1Set} />
      <MarketStack.Screen name="EntityAddressSet" component={EntityAddressSet} />
      <MarketStack.Screen name="EntityDoc1Set" component={EntityDoc1Set} />
      <MarketStack.Screen name="EntityDoc2Set" component={EntityDoc2Set} />
      <MarketStack.Screen name="EntitySummarySet" component={EntitySummarySet} />

      <MarketStack.Screen name="Proof" component={Proof} />
      <MarketStack.Screen name="Attestation1" component={Attestation1} />
      <MarketStack.Screen name="Attestation2" component={Attestation2} />
      <MarketStack.Screen name="AttestationLetter" component={AttestationLetter} />
      <MarketStack.Screen name="AttestationForm" component={AttestationForm} />
    </MarketStack.Navigator>
  )
}

export default MarketplaceStack
