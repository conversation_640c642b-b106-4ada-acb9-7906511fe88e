import * as React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import CollateralDetail from '../views/Collateral/CollateralDetail';

const HomeStack = createNativeStackNavigator();

function SaltStack () {
  return (
    <HomeStack.Navigator
      screenOptions={{
        headerShown: false,
        lazy: true
      }}
    >
      <HomeStack.Screen name="Salt" component={CollateralDetail} initialParams={{ title: 'SALT' }} />
    </HomeStack.Navigator>
  );
}

export default SaltStack;
