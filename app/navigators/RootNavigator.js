import React, {Component} from 'react'
import {AppState, View, Text, StatusBar, Linking, Platform, PermissionsAndroid} from 'react-native'
import {connect} from 'react-redux'

import AsyncStorage from '@react-native-async-storage/async-storage'
import LDClient, * as ld from 'launchdarkly-react-native-client-sdk'

import remoteConfig from '@react-native-firebase/remote-config'
import firebase from '@react-native-firebase/app'
import messaging from '@react-native-firebase/messaging'
import analytics from '@react-native-firebase/analytics'

import DeviceInfo from 'react-native-device-info'
import {Analytics} from 'react-native-google-analytics'
//import { IDFA } from 'react-native-idfa';
import * as Keychain from 'react-native-keychain'
//import AppLink from 'react-native-app-link';
import packageJson from '../../package.json'
import Tabs from './Tabs'
import AuthStack from './AuthStack'
import LoadingScreen from '../components/Screens/LoadingScreen'
import UpdateAppScreen from '../components/Screens/UpdateAppScreen'
import Maintenence from '../components/Screens/Maintenence'
import Pin from '../views/Auth/Pin'
import TransferSlide from '../views/Transfer/TransferSlide'
import NotificationsIntro from '../views/Loans/NotificationsIntro'
import NotifService from '../services/Notifications/NotifService'
import Agreements from '../components/Screens/Agreements'
import UpdateIntro from '../components/Modal/UpdateIntro'
import VersionCheck from '../components/Modal/VersionCheck'

import {
  HomeTour,
  ConvertTour,
  HealthyTour,
  MarginTour,
  StabilizationTour,
  WarningTour,
} from '../components'

//
import axios from 'axios'
import PushNotificationIOS from '@react-native-community/push-notification-ios'

import Heap from '@heap/react-native-heap'

import commonStyles from '../styles/commonStyles'
import config from '../config.json'

import {
  askingForPermissions,
  isAuthed,
  updateFCMToken,
  updatePin,
  showPinScreen,
  unAuth,
  updateDeviceId,
  showUpdateVersionScreen,
  localLogout,
} from '../store/auth/auth.actions'
import {
  updatePushNotifPermission,
  refreshExternalWallets,
  goTwoFactor,
} from '../store/user/user.actions'
import {setFlags, setClient} from '../store/launchDarkly/launchDarkly.actions'
import {updateGA, updateIDFA} from '../store/analytics/analytics.actions'
import {updateUnread} from '../store/notifications/notifications.actions'
import {Background, Notifications, Toast} from '../components'
import {compareVersionNumbers} from '../util/helpers'

class RootNavigator extends Component {
  constructor(props) {
    super(props)
    this.state = {
      appState: AppState.currentState,
      lastActiveTimeStamp: '0',
      token: null,
      userEmail: null,
      userRef: null,
      notificationsModalVisable: false,
      showUpdateError: false,
      savedPushPermission: null,
      goTo: null,
      maintenance: false,
      showVerUpdate: false,
    }
    this.getStorage()
    if (Text.defaultProps == null) Text.defaultProps = {}
    Text.defaultProps.allowFontScaling = false
    this.tabsRef = null
    this.notifService = new NotifService(this.onRegister.bind(this), this.onNotif.bind(this))
    this.ldClient = null
  }

  componentDidMount() {
    this.checkPushNotifPermission()

    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange)
    this.setVersionHeader()
    this.setUpAnalytics()
    this.getPin()
    this.setUpFirebaseAnalytics()
    this.initLaunchDarkly()
    this.lookVersion()

    Linking.getInitialURL().then(url => {
      //console.log('null on debug', url)
    })
    Linking.addEventListener('url', this.handleOpenURL)
    //this.setHeap()
  }

  shouldComponentUpdate(nextProps, nextState) {
    if (
      this.props.showSplash !== nextProps.showSplash ||
      this.props.authed !== nextProps.authed ||
      this.props.showPinScreen !== nextProps.showPinScreen ||
      this.state.notificationsModalVisable !== nextState.notificationsModalVisable ||
      this.props.pushNotifPermissions !== nextState.pushNotifPermissions
    ) {
      return true
    }
    return false
  }

  componentDidUpdate(prevProps) {
    if (this.props.authed && !this.props.showPinScreen && prevProps.showPinScreen) {
      this.checkFirebaseToken()
      this.checkNotifs()
    }

    if (this.props.pushNotifPermissions === 'show' && prevProps.pushNotifPermissions !== 'show') {
      console.log('eh1')
      this.setState({notificationsModalVisable: true})
    }
  }

  componentWillUnmount() {
    this.appStateSubscription.remove()
    //Linking.removeEventListener('url', this.handleOpenURL);
    //this.shutdownDarkly();
  }

  lookVersion = async () => {
    let v = packageJson.version
    const vlast = await AsyncStorage.getItem(`VLAST`)

    if (vlast != v) {
      this.setState({showVerUpdate: true})
    }

    this.props.WebService.versionInfo()
      .then(res => {
        console.log('res versionInfo', res.data?.latestVersion)
        this.setState({
          latestVersion: res.data?.latestVersion,
          mandatoryVersion: res.data?.mandatoryVersion,
        })
      })
      .catch(err => {
        console.log('err versionInfo', err)
      })
  }

  versionLogic = check => {
    if (!check) return false

    const partsA = check.split('.').map(Number)
    const partsB = packageJson.version?.split('.').map(Number)

    for (let i = 0; i < Math.max(partsA.length, partsB.length); i++) {
      const partA = partsA[i] || 0
      const partB = partsB[i] || 0

      if (partA > partB) return true
      if (partA < partB) return false
    }

    return false
  }

  getHigherSemver = (a, b) => {
    if (!a) return b
    if (!b) return a

    const partsA = a?.split('.').map(Number)
    const partsB = b?.split('.').map(Number)

    for (let i = 0; i < Math.max(partsA.length, partsB.length); i++) {
      const partA = partsA[i] || 0
      const partB = partsB[i] || 0

      if (partA > partB) return a
      if (partA < partB) return b
    }

    return a
  }

  setHeap = () => {
    let heapAppId = '3141895931' // prod
    if (config.env == 'staging') {
      heapAppId = '2888291685'
    }
    Heap.setAppId(heapAppId)
  }

  onRegister = token => {
    let data = {registerToken: token.token, fcmRegistered: true}
  }

  onNotif = notif => {
    //console.log('onNotif', notif.title, notif.message)
  }

  handleOpenURL = event => {
    if (event.url.includes('zabo')) {
      const decodedUrl = decodeURIComponent(event.url)
      const splitID = decodedUrl.split(`"`)[3]
      const splitToken = decodedUrl.split(`"`)[7]
      this.props.WebService.zaboConnect(splitID, splitToken)
        .then(res => {
          console.log('zaboConnect res', res)
          // go back to wallets tab & refresh external wallets
          this.props.dispatch(refreshExternalWallets())
        })
        .catch(err => {
          console.log('zabo err', err)
        })
    }
  }

  checkNotifs = () => {
    this.props.WebService.getNotifications(1).then(res => {
      const unread = res.data.unread
      this.props.dispatch(updateUnread(unread))
    })
  }

  checkPushNotifPermission = async () => {
    if (Platform.OS != 'ios') {
      //causes runtime crash 'permission is null'
      //PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS?.POST_NOTIFICATION) // after need to rename to POST_NOTIFICATION - just a rn 0.70.5 bug
    }

    this.notifService.checkPermission(res => {
      if (res.alert) {
        this.props.dispatch(updatePushNotifPermission('accepted'))
        return
      }
      this.toShowPushNotifIntro()
    })
  }

  toShowPushNotifIntro = async () => {
    const savedPushPermission = await AsyncStorage.getItem('SAVED_PUSH_PERMISSION')
    if (savedPushPermission === null) {
      // first time opening app
      this.showPushNotifScreen()
    } else if (savedPushPermission === 'later') {
      this.props.dispatch(updatePushNotifPermission('later'))
      this.setState({savedPushPermission: 'later'})
      // check if has been 1 week
      const timestamp = new Date().getTime().toString()
      const savedPushPermissionTime = await AsyncStorage.getItem('SAVED_PUSH_PERMISSION_TIME')
      if (timestamp > Number(savedPushPermissionTime) + 1000 * 60 * 60 * 24 * 7) {
        this.showPushNotifScreen()
      }
    } else if (savedPushPermission === 'denied') {
      // save to redux so that settings push preferences can know - and not turn on & show modal
      this.props.dispatch(updatePushNotifPermission('denied'))
    }
    // if savedPushPermission === 'accepted' || savedPushPermission === 'never' , do nothing
  }

  showPushNotifScreen = async () => {
    this.setState({notificationsModalVisable: true})
  }

  requestPushNotifsPermission = async () => {
    this.props.dispatch(askingForPermissions(true))

    const authStatus = await messaging().requestPermission()
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL

    if (enabled) {
      this.props.dispatch(askingForPermissions(false))
      this.props.dispatch(updatePushNotifPermission('accepted'))
      AsyncStorage.setItem('SAVED_PUSH_PERMISSION', 'accepted')

      //save fcm
      this.checkFirebaseToken()

      this.setState({notificationsModalVisable: false})

      let refArr = this.props.user?.accounts?.map(a => a.ref)

      for (const ref of refArr) {
        try {
          const res = await this.props.WebService.getNotificationPreferences(ref)
          const prefs = res.data.preferences
          for (const key in prefs) {
            prefs[key].push.value = true
          }
          const updateData = {
            preferenceId: res.data.id,
            preferences: prefs,
          }
          let updateRes = await this.props.WebService.updateNotificationPreferences(updateData)
        } catch {
          this.props.dispatch(askingForPermissions(false))
          this.setState({notificationsModalVisable: false})
        }
      }
    } else {
      //close prompt
      this.setState({notificationsModalVisable: false})
    }
  }

  notificationsRequestLater = () => {
    this.props.dispatch(updatePushNotifPermission('later'))
    AsyncStorage.setItem('SAVED_PUSH_PERMISSION', 'later')
    const timestamp = new Date().getTime().toString()
    AsyncStorage.setItem('SAVED_PUSH_PERMISSION_TIME', timestamp)
    this.setState({notificationsModalVisable: false})
  }

  notificationsRequestNever = () => {
    this.props.dispatch(updatePushNotifPermission('never'))
    AsyncStorage.setItem('SAVED_PUSH_PERMISSION', 'never')
    this.setState({notificationsModalVisable: false})
  }

  getStorage = async () => {
    const lastUseThreeHours = await this.checkLastUse()
    const userEmail = (await AsyncStorage.getItem('LOGIN_EMAIL')) || ''
    const lowerCaseEmail = userEmail.toLowerCase()

    AsyncStorage.multiGet(['LOGIN_EMAIL', `REF-${lowerCaseEmail}`, 'DEVICEID'])
      .then(async res => {
        let userEmail = res[0][1]
        const userRef = res[1][1]
        const deviceId = res[2][1]
        userEmail = userEmail.toLowerCase()
        this.setState({userEmail}, () => {
          this.checkLaunchDarkly()
        })
        const {password: token} = await Keychain.getInternetCredentials('token')
        let {password: pin} = await Keychain.getInternetCredentials(`pin-${userEmail}`)

        if (!pin) {
          Keychain.setInternetCredentials(`pin-${userEmail}`, 'user', '0', {
            securityLevel: Keychain.SECURITY_LEVEL.ANY,
            storage: Keychain.STORAGE_TYPE.AES,
          })
          pin = '0'
        }

        this.props.dispatch(updatePin(pin))

        if (deviceId) {
          this.props.dispatch(updateDeviceId(deviceId))
        }

        /*
        if (userEmail) {
          const lowerEmail = userEmail.toLowerCase();
          const lastRef = await AsyncStorage.getItem(`REF-${lowerEmail}`);

          if (lastRef && lastUseThreeHours) {
            this.props.WebService.updateRef(lastRef);
          }
        }
        */

        if (token && userEmail) {
          const ref = this.props.accountRef || '0' //lastUseThreeHours ? userRef : '0';
          this.setState({token, userEmail, userRef: ref})
          this.props.WebService.updateSID(token)
          this.props.dispatch(isAuthed({ref, email: userEmail}))
          this.props.dispatch(showPinScreen(true))
        } else {
          //this.props.dispatch(hideSplash(false))
          this.props.dispatch(unAuth())
        }
      })
      .catch(err => {
        this.props.dispatch(unAuth())
        //probably send a bug report for no multiget res
      })
  }

  async getPin() {
    const {userEmail} = this.state
    const {password: pin} = await Keychain.getInternetCredentials(`pin-${userEmail}`)
    if (!pin) {
      this.props.dispatch(updatePin('0'))
    } else {
      this.props.dispatch(updatePin(pin))
    }
  }

  handleAppStateChange = async nextAppState => {
    if (!this.props.askingPermissions) {
      if (
        // background to forground
        this.state.appState.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        this.setState({appState: nextAppState}, () => {
          this.getStorage()
        })
      } else if (
        // foreground to background
        this.state.appState === 'active' &&
        (nextAppState === 'inactive' || nextAppState === 'background')
      ) {
        this.setState({appState: nextAppState}, () => {
          this.props.dispatch(showPinScreen(true))
          const timestamp = new Date().getTime().toString()
          AsyncStorage.setItem('LAST_TIME_USE', timestamp)
        })
      }
    } else {
      this.props.dispatch(askingForPermissions(false))
    }
  }

  checkLastUse = async () => {
    const nowTimestamp = new Date().getTime().toString()
    const lastUseTimestamp = await AsyncStorage.getItem('LAST_TIME_USE')
    return new Promise((resolve, reject) => {
      const timeDifference = nowTimestamp - lastUseTimestamp
      if (timeDifference / (1000 * 60 * 60 * 3)) {
        // if 3 hours since last use
        resolve(true)
      }
      resolve(false)
    })
  }

  setUpFirebaseAnalytics = () => {
    analytics().setAnalyticsCollectionEnabled(true)
  }

  checkFirebaseToken = async () => {
    await messaging().registerDeviceForRemoteMessages()
    const fcmToken = await messaging().getToken()

    if (fcmToken && fcmToken !== this.props.fcmToken) {
      this.props.WebService.updateFCMTokenAPI(fcmToken)
        .then(res => {
          this.props.dispatch(updateFCMToken(fcmToken))
        })
        .catch(err => {
          console.log('updateFCMTokenAPI err', err)
        })
    }
  }

  setUpAnalytics = () => {
    let clientId = DeviceInfo.getUniqueId()?._z || '000'
    const ga = new Analytics('UA-125250192-1', clientId, 1, DeviceInfo.getUserAgent())
    this.props.dispatch(updateGA(ga))
    analytics().setUserId(clientId)
  }

  setVersionHeader = () => {
    const callDispatch = this.props.dispatch
    this.props.WebService.initInterceptors(callDispatch, showUpdateVersionScreen, localLogout)
    this.props.WebService.setVersionHeader(packageJson.version)
  }

  handleUpdatePress = () => {
    this.setState({showUpdateError: false})

    //handle ios vs android

    //ios
    /*
	  AppLink.openInStore({
	    appName: 'SALT App',
	    appStoreId: '1383851676',
	    playStoreId: 'com.saltlending.mobile'
	  }).catch((err) => {
	    this.setState({ showUpdateError: true });
	  });
    */
  }

  openStore = async () => {
    const appId = Platform.OS === 'ios' ? '1383851676' : 'com.saltlending.mobile'
    const url = Platform.select({
      ios: `itms-apps://itunes.apple.com/app/${appId}`, //`https://apps.apple.com/app/id${appId}`,
      android: `market://details?id=${appId}`,
    })

    const supported = await Linking.canOpenURL(url)

    if (supported) {
      await Linking.openURL(url)
    } else {
      console.log("Don't know how to open this URL: " + url)
      // You might want to show an error message to the user here
    }
  }

  updateGoTo = () => {
    this.props.dispatch(goTwoFactor())
  }

  listener = async value => {
    //get flag?
    const jsonResult = await this.ldClient.jsonVariation(value, {})
  }

  initLaunchDarkly = async () => {
    try {
      this.ldClient = new LDClient()
      const darkleyConfig = {
        mobileKey: config.launchDarkly.mobileKey,
        enableAutoEnvAttributes: true,
      }
      //const user = {key: '', email: ''};
      let context = {key: '111', kind: 'user'}

      await this.ldClient.configure(darkleyConfig, context, 5)
    } catch (err) {
      //console.log('ld err', err)
    }

    /*
    let allFlagsResult = this.ldClient.allFlags()
    allFlagsResult.then(values => {
      console.log('eh', values)
    })
    */

    //this.ldClient.registerFeatureFlagListener('test1', this.listener)
    this.props.dispatch(setClient(this.ldClient))
    this.checkLaunchDarkly()
  }

  checkLaunchDarkly = async () => {
    //this.ldClient.flush();
    await this.ldClient.identify({key: '', email: '', user: ''})
    const allFlagsResult = this.ldClient.allFlags()

    allFlagsResult.then(values => {
      this.props.dispatch(setFlags(values))
    })
  }

  contVer = async () => {
    let v = packageJson.version
    await AsyncStorage.setItem(`VLAST`, v)
    this.setState({showVerUpdate: false})
  }

  render() {
    const showPinIntro = this.props.storedPin === '0' || this.props.backToSettings
    const statusHeight = StatusBar.currentHeight
    let showScreen = <LoadingScreen />

    let notifIntro = this.state.notificationsModalVisable && this.props.storedPin != '0'

    if (!this.props.showSplash) {
      if (this.props.authed) {
        showScreen = (
          <View style={[commonStyles.tabContainer, {backgroundColor: '#28283d'}]}>
            <Tabs onRef={ref => (this.tabsRef = ref)} />
            <Toast goTo={this.updateGoTo} />

            <TransferSlide />
            <Notifications />
            {!showPinIntro && !this.props.showPinScreen && <Agreements />}

            {this.props.showPinScreen && this.props.pin !== '0' && this.props.authed && <Pin />}
            {this.props.showPinScreen && (
              <View style={{zIndex: 99}}>
                <Background />
              </View>
            )}
            {false && <UpdateIntro />}
            {notifIntro && (
              <NotificationsIntro
                notificationsModalVisable={this.state.notificationsModalVisable}
                notificationsRequestLater={this.notificationsRequestLater}
                notificationsRequestNever={this.notificationsRequestNever}
                requestPushNotifsPermission={this.requestPushNotifsPermission}
                savedPushPermission={this.state.savedPushPermission}
              />
            )}
            {!notifIntro && !showPinIntro && (
              <>
                <ConvertTour />
                <HealthyTour />
                <MarginTour />
                <StabilizationTour />
                <WarningTour />
                <HomeTour />
              </>
            )}
          </View>
        )
      } else {
        showScreen = <AuthStack />
      }

      let versionWarn =
        this.versionLogic(this.props.launchDarkly['mob-version-optional']) ||
        this.versionLogic(this.state.latestVersion)
      let versionError =
        this.versionLogic(this.props.launchDarkly['mob-version-req']) ||
        this.versionLogic(this.state.mandatoryVersion)

      let newVer = ''
      if (versionWarn && this.state.showVerUpdate) {
        newVer = this.getHigherSemver(
          this.props.launchDarkly['mob-version-optional'],
          this.state.latestVersion,
        )
        showScreen = (
          <VersionCheck openStore={this.openStore} newVer={newVer} contVer={this.contVer} />
        )
      }

      if (versionError) {
        newVer = this.getHigherSemver(
          this.props.launchDarkly['mob-version-req'],
          this.state.mandatoryVersion,
        )

        showScreen = (
          <VersionCheck
            openStore={this.openStore}
            newVer={newVer}
            req={true}
            contVer={this.contVer}
          />
        )
      }

      if (this.props.showUpdateVersionScreen) {
        showScreen = (
          <UpdateAppScreen
            handleUpdatePress={this.handleUpdatePress}
            showUpdateError={this.state.showUpdateError}
          />
        )
      }
      if (
        this.props.launchDarkly['maintenance-page'] &&
        this.props.authed &&
        !this.props.showPinScreen
      ) {
        showScreen = <Maintenence refreshDarkly={() => this.refreshDarkly} />
      }
    }

    return showScreen
  }
}

const mapStateToProps = state => ({
  appState: state.auth.appState,
  askingPermissions: state.auth.askingPermissions,
  authed: state.auth.authed,
  fcmToken: state.auth.fcmToken,
  pin: state.auth.pin,
  showPinScreen: state.auth.pinScreen,
  showUpdateVersionScreen: state.auth.showUpdateVersionScreen,
  pushNotifPermissions: state.user.pushNotifPermissions,
  showSplash: state.auth.showSplash,
  WebService: state.auth.WebService,
  storedPin: state.auth.pin,
  user: state.user.user,
  backToSettings: state.user.backToSettings,
  testSession: state.auth.testSession,
  launchDarkly: state.launchDarkly,
  accountRef: state.auth.account.ref,
})

export default connect(mapStateToProps)(RootNavigator)
