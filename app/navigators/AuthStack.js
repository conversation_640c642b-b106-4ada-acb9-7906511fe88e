import * as React from 'react'
import {createNativeStackNavigator} from '@react-navigation/native-stack'

import AuthScreen from '../views/Auth/AuthScreen'
import SignUpIndex from '../views/Auth/SignUp/SignUpIndex'
import ResetPassword from '../views/Auth/Login/ResetPassword'
import Auth1 from '../views/Auth/SignUp/Auth1'
import Auth2 from '../views/Auth/SignUp/Auth2'
import Auth3 from '../views/Auth/SignUp/Auth3'
import Auth4 from '../views/Auth/SignUp/Auth4'
import AuthTos from '../views/Auth/SignUp/AuthTos'

const HomeStack = createNativeStackNavigator()

export default function AuthStack() {
  return (
    <HomeStack.Navigator
      screenOptions={{
        headerShown: false,
        lazy: true,
      }}>
      <HomeStack.Screen name="Auth" component={AuthScreen} />
      <HomeStack.Screen name="SignUp" component={SignUpIndex} />
      <HomeStack.Screen name="ResetPassword" component={ResetPassword} />
      <HomeStack.Screen name="Auth1" component={Auth1} />
      <HomeStack.Screen name="Auth2" component={Auth2} />
      <HomeStack.Screen name="Auth3" component={Auth3} />
      <HomeStack.Screen name="Auth4" component={Auth4} />
      <HomeStack.Screen name="AuthTos" component={AuthTos} />
    </HomeStack.Navigator>
  )
}
