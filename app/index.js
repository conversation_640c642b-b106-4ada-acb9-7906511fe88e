import React, {Component} from 'react'
import {Provider} from 'react-redux'
import {createStore, applyMiddleware} from 'redux'
//import {composeWithDevTools} from 'redux-devtools-extension'
//import codePush from 'react-native-code-push'

import {GestureHandlerRootView} from 'react-native-gesture-handler'

import thunk from 'redux-thunk'
import {NavigationContainer} from '@react-navigation/native'
import Heap from '@heap/react-native-heap'
import RNBootSplash from 'react-native-bootsplash'
import RootNavigator from './navigators/RootNavigator'
import rootReducer from './store'
import config from './config.json'

class SodiumChecker extends Component {
  constructor(props) {
    super(props)
    //this.store = createStore(rootReducer, composeWithDevTools(applyMiddleware(thunk)))
    this.store = createStore(rootReducer, applyMiddleware(thunk))
    let heapAppId = '**********' // prod
    if (config.env == 'staging') {
      heapAppId = '**********'
    }
    try {
      Heap.setAppId(heapAppId)
    } catch (err) {
      //console.log('a');
    }
  }

  getActiveRouteName = state => {
    console.log('getActiveRouteName', state)
    const route = state.routes[state.index]
    // Dive into nested navigators
    if (route.state) {
      return this.getActiveRouteName(route.state)
    }
    return route.name
  }
  /*
  onStateChange={state => {
    const currentRouteName = this.getActiveRouteName(state)
    Heap.track('screen_view', {screen: currentRouteName})
  }}
  */

  render() {
    const HeapNavigationContainer = Heap.withReactNavigationAutotrack(NavigationContainer)

    return (
      <GestureHandlerRootView style={{flex: 1}}>
        <HeapNavigationContainer onReady={() => RNBootSplash.hide()}>
          <Provider store={this.store}>
            <RootNavigator />
          </Provider>
        </HeapNavigationContainer>
      </GestureHandlerRootView>
    )
  }
}

//export default codePush(SodiumChecker)
export default SodiumChecker
