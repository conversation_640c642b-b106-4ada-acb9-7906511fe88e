import * as Keychain from 'react-native-keychain'
import AuthServiceStub from '../../services/AuthService-stub'
import WebServiceStub from '../../services/WebService-stub'
import {clearLoans, clearUser, updateUser} from '../user/user.actions'
import TYPES from './auth.types'

export function hideSplash(authed) {
  return {
    type: TYPES.HIDE_SPLASH,
    authed,
  }
}

export function updatePin(pin) {
  return {
    type: TYPES.UPDATE_PIN,
    pin,
  }
}

export function showPinScreen(show) {
  return {
    type: TYPES.SHOW_PIN_SCREEN,
    show,
  }
}

export function askingForPermissions(asking) {
  return {
    type: TYPES.ASKING_FOR_PERMISSIONS,
    asking,
  }
}

export function updateAuthed(authed) {
  return {
    type: TYPES.UPDATE_AUTHED,
    authed,
  }
}

export function showSplash() {
  return {
    type: TYPES.SHOW_SPLASH,
  }
}

export function updateDisabled(disabled) {
  return {
    type: TYPES.UPDATE_DISABLED,
    disabled,
  }
}

export function authenticating() {
  return {
    type: TYPES.AUTHENTICATING,
  }
}

export function unAuth() {
  return {
    type: TYPES.NOT_AUTHED,
  }
}

export function pauseAuth() {
  return {
    type: TYPES.PAUSE_AUTH,
  }
}

export function isAuthed(account) {
  return {
    type: TYPES.IS_AUTHED,
    account,
  }
}

export function updateToken(token) {
  return {
    type: TYPES.UPDATE_TOKEN,
    token,
  }
}

export function updateFCMToken(fcmToken) {
  return {
    type: TYPES.UPDATE_FCM_TOKEN,
    fcmToken,
  }
}

export function updateWebService(env) {
  return {
    type: TYPES.UPDATE_WEB_SERVICE,
    env,
  }
}

export function updateAuthFlow(obj) {
  return {
    type: TYPES.AUTH_FLOW,
    obj,
  }
}

export function login(email, password) {
  return (dispatch, getState) => getState().auth.AuthService.login(email, password)
}

export function logout() {
  console.log('logout1')
  return (dispatch, getState) => {
    Keychain.resetInternetCredentials('token')
    getState().auth.WebService.logoutOfSalt()
    dispatch(unAuth())
    dispatch(updatePin('0'))
    dispatch(clearLoans())
    dispatch(clearUser())
  }
}

export function localLogout() {
  console.log('localLogout')

  return (dispatch, getState) => {
    Keychain.resetInternetCredentials('token')
    dispatch(unAuth())
    dispatch(updatePin('0'))
    dispatch(clearLoans())
    dispatch(clearUser())
  }
}

export function login2FA(user, twoFactor) {
  return (dispatch, getState) => getState().auth.AuthService.login2FA(user, twoFactor)
}

export function updateAuthService(env) {
  return (dispatch, getState) => getState().auth.AuthService.setCognitoCreds(env)
}

export function updateDeviceId(deviceId) {
  return {
    type: TYPES.UPDATE_DEVICE_ID,
    deviceId,
  }
}

export function showUpdateVersionScreen() {
  return {type: TYPES.SHOW_UPDATE_VERSION_SCREEN}
}

export function reviewerLogin() {
  return (dispatch, getState) => {
    dispatch(isAuthed({qwe: 'qwe', emails: []}))
    dispatch(updateUser({emails: []}))
    dispatch(updateTestSession(true, new AuthServiceStub(), new WebServiceStub()))
  }
}

export function updateTestSession(testSession, AuthServiceStub, WebServiceStub) {
  return {
    type: TYPES.UPDATE_TEST_SESSION,
    testSession,
    AuthServiceStub,
    WebServiceStub,
  }
}
