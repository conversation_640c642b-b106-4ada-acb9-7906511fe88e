import WebService from '../../services/WebService'
import initialState from '../initialState'
import TYPES from './auth.types'

export default function authReducer(state = initialState.auth, action) {
  switch (action.type) {
    case TYPES.IS_AUTHED:
      return {
        ...state,
        isAuthenticating: false,
        authed: true,
        account: action.account,
        showSplash: false,
      }
    case TYPES.NOT_AUTHED:
      return {
        ...state,
        isAuthenticating: false,
        authed: false,
        cognitoUser: {},
        showSplash: false,
      }
    case TYPES.PAUSE_AUTH:
      return {
        ...state,
        isAuthenticating: false,
      }
    case TYPES.AUTHENTICATING:
      return {
        ...state,
        isAuthenticating: true,
      }
    case TYPES.UPDATE_TOKEN:
      return {
        ...state,
        token: action.token,
      }

    case TYPES.UPDATE_AUTHED:
      console.log('UPDATE_AUTHED', action.authed)
      return {
        ...state,
        authed: action.authed,
      }

    case TYPES.HIDE_SPLASH:
      console.log('UPDATE_AUTHED', action.authed)
      return {
        ...state,
        showSplash: false,
        authed: action.authed,
      }

    case TYPES.SHOW_SPLASH:
      return {
        ...state,
        showSplash: true,
      }

    case TYPES.UPDATE_DISABLED:
      return {
        ...state,
        disabled: action.disabled,
      }

    case TYPES.UPDATE_FCM_TOKEN:
      return {
        ...state,
        fcmToken: action.fcmToken,
      }

    case TYPES.UPDATE_WEB_SERVICE: {
      let newWebService
      if (action.env === 'dev') {
        newWebService = new WebService('https://bp-api-ext-dev.saltlndg.io/v0')
      } else if (action.env === 'prod') {
        newWebService = new WebService('https://bp-api-ext-prod.saltlndg.io/v0')
      }
      return {
        ...state,
        WebService: newWebService,
      }
    }

    case TYPES.UPDATE_PIN:
      return {
        ...state,
        pin: action.pin,
      }

    case TYPES.SHOW_PIN_SCREEN:
      return {
        ...state,
        pinScreen: action.show,
        showSplash: false,
      }

    case TYPES.UPDATE_DEVICE_ID:
      return {
        ...state,
        deviceId: action.deviceId,
      }

    case TYPES.ASKING_FOR_PERMISSIONS:
      return {
        ...state,
        askingPermissions: action.asking,
      }

    case TYPES.SHOW_UPDATE_VERSION_SCREEN:
      return {
        ...state,
        showUpdateVersionScreen: true,
      }

    case TYPES.UPDATE_TEST_SESSION:
      return {
        ...state,
        testSession: action.testSession,
        AuthService: action.AuthServiceStub,
        WebService: action.WebServiceStub,
      }

    case TYPES.AUTH_FLOW:
      return {
        ...state,
        flow: {
          ...state.flow,
          ...action.obj,
        },
      }

    default:
      return state
  }
}
