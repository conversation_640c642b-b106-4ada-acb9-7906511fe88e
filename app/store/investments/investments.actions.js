import TYPES from './investments.types'

export function clearCancelledInvestmentRequest(id) {
  return {
    type: TYPES.CLEAR_CANCELLED_INVESTMENT_REQUEST,
    payload: id,
  }
}

export function saveInvestmentRequest(data) {
  return {
    type: TYPES.SAVE_INVESTMENT_REQUEST,
    payload: data,
  }
}

export function getInvestmentsPending() {
  return {
    type: TYPES.GET_INVESTMENTS_PENDING,
  }
}

export function getInvestmentsSuccess(data) {
  return {
    type: TYPES.GET_INVESTMENTS_SUCCESS,
    payload: data,
  }
}
export function getInvestmentsFailure(err) {
  return {
    type: TYPES.GET_INVESTMENTS_FAILURE,
    payload: err,
  }
}

export function setInvestmentRates(data) {
  return {
    type: TYPES.SET_INVESTMENT_RATE_MAPS,
    payload: data,
  }
}

export function getInvestments() {
  return (dispatch, getState) => {
    const {auth} = getState()

    dispatch(getInvestmentsPending())

    return auth.WebService.getInvestments()
      .then(res => {
        dispatch(getInvestmentsSuccess(res.data))
      })
      .catch(err => {
        dispatch(getInvestmentsFailure(err))
        throw err
      })
  }
}

export function getInvestmentRates() {
  return async (dispatch, getState) => {
    const {auth} = getState()
    return await auth.WebService.getInvestmentRates()
      .then(res => {
        dispatch(setInvestmentRates(res.data))
      })
      .catch(err => {
        throw err
      })
  }
}
