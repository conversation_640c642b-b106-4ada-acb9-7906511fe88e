import TYPES from './investments.types'
import {BigNumber} from 'bignumber.js'

import {initialState} from '../initialState'

const derivedStatusMap = new Map()
  .set('requested', 'pending')
  .set('in_progress', 'pending')
  .set('under_review', 'pending')
  .set('in_revision', 'pending')
  .set('pending_approval', 'pending')
  .set('pending_signatures', 'pending')
  .set('denial_recommendation', 'pending')
  .set('awaiting_collateral', 'pending')
  .set('awaiting_funding', 'pending')
  .set('denied', 'pending')
  .set('active', 'active')
  .set('voided', 'none')
  .set('closed', 'none')
  .set('cancelled', 'none')
  .set('expired', 'none')
  .set(undefined, 'none')

const actionDefaults = {
  payload: null,
  type: null,
  meta: null,
}

export default (state = initialState.investments, {payload, type} = actionDefaults) => {
  switch (type) {
    case TYPES.GET_INVESTMENTS_PENDING:
      return {
        ...state,
      }
    case TYPES.GET_INVESTMENTS_SUCCESS:
      return payload.length
        ? {
            ...state,
            derivedStatus: derivedStatusMap.get(payload[0]?.status),
            isFetched: true,
            byId: {
              ...state.byId,
              [payload[0].id]: {
                ...payload[0],
                derivedStatus: derivedStatusMap.get(payload[0]?.status),
              },
            },
            result: [...new Set([...state.result, payload[0].id])],
            totals: {
              collateralValue: payload[0].collateralValue,
              loanValue: payload[0].loanValue,
              ltv: payload[0].ltv,
            },
          }
        : {
            ...state,
            derivedStatus: derivedStatusMap.get(payload[0]?.status),
            result: [],
            isFetched: true,
          }
    case TYPES.GET_INVESTMENTS_FAILURE:
      return {
        ...state,
        isFetched: true,
      }
    case TYPES.SAVE_INVESTMENT_REQUEST:
      return {
        ...state,
        byId: {
          ...state.byId,
          [payload.id]: {
            ...payload,
            derivedStatus: derivedStatusMap.get(payload?.status),
          },
        },
        result: [...new Set([...state.result, payload.id])],
        derivedStatus: derivedStatusMap.get(payload?.status),
      }
    case TYPES.UPDATE_INVESTMENT:
      return {
        ...state,
        byId: {
          ...state.byId,
          [payload.id]: {...state.byId[payload.id], ...payload},
        },
        result: [...new Set([...state.result, payload.id])],
        derivedStatus: derivedStatusMap.get(payload?.status),
      }
    case TYPES.SET_INVESTMENT_RATE_MAPS:
      return {
        ...state,
        investmentRates: payload,
      }

    default:
      return state
  }
}

export function getCurrentInvestment(investments) {
  const activeOrPendingInvestments = getActiveOrPendingInvestment(investments)
  return activeOrPendingInvestments?.[0] || {}
}

export function hasDepositForInvestment(investment, deposit) {
  if (!investment || !deposit) return false
  const wallets = Object.values(deposit.byId)

  if (!investment || wallets?.length < 1) return false
  const depositNeeded = new BigNumber(investment.amount)

  const depositValue = wallets.reduce((sum, wallet) => {
    if (!wallet.value) return sum
    const val = new BigNumber(wallet.value)
    return sum.plus(val)
  }, new BigNumber(0))

  return depositValue.gte(depositNeeded)
}

export function getActiveOrPendingInvestment(investments) {
  return investments?.result
    ?.map(id => investments.byId[id])
    .filter(investments => {
      return ['active', 'pending'].includes(investments.derivedStatus)
    })
}

export function getPendingInvestments(investments) {
  return investments?.result
    ?.map(id => investments.byId[id])
    .filter(investments => {
      return ['pending'].includes(investments.derivedStatus)
    })
}

// export function serializeObjectToMap(rates) {
//   return new Map(toNumericPairs(rates))
// }

export function serializeObjectWithMapToMap(ratesWithTerm) {
  const newPairs = Object.keys(ratesWithTerm).map(key => [Number(key), ratesWithTerm[key]])
  return new Map(newPairs)
}
