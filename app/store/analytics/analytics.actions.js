import {Hits as GAHits} from 'react-native-google-analytics'
import {Platform} from 'react-native'
//import appsFlyer from 'react-native-appsflyer'
import DeviceInfo from 'react-native-device-info'
import {firebase} from '@react-native-firebase/analytics'
import TYPES from './analytics.types'
import config from '../../config.json'

const version = DeviceInfo.getReadableVersion()
const bundleId = DeviceInfo.getBundleId()

export function updateGA(ga) {
  return {
    type: TYPES.UPDATE_GA,
    ga,
  }
}

export function updateIDFA(idfa) {
  return {
    type: TYPES.UPDATE_IDFA,
    idfa,
  }
}

export function screenView(screenName) {
  return (dispatch, getState) => {
    if (config.env !== 'production') {
      return
    }

    const {ga} = getState().analytics
    const screen = new GAHits.ScreenView('saltMobile', screenName, version, bundleId)
    try {
      ga.send(screen)
    } catch (err) {
      console.log('ga.send screen err', err)
    }

    //firebase
    const firebaseScreenName = screenName.replace(/\s/g, '').replace(/-/g, '_')
    firebase.analytics().logEvent(firebaseScreenName)
  }
}

export function sendEvent(eventName) {
  return (dispatch, getState) => {
    //appsFlyer
    /*
		const eventValues = {
			af_content_id: 'id123',
			af_currency: 'USD',
			af_revenue: '2',
		}

		try {
			appsFlyer.logEvent(
				eventName,
				eventValues,
				(res) => {
					console.log('logEvent res', res)
				},
				(err) => {
					console.error('logEvent err', err)
				}
			)
		} catch (err) {
			//console.log('appsFlyer logevent err', err);
		}

		//google analytics
		if (config.env !== 'production') {
			return
		}
		*/

    const {ga} = getState().analytics
    const gaEvent = new GAHits.Event(eventName, 'send', 'React Native', 100)
    ga.send(gaEvent).catch(err => {
      console.log('ga.send event err', err)
    })

    //firebase
    const firebaseEventName = eventName.replace(/\s/g, '').replace(/-/g, '_')
    firebase.analytics().logEvent(firebaseEventName)
  }
}
