import initialState from '../initialState'
import TYPES from './analytics.types'

export default function analyticsReducer(
  state = initialState.analytics,
  action
) {
  switch (action.type) {
    case TYPES.UPDATE_GA:
      return {
        ...state,
        ga: action.ga,
      }

    case TYPES.UPDATE_IDFA:
      return {
        ...state,
        idfa: action.idfa,
      }

    default:
      return state
  }
}
