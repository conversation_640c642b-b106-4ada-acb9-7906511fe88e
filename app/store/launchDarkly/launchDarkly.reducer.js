import TYPES from './launchDarkly.types'
import initialState from '../initialState'

export default function launchDarklyReducer(state = initialState.launchDarkly, action) {
  switch (action.type) {
    case TYPES.SET_FLAGS:
      return {
        ...state,
        ...action.values,
      }

    case TYPES.SET_CLIENT:
      return {
        ...state,
        ldClient: action.ldClient,
      }

    default:
      return state
  }
}
