import {combineReducers} from 'redux'
import analytics from './analytics/analytics.reducer'
import auth from './auth/auth.reducer'
import notifications from './notifications/notifications.reducer'
import user from './user/user.reducer'
import launchDarkly from './launchDarkly/launchDarkly.reducer'
import investments from './investments/investments.reducer'

const appReducer = combineReducers({
  analytics,
  auth,
  notifications,
  user,
  launchDarkly,
  investments,
})

const rootReducer = (state, action) => appReducer(state, action)

export default rootReducer
