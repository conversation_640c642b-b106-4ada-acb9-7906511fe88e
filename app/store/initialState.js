import AuthService from '../services/AuthService'
import WebService from '../services/WebService'
import AuthServiceStub from '../services/AuthService-stub'
import WebServiceStub from '../services/WebService-stub'
import config from '../config.json'

export const initialState = {
  analytics: {
    ga: {
      send: () => {},
    },
    idfa: '',
  },
  auth: {
    showSplash: true,
    showUpdateVersionScreen: false,
    authed: false,
    isAuthenticating: false,
    account: {},
    AuthService: config.useStubs ? new AuthServiceStub() : new AuthService(),
    WebService: config.useStubs ? new WebServiceStub() : new WebService(config.api, config.env),
    token: '',
    disabled: false,
    fcmToken: '',
    deviceId: null,
    ref: null,
    pin: null,
    pinScreen: true,
    askingPermissions: false,
    testSession: false,
    flow: {},
  },
  launchDarkly: {
    stackwise: false,
    ldClient: {},
  },
  notifications: {
    prefs: {},
    restructuredPrefs: {},
    prefID: '',
    unread: 0,
    showNotifications: false,
    showToast: false,
    notificationArr: null,
    totalNotifs: 0,
  },
  investments: {
    isFetched: false,
    byId: {},
    result: [],
    liquidations: [],
    investmentHistory: [],
    investmentRates: {},
    totals: {
      depositValue: '',
      investmentValue: '',
      apy: '',
    },
  },
  user: {
    tab: 'Home',
    page: 'Home',
    goTwoFactor: 0,
    unit21Showing: false,
    newAgreement: {},
    merchantTos: false,
    externalSlideFn: null,
    accountCreated: null,
    showDepositModal: false,
    backToSettings: false,
    banks: [],
    name: '',
    deviceID: '',
    dataLoaded: false,
    picture: '',
    uid: '',
    pushNotifPermissions: null,
    user: {},
    refreshCount: 0,
    unit21RefreshCount: 0,
    refreshExternalWallets: 0,
    upholdHide: [],
    pauseUnit21: true,
    postNewLoan: 0,
    flowB1: {},
    referrals: {
      signed: false,
    },
    tourCount: 0,
    allWallets: [],
    loanData: {
      ltv: '0',
      loanValue: '0',
      collateralValue: '0',
      loans: [],
      collateralTotal: 0,
      status: null,
      thresholds: {
        finalNotice: '0.****************',
        liquidation: '0.****************',
        marginCall: '0.****************',
        marginCure: '0.****************',
        warning: '0.****************',
      },
      collaterals: [
        {
          currency: 'BTC',
          value: '0',
          price: '0',
          balance: '0',
          projectedBalance: '0',
        },
        {
          currency: 'BCH',
          value: '0',
          price: '0',
          balance: '0',
          projectedBalance: '0',
        },
        {
          currency: 'ETH',
          value: '0',
          price: '0',
          balance: '0',
          projectedBalance: '0',
        },
        {
          currency: 'SALT',
          value: '0',
          price: '0',
          balance: '0',
          projectedBalance: '0',
        },
        {
          currency: 'LTC',
          value: '0',
          price: '0',
          balance: '0',
          projectedBalance: '0',
        },
        {
          currency: 'DASH',
          value: '0',
          price: '0',
          balance: '0',
          projectedBalance: '0',
        },
        {
          currency: 'DOGE',
          value: '0',
          price: '0',
          balance: '0',
          projectedBalance: '0',
        },
        {
          currency: 'USDC',
          value: '0',
          price: '1',
          balance: '0',
          projectedBalance: '0',
        },
        {
          currency: 'TUSD',
          value: '0',
          price: '1',
          balance: '0',
          projectedBalance: '0',
        },
        {
          currency: 'USDP',
          value: '0',
          price: '0',
          balance: '0',
          projectedBalance: '0',
        },
        {
          currency: 'PAXG',
          value: '0',
          price: '0',
          balance: '0',
          projectedBalance: '0',
        },
        {
          currency: 'XRP',
          value: '0',
          price: '0',
          balance: '0',
          projectedBalance: '0',
        },
        {
          currency: 'USDT',
          value: '0',
          price: '0',
          balance: '0',
          projectedBalance: '0',
        },
        {
          currency: 'PREF',
          value: '0',
          price: '0.661234',
          balance: '0',
          projectedBalance: '0',
        },
      ],
    },
    prices: {
      'BTC-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.336363Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'BCH-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.336363Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'ETH-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'SALT-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'LTC-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'DASH-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'DOGE-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'USDC-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 1,
        atomic: false,
      },
      'TUSD-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 1,
        atomic: false,
      },
      'USDP-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 1,
        atomic: false,
      },
      'USDT-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 1,
        atomic: false,
      },
      'PAXG-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'XRP-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'PREF-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0.661234,
        atomic: false,
      },
    },
    prices24h: {
      'BTC-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.336363Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'BCH-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.336363Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'ETH-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'SALT-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'LTC-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'DASH-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'DOGE-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'USDC-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 1,
        atomic: false,
      },
      'TUSD-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 1,
        atomic: false,
      },
      'USDP-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 1,
        atomic: false,
      },
      'PAXG-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'USDT-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'XRP-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0,
        atomic: false,
      },
      'PREF-USD': {
        isStale: false,
        time: '2018-05-24T20:42:01.229117Z',
        volume: 0,
        price: 0.661234,
        atomic: false,
      },
    },
  },
}

export default initialState
