import TYPES from './notifications.types';
import initialState from '../initialState';

export default function notifications(state = initialState.notifications, action) {
  switch (action.type) {
    case TYPES.UPDATE_NOTIFICATIONS:
      return {
        ...state,
        prefs: action.prefs,
        restructuredPrefs: action.restructuredPrefs,
      };

    case TYPES.UPDATE_PREF_ID:
      return {
        ...state,
        prefID: action.prefID,
      };

    case TYPES.UPDATE_UNREAD:
      return {
        ...state,
        unread: action.num,
      };

    case TYPES.TOGGLE_NOTIFICATIONS:
      return {
        ...state,
        showNotifications: action.showNotifications,
      };

    case TYPES.GET_NOTIFICATIONS:
      return {
        ...state,
        totalNotifs: action.totalNotifs,
        notificationArr: action.notificationArr,
      };

    case TYPES.UPDATE_NOTIFICATION_ARR:
      return {
        ...state,
        notificationArr: action.notificationArr,
      };

    case TYPES.SHOW_TOAST:
      console.log('showtoast');
      return {
        ...state,
        showToast: action.show,
      };

    default:
      return state;
  }
}
