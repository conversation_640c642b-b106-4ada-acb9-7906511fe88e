import TYPES from './notifications.types'
import {formatToAmPmTime, notificationFormatDate} from '../../util/helpers'
import {notifEventsMap} from '../../util/enumerables'

export function savePrefs(prefs, restructuredPrefs) {
  return {
    type: TYPES.UPDATE_NOTIFICATIONS,
    prefs,
    restructuredPrefs,
  }
}

export function updatePrefID(prefID) {
  return {
    type: TYPES.UPDATE_PREF_ID,
    prefID,
  }
}

export function showNotifications(show) {
  return (dispatch, getState) => {
    dispatch({
      type: TYPES.TOGGLE_NOTIFICATIONS,
      showNotifications: show,
    })
  }
}

export function getNotifications(page = 0) {
  return (dispatch, getState) =>
    getState()
      .auth.WebService.getNotifications(page)
      .then(res => {
        const totalNotifs = res.data.total
        const notificationArr =
          res.data.notifications.map((a, k) => {
            const date = notificationFormatDate(a.createdAt)
            const time = formatToAmPmTime(a.createdAt)
            const event = notifEventsMap.get(a.event) || 'Notification'

            return {
              date,
              time,
              event,
              relationType: a.relationType,
              text: a.message,
              key: k.toString() + 100 * page,
              viewedAt: a.viewedAt,
              id: a.id,
            }
          }) || []
        dispatch({
          type: TYPES.GET_NOTIFICATIONS,
          notificationArr,
          totalNotifs,
        })
        return 'success'
      })
      .catch(err => {
        console.log('getNotifications err', err)
        return 'failure'
      })
}

export function updateNotificationArr(notificationArr) {
  return (dispatch, getState) => {
    dispatch({
      type: TYPES.UPDATE_NOTIFICATION_ARR,
      notificationArr,
    })
  }
}

export function updatePreferences(prefs) {
  return (dispatch, getState) => {
    const restructuredPrefs = {call: {}, email: {}, push: {}, text: {}}
    for (const key of Object.keys(prefs)) {
      restructuredPrefs.call[key] = prefs[key].call
      restructuredPrefs.email[key] = prefs[key].email
      restructuredPrefs.push[key] = prefs[key].push
      restructuredPrefs.text[key] = prefs[key].text
    }
    dispatch(savePrefs(prefs, restructuredPrefs))
  }
}

export function changeValue(field, newValue, type, sendToBackend = true) {
  return (dispatch, getState) => {
    const newPrefs = getState().notifications.prefs
    newPrefs[field][type].value = newValue

    dispatch(updatePreferences(newPrefs))
    const updateData = {
      preferenceId: getState().notifications.prefID,
      preferences: newPrefs,
    }
    if (!sendToBackend) {
      return
    }
    console.log('updateData', updateData)
    return getState().auth.WebService.updateNotificationPreferences(updateData)
  }
}

export function updateUnread(num) {
  return {
    type: TYPES.UPDATE_UNREAD,
    num,
  }
}

export function showToast(show) {
  return {
    type: TYPES.SHOW_TOAST,
    show,
  }
}
