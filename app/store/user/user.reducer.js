import TYPES from './user.types'
import initialState from '../initialState'

export default function userReducer(state = initialState.user, action) {
  switch (action.type) {
    case TYPES.UPDATE_LOANS:
      const existingLoanCollaterals = state.loanData.collaterals.slice()

      if (action.loanData.collaterals) {
        existingLoanCollaterals.map((a, k) => {
          let incomingIndex = -1

          action.loanData.collaterals.map((b, q) => {
            if (b.currency === a.currency) {
              incomingIndex = q
            }
          })

          if (incomingIndex > -1) {
            existingLoanCollaterals[k] = {
              ...existingLoanCollaterals[k],
              ...action.loanData.collaterals[incomingIndex],
            }
          }
        })
      }

      return {
        ...state,
        loanData: {
          ...state.loanData,
          ...action.loanData,
          collaterals: existingLoanCollaterals,
        },
      }

    case TYPES.UPDATE_PENDING_REFINANCE_LOAN:
      return {
        ...state,
        pendingRefinanceLoan: action.loanData,
      }

    case TYPES.UPDATE_LOANS_DRILL:
      //just incase collateral is already there & not wiped, ...state.loanData,
      console.log('UPDATE_LOANS_DRILL', state.loanData, action.loanData)
      if (JSON.stringify(action.loanData) === JSON.stringify({collaterals: []})) {
        console.log('collaterals: []')
        return {
          ...state,
          loanData: {
            ...initialState.user.loanData,
            ...action.loanData,
          },
        }
      }
      return {
        ...state,
        loanData: {
          ...state.loanData,
          ...action.loanData,
        },
      }

    case TYPES.UPDATE_LOANS_IN_ONE_USER:
      let userAcc = state.user?.accounts?.filter(a => a.ref == action.accountRef)[0]
      userAcc.loans[0] = {
        ...userAcc.loans[0],
        ...action.loanData,
      }
      let newAccounts = state.user?.accounts?.map(a => {
        if (a.ref == action.accountRef) {
          return userAcc
        } else {
          return a
        }
      })
      return {
        ...state,
        user: {
          ...state.user,
          accounts: newAccounts,
        },
        //loanData: action.loanData,
      }

    /*
    case TYPES.UPDATE_WALLETS:
      const existingCollaterals = state.loanData.collaterals.slice();

      existingCollaterals.map((a, k) => {
        let currency = action.wallet?.currency;
        if (currency) {
          currency = currency.toUpperCase();
        }
        if (currency === a.currency) {
          action.wallet.currency = currency;
          existingCollaterals[k] = {
            ...existingCollaterals[k],
            ...action.wallet,
          };
        }
      });
      const total = existingCollaterals.reduce(
        (sum, collateral) =>
          collateral.currency !== 'SALT'
            ? parseFloat(sum) + parseFloat(collateral.value)
            : sum + 0,
        0,
      );
      const totalWithSalt = existingCollaterals.reduce(
        (sum, collateral) => parseFloat(sum) + parseFloat(collateral.value),
        0,
      );

      let printWallets = {
        ...state,
        loanData: {
          ...state.loanData,
          collaterals: existingCollaterals,
          collateralTotal: total,
          collateralTotalWithSalt: totalWithSalt,
        },
      };
      console.log('printWallets', printWallets);

      return {
        ...state,
        loanData: {
          ...state.loanData,
          collaterals: existingCollaterals,
          collateralTotal: total,
          collateralTotalWithSalt: totalWithSalt,
        },
      };
      */

    case TYPES.UPDATE_PRICES:
      return {
        ...state,
        prices: {
          ...state.prices,
          ...action.prices,
        },
      }
    case TYPES.UPDATE_PRICES_24H:
      return {
        ...state,
        prices24h: {
          ...state.prices24h,
          ...action.prices,
        },
      }
    case TYPES.UPDATE_ALL_WALLETS:
      return {
        ...state,
        user: {
          ...state.user,
          allWallets: action.walletsRes,
        },
      }
    case TYPES.UPDATE_WALLET_TRANSACTIONS: {
      //if empty, remove that tx {}
      if (Object.keys(action.walletData).length === 0) {
        return {
          ...state,
          loanData: {
            ...state.loanData,
            collaterals: state.loanData.collaterals.map(x => {
              if (x.currency === action.title) {
                return {}
              } else return x
            }),
          },
        }
      }

      //if(action.walleData === {}){
      console.log('2')

      return {
        ...state,
        loanData: {
          ...state.loanData,
          collaterals: state.loanData.collaterals.map(x =>
            x.currency === action.walletData.currency ? {...action.walletData} : x,
          ),
        },
      }
    }
    case TYPES.CLEAR_PENDING_WITHDRAWAL:
      const thisCollateral = state.loanData.collaterals.filter(x => x.id === action.id)[0]
      const thisCollateralPendingExtractions = thisCollateral.pendingExtractions.filter(
        a => a.reason !== 'withdrawal',
      )
      return {
        ...state,
        loanData: {
          ...state.loanData,
          collaterals: state.loanData.collaterals.map(x =>
            x.id === action.id ? {...x, pendingExtractions: thisCollateralPendingExtractions} : x,
          ),
        },
      }
    case TYPES.CLEAR_LOANS:
      return {
        ...state,
        loanData: initialState.user.loanData,
      }
    case TYPES.UPDATE_USER:
      // dont update the account from this method
      return {
        ...state,
        user: {
          ...state.user,
          ...action.user,
          accounts: state.user.accounts,
        },
      }

    case TYPES.UPDATE_ACCOUNTS:
      return {
        ...state,
        user: {
          ...state.user,
          accounts: action.accounts,
        },
      }

    case TYPES.UPDATE_ACCOUNTS_PLUS:
      let accounts = state.user.accounts?.map(a => {
        let correctAcc = action.data.filter(b => b?.account?.createdAt == a.createdAt)[0]
        return {
          ...a,
          ...correctAcc?.account,
          loans: correctAcc?.loans || [],
          investments: correctAcc?.investments || [],
        }
      })
      return {
        ...state,
        user: {
          ...state.user,
          accounts: accounts,
        },
      }

    case TYPES.UPDATE_PHONE:
      return {
        ...state,
        user: {
          ...state.user,
          phone: action.phone,
        },
      }

    case TYPES.BACK_TO_SETTINGS:
      return {
        ...state,
        backToSettings: action.allow,
      }

    case TYPES.UPDATE_PUSH_NOTIF_PERMISSION:
      return {
        ...state,
        pushNotifPermissions: action.permission,
      }

    case TYPES.INCREASE_REFRESH_DATA_COUNT:
      return {
        ...state,
        refreshCount: action.refreshCount,
        accountCreated: action.accountCreated,
      }

    case TYPES.INCREASE_UNIT_21_REFRESH:
      return {
        ...state,
        unit21RefreshCount: action.unit21RefreshCount,
      }

    case TYPES.RESET_WALLETS:
      return {
        ...state,
        loanData: {
          ...state.loanData,
          collaterals: initialState.user.loanData.collaterals,
          collateralTotal: 0,
        },
      }

    case TYPES.UPDATE_UPHOLD_HIDE:
      return {
        ...state,
        upholdHide: action.upholdHide,
      }

    case TYPES.TOGGLE_NEW_AGREEMENT:
      return {
        ...state,
        newAgreement: action.data,
      }

    case TYPES.SHOW_DEPOSIT_MODAL:
      return {
        ...state,
        showDepositModal: action.show,
      }

    case TYPES.DATA_LOADED:
      return {
        ...state,
        dataLoaded: action.loaded,
      }

    case TYPES.UPDATE_BANKS:
      return {
        ...state,
        banks: action.banks,
      }

    case TYPES.PAUSE_UNIT_21:
      return {
        ...state,
        pauseUnit21: action.pause,
      }

    case TYPES.CLEAR_ACCOUNT_CREATED:
      return {
        ...state,
        accountCreated: null,
      }

    case TYPES.REFRESH_EXTERNAL_WALLETS:
      return {
        ...state,
        refreshExternalWallets: state.refreshExternalWallets + 1,
      }

    case TYPES.UPDATE_REFERRALS:
      return {
        ...state,
        referrals: {
          ...state.user.referrals,
          signed: action.signed,
          contract: action.contract,
        },
      }

    case TYPES.HIDE_SLIDE:
      return {
        ...state,
        externalSlideFn: null,
      }

    case TYPES.WALLET_ACTION:
      return {
        ...state,
        externalSlideFn: action.data,
      }

    case TYPES.SHOW_SLIDE:
      return {
        ...state,
        externalSlideFn: action.data,
      }

    case TYPES.UNIT_21_SHOWING:
      return {
        ...state,
        unit21Showing: action.showing,
      }

    case TYPES.UPDATE_FLOW_B1:
      return {
        ...state,
        flowB1: {...state.flowB1, ...action.data},
      }

    case TYPES.UPDATE_FLOW_B1_WHOLE:
      return {
        ...state,
        flowB1: {...action.data},
      }

    case TYPES.AFTER_NEW_LOAN:
      return {
        ...state,
        postNewLoan: state.postNewLoan + 1,
      }

    case TYPES.GO_TWOFACTOR:
      return {
        ...state,
        goTwoFactor: state.goTwoFactor + 1,
      }

    case TYPES.MERCHANT_TOS_READ:
      return {
        ...state,
        merchantTos: true,
      }

    case TYPES.TOUR_COUNT:
      return {
        ...state,
        tourCount: state.tourCount + 1,
      }

    case TYPES.UPDATE_TAB:
      return {
        ...state,
        tab: action.tab,
      }

    case TYPES.UPDATE_PAGE:
      return {
        ...state,
        page: action.page,
      }

    default:
      return state
  }
}
