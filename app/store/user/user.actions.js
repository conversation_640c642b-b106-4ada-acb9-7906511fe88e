import TYPES from './user.types'
import AsyncStorage from '@react-native-async-storage/async-storage'

export function showDepositModal(show) {
  return {
    type: TYPES.SHOW_DEPOSIT_MODAL,
    show,
  }
}

export function updateUser(user) {
  return dispatch => {
    dispatch({
      type: TYPES.UPDATE_USER,
      user,
    })
    return user
  }
}

export function updateAccount(accounts) {
  return {
    type: TYPES.UPDATE_ACCOUNTS,
    accounts,
  }
}

export function updateAccountPlus(data) {
  return (dispatch, getState) => {
    dispatch({
      type: TYPES.UPDATE_ACCOUNTS_PLUS,
      data,
    })
    const {user} = getState()
    return user
  }
}

export function tourCount() {
  return {
    type: TYPES.TOUR_COUNT,
  }
}

export function updateFlowB1(data) {
  return async (dispatch, getState) => {
    const {user} = getState()
    try {
      let newData = {...user?.flowB1, ...data}
      await AsyncStorage.setItem(`${user.user?.id}-FlowB1`, JSON.stringify(newData))
      dispatch({
        type: TYPES.UPDATE_FLOW_B1,
        data: newData,
      })
      return
    } catch (e) {
      throw e
    }
  }
}

export function updateFlowB1Whole(data) {
  return {
    type: TYPES.UPDATE_FLOW_B1_WHOLE,
    data,
  }
}

export function updateLoans(loanData) {
  return {
    type: TYPES.UPDATE_LOANS,
    loanData,
  }
}

export function updatePendingRefinanceLoan(loanData) {
  return {
    type: TYPES.UPDATE_PENDING_REFINANCE_LOAN,
    loanData,
  }
}

export function goTwoFactor() {
  return {
    type: TYPES.GO_TWOFACTOR,
  }
}

export function unit21Showing(showing) {
  return {
    type: TYPES.UNIT_21_SHOWING,
    showing,
  }
}

export function afterNewLoan() {
  return {
    type: TYPES.AFTER_NEW_LOAN,
  }
}

export function showSlide(data) {
  return {
    type: TYPES.SHOW_SLIDE,
    data,
  }
}

export function hideSlide(data) {
  return {
    type: TYPES.HIDE_SLIDE,
    data,
  }
}

export function walletAction(data) {
  return {
    type: TYPES.WALLET_ACTION,
    data,
  }
}

export function updateLoansDrill(loanData) {
  return {
    type: TYPES.UPDATE_LOANS_DRILL,
    loanData,
  }
}

export function updateLoansInOneUser(loanData, accountRef) {
  return {
    type: TYPES.UPDATE_LOANS_IN_ONE_USER,
    loanData,
    accountRef,
  }
}

export function updateBanks(banks) {
  return {
    type: TYPES.UPDATE_BANKS,
    banks,
  }
}

export function dataLoaded(loaded) {
  return {
    type: TYPES.DATA_LOADED,
    loaded,
  }
}

export function updateWallets(wallet) {
  return {
    type: TYPES.UPDATE_WALLETS,
    wallet,
  }
}

export function updateAllWallets(walletsRes) {
  return {
    type: TYPES.UPDATE_ALL_WALLETS,
    walletsRes,
  }
}

export function refreshExternalWallets() {
  return {
    type: TYPES.REFRESH_EXTERNAL_WALLETS,
  }
}

export function pauseUnit21(pause) {
  return {
    type: TYPES.PAUSE_UNIT_21,
    pause,
  }
}

export function resetWallets() {
  return {
    type: TYPES.RESET_WALLETS,
  }
}

export function clearLoans() {
  return {
    type: TYPES.CLEAR_LOANS,
  }
}

export function updatePrices(prices) {
  return {
    type: TYPES.UPDATE_PRICES,
    prices,
  }
}

export function updatePrices24h(prices) {
  return {
    type: TYPES.UPDATE_PRICES_24H,
    prices,
  }
}

export function updateWalletTransactions(walletData, title = null) {
  return {
    type: TYPES.UPDATE_WALLET_TRANSACTIONS,
    walletData,
    title,
  }
}

export function clearPendingWithdrawal(id) {
  return {
    type: TYPES.CLEAR_PENDING_WITHDRAWAL,
    id,
  }
}

export function allowBackToSettings(allow) {
  return {
    type: TYPES.BACK_TO_SETTINGS,
    allow,
  }
}

export function updatePushNotifPermission(permission) {
  return {
    type: TYPES.UPDATE_PUSH_NOTIF_PERMISSION,
    permission,
  }
}

export function updateUpholdHide(upholdHide) {
  return {
    type: TYPES.UPDATE_UPHOLD_HIDE,
    upholdHide,
  }
}

export function toggleNewAgreement(data) {
  return {
    type: TYPES.TOGGLE_NEW_AGREEMENT,
    data,
  }
}

export function updateTab(tab) {
  return {
    type: TYPES.UPDATE_TAB,
    tab,
  }
}

export function updatePage(page) {
  return {
    type: TYPES.UPDATE_PAGE,
    page,
  }
}

export function increaseRefreshDataCount(accountCreated = null) {
  console.log('increaseRefreshDataCount')
  return (dispatch, getState) => {
    const {user} = getState()
    dispatch({
      type: TYPES.INCREASE_REFRESH_DATA_COUNT,
      refreshCount: user.refreshCount + 1,
      accountCreated,
    })
  }
}

export function clearAccountCreated() {
  return {
    type: TYPES.CLEAR_ACCOUNT_CREATED,
  }
}

export function increaseUnit21Refresh(token) {
  return (dispatch, getState) => {
    const {user} = getState()
    dispatch({
      type: TYPES.INCREASE_UNIT_21_REFRESH,
      unit21RefreshCount: user.unit21RefreshCount + 1,
    })
  }
}

export function merchantTosRead() {
  return {
    type: TYPES.MERCHANT_TOS_READ,
  }
}

export function clearUser() {
  return dispatch => {
    dispatch(updateUser({}))
  }
}

export function getTokenPrice(token) {
  return (dispatch, getState) => {
    const {user} = getState()
    return user.prices[token].price
  }
}

export function updateReferrals(signed, contract = {}) {
  return {
    type: TYPES.UPDATE_REFERRALS,
    signed,
    contract,
  }
}

export function addPhone(country, countryCode, number) {
  return async (dispatch, getState) => {
    const {auth} = getState()
    try {
      const res = await auth.WebService.newPhone(country, countryCode, number)
      dispatch({type: TYPES.UPDATE_PHONE, phone: res.data})
      return res
    } catch (e) {
      throw e
    }
  }
}

export function getMe() {
  return async (dispatch, getState) => {
    const {auth} = getState()
    try {
      const res = await auth.WebService.getSaltUser()
      dispatch(updateUser(res.data))
      return res
    } catch (e) {
      throw e
    }
  }
}

export function deletePhone(id) {
  return async (dispatch, getState) => {
    const {auth} = getState()
    try {
      const res = await auth.WebService.deletePhone(id)
      dispatch({type: TYPES.UPDATE_PHONE, phone: null})
      return res
    } catch (e) {
      throw e
    }
  }
}
