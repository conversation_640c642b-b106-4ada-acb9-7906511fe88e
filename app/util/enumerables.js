import {getTokenPic} from './tokens'

export const derivedStatusMap = new Map()
  .set('requested', 'pending')
  .set('in_progress', 'pending')
  .set('under_review', 'pending')
  .set('in_revision', 'pending')
  .set('pending_signatures', 'pending')
  .set('denial_recommendation', 'pending')
  .set('awaiting_collateral', 'pending')
  .set('awaiting_funding', 'pending')
  .set('active', 'active')
  .set('denied', 'pending')
  .set('voided', 'none')
  .set('closed', 'none')
  .set('cancelled', 'none')
  .set('expired', 'none')
  .set(undefined, 'none')

export const cryptoNameMap = new Map()
  .set('', 'All Collateral')
  .set('btc', 'Bitcoin')
  .set('bch', 'Bitcoin Cash')
  .set('eth', 'Ethereum')
  .set('ltc', 'Litecoin')
  .set('dash', 'Dash')
  .set('salt', 'SALT')
  .set('doge', 'Dogecoin')
  .set('usdc', 'USD Coin')
  .set('dash', 'Dash')
  .set('tusd', 'True USD')
  .set('usdp', 'Paxos')
  .set('paxg', 'Paxos Gold')
  .set('xrp', 'XRP')
  .set('usdt', 'Tether')

export const CryptoType = {
  BTC: 'BTC',
  ETH: 'ETH',
  LTC: 'LTC',
  DOGE: 'DOGE',
  DASH: 'DASH',
  BCH: 'BCH',
  XRP: 'XRP',
  SALT: 'SALT',
  USDC: 'USDC',
  TUSD: 'TUSD',
  USDP: 'USDP',
  PAXG: 'PAXG',
  USDT: 'USDT',
}

export const cryptoMeta = new Map()
  // This should be the order: BTC, ETH, BCH, LTC, DASH, DOGE, SALT, USDP, USDC, TUSD
  .set('BTC', {
    currency: CryptoType.BTC,
    name: 'Bitcoin',
    balanceDigits: 8,
    color: '#ff8c00',
    priceDigits: 2,
    icon: getTokenPic(CryptoType.BTC),
    isStableCoin: false,
    needsGas: false,
    confirmations: 4,
    isDeprecated: false,
    trading: true,
    assetId: 50000,
  })
  .set('ETH', {
    currency: CryptoType.ETH,
    name: 'Ethereum',
    balanceDigits: 8,
    color: '#6585ff',
    priceDigits: 2,
    icon: getTokenPic(CryptoType.ETH),
    isStableCoin: false,
    needsGas: true,
    confirmations: 12,
    isDeprecated: false,
    trading: true,
    assetId: 50001,
  })
  .set('USDT', {
    currency: CryptoType.USDT,
    name: 'Tether',
    balanceDigits: 6,
    color: '#009393',
    priceDigits: 2,
    icon: getTokenPic(CryptoType.USDT),
    isStableCoin: true,
    needsGas: true,
    confirmations: 12,
    isDeprecated: false,
    trading: false,
    assetId: 50079,
  })
  .set('XRP', {
    currency: CryptoType.XRP,
    name: 'XRP',
    balanceDigits: 6,
    color: '#6585ff',
    priceDigits: 2,
    icon: getTokenPic(CryptoType.XRP),
    isStableCoin: false,
    needsGas: false,
    confirmations: 10,
    isDeprecated: true,
    trading: false,
    assetId: 50012,
  })
  .set('BCH', {
    currency: CryptoType.BCH,
    name: 'Bitcoin Cash',
    balanceDigits: 8,
    color: '#4CCA47',
    priceDigits: 2,
    icon: getTokenPic(CryptoType.BCH),
    isStableCoin: false,
    needsGas: false,
    confirmations: 10,
    isDeprecated: false,
    trading: true,
    assetId: 50003,
  })
  .set('LTC', {
    currency: CryptoType.LTC,
    name: 'Litecoin',
    balanceDigits: 8,
    color: '#aeaeae',
    priceDigits: 2,
    icon: getTokenPic(CryptoType.LTC),
    isStableCoin: false,
    needsGas: false,
    confirmations: 6,
    isDeprecated: false,
    trading: true,
    assetId: 50002,
  })
  .set('DASH', {
    currency: CryptoType.DASH,
    name: 'Dash',
    balanceDigits: 6,
    color: '#2ba0ff',
    priceDigits: 2,
    icon: getTokenPic(CryptoType.DASH),
    isStableCoin: false,
    needsGas: false,
    confirmations: 6,
    isDeprecated: true,
    trading: false,
    assetId: 50006,
  })
  .set('DOGE', {
    currency: CryptoType.DOGE,
    name: 'Dogecoin',
    balanceDigits: 3,
    color: '#f0d051',
    priceDigits: 4,
    icon: getTokenPic(CryptoType.DOGE),
    isStableCoin: false,
    needsGas: false,
    confirmations: 10,
    isDeprecated: true,
    trading: false,
    assetId: 50052,
  })
  .set('TUSD', {
    currency: CryptoType.TUSD,
    name: 'TrueUSD',
    balanceDigits: 6,
    color: '#68d9c9',
    priceDigits: 2,
    icon: getTokenPic(CryptoType.TUSD),
    isStableCoin: true,
    needsGas: true,
    confirmations: 12,
    isDeprecated: false,
    trading: false,
    assetId: 50136,
  })
  .set('USDP', {
    currency: CryptoType.USDP,
    name: 'Pax Dollar',
    balanceDigits: 6,
    color: '#68d9c9',
    priceDigits: 2,
    icon: getTokenPic(CryptoType.USDP),
    isStableCoin: true,
    needsGas: true,
    confirmations: 12,
    isDeprecated: false,
    trading: true,
    assetId: 52810,
  })
  .set('USDC', {
    currency: CryptoType.USDC,
    name: 'USD Coin',
    balanceDigits: 6,
    color: '#2783ee',
    priceDigits: 2,
    icon: getTokenPic(CryptoType.USDC),
    isStableCoin: true,
    needsGas: true,
    confirmations: 12,
    isDeprecated: false,
    trading: true,
    assetId: 51475,
  })
  .set('TUSD', {
    currency: CryptoType.TUSD,
    name: 'TrueUSD',
    balanceDigits: 6,
    color: '#68d9c9',
    priceDigits: 2,
    icon: getTokenPic(CryptoType.TUSD),
    isStableCoin: true,
    needsGas: true,
    confirmations: 12,
    isDeprecated: false,
    trading: false,
    assetId: 50211,
  })
  .set('PAXG', {
    currency: CryptoType.PAXG,
    name: 'Pax Gold',
    balanceDigits: 6,
    color: '#d0aa26',
    priceDigits: 2,
    icon: getTokenPic(CryptoType.PAXG),
    isStableCoin: false,
    needsGas: true,
    confirmations: 12,
    isDeprecated: false,
    trading: false,
    assetId: 52256,
  })
  .set('SALT', {
    currency: CryptoType.SALT,
    name: 'SALT',
    balanceDigits: 8,
    color: '#00b2a9',
    priceDigits: 2,
    icon: getTokenPic(CryptoType.SALT),
    isStableCoin: false,
    needsGas: true,
    confirmations: 12,
    isDeprecated: false,
    trading: false,
    assetId: 50136,
  })

export const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
]

export const tokenList = ['BTC', 'ETH', 'LTC', 'SALT', 'DOGE', 'USDC', 'DASH']

export const notifEventsMap = new Map()
  .set('', 'Notification')
  .set('collateral-call', 'Collateral Call')
  .set('deposit-confirmed', 'Deposit Confirmed')
  .set('deposit-initiated', 'Deposit Initiated')
  .set('liquidation-ltv-confirmed', 'Liquidation LTV Confirmed')
  .set('liquidation-ltv-initiated', 'Liquidation LTV Initiated')
  .set('liquidation-repayment-confirmed', 'Liquidation Repayment Confirmed')
  .set('loan-repayment-confirmed', 'Loan Repayment Confirmed')
  .set('loan-repayment-initiated', 'Loan Repayment Initiated')
  .set('ltv-eighty-eight', 'LTV 88%')
  .set('ltv-eighty-three', 'LTV 83%')
  .set('ltv-seventy-five', 'LTV 75%')
  .set('refund-confirmed', 'Refund Confirmed')
  .set('refund-initiated', 'Refund Initiated')
  .set('salt-purchase-confirmed', 'Salt Purchase Confirmed')
  .set('salt-purchase-initiated', 'Salt Purchase Initiated')
  .set('withdrawal-confirmed', 'Withdrawal Confirmed')
  .set('withdrawal-initiated', 'Withdrawal Initiated')
  .set('withdrawal-rejected', 'Withdrawal Rejected')

export const getScannerUrl = (token, txId) => {
  switch (token) {
    case 'BTC':
      return `https://live.blockcypher.com/btc/tx/${txId}`
    case 'BCH':
      return `https://live.blockcypher.com/bch/tx/${txId}`
    case 'LTC':
      return `https://live.blockcypher.com/ltc/tx/${txId}`
    case 'DASH':
      return `https://live.blockcypher.com/dash/tx/${txId}`
    case 'ETH':
      return `https://etherscan.io/tx/${txId}`
    case 'SALT':
      return `https://etherscan.io/tx/${txId}`
    case 'USDP':
      return `https://etherscan.io/tx/${txId}`
    case 'PAXG':
      return `https://etherscan.io/tx/${txId}`
    case 'USDC':
      return `https://etherscan.io/tx/${txId}`
    case 'DOGE':
      return `https://dogechain.info/tx/${txId}`
    default:
      return `https://etherscan.io/tx/${txId}`
  }
}
