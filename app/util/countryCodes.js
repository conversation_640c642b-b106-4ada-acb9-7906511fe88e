export default function countryCodes() {
  return [
    {
      name: 'Afghanistan',
      dial_code: '+93',
      code: 'AF',
      flag: '🇦🇫',
      uri: require('../imgs/phoneFlags/AF.png'),
    },
    {
      name: 'Åland Islands',
      dial_code: '+358',
      code: 'AX',
      flag: '🇦🇽',
      uri: require('../imgs/phoneFlags/AX.png'),
    },
    {
      name: 'Albania',
      dial_code: '+355',
      code: 'AL',
      flag: '🇦🇱',
      uri: require('../imgs/phoneFlags/AL.png'),
    },
    {
      name: 'Algeria',
      dial_code: '+213',
      code: 'DZ',
      flag: '🇩🇿',
      uri: require('../imgs/phoneFlags/DZ.png'),
    },
    {
      name: 'American Samoa',
      dial_code: '+1684',
      code: 'AS',
      flag: '🇺🇸',
      uri: require('../imgs/phoneFlags/AS.png'),
    },
    {
      name: 'Andorra',
      dial_code: '+376',
      code: 'AD',
      flag: '🇦🇩',
      uri: require('../imgs/phoneFlags/AD.png'),
    },
    {
      name: 'Angola',
      dial_code: '+244',
      code: 'AO',
      flag: '🇦🇴',
      uri: require('../imgs/phoneFlags/AO.png'),
    },
    {
      name: 'Anguilla',
      dial_code: '+1264',
      code: 'AI',
      flag: '🇦🇮',
      uri: null,
    },
    {
      name: 'Antarctica',
      dial_code: '+672',
      code: 'AQ',
      flag: '🇦🇶',
      uri: require('../imgs/phoneFlags/AQ.png'),
    },
    {
      name: 'Antigua and Barbuda',
      dial_code: '+1268',
      code: 'AG',
      flag: '🇦🇬',
      uri: require('../imgs/phoneFlags/AG.png'),
    },
    {
      name: 'Argentina',
      dial_code: '+54',
      code: 'AR',
      flag: '🇦🇷',
      uri: require('../imgs/phoneFlags/AR.png'),
    },
    {
      name: 'Armenia',
      dial_code: '+374',
      code: 'AM',
      flag: '🇦🇲',
      uri: require('../imgs/phoneFlags/AM.png'),
    },
    {
      name: 'Aruba',
      dial_code: '+297',
      code: 'AW',
      flag: '🇦🇼',
      uri: require('../imgs/phoneFlags/AW.png'),
    },
    {
      name: 'Australia',
      dial_code: '+61',
      code: 'AU',
      flag: '🇦🇺',
      uri: require('../imgs/phoneFlags/AU.png'),
    },
    {
      name: 'Austria',
      dial_code: '+43',
      code: 'AT',
      flag: '🇦🇹',
      uri: require('../imgs/phoneFlags/AT.png'),
    },
    {
      name: 'Azerbaijan',
      dial_code: '+994',
      code: 'AZ',
      flag: '🇦🇿',
      uri: require('../imgs/phoneFlags/AZ.png'),
    },
    {
      name: 'Bahamas',
      dial_code: '+1242',
      code: 'BS',
      flag: '🇧🇸',
      uri: require('../imgs/phoneFlags/BS.png'),
    },
    {
      name: 'Bahrain',
      dial_code: '+973',
      code: 'BH',
      flag: '🇧🇸',
      uri: require('../imgs/phoneFlags/BH.png'),
    },
    {
      name: 'Bangladesh',
      dial_code: '+880',
      code: 'BD',
      flag: '🇧🇩',
      uri: require('../imgs/phoneFlags/BD.png'),
    },
    {
      name: 'Barbados',
      dial_code: '+1246',
      code: 'BB',
      flag: '🇧🇧',
      uri: require('../imgs/phoneFlags/BB.png'),
    },
    {
      name: 'Belarus',
      dial_code: '+375',
      code: 'BY',
      flag: '🇧🇾',
      uri: require('../imgs/phoneFlags/BY.png'),
    },
    {
      name: 'Belgium',
      dial_code: '+32',
      code: 'BE',
      flag: '🇧🇪',
      uri: require('../imgs/phoneFlags/BE.png'),
    },
    {
      name: 'Belize',
      dial_code: '+501',
      code: 'BZ',
      flag: '🇧🇿',
      uri: require('../imgs/phoneFlags/BZ.png'),
    },
    {
      name: 'Benin',
      dial_code: '+229',
      code: 'BJ',
      flag: '🇧🇯',
      uri: require('../imgs/phoneFlags/BJ.png'),
    },
    {
      name: 'Bermuda',
      dial_code: '+1441',
      code: 'BM',
      flag: '🇧🇲',
      uri: require('../imgs/phoneFlags/BM.png'),
    },
    {
      name: 'Bhutan',
      dial_code: '+975',
      code: 'BT',
      flag: '🇧🇹',
      uri: require('../imgs/phoneFlags/BT.png'),
    },
    {
      name: 'Bolivia, Plurinational State of bolivia',
      dial_code: '+591',
      code: 'BO',
      flag: '🇧🇴',
      uri: require('../imgs/phoneFlags/BO.png'),
    },
    {
      name: 'Bosnia and Herzegovina',
      dial_code: '+387',
      code: 'BA',
      flag: '🇧🇦',
      uri: require('../imgs/phoneFlags/BA.png'),
    },
    {
      name: 'Botswana',
      dial_code: '+267',
      code: 'BW',
      flag: '🇧🇼',
      uri: require('../imgs/phoneFlags/BW.png'),
    },
    {
      name: 'Bouvet Island',
      dial_code: '+47',
      code: 'BV',
      flag: '🏳',
      uri: require('../imgs/phoneFlags/BV.png'),
    },
    {
      name: 'Brazil',
      dial_code: '+55',
      code: 'BR',
      flag: '🇧🇷',
      uri: require('../imgs/phoneFlags/BR.png'),
    },
    {
      name: 'British Indian Ocean Territory',
      dial_code: '+246',
      code: 'IO',
      flag: '🇮🇴',
      uri: null,
    },
    {
      name: 'Brunei Darussalam',
      dial_code: '+673',
      code: 'BN',
      flag: '🇧🇳',
      uri: require('../imgs/phoneFlags/BN.png'),
    },
    {
      name: 'Bulgaria',
      dial_code: '+359',
      code: 'BG',
      flag: '🇧🇬',
      uri: require('../imgs/phoneFlags/BG.png'),
    },
    {
      name: 'Burkina Faso',
      dial_code: '+226',
      code: 'BF',
      flag: '🇧🇫',
      uri: require('../imgs/phoneFlags/BF.png'),
    },
    {
      name: 'Burundi',
      dial_code: '+257',
      code: 'BI',
      flag: '🇧🇮',
      uri: require('../imgs/phoneFlags/BI.png'),
    },
    {
      name: 'Cambodia',
      dial_code: '+855',
      code: 'KH',
      flag: '🇰🇭',
      uri: require('../imgs/phoneFlags/KH.png'),
    },
    {
      name: 'Cameroon',
      dial_code: '+237',
      code: 'CM',
      flag: '🇨🇲',
      uri: require('../imgs/phoneFlags/CM.png'),
    },
    {
      name: 'Canada',
      dial_code: '+1',
      code: 'CA',
      flag: '🇨🇦',
      uri: require('../imgs/phoneFlags/CA.png'),
    },
    {
      name: 'Cape Verde',
      dial_code: '+238',
      code: 'CV',
      flag: '🇨🇻',
      uri: require('../imgs/phoneFlags/CV.png'),
    },
    {
      name: 'Cayman Islands',
      dial_code: '+ 345',
      code: 'KY',
      flag: '🇰🇾',
      uri: require('../imgs/phoneFlags/KY.png'),
    },
    {
      name: 'Central African Republic',
      dial_code: '+236',
      code: 'CF',
      flag: '🇨🇫',
      uri: require('../imgs/phoneFlags/CF.png'),
    },
    {
      name: 'Chad',
      dial_code: '+235',
      code: 'TD',
      flag: '🇹🇩',
      uri: require('../imgs/phoneFlags/TD.png'),
    },
    {
      name: 'Chile',
      dial_code: '+56',
      code: 'CL',
      flag: '🇨🇱',
      uri: require('../imgs/phoneFlags/CL.png'),
    },
    {
      name: 'China',
      dial_code: '+86',
      code: 'CN',
      flag: '🇨🇳',
      uri: require('../imgs/phoneFlags/CN.png'),
    },
    {
      name: 'Christmas Island',
      dial_code: '+61',
      code: 'CX',
      flag: '🇨🇽',
      uri: require('../imgs/phoneFlags/CX.png'),
    },
    {
      name: 'Cocos (Keeling) Islands',
      dial_code: '+61',
      code: 'CC',
      flag: '🇨🇨',
      uri: require('../imgs/phoneFlags/CC.png'),
    },
    {
      name: 'Colombia',
      dial_code: '+57',
      code: 'CO',
      flag: '🇨🇴',
      uri: require('../imgs/phoneFlags/CO.png'),
    },
    {
      name: 'Comoros',
      dial_code: '+269',
      code: 'KM',
      flag: '🇰🇲',
      uri: require('../imgs/phoneFlags/KM.png'),
    },
    {
      name: 'Congo',
      dial_code: '+242',
      code: 'CG',
      flag: '🇨🇬',
      uri: require('../imgs/phoneFlags/CG.png'),
    },
    {
      name: 'Congo, The Democratic Republic of the Congo',
      dial_code: '+243',
      code: 'CD',
      flag: '🇨🇩',
      uri: require('../imgs/phoneFlags/CD.png'),
    },
    {
      name: 'Cook Islands',
      dial_code: '+682',
      code: 'CK',
      flag: '🇨🇰',
      uri: require('../imgs/phoneFlags/CK.png'),
    },
    {
      name: 'Costa Rica',
      dial_code: '+506',
      code: 'CR',
      flag: '🇨🇷',
      uri: require('../imgs/phoneFlags/CR.png'),
    },
    {
      name: "Cote d'Ivoire",
      dial_code: '+225',
      code: 'CI',
      flag: '🇨🇮',
      uri: require('../imgs/phoneFlags/CI.png'),
    },
    {
      name: 'Croatia',
      dial_code: '+385',
      code: 'HR',
      flag: '🇭🇷',
      uri: require('../imgs/phoneFlags/HR.png'),
    },
    {
      name: 'Cuba',
      dial_code: '+53',
      code: 'CU',
      flag: '🇨🇺',
      uri: require('../imgs/phoneFlags/CU.png'),
    },
    {
      name: 'Cyprus',
      dial_code: '+357',
      code: 'CY',
      flag: '🇨🇾',
      uri: require('../imgs/phoneFlags/CY.png'),
    },
    {
      name: 'Czech Republic',
      dial_code: '+420',
      code: 'CZ',
      flag: '🇨🇿',
      uri: require('../imgs/phoneFlags/CZ.png'),
    },
    {
      name: 'Denmark',
      dial_code: '+45',
      code: 'DK',
      flag: '🇩🇰',
      uri: require('../imgs/phoneFlags/DK.png'),
    },
    {
      name: 'Djibouti',
      dial_code: '+253',
      code: 'DJ',
      flag: '🇩🇯',
      uri: require('../imgs/phoneFlags/DJ.png'),
    },
    {
      name: 'Dominica',
      dial_code: '+1767',
      code: 'DM',
      flag: '🇩🇲',
      uri: require('../imgs/phoneFlags/DM.png'),
    },
    {
      name: 'Dominican Republic',
      dial_code: '+1849',
      code: 'DO',
      flag: '🇨🇩',
      uri: require('../imgs/phoneFlags/DO.png'),
    },
    {
      name: 'Ecuador',
      dial_code: '+593',
      code: 'EC',
      flag: '🇪🇨',
      uri: require('../imgs/phoneFlags/EC.png'),
    },
    {
      name: 'Egypt',
      dial_code: '+20',
      code: 'EG',
      flag: '🇪🇬',
      uri: require('../imgs/phoneFlags/EG.png'),
    },
    {
      name: 'El Salvador',
      dial_code: '+503',
      code: 'SV',
      flag: '🇸🇻',
      uri: require('../imgs/phoneFlags/SV.png'),
    },
    {
      name: 'Equatorial Guinea',
      dial_code: '+240',
      code: 'GQ',
      flag: '🇬🇶',
      uri: require('../imgs/phoneFlags/GQ.png'),
    },
    {
      name: 'Eritrea',
      dial_code: '+291',
      code: 'ER',
      flag: '🇪🇷',
      uri: require('../imgs/phoneFlags/ER.png'),
    },
    {
      name: 'Estonia',
      dial_code: '+372',
      code: 'EE',
      flag: '🇪🇪',
      uri: require('../imgs/phoneFlags/EE.png'),
    },
    {
      name: 'Ethiopia',
      dial_code: '+251',
      code: 'ET',
      flag: '🇪🇹',
      uri: require('../imgs/phoneFlags/ET.png'),
    },
    {
      name: 'Falkland Islands (Malvinas)',
      dial_code: '+500',
      code: 'FK',
      flag: '🇫🇰',
      uri: require('../imgs/phoneFlags/FK.png'),
    },
    {
      name: 'Faroe Islands',
      dial_code: '+298',
      code: 'FO',
      flag: '🇫🇴',
      uri: require('../imgs/phoneFlags/FO.png'),
    },
    {
      name: 'Fiji',
      dial_code: '+679',
      code: 'FJ',
      flag: '🇫🇯',
      uri: require('../imgs/phoneFlags/FJ.png'),
    },
    {
      name: 'Finland',
      dial_code: '+358',
      code: 'FI',
      flag: '🇫🇮',
      uri: require('../imgs/phoneFlags/FI.png'),
    },
    {
      name: 'France',
      dial_code: '+33',
      code: 'FR',
      flag: '🇫🇷',
      uri: require('../imgs/phoneFlags/FR.png'),
    },
    {
      name: 'French Guiana',
      dial_code: '+594',
      code: 'GF',
      flag: '🇬🇫',
      uri: require('../imgs/phoneFlags/GF.png'),
    },
    {
      name: 'French Polynesia',
      dial_code: '+689',
      code: 'PF',
      flag: '🇵🇫',
      uri: require('../imgs/phoneFlags/PF.png'),
    },
    {
      name: 'French Southern Territories',
      dial_code: '+262',
      code: 'TF',
      flag: '🇹🇫',
      uri: require('../imgs/phoneFlags/TF.png'),
    },
    {
      name: 'Gabon',
      dial_code: '+241',
      code: 'GA',
      flag: '🇬🇦',
      uri: require('../imgs/phoneFlags/GA.png'),
    },
    {
      name: 'Gambia',
      dial_code: '+220',
      code: 'GM',
      flag: '🇬🇲',
      uri: require('../imgs/phoneFlags/GM.png'),
    },
    {
      name: 'Georgia',
      dial_code: '+995',
      code: 'GE',
      flag: '🇬🇪',
      uri: require('../imgs/phoneFlags/GE.png'),
    },
    {
      name: 'Germany',
      dial_code: '+49',
      code: 'DE',
      flag: '🇩🇪',
      uri: require('../imgs/phoneFlags/DE.png'),
    },
    {
      name: 'Ghana',
      dial_code: '+233',
      code: 'GH',
      flag: '🇬🇭',
      uri: require('../imgs/phoneFlags/GH.png'),
    },
    {
      name: 'Gibraltar',
      dial_code: '+350',
      code: 'GI',
      flag: '🇬🇮',
      uri: require('../imgs/phoneFlags/GI.png'),
    },
    {
      name: 'Greece',
      dial_code: '+30',
      code: 'GR',
      flag: '🇬🇷',
      uri: require('../imgs/phoneFlags/GR.png'),
    },
    {
      name: 'Greenland',
      dial_code: '+299',
      code: 'GL',
      flag: '🇬🇱',
      uri: require('../imgs/phoneFlags/GL.png'),
    },
    {
      name: 'Grenada',
      dial_code: '+1473',
      code: 'GD',
      flag: '🇬🇩',
      uri: require('../imgs/phoneFlags/GD.png'),
    },
    {
      name: 'Guadeloupe',
      dial_code: '+590',
      code: 'GP',
      flag: '🇬🇵',
      uri: require('../imgs/phoneFlags/GP.png'),
    },
    {
      name: 'Guam',
      dial_code: '+1671',
      code: 'GU',
      flag: '🇬🇺',
      uri: require('../imgs/phoneFlags/GU.png'),
    },
    {
      name: 'Guatemala',
      dial_code: '+502',
      code: 'GT',
      flag: '🇬🇹',
      uri: require('../imgs/phoneFlags/GT.png'),
    },
    {
      name: 'Guernsey',
      dial_code: '+44',
      code: 'GG',
      flag: '🇬🇬',
      uri: require('../imgs/phoneFlags/GG.png'),
    },
    {
      name: 'Guinea',
      dial_code: '+224',
      code: 'GN',
      flag: '🇬🇳',
      uri: require('../imgs/phoneFlags/GN.png'),
    },
    {
      name: 'Guinea-Bissau',
      dial_code: '+245',
      code: 'GW',
      flag: '🇬🇼',
      uri: require('../imgs/phoneFlags/GW.png'),
    },
    {
      name: 'Guyana',
      dial_code: '+592',
      code: 'GY',
      flag: '🇬🇾',
      uri: require('../imgs/phoneFlags/GY.png'),
    },
    {
      name: 'Haiti',
      dial_code: '+509',
      code: 'HT',
      flag: '🇭🇹',
      uri: require('../imgs/phoneFlags/HT.png'),
    },
    {
      name: 'Heard Island and Mcdonald Islands',
      dial_code: '+0',
      code: 'HM',
      flag: '🏳',
      uri: require('../imgs/phoneFlags/HM.png'),
    },
    {
      name: 'Holy See (Vatican City State)',
      dial_code: '+379',
      code: 'VA',
      flag: '🇻🇦',
      uri: null,
    },
    {
      name: 'Honduras',
      dial_code: '+504',
      code: 'HN',
      flag: '🇭🇳',
      uri: require('../imgs/phoneFlags/HN.png'),
    },
    {
      name: 'Hong Kong',
      dial_code: '+852',
      code: 'HK',
      flag: '🇭🇰',
      uri: require('../imgs/phoneFlags/HK.png'),
    },
    {
      name: 'Hungary',
      dial_code: '+36',
      code: 'HU',
      flag: '🇭🇺',
      uri: require('../imgs/phoneFlags/HU.png'),
    },
    {
      name: 'Iceland',
      dial_code: '+354',
      code: 'IS',
      flag: '🇮🇸',
      uri: require('../imgs/phoneFlags/IS.png'),
    },
    {
      name: 'India',
      dial_code: '+91',
      code: 'IN',
      flag: '🇮🇳',
      uri: require('../imgs/phoneFlags/IN.png'),
    },
    {
      name: 'Indonesia',
      dial_code: '+62',
      code: 'ID',
      flag: '🇮🇩',
      uri: require('../imgs/phoneFlags/ID.png'),
    },
    {
      name: 'Iran, Islamic Republic of Persian Gulf',
      dial_code: '+98',
      code: 'IR',
      flag: '🇮🇷',
      uri: require('../imgs/phoneFlags/IR.png'),
    },
    {
      name: 'Iraq',
      dial_code: '+964',
      code: 'IQ',
      flag: '🇮🇶',
      uri: require('../imgs/phoneFlags/IQ.png'),
    },
    {
      name: 'Ireland',
      dial_code: '+353',
      code: 'IE',
      flag: '🇮🇪',
      uri: require('../imgs/phoneFlags/IE.png'),
    },
    {
      name: 'Isle of Man',
      dial_code: '+44',
      code: 'IM',
      flag: '🇮🇲',
      uri: null,
    },
    {
      name: 'Israel',
      dial_code: '+972',
      code: 'IL',
      flag: '🇮🇱',
      uri: require('../imgs/phoneFlags/IL.png'),
    },
    {
      name: 'Italy',
      dial_code: '+39',
      code: 'IT',
      flag: '🇮🇹',
      uri: require('../imgs/phoneFlags/IT.png'),
    },
    {
      name: 'Jamaica',
      dial_code: '+1876',
      code: 'JM',
      flag: '🇯🇲',
      uri: require('../imgs/phoneFlags/JM.png'),
    },
    {
      name: 'Japan',
      dial_code: '+81',
      code: 'JP',
      flag: '🇯🇵',
      uri: require('../imgs/phoneFlags/JP.png'),
    },
    {
      name: 'Jersey',
      dial_code: '+44',
      code: 'JE',
      flag: '🇯🇪',
      uri: require('../imgs/phoneFlags/JE.png'),
    },
    {
      name: 'Jordan',
      dial_code: '+962',
      code: 'JO',
      flag: '🇯🇴',
      uri: require('../imgs/phoneFlags/JO.png'),
    },
    {
      name: 'Kazakhstan',
      dial_code: '+7',
      code: 'KZ',
      flag: '🇰🇿',
      uri: require('../imgs/phoneFlags/KZ.png'),
    },
    {
      name: 'Kenya',
      dial_code: '+254',
      code: 'KE',
      flag: '🇰🇪',
      uri: require('../imgs/phoneFlags/KE.png'),
    },
    {
      name: 'Kiribati',
      dial_code: '+686',
      code: 'KI',
      flag: '🇰🇮',
      uri: require('../imgs/phoneFlags/KI.png'),
    },
    {
      name: "Korea, Democratic People's Republic of Korea",
      dial_code: '+850',
      code: 'KP',
      flag: '🇰🇵',
      uri: require('../imgs/phoneFlags/KP.png'),
    },
    {
      name: 'Korea, Republic of South Korea',
      dial_code: '+82',
      code: 'KR',
      flag: '🇰🇷',
      uri: require('../imgs/phoneFlags/KR.png'),
    },
    {
      name: 'Kosovo',
      dial_code: '+383',
      code: 'XK',
      flag: '🇽🇰',
      uri: require('../imgs/phoneFlags/XK.png'),
    },
    {
      name: 'Kuwait',
      dial_code: '+965',
      code: 'KW',
      flag: '🇰🇼',
      uri: require('../imgs/phoneFlags/KW.png'),
    },
    {
      name: 'Kyrgyzstan',
      dial_code: '+996',
      code: 'KG',
      flag: '🇰🇬',
      uri: require('../imgs/phoneFlags/KG.png'),
    },
    {
      name: 'Laos',
      dial_code: '+856',
      code: 'LA',
      flag: '🇱🇦',
      uri: require('../imgs/phoneFlags/LA.png'),
    },
    {
      name: 'Latvia',
      dial_code: '+371',
      code: 'LV',
      flag: '🇱🇻',
      uri: require('../imgs/phoneFlags/LV.png'),
    },
    {
      name: 'Lebanon',
      dial_code: '+961',
      code: 'LB',
      flag: '🇱🇧',
      uri: require('../imgs/phoneFlags/LB.png'),
    },
    {
      name: 'Lesotho',
      dial_code: '+266',
      code: 'LS',
      flag: '🇱🇸',
      uri: require('../imgs/phoneFlags/LS.png'),
    },
    {
      name: 'Liberia',
      dial_code: '+231',
      code: 'LR',
      flag: '🇱🇷',
      uri: require('../imgs/phoneFlags/LR.png'),
    },
    {
      name: 'Libyan Arab Jamahiriya',
      dial_code: '+218',
      code: 'LY',
      flag: '🇱🇾',
      uri: require('../imgs/phoneFlags/LY.png'),
    },
    {
      name: 'Liechtenstein',
      dial_code: '+423',
      code: 'LI',
      flag: '🇱🇮',
      uri: require('../imgs/phoneFlags/LI.png'),
    },
    {
      name: 'Lithuania',
      dial_code: '+370',
      code: 'LT',
      flag: '🇱🇹',
      uri: require('../imgs/phoneFlags/LT.png'),
    },
    {
      name: 'Luxembourg',
      dial_code: '+352',
      code: 'LU',
      flag: '🇱🇺',
      uri: require('../imgs/phoneFlags/LU.png'),
    },
    {
      name: 'Macao',
      dial_code: '+853',
      code: 'MO',
      flag: '🇲🇴',
      uri: require('../imgs/phoneFlags/MO.png'),
    },
    {
      name: 'Macedonia',
      dial_code: '+389',
      code: 'MK',
      flag: '🇲🇰',
      uri: require('../imgs/phoneFlags/MK.png'),
    },
    {
      name: 'Madagascar',
      dial_code: '+261',
      code: 'MG',
      flag: '🇲🇬',
      uri: require('../imgs/phoneFlags/MG.png'),
    },
    {
      name: 'Malawi',
      dial_code: '+265',
      code: 'MW',
      flag: '🇲🇼',
      uri: require('../imgs/phoneFlags/MW.png'),
    },
    {
      name: 'Malaysia',
      dial_code: '+60',
      code: 'MY',
      flag: '🇲🇾',
      uri: require('../imgs/phoneFlags/MY.png'),
    },
    {
      name: 'Maldives',
      dial_code: '+960',
      code: 'MV',
      flag: '🇲🇻',
      uri: require('../imgs/phoneFlags/MV.png'),
    },
    {
      name: 'Mali',
      dial_code: '+223',
      code: 'ML',
      flag: '🇲🇱',
      uri: require('../imgs/phoneFlags/ML.png'),
    },
    {
      name: 'Malta',
      dial_code: '+356',
      code: 'MT',
      flag: '🇲🇹',
      uri: require('../imgs/phoneFlags/MT.png'),
    },
    {
      name: 'Marshall Islands',
      dial_code: '+692',
      code: 'MH',
      flag: '🇲🇭',
      uri: require('../imgs/phoneFlags/MH.png'),
    },
    {
      name: 'Martinique',
      dial_code: '+596',
      code: 'MQ',
      flag: '🇲🇶',
      uri: require('../imgs/phoneFlags/MQ.png'),
    },
    {
      name: 'Mauritania',
      dial_code: '+222',
      code: 'MR',
      flag: '🇲🇷',
      uri: require('../imgs/phoneFlags/MR.png'),
    },
    {
      name: 'Mauritius',
      dial_code: '+230',
      code: 'MU',
      flag: '🇲🇺',
      uri: require('../imgs/phoneFlags/MU.png'),
    },
    {
      name: 'Mayotte',
      dial_code: '+262',
      code: 'YT',
      flag: '🇾🇹',
      uri: require('../imgs/phoneFlags/YT.png'),
    },
    {
      name: 'Mexico',
      dial_code: '+52',
      code: 'MX',
      flag: '🇲🇽',
      uri: require('../imgs/phoneFlags/MX.png'),
    },
    {
      name: 'Micronesia, Federated States of Micronesia',
      dial_code: '+691',
      code: 'FM',
      flag: '🇫🇲',
      uri: require('../imgs/phoneFlags/FM.png'),
    },
    {
      name: 'Moldova',
      dial_code: '+373',
      code: 'MD',
      flag: '🇲🇩',
      uri: require('../imgs/phoneFlags/MD.png'),
    },
    {
      name: 'Monaco',
      dial_code: '+377',
      code: 'MC',
      flag: '🇲🇨',
      uri: require('../imgs/phoneFlags/MC.png'),
    },
    {
      name: 'Mongolia',
      dial_code: '+976',
      code: 'MN',
      flag: '🇲🇳',
      uri: require('../imgs/phoneFlags/MN.png'),
    },
    {
      name: 'Montenegro',
      dial_code: '+382',
      code: 'ME',
      flag: '🇲🇪',
      uri: require('../imgs/phoneFlags/ME.png'),
    },
    {
      name: 'Montserrat',
      dial_code: '+1664',
      code: 'MS',
      flag: '🇲🇸',
      uri: require('../imgs/phoneFlags/MS.png'),
    },
    {
      name: 'Morocco',
      dial_code: '+212',
      code: 'MA',
      flag: '🇲🇦',
      uri: require('../imgs/phoneFlags/MA.png'),
    },
    {
      name: 'Mozambique',
      dial_code: '+258',
      code: 'MZ',
      flag: '🇲🇿',
      uri: require('../imgs/phoneFlags/MZ.png'),
    },
    {
      name: 'Myanmar',
      dial_code: '+95',
      code: 'MM',
      flag: '🇲🇲',
      uri: require('../imgs/phoneFlags/MM.png'),
    },
    {
      name: 'Namibia',
      dial_code: '+264',
      code: 'NA',
      flag: '🇳🇦',
      uri: require('../imgs/phoneFlags/NA.png'),
    },
    {
      name: 'Nauru',
      dial_code: '+674',
      code: 'NR',
      flag: '🇳🇷',
      uri: require('../imgs/phoneFlags/NR.png'),
    },
    {
      name: 'Nepal',
      dial_code: '+977',
      code: 'NP',
      flag: '🇳🇵',
      uri: require('../imgs/phoneFlags/NP.png'),
    },
    {
      name: 'Netherlands',
      dial_code: '+31',
      code: 'NL',
      flag: '🇳🇱',
      uri: require('../imgs/phoneFlags/NL.png'),
    },
    {
      name: 'Netherlands Antilles',
      dial_code: '+599',
      code: 'AN',
      flag: '🇳🇱',
      uri: null,
    },
    {
      name: 'New Caledonia',
      dial_code: '+687',
      code: 'NC',
      flag: '🇳🇨',
      uri: require('../imgs/phoneFlags/NC.png'),
    },
    {
      name: 'New Zealand',
      dial_code: '+64',
      code: 'NZ',
      flag: '🇳🇿',
      uri: require('../imgs/phoneFlags/NZ.png'),
    },
    {
      name: 'Nicaragua',
      dial_code: '+505',
      code: 'NI',
      flag: '🇳🇮',
      uri: require('../imgs/phoneFlags/NI.png'),
    },
    {
      name: 'Niger',
      dial_code: '+227',
      code: 'NE',
      flag: '🇳🇪',
      uri: require('../imgs/phoneFlags/NE.png'),
    },
    {
      name: 'Nigeria',
      dial_code: '+234',
      code: 'NG',
      flag: '🇳🇬',
      uri: require('../imgs/phoneFlags/NG.png'),
    },
    {
      name: 'Niue',
      dial_code: '+683',
      code: 'NU',
      flag: '🇳🇺',
      uri: require('../imgs/phoneFlags/NU.png'),
    },
    {
      name: 'Norfolk Island',
      dial_code: '+672',
      code: 'NF',
      flag: '🇳🇫',
      uri: require('../imgs/phoneFlags/NF.png'),
    },
    {
      name: 'Northern Mariana Islands',
      dial_code: '+1670',
      code: 'MP',
      flag: '🏳',
      uri: require('../imgs/phoneFlags/MP.png'),
    },
    {
      name: 'Norway',
      dial_code: '+47',
      code: 'NO',
      flag: '🇳🇴',
      uri: require('../imgs/phoneFlags/NO.png'),
    },
    {
      name: 'Oman',
      dial_code: '+968',
      code: 'OM',
      flag: '🇴🇲',
      uri: require('../imgs/phoneFlags/OM.png'),
    },
    {
      name: 'Pakistan',
      dial_code: '+92',
      code: 'PK',
      flag: '🇵🇰',
      uri: require('../imgs/phoneFlags/PK.png'),
    },
    {
      name: 'Palau',
      dial_code: '+680',
      code: 'PW',
      flag: '🇵🇼',
      uri: require('../imgs/phoneFlags/PW.png'),
    },
    {
      name: 'Palestinian Territory, Occupied',
      dial_code: '+970',
      code: 'PS',
      flag: '🇵🇸',
      uri: require('../imgs/phoneFlags/PS.png'),
    },
    {
      name: 'Panama',
      dial_code: '+507',
      code: 'PA',
      flag: '🇵🇦',
      uri: require('../imgs/phoneFlags/PA.png'),
    },
    {
      name: 'Papua New Guinea',
      dial_code: '+675',
      code: 'PG',
      flag: '🇵🇬',
      uri: require('../imgs/phoneFlags/PG.png'),
    },
    {
      name: 'Paraguay',
      dial_code: '+595',
      code: 'PY',
      flag: '🇵🇾',
      uri: require('../imgs/phoneFlags/PY.png'),
    },
    {
      name: 'Peru',
      dial_code: '+51',
      code: 'PE',
      flag: '🇵🇪',
      uri: require('../imgs/phoneFlags/PE.png'),
    },
    {
      name: 'Philippines',
      dial_code: '+63',
      code: 'PH',
      flag: '🇵🇭',
      uri: require('../imgs/phoneFlags/PH.png'),
    },
    {
      name: 'Pitcairn',
      dial_code: '+64',
      code: 'PN',
      flag: '🇵🇳',
      uri: require('../imgs/phoneFlags/PN.png'),
    },
    {
      name: 'Poland',
      dial_code: '+48',
      code: 'PL',
      flag: '🇵🇱',
      uri: require('../imgs/phoneFlags/PL.png'),
    },
    {
      name: 'Portugal',
      dial_code: '+351',
      code: 'PT',
      flag: '🇵🇹',
      uri: require('../imgs/phoneFlags/PT.png'),
    },
    {
      name: 'Puerto Rico',
      dial_code: '+1939',
      code: 'PR',
      flag: '🇵🇷',
      uri: require('../imgs/phoneFlags/PR.png'),
    },
    {
      name: 'Qatar',
      dial_code: '+974',
      code: 'QA',
      flag: '🇶🇦',
      uri: require('../imgs/phoneFlags/QA.png'),
    },
    {
      name: 'Romania',
      dial_code: '+40',
      code: 'RO',
      flag: '🇷🇴',
      uri: require('../imgs/phoneFlags/RO.png'),
    },
    {
      name: 'Russia',
      dial_code: '+7',
      code: 'RU',
      flag: '🇷🇺',
      uri: require('../imgs/phoneFlags/RU.png'),
    },
    {
      name: 'Rwanda',
      dial_code: '+250',
      code: 'RW',
      flag: '🇷🇼',
      uri: require('../imgs/phoneFlags/RW.png'),
    },
    {
      name: 'Reunion',
      dial_code: '+262',
      code: 'RE',
      flag: '🇫🇷',
      uri: require('../imgs/phoneFlags/RE.png'),
    },
    {
      name: 'Saint Barthelemy',
      dial_code: '+590',
      code: 'BL',
      flag: '🇧🇱',
      uri: require('../imgs/phoneFlags/BL.png'),
    },
    {
      name: 'Saint Helena, Ascension and Tristan Da Cunha',
      dial_code: '+290',
      code: 'SH',
      flag: '🇸🇭',
      uri: require('../imgs/phoneFlags/SH.png'),
    },
    {
      name: 'Saint Kitts and Nevis',
      dial_code: '+1869',
      code: 'KN',
      flag: '🇰🇳',
      uri: require('../imgs/phoneFlags/KN.png'),
    },
    {
      name: 'Saint Lucia',
      dial_code: '+1758',
      code: 'LC',
      flag: '🇱🇨',
      uri: require('../imgs/phoneFlags/LC.png'),
    },
    {
      name: 'Saint Martin',
      dial_code: '+590',
      code: 'MF',
      flag: '🏳',
      uri: require('../imgs/phoneFlags/MF.png'),
    },
    {
      name: 'Saint Pierre and Miquelon',
      dial_code: '+508',
      code: 'PM',
      flag: '🇵🇲',
      uri: require('../imgs/phoneFlags/PM.png'),
    },
    {
      name: 'Saint Vincent and the Grenadines',
      dial_code: '+1784',
      code: 'VC',
      flag: '🇻🇨',
      uri: require('../imgs/phoneFlags/VC.png'),
    },
    {
      name: 'Samoa',
      dial_code: '+685',
      code: 'WS',
      flag: '🇼🇸',
      uri: require('../imgs/phoneFlags/WS.png'),
    },
    {
      name: 'San Marino',
      dial_code: '+378',
      code: 'SM',
      flag: '🇸🇲',
      uri: require('../imgs/phoneFlags/SM.png'),
    },
    {
      name: 'Sao Tome and Principe',
      dial_code: '+239',
      code: 'ST',
      flag: '🇸🇹',
      uri: require('../imgs/phoneFlags/ST.png'),
    },
    {
      name: 'Saudi Arabia',
      dial_code: '+966',
      code: 'SA',
      flag: '🇸🇦',
      uri: require('../imgs/phoneFlags/SA.png'),
    },
    {
      name: 'Senegal',
      dial_code: '+221',
      code: 'SN',
      flag: '🇸🇳',
      uri: require('../imgs/phoneFlags/SN.png'),
    },
    {
      name: 'Serbia',
      dial_code: '+381',
      code: 'RS',
      flag: '🇷🇸',
      uri: require('../imgs/phoneFlags/RS.png'),
    },
    {
      name: 'Seychelles',
      dial_code: '+248',
      code: 'SC',
      flag: '🇸🇨',
      uri: require('../imgs/phoneFlags/SC.png'),
    },
    {
      name: 'Sierra Leone',
      dial_code: '+232',
      code: 'SL',
      flag: '🇸🇱',
      uri: require('../imgs/phoneFlags/SL.png'),
    },
    {
      name: 'Singapore',
      dial_code: '+65',
      code: 'SG',
      flag: '🇸🇬',
      uri: require('../imgs/phoneFlags/SG.png'),
    },
    {
      name: 'Slovakia',
      dial_code: '+421',
      code: 'SK',
      flag: '🇸🇰',
      uri: require('../imgs/phoneFlags/SK.png'),
    },
    {
      name: 'Slovenia',
      dial_code: '+386',
      code: 'SI',
      flag: '🇸🇮',
      uri: require('../imgs/phoneFlags/SI.png'),
    },
    {
      name: 'Solomon Islands',
      dial_code: '+677',
      code: 'SB',
      flag: '🇸🇧',
      uri: require('../imgs/phoneFlags/SB.png'),
    },
    {
      name: 'Somalia',
      dial_code: '+252',
      code: 'SO',
      flag: '🇸🇴',
      uri: require('../imgs/phoneFlags/SO.png'),
    },
    {
      name: 'South Africa',
      dial_code: '+27',
      code: 'ZA',
      flag: '🇿🇦',
      uri: require('../imgs/phoneFlags/ZA.png'),
    },
    {
      name: 'South Sudan',
      dial_code: '+211',
      code: 'SS',
      flag: '🇸🇸',
      uri: require('../imgs/phoneFlags/SS.png'),
    },
    {
      name: 'South Georgia and the South Sandwich Islands',
      dial_code: '+500',
      code: 'GS',
      flag: '🇬🇸',
      uri: require('../imgs/phoneFlags/GS.png'),
    },
    {
      name: 'Spain',
      dial_code: '+34',
      code: 'ES',
      flag: '🇪🇸',
      uri: require('../imgs/phoneFlags/ES.png'),
    },
    {
      name: 'Sri Lanka',
      dial_code: '+94',
      code: 'LK',
      flag: '🇱🇰',
      uri: require('../imgs/phoneFlags/LK.png'),
    },
    {
      name: 'Sudan',
      dial_code: '+249',
      code: 'SD',
      flag: '🇸🇩',
      uri: require('../imgs/phoneFlags/SD.png'),
    },
    {
      name: 'Suriname',
      dial_code: '+597',
      code: 'SR',
      flag: '🇸🇷',
      uri: require('../imgs/phoneFlags/SR.png'),
    },
    {
      name: 'Svalbard and Jan Mayen',
      dial_code: '+47',
      code: 'SJ',
      flag: '🇩🇰',
      uri: require('../imgs/phoneFlags/SJ.png'),
    },
    {
      name: 'Swaziland',
      dial_code: '+268',
      code: 'SZ',
      flag: '🇸🇿',
      uri: require('../imgs/phoneFlags/SZ.png'),
    },
    {
      name: 'Sweden',
      dial_code: '+46',
      code: 'SE',
      flag: '🇸🇪',
      uri: require('../imgs/phoneFlags/SE.png'),
    },
    {
      name: 'Switzerland',
      dial_code: '+41',
      code: 'CH',
      flag: '🇨🇭',
      uri: require('../imgs/phoneFlags/CH.png'),
    },
    {
      name: 'Syrian Arab Republic',
      dial_code: '+963',
      code: 'SY',
      flag: '🇸🇾',
      uri: require('../imgs/phoneFlags/SY.png'),
    },
    {
      name: 'Taiwan',
      dial_code: '+886',
      code: 'TW',
      flag: '🇹🇼',
      uri: require('../imgs/phoneFlags/TW.png'),
    },
    {
      name: 'Tajikistan',
      dial_code: '+992',
      code: 'TJ',
      flag: '🇹🇯',
      uri: require('../imgs/phoneFlags/TJ.png'),
    },
    {
      name: 'Tanzania, United Republic of Tanzania',
      dial_code: '+255',
      code: 'TZ',
      flag: '🇹🇿',
      uri: require('../imgs/phoneFlags/TZ.png'),
    },
    {
      name: 'Thailand',
      dial_code: '+66',
      code: 'TH',
      flag: '🇹🇭',
      uri: require('../imgs/phoneFlags/TH.png'),
    },
    {
      name: 'Timor-Leste',
      dial_code: '+670',
      code: 'TL',
      flag: '🇹🇱',
      uri: require('../imgs/phoneFlags/TL.png'),
    },
    {
      name: 'Togo',
      dial_code: '+228',
      code: 'TG',
      flag: '🇹🇬',
      uri: require('../imgs/phoneFlags/TG.png'),
    },
    {
      name: 'Tokelau',
      dial_code: '+690',
      code: 'TK',
      flag: '🇹🇰',
      uri: require('../imgs/phoneFlags/TK.png'),
    },
    {
      name: 'Tonga',
      dial_code: '+676',
      code: 'TO',
      flag: '🇹🇴',
      uri: require('../imgs/phoneFlags/TO.png'),
    },
    {
      name: 'Trinidad and Tobago',
      dial_code: '+1868',
      code: 'TT',
      flag: '🇹🇹',
      uri: require('../imgs/phoneFlags/TT.png'),
    },
    {
      name: 'Tunisia',
      dial_code: '+216',
      code: 'TN',
      flag: '🇹🇳',
      uri: require('../imgs/phoneFlags/TN.png'),
    },
    {
      name: 'Turkey',
      dial_code: '+90',
      code: 'TR',
      flag: '🇹🇷',
      uri: require('../imgs/phoneFlags/TR.png'),
    },
    {
      name: 'Turkmenistan',
      dial_code: '+993',
      code: 'TM',
      flag: '🇹🇲',
      uri: require('../imgs/phoneFlags/TM.png'),
    },
    {
      name: 'Turks and Caicos Islands',
      dial_code: '+1649',
      code: 'TC',
      flag: '🇹🇨',
      uri: require('../imgs/phoneFlags/TC.png'),
    },
    {
      name: 'Tuvalu',
      dial_code: '+688',
      code: 'TV',
      flag: '🇹🇻',
      uri: require('../imgs/phoneFlags/TV.png'),
    },
    {
      name: 'Uganda',
      dial_code: '+256',
      code: 'UG',
      flag: '🇺🇬',
      uri: require('../imgs/phoneFlags/UG.png'),
    },
    {
      name: 'Ukraine',
      dial_code: '+380',
      code: 'UA',
      flag: '🇺🇦',
      uri: require('../imgs/phoneFlags/UA.png'),
    },
    {
      name: 'United Arab Emirates',
      dial_code: '+971',
      code: 'AE',
      flag: '🇦🇪',
      uri: require('../imgs/phoneFlags/AE.png'),
    },
    {
      name: 'United Kingdom',
      dial_code: '+44',
      code: 'GB',
      flag: '🇬🇧',
      uri: require('../imgs/phoneFlags/GB.png'),
    },
    {
      name: 'United States',
      dial_code: '+1',
      code: 'US',
      flag: '🇺🇸',
      uri: require('../imgs/phoneFlags/US.png'),
    },
    {
      name: 'Uruguay',
      dial_code: '+598',
      code: 'UY',
      flag: '🇺🇾',
      uri: require('../imgs/phoneFlags/UY.png'),
    },
    {
      name: 'Uzbekistan',
      dial_code: '+998',
      code: 'UZ',
      flag: '🇺🇿',
      uri: require('../imgs/phoneFlags/UZ.png'),
    },
    {
      name: 'Vanuatu',
      dial_code: '+678',
      code: 'VU',
      flag: '🇻🇺',
      uri: require('../imgs/phoneFlags/VU.png'),
    },
    {
      name: 'Venezuela, Bolivarian Republic of Venezuela',
      dial_code: '+58',
      code: 'VE',
      flag: '🇻🇪',
      uri: require('../imgs/phoneFlags/VE.png'),
    },
    {
      name: 'Vietnam',
      dial_code: '+84',
      code: 'VN',
      flag: '🇻🇳',
      uri: require('../imgs/phoneFlags/VN.png'),
    },
    {
      name: 'Virgin Islands, British',
      dial_code: '+1284',
      code: 'VG',
      flag: '🇻🇬',
      uri: require('../imgs/phoneFlags/VG.png'),
    },
    {
      name: 'Virgin Islands, U.S.',
      dial_code: '+1340',
      code: 'VI',
      flag: '🇻🇮',
      uri: require('../imgs/phoneFlags/VI.png'),
    },
    {
      name: 'Wallis and Futuna',
      dial_code: '+681',
      code: 'WF',
      flag: '🇼🇫',
      uri: require('../imgs/phoneFlags/WF.png'),
    },
    {
      name: 'Yemen',
      dial_code: '+967',
      code: 'YE',
      flag: '🇾🇪',
      uri: require('../imgs/phoneFlags/YE.png'),
    },
    {
      name: 'Zambia',
      dial_code: '+260',
      code: 'ZM',
      flag: '🇿🇲',
      uri: require('../imgs/phoneFlags/ZM.png'),
    },
    {
      name: 'Zimbabwe',
      dial_code: '+263',
      code: 'ZW',
      flag: '🇿🇼',
      uri: require('../imgs/phoneFlags/ZW.png'),
    },
  ]
}
