import btcLogo from '../imgs/logos/main/btc.png'
import bchLogo from '../imgs/logos/main/bch.png'
import ethLogo from '../imgs/logos/main/eth.png'
import ltcLogo from '../imgs/logos/main/ltc.png'
import saltLogo from '../imgs/logos/main/salt.png'
import doge<PERSON>ogo from '../imgs/logos/main/doge.png'
import usdcLogo from '../imgs/logos/main/usdc.png'
import tusdLogo from '../imgs/logos/main/tusd.png'
import dashLogo from '../imgs/logos/main/dash.png'
import paxLogo from '../imgs/logos/main/pax.png'
import paxGoldLogo from '../imgs/logos/main/paxg.png'
import xrpLogo from '../imgs/logos/main/xrp.png'
import xrpWhiteLogo from '../imgs/logos/main/xrpWhite.png'

import usdtLogo from '../imgs/logos/main/usdt.png'
import prefLogo from '../imgs/logos/main/pref.png'

import btcDetailLogo from '../imgs/logos/white/btc-white.png'
import bchDetailLogo from '../imgs/logos/white/bch-white.png'
import ethDetailLogo from '../imgs/logos/white/eth-white.png'
import ltcDetailLogo from '../imgs/logos/white/ltc-white.png'
import dogeDetailLogo from '../imgs/logos/white/doge-white.png'
import usdcDetailLogo from '../imgs/logos/white/usdc-white.png'
import tusdDetailLogo from '../imgs/logos/white/tusd-white.png'
import saltDetailLogo from '../imgs/logos/white/salt-white.png'
import dashDetailLogo from '../imgs/logos/white/dash-white.png'
import paxDetailLogo from '../imgs/logos/white/pax-white.png'
import xrpDetailLogo from '../imgs/logos/white/xrp-white.png'
import usdtDetailLogo from '../imgs/logos/white/usdt-white.png'
import prefDetailLogo from '../imgs/logos/white/pref-white.png'

import btcDepositImg from '../imgs/logos/depositCollateral/BTC.png'
import bchDepositImg from '../imgs/logos/depositCollateral/BCH.png'
import ethDepositImg from '../imgs/logos/depositCollateral/ETH.png'
import ltcDepositImg from '../imgs/logos/depositCollateral/LTC.png'
import dashDepositImg from '../imgs/logos/depositCollateral/DASH.png'
import dogeDepositImg from '../imgs/logos/depositCollateral/DOGE.png'
import tusdDepositImg from '../imgs/logos/depositCollateral/TUSD.png'
import usdcDepositImg from '../imgs/logos/depositCollateral/USDC.png'
import paxDepositImg from '../imgs/logos/depositCollateral/PAX.png'
import paxGoldDepositImg from '../imgs/logos/depositCollateral/PAXG.png'
import saltDepositImg from '../imgs/logos/depositCollateral/SALT.png'
import xrpDepositImg from '../imgs/logos/depositCollateral/XRP.png'
import usdtDepositImg from '../imgs/logos/depositCollateral/USDT.png'
import prefDepositImg from '../imgs/logos/depositCollateral/PREF.png'

import zaboKraken from '../imgs/zabo/accounts/kraken.png'
import zaboLedger from '../imgs/zabo/accounts/ledger.png'
import zaboGemini from '../imgs/zabo/accounts/gemini.png'
import zaboLiquid from '../imgs/zabo/accounts/liquid.png'
import zaboBitmex from '../imgs/zabo/accounts/bitmex.png'
import zaboMetamask from '../imgs/zabo/accounts/metamask.png'
import zaboBittrex from '../imgs/zabo/accounts/bittrex.png'
import zaboBinance from '../imgs/zabo/accounts/binance.png'
import zaboCoinbase from '../imgs/zabo/accounts/coinbase.png'
import zaboTrezor from '../imgs/zabo/accounts/trezor.png'
import zaboBitstamp from '../imgs/zabo/accounts/bitstamp.png'
import zaboBitfinex from '../imgs/zabo/accounts/bitfinex.png'
import zaboBitcoin from '../imgs/zabo/accounts/bitcoin.png'
import zaboHuobi from '../imgs/zabo/accounts/huobi.png'
import zaboKucoin from '../imgs/zabo/accounts/kucoin.png'
import zaboBitgo from '../imgs/zabo/accounts/bitgo.png'
import zaboFrost from '../imgs/zabo/accounts/frost.png'
import zaboEqual from '../imgs/zabo/accounts/equal.png'
import zaboPublicKey from '../imgs/zabo/accounts/pubKey.png'
import zaboCoinsquare from '../imgs/zabo/accounts/coinsquare.png'
import zaboNifty from '../imgs/zabo/accounts/nifty.png'
import zaboMew from '../imgs/zabo/accounts/mew.png'
import zaboBybit from '../imgs/zabo/accounts/bybit.png'
import zaboGoWallet from '../imgs/zabo/accounts/goWallet.png'
import zaboTrust from '../imgs/zabo/accounts/trust.png'
import zaboAlphaWallet from '../imgs/zabo/accounts/alphaWallet.png'
import zaboStatus from '../imgs/zabo/accounts/status.png'
import zaboDapper from '../imgs/zabo/accounts/dapper.png'
import zaboGateIo from '../imgs/zabo/accounts/gateIo.png'

export const getDepositCollateralPic = token => {
  token = token.toUpperCase()
  switch (token) {
    case 'BTC':
      return btcDepositImg
    case 'BCH':
      return bchDepositImg
    case 'ETH':
      return ethDepositImg
    case 'LTC':
      return ltcDepositImg
    case 'DASH':
      return dashDepositImg
    case 'DOGE':
      return dogeDepositImg
    case 'USDC':
      return usdcDepositImg
    case 'TUSD':
      return tusdDepositImg
    case 'USDP':
      return paxDepositImg
    case 'PAXG':
      return paxGoldDepositImg
    case 'SALT':
      return saltDepositImg
    case 'XRP':
      return xrpDepositImg
    case 'USDT':
      return usdtDepositImg
    case 'PREF':
      return prefDepositImg
    default:
      return btcDepositImg
  }
}

export const getTokenPic = token => {
  token = token?.toUpperCase()
  switch (token) {
    case 'BTC':
      return btcLogo
    case 'BCH':
      return bchLogo
    case 'ETH':
      return ethLogo
    case 'SALT':
      return saltLogo
    case 'LTC':
      return ltcLogo
    case 'DASH':
      return dashLogo
    case 'DOGE':
      return dogeLogo
    case 'USDC':
      return usdcLogo
    case 'TUSD':
      return tusdLogo
    case 'USDP':
      return paxLogo
    case 'PAXG':
      return paxGoldLogo
    case 'XRP':
      return xrpLogo
    case 'XRPW':
      return xrpWhiteLogo
    case 'USDT':
      return usdtLogo
    case 'PREF':
      return prefLogo
    default:
      return btcLogo
  }
}

export const getZaboAccountLogo = account => {
  switch (account) {
    case 'bittrex':
      return zaboBittrex
    case 'binance':
      return zaboBinance
    case 'coinbase':
      return zaboCoinbase
    case 'kraken':
      return zaboKraken
    case 'gemini':
      return zaboGemini
    case 'ledger':
      return zaboLedger
    case 'trezor':
      return zaboTrezor
    case 'bitstamp':
      return zaboBitstamp
    case 'metamask':
      return zaboMetamask
    case 'bitfinex':
      return zaboBitfinex
    case 'bitmex':
      return zaboBitmex
    case 'bitcoin':
      return zaboBitcoin
    case 'ethereum':
      return ethLogo
    case 'coinbasePro':
      return zaboCoinbase
    case 'huobi':
      return zaboHuobi
    case 'kucoin':
      return zaboKucoin
    case 'liquid':
      return zaboLiquid
    case 'bitgo':
      return zaboBitgo
    case 'coinsquare':
      return zaboCoinsquare
    case 'publicKey':
      return zaboPublicKey
    case 'binanceUS':
      return zaboBinance
    case 'equal':
      return zaboEqual
    case 'nifty':
      return zaboNifty
    case 'mew':
      return zaboMew
    case 'frost':
      return zaboFrost
    case 'bybit':
      return zaboBybit
    case 'goWallet':
      return zaboGoWallet
    case 'trust':
      return zaboTrust
    case 'alphaWallet':
      return zaboAlphaWallet
    case 'status':
      return zaboStatus
    case 'dapper':
      return zaboDapper
    case 'gateio':
      return zaboGateIo
    case 'coinbasePrime':
      return zaboCoinbase
    default:
      return null
  }
}

export const getTokenDetailLogo = token => {
  token = token.toUpperCase()
  switch (token) {
    case 'BTC':
      return btcDetailLogo
    case 'BCH':
      return bchDetailLogo
    case 'ETH':
      return ethDetailLogo
    case 'LTC':
      return ltcDetailLogo
    case 'DOGE':
      return dogeDetailLogo
    case 'DASH':
      return dashDetailLogo
    case 'USDC':
      return usdcDetailLogo
    case 'TUSD':
      return tusdDetailLogo
    case 'SALT':
      return saltDetailLogo
    case 'USDP':
      return paxDetailLogo
    case 'PAXG':
      return paxDetailLogo
    case 'XRP':
      return xrpDetailLogo
    case 'USDT':
      return usdtDetailLogo
    case 'PREF':
      return prefDetailLogo
    default:
      return btcLogo
  }
}

export const getTokenDetailLogoSize = token => {
  token = token.toUpperCase()
  switch (token) {
    case 'BTC':
      return {logoHeight: 30, logoWidth: 24}
    case 'BCH':
      return {logoHeight: 30, logoWidth: 24}
    case 'ETH':
      return {logoHeight: 30, logoWidth: 18}
    case 'LTC':
      return {logoHeight: 30, logoWidth: 27}
    case 'DOGE':
      return {logoHeight: 30, logoWidth: 30}
    case 'DASH':
      return {logoHeight: 25, logoWidth: 30}
    case 'USDC':
      return {logoHeight: 30, logoWidth: 32}
    case 'SALT':
      return {logoHeight: 30, logoWidth: 27}
    case 'TUSD':
      return {logoHeight: 30, logoWidth: 30}
    case 'USDP':
      return {logoHeight: 30, logoWidth: 30}
    case 'PAXG':
      return {logoHeight: 30, logoWidth: 30}
    case 'XRP':
      return {logoHeight: 30, logoWidth: 30}
    case 'USDT':
      return {logoHeight: 28, logoWidth: 28}
    case 'PREF':
      return {logoHeight: 28, logoWidth: 30}
    default:
      return {logoHeight: 30, logoWidth: 24}
  }
}
