import sha3 from 'crypto-js/sha3'
//import coinAddressValidator from 'coin-address-validator';
import WAValidator from 'multicoin-address-validator'
import BigNumber from 'bignumber.js'

import {cryptoMeta, months} from './enumerables'
import moment from 'moment'

export function updateActiveTabListener(navigation) {
  navigation.addListener('willFocus', () => {
    navigation.setParams({
      tabActive: true,
    })
  })
  navigation.addListener('didBlur', () => {
    navigation.setParams({
      tabActive: false,
    })
  })
}

export function numberWithCommas(x) {
  const parts = x.toString().split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  return parts.join('.')
}

export const dig = (target, ...keys) => {
  let digged = target
  for (const key of keys) {
    if (typeof digged === 'undefined' || digged === null) {
      return undefined
    }

    digged = digged[key]
  }
  return digged
}

export const capitalizeFirstLetter = string => string.charAt(0).toUpperCase() + string.slice(1)

export function splitByCamelCase(string) {
  return string
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str)
    .split(' ')
    .map(a => `${a[0].toUpperCase()}${a.slice(1)}`)
    .join(' ')
}

const isChecksumAddress = address => {
  // Check each case
  address = address.replace('0x', '')
  const addressHash = sha3(address.toLowerCase())
  for (let i = 0; i < 40; i++) {
    // the nth letter should be uppercase if the nth digit of casemap is 1
    if (
      (parseInt(addressHash[i], 16) > 7 && address[i].toUpperCase() !== address[i]) ||
      (parseInt(addressHash[i], 16) <= 7 && address[i].toLowerCase() !== address[i])
    ) {
      return false
    }
  }
  return true
}

export function isValidBTCAddress(address) {
  let valid = false
  try {
    valid = WAValidator.validate(address, 'btc', 'both')
  } catch (e) {
    return false
  }
  return valid
}

export function isValidETHAddress(address) {
  if (!/^(0x)[0-9a-f]{40}$/i.test(address)) {
    // check if it has the basic requirements of an address
    return false
  } else if (/^(0x)[0-9a-f]{40}$/.test(address) || /^(0x)[0-9A-F]{40}$/.test(address)) {
    // If it's all small caps or all all caps, return true
    return true
  }
  // Otherwise check each case
  return isChecksumAddress(address)
}

export function isValidDASHAddress(address) {
  let valid = false
  try {
    valid = WAValidator.validate(address, 'dash', 'both')
  } catch (e) {
    return false
  }
  return valid
}

export function isValidXRPAddress(address) {
  let valid = false
  try {
    valid = WAValidator.validate(address, 'xrp', 'both')
  } catch (e) {
    return false
  }
  return valid
}

export function isValidLTCAddress(address) {
  let valid = false
  try {
    valid = WAValidator.validate(address, 'litecoin', 'both')
  } catch (e) {
    return false
  }
  return valid
}

export function isValidDOGEAddress(address) {
  let valid = false
  try {
    valid = WAValidator.validate(address, 'doge', 'both')
  } catch (e) {
    return false
  }
  return valid
}

export function validateTokenAddress(token, address) {
  if (token === 'BTC') {
    // ******************************************
    const addressBc1 = address.substring(0, 4).toLowerCase()
    console.log('addressBc1', addressBc1)
    if (!isValidBTCAddress(address)) {
      return false
    }
  }

  if (token === 'ETH' || token === 'SALT' || token === 'USDC' || token === 'TUSD') {
    if (!isValidETHAddress(address)) {
      return false
    }
  }

  if (token === 'LTC') {
    if (!isValidLTCAddress(address)) {
      return false
    }
  }

  if (token === 'DASH') {
    if (!isValidDASHAddress(address)) {
      return false
    }
  }

  if (token === 'DOGE') {
    if (!isValidDOGEAddress(address)) {
      return false
    }
  }

  if (token === 'XRP') {
    if (!isValidXRPAddress(address)) {
      return false
    }
  }

  return true
}

export function formatToAmPmTime(date) {
  const convertedDate = new Date(date)
  let hours = convertedDate.getHours()
  let minutes = convertedDate.getMinutes()
  const ampm = hours >= 12 ? 'PM' : 'AM'
  hours %= 12
  hours = hours || 12
  minutes = minutes < 10 ? '0' + minutes : minutes
  const strTime = hours + ':' + minutes + ' ' + ampm
  return strTime
}

export function notificationFormatDate(string) {
  const currentDate = new Date()
  const notificationDate = new Date(string)
  const today = currentDate.toDateString() === notificationDate.toDateString()
  const yesterday =
    new Date(currentDate.setDate(currentDate.getDate() - 1)).toDateString() ===
    notificationDate.toDateString()

  if (today) {
    return `Today`
  }

  if (yesterday) {
    return `Yesterday`
  }

  let year = notificationDate.getFullYear().toString()
  year = year.substring(2, 4)

  const month = notificationDate.getMonth() + 1

  return `${month}/${notificationDate.getDate()}/${year}`
}

export function nextPaymentFormatDate(string) {
  const notificationDate = new Date(string)
  const monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ]
  const month = notificationDate.getMonth()
  const monthWord = monthNames[month]
  const year = notificationDate.getFullYear()
  return `${monthWord} ${notificationDate.getDate()}, ${year}`
}

export function shortMonth(num) {
  const monthNames = [
    'Jan',
    'Feb',
    'March',
    'April',
    'May',
    'June',
    'July',
    'Aug',
    'Sept',
    'Oct',
    'Nov',
    'Dec',
  ]
  return monthNames[num - 1]
}

export const mockRes = (status, data = {}, headers = {}) =>
  status >= 200 && status < 300
    ? Promise.resolve({status, data, headers})
    : Promise.reject({response: {status, headers}, message: data})

export const buildQueryString = params =>
  Object.keys(params)
    .map(k => `${encodeURIComponent(k)}=${encodeURIComponent(params[k])}`)
    .join('&')

export const formatDateText = (text, oldText) => {
  if (/[^0-9\/]/.test(text)) {
    return false
  } else if (oldText && text.length < oldText.length) {
    return text
  } else if (
    oldText &&
    oldText.length == 2 &&
    text.length > oldText.length &&
    text.substring(2) != '/'
  ) {
    return text.substring(0, 2) + '/' + text.substring(2)
  } else if (
    oldText &&
    oldText.length == 5 &&
    text.length > oldText.length &&
    text.substring(5) != '/'
  ) {
    return text.substring(0, 5) + '/' + text.substring(5)
  } else if (text.length == 2 || text.length == 5) {
    return text + '/'
  } else if (text.length == 10) {
    const one = text.substring(0, 2)
    const two = text.substring(3, 5)
    const three = text.substring(6, 10)
    const testOne = /^\d+$/.test(one)
    const testTwo = /^\d+$/.test(two)
    const testThree = /^\d+$/.test(three)
    const firstSlash = text.substring(2, 3)
    const secondSlash = text.substring(5, 6)
    const year = new Date().getFullYear()
    if (
      !testOne ||
      !testTwo ||
      !testThree ||
      firstSlash != '/' ||
      secondSlash != '/' ||
      Number(one) > 12 ||
      Number(two) > 31 ||
      Number(three) > year
    ) {
      return false
    }
  } else if (text.length > 10) {
    return false
  }
  return text
}

export const listPossibleWallets = () => {
  return ['BTC', 'ETH', 'BCH', 'LTC', 'SALT', 'USDP', 'USDC', 'TUSD', 'PAXG', 'USDT']
}

export function nthNumber(number) {
  return number > 0
    ? ['th', 'st', 'nd', 'rd'][(number > 3 && number < 21) || number % 10 > 3 ? 0 : number % 10]
    : ''
}

export function formatCrypto(amount, currency) {
  return new BigNumber(amount).isNaN() || !currency
    ? null
    : numberWithCommas(
        parseFloat(new BigNumber(amount).toFixed(cryptoMeta.get(currency).balanceDigits)),
      )
}
export function formatCurrency(amount, decimal = 2) {
  console.log('amount: ', amount)
  return new BigNumber(amount).isNaN()
    ? null
    : numberWithCommas(parseFloat(new BigNumber(amount).toFixed(decimal)))
}

export function addMonths(dt, n) {
  // Set the month of the provided date to the result of adding n months to its current month
  return new Date(dt.setMonth(dt.getMonth() + n))
}

export function getNextMonthDate(dayOfMonth) {
  // Get today's date
  const today = moment()

  // Get the current year and month
  let year = today.year()
  const month = today.month() // January is 0!

  // Calculate the next month and handle year transition
  let nextMonth = month + 1
  if (nextMonth > 11) {
    // if month is December
    nextMonth = 0 // January
    year += 1 // next year
  }

  // Create the new date with the specified day of the month
  let nextMonthDate = moment([year, nextMonth, dayOfMonth])

  // If the day of the month is invalid (e.g., February 30), moment will adjust it to a valid date
  if (nextMonthDate.month() !== nextMonth) {
    nextMonthDate = moment([year, nextMonth, 0]) // Get the last day of the next month
  }

  return nextMonthDate.format('MMM Do, YYYY')
}

export function calculateMaturityDate(startDate, investment) {
  // Parse the start date using Moment.js
  const startMoment = moment(startDate)
  // Get the current date
  const currentMoment = moment()
  const resultMoment = startMoment.clone().add(investment.term, 'months')
  // Calculate the difference in complete months between the current date and the start date
  const completedMonths = currentMoment.diff(startMoment, 'months')

  // Format the result to get the next month date and year
  const resultDate = resultMoment.format('MMM Do, YYYY')

  return {
    nextDate: resultDate,
    remainingMonths: Math.min(completedMonths, Number(investment?.term)),
  }
}

export function isMonthAndDayLessThanCurrent(dateString) {
  const givenDate = moment(dateString)
  const currentDate = moment()

  // Check if the year of the given date is less than the current year
  if (givenDate.year() < currentDate.year()) {
    return true
  }

  // Check if the year is the same, but the month is less
  if (givenDate.year() === currentDate.year() && givenDate.month() < currentDate.month()) {
    return true
  }

  // Check if the year and month are the same, but the day is less
  return (
    givenDate.year() === currentDate.year() &&
    givenDate.month() === currentDate.month() &&
    givenDate.date() <= currentDate.date()
  )
}

export function isMonthCurrent(dateString) {
  const givenDate = moment(dateString)
  const currentDate = moment()

  // Check if the year and month of the given date match the current year and month
  return givenDate.year() === currentDate.year() && givenDate.month() === currentDate.month()
}

export function isMaturityDate(originationDate, maturityMonths) {
  const startDate = moment(originationDate)
  // Get the current date
  const maturityDate = startDate.clone().add(maturityMonths, 'months').startOf('day')

  // Get today's date (starting at midnight)
  const today = moment().startOf('day')
  // Compare the maturity date with today's date
  return today.isSameOrAfter(maturityDate)
}

export const collateralMarketCapSort = (a, b) => {
  const marketCaps = [
    {
      currency: 'BTC',
      index: 1,
    },
    {
      currency: 'ETH',
      index: 2,
    },
    {
      currency: 'XRP',
      index: 3,
    },
    {
      currency: 'BCH',
      index: 4,
    },
    {
      currency: 'LTC',
      index: 5,
    },
    {
      currency: 'DASH',
      index: 6,
    },
    {
      currency: 'DOGE',
      index: 7,
    },
    {
      currency: 'SALT',
      index: 8,
    },
    {
      currency: 'USDT',
      index: 9,
    },
    {
      currency: 'USDP',
      index: 10,
    },
    {
      currency: 'USDC',
      index: 11,
    },
    {
      currency: 'TUSD',
      index: 12,
    },
    {
      currency: 'PAXG',
      index: 13,
    },
    {
      currency: 'PREF',
      index: 14,
    },
  ]
  const marketA = marketCaps?.filter(c => c.currency == a.title)[0].index
  const marketB = marketCaps?.filter(c => c.currency == b.title)[0].index

  if (marketA < marketB) {
    return -1
  }
  if (marketB < marketA) {
    return 1
  }
  return 0
}

export const getBaseRate = (baseLTV, baseRates, termLength) => {
  if (baseRates && baseRates[termLength]) {
    return baseRates[termLength][baseLTV]
  }
  const allProducts = new Map()
    .set(0.3, 0.0999)
    .set(0.4, 0.0999)
    .set(0.5, 0.0999)
    .set(0.6, 0.0999)
    .set(0.7, 0.0999)
  const base = allProducts.get(baseLTV)
  return base
}

export function dynamicRoundUp(value) {
  if (value === 0) return 0
  const decimalPlaces = -Math.floor(Math.log10(value)) + 1 // Find where the positive number starts
  const factor = Math.pow(10, decimalPlaces)
  return Math.ceil(value * factor) / factor
}

export const getPayment = (baseRate, loanAmount, termLength, isInterestOnly) => {
  const msPerDay = 24 * 60 * 60 * 1000
  const msPerMonth = msPerDay * 30
  const periodRate = baseRate / 12
  if (isInterestOnly) {
    return loanAmount * periodRate
  }
  const math = Math.pow(1 + periodRate, -1 * Math.round(termLength / msPerMonth))
  const periodRateMaths = (periodRate * loanAmount) / (1 - math)
  return Number(periodRateMaths.toFixed(2))
}

export const getRewardRateMap = (baseLTV, rewardRates, termLength) => {
  if (rewardRates && rewardRates[termLength]) {
    return rewardRates[termLength][baseLTV]
  }
  switch (baseLTV) {
    case 0.7:
      return 0.0
    case 0.6:
      return 0.005
    case 0.5:
      return 0.0149
    case 0.4:
      return 0.0249
    case 0.3:
      return 0.0449
    default:
      return 0.0449
  }
}

export const createMiniAmSchedule = (loanAmount, baseLTV, termLength, isInterestOnly, baseRate) => {
  const periodicInterestRate = baseRate / 12
  const termInMs = ********** * termLength
  const msPerDay = 24 * 60 * 60 * 1000
  const msPerMonth = msPerDay * 30
  const tMonths = termInMs / msPerMonth
  const monthsToMilli = ********** * termLength
  const monthlyPayments = getPayment(baseRate, loanAmount, monthsToMilli, isInterestOnly)

  return Array.from(Array(tMonths)).reduce(
    (accumulator, _, currentIndex) => {
      const balanceBeforePayment = accumulator[currentIndex].balanceAfterPayment
      const interestDue = isInterestOnly
        ? parseFloat(monthlyPayments.toFixed(2))
        : parseFloat((balanceBeforePayment * periodicInterestRate).toFixed(2))
      const principalDue = isInterestOnly
        ? 0
        : parseFloat((monthlyPayments - interestDue).toFixed(2))

      return [
        ...accumulator,
        {
          term: currentIndex + 1,
          balanceBeforePayment,
          interestDue,
          principalDue,
          balanceAfterPayment: balanceBeforePayment - principalDue,
        },
      ]
    },
    [
      {
        term: 0,
        balanceBeforePayement: null,
        intersetDue: null,
        principalDue: null,
        balanceAfterPayment: parseFloat(loanAmount),
      },
    ],
  )
}

export const getTotalInterest = (loanAmount, baseLTV, termLength, isInterestOnly, baseRate) => {
  const amSchedule = createMiniAmSchedule(loanAmount, baseLTV, termLength, isInterestOnly, baseRate)
  const sumOfMonthlyInterestDue = amSchedule.reduce((accumulator, currentValue) => {
    const interestDue = currentValue.interestDue ? currentValue.interestDue : 0
    return accumulator + interestDue
  }, 0)
  return Math.round(sumOfMonthlyInterestDue * 100) / 100
}

export const getTotalRewards = (
  loanAmount,
  baseLTV,
  termLength,
  isInterestOnly,
  rewardRate,
  baseRate,
) => {
  const amSchedule = createMiniAmSchedule(loanAmount, baseLTV, termLength, isInterestOnly, baseRate)
  const periodicRewardRate = rewardRate / 12
  const sumOfMonthlyRewards = amSchedule.reduce((accumulator, currentValue) => {
    const stackwiseRewards = currentValue.balanceBeforePayment
      ? parseFloat((currentValue.balanceBeforePayment * periodicRewardRate).toFixed(2))
      : 0
    return accumulator + stackwiseRewards
  }, 0)
  return Math.round(sumOfMonthlyRewards * 100) / 100
}

export function calculateLoanPayoffAmount(loan, date) {
  const {interestRate, reward} = loan

  const dayDiffFromScheduleDate = moment(date).diff(new Date(), 'days')

  const currentReward = reward
    ? reward.rewardSchedule.find(r => r.disbursementPeriod === reward.currentDisbursementPeriod)
    : null

  const hasCurrentRewardBeenEarned =
    // check if loan is stackwise loan or not
    // AND
    // check if reward allocation is towards monthly payment
    currentReward && reward?.useRewardAsPayment
      ? // if true, check if it has been earned from the reward schedule
        currentReward.earned
      : // if false (meaning if its a prestackwise loan or reward allocation IS NOT towards monthly payment), return true so that the constant 'upcomingRewardTowardsMonthlyPayment' will default to 0
        true

  const upcomingRewardTowardsMonthlyPayment = hasCurrentRewardBeenEarned
    ? new BigNumber(0)
    : new BigNumber(reward?.nextRewardAmount)

  const totalDaysOfRewardsEarned = new BigNumber(
    reward?.daysOfRewardEarnedInCurrentPeriod || 0,
  ).plus(dayDiffFromScheduleDate)

  const amountOfRewardsEarnedForMontlyPayment = upcomingRewardTowardsMonthlyPayment
    .div(currentReward?.days || 1)
    .times(totalDaysOfRewardsEarned)

  return (
    new BigNumber(interestRate || 0)
      .dividedBy(360)
      // daily interest rate
      .times(dayDiffFromScheduleDate)
      // times the amount of days different from today and the selected date (date picker in bank and cryptopayment options)
      .times(loan?.amortizationInfo?.currentBalance || 0)
      .dp(2)
      .plus(loan?.amortizationInfo?.currentBalance || 0)
      .plus(loan?.amortizationInfo?.interestBalance || 0)
      .minus(amountOfRewardsEarnedForMontlyPayment)
  )
}

export const getAprForRewards = (
  loanAmount,
  baseLTV,
  isInterestOnly,
  termLength,
  baseRate,
  rewardRates,
  isLeveraged,
) => {
  let rewardRate = getRewardRateMap(baseLTV, rewardRates, termLength)
  rewardRate = Math.round(rewardRate * 1000000) / 1000000
  if (!isLeveraged) {
    rewardRate = 0
  }
  const showRewardRate = (rewardRate * 100).toFixed(6)

  const totalInterest = getTotalInterest(loanAmount, baseLTV, termLength, isInterestOnly, baseRate)
  const totalRewards = getTotalRewards(
    loanAmount,
    baseLTV,
    termLength,
    isInterestOnly,
    rewardRate,
    baseRate,
  )
  const showTotalRewards = totalRewards.toFixed(2)

  const termInMs = ********** * termLength
  const msPerDay = 24 * 60 * 60 * 1000
  const msPerMonth = msPerDay * 30
  const tMonths = termInMs / msPerMonth
  let decimalApr = ((totalInterest - totalRewards) / loanAmount) * (12 / tMonths)
  decimalApr = Math.round(decimalApr * 1000000) / 1000000
  return {decimalApr, showRewardRate, showTotalRewards, totalRewards}
}

export const getFeeFromCashInHandForRefinance = (cashInHand, feeRate) => {
  let loanFee = cashInHand / (1 - feeRate) - cashInHand
  loanFee = Number((Math.ceil(loanFee * 100) / 100).toFixed(2))

  return loanFee
}

export const getOriginationFeeAmount = (cashInHand, feeRate) => {
  const feeAmount = cashInHand > 0 ? cashInHand / (1 - feeRate) - cashInHand : 0
  return Number(Math.round(feeAmount * 100) / 100)
}

export const getOriginationFeeRate = (cashInHand, feeAmount) => {
  const feeRate = cashInHand > 0 ? feeAmount / (feeAmount + cashInHand) : 0
  return feeRate
}

export const getFeeRateWithDiscount = (feeRate, feeRateDiscount) => {
  return Math.round((feeRate - feeRateDiscount) * 1000000) / 1000000
}

export const getApr = (loanAmount, baseLTV, termLength, baseRate) => {
  /*
  const interestRate = this.baseRateMap.get(termLength).get(parseFloat(baseLTV))
  const totalInterest = Math.round(interestRate * 1000000) / 1000000
  */

  const termInMs = ********** * termLength
  const msPerDay = 24 * 60 * 60 * 1000
  const msPerMonth = msPerDay * 30
  const tMonths = termInMs / msPerMonth

  //let decimalApr = baseRate * (12 / tMonths)
  let decimalApr = Math.round(baseRate * 1000000) / 1000000
  return decimalApr
}

export const getFeeAmount = (cashInHand, feeRate) => {
  const loanAmount = getLoanAmount(cashInHand, feeRate)
  return Math.round((loanAmount - cashInHand) * 100) / 100
}

export const getLoanAmount = (cashInHand, feeRate) => {
  return Math.ceil((cashInHand / (1 - feeRate)) * 100) / 100
}

export const getSimpleInterest = (loanAmount, baseRate, term) => {
  const interestOnLoan = loanAmount * baseRate * (term / 12)
  return Math.round(interestOnLoan * 1000000) / 1000000
}

export const getPaymentForRefinance = (loanAmount, interestRate, term, isInterestOnly) => {
  const periodRate = interestRate / 12
  if (isInterestOnly) {
    return loanAmount * periodRate
  } else {
    return Number(((periodRate * loanAmount) / (1 - Math.pow(1 + periodRate, -term))).toFixed(2))
  }
}

export const getAprForRefinance = (
  loanAmount,
  cashInHand,
  interestRate,
  term,
  isInterestOnly,
  feeRate,
  totalSaltValue,
) => {
  const feeAmount = getFeeAmount(cashInHand, feeRate)
  const amountFinanced = loanAmount + feeAmount
  if (isInterestOnly) {
    const totalInterest = getSimpleInterest(amountFinanced, interestRate, term)
    const apr = ((totalInterest + feeAmount + totalSaltValue) / amountFinanced) * (12 / term)
    return Math.round(apr * 1000000) / 1000000
  } else {
    const monthlyPayment = getPaymentForRefinance(
      amountFinanced,
      interestRate,
      term,
      isInterestOnly,
    )
    const apr = newRate(term, -monthlyPayment, loanAmount)
    return Math.round(apr * 1000000 * 12) / 1000000
  }
}

export const newRate = (periods, payment, present, future, type, guess) => {
  guess = guess === undefined ? 0.01 : guess
  future = future === undefined ? 0 : future
  type = type === undefined ? 0 : type

  // Set maximum epsilon for end of iteration
  var epsMax = 1e-10

  // Set maximum number of iterations
  var iterMax = 10

  // Implement Newton's method
  var y,
    y0,
    y1,
    x0,
    x1 = 0,
    f = 0,
    i = 0
  var rate = guess
  if (Math.abs(rate) < epsMax) {
    y = present * (1 + periods * rate) + payment * (1 + rate * type) * periods + future
  } else {
    f = Math.exp(periods * Math.log(1 + rate))
    y = present * f + payment * (1 / rate + type) * (f - 1) + future
  }
  y0 = present + payment * periods + future
  y1 = present * f + payment * (1 / rate + type) * (f - 1) + future
  i = x0 = 0
  x1 = rate
  while (Math.abs(y0 - y1) > epsMax && i < iterMax) {
    rate = (y1 * x0 - y0 * x1) / (y1 - y0)
    x0 = x1
    x1 = rate
    if (Math.abs(rate) < epsMax) {
      y = present * (1 + periods * rate) + payment * (1 + rate * type) * periods + future
    } else {
      f = Math.exp(periods * Math.log(1 + rate))
      y = present * f + payment * (1 / rate + type) * (f - 1) + future
    }
    y0 = y1
    y1 = y
    ++i
  }
  return rate
}
