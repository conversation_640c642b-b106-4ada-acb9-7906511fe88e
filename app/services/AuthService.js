import Amplify, {Auth} from 'aws-amplify';

import config from '../config.json';

export class AuthService {
  constructor() {
    const cognitoCreds = {
      Auth: config.cognito,
    };
    console.log('cognitoCreds'.cognitoCreds);
    Auth.configure(cognitoCreds);
    //Amplify.configure(cognitoCreds);
  }

  login = (username, password) => {
    const lowercaseUsername = username.toLowerCase();
    return Auth.signIn(lowercaseUsername, password);
  };

  authenticateUser = user => user.authenticateUser();

  login2FA = (user, code) =>
    //confirmSignInCustom(user, code, 'SOFTWARE_TOKEN_MFA') // return Auth.confirmSignIn
    Auth.confirmSignIn(user, code, 'SOFTWARE_TOKEN_MFA');

  getCurrentSession = () => Auth.currentSession();

  getCurrentUser = () => Auth.currentAuthenticatedUser();

  changePassword = (user, oldPass, newPass) => Auth.changePassword(user, oldPass, newPass);

  completeNewPassword = (user, password) => Auth.completeNewPassword(user, password);

  currentUserPoolUser = () => Auth.currentUserPoolUser();

  currentSession = () => Auth.currentSession();

  userSession = user => Auth.userSession(user);

  signUp = (email, password) => Auth.signUp(email, password);

  verifyTotpToken = (cognitoUser, code) => Auth.verifyTotpToken(cognitoUser, code);
}

function confirmSignInCustom(user, code, mfaType) {
  // const that = this
  return new Promise((resolve, reject) => {
    user.sendMFACode(
      code,
      {
        onSuccess: session => {
          resolve(session);
          /*
          that
            ._setCredentialsFromSession(session)
            .then(cred => {
              console.log('cred', cred)
              // that.user = user
              // dispatchAuthEvent('signIn', user)
              resolve(user)
            })
            .catch(e => {
              console.log('e', e)
              console.log('cannot get cognito credentials')
              reject(e)
            })
            */
        },
        onFailure: err => {
          console.log('confirm signIn failure', err);
          reject(err);
        },
      },
      mfaType,
    );
  });
}

export default AuthService;
