import loans from './mocks/loans'
import notifications from './mocks/notifications'
import preferences from './mocks/preferences'
import account from './mocks/account'
import accountsAll from './mocks/accountsAll'
import user from './mocks/user'
import prices from './mocks/prices'
import wallets from './mocks/wallets'
import {mockRes} from '../util/helpers'

let loggedIn = true

export class WebServiceStub {
  initInterceptors = () => Promise.resolve()

  updateSID = () => Promise.resolve()

  setVersionHeader = () => Promise.resolve()

  updateRef = () => Promise.resolve()

  getLoans = () => mockRes(200, accountsAll)

  getUpholdState = () => mockRes(400)

  fetchJurisdictionInfo = () => mockRes(200)

  fetchLoanConstraints = () => mockRes(200)

  getLoanRatesMaps = () => mockRes(200)

  checkAch = () => mockRes(400)

  transferSend = () => mockRes(400)

  submitNonLendable = () => mockRes(200)

  submitLoanRequest = () => mockRes(200)

  checkLoanLegality = () => mockRes(200)

  getSaltAccount = () => (loggedIn ? mockRes(200, account) : Promise.resolve({}))

  getPrices = () => mockRes(200, prices)

  getPrices24h = () => mockRes(200, prices)

  getPrices30d = () => mockRes(200, prices)

  getPrices7d = () => mockRes(200, prices)

  updateFCMTokenAPI = () => mockRes(200)

  updateFCMPermissionAPI = () => mockRes(200, 'qwe')

  getZendeskCategories = () => mockRes(200)

  postZendeskFeedback = () => mockRes(200)

  updateZendeskField = () => mockRes(200)

  getZendeskUser = () => mockRes(200)

  getJumioCreds = () => mockRes(200)

  getBank = () => mockRes(200, [])

  getSaltUser = () => mockRes(200, user)

  newPhone(callingCode, number) {
    return mockRes(200, {
      data: {
        id: '123',
        countryCode: 'US',
        callingCode,
        number,
        isVerified: false,
      },
    })
  }

  getInvestmentRates = () =>
    mockRes(200, [
      {
        id: '3dd9618b-d165-4a5b-a47e-d392beead2fe',
        term: 12,
        earnRate: '0.03',
        currency: 'BTC',
        isActive: false,
        lockUpPeriod: '12',
      },
      {
        id: 'fc24a59a-de03-4707-89d2-cf94aa486fd5',
        term: 6,
        earnRate: '0.065',
        currency: 'BTC',
        isActive: true,
        lockUpPeriod: '9',
      },
      {
        id: '03fbaf86-9b49-447e-9b53-dc0cfcd16192',
        term: 9,
        earnRate: '0.075',
        currency: 'BTC',
        isActive: true,
        lockUpPeriod: '9',
      },
      {
        id: '1795e16d-cbc8-454f-b202-0db094b34b3f',
        term: 3,
        earnRate: '0.02',
        currency: 'ETH',
        isActive: true,
        lockUpPeriod: '3',
      },
      {
        id: 'fe7705b2-1168-43ad-9a38-e2b25ed3df5a',
        term: 3,
        earnRate: '0.0555',
        currency: 'BTC',
        isActive: true,
        lockUpPeriod: '3',
      },
      {
        id: '1c8429c8-611b-4c04-b0ea-59be87c1181d',
        term: 6,
        earnRate: '0.04',
        currency: 'ETH',
        isActive: true,
        lockUpPeriod: '6',
      },
      {
        id: 'ba8c54dc-35f5-4237-aba0-7bec394b6a5f',
        term: 6,
        earnRate: '0.065',
        currency: 'USDC',
        isActive: true,
        lockUpPeriod: '1',
      },
      {
        id: 'e5758e24-176d-4fe9-9b64-a3923ca177e1',
        term: 9,
        earnRate: '0.075',
        currency: 'USDC',
        isActive: true,
        lockUpPeriod: '1',
      },
      {
        id: 'ebdbdfec-daf8-40ba-a7a2-be9cdca14ec8',
        term: 12,
        earnRate: '0.1',
        currency: 'ETH',
        isActive: false,
        lockUpPeriod: '1',
      },
      {
        id: 'b1242fa7-c318-4479-9020-e8e0d64713fb',
        term: 3,
        earnRate: '0.055',
        currency: 'USDT',
        isActive: true,
        lockUpPeriod: '1',
      },
      {
        id: 'bb32b2a9-6497-43c1-af6a-cf18df083b03',
        term: 3,
        earnRate: '0.055',
        currency: 'USDC',
        isActive: true,
        lockUpPeriod: '1',
      },
      {
        id: '78372a34-5b4e-40dc-950e-45d4098cfbfa',
        term: 6,
        earnRate: '0.065',
        currency: 'USDT',
        isActive: true,
        lockUpPeriod: '1',
      },
      {
        id: '81f4d33a-4c68-4c6a-9580-9b1761cf4f78',
        term: 9,
        earnRate: '0.08',
        currency: 'ETH',
        isActive: true,
        lockUpPeriod: '1',
      },
      {
        id: 'd31ce165-f736-4532-a189-8f7061bb3ea0',
        term: 9,
        earnRate: '0.09',
        currency: 'USDT',
        isActive: true,
        lockUpPeriod: '1',
      },
      {
        id: '8f91da84-5498-4fa5-a78d-ee855da389c5',
        term: 12,
        earnRate: '0.12',
        currency: 'USDT',
        isActive: false,
        lockUpPeriod: '1',
      },
      {
        id: 'f51d5575-1644-4133-a6c5-f597a92bca90',
        term: 12,
        earnRate: '0.1299',
        currency: 'USDC',
        isActive: false,
        lockUpPeriod: '1',
      },
    ])

  deletePhone = () => mockRes(200)

  withdraw = () => mockRes(200)

  getNotifications = () => mockRes(200, notifications)

  getNotificationPreferences = () => mockRes(200, preferences)

  updateNotificationPreferences = () => mockRes(200, preferences)

  readNotification = id => mockRes(200)

  readAllNotifications = () => mockRes(200)

  getTransactionHistory = () =>
    mockRes(200, {
      id: 'c2a016ea-36b9-4356-b27c-249c7564bb5d',
      currency: 'SALT',
      address: '******************************************',
      price: 1.*********,
      balance: '0.********',
      value: '0',
      hasLegacyTx: true,
      pendingWithdrawal: {},
      transactions: [],
    })

  verifyPhonePin = () => mockRes(200)

  resendPhonePin = () => mockRes(200)

  cancelWithdrawal = () => mockRes(200)

  getWallets = () => mockRes(200, wallets)

  saltPurchase = () => mockRes(200)

  createWallet = () => mockRes(200)

  getAllAccounts = () => mockRes(200, accountsAll)

  getUpholdCards = () => mockRes(200)

  getAccountsLend = () => mockRes(200, account)

  getTxAll = () => mockRes(200, account)

  loginToSalt = () => {
    loggedIn = true
    return mockRes(200, {deviceId: '1'}, {'sess-auth': '1'})
  }

  logoutOfSalt = () => mockRes(200)

  zaboConnect = (id, token) => mockRes(200)

  getZaboWallets = () => mockRes(200)

  deleteZaboWallet = id => mockRes(200)

  getZaboTx = id => mockRes(200)

  getUnsignedAgreement = payload => mockRes(200)

  createSaltAccount = data => mockRes(200, account)

  getPrices24h = () => mockRes(200)
}

export default WebServiceStub
