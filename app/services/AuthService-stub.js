class AuthServiceStub {
  currentSession = () =>
    Promise.resolve({
      accessToken: {
        jwt: 'session',
      },
    })

  currentAuthenticatedUser = () => Promise.resolve()

  setupTOTP = () => Promise.resolve()

  login2FA = () =>
    Promise.resolve({
      signInUserSession: {
        accessToken: {
          jwtToken: 'jwtToken',
          payload: {
            username: 'username',
            device_key: 'deviceKey',
          },
        },
      },
    })

  confirmSignIn = () =>
    Promise.resolve({
      username: 'username',
      deviceKey: 'deviceKey',
      signInUserSession: {
        accessToken: {
          jwtToken: 'jwtToken',
        },
      },
    })

  verifyTotpToken = () =>
    Promise.resolve({
      accessToken: {
        payload: {
          username: 'username',
          device_key: 'deviceKey',
        },
        jwtToken: 'jwtToken',
      },
    })

  setPreferredMFA = () => Promise.resolve()

  login = () =>
    Promise.resolve({
      challengeName: 'SOFTWARE_TOKEN_MFA',
      challengeParameters: { FRIENDLY_DEVICE_NAME: 'My TOTP device' },
      Session: 'session',
      username: 'username',
    })

  logout = () => Promise.resolve()

  signUp = () => Promise.resolve()

  changePassword = () => Promise.resolve()
}

export default AuthServiceStub
