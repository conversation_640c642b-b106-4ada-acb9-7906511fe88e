import axios from 'axios'
import {buildQueryString} from '../util/helpers'
import RNFetchBlob from 'rn-fetch-blob'
import Heap from '@heap/react-native-heap'
import {Platform} from 'react-native'

export class WebService {
  constructor(apiUrl, env) {
    this.env = env
    this.requests = axios.create({
      baseURL: apiUrl,
      params: {ref: 1},
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-store',
      },
      withCredentials: true,
      timeout: 90000,
    })
  }

  initInterceptors = (callDispatch, showUpdateVersionScreen, localLogout) => {
    this.requests.interceptors.request.use(
      config => {
        //console.log('Sending request with Sess-Auth:', config.headers.common['Sess-Auth'])
        return config
      },
      error => {
        return Promise.reject(error)
      },
    )

    this.requests.interceptors.response.use(
      res => res,
      error => {
        let message = error?.response?.data || error?.message || 'error'
        Heap.track('Error', {message})
        if (error?.response && error?.response?.data === 'User must update mobile app') {
          callDispatch(showUpdateVersionScreen())
        }
        if (error?.response && error?.response.data === 'Invalid Session') {
          //console.log('eh?', error?.response)
          //callDispatch(localLogout())
        }

        return Promise.reject(error?.response)
      },
    )
  }

  updateSID = sid => {
    this.requests.defaults.headers.common['Sess-Auth'] = sid
    console.log('updateSID', sid)
  }

  setVersionHeader = version => {
    this.requests.defaults.headers.common['Mobile-Version'] = version
  }

  updateRef = ref => {
    this.requests.defaults.params.ref = ref
  }

  patch = (endpoint, data) => {
    let url = `${this.requests.defaults.baseURL}${endpoint}`
    const queryString = buildQueryString(this.requests.defaults.params)
    if (queryString) url = `${url}?${queryString}`

    return fetch(url, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        ...this.requests.defaults.headers.common,
      },
      credentials: 'include',
      body: JSON.stringify(data),
    })
  }

  getLoans = ref =>
    ref ? this.requests.get(`/loans/myLoans?ref=${ref}`) : this.requests.get(`/loans/myLoans`)

  getLoanAssets = () => this.requests.ge('/assets')

  getLoanWallet = id => this.requests.get(`/loans/${id}/wallets`)

  getSaltAccount = () => this.requests.get('/accounts')

  getAllAccounts = () => this.requests.get('/users/accounts/loans')

  getAccountsLend = () => this.requests.get('/users/accounts/investments')

  createSaltAccount = data => this.requests.post('/accounts/create', data)

  setAccountName = (id, ref = null, body) => {
    if (ref) {
      return this.patch(`/accounts/${id}/setAccountName`, body)
    } else {
      return this.patch(`/accounts/${id}/setAccountName?ref=${ref}`, body)
    }
  }

  getAccountMembers = () => this.requests.get('/accounts/myAccount/members')

  getAccountMembersID = id => this.requests.get(`/accounts/myAccount/members/${id}`)

  deleteAccount = (id, ref) => {
    const config = {
      params: {
        ref,
      },
    }
    return this.requests.delete(`/accounts/${id}/deleteAccount`, config)
  }

  getPrices = () =>
    this.requests.get(
      '/prices?tradingPairs=BTC-USD,BCH-USD,ETH-USD,SALT-USD,LTC-USD,DASH-USD,DOGE-USD,USDC-USD,USDP-USD,PAXG-USD,XRP-USD,USDT-USD',
    )

  getPrices24h = () => {
    const FullDayInMS = ******** * 1
    const yesterdayStamp = new Date(Date.now() - FullDayInMS).toISOString()
    return this.requests.get(
      `/prices?tradingPairs=BTC-USD,BCH-USD,ETH-USD,SALT-USD,LTC-USD,DASH-USD,DOGE-USD,USDC-USD,USDP-USD,PAXG-USD,XRP-USD,USDT-USD&time=${yesterdayStamp}`,
    )
  }

  getPrices30d = () => {
    return this.requests.get(
      `/prices/historical?tradingPairs=BTC-USD,BCH-USD,ETH-USD,LTC-USD,SALT-USD,USDC-USD,USDP-USD,PAXG-USD,USDT-USD&days=300`,
    )
  }

  getPrices7d = () => {
    return this.requests.get(
      `/prices/historical?tradingPairs=BTC-USD,BCH-USD,ETH-USD,LTC-USD,SALT-USD,USDC-USD,USDP-USD,PAXG-USD,USDT-USD&days=7&time=100`,
    )
  }

  getPricesX = (pairs, days) => {
    return this.requests.get(`/prices/historical?tradingPairs=${pairs}&days=${days}`)
  }

  // /wallets/all/withTransactions & ref

  updateFCMTokenAPI = fcmToken => this.patch('/devices/register', {registrationId: fcmToken})

  updateFCMPermissionAPI = permission =>
    //return this.requests.post('/fcmPermission',permission)
    Promise.resolve({data: 'qwe'})

  kustomerSupport1 = data => this.requests.post('/kustomer/customer', data)
  kustomerSupport2 = data => this.requests.post('/kustomer/convo', data)
  kustomerSupportGet = data => this.requests.post('/kustomer/customerGet', data)
  kustomerSupport3 = data => this.requests.post('/kustomer/message', data)

  getSaltUser = () => this.requests.get(`/users/me`)

  newPhone = (countryCode, callingCode, number) =>
    this.requests.post(`/phones`, {
      countryCode,
      callingCode,
      number,
    })

  unit21NewPhone = (countryCode, callingCode, number) =>
    this.requests.post(`/phones/addPhone`, {
      countryCode,
      callingCode,
      number,
    })

  deletePhone = id => this.requests.delete(`/phones/${id}`)

  withdraw = (data, ref) => {
    const config = {
      params: {
        ref,
      },
    }
    return this.requests.post(`/outboundTransactions/withdrawal`, data, config)
  }

  getNotifications = page =>
    this.requests.get(`/notifications`, {
      params: {page, pagesize: 500},
    })

  readNotification = id => this.requests.get(`/notifications/${id}`)

  readAllNotifications = () => this.patch('/notifications/all/read')

  getNotificationPreferences = (ref = null) => {
    const config = {
      params: {
        ref,
      },
    }
    if (ref) {
      return this.requests.get(`/accounts/myAccount/preferences`, config)
    }
    return this.requests.get(`/accounts/myAccount/preferences`)
  }

  updateNotificationPreferences = data => this.patch(`/accounts/myAccount/preferences`, data)

  getLoanEventHistory = loanId => this.requests.get(`/loans/${loanId}/history`)

  getTransactionHistory = (currency, ref) => {
    const config = {
      params: {
        ref,
      },
    }
    return this.requests.get(`/wallets/${currency}`, config)
  }

  getTxAll = ref => {
    const config = {
      params: {
        ref,
      },
    }
    return this.requests.get(`/wallets/all/withTransactions`, config)
  }

  walletStatus = id => this.requests.get(`/wallets/${id}/status`)

  verifyPhonePin = (id, pin) => this.requests.post(`/phones/${id}/verify`, {pin})

  resendPhonePin = id => this.requests.post(`/phones/${id}/resend`)

  cancelWithdrawal = id => this.requests.post(`/outboundTransactions/${id}/cancel`)

  cancelVirtualTransaction = id => this.requests.post(`/vTransactions/${id}/cancel`)

  getWallets = (ref = this.requests.defaults.params.ref) => {
    const config = {
      params: {
        ref,
      },
    }
    return this.requests.get(`/wallets`, config)
  }

  updatePayout = (id, payload) => {
    return this.requests.post(`/investments/${id}/payouts/wallet`, payload)
  }

  getGasCost = token => this.requests.get(`/transactions/${token}/txCost/high`)

  getUpholdState = () => this.requests.get(`/uphold/state`)

  requestUpholdToken = data => this.requests.post('/uphold/oauth2/token', data)

  getUpholdCards = () => this.requests.get(`/uphold/cards`)

  upholdTransaction = data => this.requests.post('/uphold/transaction', data)

  removeUphold = () => this.requests.delete(`/uphold/me/tokens`)

  saltPurchase = refundAddress =>
    this.requests.post(`/saltPurchases`, {
      currency: 'BTC',
      refundAddress,
    })

  createWallet = (currency, ref = this.requests.defaults.params.ref) => {
    const config = {
      params: {
        ref,
      },
    }
    return this.requests.post(
      `/wallets`,
      {
        currency,
      },
      config,
    )
  }

  loginToSalt = (payload, jwtToken) => {
    const config = {
      headers: {
        Authorization: `Bearer ${jwtToken}`,
      },
    }

    return this.requests.post(`/login`, payload, config)
  }

  logoutOfSalt = () => this.requests.post(`/logout`)

  createUser = data => this.requests.post(`/users`, data)

  addEmail = data => this.requests.post(`/emails`, data)

  verifyEmail = (data = {}) => this.requests.post(`/emails/verify`, data)

  resendVerifyEmail = email => this.requests.post(`/emails/resend`, {email})

  deleteEmail = id => this.requests.delete(`/emails/${id}`)

  makeEmailPrimary = (id, code) => this.requests.post('/emails/' + id + '/primate', code)

  updateMe = data => this.requests.post(`/users/register`, data)

  checkMFA = data => this.requests.post('/checkMFA', data)

  getJumioCreds = productID => this.requests.get(`/externalVerifications/${productID}/jumio/mobile`)

  jumioFormComplete = productID =>
    this.requests.post(`/externalVerifications/${productID}/jumio/formComplete`, {})

  getTos = () => this.requests.get('/agreements/referral-terms')

  getUnsignedAgreement = (contractName, countryCode, province, sid) =>
    this.requests.get(`/agreements/${contractName}/unsigned`, {
      params: {
        countryCode,
        province,
        sid,
      },
    })

  getSignedAgreement = (contractName, countryCode, province, sid) =>
    this.requests.get(`/agreements/${contractName}`, {
      params: {
        countryCode,
        province,
      },
    })

  logAgreementView = (contractName, query) =>
    this.requests.post('/agreements/visit', {
      contractName,
      query: {
        cid: query.contractId,
        vid: query.versionId,
        sid: query.siteId,
      },
    })

  signAgreement = query =>
    this.requests.post('/users/register', {
      membershipAgreement: {
        cid: query.contractId,
        vid: query.versionId,
        sid: query.siteId,
      },
    })

  //addBank = data => this.requests.post('banks/plaid', data)

  getBank = id => {
    console.log('getBank', this.requests.defaults.params.ref)
    return this.requests.get(`banks`)
  }

  deleteBank = id => this.requests.delete(`/banks/${id}`)

  addManualBank = data => this.requests.post('/banks', data)

  getDwollaFunding = () => this.requests.get('/dwolla/funding-sources')

  getDwollaBanks = () => this.requests.get('/dwolla/banks')

  addDwollaBank = ({fundingSourceId}) => this.requests.post('/dwolla/banks', {fundingSourceId})

  dwollaRemove = id => this.requests.delete(`/dwolla/funding-sources/${id}`)

  uploadDocument = (referenceId, referenceType, documentType, data, fileName) => {
    const endpoint = `/documents/${referenceId}/${referenceType}/${documentType}`
    let url = `${this.requests.defaults.baseURL}${endpoint}`
    const queryString = buildQueryString(this.requests.defaults.params)
    if (queryString) url = `${url}?${queryString}`

    return RNFetchBlob.fetch(
      'POST',
      url,
      {
        'Sess-Auth': this.requests.defaults.headers.common['Sess-Auth'],
        'Content-Type': 'multipart/form-data',
      },
      [
        {
          name: 'file',
          filename: fileName,
          type: 'image/jpeg',
          data: RNFetchBlob.wrap(data),
        },
      ],
    )
  }

  verify2FACode = data => this.requests.post('/users/verify2FACode', data)

  send2FAVerificationEmail = () => this.requests.post('/users/send2FAVerificationEmail')

  generateNewSecret = () => this.requests.get('/users/generateNewSecret')

  verify2FAVerificationEmail = data => this.requests.post('/users/verify2FAVerificationEmail', data)

  setNewSecret = data => this.requests.post('/users/setNewSecret', data)

  checkMFA = data => this.requests.post('/checkMFA', data)

  getLatestTransactions = () => this.requests.get('/accounts/myAccount/latestTransactions')

  removeBankFromLoan = ({purpose, loanId, referenceId, referenceType}) =>
    this.requests.post(`/loans/${loanId}/bankAccount/${referenceId}/remove`, {
      purpose,
      referenceType,
    })

  connectBankToLoan = ({purpose, loanId, referenceId, referenceType}) =>
    this.requests.post(`/loans/${loanId}/bankAccount/${referenceId}`, {
      purpose,
      referenceType,
    })

  setPaymentPreference = (id, type) =>
    this.patch(`/loans/${id}/setPaymentPreference`, {
      automatedPaymentType: type,
    })

  schedulePayment = (loanId, data) =>
    this.requests.post(`/loans/${loanId}/scheduleOneTimePayment`, data)

  cancelPayment = id => this.requests.delete(`/scheduledPayments/${id}`)

  stablecoinPayment = (loanId, data) => this.requests.post(`/loans/${loanId}/cryptoPayments`, data)

  getLender = lenderId => this.requests.get(`/lenders/${lenderId}`)

  fetchJurisdictionInfo = data => this.requests.post(`/loans/jurisdictions/find`, data)

  submitLoanRequest = data => this.requests.post(`/loans/request`, data)

  submitNonLendable = data => this.requests.post(`/loanBacklogs`, data)

  fetchLoanConstraints = (query, loan) => this.requests.post(`/legality`, {query, loan})

  checkLoanLegality = loan => this.requests.post(`/legality`, {loan})

  deleteLoanRequest = id => this.requests.delete(`/loans/request/${id}`)

  createWithdrawalAddress = payload => this.requests.post('/withdrawalAddresses', payload)

  resendWithdrawalAddress = id => this.requests.post(`/withdrawalAddresses/${id}/resend`)

  verifyWithdrawalAddress = (id, payload) =>
    this.requests.post(`/withdrawalAddresses/${id}/verify`, payload)

  patchAddress = (id, payload) => this.patch(`/addresses/${id}`, payload)

  patchUser = payload => this.patch(`/users/me`, payload)

  patchBusiness = payload => this.patch(`/accounts/myAccount/businessProfile`, payload)

  validateAffiliate = code => this.requests.get(`/affiliates/${code}/validate`)

  getLendableAreas = () => this.requests.get(`/jurisdictions/all`)

  checkIllegal = payload => this.requests.post(`/legality`, payload)

  nonLendableLoanReq = data => this.requests.post(`/loanBacklogs`, data)

  cancelNonLendable = id => this.requests.delete(`/loanBacklogs/${id}`)

  getStabilization = id => this.requests.get(`loans/${id}/latestStabilization`)

  requestConversion = (id, payload) => this.requests.post(`loans/${id}/requestConversion`, payload)

  redeemSalt = id => this.requests.post(`loans/${id}/redeemSalt`)

  addToMailingList = payload =>
    this.requests.post(`questionnaire/addToMarketingMailingList`, payload)

  getLoanRatesMaps = () => this.requests.get(`/loanTerms/rateMaps`)

  getAdminFee = () => this.requests.get(`/loanTerms/fees`)

  zaboConnect = (id, token) => this.requests.post(`/zabo`, {id, token})

  getZaboWallets = () => this.requests.get(`/zabo/wallets`)

  deleteZaboWallet = id => this.requests.delete(`/zabo/${id}`)

  getZaboTx = id => this.requests.get(`/zabo/${id}/transactions`)

  getInvestmentRates = () => {
    const config = {
      params: {
        ref: 1,
      },
    }
    return this.requests.get('/investmentRates', config)
  }

  getInvestments = () => this.requests.get('/investments/myInvestments')

  submitInvestmentRequest = data => this.requests.post('/investments/request', data)

  sendAccreditedEmail = () =>
    this.requests.post('/users/sendAccreditedInvestorVerificationEmail', {})

  cancelInvestmentRequest = id => this.requests.delete(`/investments/request/${id}`)

  acknowledgeInvestmentRequest = id => this.requests.patch(`/investments/${id}/acknowledge`)

  getPayout = id => {
    return this.requests.get(`/investments/${id}/payouts`)
  }

  requestClosure = id => {
    return this.requests.post(`/investments/${id}/requestClosure`)
  }

  closeAccount = (investmentId, body) => {
    return this.requests.post(`/investments/${investmentId}/withdraw`, body)
  }

  //remove after u21 flag removal - disable-unit-21
  unit21UpdateEntity = id => this.requests.post(`/externalVerifications/${id}/updateEntity`)

  unit21Check1 = id => this.requests.post(`/externalVerifications/${id}/identityVerification`)

  //remove after u21 flag removal - disable-unit-21
  unit21UploadID = (id, payload) =>
    this.requests.post(`/externalVerifications/${id}/manualUploadPhotoIdVerification`, payload)

  unit21Bankruptcy = payload => this.requests.post(`/externalVerifications/bankruptcy`, payload)

  addNewBeneficiary = payload =>
    this.requests.post(`/externalVerifications/addBeneficiary`, payload)

  submitBeneficiaries = () => this.requests.post(`/externalVerifications/beneficiaries`)

  updateBusinessEntity = payload =>
    this.requests.post(`/externalVerifications/updateBusinessEntity`, payload)

  businessVerification = () => this.requests.post(`/externalVerifications/businessVerification`)

  removeBeneficiary = id => this.requests.delete(`/externalVerifications/${id}/deleteBeneficiary`)

  setNotAvailable = id =>
    this.patch(`/externalVerifications/${id}/updateIdentityVerificationStatus`)

  removeDocument = id => this.requests.delete(`/documents/${id}`)

  getStackwise = id => this.requests.get(`/rewards/${id}/getCurrentRewards`)

  updateRewardPreference = (id, payload) =>
    this.patch(`/rewards/${id}/updateRewardPreference`, payload)

  updateRewardAllocation = (id, payload) =>
    this.patch(`/rewards/${id}/updateRewardAllocation`, payload)

  transferSend = (payload, ref) => {
    const config = {
      params: {
        ref: ref,
      },
    }
    return this.requests.post(`/wallets/transfer`, payload, config)
  }

  getWithdrawalOLTVMax = (id, token) => this.requests.get(`/loans/${id}/maxwithdrawal/${token}`)

  loanMilitaryQuestions = (payload, id) => this.patch(`/loans/${id}/questionnaire`, payload)

  checkAch = id => this.requests.get(`/scheduledPayments/${id}/pendingACH`)

  pendingAch = loanId => this.requests.get(`/dwolla/${loanId}/pendingACH`)

  achLimit = payload => this.requests.post(`/dwolla/validateAchLimit`, payload)

  sourceBalance = payload => this.requests.post(`/dwolla/validateFundingSourceBalance`, payload)

  checkReferral = code => this.requests.get(`/users/validateReferralCode/${code}`)

  getEntitys = () => this.requests.get(`/entityProfiles`)
  newEntity = () => this.requests.post(`/entityProfiles`)
  updateEntity = (id, payload) => this.patch(`/entityProfiles/${id}`, payload)
  deleteEntity = id => this.requests.delete(`/entityProfiles/${id}`)
  assignEntity = (id, ref) => {
    const config = {
      params: {
        ref: ref,
      },
    }
    return this.requests.post(`/entityProfiles/${id}/assignToAccount`, {}, config)
  }

  versionInfo = () => this.requests.get(`/devices/versionInfo`)

  devices = () => this.requests.get(`/devices/myDevices`)

  checkDiscount = code => this.requests.get(`/affiliates/${code}/validate`)

  getRefinanceInfo = (loanId, payload) => {
    return this.requests.post(`/loans/${loanId}/refinanceInfo`, payload)
  }

  loanRefinance = (id, payload) => {
    return this.requests.post(`/loans/${id}/refinance/request`, payload)
  }

  getPendingRefinanceLoan = () => {
    return this.requests.get(`/loans/pendingRefinanceLoan`)
  }

  getPendingRefinanceLoanRef = ref => {
    return this.requests.get(`/loans/pendingRefinanceLoan`, {
      params: {ref: ref},
    })
  }

  deleteDocument = id => {
    return this.requests.delete(`/documents/references/${id}`)
  }

  payoffLoans = (id, payload) => {
    return this.requests.post(`/loans/${id}/cryptoPayoff`, payload)
  }

  payoffWithStablecoin = (loanId, data) => {
    const reqData = {...data, intent: 'payment'} // default to payment
    return this.requests.post(`/loans/${loanId}/cryptoPayments/payoff`, reqData)
  }

  scheduleLoanPayoff = (loanId, data) => {
    return this.requests.post(`/loans/${loanId}/schedulePayoff`, data)
  }

  getDwollaCustomers = () => {
    return this.requests.get(`/dwolla/customers`)
  }

  makeDwollaCustomer = data => {
    return this.requests.post(`/dwolla/customers`, data)
  }

  dwollaExchangeSession = (customerId, mobileOS = Platform.OS == 'android' ? 'Android' : 'IOS') => {
    console.log('dwollaExchangeSession', customerId, mobileOS)
    return this.requests.post(`/dwolla/customers/${customerId}/exchange-sessions`, {mobileOS})
  }

  dwollaExchange = (customerId, data) => {
    return this.requests.post(`/dwolla/customers/${customerId}/exchanges`, data)
  }

  dwollaFundingSource = (customerId, data) => {
    return this.requests.post(`/dwolla/customers/${customerId}/funding-sources`, data)
  }

  setMarginManagementPreference = (id, data) => {
    return this.requests.patch(`/loans/${id}/setMarginManagementPreference`, data)
  }
}

export default WebService
