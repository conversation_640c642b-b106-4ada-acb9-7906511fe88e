import {StyleSheet, StatusBar} from 'react-native'

const statusHeight = StatusBar.currentHeight

// Colors

const brightMagenta = '#00FFBD'

const commonStyles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: '#28283D', //#F5FCFF
  },
  tabContainer: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: '#FFF', //'#FFFFFF', //#F5FCFF
  },
  scrollContainerView: {
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  tabBarIcon: {
    height: 21,
    width: 20,
  },
  tabText: {
    color: '#00FFBD',
    fontSize: 11,
    marginTop: -4,
    textAlign: 'center',
    marginBottom: 5,
  },
  tabTextLeft: {
    color: '#00FFBD',
    fontSize: 11,
    marginTop: -4,
    textAlign: 'center',
    marginBottom: 5,
    marginLeft: -30,
  },
  tabTextRight: {
    color: '#00FFBD',
    fontSize: 11,
    marginTop: -4,
    textAlign: 'center',
    marginBottom: 5,
    marginRight: -30,
  },
  tabTextLeftIpad: {
    color: '#00FFBD',
    fontSize: 11,
    marginTop: 38,
    textAlign: 'center',
    marginLeft: -30,
  },
  tabTextRightIpad: {
    color: '#00FFBD',
    fontSize: 11,
    marginTop: 38,
    textAlign: 'center',
    marginRight: -30,
  },
  button: {
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    zIndex: 5,
    minWidth: 220,
    height: 50,
    fontSize: 22,
    paddingVertical: 9,
    paddingHorizontal: 21,
    backgroundColor: '#00FFBD',
  },
  modalBox: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: '#FFF', //'rgba(74, 74, 74, 0.5)',
    marginTop: statusHeight,
  },
  modalSquare: {
    width: '86%',
    backgroundColor: '#3D3D50',
    borderRadius: 8,
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: 30,
  },
  modalX: {
    position: 'absolute',
    top: 4,
    right: 4,
  },
  modalTitleBox: {
    marginTop: 24,
    marginBottom: 14,
  },
  modalTitle: {
    fontSize: 24,
    color: '#e6e6e6',
  },
  modalButton: {
    backgroundColor: '#05868e',
    borderRadius: 6,
    width: 160,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 30,
    marginBottom: 30,
  },
  modalButtonText: {
    fontSize: 22,
    color: '#FFF',
  },
  modalDescriptionBox: {
    marginLeft: 30,
    marginRight: 30,
  },
  modalDescription: {
    color: '#e6e6e6',
    textAlign: 'center',
    fontSize: 16,
  },
  closeModalImg: {
    height: 30,
    width: 30,
    margin: 4,
  },
  splashText: {
    marginTop: 100,
  },

  //update Phone Screen
  updatePhonePic: {
    height: 125,
    width: 96,
    marginTop: 140,
    marginRight: -24,
    marginBottom: 40,
  },
  updateTitle: {
    fontFamily: 'Europa-Regular',
    fontSize: 28,
    color: '#e6e6e6',
    marginBottom: 30,
  },
  updateDescription: {
    fontFamily: 'Europa-Regular',
    fontSize: 18,
    textAlign: 'center',
    color: '#e6e6e6',
    width: 280,
    marginBottom: 30,
  },
  updateErrorText: {
    color: '#E5705A',
    fontSize: 16,
    marginTop: 14,
  },
  wavesBox: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    height: 600,
    borderRadius: 0,
    zIndex: -1,
  },
  waves: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    height: 600,
    borderRadius: 0,
    zIndex: -1,
  },
  //Fonts
  EuropaRegular: {
    fontFamily: 'Europa-Regular',
  },
  EuropaBold: {
    fontFamily: 'Europa-Bold',
  },

  //Tiles
  tileInner: {
    alignSelf: 'stretch',
    padding: 10,
    backgroundColor: '#3D3D50',
    borderRadius: 14,
    flexDirection: 'column',
    alignItems: 'center',
    overflow: 'hidden',
  },
  tileContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    backgroundColor: '#28283D',
    alignItems: 'center',
    //paddingTop: cardContainerMarginTop,
  },
  tileContainerDark: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    backgroundColor: '#28283d',
    alignItems: 'center',
  },
  backgroundImg: {
    top: 0,
    bottom: 0,
    width: '100%',
    flex: 1,
    borderRadius: 0,
    zIndex: -1,
    padding: 0,
    opacity: 1,
  },
})

export default commonStyles
