
> mobile73@2.15.53 android
> react-native run-android

info Installing the app...
> Task :gradle-plugin:compileKotlin UP-TO-DATE
> Task :gradle-plugin:compileJava NO-SOURCE
> Task :gradle-plugin:pluginDescriptors UP-TO-DATE
> Task :gradle-plugin:processResources UP-TO-DATE
> Task :gradle-plugin:classes UP-TO-DATE
> Task :gradle-plugin:jar UP-TO-DATE
> Task :gradle-plugin:inspectClassesForKotlinIC UP-TO-DATE

> Configure project :heap_react-native-heap
Couldn't auto-initialize the Heap library; please initialize it manually by following the instructions at https://docs.heap.io/docs/react-native.
Couldn't auto-initialize the Heap library; please initialize it manually by following the instructions at https://docs.heap.io/docs/react-native.

> Configure project :react-native-firebase_analytics
:react-native-firebase_analytics package.json found at /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/analytics/package.json
:react-native-firebase_app package.json found at /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/app/package.json
:react-native-firebase_analytics:firebase.bom using default value: 32.8.1
:react-native-firebase_analytics package.json found at /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/analytics/package.json
:react-native-firebase_analytics:version set from package.json: 19.2.2 (19,2,2 - 19002002)
:react-native-firebase_analytics:android.compileSdk using custom value: 34
:react-native-firebase_analytics:android.targetSdk using custom value: 34
:react-native-firebase_analytics:android.minSdk using custom value: 21
:react-native-firebase_analytics:reactNativeAndroidDir /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native/android

> Configure project :react-native-firebase_app
:react-native-firebase_app package.json found at /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/app/package.json
:react-native-firebase_app:firebase.bom using default value: 32.8.1
:react-native-firebase_app:play.play-services-auth using default value: 21.0.0
:react-native-firebase_app package.json found at /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/app/package.json
:react-native-firebase_app:version set from package.json: 19.2.2 (19,2,2 - 19002002)
:react-native-firebase_app:android.compileSdk using custom value: 34
:react-native-firebase_app:android.targetSdk using custom value: 34
:react-native-firebase_app:android.minSdk using custom value: 21
:react-native-firebase_app:reactNativeAndroidDir /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native/android

> Configure project :react-native-firebase_messaging
:react-native-firebase_messaging package.json found at /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/messaging/package.json
:react-native-firebase_app package.json found at /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/app/package.json
:react-native-firebase_messaging:firebase.bom using default value: 32.8.1
:react-native-firebase_messaging package.json found at /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/messaging/package.json
:react-native-firebase_messaging:version set from package.json: 19.2.2 (19,2,2 - 19002002)
:react-native-firebase_messaging:android.compileSdk using custom value: 34
:react-native-firebase_messaging:android.targetSdk using custom value: 34
:react-native-firebase_messaging:android.minSdk using custom value: 21
:react-native-firebase_messaging:reactNativeAndroidDir /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native/android

> Configure project :react-native-firebase_remote-config
:react-native-firebase_remote-config package.json found at /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/remote-config/package.json
:react-native-firebase_app package.json found at /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/app/package.json
:react-native-firebase_remote-config:firebase.bom using default value: 32.8.1
:react-native-firebase_remote-config package.json found at /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/remote-config/package.json
:react-native-firebase_remote-config:version set from package.json: 19.2.2 (19,2,2 - 19002002)
:react-native-firebase_remote-config:android.compileSdk using custom value: 34
:react-native-firebase_remote-config:android.targetSdk using custom value: 34
:react-native-firebase_remote-config:android.minSdk using custom value: 21
:react-native-firebase_remote-config:reactNativeAndroidDir /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native/android

> Configure project :react-native-reanimated
Android gradle plugin: 8.1.1
Gradle: 8.3

> Configure project :react-native-vision-camera
[VisionCamera] Thank you for using VisionCamera ❤️
[VisionCamera] If you enjoy using VisionCamera, please consider sponsoring this project: http://github.com/sponsors/mrousavy
[VisionCamera] node_modules found at /Users/<USER>/Documents/reactNative/mobile73/node_modules
[VisionCamera] VisionCamera_enableFrameProcessors is set to true!
[VisionCamera] react-native-worklets-core not found, Frame Processors are disabled!
[VisionCamera] VisionCamera_enableCodeScanner is set to false!
org.jetbrains.kotlin
com.facebook.react
com.facebook.react
com.facebook.react
androidx.core
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
org.jetbrains.kotlin
org.jetbrains.kotlin
androidx.appcompat
androidx.appcompat
androidx.autofill
androidx.swiperefreshlayout
androidx.tracing
com.facebook.fbjni
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.infer.annotation
com.facebook.soloader
com.facebook.yoga
com.google.code.findbugs
com.squareup.okhttp3
com.squareup.okhttp3
com.squareup.okio
javax.inject
com.facebook.flipper
com.facebook.flipper
com.facebook.fresco
com.facebook.react
androidx.annotation
androidx.annotation
org.jetbrains.kotlin
org.jetbrains.kotlin
androidx.activity
androidx.multidex
com.jumio.android
com.jumio.android
com.jumio.android
com.jumio.android
com.jumio.android
com.jumio.android
com.jumio.android
com.jumio.android
com.jumio.android
com.jumio.android
com.facebook.react
com.heapanalytics.android
com.google.firebase
com.google.firebase
com.google.firebase
com.google.android.gms
com.google.firebase
com.google.firebase
com.facebook.react
com.launchdarkly
com.jakewharton.timber
com.google.code.gson
com.airbnb.android
com.google.android.gms
com.google.android.gms
com.google.zxing
com.drewnoakes
androidx.exifinterface
androidx.annotation
androidx.legacy
com.google.android.gms
com.android.installreferrer
com.google.android.gms
androidx.appcompat
androidx.core
androidx.core
androidx.exifinterface
androidx.appcompat
androidx.biometric
androidx.lifecycle
androidx.fragment
com.facebook.conceal
com.plaid.link
org.jetbrains.kotlin
com.jakewharton.rxrelay2
com.android.support
me.leolin
com.google.firebase
androidx.transition
androidx.core
androidx.fragment
androidx.coordinatorlayout
com.google.android.material
androidx.core
androidx.lifecycle
com.facebook.react
androidx.camera
androidx.camera
androidx.camera
androidx.camera
androidx.camera
androidx.camera
org.jetbrains.kotlinx
com.google.android.gms
androidx.webkit
com.squareup.okhttp3
com.squareup.okhttp3
com.squareup.okhttp3
org.jetbrains.kotlin
org.jetbrains
androidx.activity
androidx.annotation
androidx.collection
androidx.core
androidx.cursoradapter
androidx.drawerlayout
androidx.emoji2
androidx.emoji2
androidx.lifecycle
androidx.lifecycle
androidx.resourceinspection
androidx.savedstate
org.jetbrains.kotlin
androidx.vectordrawable
androidx.vectordrawable
androidx.core
androidx.annotation
androidx.interpolator
com.facebook.soloader
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
org.jetbrains.kotlin
com.squareup.okhttp3
com.google.code.findbugs
org.jetbrains.kotlin
com.facebook.soloader
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlinx
com.android.ndk.thirdparty
com.facebook.fbjni
com.facebook.soloader
androidx.appcompat
androidx.sqlite
org.java-websocket
org.jetbrains.kotlinx
com.facebook.fresco
com.facebook.fresco
com.facebook.flipper
com.facebook.stetho
com.parse.bolts
androidx.annotation
org.jetbrains.kotlin
org.jetbrains.kotlin
androidx.activity
androidx.core
androidx.lifecycle
androidx.lifecycle
androidx.savedstate
org.jetbrains.kotlin
androidx.activity
io.github.crow-misia.libyuv
com.google.android.material
androidx.constraintlayout
androidx.core
org.tensorflow
androidx.annotation
org.tensorflow
org.bouncycastle
org.jmrtd
net.sf.scuba
com.iproov.sdk
androidx.recyclerview
androidx.navigation
androidx.navigation
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
com.datadoghq
androidx.camera
androidx.camera
androidx.camera
androidx.camera
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
androidx.fragment
androidx.loader
com.google.android.gms
com.google.android.gms
com.google.android.gms
com.google.android.gms
com.google.android.gms
com.google.android.gms
com.google.code.gson
androidx.appcompat
com.launchdarkly
com.launchdarkly
com.launchdarkly
com.launchdarkly
org.jetbrains.kotlin
org.jetbrains
androidx.appcompat
com.squareup.okio
com.google.android.datatransport
com.google.android.datatransport
com.google.android.datatransport
com.google.android.gms
com.google.android.gms
com.google.android.gms
com.google.android.odml
com.google.firebase
com.google.firebase
com.google.firebase
com.google.mlkit
com.google.mlkit
com.adobe.xmp
androidx.core
androidx.media
androidx.legacy
androidx.legacy
androidx.fragment
com.google.android.gms
com.google.android.gms
com.google.android.gms
com.google.android.gms
androidx.databinding
org.jetbrains.kotlin
androidx.browser
androidx.fragment
androidx.constraintlayout
com.google.android.material
androidx.recyclerview
androidx.room
androidx.lifecycle
io.coil-kt
com.google.dagger
com.google.code.gson
org.jetbrains.kotlinx
org.jetbrains.kotlinx
com.squareup.retrofit2
com.squareup.retrofit2
com.squareup.retrofit2
com.squareup.okhttp3
org.bouncycastle
com.google.protobuf
androidx.work
org.jetbrains.kotlin
io.reactivex.rxjava2
androidx.core
androidx.core
androidx.customview
com.google.android.gms
com.google.android.gms
com.google.android.gms
com.google.mlkit
com.google.mlkit
com.google.mlkit
com.google.mlkit
androidx.annotation
androidx.concurrent
androidx.lifecycle
androidx.lifecycle
com.google.auto.value
com.google.guava
androidx.camera
androidx.camera
androidx.camera
androidx.camera
androidx.core
androidx.lifecycle
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlin
com.squareup.okhttp3
com.squareup.okhttp3
org.jetbrains.kotlin
androidx.annotation
androidx.lifecycle
androidx.versionedparcelable
androidx.lifecycle
androidx.collection
androidx.lifecycle
androidx.startup
androidx.arch.core
androidx.arch.core
androidx.lifecycle
androidx.lifecycle
org.jetbrains.kotlin
org.jetbrains.kotlin
androidx.sqlite
org.slf4j
org.jetbrains.kotlinx
commons-cli
androidx.core
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.profileinstaller
androidx.savedstate
androidx.tracing
org.jetbrains.kotlin
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
org.jetbrains.kotlin
com.google.errorprone
androidx.cardview
androidx.coordinatorlayout
androidx.constraintlayout
androidx.drawerlayout
androidx.dynamicanimation
androidx.annotation
androidx.fragment
androidx.lifecycle
androidx.recyclerview
androidx.transition
androidx.viewpager2
androidx.constraintlayout
androidx.core
org.tensorflow
org.checkerframework
com.google.flatbuffers
net.sf.scuba
org.bouncycastle
org.ejbca.cvc
org.jetbrains.kotlinx
org.jetbrains.kotlinx
com.tinder.statemachine
androidx.appcompat
com.squareup.okhttp3
com.google.protobuf
androidx.databinding
org.jetbrains.kotlin
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.core
androidx.customview
androidx.viewpager2
androidx.navigation
androidx.navigation
androidx.navigation
androidx.navigation
androidx.navigation
androidx.navigation
androidx.navigation
androidx.navigation
androidx.navigation
androidx.navigation
androidx.navigation
androidx.navigation
androidx.lifecycle
androidx.fragment
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
org.jetbrains.kotlinx
androidx.core
com.squareup.okhttp3
androidx.metrics
androidx.navigation
androidx.navigation
com.datadoghq
com.google.android.gms
com.google.android.gms
com.google.android.gms
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.errorprone
com.google.firebase
com.google.firebase
com.google.android.datatransport
com.google.android.datatransport
com.google.android.datatransport
com.google.android.gms
com.google.android.gms
com.google.firebase
com.google.firebase
com.google.firebase
org.jetbrains.kotlinx
androidx.annotation
androidx.concurrent
com.google.android.gms
com.google.android.gms
androidx.activity
androidx.lifecycle
androidx.loader
androidx.viewpager
androidx.lifecycle
androidx.lifecycle
com.google.android.gms
com.google.android.gms
com.launchdarkly
androidx.versionedparcelable
androidx.documentfile
androidx.localbroadcastmanager
androidx.print
androidx.coordinatorlayout
androidx.slidingpanelayout
androidx.swiperefreshlayout
androidx.asynclayoutinflater
org.jetbrains.kotlin
androidx.activity
androidx.collection
androidx.fragment
androidx.lifecycle
androidx.lifecycle
androidx.savedstate
androidx.room
androidx.room
org.jetbrains.kotlinx
androidx.room
androidx.room
androidx.room
androidx.room
androidx.room
androidx.room
androidx.room
androidx.room
androidx.room
androidx.room
androidx.room
androidx.room
androidx.room
io.coil-kt
com.google.code.gson
com.squareup.okhttp3
com.google.protobuf
org.jetbrains.kotlin
androidx.lifecycle
androidx.room
androidx.sqlite
org.jetbrains.kotlin
org.jetbrains.kotlin
org.reactivestreams
androidx.core
androidx.exifinterface
com.google.mlkit
com.google.mlkit
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
com.squareup.okio
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlinx
org.jetbrains.kotlin
androidx.startup
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
androidx.customview
androidx.core
org.jetbrains.kotlin
androidx.arch.core
androidx.arch.core
androidx.fragment
androidx.recyclerview
androidx.core
androidx.fragment
androidx.slidingpanelayout
androidx.annotation
androidx.transition
com.google.android.material
androidx.core
com.lyft.kronos
androidx.work
androidx.activity
com.google.android.gms
com.google.android.gms
com.google.android.gms
com.google.android.gms
com.google.firebase
com.google.firebase
com.google.guava
com.google.firebase
com.google.firebase
org.jetbrains.kotlin
com.google.firebase
com.google.firebase
com.google.android.datatransport
com.google.firebase
androidx.annotation
androidx.sqlite
androidx.sqlite
org.jetbrains.kotlinx
androidx.lifecycle
com.squareup.okhttp3
org.jetbrains.kotlin
androidx.appcompat
androidx.core
androidx.exifinterface
com.google.android.gms
org.jetbrains
org.jetbrains.kotlin
com.squareup.okio
org.jetbrains.kotlinx
androidx.activity
androidx.fragment
androidx.fragment
androidx.fragment
androidx.window
androidx.transition
com.lyft.kronos
androidx.core
androidx.privacysandbox.ads
androidx.privacysandbox.ads
com.google.guava
com.google.guava
org.checkerframework
com.google.errorprone
com.google.j2objc
androidx.sqlite
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
androidx.activity
org.jetbrains.kotlinx
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains
org.jetbrains.kotlin
org.jetbrains.kotlin
com.facebook.react
androidx.appcompat
androidx.activity
androidx.annotation
androidx.annotation
androidx.collection
androidx.core
androidx.annotation
androidx.concurrent
com.google.guava
androidx.interpolator
androidx.lifecycle
androidx.arch.core
androidx.arch.core
androidx.lifecycle
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
com.google.android.gms
com.google.android.gms
androidx.fragment
androidx.core
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.startup
androidx.tracing
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.savedstate
androidx.savedstate
androidx.lifecycle
androidx.loader
androidx.profileinstaller
androidx.viewpager
androidx.customview
androidx.fragment
androidx.activity
androidx.collection
androidx.versionedparcelable
androidx.appcompat
androidx.vectordrawable
androidx.vectordrawable
androidx.cursoradapter
androidx.drawerlayout
androidx.emoji2
androidx.emoji2
androidx.resourceinspection
androidx.autofill
androidx.swiperefreshlayout
com.facebook.fbjni
com.facebook.soloader
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.soloader
com.facebook.soloader
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.parse.bolts
com.facebook.fresco
com.facebook.infer.annotation
com.google.code.findbugs
org.jetbrains.kotlin
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.squareup.okhttp3
com.squareup.okhttp3
com.squareup.okio
com.squareup.okio
com.facebook.yoga
com.squareup.okhttp3
javax.inject
com.facebook.react
com.facebook.flipper
com.android.ndk.thirdparty
androidx.sqlite
androidx.sqlite
org.java-websocket
org.slf4j
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
com.facebook.flipper
com.facebook.fresco
com.facebook.fresco
com.facebook.stetho
commons-cli
com.facebook.react
androidx.core
androidx.multidex
com.jumio.android
io.github.crow-misia.libyuv
com.google.android.material
org.jetbrains.kotlin
com.google.errorprone
androidx.cardview
androidx.coordinatorlayout
androidx.constraintlayout
androidx.constraintlayout
androidx.dynamicanimation
androidx.legacy
androidx.documentfile
androidx.localbroadcastmanager
androidx.print
androidx.recyclerview
androidx.customview
androidx.viewpager2
androidx.transition
com.jumio.android
org.tensorflow
org.tensorflow
org.tensorflow
org.checkerframework
com.google.flatbuffers
com.jumio.android
org.bouncycastle
org.jmrtd
net.sf.scuba
org.ejbca.cvc
net.sf.scuba
com.jumio.android
com.google.android.gms
com.google.android.datatransport
com.google.android.datatransport
com.google.android.datatransport
com.google.firebase
com.google.firebase
com.google.firebase
com.google.android.gms
com.google.android.odml
com.google.firebase
com.google.firebase
com.google.mlkit
com.google.mlkit
androidx.exifinterface
com.google.mlkit
com.google.mlkit
com.jumio.android
com.jumio.android
com.iproov.sdk
com.tinder.statemachine
com.google.protobuf
androidx.databinding
com.jumio.android
androidx.navigation
androidx.navigation
androidx.navigation
androidx.navigation
androidx.navigation
androidx.navigation
androidx.navigation
androidx.navigation
androidx.slidingpanelayout
androidx.window
androidx.lifecycle
com.jumio.android
com.datadoghq
com.google.code.gson
androidx.metrics
com.datadoghq
com.lyft.kronos
com.lyft.kronos
androidx.work
androidx.room
androidx.room
androidx.room
com.jumio.android
com.jumio.android
androidx.camera
androidx.camera
com.google.auto.value
androidx.camera
androidx.camera
androidx.camera
androidx.camera
com.heapanalytics.android
com.google.firebase
com.google.firebase
com.google.android.gms
com.google.android.gms
com.google.android.gms
com.google.android.gms
androidx.privacysandbox.ads
androidx.privacysandbox.ads
com.google.guava
com.google.guava
com.google.j2objc
com.google.android.gms
com.google.android.gms
com.google.android.gms
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.android.gms
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.firebase
com.google.android.gms
com.google.firebase
com.google.android.gms
com.google.android.gms
com.google.android.gms
com.google.android.gms
com.launchdarkly
com.launchdarkly
com.launchdarkly
com.launchdarkly
com.launchdarkly
com.jakewharton.timber
com.airbnb.android
com.google.android.gms
com.google.zxing
com.drewnoakes
com.adobe.xmp
androidx.legacy
androidx.media
androidx.legacy
androidx.asynclayoutinflater
com.google.android.gms
com.android.installreferrer
com.google.android.gms
androidx.biometric
com.facebook.conceal
com.plaid.link
androidx.browser
io.coil-kt
io.coil-kt
org.jetbrains.kotlin
com.google.dagger
com.squareup.retrofit2
com.squareup.retrofit2
com.squareup.retrofit2
com.google.protobuf
org.jetbrains.kotlin
com.jakewharton.rxrelay2
io.reactivex.rxjava2
org.reactivestreams
me.leolin
androidx.webkit
com.squareup.okhttp3
com.facebook.react
com.facebook.react
com.facebook.react
androidx.core
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
mobile73
com.facebook.react
com.facebook.react
com.facebook.react
org.jetbrains.kotlin
org.jetbrains.kotlin
com.heapanalytics.android
com.facebook.react
androidx.appcompat
androidx.appcompat
androidx.autofill
androidx.swiperefreshlayout
androidx.tracing
com.facebook.fbjni
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.infer.annotation
com.facebook.soloader
com.facebook.yoga
com.google.code.findbugs
com.squareup.okhttp3
com.squareup.okhttp3
com.squareup.okio
javax.inject
org.jetbrains.kotlin
org.jetbrains
org.jetbrains.kotlin
org.jetbrains.kotlin
androidx.activity
androidx.annotation
androidx.core
androidx.cursoradapter
androidx.drawerlayout
androidx.fragment
androidx.savedstate
androidx.annotation
androidx.core
androidx.vectordrawable
androidx.vectordrawable
androidx.annotation
androidx.core
androidx.interpolator
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
com.facebook.fresco
org.jetbrains.kotlin
com.squareup.okhttp3
com.google.code.findbugs
org.jetbrains.kotlin
com.facebook.soloader
com.facebook.soloader
com.squareup.okhttp3
org.jetbrains.kotlin
com.squareup.okhttp3
com.squareup.okio
androidx.core
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.savedstate
org.jetbrains.kotlin
androidx.activity
androidx.activity
androidx.annotation
androidx.annotation
androidx.annotation
androidx.lifecycle
androidx.versionedparcelable
androidx.annotation
androidx.core
androidx.customview
androidx.activity
androidx.annotation
androidx.collection
androidx.core
androidx.lifecycle
androidx.loader
androidx.viewpager
org.jetbrains.kotlin
androidx.fragment
androidx.fragment
androidx.fragment
org.jetbrains.kotlin
androidx.savedstate
com.facebook.fresco
com.squareup.okio
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
androidx.arch.core
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
androidx.lifecycle
org.jetbrains.kotlinx
org.jetbrains.kotlin
androidx.collection
androidx.core
androidx.core
androidx.core
androidx.lifecycle
androidx.lifecycle
androidx.customview
org.jetbrains.kotlin
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlin
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains.kotlinx
org.jetbrains
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.kotlin
org.jetbrains.intellij.deps
net.java.dev.jna
org.jetbrains.kotlin
org.jetbrains
WARNING: The specified Android SDK Build Tools version (30.0.3) is ignored, as it is below the minimum supported version (33.0.1) for Android Gradle Plugin 8.1.1.
Android SDK Build Tools 33.0.1 will be used.
To suppress this warning, remove "buildToolsVersion '30.0.3'" from your build.gradle file, as each version of the Android Gradle Plugin now has a default version of the build tools.

> Task :amazon-cognito-identity-js:preBuild UP-TO-DATE
> Task :amazon-cognito-identity-js:preDebugBuild UP-TO-DATE
> Task :amazon-cognito-identity-js:generateDebugBuildConfig
> Task :amazon-cognito-identity-js:generateDebugResValues
> Task :amazon-cognito-identity-js:generateDebugResources
> Task :amazon-cognito-identity-js:packageDebugResources
> Task :amazon-cognito-identity-js:parseDebugLocalResources
> Task :app:generateCodegenSchemaFromJavaScript SKIPPED
> Task :app:generateCodegenArtifactsFromSchema SKIPPED
> Task :amazon-cognito-identity-js:javaPreCompileDebug
> Task :amazon-cognito-identity-js:processDebugManifest
> Task :app:generatePackageList
> Task :app:preBuild
> Task :app:preDebugBuild
> Task :amazon-cognito-identity-js:generateDebugRFile
> Task :app:generateDebugBuildConfig
> Task :amazon-cognito-identity-js:compileDebugJavaWithJavac
> Task :amazon-cognito-identity-js:bundleLibCompileToJarDebug
> Task :amazon-cognito-identity-js:compileDebugLibraryResources
> Task :heap_react-native-heap:preBuild UP-TO-DATE
> Task :heap_react-native-heap:preDebugBuild UP-TO-DATE
> Task :amazon-cognito-identity-js:writeDebugAarMetadata
> Task :launchdarkly-react-native-client-sdk:preBuild UP-TO-DATE
> Task :launchdarkly-react-native-client-sdk:preDebugBuild UP-TO-DATE
> Task :heap_react-native-heap:writeDebugAarMetadata
> Task :lottie-react-native:preBuild UP-TO-DATE
> Task :lottie-react-native:preDebugBuild UP-TO-DATE
> Task :launchdarkly-react-native-client-sdk:writeDebugAarMetadata
> Task :react-native-async-storage_async-storage:preBuild UP-TO-DATE
> Task :react-native-async-storage_async-storage:preDebugBuild UP-TO-DATE
> Task :lottie-react-native:writeDebugAarMetadata
> Task :react-native-bootsplash:preBuild UP-TO-DATE
> Task :react-native-bootsplash:preDebugBuild UP-TO-DATE
> Task :react-native-async-storage_async-storage:writeDebugAarMetadata
> Task :react-native-camera:preBuild UP-TO-DATE
> Task :react-native-camera:preGeneralDebugBuild UP-TO-DATE
> Task :react-native-bootsplash:writeDebugAarMetadata
> Task :react-native-community_netinfo:preBuild UP-TO-DATE
> Task :react-native-community_netinfo:preDebugBuild UP-TO-DATE
> Task :react-native-camera:writeGeneralDebugAarMetadata
> Task :react-native-device-info:preBuild UP-TO-DATE
> Task :react-native-device-info:preDebugBuild UP-TO-DATE
> Task :react-native-community_netinfo:writeDebugAarMetadata
> Task :react-native-firebase_analytics:preBuild UP-TO-DATE
> Task :react-native-firebase_analytics:preDebugBuild UP-TO-DATE
> Task :react-native-device-info:writeDebugAarMetadata
> Task :react-native-firebase_app:preBuild UP-TO-DATE
> Task :react-native-firebase_app:preDebugBuild UP-TO-DATE
> Task :react-native-firebase_analytics:writeDebugAarMetadata
> Task :react-native-firebase_messaging:preBuild UP-TO-DATE
> Task :react-native-firebase_messaging:preDebugBuild UP-TO-DATE
> Task :react-native-firebase_app:writeDebugAarMetadata
> Task :react-native-firebase_remote-config:preBuild UP-TO-DATE
> Task :react-native-firebase_remote-config:preDebugBuild UP-TO-DATE
> Task :react-native-firebase_messaging:writeDebugAarMetadata
> Task :react-native-fs:preBuild UP-TO-DATE
> Task :react-native-fs:preDebugBuild UP-TO-DATE
> Task :react-native-firebase_remote-config:writeDebugAarMetadata
> Task :react-native-gesture-handler:preBuild UP-TO-DATE
> Task :react-native-gesture-handler:preDebugBuild UP-TO-DATE
> Task :react-native-fs:writeDebugAarMetadata
> Task :react-native-image-picker:preBuild UP-TO-DATE
> Task :react-native-image-picker:preDebugBuild UP-TO-DATE
> Task :react-native-gesture-handler:writeDebugAarMetadata
> Task :react-native-jumio-mobilesdk:preBuild UP-TO-DATE
> Task :react-native-jumio-mobilesdk:preDebugBuild UP-TO-DATE
> Task :react-native-image-picker:writeDebugAarMetadata
> Task :react-native-keychain:preBuild UP-TO-DATE
> Task :react-native-keychain:preDebugBuild UP-TO-DATE
> Task :react-native-jumio-mobilesdk:writeDebugAarMetadata
> Task :react-native-linear-gradient:preBuild UP-TO-DATE
> Task :react-native-linear-gradient:preDebugBuild UP-TO-DATE
> Task :react-native-keychain:writeDebugAarMetadata
> Task :react-native-permissions:preBuild UP-TO-DATE
> Task :react-native-permissions:preDebugBuild UP-TO-DATE
> Task :react-native-linear-gradient:writeDebugAarMetadata
> Task :react-native-plaid-link-sdk:preBuild UP-TO-DATE
> Task :react-native-plaid-link-sdk:preDebugBuild UP-TO-DATE
> Task :react-native-permissions:writeDebugAarMetadata
> Task :react-native-push-notification:preBuild UP-TO-DATE
> Task :react-native-push-notification:preDebugBuild UP-TO-DATE
> Task :react-native-plaid-link-sdk:writeDebugAarMetadata
> Task :react-native-reanimated:assertLatestReactNativeWithNewArchitectureTask SKIPPED
> Task :react-native-reanimated:assertMinimalReactNativeVersionTask SKIPPED
> Task :react-native-push-notification:writeDebugAarMetadata
> Task :react-native-reanimated:prepareHeadersForPrefab
> Task :react-native-reanimated:preBuild
> Task :react-native-reanimated:preDebugBuild
> Task :react-native-safe-area-context:preBuild UP-TO-DATE
> Task :react-native-safe-area-context:preDebugBuild UP-TO-DATE
> Task :react-native-reanimated:writeDebugAarMetadata
> Task :react-native-screens:preBuild UP-TO-DATE
> Task :react-native-screens:preDebugBuild UP-TO-DATE
> Task :react-native-safe-area-context:writeDebugAarMetadata
> Task :react-native-svg:preBuild UP-TO-DATE
> Task :react-native-svg:preDebugBuild UP-TO-DATE
> Task :react-native-screens:writeDebugAarMetadata
> Task :react-native-svg:writeDebugAarMetadata
> Task :react-native-vision-camera:prepareHeaders
> Task :react-native-vision-camera:preBuild
> Task :react-native-vision-camera:preDebugBuild
> Task :react-native-webview:preBuild UP-TO-DATE
> Task :react-native-webview:preDebugBuild UP-TO-DATE
> Task :react-native-vision-camera:writeDebugAarMetadata
> Task :rn-fetch-blob:preBuild UP-TO-DATE
> Task :react-native-webview:writeDebugAarMetadata
> Task :rn-fetch-blob:preDebugBuild UP-TO-DATE
> Task :rn-fetch-blob:writeDebugAarMetadata
> Task :app:generateDebugResValues
> Task :app:processDebugGoogleServices
> Task :heap_react-native-heap:generateDebugResValues
> Task :heap_react-native-heap:generateDebugResources
> Task :heap_react-native-heap:packageDebugResources
> Task :launchdarkly-react-native-client-sdk:generateDebugResValues
> Task :launchdarkly-react-native-client-sdk:generateDebugResources
> Task :launchdarkly-react-native-client-sdk:packageDebugResources
> Task :lottie-react-native:generateDebugResValues
> Task :lottie-react-native:generateDebugResources
> Task :lottie-react-native:packageDebugResources
> Task :react-native-async-storage_async-storage:generateDebugResValues
> Task :react-native-async-storage_async-storage:generateDebugResources
> Task :react-native-async-storage_async-storage:packageDebugResources
> Task :react-native-bootsplash:generateDebugResValues
> Task :react-native-bootsplash:generateDebugResources
> Task :app:checkDebugAarMetadata
> Task :react-native-bootsplash:packageDebugResources
> Task :react-native-camera:generateGeneralDebugResValues
> Task :react-native-camera:generateGeneralDebugResources
> Task :react-native-camera:packageGeneralDebugResources
> Task :react-native-community_netinfo:generateDebugResValues
> Task :react-native-community_netinfo:generateDebugResources
> Task :react-native-community_netinfo:packageDebugResources
> Task :react-native-device-info:generateDebugResValues
> Task :react-native-device-info:generateDebugResources
> Task :react-native-device-info:packageDebugResources
> Task :react-native-firebase_analytics:generateDebugResValues
> Task :react-native-firebase_analytics:generateDebugResources
> Task :react-native-firebase_analytics:packageDebugResources
> Task :react-native-firebase_app:generateDebugResValues
> Task :react-native-firebase_app:generateDebugResources
> Task :react-native-firebase_app:packageDebugResources
> Task :react-native-firebase_messaging:generateDebugResValues
> Task :react-native-firebase_messaging:generateDebugResources
> Task :react-native-firebase_messaging:packageDebugResources
> Task :react-native-firebase_remote-config:generateDebugResValues
> Task :react-native-firebase_remote-config:generateDebugResources
> Task :react-native-firebase_remote-config:packageDebugResources
> Task :react-native-fs:generateDebugResValues
> Task :react-native-fs:generateDebugResources
> Task :react-native-fs:packageDebugResources
> Task :react-native-gesture-handler:generateDebugResValues
> Task :react-native-gesture-handler:generateDebugResources
> Task :react-native-gesture-handler:packageDebugResources
> Task :react-native-image-picker:generateDebugResValues
> Task :react-native-image-picker:generateDebugResources
> Task :react-native-image-picker:packageDebugResources
> Task :react-native-jumio-mobilesdk:generateDebugResValues
> Task :react-native-jumio-mobilesdk:generateDebugResources
> Task :react-native-jumio-mobilesdk:packageDebugResources
> Task :react-native-keychain:generateDebugResValues
> Task :react-native-keychain:generateDebugResources
> Task :react-native-keychain:packageDebugResources
> Task :react-native-linear-gradient:generateDebugResValues
> Task :react-native-linear-gradient:generateDebugResources
> Task :react-native-linear-gradient:packageDebugResources
> Task :react-native-permissions:generateDebugResValues
> Task :react-native-permissions:generateDebugResources
> Task :react-native-permissions:packageDebugResources
> Task :react-native-plaid-link-sdk:generateDebugResValues
> Task :react-native-plaid-link-sdk:generateDebugResources
> Task :react-native-plaid-link-sdk:packageDebugResources
> Task :react-native-push-notification:generateDebugResValues
> Task :react-native-push-notification:generateDebugResources
> Task :react-native-push-notification:packageDebugResources
> Task :react-native-reanimated:generateDebugResValues
> Task :react-native-reanimated:generateDebugResources
> Task :react-native-reanimated:packageDebugResources
> Task :react-native-safe-area-context:generateDebugResValues
> Task :react-native-safe-area-context:generateDebugResources
> Task :react-native-safe-area-context:packageDebugResources
> Task :react-native-screens:generateDebugResValues
> Task :react-native-screens:generateDebugResources
> Task :react-native-screens:packageDebugResources
> Task :react-native-svg:generateDebugResValues
> Task :react-native-svg:generateDebugResources
> Task :react-native-svg:packageDebugResources
> Task :react-native-vision-camera:generateDebugResValues
> Task :react-native-vision-camera:generateDebugResources
> Task :react-native-vision-camera:packageDebugResources
> Task :react-native-webview:generateDebugResValues
> Task :react-native-webview:generateDebugResources
> Task :react-native-webview:packageDebugResources
> Task :rn-fetch-blob:generateDebugResValues
> Task :rn-fetch-blob:generateDebugResources
> Task :rn-fetch-blob:packageDebugResources
> Task :app:mapDebugSourceSetPaths
> Task :app:generateDebugResources
> Task :app:packageDebugResources
> Task :app:createDebugCompatibleScreenManifests
> Task :amazon-cognito-identity-js:extractDeepLinksDebug
> Task :app:extractDeepLinksDebug
> Task :app:parseDebugLocalResources
> Task :heap_react-native-heap:extractDeepLinksDebug
> Task :launchdarkly-react-native-client-sdk:extractDeepLinksDebug

> Task :heap_react-native-heap:processDebugManifest
package="com.heapanalytics.reactnative" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/@heap/react-native-heap/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.heapanalytics.reactnative" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/@heap/react-native-heap/android/src/main/AndroidManifest.xml.

> Task :lottie-react-native:extractDeepLinksDebug

> Task :launchdarkly-react-native-client-sdk:processDebugManifest
package="com.launchdarkly.reactnative" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/launchdarkly-react-native-client-sdk/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.launchdarkly.reactnative" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/launchdarkly-react-native-client-sdk/android/src/main/AndroidManifest.xml.

> Task :react-native-async-storage_async-storage:extractDeepLinksDebug

> Task :lottie-react-native:processDebugManifest
package="com.airbnb.android.react.lottie" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/lottie-react-native/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.airbnb.android.react.lottie" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/lottie-react-native/android/src/main/AndroidManifest.xml.

> Task :react-native-bootsplash:extractDeepLinksDebug

> Task :react-native-async-storage_async-storage:processDebugManifest
package="com.reactnativecommunity.asyncstorage" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-async-storage/async-storage/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.reactnativecommunity.asyncstorage" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-async-storage/async-storage/android/src/main/AndroidManifest.xml.

> Task :react-native-camera:extractDeepLinksGeneralDebug
> Task :react-native-bootsplash:processDebugManifest
> Task :react-native-community_netinfo:extractDeepLinksDebug

> Task :react-native-camera:processGeneralDebugManifest
package="org.reactnative.camera" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-camera/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="org.reactnative.camera" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-camera/android/src/main/AndroidManifest.xml.

> Task :react-native-device-info:extractDeepLinksDebug

> Task :react-native-community_netinfo:processDebugManifest
package="com.reactnativecommunity.netinfo" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-community/netinfo/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.reactnativecommunity.netinfo" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-community/netinfo/android/src/main/AndroidManifest.xml.

> Task :react-native-firebase_analytics:extractDeepLinksDebug

> Task :react-native-device-info:processDebugManifest
package="com.learnium.RNDeviceInfo" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-device-info/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.learnium.RNDeviceInfo" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-device-info/android/src/main/AndroidManifest.xml.

> Task :react-native-firebase_app:extractDeepLinksDebug

> Task :react-native-firebase_analytics:processDebugManifest
package="io.invertase.firebase.analytics" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/analytics/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="io.invertase.firebase.analytics" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/analytics/android/src/main/AndroidManifest.xml.

> Task :react-native-firebase_messaging:extractDeepLinksDebug

> Task :react-native-firebase_app:processDebugManifest
package="io.invertase.firebase" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="io.invertase.firebase" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml.

> Task :react-native-firebase_remote-config:extractDeepLinksDebug

> Task :react-native-firebase_messaging:processDebugManifest
package="io.invertase.firebase.messaging" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="io.invertase.firebase.messaging" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml.

> Task :react-native-fs:extractDeepLinksDebug

> Task :react-native-firebase_remote-config:processDebugManifest
package="io.invertase.firebase.config" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/remote-config/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="io.invertase.firebase.config" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/@react-native-firebase/remote-config/android/src/main/AndroidManifest.xml.

> Task :react-native-gesture-handler:extractDeepLinksDebug

> Task :react-native-fs:processDebugManifest
package="com.rnfs" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-fs/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.rnfs" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-fs/android/src/main/AndroidManifest.xml.

> Task :react-native-image-picker:extractDeepLinksDebug

> Task :react-native-gesture-handler:processDebugManifest
package="com.swmansion.gesturehandler" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-gesture-handler/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.swmansion.gesturehandler" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-gesture-handler/android/src/main/AndroidManifest.xml.

> Task :react-native-jumio-mobilesdk:extractDeepLinksDebug

> Task :react-native-image-picker:processDebugManifest
package="com.imagepicker" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-image-picker/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.imagepicker" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-image-picker/android/src/main/AndroidManifest.xml.

> Task :react-native-jumio-mobilesdk:processDebugManifest
package="com.jumio.react" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-jumio-mobilesdk/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.jumio.react" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-jumio-mobilesdk/android/src/main/AndroidManifest.xml.
/Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-jumio-mobilesdk/android/src/main/AndroidManifest.xml:13:9-16:44 Warning:
	meta-data#com.google.android.gms.vision.DEPENDENCIES@android:value was tagged at AndroidManifest.xml:13 to replace other declarations but no other declaration present

> Task :react-native-keychain:extractDeepLinksDebug
> Task :react-native-linear-gradient:extractDeepLinksDebug
> Task :react-native-permissions:extractDeepLinksDebug

> Task :react-native-keychain:processDebugManifest
package="com.oblador.keychain" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-keychain/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.oblador.keychain" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-keychain/android/src/main/AndroidManifest.xml.

> Task :react-native-linear-gradient:processDebugManifest
package="com.BV.LinearGradient" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-linear-gradient/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.BV.LinearGradient" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-linear-gradient/android/src/main/AndroidManifest.xml.

> Task :react-native-plaid-link-sdk:extractDeepLinksDebug
> Task :react-native-permissions:processDebugManifest
> Task :app:mergeDebugResources
> Task :react-native-push-notification:extractDeepLinksDebug

> Task :react-native-plaid-link-sdk:processDebugManifest
package="com.plaid" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-plaid-link-sdk/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.plaid" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-plaid-link-sdk/android/src/main/AndroidManifest.xml.

> Task :react-native-reanimated:extractDeepLinksDebug

> Task :react-native-push-notification:processDebugManifest
package="com.dieam.reactnativepushnotification" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-push-notification/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.dieam.reactnativepushnotification" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-push-notification/android/src/main/AndroidManifest.xml.

> Task :react-native-safe-area-context:extractDeepLinksDebug

> Task :react-native-reanimated:processDebugManifest
package="com.swmansion.reanimated" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-reanimated/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.swmansion.reanimated" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-reanimated/android/src/main/AndroidManifest.xml.

> Task :react-native-screens:extractDeepLinksDebug

> Task :react-native-safe-area-context:processDebugManifest
package="com.th3rdwave.safeareacontext" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.th3rdwave.safeareacontext" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-safe-area-context/android/src/main/AndroidManifest.xml.

> Task :react-native-svg:extractDeepLinksDebug

> Task :react-native-screens:processDebugManifest
package="com.swmansion.rnscreens" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.swmansion.rnscreens" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/AndroidManifest.xml.

> Task :react-native-vision-camera:extractDeepLinksDebug
> Task :react-native-webview:extractDeepLinksDebug

> Task :react-native-svg:processDebugManifest
package="com.horcrux.svg" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-svg/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.horcrux.svg" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-svg/android/src/main/AndroidManifest.xml.

> Task :react-native-vision-camera:processDebugManifest
> Task :rn-fetch-blob:extractDeepLinksDebug
> Task :react-native-webview:processDebugManifest
> Task :heap_react-native-heap:compileDebugLibraryResources

> Task :rn-fetch-blob:processDebugManifest
package="com.RNFetchBlob" found in source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/rn-fetch-blob/android/src/main/AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.RNFetchBlob" from the source AndroidManifest.xml: /Users/<USER>/Documents/reactNative/mobile73/node_modules/rn-fetch-blob/android/src/main/AndroidManifest.xml.

> Task :heap_react-native-heap:parseDebugLocalResources
> Task :launchdarkly-react-native-client-sdk:compileDebugLibraryResources

> Task :app:processDebugMainManifest
[org.tensorflow:tensorflow-lite:2.10.0] /Users/<USER>/.gradle/caches/transforms-3/1fbdaef2b480a41a048dfc7490df0afc/transformed/jetified-tensorflow-lite-2.10.0/AndroidManifest.xml Warning:
	Namespace 'org.tensorflow.lite' is used in multiple modules and/or libraries: org.tensorflow:tensorflow-lite:2.10.0, org.tensorflow:tensorflow-lite-api:2.10.0. Please ensure that all modules and libraries have a unique namespace. For more information, See https://developer.android.com/studio/build/configure-app-module#set-namespace

> Task :app:processDebugManifest
> Task :heap_react-native-heap:generateDebugRFile
> Task :launchdarkly-react-native-client-sdk:parseDebugLocalResources
> Task :lottie-react-native:compileDebugLibraryResources
> Task :launchdarkly-react-native-client-sdk:generateDebugRFile
> Task :lottie-react-native:parseDebugLocalResources
> Task :react-native-async-storage_async-storage:compileDebugLibraryResources
> Task :lottie-react-native:generateDebugRFile
> Task :react-native-async-storage_async-storage:parseDebugLocalResources
> Task :react-native-bootsplash:compileDebugLibraryResources
> Task :react-native-async-storage_async-storage:generateDebugRFile
> Task :react-native-camera:compileGeneralDebugLibraryResources
> Task :react-native-community_netinfo:compileDebugLibraryResources
> Task :react-native-bootsplash:parseDebugLocalResources
> Task :react-native-camera:parseGeneralDebugLocalResources
> Task :react-native-community_netinfo:parseDebugLocalResources
> Task :react-native-bootsplash:generateDebugRFile
> Task :react-native-camera:generateGeneralDebugRFile
> Task :react-native-device-info:compileDebugLibraryResources
> Task :react-native-community_netinfo:generateDebugRFile
> Task :react-native-device-info:parseDebugLocalResources
> Task :react-native-firebase_analytics:compileDebugLibraryResources
> Task :react-native-device-info:generateDebugRFile
> Task :react-native-firebase_analytics:parseDebugLocalResources
> Task :react-native-firebase_app:compileDebugLibraryResources
> Task :react-native-firebase_app:parseDebugLocalResources
> Task :react-native-firebase_analytics:generateDebugRFile
> Task :react-native-firebase_messaging:compileDebugLibraryResources
> Task :react-native-firebase_remote-config:compileDebugLibraryResources
> Task :react-native-firebase_messaging:parseDebugLocalResources
> Task :react-native-firebase_app:generateDebugRFile
> Task :react-native-firebase_remote-config:parseDebugLocalResources
> Task :react-native-firebase_messaging:generateDebugRFile
> Task :react-native-fs:compileDebugLibraryResources
> Task :react-native-firebase_remote-config:generateDebugRFile
> Task :react-native-fs:parseDebugLocalResources
> Task :react-native-gesture-handler:compileDebugLibraryResources
> Task :react-native-gesture-handler:parseDebugLocalResources
> Task :react-native-fs:generateDebugRFile
> Task :react-native-image-picker:compileDebugLibraryResources
> Task :react-native-image-picker:parseDebugLocalResources
> Task :react-native-gesture-handler:generateDebugRFile
> Task :react-native-jumio-mobilesdk:compileDebugLibraryResources
> Task :react-native-image-picker:generateDebugRFile
> Task :react-native-jumio-mobilesdk:parseDebugLocalResources
> Task :react-native-keychain:compileDebugLibraryResources
> Task :react-native-keychain:parseDebugLocalResources
> Task :react-native-jumio-mobilesdk:generateDebugRFile
> Task :react-native-linear-gradient:compileDebugLibraryResources
> Task :react-native-keychain:generateDebugRFile
> Task :react-native-linear-gradient:parseDebugLocalResources
> Task :react-native-permissions:compileDebugLibraryResources
> Task :react-native-linear-gradient:generateDebugRFile
> Task :react-native-permissions:parseDebugLocalResources
> Task :react-native-plaid-link-sdk:compileDebugLibraryResources
> Task :react-native-permissions:generateDebugRFile
> Task :react-native-push-notification:compileDebugLibraryResources
> Task :react-native-plaid-link-sdk:parseDebugLocalResources
> Task :react-native-push-notification:parseDebugLocalResources
> Task :react-native-reanimated:compileDebugLibraryResources
> Task :react-native-plaid-link-sdk:generateDebugRFile
> Task :react-native-safe-area-context:compileDebugLibraryResources
> Task :react-native-push-notification:generateDebugRFile
> Task :react-native-reanimated:parseDebugLocalResources
> Task :react-native-safe-area-context:parseDebugLocalResources
> Task :react-native-reanimated:generateDebugRFile
> Task :react-native-safe-area-context:generateDebugRFile
> Task :react-native-screens:parseDebugLocalResources
> Task :react-native-svg:compileDebugLibraryResources
> Task :react-native-svg:parseDebugLocalResources
> Task :app:processDebugManifestForPackage
> Task :react-native-screens:compileDebugLibraryResources
> Task :react-native-screens:generateDebugRFile
> Task :react-native-vision-camera:compileDebugLibraryResources
> Task :react-native-svg:generateDebugRFile
> Task :react-native-vision-camera:parseDebugLocalResources
> Task :react-native-webview:compileDebugLibraryResources
> Task :react-native-vision-camera:generateDebugRFile
> Task :react-native-webview:parseDebugLocalResources
> Task :rn-fetch-blob:compileDebugLibraryResources
> Task :rn-fetch-blob:parseDebugLocalResources
> Task :heap_react-native-heap:generateDebugBuildConfig
> Task :react-native-webview:generateDebugRFile
> Task :heap_react-native-heap:javaPreCompileDebug
> Task :rn-fetch-blob:generateDebugRFile
> Task :launchdarkly-react-native-client-sdk:generateDebugBuildConfig
> Task :heap_react-native-heap:compileDebugJavaWithJavac
> Task :launchdarkly-react-native-client-sdk:javaPreCompileDebug
> Task :heap_react-native-heap:bundleLibCompileToJarDebug
> Task :lottie-react-native:generateDebugBuildConfig

> Task :launchdarkly-react-native-client-sdk:compileDebugJavaWithJavac

> Task :launchdarkly-react-native-client-sdk:bundleLibCompileToJarDebug
> Task :lottie-react-native:javaPreCompileDebug
> Task :react-native-async-storage_async-storage:generateDebugBuildConfig
> Task :react-native-async-storage_async-storage:javaPreCompileDebug
> Task :react-native-bootsplash:generateDebugBuildConfig

> Task :react-native-async-storage_async-storage:compileDebugJavaWithJavac

> Task :react-native-bootsplash:javaPreCompileDebug
> Task :react-native-async-storage_async-storage:bundleLibCompileToJarDebug
> Task :react-native-camera:generateGeneralDebugBuildConfig

> Task :react-native-bootsplash:compileDebugJavaWithJavac

> Task :react-native-camera:javaPreCompileGeneralDebug
> Task :react-native-bootsplash:bundleLibCompileToJarDebug
> Task :react-native-community_netinfo:generateDebugBuildConfig

> Task :react-native-camera:compileGeneralDebugJavaWithJavac

> Task :react-native-community_netinfo:javaPreCompileDebug
> Task :react-native-device-info:generateDebugBuildConfig
> Task :react-native-camera:bundleLibCompileToJarGeneralDebug

> Task :react-native-community_netinfo:compileDebugJavaWithJavac

> Task :react-native-device-info:javaPreCompileDebug
> Task :react-native-community_netinfo:bundleLibCompileToJarDebug
> Task :react-native-firebase_analytics:generateDebugBuildConfig

> Task :react-native-device-info:compileDebugJavaWithJavac

> Task :lottie-react-native:compileDebugKotlin
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/lottie-react-native/android/src/main/java/com/airbnb/android/react/lottie/LottieAnimationViewPropertyManager.kt:20:38 'ReactFontManager' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/lottie-react-native/android/src/main/java/com/airbnb/android/react/lottie/LottieAnimationViewPropertyManager.kt:71:24 'ReactFontManager' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/lottie-react-native/android/src/main/java/com/airbnb/android/react/lottie/LottieAnimationViewPropertyManager.kt:85:24 'ReactFontManager' is deprecated. Deprecated in Java

> Task :react-native-device-info:bundleLibCompileToJarDebug
> Task :lottie-react-native:compileDebugJavaWithJavac
> Task :react-native-firebase_analytics:javaPreCompileDebug
> Task :lottie-react-native:bundleLibCompileToJarDebug
> Task :react-native-firebase_app:generateDebugBuildConfig
> Task :react-native-firebase_app:javaPreCompileDebug
> Task :react-native-firebase_messaging:generateDebugBuildConfig

> Task :react-native-firebase_app:compileDebugJavaWithJavac

> Task :react-native-firebase_messaging:javaPreCompileDebug
> Task :react-native-firebase_app:bundleLibCompileToJarDebug
> Task :react-native-firebase_remote-config:generateDebugBuildConfig
> Task :app:processDebugResources

> Task :react-native-firebase_analytics:compileDebugJavaWithJavac

> Task :react-native-firebase_analytics:bundleLibCompileToJarDebug

> Task :react-native-firebase_messaging:compileDebugJavaWithJavac

> Task :react-native-firebase_remote-config:javaPreCompileDebug
> Task :react-native-firebase_messaging:bundleLibCompileToJarDebug
> Task :react-native-fs:generateDebugBuildConfig

> Task :react-native-firebase_remote-config:compileDebugJavaWithJavac

> Task :react-native-fs:javaPreCompileDebug
> Task :react-native-firebase_remote-config:bundleLibCompileToJarDebug
> Task :react-native-gesture-handler:generateDebugBuildConfig

> Task :react-native-fs:compileDebugJavaWithJavac

> Task :react-native-reanimated:generateDebugBuildConfig
> Task :react-native-fs:bundleLibCompileToJarDebug
> Task :react-native-reanimated:packageNdkLibs NO-SOURCE
> Task :react-native-reanimated:javaPreCompileDebug
> Task :react-native-gesture-handler:javaPreCompileDebug

> Task :react-native-reanimated:compileDebugJavaWithJavac

> Task :react-native-image-picker:generateDebugBuildConfig
> Task :react-native-image-picker:javaPreCompileDebug
> Task :react-native-jumio-mobilesdk:generateDebugBuildConfig
> Task :react-native-reanimated:bundleLibCompileToJarDebug

> Task :react-native-image-picker:compileDebugJavaWithJavac

> Task :react-native-image-picker:bundleLibCompileToJarDebug
> Task :react-native-jumio-mobilesdk:javaPreCompileDebug
> Task :react-native-keychain:generateDebugBuildConfig
> Task :react-native-keychain:javaPreCompileDebug
> Task :react-native-linear-gradient:generateDebugBuildConfig

> Task :react-native-keychain:compileDebugJavaWithJavac

> Task :react-native-linear-gradient:javaPreCompileDebug
> Task :react-native-permissions:generateDebugBuildConfig
> Task :react-native-keychain:bundleLibCompileToJarDebug
> Task :react-native-linear-gradient:compileDebugJavaWithJavac
> Task :react-native-permissions:javaPreCompileDebug
> Task :react-native-linear-gradient:bundleLibCompileToJarDebug
> Task :react-native-plaid-link-sdk:generateDebugBuildConfig

> Task :react-native-permissions:compileDebugJavaWithJavac

> Task :react-native-permissions:bundleLibCompileToJarDebug
> Task :react-native-plaid-link-sdk:javaPreCompileDebug
> Task :react-native-push-notification:generateDebugBuildConfig
> Task :react-native-push-notification:javaPreCompileDebug
> Task :react-native-safe-area-context:generateDebugBuildConfig

> Task :react-native-push-notification:compileDebugJavaWithJavac

> Task :react-native-push-notification:bundleLibCompileToJarDebug
> Task :react-native-safe-area-context:javaPreCompileDebug
> Task :react-native-screens:generateDebugBuildConfig
> Task :react-native-screens:javaPreCompileDebug
> Task :react-native-svg:generateDebugBuildConfig
> Task :react-native-svg:javaPreCompileDebug
> Task :react-native-vision-camera:generateDebugBuildConfig

> Task :react-native-jumio-mobilesdk:compileDebugKotlin
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-jumio-mobilesdk/android/src/main/java/com/jumio/react/JumioModule.kt:73:29 Parameter 'customizations' is never used

> Task :react-native-svg:compileDebugJavaWithJavac

> Task :react-native-jumio-mobilesdk:compileDebugJavaWithJavac
> Task :react-native-jumio-mobilesdk:bundleLibCompileToJarDebug
> Task :react-native-svg:bundleLibCompileToJarDebug
> Task :react-native-vision-camera:javaPreCompileDebug
> Task :react-native-webview:generateDebugBuildConfig
> Task :react-native-webview:javaPreCompileDebug
> Task :rn-fetch-blob:generateDebugBuildConfig
> Task :rn-fetch-blob:javaPreCompileDebug
> Task :app:javaPreCompileDebug

> Task :react-native-plaid-link-sdk:compileDebugKotlin
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-plaid-link-sdk/android/src/main/java/com/plaid/PLKEmbeddedView.kt:14:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-plaid-link-sdk/android/src/main/java/com/plaid/PLKEmbeddedView.kt:90:34 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-plaid-link-sdk/android/src/main/java/com/plaid/PLKEmbeddedView.kt:90:63 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-plaid-link-sdk/android/src/main/java/com/plaid/PLKEmbeddedView.kt:111:38 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-plaid-link-sdk/android/src/main/java/com/plaid/PLKEmbeddedView.kt:111:67 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-plaid-link-sdk/android/src/main/java/com/plaid/PLKEmbeddedView.kt:135:32 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-plaid-link-sdk/android/src/main/java/com/plaid/PLKEmbeddedView.kt:135:61 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-plaid-link-sdk/android/src/main/java/com/plaid/PLKEmbeddedView.kt:159:30 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-plaid-link-sdk/android/src/main/java/com/plaid/PLKEmbeddedView.kt:159:59 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-plaid-link-sdk/android/src/main/java/com/plaid/PLKEmbeddedViewManager.kt:3:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-plaid-link-sdk/android/src/main/java/com/plaid/PLKEmbeddedViewManager.kt:11:2 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-plaid-link-sdk/android/src/main/java/com/plaid/PlaidModule.kt:12:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-plaid-link-sdk/android/src/main/java/com/plaid/PlaidModule.kt:25:2 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-plaid-link-sdk/android/src/main/java/com/plaid/PlaidModule.kt:63:9 Condition 'token == null' is always 'false'

> Task :rn-fetch-blob:compileDebugJavaWithJavac

> Task :react-native-plaid-link-sdk:compileDebugJavaWithJavac

> Task :react-native-plaid-link-sdk:bundleLibCompileToJarDebug
> Task :rn-fetch-blob:bundleLibCompileToJarDebug
> Task :amazon-cognito-identity-js:mergeDebugShaders
> Task :amazon-cognito-identity-js:compileDebugShaders NO-SOURCE
> Task :amazon-cognito-identity-js:generateDebugAssets UP-TO-DATE
> Task :amazon-cognito-identity-js:packageDebugAssets
> Task :app:mergeDebugShaders
> Task :app:compileDebugShaders NO-SOURCE
> Task :app:generateDebugAssets UP-TO-DATE
> Task :heap_react-native-heap:mergeDebugShaders
> Task :heap_react-native-heap:compileDebugShaders NO-SOURCE
> Task :heap_react-native-heap:generateDebugAssets UP-TO-DATE
> Task :heap_react-native-heap:packageDebugAssets
> Task :launchdarkly-react-native-client-sdk:mergeDebugShaders
> Task :launchdarkly-react-native-client-sdk:compileDebugShaders NO-SOURCE
> Task :launchdarkly-react-native-client-sdk:generateDebugAssets UP-TO-DATE
> Task :launchdarkly-react-native-client-sdk:packageDebugAssets
> Task :lottie-react-native:mergeDebugShaders
> Task :lottie-react-native:compileDebugShaders NO-SOURCE
> Task :lottie-react-native:generateDebugAssets UP-TO-DATE
> Task :lottie-react-native:packageDebugAssets
> Task :react-native-async-storage_async-storage:mergeDebugShaders
> Task :react-native-async-storage_async-storage:compileDebugShaders NO-SOURCE
> Task :react-native-async-storage_async-storage:generateDebugAssets UP-TO-DATE
> Task :react-native-async-storage_async-storage:packageDebugAssets
> Task :react-native-bootsplash:mergeDebugShaders
> Task :react-native-bootsplash:compileDebugShaders NO-SOURCE
> Task :react-native-bootsplash:generateDebugAssets UP-TO-DATE
> Task :react-native-bootsplash:packageDebugAssets
> Task :react-native-camera:mergeGeneralDebugShaders
> Task :react-native-camera:compileGeneralDebugShaders NO-SOURCE
> Task :react-native-camera:generateGeneralDebugAssets UP-TO-DATE
> Task :react-native-camera:packageGeneralDebugAssets
> Task :react-native-community_netinfo:mergeDebugShaders
> Task :react-native-community_netinfo:compileDebugShaders NO-SOURCE
> Task :react-native-community_netinfo:generateDebugAssets UP-TO-DATE
> Task :react-native-community_netinfo:packageDebugAssets
> Task :react-native-device-info:mergeDebugShaders
> Task :react-native-device-info:compileDebugShaders NO-SOURCE
> Task :react-native-device-info:generateDebugAssets UP-TO-DATE
> Task :react-native-device-info:packageDebugAssets
> Task :react-native-firebase_analytics:mergeDebugShaders
> Task :react-native-firebase_analytics:compileDebugShaders NO-SOURCE
> Task :react-native-firebase_analytics:generateDebugAssets UP-TO-DATE
> Task :react-native-firebase_analytics:packageDebugAssets
> Task :react-native-firebase_app:mergeDebugShaders
> Task :react-native-firebase_app:compileDebugShaders NO-SOURCE
> Task :react-native-firebase_app:generateDebugAssets UP-TO-DATE
> Task :react-native-firebase_app:packageDebugAssets
> Task :react-native-firebase_messaging:mergeDebugShaders
> Task :react-native-firebase_messaging:compileDebugShaders NO-SOURCE
> Task :react-native-firebase_messaging:generateDebugAssets UP-TO-DATE
> Task :react-native-firebase_messaging:packageDebugAssets
> Task :react-native-firebase_remote-config:mergeDebugShaders
> Task :react-native-firebase_remote-config:compileDebugShaders NO-SOURCE
> Task :react-native-firebase_remote-config:generateDebugAssets UP-TO-DATE
> Task :react-native-firebase_remote-config:packageDebugAssets
> Task :react-native-fs:mergeDebugShaders
> Task :react-native-fs:compileDebugShaders NO-SOURCE
> Task :react-native-fs:generateDebugAssets UP-TO-DATE
> Task :react-native-fs:packageDebugAssets
> Task :react-native-gesture-handler:mergeDebugShaders
> Task :react-native-gesture-handler:compileDebugShaders NO-SOURCE
> Task :react-native-gesture-handler:generateDebugAssets UP-TO-DATE
> Task :react-native-gesture-handler:packageDebugAssets
> Task :react-native-image-picker:mergeDebugShaders
> Task :react-native-image-picker:compileDebugShaders NO-SOURCE
> Task :react-native-image-picker:generateDebugAssets UP-TO-DATE
> Task :react-native-image-picker:packageDebugAssets
> Task :react-native-jumio-mobilesdk:mergeDebugShaders
> Task :react-native-jumio-mobilesdk:compileDebugShaders NO-SOURCE
> Task :react-native-jumio-mobilesdk:generateDebugAssets UP-TO-DATE
> Task :react-native-jumio-mobilesdk:packageDebugAssets
> Task :react-native-keychain:mergeDebugShaders
> Task :react-native-keychain:compileDebugShaders NO-SOURCE
> Task :react-native-keychain:generateDebugAssets UP-TO-DATE
> Task :react-native-keychain:packageDebugAssets
> Task :react-native-linear-gradient:mergeDebugShaders
> Task :react-native-linear-gradient:compileDebugShaders NO-SOURCE
> Task :react-native-linear-gradient:generateDebugAssets UP-TO-DATE
> Task :react-native-linear-gradient:packageDebugAssets
> Task :react-native-permissions:mergeDebugShaders
> Task :react-native-permissions:compileDebugShaders NO-SOURCE
> Task :react-native-permissions:generateDebugAssets UP-TO-DATE
> Task :react-native-permissions:packageDebugAssets
> Task :react-native-plaid-link-sdk:mergeDebugShaders
> Task :react-native-plaid-link-sdk:compileDebugShaders NO-SOURCE
> Task :react-native-plaid-link-sdk:generateDebugAssets UP-TO-DATE
> Task :react-native-plaid-link-sdk:packageDebugAssets
> Task :react-native-push-notification:mergeDebugShaders
> Task :react-native-push-notification:compileDebugShaders NO-SOURCE
> Task :react-native-push-notification:generateDebugAssets UP-TO-DATE
> Task :react-native-push-notification:packageDebugAssets
> Task :react-native-reanimated:mergeDebugShaders
> Task :react-native-reanimated:compileDebugShaders NO-SOURCE
> Task :react-native-reanimated:generateDebugAssets UP-TO-DATE
> Task :react-native-reanimated:packageDebugAssets
> Task :react-native-safe-area-context:mergeDebugShaders
> Task :react-native-safe-area-context:compileDebugShaders NO-SOURCE
> Task :react-native-safe-area-context:generateDebugAssets UP-TO-DATE
> Task :react-native-safe-area-context:packageDebugAssets
> Task :react-native-screens:mergeDebugShaders
> Task :react-native-screens:compileDebugShaders NO-SOURCE
> Task :react-native-screens:generateDebugAssets UP-TO-DATE

> Task :react-native-safe-area-context:compileDebugKotlin
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-safe-area-context/android/src/main/java/com/th3rdwave/safeareacontext/SafeAreaContextModule.kt:6:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-safe-area-context/android/src/main/java/com/th3rdwave/safeareacontext/SafeAreaContextModule.kt:8:2 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-safe-area-context/android/src/main/java/com/th3rdwave/safeareacontext/SafeAreaContextPackage.kt:6:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-safe-area-context/android/src/main/java/com/th3rdwave/safeareacontext/SafeAreaContextPackage.kt:25:51 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-safe-area-context/android/src/main/java/com/th3rdwave/safeareacontext/SafeAreaContextPackage.kt:27:11 'constructor ReactModuleInfo(String!, String!, Boolean, Boolean, Boolean, Boolean, Boolean)' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-safe-area-context/android/src/main/java/com/th3rdwave/safeareacontext/SafeAreaContextPackage.kt:33:27 'hasConstants: Boolean' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-safe-area-context/android/src/main/java/com/th3rdwave/safeareacontext/SafeAreaProviderManager.kt:4:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-safe-area-context/android/src/main/java/com/th3rdwave/safeareacontext/SafeAreaProviderManager.kt:11:2 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-safe-area-context/android/src/main/java/com/th3rdwave/safeareacontext/SafeAreaView.kt:59:23 'getter for uiImplementation: UIImplementation!' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-safe-area-context/android/src/main/java/com/th3rdwave/safeareacontext/SafeAreaViewManager.kt:4:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-safe-area-context/android/src/main/java/com/th3rdwave/safeareacontext/SafeAreaViewManager.kt:14:2 'ReactModule' is deprecated. Deprecated in Java

> Task :react-native-screens:packageDebugAssets

> Task :react-native-safe-area-context:compileDebugJavaWithJavac

> Task :react-native-svg:mergeDebugShaders
> Task :react-native-svg:compileDebugShaders NO-SOURCE
> Task :react-native-svg:generateDebugAssets UP-TO-DATE
> Task :react-native-safe-area-context:bundleLibCompileToJarDebug
> Task :react-native-svg:packageDebugAssets
> Task :react-native-vision-camera:mergeDebugShaders
> Task :react-native-vision-camera:compileDebugShaders NO-SOURCE
> Task :react-native-vision-camera:generateDebugAssets UP-TO-DATE
> Task :react-native-vision-camera:packageDebugAssets
> Task :react-native-webview:mergeDebugShaders
> Task :react-native-webview:compileDebugShaders NO-SOURCE
> Task :react-native-webview:generateDebugAssets UP-TO-DATE
> Task :react-native-webview:packageDebugAssets
> Task :rn-fetch-blob:mergeDebugShaders
> Task :rn-fetch-blob:compileDebugShaders NO-SOURCE
> Task :rn-fetch-blob:generateDebugAssets UP-TO-DATE
> Task :rn-fetch-blob:packageDebugAssets
> Task :app:mergeDebugAssets
> Task :app:compressDebugAssets
> Task :react-native-jumio-mobilesdk:bundleLibRuntimeToJarDebug
> Task :heap_react-native-heap:bundleLibRuntimeToJarDebug
> Task :react-native-async-storage_async-storage:bundleLibRuntimeToJarDebug
> Task :react-native-community_netinfo:bundleLibRuntimeToJarDebug
> Task :react-native-firebase_analytics:bundleLibRuntimeToJarDebug
> Task :react-native-firebase_messaging:bundleLibRuntimeToJarDebug
> Task :react-native-firebase_app:bundleLibRuntimeToJarDebug
> Task :react-native-firebase_remote-config:bundleLibRuntimeToJarDebug
> Task :amazon-cognito-identity-js:bundleLibRuntimeToJarDebug
> Task :launchdarkly-react-native-client-sdk:bundleLibRuntimeToJarDebug
> Task :lottie-react-native:bundleLibRuntimeToJarDebug
> Task :react-native-bootsplash:bundleLibRuntimeToJarDebug
> Task :react-native-camera:bundleLibRuntimeToJarGeneralDebug
> Task :react-native-device-info:bundleLibRuntimeToJarDebug
> Task :react-native-fs:bundleLibRuntimeToJarDebug
> Task :react-native-image-picker:bundleLibRuntimeToJarDebug
> Task :react-native-reanimated:bundleLibRuntimeToJarDebug
> Task :react-native-keychain:bundleLibRuntimeToJarDebug
> Task :react-native-linear-gradient:bundleLibRuntimeToJarDebug
> Task :react-native-permissions:bundleLibRuntimeToJarDebug
> Task :react-native-plaid-link-sdk:bundleLibRuntimeToJarDebug
> Task :react-native-push-notification:bundleLibRuntimeToJarDebug
> Task :react-native-safe-area-context:bundleLibRuntimeToJarDebug
> Task :react-native-svg:bundleLibRuntimeToJarDebug
> Task :rn-fetch-blob:bundleLibRuntimeToJarDebug
> Task :app:desugarDebugFileDependencies
> Task :amazon-cognito-identity-js:processDebugJavaRes NO-SOURCE
> Task :heap_react-native-heap:processDebugJavaRes NO-SOURCE
> Task :launchdarkly-react-native-client-sdk:processDebugJavaRes NO-SOURCE
> Task :lottie-react-native:processDebugJavaRes
> Task :react-native-async-storage_async-storage:processDebugJavaRes NO-SOURCE
> Task :react-native-bootsplash:processDebugJavaRes NO-SOURCE
> Task :react-native-camera:processGeneralDebugJavaRes NO-SOURCE
> Task :react-native-community_netinfo:processDebugJavaRes NO-SOURCE
> Task :react-native-device-info:processDebugJavaRes NO-SOURCE
> Task :react-native-firebase_analytics:processDebugJavaRes NO-SOURCE
> Task :react-native-firebase_app:processDebugJavaRes NO-SOURCE
> Task :react-native-firebase_messaging:processDebugJavaRes NO-SOURCE
> Task :react-native-firebase_remote-config:processDebugJavaRes NO-SOURCE
> Task :react-native-fs:processDebugJavaRes NO-SOURCE
> Task :react-native-image-picker:processDebugJavaRes NO-SOURCE
> Task :react-native-jumio-mobilesdk:processDebugJavaRes
> Task :react-native-keychain:processDebugJavaRes NO-SOURCE
> Task :react-native-linear-gradient:processDebugJavaRes NO-SOURCE
> Task :react-native-permissions:processDebugJavaRes NO-SOURCE
> Task :react-native-plaid-link-sdk:processDebugJavaRes
> Task :react-native-push-notification:processDebugJavaRes NO-SOURCE
> Task :react-native-reanimated:processDebugJavaRes NO-SOURCE
> Task :react-native-safe-area-context:processDebugJavaRes
> Task :react-native-svg:processDebugJavaRes NO-SOURCE
> Task :rn-fetch-blob:processDebugJavaRes NO-SOURCE
> Task :amazon-cognito-identity-js:mergeDebugJniLibFolders
> Task :amazon-cognito-identity-js:mergeDebugNativeLibs NO-SOURCE
> Task :amazon-cognito-identity-js:copyDebugJniLibsProjectOnly
> Task :app:mergeDebugJniLibFolders
> Task :heap_react-native-heap:mergeDebugJniLibFolders
> Task :heap_react-native-heap:mergeDebugNativeLibs NO-SOURCE
> Task :heap_react-native-heap:copyDebugJniLibsProjectOnly
> Task :launchdarkly-react-native-client-sdk:mergeDebugJniLibFolders
> Task :launchdarkly-react-native-client-sdk:mergeDebugNativeLibs NO-SOURCE
> Task :launchdarkly-react-native-client-sdk:copyDebugJniLibsProjectOnly
> Task :lottie-react-native:mergeDebugJniLibFolders
> Task :lottie-react-native:mergeDebugNativeLibs NO-SOURCE
> Task :lottie-react-native:copyDebugJniLibsProjectOnly
> Task :react-native-async-storage_async-storage:mergeDebugJniLibFolders
> Task :react-native-async-storage_async-storage:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-async-storage_async-storage:copyDebugJniLibsProjectOnly
> Task :react-native-bootsplash:mergeDebugJniLibFolders
> Task :react-native-bootsplash:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-bootsplash:copyDebugJniLibsProjectOnly
> Task :react-native-camera:mergeGeneralDebugJniLibFolders
> Task :react-native-camera:mergeGeneralDebugNativeLibs NO-SOURCE
> Task :react-native-camera:copyGeneralDebugJniLibsProjectOnly
> Task :react-native-community_netinfo:mergeDebugJniLibFolders
> Task :react-native-community_netinfo:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-community_netinfo:copyDebugJniLibsProjectOnly
> Task :react-native-device-info:mergeDebugJniLibFolders
> Task :react-native-device-info:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-device-info:copyDebugJniLibsProjectOnly
> Task :react-native-firebase_analytics:mergeDebugJniLibFolders
> Task :react-native-firebase_analytics:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-firebase_analytics:copyDebugJniLibsProjectOnly
> Task :react-native-firebase_app:mergeDebugJniLibFolders
> Task :react-native-firebase_app:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-firebase_app:copyDebugJniLibsProjectOnly
> Task :react-native-firebase_messaging:mergeDebugJniLibFolders
> Task :react-native-firebase_messaging:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-firebase_messaging:copyDebugJniLibsProjectOnly
> Task :react-native-firebase_remote-config:mergeDebugJniLibFolders
> Task :react-native-firebase_remote-config:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-firebase_remote-config:copyDebugJniLibsProjectOnly
> Task :react-native-fs:mergeDebugJniLibFolders
> Task :react-native-fs:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-fs:copyDebugJniLibsProjectOnly
> Task :react-native-gesture-handler:mergeDebugJniLibFolders
> Task :react-native-gesture-handler:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-gesture-handler:copyDebugJniLibsProjectOnly
> Task :react-native-image-picker:mergeDebugJniLibFolders
> Task :react-native-image-picker:mergeDebugNativeLibs NO-SOURCE
> Task :app:checkDebugDuplicateClasses
> Task :react-native-image-picker:copyDebugJniLibsProjectOnly

> Task :react-native-webview:compileDebugKotlin
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:40:71 Parameter 'webView' is never used, could be renamed to _
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:81:18 'setter for allowFileAccessFromFileURLs: Boolean' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:82:18 'setter for allowUniversalAccessFromFileURLs: Boolean' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:93:102 Parameter 'contentLength' is never used, could be renamed to _
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:110:32 Variable 'urlObj' initializer is redundant
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:124:21 'allowScanningByMediaScanner(): Unit' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:161:36 'setter for systemUiVisibility: Int' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:336:15 Condition 'args == null' is always 'false'
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:345:34 Condition 'args != null' is always 'true'
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:364:38 'setter for allowUniversalAccessFromFileURLs: Boolean' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:421:51 Unchecked cast: Any! to kotlin.collections.HashMap<String, String> /* = java.util.HashMap<String, String> */
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:477:23 'setter for savePassword: Boolean' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:478:23 'setter for saveFormData: Boolean' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:548:23 'setter for allowFileAccessFromFileURLs: Boolean' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:647:53 Unchecked cast: ArrayList<Any!> to List<Map<String, String>>
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:667:23 'setter for saveFormData: Boolean' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/RNCWebViewManagerImpl.kt:702:36 Parameter 'viewWrapper' is never used
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopCustomMenuSelectionEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopCustomMenuSelectionEvent.kt:11:3 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopCustomMenuSelectionEvent.kt:22:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopCustomMenuSelectionEvent.kt:23:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopHttpErrorEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopHttpErrorEvent.kt:11:3 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopHttpErrorEvent.kt:22:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopHttpErrorEvent.kt:23:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingErrorEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingErrorEvent.kt:11:3 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingErrorEvent.kt:22:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingErrorEvent.kt:23:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingFinishEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingFinishEvent.kt:11:3 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingFinishEvent.kt:22:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingFinishEvent.kt:23:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingProgressEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingProgressEvent.kt:11:3 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingProgressEvent.kt:22:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingProgressEvent.kt:23:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingStartEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingStartEvent.kt:11:3 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingStartEvent.kt:22:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopLoadingStartEvent.kt:23:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopMessageEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopMessageEvent.kt:10:75 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopMessageEvent.kt:21:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopMessageEvent.kt:22:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopNewWindowEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopNewWindowEvent.kt:11:3 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopNewWindowEvent.kt:22:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopNewWindowEvent.kt:23:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopRenderProcessGoneEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopRenderProcessGoneEvent.kt:12:3 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopRenderProcessGoneEvent.kt:23:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopRenderProcessGoneEvent.kt:24:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopShouldStartLoadWithRequestEvent.kt:5:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopShouldStartLoadWithRequestEvent.kt:10:89 'constructor Event<T : Event<(raw) Event<*>>!>(Int)' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopShouldStartLoadWithRequestEvent.kt:27:42 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-webview/android/src/main/java/com/reactnativecommunity/webview/events/TopShouldStartLoadWithRequestEvent.kt:28:21 'receiveEvent(Int, String!, WritableMap?): Unit' is deprecated. Deprecated in Java

> Task :react-native-gesture-handler:compileDebugKotlin
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/RNGestureHandlerPackage.kt:8:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/RNGestureHandlerPackage.kt:66:26 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/RNGestureHandlerPackage.kt:66:89 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/RNGestureHandlerPackage.kt:69:42 'constructor ReactModuleInfo(String!, String!, Boolean, Boolean, Boolean, Boolean, Boolean)' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/core/FlingGestureHandler.kt:25:26 Parameter 'event' is never used
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/react/RNGestureHandlerButtonViewManager.kt:26:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/react/RNGestureHandlerButtonViewManager.kt:38:2 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/react/RNGestureHandlerModule.kt:13:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/react/RNGestureHandlerRootViewManager.kt:3:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/react/RNGestureHandlerRootViewManager.kt:15:2 'ReactModule' is deprecated. Deprecated in Java

> Task :react-native-screens:compileDebugKotlin
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ModalScreenViewManager.kt:3:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ModalScreenViewManager.kt:5:2 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/RNScreensPackage.kt:42:47 'constructor ReactModuleInfo(String!, String!, Boolean, Boolean, Boolean, Boolean, Boolean)' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenContainerViewManager.kt:5:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenContainerViewManager.kt:10:2 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackFragment.kt:65:27 'setter for targetElevation: Float' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackFragment.kt:128:27 'setter for targetElevation: Float' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderConfig.kt:86:34 'getter for systemWindowInsetTop: Int' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderConfig.kt:231:37 'setColorFilter(Int, PorterDuff.Mode): Unit' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderConfigViewManager.kt:7:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderConfigViewManager.kt:18:2 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderSubviewManager.kt:4:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackHeaderSubviewManager.kt:12:2 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackViewManager.kt:6:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenStackViewManager.kt:15:2 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenViewManager.kt:6:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenViewManager.kt:24:2 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenViewManager.kt:47:42 'setStateWrapper(StateWrapper!): Unit' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:135:47 'replaceSystemWindowInsets(Int, Int, Int, Int): WindowInsetsCompat' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:136:51 'getter for systemWindowInsetLeft: Int' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:138:51 'getter for systemWindowInsetRight: Int' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreenWindowTraits.kt:139:51 'getter for systemWindowInsetBottom: Int' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreensModule.kt:7:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/ScreensModule.kt:12:2 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/SearchBarManager.kt:6:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/SearchBarManager.kt:17:2 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/SearchBarManager.kt:100:22 Parameter 'view' is never used
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/SearchBarManager.kt:100:43 Parameter 'placeholder' is never used
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/SearchBarView.kt:147:43 Parameter 'flag' is never used
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/main/java/com/swmansion/rnscreens/events/HeaderHeightChangeEvent.kt:6:44 'RCTEventEmitter' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/paper/java/com/swmansion/rnscreens/FabricEnabledViewGroup.kt:5:37 'FabricViewStateManager' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/paper/java/com/swmansion/rnscreens/FabricEnabledViewGroup.kt:9:48 'FabricViewStateManager' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/paper/java/com/swmansion/rnscreens/FabricEnabledViewGroup.kt:11:42 Parameter 'width' is never used
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/paper/java/com/swmansion/rnscreens/FabricEnabledViewGroup.kt:11:54 Parameter 'height' is never used
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-screens/android/src/paper/java/com/swmansion/rnscreens/FabricEnabledViewGroup.kt:11:67 Parameter 'headerHeight' is never used

> Task :react-native-vision-camera:compileDebugKotlin
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-vision-camera/android/src/main/java/com/mrousavy/camera/core/utils/CamcorderProfileUtils.kt:66:42 'get(Int, Int): CamcorderProfile!' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-vision-camera/android/src/main/java/com/mrousavy/camera/core/utils/CamcorderProfileUtils.kt:90:42 'get(Int, Int): CamcorderProfile!' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-vision-camera/android/src/main/java/com/mrousavy/camera/react/CameraViewModule.kt:13:46 'ReactModule' is deprecated. Deprecated in Java
w: file:///Users/<USER>/Documents/reactNative/mobile73/node_modules/react-native-vision-camera/android/src/main/java/com/mrousavy/camera/react/CameraViewModule.kt:37:2 'ReactModule' is deprecated. Deprecated in Java

> Task :app:mergeExtDexDebug
WARNING: /Users/<USER>/.gradle/caches/transforms-3/d1aacfb2d708b80df60689c5bf61844c/transformed/jetified-iproov-9.0.3-runtime.jar: D8: The companion object Companion could not be found in class char

> Task :react-native-gesture-handler:compileDebugJavaWithJavac
> Task :react-native-gesture-handler:bundleLibCompileToJarDebug

> Task :react-native-screens:compileDebugJavaWithJavac

> Task :react-native-screens:bundleLibCompileToJarDebug
> Task :react-native-vision-camera:compileDebugJavaWithJavac
> Task :react-native-vision-camera:bundleLibCompileToJarDebug

> Task :react-native-webview:compileDebugJavaWithJavac

> Task :react-native-webview:bundleLibCompileToJarDebug
> Task :react-native-webview:bundleLibRuntimeToJarDebug
> Task :react-native-gesture-handler:processDebugJavaRes
> Task :react-native-gesture-handler:bundleLibRuntimeToJarDebug
> Task :react-native-screens:bundleLibRuntimeToJarDebug
> Task :react-native-vision-camera:bundleLibRuntimeToJarDebug
> Task :react-native-screens:processDebugJavaRes
> Task :react-native-vision-camera:processDebugJavaRes
> Task :react-native-webview:processDebugJavaRes
> Task :react-native-jumio-mobilesdk:mergeDebugJniLibFolders
> Task :react-native-jumio-mobilesdk:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-jumio-mobilesdk:copyDebugJniLibsProjectOnly
> Task :react-native-keychain:mergeDebugJniLibFolders
> Task :react-native-keychain:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-keychain:copyDebugJniLibsProjectOnly
> Task :react-native-linear-gradient:mergeDebugJniLibFolders
> Task :react-native-linear-gradient:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-linear-gradient:copyDebugJniLibsProjectOnly
> Task :react-native-permissions:mergeDebugJniLibFolders
> Task :react-native-permissions:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-permissions:copyDebugJniLibsProjectOnly
> Task :react-native-plaid-link-sdk:mergeDebugJniLibFolders
> Task :react-native-plaid-link-sdk:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-plaid-link-sdk:copyDebugJniLibsProjectOnly
> Task :react-native-push-notification:mergeDebugJniLibFolders
> Task :react-native-push-notification:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-push-notification:copyDebugJniLibsProjectOnly
> Task :app:compileDebugKotlin
> Task :react-native-reanimated:configureCMakeDebug[arm64-v8a]
> Task :app:compileDebugJavaWithJavac
> Task :app:dexBuilderDebug
> Task :app:processDebugJavaRes
> Task :app:mergeDebugGlobalSynthetics
> Task :app:mergeLibDexDebug
> Task :app:mergeProjectDexDebug
> Task :app:mergeDebugJavaResource
> Task :react-native-reanimated:buildCMakeDebug[arm64-v8a]
> Task :react-native-reanimated:configureCMakeDebug[armeabi-v7a]
> Task :react-native-reanimated:buildCMakeDebug[armeabi-v7a]
> Task :react-native-reanimated:configureCMakeDebug[x86]
> Task :react-native-reanimated:buildCMakeDebug[x86]
> Task :react-native-reanimated:configureCMakeDebug[x86_64]
> Task :react-native-reanimated:buildCMakeDebug[x86_64]
> Task :react-native-reanimated:mergeDebugJniLibFolders
> Task :react-native-safe-area-context:mergeDebugJniLibFolders
> Task :react-native-safe-area-context:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-safe-area-context:copyDebugJniLibsProjectOnly
> Task :react-native-reanimated:mergeDebugNativeLibs
> Task :react-native-screens:configureCMakeDebug[arm64-v8a]
> Task :react-native-reanimated:copyDebugJniLibsProjectOnly
> Task :react-native-screens:buildCMakeDebug[arm64-v8a]
> Task :react-native-screens:configureCMakeDebug[armeabi-v7a]
> Task :react-native-screens:buildCMakeDebug[armeabi-v7a]
> Task :react-native-screens:configureCMakeDebug[x86]
> Task :react-native-screens:buildCMakeDebug[x86]
> Task :react-native-screens:configureCMakeDebug[x86_64]
> Task :react-native-screens:buildCMakeDebug[x86_64]
> Task :react-native-screens:mergeDebugJniLibFolders
> Task :react-native-svg:mergeDebugJniLibFolders
> Task :react-native-svg:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-svg:copyDebugJniLibsProjectOnly
> Task :react-native-screens:mergeDebugNativeLibs

> Task :react-native-vision-camera:configureCMakeDebug[arm64-v8a]
C/C++: VisionCamera: Frame Processors: OFF!

> Task :react-native-screens:copyDebugJniLibsProjectOnly
> Task :react-native-vision-camera:buildCMakeDebug[arm64-v8a]

> Task :react-native-vision-camera:configureCMakeDebug[armeabi-v7a]
C/C++: VisionCamera: Frame Processors: OFF!

> Task :react-native-vision-camera:buildCMakeDebug[armeabi-v7a]

> Task :react-native-vision-camera:configureCMakeDebug[x86]
C/C++: VisionCamera: Frame Processors: OFF!

> Task :react-native-vision-camera:buildCMakeDebug[x86]

> Task :react-native-vision-camera:configureCMakeDebug[x86_64]
C/C++: VisionCamera: Frame Processors: OFF!

> Task :react-native-vision-camera:buildCMakeDebug[x86_64]
> Task :react-native-vision-camera:mergeDebugJniLibFolders
> Task :react-native-webview:mergeDebugJniLibFolders
> Task :react-native-webview:mergeDebugNativeLibs NO-SOURCE
> Task :react-native-webview:copyDebugJniLibsProjectOnly
> Task :rn-fetch-blob:mergeDebugJniLibFolders
> Task :rn-fetch-blob:mergeDebugNativeLibs NO-SOURCE
> Task :rn-fetch-blob:copyDebugJniLibsProjectOnly
> Task :app:validateSigningDebug
> Task :app:writeDebugAppMetadata
> Task :app:writeDebugSigningConfigVersions
> Task :react-native-vision-camera:mergeDebugNativeLibs
> Task :react-native-vision-camera:copyDebugJniLibsProjectOnly
> Task :app:mergeDebugNativeLibs

> Task :app:stripDebugDebugSymbols
Unable to strip the following libraries, packaging them as they are: libconceal.so, libnative-lib.so.

> Task :app:packageDebug
> Task :app:createDebugApkListingFileRedirect

> Task :app:installDebug
Skipping device 'emulator-5562' (emulator-5562): Device is OFFLINE.
Installing APK 'app-debug.apk' on 'SM-X900 - 14' for :app:debug
Installed on 1 device.

Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

For more on this, please refer to https://docs.gradle.org/8.3/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.

BUILD SUCCESSFUL in 3m 15s
582 actionable tasks: 577 executed, 5 up-to-date
info Connecting to the development server...
info Starting the app on "R52W6099GHN"...
Starting: Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] cmp=com.saltlending.mobile/.MainActivity }
